# 🗄️ استيراد البيانات اليمنية إلى قاعدة البيانات

## نظرة عامة

هذا المجلد يحتوي على الأدوات اللازمة لاستيراد البيانات اليمنية المولدة إلى قاعدة بيانات TECNODRIVE Platform.

## 📁 الملفات

### 🐍 ملفات Python
- **`import_to_database.py`** - Script Python الرئيسي لتحويل البيانات من JSON إلى SQL
- **`generate_yemen_data.py`** - مولد البيانات اليمنية الأصلي

### 🔧 ملفات التشغيل
- **`import_yemen_data.bat`** - Script Windows لتشغيل العملية بالكامل (يتطلب Python)
- **`import_yemen_data.sh`** - Script Linux/Mac لتشغيل العملية بالكامل (يتطلب Python)
- **`import_data_direct.bat`** - Script Windows لاستيراد البيانات مباشرة (لا يتطلب Python)
- **`test_import.py`** - Script اختبار لتحويل البيانات
- **`simple_test.py`** - اختبار بسيط لـ Python

### 📊 ملفات البيانات
- **`generated_data/`** - مجلد يحتوي على ملفات JSON المولدة
- **`yemen_data_import.sql`** - ملف SQL المولد (يتم إنشاؤه تلقائياً)

## 🚀 كيفية الاستخدام

### المتطلبات الأساسية

1. **Python 3.7+** مثبت على النظام
2. **PostgreSQL** مثبت ويعمل
3. **قاعدة بيانات TECNODRIVE** منشأة ومهيأة

### خطوات التشغيل

#### الطريقة الأسهل - استيراد مباشر (Windows):
```batch
# تشغيل Script الاستيراد المباشر (لا يتطلب Python)
import_data_direct.bat
```

#### الطريقة المتقدمة - مع Python (Windows):
```batch
# تشغيل Script التلقائي الكامل
import_yemen_data.bat
```

#### في Linux/Mac:
```bash
# جعل الملف قابل للتنفيذ (مرة واحدة فقط)
chmod +x import_yemen_data.sh

# تشغيل Script التلقائي
./import_yemen_data.sh
```

#### تشغيل يدوي:
```bash
# 1. تحويل البيانات إلى SQL (إذا كان لديك Python)
python import_to_database.py

# 2. استيراد البيانات إلى قاعدة البيانات
psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f yemen_data_import.sql
```

#### إذا لم يعمل Python:
```bash
# استخدم ملف SQL الجاهز مباشرة
psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f yemen_data_import.sql
```

## 📋 البيانات المستوردة

### 🏢 الشركات والمؤسسات
- شركات النقل اليمنية
- مؤسسات التوصيل
- شركات الخدمات اللوجستية

### 👥 المستخدمين
- **الركاب**: مستخدمين عاديين من مختلف المحافظات اليمنية
- **السائقين**: سائقين مسجلين مع تقييمات ومعلومات المركبات
- **المديرين**: حسابات إدارية للنظام

### 🚗 المركبات
- سيارات اقتصادية ومريحة وفاخرة
- دراجات نارية للتوصيل السريع
- فانات للمجموعات الكبيرة

### 🛣️ الرحلات
- رحلات تجريبية بين المدن اليمنية
- رحلات داخل المدن
- رحلات مكتملة ومعلقة

### 📦 الطرود
- طرود للتوصيل بين المحافظات
- شحنات تجارية
- طرود شخصية

## ⚙️ إعدادات قاعدة البيانات

### معلومات الاتصال الافتراضية:
- **المضيف**: localhost
- **المنفذ**: 5432
- **المستخدم**: postgres
- **قاعدة البيانات**: tecnodrive_main

### تغيير الإعدادات:
يمكنك تعديل إعدادات الاتصال في ملفات `.bat` أو `.sh` حسب بيئتك.

## 🔧 استكشاف الأخطاء

### خطأ: Python غير موجود
```bash
# تثبيت Python
# Windows: تحميل من python.org
# Ubuntu/Debian: sudo apt install python3
# CentOS/RHEL: sudo yum install python3
# macOS: brew install python3
```

### خطأ: PostgreSQL غير موجود
```bash
# تثبيت PostgreSQL
# Windows: تحميل من postgresql.org
# Ubuntu/Debian: sudo apt install postgresql postgresql-contrib
# CentOS/RHEL: sudo yum install postgresql postgresql-server
# macOS: brew install postgresql
```

### خطأ: قاعدة البيانات غير موجودة
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE tecnodrive_main;

-- تطبيق Schema
\c tecnodrive_main;
\i ../services/database/tecnodrive_schema.sql
```

### خطأ: ملفات JSON غير موجودة
```bash
# تشغيل مولد البيانات أولاً
python generate_yemen_data.py
```

## 📊 التحقق من البيانات

بعد الاستيراد، يمكنك التحقق من البيانات:

```sql
-- التحقق من عدد المستخدمين
SELECT user_type, COUNT(*) FROM users GROUP BY user_type;

-- التحقق من المركبات
SELECT vehicle_type, COUNT(*) FROM vehicles GROUP BY vehicle_type;

-- التحقق من الرحلات
SELECT status, COUNT(*) FROM trips GROUP BY status;

-- التحقق من الطرود
SELECT status, COUNT(*) FROM parcels GROUP BY status;
```

## 🎯 الخطوات التالية

بعد استيراد البيانات بنجاح:

1. **تشغيل التطبيق**: ابدأ خدمات TECNODRIVE
2. **اختبار الوظائف**: جرب الحجز والتوصيل
3. **مراجعة البيانات**: تحقق من صحة البيانات في لوحة التحكم
4. **إضافة بيانات جديدة**: استخدم التطبيق لإضافة المزيد من البيانات

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من ملفات السجل (logs)
2. راجع رسائل الخطأ في Terminal
3. تأكد من تشغيل جميع الخدمات المطلوبة
4. راجع وثائق قاعدة البيانات في `../docs/`
