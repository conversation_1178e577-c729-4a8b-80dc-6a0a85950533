# 🚗 TECNO DRIVE Platform - منصة تكنو درايف

## 🎯 نظرة عامة
منصة TECNO DRIVE هي حل شامل عند الطلب لخدمات النقل وتوصيل الطرود، مصممة لتوفير حلول نقل مخصصة للشركات والأفراد. تستفيد المنصة من التقنيات المتطورة مثل الذكاء الاصطناعي والتعلم الآلي والواقع المعزز لتبسيط العمليات وتعزيز الكفاءة وتقليل التكاليف وتقديم تجربة مستخدم آمنة وذكية.

## ✅ حالة المشروع
**المرحلة الأولى**: ✅ **مكتملة 100%** - لوحة التحكم المباشرة والعمليات الحية
**تنظيم المشروع**: ✅ **مكتمل بالكامل** - تم إعادة هيكلة وتنظيم المشروع بشكل احترافي

## 🎯 **التحديث الأخير - إعادة تنظيم شاملة للمشروع**

### ✅ **ما تم إنجازه:**
- ✅ نقل المجلدات المكررة إلى مجلد `المحذوف` المنظم
- ✅ إعادة هيكلة المشروع بطريقة احترافية ومنطقية
- ✅ تنظيم الملفات في الجذر وتصنيفها حسب الوظيفة
- ✅ دمج `kubernetes/` مع `infrastructure/kubernetes/`
- ✅ تنظيم التوثيق في مجلد `docs/project-organization/`
- ✅ نقل أدوات التكامل إلى `tools/integration/`
- ✅ تحسين هيكل السكريبتات في `scripts/startup/`

### 🗂️ **المجلدات المنقولة إلى "المحذوف":**
- `advanced-dashboard/` (مكرر مع `frontend/advanced-dashboard/`)
- `simple-advanced-dashboard/` (خادم مكرر)
- `frontend-server/` (خادم مكرر)
- `backups/` (نسخة احتياطية قديمة من 2025-07-14)

### 🚀 **الفوائد المحققة:**
- 🗂️ **هيكل منطقي**: تنظيم واضح ومنطقي للمجلدات
- 🧹 **مشروع نظيف**: إزالة التكرار والفوضى
- 📋 **توثيق محسن**: تنظيم شامل للتوثيق
- 🔄 **Live Data Viewer**: عرض البيانات المباشرة مع التحديث التلقائي
- 🔧 **System Health Monitoring**: مراقبة صحة النظام
- 📋 **Troubleshooting Tools**: أدوات استكشاف الأخطاء
- 🎯 **سهولة الصيانة**: هيكل أسهل للفهم والصيانة

**المرحلة الثانية**: 📋 **في مرحلة التخطيط** - المحافظ الإلكترونية والتحليلات المتقدمة

## 🚀 البدء السريع

### المتطلبات المسبقة
- Java 17+
- Node.js 16+
- Docker Desktop
- PostgreSQL 15+
- Redis 6+

### التشغيل السريع
```bash
# 1. تشخيص النظام
.\quick-diagnosis.ps1

# 2. تشغيل الخدمات الخلفية
.\start-all-services.ps1

# 3. تشغيل الواجهة الأمامية
.\start-frontend.ps1

# 4. الوصول للوحة التحكم
# http://localhost:3000/live-operations
```

## 🏗️ الهيكل المعماري
المنصة مبنية على هيكل الخدمات المصغرة (Microservices) لضمان القابلية للتوسع والمرونة وسهولة الصيانة.

### الخدمات الأساسية
- **خدمة المصادقة وإدارة المستخدمين** - Authentication & User Management Service
- **خدمة طلبات الرحلات** - Ride Request Service  
- **خدمة إدارة الأسطول والموقع** - Fleet Management & Location Tracking Service
- **خدمة الطرود والتوصيل** - Parcel & Delivery Management Service
- **خدمة الدفع والمحفظة** - Payment & Wallet Service
- **خدمة الإشعارات** - Notifications & Alerts Service
- **خدمة الإدارة المالية** - Financial Management Service
- **خدمة الموارد البشرية** - HR Management Service

### التطبيقات
- **تطبيق الركاب المحمول** - Passenger Mobile App
- **تطبيق السائقين المحمول** - Driver Mobile App  
- **تطبيق مرسلي الطرود** - Parcel Sender/Recipient App
- **بوابة موظفي الطرود** - Parcel Employee Web Portal
- **بوابة الإدارة** - Admin Web Portal

## التقنيات المستخدمة

### Backend
- **Java Spring Boot** - للخدمات المصغرة
- **PostgreSQL** - قاعدة البيانات الرئيسية
- **Redis** - للتخزين المؤقت والجلسات
- **Apache Kafka** - لمعالجة الأحداث
- **Docker** - للحاويات
- **Kubernetes** - لإدارة الحاويات

### Frontend
- **React Native** - للتطبيقات المحمولة
- **React.js** - للبوابات الإلكترونية
- **TypeScript** - للتطوير الآمن

### Infrastructure
- **AWS/Azure** - الحوسبة السحابية
- **Terraform** - Infrastructure as Code
- **Prometheus/Grafana** - المراقبة
- **ELK Stack** - السجلات والتحليلات

## نموذج SaaS
المنصة تقدم خدماتها كـ "Software as a Service" (SaaS) مما يعني أن أي شركة أو مؤسسة يمكنها طلب الخدمات التي تناسب احتياجاتها مع إمكانية ربط هذه الخدمات بتطبيقاتها الخاصة عبر واجهات برمجة التطبيقات المخصصة (APIs).

## الميزات الرئيسية

### للركاب
- طلب الرحلات وتتبعها في الوقت الفعلي
- تقدير الأسعار والدفع الآمن
- تاريخ الرحلات والتقييمات
- دعم المحافظ الرقمية اليمنية

### للسائقين
- إدارة الحالة والتوفر
- استقبال طلبات الرحلات والطرود
- تتبع الأرباح والسحوبات
- إدارة معلومات المركبة

### للإدارة
- لوحة تحكم شاملة
- إدارة المستخدمين والأسطول
- التقارير المالية والتشغيلية
- إدارة الموارد البشرية

## البدء السريع

### متطلبات النظام
- Java 17+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### تشغيل البيئة المحلية
```bash
# استنساخ المشروع
git clone https://github.com/tecno-drive/platform.git
cd platform

# تشغيل البنية التحتية
cd infra
docker-compose up -d

# تشغيل الخدمات
cd ../services
./start-services.sh

# تشغيل التطبيقات
cd ../frontend
npm install && npm start
```

## الهيكل التنظيمي للمشروع
```
tecno-drive/
├── services/              # الخدمات المصغرة
│   ├── auth-service/      # خدمة المصادقة
│   ├── ride-service/      # خدمة الرحلات
│   ├── fleet-service/     # خدمة الأسطول
│   ├── parcel-service/    # خدمة الطرود
│   ├── payment-service/   # خدمة الدفع
│   ├── notification-service/ # خدمة الإشعارات
│   ├── financial-service/ # خدمة المالية
│   ├── hr-service/        # خدمة الموارد البشرية
│   └── api-gateway/       # بوابة API
├── frontend/              # التطبيقات الأمامية
│   ├── passenger-app/     # تطبيق الركاب
│   ├── driver-app/        # تطبيق السائقين
│   ├── parcel-app/        # تطبيق الطرود
│   ├── employee-portal/   # بوابة الموظفين
│   └── admin-portal/      # بوابة الإدارة
├── infra/                 # البنية التحتية
│   ├── terraform/         # Terraform configs
│   ├── k8s/              # Kubernetes manifests
│   └── docker-compose.yml # البيئة المحلية
├── shared/                # المكتبات المشتركة
│   ├── common/           # الكود المشترك
│   ├── proto/            # Protocol Buffers
│   └── schemas/          # JSON Schemas
└── docs/                  # الوثائق
    ├── api/              # وثائق API
    ├── architecture/     # الهيكل المعماري
    └── deployment/       # دليل النشر
```

## المساهمة
نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للحصول على التفاصيل.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم
للحصول على الدعم، يرجى إنشاء issue في المستودع أو التواصل مع فريق التطوير.

---

**تم تطوير هذه المنصة بواسطة فريق TECNO DRIVE لتوفير حلول نقل ذكية ومبتكرة.**
