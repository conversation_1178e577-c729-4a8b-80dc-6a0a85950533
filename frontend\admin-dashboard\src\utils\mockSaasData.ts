import { 
  TenantDto, 
  CreateTenantRequest, 
  UpdateTenantRequest,
  SubscriptionDto,
  CreateSubscriptionRequest,
  BillingInvoiceDto,
  UsageAnalyticsDto,
  ApiResponse 
} from '../services/saasService';
import { createMockResponse, simulateApiDelay, generateMockId } from '../services/api';

// Mock Tenants Data
const mockTenants: TenantDto[] = [
  {
    id: '1',
    name: 'شركة الأعمال المتقدمة',
    domain: 'advanced-business.ye',
    phone: '+967712345678',
    email: '<EMAIL>',
    status: 'ACTIVE',
    createdAt: '2025-01-15T08:00:00Z',
    updatedAt: '2025-07-01T10:30:00Z',
    subscriptionsCount: 3,
    totalUsers: 45,
    billingBalance: 15000,
    monthlyRevenue: 25000,
    dataUsage: 120.5,
    apiCalls: 15000,
    lastActivity: '2025-07-09T14:30:00Z',
  },
  {
    id: '2',
    name: 'مؤسسة التجارة الذكية',
    domain: 'smart-trade.com',
    phone: '+967777888999',
    email: '<EMAIL>',
    status: 'ACTIVE',
    createdAt: '2025-02-20T09:15:00Z',
    updatedAt: '2025-07-05T16:45:00Z',
    subscriptionsCount: 2,
    totalUsers: 28,
    billingBalance: 8500,
    monthlyRevenue: 18000,
    dataUsage: 85.2,
    apiCalls: 9500,
    lastActivity: '2025-07-09T12:15:00Z',
  },
  {
    id: '3',
    name: 'شركة الحلول التقنية',
    domain: 'tech-solutions.ye',
    phone: '+967733444555',
    email: '<EMAIL>',
    status: 'INACTIVE',
    createdAt: '2025-03-10T11:30:00Z',
    updatedAt: '2025-06-15T09:20:00Z',
    subscriptionsCount: 1,
    totalUsers: 12,
    billingBalance: 0,
    monthlyRevenue: 0,
    dataUsage: 25.8,
    apiCalls: 2100,
    lastActivity: '2025-06-15T09:20:00Z',
  },
  {
    id: '4',
    name: 'مجموعة الابتكار الرقمي',
    domain: 'digital-innovation.com',
    phone: '+967755666777',
    email: '<EMAIL>',
    status: 'ACTIVE',
    createdAt: '2025-04-05T14:20:00Z',
    updatedAt: '2025-07-08T11:10:00Z',
    subscriptionsCount: 4,
    totalUsers: 67,
    billingBalance: 22000,
    monthlyRevenue: 35000,
    dataUsage: 180.3,
    apiCalls: 28000,
    lastActivity: '2025-07-09T15:45:00Z',
  },
];

// Mock Subscriptions Data
const mockSubscriptions: SubscriptionDto[] = [
  {
    id: 'sub-1',
    tenantId: '1',
    planId: 'plan-basic',
    planName: 'الخطة الأساسية',
    planType: 'BASIC',
    startDate: '2025-01-15T00:00:00Z',
    endDate: '2026-01-15T00:00:00Z',
    status: 'ACTIVE',
    seats: 25,
    monthlyPrice: 12500,
    features: ['إدارة المستخدمين', 'التقارير الأساسية', 'الدعم الفني'],
    createdAt: '2025-01-15T08:00:00Z',
    updatedAt: '2025-07-01T10:30:00Z',
  },
  {
    id: 'sub-2',
    tenantId: '1',
    planId: 'plan-premium',
    planName: 'الخطة المتقدمة',
    planType: 'PREMIUM',
    startDate: '2025-03-01T00:00:00Z',
    endDate: '2026-03-01T00:00:00Z',
    status: 'ACTIVE',
    seats: 20,
    monthlyPrice: 18000,
    features: ['جميع مميزات الأساسية', 'التحليلات المتقدمة', 'API مخصص', 'دعم أولوية'],
    createdAt: '2025-03-01T09:00:00Z',
    updatedAt: '2025-07-01T10:30:00Z',
  },
  {
    id: 'sub-3',
    tenantId: '2',
    planId: 'plan-basic',
    planName: 'الخطة الأساسية',
    planType: 'BASIC',
    startDate: '2025-02-20T00:00:00Z',
    endDate: '2026-02-20T00:00:00Z',
    status: 'ACTIVE',
    seats: 30,
    monthlyPrice: 15000,
    features: ['إدارة المستخدمين', 'التقارير الأساسية', 'الدعم الفني'],
    createdAt: '2025-02-20T09:15:00Z',
    updatedAt: '2025-07-05T16:45:00Z',
  },
];

// Mock Invoices Data
const mockInvoices: BillingInvoiceDto[] = [
  {
    id: 'inv-1',
    tenantId: '1',
    invoiceNumber: 'INV-2025-001',
    amount: 30500,
    currency: 'YER',
    status: 'PAID',
    issueDate: '2025-07-01T00:00:00Z',
    dueDate: '2025-07-31T00:00:00Z',
    paidDate: '2025-07-15T10:30:00Z',
    items: [
      {
        description: 'الخطة الأساسية - 25 مقعد',
        quantity: 1,
        unitPrice: 12500,
        totalPrice: 12500,
      },
      {
        description: 'الخطة المتقدمة - 20 مقعد',
        quantity: 1,
        unitPrice: 18000,
        totalPrice: 18000,
      },
    ],
  },
  {
    id: 'inv-2',
    tenantId: '1',
    invoiceNumber: 'INV-2025-002',
    amount: 30500,
    currency: 'YER',
    status: 'PENDING',
    issueDate: '2025-08-01T00:00:00Z',
    dueDate: '2025-08-31T00:00:00Z',
    items: [
      {
        description: 'الخطة الأساسية - 25 مقعد',
        quantity: 1,
        unitPrice: 12500,
        totalPrice: 12500,
      },
      {
        description: 'الخطة المتقدمة - 20 مقعد',
        quantity: 1,
        unitPrice: 18000,
        totalPrice: 18000,
      },
    ],
  },
];

// Mock Usage Analytics Data
const mockUsageAnalytics: { [key: string]: UsageAnalyticsDto } = {
  '1': {
    tenantId: '1',
    period: 'last_30_days',
    activeUsers: 42,
    totalApiCalls: 15000,
    dataStorage: 120.5,
    bandwidth: 85.2,
    features: {
      'user_management': 1250,
      'reports': 850,
      'analytics': 650,
      'api_calls': 15000,
    },
  },
  '2': {
    tenantId: '2',
    period: 'last_30_days',
    activeUsers: 26,
    totalApiCalls: 9500,
    dataStorage: 85.2,
    bandwidth: 62.1,
    features: {
      'user_management': 780,
      'reports': 520,
      'analytics': 320,
      'api_calls': 9500,
    },
  },
};

// Mock SaaS Data Service
export const mockSaasData = {
  // Tenant Management
  async getTenants(params?: {
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<TenantDto[]>> {
    await simulateApiDelay();
    
    let filteredTenants = [...mockTenants];
    
    // Apply search filter
    if (params?.search) {
      const searchTerm = params.search.toLowerCase();
      filteredTenants = filteredTenants.filter(tenant =>
        tenant.name.toLowerCase().includes(searchTerm) ||
        tenant.domain.toLowerCase().includes(searchTerm) ||
        tenant.email.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply status filter
    if (params?.status && params.status !== 'ALL') {
      filteredTenants = filteredTenants.filter(tenant => tenant.status === params.status);
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTenants = filteredTenants.slice(startIndex, endIndex);
    
    return createMockResponse(paginatedTenants);
  },

  async getTenantById(tenantId: string): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();
    const tenant = mockTenants.find(t => t.id === tenantId);
    
    if (!tenant) {
      throw new Error('العميل غير موجود');
    }
    
    return createMockResponse(tenant);
  },

  async createTenant(tenantData: CreateTenantRequest): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();
    
    const newTenant: TenantDto = {
      id: generateMockId(),
      ...tenantData,
      status: tenantData.status || 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subscriptionsCount: 0,
      totalUsers: 0,
      billingBalance: 0,
      monthlyRevenue: 0,
      dataUsage: 0,
      apiCalls: 0,
      lastActivity: new Date().toISOString(),
    };
    
    mockTenants.push(newTenant);
    return createMockResponse(newTenant);
  },

  async updateTenant(tenantId: string, tenantData: UpdateTenantRequest): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();
    
    const tenantIndex = mockTenants.findIndex(t => t.id === tenantId);
    if (tenantIndex === -1) {
      throw new Error('العميل غير موجود');
    }
    
    const updatedTenant = {
      ...mockTenants[tenantIndex],
      ...tenantData,
      updatedAt: new Date().toISOString(),
    };
    
    mockTenants[tenantIndex] = updatedTenant;
    return createMockResponse(updatedTenant);
  },

  async deleteTenant(tenantId: string): Promise<ApiResponse<void>> {
    await simulateApiDelay();
    
    const tenantIndex = mockTenants.findIndex(t => t.id === tenantId);
    if (tenantIndex === -1) {
      throw new Error('العميل غير موجود');
    }
    
    mockTenants.splice(tenantIndex, 1);
    return createMockResponse(undefined);
  },

  // Subscription Management
  async getSubscriptions(tenantId: string): Promise<ApiResponse<SubscriptionDto[]>> {
    await simulateApiDelay();
    
    const tenantSubscriptions = mockSubscriptions.filter(sub => sub.tenantId === tenantId);
    return createMockResponse(tenantSubscriptions);
  },

  async createSubscription(subscriptionData: CreateSubscriptionRequest): Promise<ApiResponse<SubscriptionDto>> {
    await simulateApiDelay();
    
    const newSubscription: SubscriptionDto = {
      id: generateMockId(),
      ...subscriptionData,
      planName: 'خطة جديدة',
      planType: 'BASIC',
      status: 'ACTIVE',
      monthlyPrice: 15000,
      features: ['إدارة المستخدمين', 'التقارير الأساسية'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockSubscriptions.push(newSubscription);
    return createMockResponse(newSubscription);
  },

  // Billing Management
  async getBillingInvoices(tenantId: string): Promise<ApiResponse<BillingInvoiceDto[]>> {
    await simulateApiDelay();
    
    const tenantInvoices = mockInvoices.filter(inv => inv.tenantId === tenantId);
    return createMockResponse(tenantInvoices);
  },

  async generateInvoice(tenantId: string): Promise<ApiResponse<BillingInvoiceDto>> {
    await simulateApiDelay();
    
    const newInvoice: BillingInvoiceDto = {
      id: generateMockId(),
      tenantId,
      invoiceNumber: `INV-${Date.now()}`,
      amount: 25000,
      currency: 'YER',
      status: 'PENDING',
      issueDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      items: [
        {
          description: 'اشتراك شهري',
          quantity: 1,
          unitPrice: 25000,
          totalPrice: 25000,
        },
      ],
    };
    
    mockInvoices.push(newInvoice);
    return createMockResponse(newInvoice);
  },

  // Usage Analytics
  async getUsageAnalytics(tenantId: string, period: string): Promise<ApiResponse<UsageAnalyticsDto>> {
    await simulateApiDelay();
    
    const analytics = mockUsageAnalytics[tenantId] || {
      tenantId,
      period,
      activeUsers: 0,
      totalApiCalls: 0,
      dataStorage: 0,
      bandwidth: 0,
      features: {},
    };
    
    return createMockResponse(analytics);
  },
};
