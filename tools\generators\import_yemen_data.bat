@echo off
chcp 65001 >nul
echo ===================================================================
echo TECNODRIVE Platform - استيراد البيانات اليمنية إلى قاعدة البيانات
echo ===================================================================

echo.
echo 🔄 بدء عملية استيراد البيانات اليمنية...
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

:: التحقق من وجود ملف البيانات
if not exist "import_to_database.py" (
    echo ❌ خطأ: ملف import_to_database.py غير موجود
    echo تأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف التحويل

:: تشغيل script Python لتحويل البيانات
echo.
echo 🔧 جاري تحويل البيانات من JSON إلى SQL...
python import_to_database.py

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تحويل البيانات
    pause
    exit /b 1
)

echo ✅ تم تحويل البيانات بنجاح

:: التحقق من وجود ملف SQL المولد
if not exist "yemen_data_import.sql" (
    echo ❌ خطأ: لم يتم إنشاء ملف SQL
    pause
    exit /b 1
)

echo ✅ تم إنشاء ملف SQL

:: البحث عن PostgreSQL
echo.
echo 🔍 جاري البحث عن PostgreSQL...
set PSQL_PATH=""

if exist "C:\Program Files\PostgreSQL\16\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\16\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 16
) else if exist "C:\Program Files\PostgreSQL\15\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\15\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 15
) else if exist "C:\Program Files\PostgreSQL\14\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\14\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 14
) else (
    echo ⚠️ لم يتم العثور على PostgreSQL في المسارات الافتراضية
    echo سيتم محاولة استخدام psql من PATH
    set PSQL_PATH=psql
)

echo.
echo 📋 معلومات الاتصال بقاعدة البيانات:
echo    المضيف: localhost
echo    المنفذ: 5432
echo    المستخدم: postgres
echo    قاعدة البيانات: tecnodrive_main
echo.

:: السؤال عن المتابعة
set /p CONTINUE="هل تريد المتابعة مع استيراد البيانات إلى قاعدة البيانات؟ (y/n): "
if /i "%CONTINUE%" NEQ "y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

:: تشغيل استيراد البيانات
echo.
echo 🚀 جاري استيراد البيانات إلى قاعدة البيانات...
echo سيتم طلب كلمة مرور PostgreSQL...
echo.

%PSQL_PATH% -h localhost -p 5432 -U postgres -d tecnodrive_main -f yemen_data_import.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم استيراد البيانات اليمنية بنجاح!
    echo.
    echo 📊 البيانات المستوردة:
    echo    • الشركات والمؤسسات اليمنية
    echo    • المستخدمين والسائقين
    echo    • المركبات المسجلة
    echo    • الرحلات التجريبية
    echo    • الطرود والشحنات
    echo.
    echo 🎯 يمكنك الآن:
    echo    1. تشغيل تطبيق TECNODRIVE
    echo    2. اختبار الوظائف مع البيانات اليمنية
    echo    3. عرض البيانات في لوحة التحكم
    echo.
) else (
    echo.
    echo ❌ فشل في استيراد البيانات
    echo.
    echo 🔧 تحقق من:
    echo    1. تشغيل PostgreSQL على المنفذ 5432
    echo    2. وجود قاعدة البيانات tecnodrive_main
    echo    3. صحة كلمة مرور المستخدم postgres
    echo    4. تطبيق schema قاعدة البيانات أولاً
    echo.
    echo 💡 لتطبيق schema قاعدة البيانات:
    echo    psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f ../services/database/tecnodrive_schema.sql
    echo.
)

echo.
pause
