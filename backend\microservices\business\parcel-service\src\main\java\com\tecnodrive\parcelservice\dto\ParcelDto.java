package com.tecnodrive.parcelservice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * DTO لبيانات الطرد
 * يستخدم لنقل البيانات بين الطبقات والخدمات
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParcelDto {
    
    @JsonProperty("parcelId")
    private String parcelId;
    
    @NotBlank(message = "الباركود مطلوب")
    @Size(min = 12, max = 20, message = "الباركود يجب أن يكون بين 12-20 رقم")
    @JsonProperty("barcode")
    private String barcode;
    
    @NotBlank(message = "اسم المرسل مطلوب")
    @Size(max = 100, message = "اسم المرسل لا يجب أن يتجاوز 100 حرف")
    @JsonProperty("senderName")
    private String senderName;
    
    @NotBlank(message = "اسم المستقبل مطلوب")
    @Size(max = 100, message = "اسم المستقبل لا يجب أن يتجاوز 100 حرف")
    @JsonProperty("receiverName")
    private String receiverName;
    
    @NotBlank(message = "عنوان المرسل مطلوب")
    @Size(max = 255, message = "عنوان المرسل لا يجب أن يتجاوز 255 حرف")
    @JsonProperty("senderAddress")
    private String senderAddress;
    
    @NotBlank(message = "عنوان المستقبل مطلوب")
    @Size(max = 255, message = "عنوان المستقبل لا يجب أن يتجاوز 255 حرف")
    @JsonProperty("receiverAddress")
    private String receiverAddress;
    
    @NotNull(message = "الوزن مطلوب")
    @DecimalMin(value = "0.1", message = "الوزن يجب أن يكون أكبر من 0.1 كيلو")
    @DecimalMax(value = "50.0", message = "الوزن لا يجب أن يتجاوز 50 كيلو")
    @JsonProperty("weightKg")
    private Double weightKg;
    
    @NotNull(message = "أبعاد الطرد مطلوبة")
    @JsonProperty("dimensions")
    private DimensionsDto dimensions;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;
    
    @JsonProperty("estimatedDeliveryDate")
    private LocalDateTime estimatedDeliveryDate;
    
    @JsonProperty("actualDeliveryDate")
    private LocalDateTime actualDeliveryDate;
    
    @JsonProperty("notes")
    private String notes;
    
    @JsonProperty("priority")
    private String priority; // LOW, MEDIUM, HIGH, URGENT
    
    @JsonProperty("fragile")
    private Boolean fragile;
    
    @JsonProperty("insuranceValue")
    private Double insuranceValue;
    
    /**
     * DTO للأبعاد المدمجة
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DimensionsDto {
        
        @NotNull(message = "الطول مطلوب")
        @Min(value = 1, message = "الطول يجب أن يكون أكبر من 1 سم")
        @Max(value = 200, message = "الطول لا يجب أن يتجاوز 200 سم")
        @JsonProperty("lengthCm")
        private Integer lengthCm;
        
        @NotNull(message = "العرض مطلوب")
        @Min(value = 1, message = "العرض يجب أن يكون أكبر من 1 سم")
        @Max(value = 200, message = "العرض لا يجب أن يتجاوز 200 سم")
        @JsonProperty("widthCm")
        private Integer widthCm;
        
        @NotNull(message = "الارتفاع مطلوب")
        @Min(value = 1, message = "الارتفاع يجب أن يكون أكبر من 1 سم")
        @Max(value = 200, message = "الارتفاع لا يجب أن يتجاوز 200 سم")
        @JsonProperty("heightCm")
        private Integer heightCm;
        
        /**
         * حساب الحجم بالسنتيمتر المكعب
         */
        public Double getVolumeCm3() {
            if (lengthCm != null && widthCm != null && heightCm != null) {
                return lengthCm * widthCm * heightCm * 1.0;
            }
            return 0.0;
        }
        
        /**
         * حساب الوزن الحجمي (للشحن)
         */
        public Double getVolumetricWeight() {
            return getVolumeCm3() / 5000.0; // معامل تحويل قياسي
        }
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return parcelId != null && !parcelId.trim().isEmpty() &&
               barcode != null && !barcode.trim().isEmpty() &&
               senderName != null && !senderName.trim().isEmpty() &&
               receiverName != null && !receiverName.trim().isEmpty() &&
               weightKg != null && weightKg > 0 &&
               dimensions != null && dimensions.lengthCm != null && 
               dimensions.widthCm != null && dimensions.heightCm != null;
    }
    
    /**
     * حساب التكلفة المقدرة بناءً على الوزن والحجم
     */
    public Double getEstimatedCost() {
        if (!isValid()) return 0.0;
        
        double weightCost = weightKg * 10.0; // 10 ريال لكل كيلو
        double volumeCost = dimensions.getVolumetricWeight() * 8.0; // 8 ريال للوزن الحجمي
        double baseCost = 15.0; // تكلفة أساسية
        
        double totalCost = baseCost + Math.max(weightCost, volumeCost);
        
        // إضافة رسوم إضافية للطرود الهشة
        if (Boolean.TRUE.equals(fragile)) {
            totalCost += 20.0;
        }
        
        // إضافة رسوم التأمين
        if (insuranceValue != null && insuranceValue > 0) {
            totalCost += insuranceValue * 0.02; // 2% من قيمة التأمين
        }
        
        return Math.round(totalCost * 100.0) / 100.0; // تقريب لأقرب قرش
    }
}
