package com.tecnodrive.gateway.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Map;

/**
 * Fallback Controller for Circuit Breaker patterns
 * Provides fallback responses when services are unavailable
 */
@RestController
@RequestMapping("/fallback")
public class FallbackController {

    /**
     * General fallback for any service
     */
    @GetMapping
    public Mono<ResponseEntity<Map<String, Object>>> generalFallback() {
        Map<String, Object> response = Map.of(
                "error", "Service Temporarily Unavailable",
                "message", "The requested service is currently experiencing issues. Please try again later.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value()
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for ride service
     */
    @GetMapping("/ride-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> rideServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Ride Service Unavailable",
                "message", "The ride booking service is temporarily unavailable. Please try again in a few minutes.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "estimatedWaitTime", "5-10 minutes",
                        "alternativeOptions", new String[]{"Call customer service", "Try again later"}
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for payment service
     */
    @GetMapping("/payment-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> paymentServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Payment Service Unavailable",
                "message", "Payment processing is temporarily unavailable. Your transaction has been queued and will be processed when the service is restored.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "transactionStatus", "QUEUED",
                        "estimatedProcessingTime", "15-30 minutes",
                        "customerServiceNumber", "+967-1-234-5678"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for location service
     */
    @GetMapping("/location-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> locationServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Location Service Unavailable",
                "message", "Location tracking is temporarily unavailable. GPS data will be cached locally and synchronized when service is restored.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "cacheMode", "ENABLED",
                        "syncWhenAvailable", true,
                        "lastKnownLocation", "Available in mobile app"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for notification service
     */
    @GetMapping("/notification-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> notificationServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Notification Service Unavailable",
                "message", "Notification delivery is temporarily delayed. Messages will be sent when service is restored.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "deliveryStatus", "DELAYED",
                        "queuedMessages", "Will be delivered when service is available",
                        "alternativeContact", "Check mobile app for updates"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for authentication service
     */
    @GetMapping("/auth-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> authServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Authentication Service Unavailable",
                "message", "Authentication service is temporarily unavailable. Please try logging in again in a few minutes.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "retryAfter", "2-5 minutes",
                        "cachedSession", "May still be valid",
                        "emergencyAccess", "Contact customer service for urgent access"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for fleet service
     */
    @GetMapping("/fleet-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> fleetServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Fleet Management Unavailable",
                "message", "Fleet management service is temporarily unavailable. Vehicle assignments and tracking may be delayed.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "manualDispatch", "Available via radio",
                        "emergencyContact", "+967-1-234-5679",
                        "estimatedRestoration", "30-60 minutes"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Fallback for analytics service
     */
    @GetMapping("/analytics-unavailable")
    public Mono<ResponseEntity<Map<String, Object>>> analyticsServiceFallback() {
        Map<String, Object> response = Map.of(
                "error", "Analytics Service Unavailable",
                "message", "Analytics and reporting service is temporarily unavailable. Data collection continues in the background.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.SERVICE_UNAVAILABLE.value(),
                "fallbackData", Map.of(
                        "dataCollection", "ACTIVE",
                        "reportGeneration", "DELAYED",
                        "cachedReports", "Available for last 24 hours"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }

    /**
     * Rate limit exceeded fallback
     */
    @GetMapping("/rate-limit-exceeded")
    public Mono<ResponseEntity<Map<String, Object>>> rateLimitExceeded() {
        Map<String, Object> response = Map.of(
                "error", "Rate Limit Exceeded",
                "message", "Too many requests. Please slow down and try again later.",
                "timestamp", Instant.now().toString(),
                "status", HttpStatus.TOO_MANY_REQUESTS.value(),
                "fallbackData", Map.of(
                        "retryAfter", "60 seconds",
                        "maxRequestsPerMinute", "100",
                        "currentUsage", "Exceeded"
                )
        );
        
        return Mono.just(ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(response));
    }
}
