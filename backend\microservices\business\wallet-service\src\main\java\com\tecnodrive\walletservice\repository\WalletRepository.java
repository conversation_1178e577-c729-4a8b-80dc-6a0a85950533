package com.tecnodrive.walletservice.repository;

import com.tecnodrive.walletservice.entity.Wallet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Wallet Repository
 */
@Repository
public interface WalletRepository extends JpaRepository<Wallet, UUID> {

    /**
     * Find wallet by user ID
     */
    Optional<Wallet> findByUserId(UUID userId);

    /**
     * Find wallet by phone number
     */
    Optional<Wallet> findByPhoneNumber(String phoneNumber);

    /**
     * Check if wallet exists for user
     */
    boolean existsByUserId(UUID userId);

    /**
     * Check if wallet exists for phone number
     */
    boolean existsByPhoneNumber(String phoneNumber);

    /**
     * Find wallets by status
     */
    List<Wallet> findByStatus(Wallet.WalletStatus status);

    /**
     * Find wallets by status with pagination
     */
    Page<Wallet> findByStatus(Wallet.WalletStatus status, Pageable pageable);

    /**
     * Find wallets by verification level
     */
    List<Wallet> findByVerificationLevel(Wallet.VerificationLevel verificationLevel);

    /**
     * Find wallets with balance greater than amount
     */
    @Query("SELECT w FROM Wallet w WHERE w.balance > :amount")
    List<Wallet> findWalletsWithBalanceGreaterThan(@Param("amount") BigDecimal amount);

    /**
     * Find wallets with balance less than amount
     */
    @Query("SELECT w FROM Wallet w WHERE w.balance < :amount")
    List<Wallet> findWalletsWithBalanceLessThan(@Param("amount") BigDecimal amount);

    /**
     * Find wallets with balance between amounts
     */
    @Query("SELECT w FROM Wallet w WHERE w.balance BETWEEN :minAmount AND :maxAmount")
    List<Wallet> findWalletsWithBalanceBetween(@Param("minAmount") BigDecimal minAmount, 
                                               @Param("maxAmount") BigDecimal maxAmount);

    /**
     * Find active wallets
     */
    @Query("SELECT w FROM Wallet w WHERE w.status = 'ACTIVE'")
    List<Wallet> findActiveWallets();

    /**
     * Find active wallets with pagination
     */
    @Query("SELECT w FROM Wallet w WHERE w.status = 'ACTIVE'")
    Page<Wallet> findActiveWallets(Pageable pageable);

    /**
     * Find wallets that need daily reset
     */
    @Query("SELECT w FROM Wallet w WHERE w.dailySpent > 0 AND DATE(w.lastTransactionDate) < CURRENT_DATE")
    List<Wallet> findWalletsNeedingDailyReset();

    /**
     * Find wallets that need monthly reset
     */
    @Query("SELECT w FROM Wallet w WHERE w.monthlySpent > 0 AND EXTRACT(MONTH FROM w.lastTransactionDate) < EXTRACT(MONTH FROM CURRENT_DATE)")
    List<Wallet> findWalletsNeedingMonthlyReset();

    /**
     * Find locked wallets that can be unlocked
     */
    @Query("SELECT w FROM Wallet w WHERE w.lockedUntil IS NOT NULL AND w.lockedUntil < :now")
    List<Wallet> findWalletsToUnlock(@Param("now") LocalDateTime now);

    /**
     * Find wallets created between dates
     */
    @Query("SELECT w FROM Wallet w WHERE w.createdAt BETWEEN :startDate AND :endDate")
    List<Wallet> findWalletsCreatedBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    /**
     * Count wallets by status
     */
    @Query("SELECT COUNT(w) FROM Wallet w WHERE w.status = :status")
    Long countByStatus(@Param("status") Wallet.WalletStatus status);

    /**
     * Get total balance across all active wallets
     */
    @Query("SELECT SUM(w.balance) FROM Wallet w WHERE w.status = 'ACTIVE'")
    BigDecimal getTotalActiveBalance();

    /**
     * Get average balance across all active wallets
     */
    @Query("SELECT AVG(w.balance) FROM Wallet w WHERE w.status = 'ACTIVE'")
    BigDecimal getAverageActiveBalance();

    /**
     * Find wallets by phone number pattern
     */
    @Query("SELECT w FROM Wallet w WHERE w.phoneNumber LIKE %:pattern%")
    List<Wallet> findByPhoneNumberContaining(@Param("pattern") String pattern);

    /**
     * Search wallets by multiple criteria
     */
    @Query("SELECT w FROM Wallet w WHERE " +
           "(:phoneNumber IS NULL OR w.phoneNumber LIKE %:phoneNumber%) AND " +
           "(:status IS NULL OR w.status = :status) AND " +
           "(:verificationLevel IS NULL OR w.verificationLevel = :verificationLevel) AND " +
           "(:minBalance IS NULL OR w.balance >= :minBalance) AND " +
           "(:maxBalance IS NULL OR w.balance <= :maxBalance)")
    Page<Wallet> searchWallets(@Param("phoneNumber") String phoneNumber,
                               @Param("status") Wallet.WalletStatus status,
                               @Param("verificationLevel") Wallet.VerificationLevel verificationLevel,
                               @Param("minBalance") BigDecimal minBalance,
                               @Param("maxBalance") BigDecimal maxBalance,
                               Pageable pageable);

    /**
     * Find top wallets by balance
     */
    @Query("SELECT w FROM Wallet w WHERE w.status = 'ACTIVE' ORDER BY w.balance DESC")
    List<Wallet> findTopWalletsByBalance(Pageable pageable);

    /**
     * Find wallets with recent activity
     */
    @Query("SELECT w FROM Wallet w WHERE w.lastTransactionDate >= :since")
    List<Wallet> findWalletsWithRecentActivity(@Param("since") LocalDateTime since);

    /**
     * Find inactive wallets
     */
    @Query("SELECT w FROM Wallet w WHERE w.lastTransactionDate IS NULL OR w.lastTransactionDate < :since")
    List<Wallet> findInactiveWallets(@Param("since") LocalDateTime since);

    /**
     * Get wallet statistics
     */
    @Query("SELECT " +
           "COUNT(w) as totalWallets, " +
           "SUM(CASE WHEN w.status = 'ACTIVE' THEN 1 ELSE 0 END) as activeWallets, " +
           "SUM(CASE WHEN w.status = 'SUSPENDED' THEN 1 ELSE 0 END) as suspendedWallets, " +
           "SUM(CASE WHEN w.status = 'BLOCKED' THEN 1 ELSE 0 END) as blockedWallets, " +
           "SUM(w.balance) as totalBalance, " +
           "AVG(w.balance) as averageBalance " +
           "FROM Wallet w")
    Object[] getWalletStatistics();
}
