package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.NotificationLog;
import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;

/**
 * Notification Log Response DTO
 * 
 * Used for returning notification log information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationLogResponse {

    /**
     * Log ID
     */
    private String id;

    /**
     * User ID who received the notification
     */
    private String userId;

    /**
     * Template used for this notification
     */
    private String templateName;

    /**
     * Notification channel used
     */
    private NotificationTemplate.NotificationChannel channel;

    /**
     * Notification priority
     */
    private NotificationTemplate.NotificationPriority priority;

    /**
     * Delivery status
     */
    private NotificationLog.DeliveryStatus status;

    /**
     * Recipient address
     */
    private String recipientAddress;

    /**
     * Final message subject
     */
    private String messageSubject;

    /**
     * Final message content
     */
    private String messageContent;

    /**
     * Template variables used
     */
    private String templateVariables;

    /**
     * External provider message ID
     */
    private String externalMessageId;

    /**
     * Error message if delivery failed
     */
    private String errorMessage;

    /**
     * Number of delivery attempts
     */
    private Integer attemptCount;

    /**
     * Tenant ID
     */
    private String tenantId;

    /**
     * When the notification was sent
     */
    private Instant sentAt;

    /**
     * When the notification was delivered
     */
    private Instant deliveredAt;

    /**
     * When the notification was read/opened
     */
    private Instant readAt;
}
