package com.tecnodrive.analyticsservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Analytics Service Application
 *
 * This service handles:
 * - Business intelligence and analytics
 * - Report generation and export
 * - Data aggregation from multiple services
 * - Real-time dashboard metrics
 * - Financial analytics and KPIs
 * - Operational performance metrics
 * - User behavior analytics
 * - Geographic and temporal analysis
 * - Custom report templates
 * - Scheduled report generation
 * - Multi-format export (JSON, CSV, PDF, Excel, HTML)
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class AnalyticsServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AnalyticsServiceApplication.class, args);
    }
}
