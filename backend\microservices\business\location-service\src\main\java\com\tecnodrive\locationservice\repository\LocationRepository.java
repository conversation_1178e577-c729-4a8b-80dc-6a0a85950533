package com.tecnodrive.locationservice.repository;

import com.tecnodrive.locationservice.entity.Location;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface LocationRepository extends JpaRepository<Location, UUID> {
    
    // Find latest location for an entity
    Optional<Location> findTopByEntityIdAndIsActiveTrueOrderByTimestampDesc(String entityId);
    
    // Find all locations for an entity
    List<Location> findByEntityIdAndIsActiveTrueOrderByTimestampDesc(String entityId);
    
    // Find locations by entity type
    List<Location> findByEntityTypeAndIsActiveTrueOrderByTimestampDesc(String entityType);
    
    // Find locations within time range
    List<Location> findByEntityIdAndTimestampBetweenAndIsActiveTrueOrderByTimestampDesc(
            String entityId, LocalDateTime startTime, LocalDateTime endTime);
    
    // Find locations within radius using PostGIS
    @Query(value = "SELECT * FROM locations l WHERE " +
                   "ST_DWithin(l.coordinates, ST_GeomFromText(:point, 4326), :radiusMeters) " +
                   "AND l.is_active = true " +
                   "ORDER BY ST_Distance(l.coordinates, ST_GeomFromText(:point, 4326))",
           nativeQuery = true)
    List<Location> findLocationsWithinRadius(@Param("point") String point, 
                                           @Param("radiusMeters") double radiusMeters);
    
    // Find nearest locations using PostGIS
    @Query(value = "SELECT *, ST_Distance(coordinates, ST_GeomFromText(:point, 4326)) as distance " +
                   "FROM locations " +
                   "WHERE is_active = true " +
                   "ORDER BY coordinates <-> ST_GeomFromText(:point, 4326) " +
                   "LIMIT :limit",
           nativeQuery = true)
    List<Location> findNearestLocations(@Param("point") String point, @Param("limit") int limit);
    
    // Find locations by city
    List<Location> findByCityAndIsActiveTrueOrderByTimestampDesc(String city);
    
    // Find locations by country
    List<Location> findByCountryAndIsActiveTrueOrderByTimestampDesc(String country);
    
    // Count active locations by entity type
    @Query("SELECT COUNT(l) FROM Location l WHERE l.entityType = :entityType AND l.isActive = true")
    long countActiveLocationsByEntityType(@Param("entityType") String entityType);
    
    // Find locations updated after specific time
    List<Location> findByUpdatedAtAfterAndIsActiveTrueOrderByUpdatedAtDesc(LocalDateTime after);
    
    // Find stale locations (not updated for a while)
    @Query("SELECT l FROM Location l WHERE l.updatedAt < :cutoffTime AND l.isActive = true")
    List<Location> findStaleLocations(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Calculate distance between two points
    @Query(value = "SELECT ST_Distance(" +
                   "ST_GeomFromText('POINT(:lon1 :lat1)', 4326), " +
                   "ST_GeomFromText('POINT(:lon2 :lat2)', 4326)" +
                   ") as distance",
           nativeQuery = true)
    Double calculateDistance(@Param("lat1") double lat1, @Param("lon1") double lon1,
                           @Param("lat2") double lat2, @Param("lon2") double lon2);
    
    // Find locations along a route (within buffer of a line)
    @Query(value = "SELECT * FROM locations l WHERE " +
                   "ST_DWithin(l.coordinates, ST_GeomFromText(:lineString, 4326), :bufferMeters) " +
                   "AND l.is_active = true " +
                   "ORDER BY l.timestamp DESC",
           nativeQuery = true)
    List<Location> findLocationsAlongRoute(@Param("lineString") String lineString, 
                                         @Param("bufferMeters") double bufferMeters);
    
    // Find locations within polygon (delivery zone, service area, etc.)
    @Query(value = "SELECT * FROM locations l WHERE " +
                   "ST_Within(l.coordinates, ST_GeomFromText(:polygon, 4326)) " +
                   "AND l.is_active = true " +
                   "ORDER BY l.timestamp DESC",
           nativeQuery = true)
    List<Location> findLocationsWithinPolygon(@Param("polygon") String polygon);
    
    // Get location statistics by entity type
    @Query(value = "SELECT " +
                   "entity_type, " +
                   "COUNT(*) as total_locations, " +
                   "COUNT(DISTINCT entity_id) as unique_entities, " +
                   "AVG(accuracy) as avg_accuracy, " +
                   "MAX(timestamp) as latest_update " +
                   "FROM locations " +
                   "WHERE is_active = true " +
                   "GROUP BY entity_type",
           nativeQuery = true)
    List<Object[]> getLocationStatsByEntityType();
}
