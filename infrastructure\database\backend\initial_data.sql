-- =====================================================
-- TECNO DRIVE - Initial Data & RBAC Setup
-- البيانات الأولية ونظام الأدوار والصلاحيات
-- =====================================================

-- =====================================================
-- 1. ROLES (الأدوار)
-- =====================================================

INSERT INTO roles (role_name, display_name, description, is_system) VALUES
('super_admin', 'مدير النظام الرئيسي', 'صلاحيات كاملة على النظام', true),
('admin', 'مدير', 'صلاحيات إدارية عامة', true),
('manager', 'مدير قسم', 'إدارة قسم محدد', false),
('operator', 'مشغل', 'تشغيل العمليات اليومية', false),
('customer_service', 'خدمة العملاء', 'دعم ومساعدة العملاء', false),
('driver', 'سائق', 'سائق مركبة', false),
('captain', 'كابتن', 'مالك مركبة ومدير سائقين', false),
('customer', 'عميل', 'مستخدم عادي', true),
('guest', 'زائر', 'مستخدم غير مسجل', true);

-- =====================================================
-- 2. PERMISSIONS (الصلاحيات)
-- =====================================================

-- صلاحيات المستخدمين
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('users.create', 'إنشاء مستخدم', 'إنشاء مستخدم جديد', 'users', 'create'),
('users.read', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users', 'read'),
('users.update', 'تعديل مستخدم', 'تعديل بيانات المستخدم', 'users', 'update'),
('users.delete', 'حذف مستخدم', 'حذف مستخدم من النظام', 'users', 'delete'),
('users.manage_roles', 'إدارة أدوار المستخدمين', 'تعيين وإزالة الأدوار', 'users', 'manage_roles'),

-- صلاحيات الحجوزات
('bookings.create', 'إنشاء حجز', 'إنشاء حجز جديد', 'bookings', 'create'),
('bookings.read', 'عرض الحجوزات', 'عرض قائمة الحجوزات', 'bookings', 'read'),
('bookings.update', 'تعديل حجز', 'تعديل بيانات الحجز', 'bookings', 'update'),
('bookings.delete', 'حذف حجز', 'حذف حجز من النظام', 'bookings', 'delete'),
('bookings.cancel', 'إلغاء حجز', 'إلغاء حجز موجود', 'bookings', 'cancel'),
('bookings.confirm', 'تأكيد حجز', 'تأكيد الحجز', 'bookings', 'confirm'),

-- صلاحيات الطرود
('parcels.create', 'إنشاء طرد', 'إنشاء طرد جديد', 'parcels', 'create'),
('parcels.read', 'عرض الطرود', 'عرض قائمة الطرود', 'parcels', 'read'),
('parcels.update', 'تعديل طرد', 'تعديل بيانات الطرد', 'parcels', 'update'),
('parcels.delete', 'حذف طرد', 'حذف طرد من النظام', 'parcels', 'delete'),
('parcels.track', 'تتبع طرد', 'تتبع حالة الطرد', 'parcels', 'track'),
('parcels.update_status', 'تحديث حالة الطرد', 'تحديث حالة الطرد', 'parcels', 'update_status'),

-- صلاحيات المدفوعات
('payments.create', 'إنشاء دفعة', 'إنشاء دفعة جديدة', 'payments', 'create'),
('payments.read', 'عرض المدفوعات', 'عرض قائمة المدفوعات', 'payments', 'read'),
('payments.update', 'تعديل دفعة', 'تعديل بيانات الدفعة', 'payments', 'update'),
('payments.refund', 'استرداد دفعة', 'استرداد مبلغ مدفوع', 'payments', 'refund'),

-- صلاحيات المركبات والسائقين
('vehicles.create', 'إنشاء مركبة', 'إنشاء مركبة جديدة', 'vehicles', 'create'),
('vehicles.read', 'عرض المركبات', 'عرض قائمة المركبات', 'vehicles', 'read'),
('vehicles.update', 'تعديل مركبة', 'تعديل بيانات المركبة', 'vehicles', 'update'),
('vehicles.delete', 'حذف مركبة', 'حذف مركبة من النظام', 'vehicles', 'delete'),

('drivers.create', 'إنشاء سائق', 'إنشاء سائق جديد', 'drivers', 'create'),
('drivers.read', 'عرض السائقين', 'عرض قائمة السائقين', 'drivers', 'read'),
('drivers.update', 'تعديل سائق', 'تعديل بيانات السائق', 'drivers', 'update'),
('drivers.delete', 'حذف سائق', 'حذف سائق من النظام', 'drivers', 'delete'),
('drivers.verify', 'التحقق من السائق', 'التحقق من هوية السائق', 'drivers', 'verify'),

-- صلاحيات التقارير والتحليلات
('reports.view', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports', 'view'),
('reports.export', 'تصدير التقارير', 'تصدير التقارير', 'reports', 'export'),
('analytics.view', 'عرض التحليلات', 'عرض لوحة التحليلات', 'analytics', 'view'),

-- صلاحيات الدعم
('support.create', 'إنشاء تذكرة دعم', 'إنشاء تذكرة دعم جديدة', 'support', 'create'),
('support.read', 'عرض تذاكر الدعم', 'عرض قائمة تذاكر الدعم', 'support', 'read'),
('support.update', 'تعديل تذكرة دعم', 'تعديل تذكرة دعم', 'support', 'update'),
('support.respond', 'الرد على تذكرة دعم', 'الرد على تذكرة دعم', 'support', 'respond'),
('support.close', 'إغلاق تذكرة دعم', 'إغلاق تذكرة دعم', 'support', 'close'),

-- صلاحيات النظام
('system.settings', 'إعدادات النظام', 'تعديل إعدادات النظام', 'system', 'settings'),
('system.backup', 'نسخ احتياطي', 'إنشاء نسخة احتياطية', 'system', 'backup'),
('system.logs', 'سجلات النظام', 'عرض سجلات النظام', 'system', 'logs'),
('system.maintenance', 'صيانة النظام', 'وضع النظام في وضع الصيانة', 'system', 'maintenance');

-- =====================================================
-- 3. ROLE PERMISSIONS (ربط الأدوار بالصلاحيات)
-- =====================================================

-- مدير النظام الرئيسي - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'super_admin';

-- مدير - معظم الصلاحيات عدا إعدادات النظام الحساسة
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'admin'
AND p.name NOT IN ('system.backup', 'system.maintenance', 'users.delete');

-- مدير قسم - صلاحيات محدودة
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'manager'
AND p.name IN (
    'users.read', 'users.update',
    'bookings.read', 'bookings.update', 'bookings.cancel', 'bookings.confirm',
    'parcels.read', 'parcels.update', 'parcels.update_status',
    'payments.read', 'payments.refund',
    'vehicles.read', 'vehicles.update',
    'drivers.read', 'drivers.update', 'drivers.verify',
    'reports.view', 'analytics.view',
    'support.read', 'support.respond', 'support.close'
);

-- مشغل - صلاحيات العمليات اليومية
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'operator'
AND p.name IN (
    'bookings.read', 'bookings.update', 'bookings.confirm',
    'parcels.read', 'parcels.update_status', 'parcels.track',
    'payments.read',
    'vehicles.read',
    'drivers.read',
    'support.read', 'support.respond'
);

-- خدمة العملاء
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'customer_service'
AND p.name IN (
    'users.read', 'users.update',
    'bookings.read', 'bookings.update', 'bookings.cancel',
    'parcels.read', 'parcels.track',
    'payments.read', 'payments.refund',
    'support.create', 'support.read', 'support.update', 'support.respond', 'support.close'
);

-- سائق
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'driver'
AND p.name IN (
    'bookings.read', 'bookings.update',
    'parcels.read', 'parcels.update_status',
    'support.create'
);

-- كابتن
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'captain'
AND p.name IN (
    'bookings.read', 'bookings.update',
    'parcels.read', 'parcels.update_status',
    'vehicles.create', 'vehicles.read', 'vehicles.update',
    'drivers.create', 'drivers.read', 'drivers.update',
    'payments.read',
    'reports.view',
    'support.create'
);

-- عميل
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'customer'
AND p.name IN (
    'bookings.create', 'bookings.read', 'bookings.update', 'bookings.cancel',
    'parcels.create', 'parcels.read', 'parcels.track',
    'payments.create', 'payments.read',
    'support.create'
);

-- =====================================================
-- 4. DEFAULT ADMIN USER
-- =====================================================

-- إنشاء مستخدم مدير افتراضي
INSERT INTO users (
    full_name, 
    email, 
    password_hash, 
    phone, 
    is_active, 
    is_verified, 
    email_verified_at,
    phone_verified_at
) VALUES (
    'مدير النظام',
    '<EMAIL>',
    crypt('TecnoDrive@2024', gen_salt('bf')), -- كلمة مرور مشفرة
    '+967777777777',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- تعيين دور مدير النظام للمستخدم الافتراضي
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.role_id
FROM users u, roles r
WHERE u.email = '<EMAIL>'
AND r.role_name = 'super_admin';

-- =====================================================
-- 5. SAMPLE DATA
-- =====================================================

-- مدن يمنية رئيسية للطرق
INSERT INTO routes (route_name, from_location, to_location, distance_km, estimated_duration, base_price, price_per_km) VALUES
('صنعاء - عدن', 'صنعاء', 'عدن', 363, '6 hours', 2500.00, 8.00),
('صنعاء - تعز', 'صنعاء', 'تعز', 256, '4 hours', 2000.00, 7.50),
('صنعاء - الحديدة', 'صنعاء', 'الحديدة', 226, '3.5 hours', 1800.00, 7.00),
('عدن - تعز', 'عدن', 'تعز', 120, '2 hours', 1200.00, 9.00),
('تعز - إب', 'تعز', 'إب', 54, '1 hour', 500.00, 8.50),
('صنعاء - إب', 'صنعاء', 'إب', 193, '3 hours', 1500.00, 7.50);

-- أسعار الطرود حسب المناطق
INSERT INTO parcel_rates (from_city, to_city, weight_from_kg, weight_to_kg, base_price, price_per_kg) VALUES
('صنعاء', 'عدن', 0.0, 5.0, 150.00, 25.00),
('صنعاء', 'عدن', 5.0, 20.0, 200.00, 20.00),
('صنعاء', 'عدن', 20.0, 50.0, 300.00, 15.00),
('صنعاء', 'تعز', 0.0, 5.0, 120.00, 20.00),
('صنعاء', 'تعز', 5.0, 20.0, 160.00, 16.00),
('صنعاء', 'تعز', 20.0, 50.0, 240.00, 12.00),
('صنعاء', 'الحديدة', 0.0, 5.0, 100.00, 18.00),
('صنعاء', 'الحديدة', 5.0, 20.0, 140.00, 14.00),
('صنعاء', 'الحديدة', 20.0, 50.0, 200.00, 10.00);

-- فئات تذاكر الدعم
INSERT INTO support_tickets (ticket_number, user_id, category, subject, description, priority, status)
SELECT 
    'DEMO-001',
    u.id,
    'تقني',
    'مشكلة في تسجيل الدخول',
    'لا أستطيع تسجيل الدخول إلى التطبيق',
    'medium',
    'open'
FROM users u WHERE u.email = '<EMAIL>' LIMIT 1;
