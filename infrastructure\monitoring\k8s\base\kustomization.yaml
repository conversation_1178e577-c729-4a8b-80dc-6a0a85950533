apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: tecnodrive-base

resources:
  - namespace.yaml
  - secrets.yaml
  - configmaps.yaml
  - rbac.yaml
  - network-policies.yaml

commonLabels:
  app.kubernetes.io/part-of: tecnodrive
  app.kubernetes.io/managed-by: kustomize

commonAnnotations:
  app.kubernetes.io/version: "1.0.0"

images:
  - name: tecnodrive/auth-service
    newTag: latest
  - name: tecnodrive/booking-service
    newTag: latest
  - name: tecnodrive/location-service
    newTag: latest
  - name: tecnodrive/notification-service
    newTag: latest

configMapGenerator:
  - name: tecnodrive-config
    literals:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - METRICS_ENABLED=true
      - TRACING_ENABLED=true

secretGenerator:
  - name: tecnodrive-secrets
    literals:
      - JWT_SECRET=your-jwt-secret-here
      - DATABASE_PASSWORD=your-db-password-here
      - KAFKA_PASSWORD=your-kafka-password-here
    type: Opaque

generatorOptions:
  disableNameSuffixHash: false
  labels:
    app.kubernetes.io/component: config
