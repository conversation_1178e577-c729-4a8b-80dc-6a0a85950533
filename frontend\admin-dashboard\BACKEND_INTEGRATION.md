# دليل ربط لوحة التحكم بالخدمات الخلفية

## نظرة عامة

تم تصميم لوحة التحكم الإدارية للعمل مع الخدمات الخلفية لنظام TECNO DRIVE. يمكن للنظام العمل في وضعين:

1. **الوضع المتصل**: عند توفر الخدمات الخلفية
2. **الوضع التجريبي**: باستخدام بيانات تجريبية عند عدم توفر الخدمات

## إعداد الاتصال بالخدمات

### 1. إعداد متغيرات البيئة

قم بتحديث ملف `.env` مع عناوين الخدمات الصحيحة:

```env
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_API_TIMEOUT=30000

# Service URLs
REACT_APP_RIDE_SERVICE_URL=http://localhost:8081
REACT_APP_FLEET_SERVICE_URL=http://localhost:8082
REACT_APP_AUTH_SERVICE_URL=http://localhost:8083
REACT_APP_PAYMENT_SERVICE_URL=http://localhost:8084
REACT_APP_ANALYTICS_SERVICE_URL=http://localhost:8085
REACT_APP_NOTIFICATION_SERVICE_URL=http://localhost:8086
REACT_APP_HR_SERVICE_URL=http://localhost:8087
REACT_APP_PARCEL_SERVICE_URL=http://localhost:8088
REACT_APP_USER_SERVICE_URL=http://localhost:8089

# Features
REACT_APP_ENABLE_MOCK_DATA=true
REACT_APP_ENABLE_LOGGING=true
```

### 2. تشغيل الخدمات الخلفية

تأكد من تشغيل الخدمات التالية:

- **Eureka Server** (Service Discovery)
- **API Gateway** (Port 8080)
- **Auth Service** (Port 8083)
- **Ride Service** (Port 8081)
- **Fleet Service** (Port 8082)
- **Payment Service** (Port 8084)
- **Analytics Service** (Port 8085)
- **Notification Service** (Port 8086)
- **HR Service** (Port 8087)
- **Parcel Service** (Port 8088)
- **User Service** (Port 8089)

## APIs المطلوبة

### خدمة المصادقة (Auth Service)

```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
PUT  /api/auth/profile
POST /api/auth/change-password
```

### خدمة الرحلات (Ride Service)

```
GET    /api/v1/rides
GET    /api/v1/rides/{id}
POST   /api/v1/rides/request
PUT    /api/v1/rides/{id}/status
POST   /api/v1/rides/{id}/assign-driver
POST   /api/v1/rides/{id}/complete
POST   /api/v1/rides/{id}/cancel
GET    /api/v1/rides/metrics
```

### خدمة الأسطول (Fleet Service)

```
GET    /api/fleet/vehicles
GET    /api/fleet/vehicles/{id}
POST   /api/fleet/vehicles
PUT    /api/fleet/vehicles/{id}
DELETE /api/fleet/vehicles/{id}
PATCH  /api/fleet/vehicles/{id}/status
POST   /api/fleet/vehicles/{id}/assign-driver
GET    /api/fleet/vehicles/metrics
```

### خدمة المدفوعات (Payment Service)

```
GET    /api/v1/payments
GET    /api/v1/payments/{id}
POST   /api/v1/payments/process
POST   /api/v1/payments/{id}/refund
GET    /api/v1/payments/metrics
```

### خدمة التحليلات (Analytics Service)

```
GET    /api/v1/analytics
GET    /api/v1/analytics/dashboard
GET    /api/v1/analytics/rides
GET    /api/v1/analytics/revenue
GET    /api/v1/analytics/drivers/performance
```

### خدمة المستخدمين (User Service)

```
GET    /api/v1/users
GET    /api/v1/users/{id}
POST   /api/v1/users
PUT    /api/v1/users/{id}
DELETE /api/v1/users/{id}
POST   /api/v1/users/{id}/suspend
POST   /api/v1/users/{id}/activate
```

## تنسيق الاستجابات

جميع APIs يجب أن ترجع استجابات بالتنسيق التالي:

```json
{
  "success": true,
  "message": "رسالة نجاح",
  "data": { /* البيانات */ },
  "total": 100,
  "page": 1,
  "limit": 10
}
```

في حالة الخطأ:

```json
{
  "success": false,
  "message": "رسالة الخطأ",
  "error": "تفاصيل الخطأ"
}
```

## المصادقة والتفويض

- يتم إرسال JWT token في header: `Authorization: Bearer <token>`
- يجب على جميع APIs التحقق من صحة التوكن
- في حالة انتهاء صلاحية التوكن، يتم إرجاع 401 Unauthorized

## فحص حالة الخدمات

يمكنك فحص حالة الخدمات من خلال:

1. زيارة صفحة "حالة الخدمات" في لوحة التحكم
2. استخدام endpoint: `GET /health` لكل خدمة

## الوضع التجريبي

عند تعيين `REACT_APP_ENABLE_MOCK_DATA=true`:

- سيتم استخدام بيانات تجريبية عند فشل الاتصال بالخدمات
- يمكن اختبار جميع وظائف لوحة التحكم بدون الحاجة للخدمات الخلفية
- مفيد للتطوير والاختبار

## استكشاف الأخطاء

### مشاكل الاتصال

1. تأكد من تشغيل جميع الخدمات
2. تحقق من عناوين الخدمات في `.env`
3. تأكد من عدم وجود مشاكل CORS
4. فحص logs المتصفح للأخطاء

### مشاكل المصادقة

1. تأكد من صحة بيانات تسجيل الدخول
2. تحقق من صلاحية JWT token
3. تأكد من تطابق تنسيق الاستجابات

### مشاكل البيانات

1. تحقق من تنسيق JSON للاستجابات
2. تأكد من وجود الحقول المطلوبة
3. فحص أنواع البيانات المرسلة

## الدعم

للحصول على المساعدة:

1. فحص console المتصفح للأخطاء
2. مراجعة logs الخدمات الخلفية
3. التأكد من تطابق إصدارات APIs

## ملاحظات مهمة

- تأكد من تشغيل Eureka Server أولاً
- API Gateway يجب أن يكون متاحاً على Port 8080
- جميع الخدمات يجب أن تكون مسجلة في Eureka
- استخدم HTTPS في الإنتاج
- قم بتحديث CORS settings للسماح بالوصول من لوحة التحكم
