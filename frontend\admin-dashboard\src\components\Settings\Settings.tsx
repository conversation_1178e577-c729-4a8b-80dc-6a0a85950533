import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  Palette as ThemeIcon,
  Storage as DatabaseIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [settings, setSettings] = useState({
    notifications: {
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      rideAlerts: true,
      paymentAlerts: true,
    },
    system: {
      language: 'ar',
      theme: 'light',
      timezone: 'Asia/Riyadh',
      currency: 'SAR',
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginAttempts: 5,
    },
    email: {
      smtpServer: 'smtp.gmail.com',
      smtpPort: 587,
      username: '',
      password: '',
      encryption: 'TLS',
    },
  });

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value,
      },
    }));
  };

  const handleSaveSettings = () => {
    // Here you would typically save to backend
    console.log('Saving settings:', settings);
    // Show success message
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        الإعدادات
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="عام" icon={<SettingsIcon />} />
        <Tab label="الإشعارات" icon={<NotificationsIcon />} />
        <Tab label="الأمان" icon={<SecurityIcon />} />
        <Tab label="البريد الإلكتروني" icon={<EmailIcon />} />
        <Tab label="قاعدة البيانات" icon={<DatabaseIcon />} />
      </Tabs>

      {/* General Settings */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <LanguageIcon />
                  إعدادات اللغة والمنطقة
                </Typography>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>اللغة</InputLabel>
                  <Select
                    value={settings.system.language}
                    label="اللغة"
                    onChange={(e) => handleSettingChange('system', 'language', e.target.value)}
                  >
                    <MenuItem value="ar">العربية</MenuItem>
                    <MenuItem value="en">English</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>المنطقة الزمنية</InputLabel>
                  <Select
                    value={settings.system.timezone}
                    label="المنطقة الزمنية"
                    onChange={(e) => handleSettingChange('system', 'timezone', e.target.value)}
                  >
                    <MenuItem value="Asia/Riyadh">الرياض (GMT+3)</MenuItem>
                    <MenuItem value="Asia/Dubai">دبي (GMT+4)</MenuItem>
                    <MenuItem value="UTC">UTC (GMT+0)</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>العملة</InputLabel>
                  <Select
                    value={settings.system.currency}
                    label="العملة"
                    onChange={(e) => handleSettingChange('system', 'currency', e.target.value)}
                  >
                    <MenuItem value="SAR">ريال سعودي (SAR)</MenuItem>
                    <MenuItem value="AED">درهم إماراتي (AED)</MenuItem>
                    <MenuItem value="USD">دولار أمريكي (USD)</MenuItem>
                  </Select>
                </FormControl>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ThemeIcon />
                  إعدادات المظهر
                </Typography>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>المظهر</InputLabel>
                  <Select
                    value={settings.system.theme}
                    label="المظهر"
                    onChange={(e) => handleSettingChange('system', 'theme', e.target.value)}
                  >
                    <MenuItem value="light">فاتح</MenuItem>
                    <MenuItem value="dark">داكن</MenuItem>
                    <MenuItem value="auto">تلقائي</MenuItem>
                  </Select>
                </FormControl>

                <Alert severity="info">
                  سيتم تطبيق التغييرات على جميع المستخدمين في النظام.
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Notifications Settings */}
      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              إعدادات الإشعارات
            </Typography>
            
            <List>
              <ListItem>
                <ListItemText
                  primary="إشعارات البريد الإلكتروني"
                  secondary="تلقي الإشعارات عبر البريد الإلكتروني"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications.emailNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemText
                  primary="إشعارات الرسائل النصية"
                  secondary="تلقي الإشعارات عبر الرسائل النصية"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications.smsNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'smsNotifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemText
                  primary="الإشعارات الفورية"
                  secondary="تلقي الإشعارات الفورية في المتصفح"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications.pushNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemText
                  primary="تنبيهات الرحلات"
                  secondary="تلقي تنبيهات عند طلب رحلة جديدة أو تغيير حالة الرحلة"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications.rideAlerts}
                    onChange={(e) => handleSettingChange('notifications', 'rideAlerts', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemText
                  primary="تنبيهات المدفوعات"
                  secondary="تلقي تنبيهات عند استلام أو معالجة المدفوعات"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications.paymentAlerts}
                    onChange={(e) => handleSettingChange('notifications', 'paymentAlerts', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Security Settings */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  إعدادات الأمان
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.security.twoFactorAuth}
                      onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
                    />
                  }
                  label="المصادقة الثنائية"
                  sx={{ mb: 2, display: 'block' }}
                />
                
                <TextField
                  fullWidth
                  label="مهلة انتهاء الجلسة (دقيقة)"
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                
                <TextField
                  fullWidth
                  label="انتهاء صلاحية كلمة المرور (يوم)"
                  type="number"
                  value={settings.security.passwordExpiry}
                  onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                
                <TextField
                  fullWidth
                  label="عدد محاولات تسجيل الدخول المسموحة"
                  type="number"
                  value={settings.security.loginAttempts}
                  onChange={(e) => handleSettingChange('security', 'loginAttempts', parseInt(e.target.value))}
                />
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  سياسات الأمان
                </Typography>
                
                <List dense>
                  <ListItem>
                    <ListItemText primary="كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل" />
                    <Chip label="مفعل" color="success" size="small" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="كلمة المرور يجب أن تحتوي على أرقام وحروف" />
                    <Chip label="مفعل" color="success" size="small" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="منع إعادة استخدام آخر 5 كلمات مرور" />
                    <Chip label="مفعل" color="success" size="small" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="قفل الحساب بعد محاولات فاشلة متعددة" />
                    <Chip label="مفعل" color="success" size="small" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Email Settings */}
      <TabPanel value={tabValue} index={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                إعدادات البريد الإلكتروني
              </Typography>
              <Button
                variant="outlined"
                onClick={() => setEmailDialogOpen(true)}
                startIcon={<EditIcon />}
              >
                تعديل الإعدادات
              </Button>
            </Box>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="خادم SMTP"
                  value={settings.email.smtpServer}
                  InputProps={{ readOnly: true }}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="منفذ SMTP"
                  value={settings.email.smtpPort}
                  InputProps={{ readOnly: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="اسم المستخدم"
                  value={settings.email.username}
                  InputProps={{ readOnly: true }}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="التشفير"
                  value={settings.email.encryption}
                  InputProps={{ readOnly: true }}
                />
              </Grid>
            </Grid>
            
            <Box sx={{ mt: 3 }}>
              <Button variant="outlined" startIcon={<EmailIcon />}>
                اختبار الاتصال
              </Button>
            </Box>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Database Settings */}
      <TabPanel value={tabValue} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  النسخ الاحتياطي
                </Typography>
                
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="النسخ الاحتياطي التلقائي"
                  sx={{ mb: 2, display: 'block' }}
                />
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>تكرار النسخ الاحتياطي</InputLabel>
                  <Select defaultValue="daily" label="تكرار النسخ الاحتياطي">
                    <MenuItem value="hourly">كل ساعة</MenuItem>
                    <MenuItem value="daily">يومياً</MenuItem>
                    <MenuItem value="weekly">أسبوعياً</MenuItem>
                    <MenuItem value="monthly">شهرياً</MenuItem>
                  </Select>
                </FormControl>
                
                <Button variant="contained" fullWidth>
                  إنشاء نسخة احتياطية الآن
                </Button>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  صيانة قاعدة البيانات
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemText
                      primary="تحسين الجداول"
                      secondary="آخر تحسين: منذ 3 أيام"
                    />
                    <ListItemSecondaryAction>
                      <Button size="small">تشغيل</Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText
                      primary="تنظيف السجلات القديمة"
                      secondary="آخر تنظيف: منذ أسبوع"
                    />
                    <ListItemSecondaryAction>
                      <Button size="small">تشغيل</Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText
                      primary="فحص سلامة البيانات"
                      secondary="آخر فحص: منذ يومين"
                    />
                    <ListItemSecondaryAction>
                      <Button size="small">تشغيل</Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Save Button */}
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          size="large"
          onClick={handleSaveSettings}
        >
          حفظ الإعدادات
        </Button>
      </Box>

      {/* Email Configuration Dialog */}
      <Dialog open={emailDialogOpen} onClose={() => setEmailDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تكوين البريد الإلكتروني</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="خادم SMTP"
                value={settings.email.smtpServer}
                onChange={(e) => handleSettingChange('email', 'smtpServer', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="منفذ SMTP"
                type="number"
                value={settings.email.smtpPort}
                onChange={(e) => handleSettingChange('email', 'smtpPort', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم المستخدم"
                value={settings.email.username}
                onChange={(e) => handleSettingChange('email', 'username', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="كلمة المرور"
                type="password"
                value={settings.email.password}
                onChange={(e) => handleSettingChange('email', 'password', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>نوع التشفير</InputLabel>
                <Select
                  value={settings.email.encryption}
                  label="نوع التشفير"
                  onChange={(e) => handleSettingChange('email', 'encryption', e.target.value)}
                >
                  <MenuItem value="TLS">TLS</MenuItem>
                  <MenuItem value="SSL">SSL</MenuItem>
                  <MenuItem value="None">بدون تشفير</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmailDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained" onClick={() => setEmailDialogOpen(false)}>
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Settings;
