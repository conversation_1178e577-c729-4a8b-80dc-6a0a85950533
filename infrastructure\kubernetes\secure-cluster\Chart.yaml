apiVersion: v2
name: tecno-drive-secure-cluster
description: TECNO DRIVE Secure Kubernetes Cluster with Integrated Security, Monitoring, and Resilience
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - kubernetes
  - security
  - monitoring
  - gatekeeper
  - argocd
  - grafana
  - prometheus
  - fluent-bit
  - siem
  - tecno-drive

maintainers:
  - name: TECNO DRIVE Team
    email: <EMAIL>

dependencies:
  - name: gatekeeper
    version: "3.14.0"
    repository: "https://open-policy-agent.github.io/gatekeeper/charts"
    condition: gatekeeper.enabled

  - name: prometheus
    version: "25.8.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled

  - name: grafana
    version: "7.0.0"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled

  - name: fluent-bit
    version: "0.21.0"
    repository: "https://fluent.github.io/helm-charts"
    condition: logging.fluentBit.enabled

annotations:
  category: Security
  licenses: Apache-2.0
  tecno-drive.com/version: "1.0.0"
  tecno-drive.com/component: "security-platform"
