# لوحة التحكم الإدارية - TECNO DRIVE

لوحة تحكم إدارية شاملة لنظام TECNO DRIVE مع تتبع مباشر عبر خرائط جوجل والتحديثات الفورية.

## 🚀 المميزات الجديدة المضافة

### 📍 التتبع المباشر عبر خرائط جوجل
- **تتبع الرحلات المباشر**: عرض موقع السائق والراكب في الوقت الحقيقي
- **تتبع الطرود المباشر**: متابعة مسار الطرد مع Timeline تفصيلي
- **خريطة الأسطول**: عرض جميع المركبات وحالتها على خريطة واحدة
- **رسم المسارات**: عرض الطرق والاتجاهات باستخدام Google Directions API

### 🔄 التحديثات الفورية
- **WebSocket Integration**: تحديثات فورية للمواقع والحالات
- **إشعارات مباشرة**: تنبيهات فورية للأحداث المهمة
- **تزامن البيانات**: تحديث البيانات في الوقت الحقيقي عبر جميع المكونات

### 📦 إدارة الطرود المحسّنة
- **واجهة شاملة**: إدارة كاملة للطرود والشحنات
- **Timeline تفاعلي**: عرض مسار الطرد بالتفصيل
- **إحصائيات متقدمة**: تقارير شاملة عن حالة الطرود

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Material-UI (MUI)** للتصميم
- **Redux Toolkit** لإدارة الحالة
- **React Router** للتوجيه
- **Socket.IO Client** للتحديثات المباشرة

### Maps & Real-time
- **Google Maps JavaScript API** للخرائط التفاعلية
- **Google Directions API** لرسم المسارات
- **WebSocket** للتحديثات الفورية
- **Geolocation API** لتتبع المواقع

### Charts & Analytics
- **Recharts** للمخططات البيانية
- **MUI X Data Grid** للجداول المتقدمة
- **MUI Lab Timeline** للعرض الزمني

## 📱 الصفحات والمكونات الجديدة

### 🗺️ مكونات الخرائط
| المكون | المسار | الوصف |
|--------|--------|--------|
| `LiveRideTracker` | `/rides/track/:id` | تتبع مباشر للرحلة مع خريطة |
| `LiveParcelTracker` | `/parcels/track/:id` | تتبع مباشر للطرد مع Timeline |
| `FleetMap` | `/fleet-map` | خريطة شاملة للأسطول |

### 📦 إدارة الطرود
| المكون | المسار | الوصف |
|--------|--------|--------|
| `ParcelsManagement` | `/parcels` | إدارة شاملة للطرود |
| `ParcelDetails` | `/parcels/:id` | تفاصيل الطرد |

### 🔌 خدمات WebSocket
| الخدمة | الوصف |
|--------|--------|
| `websocketService` | إدارة اتصالات WebSocket |
| `initializeWebSocket` | تهيئة الاتصال عند تسجيل الدخول |
| `cleanupWebSocket` | تنظيف الاتصال عند تسجيل الخروج |

## 🔧 التثبيت والتشغيل

### المتطلبات
- Node.js 16+
- npm أو yarn
- مفتاح Google Maps API

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd admin-dashboard
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

4. **تحديث ملف .env**
```env
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8080

# Google Maps
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# WebSocket
REACT_APP_WEBSOCKET_URL=ws://localhost:8080/ws

# Development
REACT_APP_ENABLE_MOCK_DATA=true
TSC_COMPILE_ON_ERROR=true
```

5. **تشغيل التطبيق**
```bash
npm start
```

## 🗺️ إعداد خرائط جوجل

### الحصول على مفتاح API
1. انتقل إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل APIs التالية:
   - Maps JavaScript API
   - Directions API
   - Places API
   - Geocoding API

### تكوين المفتاح
```env
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8
```

## 🔌 إعداد WebSocket

### تكوين الخادم
```env
REACT_APP_WEBSOCKET_URL=ws://localhost:8080/ws
```

### الأحداث المدعومة
- `ride_update:${rideId}` - تحديثات الرحلة
- `parcel_update:${trackingNumber}` - تحديثات الطرد
- `fleet_location_update` - تحديثات مواقع الأسطول
- `notification:${userId}` - إشعارات المستخدم

## 📋 APIs المحدّثة

### مسارات الخدمات الجديدة
```typescript
export const SERVICE_URLS = {
  RIDE_SERVICE: '/api/rides',
  FLEET_SERVICE: '/api/fleet/vehicles',
  PARCEL_SERVICE: '/api/parcels',
  LOCATION_SERVICE: '/api/location/logs',
  FINANCE_SERVICE: '/api/finance/invoices',
  SAAS_SERVICE: '/api/saas/tenants',
  AUDIT_SERVICE: '/api/audit/logs',
  // ... المزيد
};
```

### DTOs المحدّثة
- `RideDto` - بيانات الرحلة مع المواقع
- `ParcelDto` - بيانات الطرد مع التتبع
- `LocationUpdate` - تحديثات الموقع
- `VehicleLocation` - موقع المركبة

## 🎯 الوضع التجريبي

عند تعيين `REACT_APP_ENABLE_MOCK_DATA=true`:
- بيانات تجريبية شاملة لجميع الخدمات
- محاكاة تحديثات WebSocket
- خرائط تفاعلية مع بيانات وهمية
- جميع المميزات متاحة للاختبار

## 🔐 الأمان والمصادقة

- JWT Authentication مع تجديد تلقائي
- حماية مسارات الخرائط
- تشفير اتصالات WebSocket
- التحقق من صحة البيانات

## 📱 التصميم المتجاوب

- يعمل على جميع الأجهزة
- خرائط متجاوبة مع اللمس
- واجهة عربية (RTL) كاملة
- تحسين الأداء للهواتف

## 🚀 النشر

### بناء الإنتاج
```bash
npm run build
```

### متغيرات الإنتاج
```env
REACT_APP_API_BASE_URL=https://api.tecno-drive.com
REACT_APP_ENABLE_MOCK_DATA=false
REACT_APP_GOOGLE_MAPS_API_KEY=production_key
REACT_APP_WEBSOCKET_URL=wss://api.tecno-drive.com/ws
```

## 📋 الصفحات المتاحة

| الصفحة | المسار | الوصف |
|--------|--------|--------|
| لوحة المعلومات | `/` | نظرة عامة على النظام |
| إدارة الرحلات | `/rides` | عرض وإدارة الرحلات |
| تتبع الرحلة | `/rides/track/:id` | تتبع مباشر للرحلة |
| إدارة الأسطول | `/fleet` | إدارة المركبات |
| خريطة الأسطول | `/fleet-map` | خريطة مباشرة للأسطول |
| إدارة الطرود | `/parcels` | إدارة الطرود والشحنات |
| تتبع الطرد | `/parcels/track/:id` | تتبع مباشر للطرد |
| إدارة المستخدمين | `/users` | إدارة الركاب والسائقين |
| إدارة المدفوعات | `/payments` | المعاملات المالية |
| التحليلات | `/analytics` | تقارير وإحصائيات |
| حالة الخدمات | `/services` | مراقبة الخدمات الخلفية |

## 🎮 كيفية الاستخدام

### تتبع الرحلات المباشر
1. انتقل إلى صفحة إدارة الرحلات
2. اختر رحلة نشطة
3. انقر على زر "تتبع"
4. ستظهر خريطة مباشرة مع موقع السائق والراكب

### تتبع الطرود المباشر
1. انتقل إلى صفحة إدارة الطرود
2. اختر طرد في الطريق
3. انقر على زر "تتبع"
4. ستظهر خريطة مع Timeline مفصل

### خريطة الأسطول
1. انتقل إلى "خريطة الأسطول"
2. شاهد جميع المركبات على الخريطة
3. انقر على أي مركبة لرؤية التفاصيل
4. استخدم الفلاتر لعرض مركبات محددة

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع ملف `BACKEND_INTEGRATION.md`
2. تحقق من console المتصفح للأخطاء
3. راجع logs الخدمات الخلفية
4. تأكد من صحة مفتاح Google Maps

---

**TECNO DRIVE** - نظام إدارة النقل الذكي مع تتبع مباشر 🚗🗺️✨
