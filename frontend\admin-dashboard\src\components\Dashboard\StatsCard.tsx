import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';

interface StatsCardProps {
  title: string;
  value: string | number;
  growth?: number;
  icon: React.ReactNode;
  color: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  growth,
  icon,
  color,
}) => {
  const isPositiveGrowth = growth && growth > 0;

  return (
    <Card
      className="enhanced-card"
      sx={{
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-8px) scale(1.02)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, ${color}, ${color}88)`,
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box
            sx={{
              width: 64,
              height: 64,
              borderRadius: 3,
              background: `linear-gradient(135deg, ${color}20, ${color}40)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: color,
              fontSize: '1.5rem',
            }}
          >
            {icon}
          </Box>
          {growth !== undefined && (
            <Chip
              icon={isPositiveGrowth ? <TrendingUp /> : <TrendingDown />}
              label={`${growth > 0 ? '+' : ''}${growth.toFixed(1)}%`}
              sx={{
                background: isPositiveGrowth
                  ? 'linear-gradient(135deg, #48bb78, #38a169)'
                  : 'linear-gradient(135deg, #f56565, #e53e3e)',
                color: 'white',
                fontWeight: 600,
                '& .MuiChip-icon': {
                  color: 'white',
                },
              }}
              size="small"
            />
          )}
        </Box>

        <Typography
          variant="h3"
          sx={{
            fontWeight: 800,
            mb: 1,
            color: 'text.primary',
            background: `linear-gradient(135deg, ${color}, ${color}cc)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}
        </Typography>

        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            fontWeight: 600,
            fontSize: '1rem',
          }}
        >
          {title}
        </Typography>

        {/* Decorative elements */}
        <Box
          sx={{
            position: 'absolute',
            bottom: -20,
            right: -20,
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: `linear-gradient(135deg, ${color}10, ${color}20)`,
            opacity: 0.5,
          }}
        />
      </CardContent>
    </Card>
  );
};

export default StatsCard;
