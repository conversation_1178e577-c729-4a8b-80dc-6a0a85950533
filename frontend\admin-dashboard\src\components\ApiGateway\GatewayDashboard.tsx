import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Api as ApiIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  <PERSON>,
} from 'recharts';

interface GatewayMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  activeRoutes: number;
  activeServices: number;
  cacheHitRate: number;
  uptime: number;
}

interface ServiceHealth {
  id: string;
  name: string;
  status: 'HEALTHY' | 'DEGRADED' | 'DOWN';
  responseTime: number;
  errorRate: number;
  lastCheck: string;
}

interface RouteMetrics {
  route: string;
  requests: number;
  avgResponseTime: number;
  errorRate: number;
  status: 'ACTIVE' | 'INACTIVE';
}

const GatewayDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<GatewayMetrics | null>(null);
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth[]>([]);
  const [routeMetrics, setRouteMetrics] = useState<RouteMetrics[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockMetrics: GatewayMetrics = {
    totalRequests: 1250000,
    successfulRequests: 1187500,
    failedRequests: 62500,
    avgResponseTime: 145,
    activeRoutes: 24,
    activeServices: 12,
    cacheHitRate: 94.5,
    uptime: 99.8,
  };

  const mockServiceHealth: ServiceHealth[] = [
    {
      id: 'auth-service',
      name: 'خدمة المصادقة',
      status: 'HEALTHY',
      responseTime: 120,
      errorRate: 0.2,
      lastCheck: '2025-07-09T14:30:00Z',
    },
    {
      id: 'fleet-service',
      name: 'خدمة الأسطول',
      status: 'HEALTHY',
      responseTime: 180,
      errorRate: 0.5,
      lastCheck: '2025-07-09T14:30:00Z',
    },
    {
      id: 'rides-service',
      name: 'خدمة الرحلات',
      status: 'DEGRADED',
      responseTime: 350,
      errorRate: 2.1,
      lastCheck: '2025-07-09T14:30:00Z',
    },
    {
      id: 'notification-service',
      name: 'خدمة الإشعارات',
      status: 'HEALTHY',
      responseTime: 95,
      errorRate: 0.1,
      lastCheck: '2025-07-09T14:30:00Z',
    },
  ];

  const mockRouteMetrics: RouteMetrics[] = [
    { route: '/api/auth/**', requests: 450000, avgResponseTime: 120, errorRate: 0.2, status: 'ACTIVE' },
    { route: '/api/fleet/**', requests: 320000, avgResponseTime: 180, errorRate: 0.5, status: 'ACTIVE' },
    { route: '/api/rides/**', requests: 280000, avgResponseTime: 350, errorRate: 2.1, status: 'ACTIVE' },
    { route: '/api/notifications/**', requests: 200000, avgResponseTime: 95, errorRate: 0.1, status: 'ACTIVE' },
  ];

  // Performance data for charts
  const performanceData = [
    { time: '14:00', requests: 8500, responseTime: 140, errors: 45 },
    { time: '14:05', requests: 9200, responseTime: 135, errors: 52 },
    { time: '14:10', requests: 8800, responseTime: 150, errors: 38 },
    { time: '14:15', requests: 9500, responseTime: 145, errors: 61 },
    { time: '14:20', requests: 9100, responseTime: 142, errors: 43 },
    { time: '14:25', requests: 9800, responseTime: 138, errors: 55 },
    { time: '14:30', requests: 10200, responseTime: 145, errors: 48 },
  ];

  const statusDistribution = [
    { name: 'نجح', value: 95, color: '#4caf50' },
    { name: 'فشل', value: 5, color: '#f44336' },
  ];

  const serviceDistribution = [
    { name: 'سليم', value: 75, color: '#4caf50' },
    { name: 'متدهور', value: 20, color: '#ff9800' },
    { name: 'معطل', value: 5, color: '#f44336' },
  ];

  useEffect(() => {
    setMetrics(mockMetrics);
    setServiceHealth(mockServiceHealth);
    setRouteMetrics(mockRouteMetrics);
  }, []);

  const getHealthStatusChip = (status: string) => {
    const statusConfig = {
      HEALTHY: { label: 'سليم', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      DEGRADED: { label: 'متدهور', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
      DOWN: { label: 'معطل', color: 'error' as const, icon: <ErrorIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const handleRefresh = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!metrics) {
    return <LinearProgress />;
  }

  const successRate = ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            لوحة تحكم API Gateway
          </Typography>
          <Typography variant="body1" color="text.secondary">
            مراقبة وإدارة بوابة API في الوقت الفعلي
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          تحديث
        </Button>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <ApiIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {(metrics.totalRequests / 1000000).toFixed(1)}M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TrendingUpIcon fontSize="small" color="success" />
                    <Typography variant="caption" color="success.main">
                      +12.5%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <CheckCircleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {successRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل النجاح
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TrendingUpIcon fontSize="small" color="success" />
                    <Typography variant="caption" color="success.main">
                      +0.3%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <SpeedIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics.avgResponseTime}ms
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط زمن الاستجابة
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TrendingDownIcon fontSize="small" color="success" />
                    <Typography variant="caption" color="success.main">
                      -5ms
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <NetworkIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics.uptime}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل التوفر
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CheckCircleIcon fontSize="small" color="success" />
                    <Typography variant="caption" color="success.main">
                      مستقر
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                أداء API في الوقت الفعلي
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <RechartsTooltip />
                  <Area 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="requests" 
                    stackId="1"
                    stroke="#2196f3" 
                    fill="#2196f3"
                    fillOpacity={0.3}
                    name="الطلبات"
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="responseTime" 
                    stroke="#ff9800" 
                    strokeWidth={3}
                    name="زمن الاستجابة (ms)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    توزيع حالة الطلبات
                  </Typography>
                  <ResponsiveContainer width="100%" height={150}>
                    <PieChart>
                      <Pie
                        data={statusDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {statusDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    إحصائيات سريعة
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="المسارات النشطة"
                        secondary={metrics.activeRoutes}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="الخدمات النشطة"
                        secondary={metrics.activeServices}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="معدل نجاح الكاش"
                        secondary={`${metrics.cacheHitRate}%`}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Service Health and Route Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                صحة الخدمات
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>الخدمة</TableCell>
                      <TableCell>الحالة</TableCell>
                      <TableCell>زمن الاستجابة</TableCell>
                      <TableCell>معدل الخطأ</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {serviceHealth.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell>{service.name}</TableCell>
                        <TableCell>
                          {getHealthStatusChip(service.status)}
                        </TableCell>
                        <TableCell>{service.responseTime}ms</TableCell>
                        <TableCell>
                          <Typography 
                            variant="body2" 
                            color={service.errorRate > 1 ? 'error.main' : 'success.main'}
                          >
                            {service.errorRate}%
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                أداء المسارات
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>المسار</TableCell>
                      <TableCell>الطلبات</TableCell>
                      <TableCell>زمن الاستجابة</TableCell>
                      <TableCell>معدل الخطأ</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {routeMetrics.map((route, index) => (
                      <TableRow key={index}>
                        <TableCell sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          {route.route}
                        </TableCell>
                        <TableCell>{(route.requests / 1000).toFixed(0)}K</TableCell>
                        <TableCell>{route.avgResponseTime}ms</TableCell>
                        <TableCell>
                          <Typography 
                            variant="body2" 
                            color={route.errorRate > 1 ? 'error.main' : 'success.main'}
                          >
                            {route.errorRate}%
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status Alert */}
      <Box sx={{ mt: 3 }}>
        <Alert severity="success" icon={<CheckCircleIcon />}>
          جميع الأنظمة تعمل بشكل طبيعي. آخر فحص: {new Date().toLocaleString('ar-SA')}
        </Alert>
      </Box>
    </Box>
  );
};

export default GatewayDashboard;
