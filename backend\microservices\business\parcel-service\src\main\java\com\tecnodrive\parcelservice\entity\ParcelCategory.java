package com.tecnodrive.parcelservice.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Parcel Category Entity
 */
@Entity
@Table(name = "parcel_categories")
public class ParcelCategory {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(unique = true, nullable = false)
    private String name;

    @Column(name = "name_ar", nullable = false)
    private String nameAr;

    private String description;

    @Column(name = "max_weight_kg", precision = 8, scale = 2, nullable = false)
    private BigDecimal maxWeightKg = BigDecimal.valueOf(50.00);

    @Column(name = "max_dimensions_cm")
    private String maxDimensionsCm;

    @Column(name = "base_price", precision = 10, scale = 2, nullable = false)
    private BigDecimal basePrice = BigDecimal.ZERO;

    @Column(name = "price_per_kg", precision = 10, scale = 2, nullable = false)
    private BigDecimal pricePerKg = BigDecimal.ZERO;

    @Column(name = "price_per_km", precision = 10, scale = 2, nullable = false)
    private BigDecimal pricePerKm = BigDecimal.ZERO;

    @Column(name = "is_fragile")
    private Boolean isFragile = false;

    @Column(name = "requires_signature")
    private Boolean requiresSignature = false;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "created_at", updatable = false)
    private Instant createdAt = Instant.now();

    @Column(name = "updated_at")
    private Instant updatedAt = Instant.now();

    // Constructors
    public ParcelCategory() {}

    public ParcelCategory(String name, String nameAr, BigDecimal maxWeightKg, 
                         BigDecimal basePrice, BigDecimal pricePerKg, BigDecimal pricePerKm) {
        this.name = name;
        this.nameAr = nameAr;
        this.maxWeightKg = maxWeightKg;
        this.basePrice = basePrice;
        this.pricePerKg = pricePerKg;
        this.pricePerKm = pricePerKm;
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getNameAr() { return nameAr; }
    public void setNameAr(String nameAr) { this.nameAr = nameAr; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public BigDecimal getMaxWeightKg() { return maxWeightKg; }
    public void setMaxWeightKg(BigDecimal maxWeightKg) { this.maxWeightKg = maxWeightKg; }

    public String getMaxDimensionsCm() { return maxDimensionsCm; }
    public void setMaxDimensionsCm(String maxDimensionsCm) { this.maxDimensionsCm = maxDimensionsCm; }

    public BigDecimal getBasePrice() { return basePrice; }
    public void setBasePrice(BigDecimal basePrice) { this.basePrice = basePrice; }

    public BigDecimal getPricePerKg() { return pricePerKg; }
    public void setPricePerKg(BigDecimal pricePerKg) { this.pricePerKg = pricePerKg; }

    public BigDecimal getPricePerKm() { return pricePerKm; }
    public void setPricePerKm(BigDecimal pricePerKm) { this.pricePerKm = pricePerKm; }

    public Boolean getIsFragile() { return isFragile; }
    public void setIsFragile(Boolean isFragile) { this.isFragile = isFragile; }

    public Boolean getRequiresSignature() { return requiresSignature; }
    public void setRequiresSignature(Boolean requiresSignature) { this.requiresSignature = requiresSignature; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    public Instant getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public String toString() {
        return "ParcelCategory{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", nameAr='" + nameAr + '\'' +
                ", maxWeightKg=" + maxWeightKg +
                ", basePrice=" + basePrice +
                '}';
    }
}
