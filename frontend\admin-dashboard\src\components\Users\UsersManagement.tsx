import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  People,
  PersonAdd,
  Block,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchUsers } from '../../store/slices/usersSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`users-tabpanel-${index}`}
      aria-labelledby={`users-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

const UsersManagement: React.FC = () => {
  const dispatch = useDispatch();
  const { users, loading, totalUsers, activeUsers, drivers, passengers } = useSelector(
    (state: RootState) => state.users
  );

  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    dispatch(fetchUsers() as any);
  }, [dispatch]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const },
      INACTIVE: { label: 'غير نشط', color: 'default' as const },
      SUSPENDED: { label: 'موقوف', color: 'error' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getUserTypeChip = (userType: string) => {
    const typeConfig = {
      PASSENGER: { label: 'راكب', color: 'primary' as const },
      DRIVER: { label: 'سائق', color: 'secondary' as const },
      ADMIN: { label: 'مدير', color: 'warning' as const },
    };

    const config = typeConfig[userType as keyof typeof typeConfig] || { label: userType, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
      />
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'الاسم',
      width: 150,
    },
    {
      field: 'email',
      headerName: 'البريد الإلكتروني',
      width: 200,
    },
    {
      field: 'phone',
      headerName: 'رقم الهاتف',
      width: 150,
    },
    {
      field: 'userType',
      headerName: 'نوع المستخدم',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getUserTypeChip(params.value),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'rating',
      headerName: 'التقييم',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value ? `${params.value} ⭐` : 'غير مقيم'}
        </Typography>
      ),
    },
    {
      field: 'totalRides',
      headerName: 'عدد الرحلات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value || 0}
        </Typography>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ التسجيل',
      width: 150,
      valueGetter: (params) => {
        if (!params || !params.value) return 'غير محدد';
        try {
          return new Date(params.value).toLocaleDateString('ar-SA');
        } catch {
          return 'غير محدد';
        }
      },
    },
  ];

  // Mock data for demonstration
  const mockUsers = [
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '+967771234567',
      userType: 'PASSENGER',
      status: 'ACTIVE',
      rating: 4.8,
      totalRides: 25,
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'علي أحمد',
      email: '<EMAIL>',
      phone: '+967771234568',
      userType: 'DRIVER',
      status: 'ACTIVE',
      rating: 4.9,
      totalRides: 150,
      createdAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'فاطمة علي',
      email: '<EMAIL>',
      phone: '+967771234569',
      userType: 'PASSENGER',
      status: 'ACTIVE',
      rating: 4.7,
      totalRides: 12,
      createdAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'محمد سالم',
      email: '<EMAIL>',
      phone: '+967771234570',
      userType: 'DRIVER',
      status: 'SUSPENDED',
      rating: 4.2,
      totalRides: 89,
      createdAt: new Date().toISOString(),
    },
  ];

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.phone.includes(searchTerm);
    
    let matchesTab = true;
    switch (tabValue) {
      case 1:
        matchesTab = user.userType === 'PASSENGER';
        break;
      case 2:
        matchesTab = user.userType === 'DRIVER';
        break;
      case 3:
        matchesTab = user.userType === 'ADMIN';
        break;
      default:
        matchesTab = true;
    }
    
    return matchesSearch && matchesTab;
  });

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة المستخدمين
        </Typography>
        <Typography variant="body1" color="text.secondary">
          عرض وإدارة جميع المستخدمين في النظام
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalUsers || mockUsers.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي المستخدمين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {activeUsers || mockUsers.filter(u => u.status === 'ACTIVE').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مستخدمون نشطون
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'secondary.main' }}>
                {drivers || mockUsers.filter(u => u.userType === 'DRIVER').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                السائقون
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {passengers || mockUsers.filter(u => u.userType === 'PASSENGER').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الركاب
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في المستخدمين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <Button
              variant="contained"
              startIcon={<AddIcon />}
            >
              إضافة مستخدم
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="جميع المستخدمين" />
            <Tab label="الركاب" />
            <Tab label="السائقون" />
            <Tab label="المديرون" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredUsers}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredUsers.filter(u => u.userType === 'PASSENGER')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredUsers.filter(u => u.userType === 'DRIVER')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredUsers.filter(u => u.userType === 'ADMIN')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default UsersManagement;
