# TECNODRIVE Platform - Project Cleanup Script
# This script cleans up unnecessary files and organizes the project structure

param(
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Aggressive = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst = $true
)

# Function to log actions
function Write-CleanupLog {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Type) {
        "INFO" { "White" }
        "SUCCESS" { "Green" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "DELETED" { "Magenta" }
    }
    
    Write-Host "[$timestamp] [$Type] $Message" -ForegroundColor $color
}

# Function to safely remove files/folders
function Remove-SafelyWithLog {
    param(
        [string]$Path,
        [string]$Reason
    )
    
    if (Test-Path $Path) {
        if ($DryRun) {
            Write-CleanupLog "Would delete: $Path ($Reason)" "WARNING"
        } else {
            try {
                Remove-Item -Path $Path -Recurse -Force
                Write-CleanupLog "Deleted: $Path ($Reason)" "DELETED"
                return $true
            } catch {
                Write-CleanupLog "Failed to delete: $Path - $_" "ERROR"
                return $false
            }
        }
    }
    return $false
}

# Function to create backup
function Create-ProjectBackup {
    if (-not $BackupFirst) { return }
    
    Write-CleanupLog "Creating project backup..." "INFO"
    
    $backupDir = "backups\project-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    
    if (-not $DryRun) {
        try {
            New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
            
            # Backup important files
            $importantFiles = @(
                "docker-compose.yml",
                "package.json",
                "pom.xml",
                ".env*",
                "README.md"
            )
            
            foreach ($file in $importantFiles) {
                if (Test-Path $file) {
                    Copy-Item -Path $file -Destination $backupDir -Force
                }
            }
            
            Write-CleanupLog "Backup created: $backupDir" "SUCCESS"
        } catch {
            Write-CleanupLog "Failed to create backup: $_" "ERROR"
        }
    }
}

# Main cleanup function
function Start-ProjectCleanup {
    Write-CleanupLog "Starting TECNODRIVE project cleanup..." "INFO"
    Write-CleanupLog "Dry Run: $DryRun, Aggressive: $Aggressive" "INFO"
    
    $deletedCount = 0
    $totalSize = 0
    
    # 1. Clean up build artifacts
    Write-CleanupLog "Cleaning build artifacts..." "INFO"
    
    $buildPaths = @(
        "services\*\target",
        "frontend\*\node_modules",
        "frontend\*\dist",
        "frontend\*\build",
        "libs\*\target",
        "shared\*\target"
    )
    
    foreach ($pattern in $buildPaths) {
        Get-ChildItem -Path $pattern -Recurse -ErrorAction SilentlyContinue | ForEach-Object {
            if (Remove-SafelyWithLog $_.FullName "Build artifact") {
                $deletedCount++
            }
        }
    }
    
    # 2. Clean up log files
    Write-CleanupLog "Cleaning log files..." "INFO"
    
    $logPaths = @(
        "services\*\logs",
        "*.log",
        "logs\*"
    )
    
    foreach ($pattern in $logPaths) {
        Get-ChildItem -Path $pattern -Recurse -ErrorAction SilentlyContinue | ForEach-Object {
            if (Remove-SafelyWithLog $_.FullName "Log file") {
                $deletedCount++
            }
        }
    }
    
    # 3. Clean up duplicate and old SQL files
    Write-CleanupLog "Cleaning duplicate SQL files..." "INFO"
    
    $duplicateSqlFiles = @(
        "complete_yemen_database.sql",
        "fleet_data.sql",
        "simple_yemen_data.sql",
        "yemen_data_complete.sql",
        "schema.sql",
        "services\create_sample_data.sql",
        "services\tecnodrive_schema.sql"
    )
    
    foreach ($file in $duplicateSqlFiles) {
        if (Remove-SafelyWithLog $file "Duplicate/old SQL file") {
            $deletedCount++
        }
    }
    
    # 4. Clean up old scripts
    Write-CleanupLog "Cleaning old scripts..." "INFO"
    
    $oldScripts = @(
        "comprehensive-fix.sh",
        "fix-all-issues.sh",
        "fix-lombok-issues.sh",
        "build-and-test.sh"
    )
    
    foreach ($script in $oldScripts) {
        if (Remove-SafelyWithLog $script "Old script") {
            $deletedCount++
        }
    }
    
    # 5. Clean up backup files
    Write-CleanupLog "Cleaning backup files..." "INFO"
    
    $backupFiles = @(
        "pom.xml.backup",
        "services\*\pom-backup.xml",
        "services\*\pom-standalone.xml"
    )
    
    foreach ($pattern in $backupFiles) {
        Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue | ForEach-Object {
            if (Remove-SafelyWithLog $_.FullName "Backup file") {
                $deletedCount++
            }
        }
    }
    
    # 6. Clean up duplicate Docker Compose files
    Write-CleanupLog "Cleaning duplicate Docker files..." "INFO"
    
    $duplicateDockerFiles = @(
        "services\docker-compose-simple.yml",
        "infra\docker-compose.yml",
        "infra\docker-compose.test.yml"
    )
    
    foreach ($file in $duplicateDockerFiles) {
        if (Remove-SafelyWithLog $file "Duplicate Docker file") {
            $deletedCount++
        }
    }
    
    # 7. Clean up empty directories
    Write-CleanupLog "Cleaning empty directories..." "INFO"
    
    $emptyDirs = @(
        "services\services",
        "frontend\مجلد جديد"
    )
    
    foreach ($dir in $emptyDirs) {
        if (Test-Path $dir) {
            $items = Get-ChildItem -Path $dir -Recurse -ErrorAction SilentlyContinue
            if ($items.Count -eq 0) {
                if (Remove-SafelyWithLog $dir "Empty directory") {
                    $deletedCount++
                }
            }
        }
    }
    
    # 8. Aggressive cleanup (if enabled)
    if ($Aggressive) {
        Write-CleanupLog "Performing aggressive cleanup..." "WARNING"
        
        $aggressiveTargets = @(
            "apps",  # Duplicate of services
            "charts", # Helm charts (can be regenerated)
            "init-scripts", # Old init scripts
            "services\database", # Duplicate database files
            "services\simple-service" # Test service
        )
        
        foreach ($target in $aggressiveTargets) {
            if (Remove-SafelyWithLog $target "Aggressive cleanup") {
                $deletedCount++
            }
        }
    }
    
    # 9. Organize remaining files
    Write-CleanupLog "Organizing remaining files..." "INFO"
    
    if (-not $DryRun) {
        # Create organized directory structure
        $dirs = @(
            "docs",
            "scripts\database",
            "scripts\deployment",
            "scripts\monitoring"
        )
        
        foreach ($dir in $dirs) {
            if (-not (Test-Path $dir)) {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-CleanupLog "Created directory: $dir" "SUCCESS"
            }
        }
        
        # Move files to appropriate locations
        if (Test-Path "DEPLOYMENT.md") {
            Move-Item "DEPLOYMENT.md" "docs\" -Force
            Write-CleanupLog "Moved DEPLOYMENT.md to docs/" "SUCCESS"
        }
        
        if (Test-Path "scripts\backup.sh") {
            Move-Item "scripts\backup.sh" "scripts\database\" -Force
            Write-CleanupLog "Moved backup.sh to scripts/database/" "SUCCESS"
        }
    }
    
    # Summary
    Write-CleanupLog "Cleanup completed!" "SUCCESS"
    Write-CleanupLog "Files/directories processed: $deletedCount" "INFO"
    
    if ($DryRun) {
        Write-CleanupLog "This was a dry run. No files were actually deleted." "WARNING"
        Write-CleanupLog "Run without -DryRun to perform actual cleanup." "INFO"
    }
}

# Execute cleanup
try {
    Create-ProjectBackup
    Start-ProjectCleanup
    
    Write-CleanupLog "Project cleanup completed successfully!" "SUCCESS"
    Write-CleanupLog "Next steps:" "INFO"
    Write-CleanupLog "1. Review the changes" "INFO"
    Write-CleanupLog "2. Test the application" "INFO"
    Write-CleanupLog "3. Commit the cleaned project" "INFO"
    
} catch {
    Write-CleanupLog "Cleanup failed: $_" "ERROR"
    exit 1
}
