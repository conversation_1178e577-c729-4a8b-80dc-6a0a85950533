﻿server:
  port: 8080

spring:
  application:
    name: api-gateway
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: true # تحسين وقت بدء التشغيل
  data:
    redis:
      host: redis
      port: 6379
      password: TecnoDrive2025!Redis#Cache
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  cloud:
    gateway:
      enabled: true
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      # تحسين إعدادات WebSocket
      websocket:
        enabled: true
      # تحسين إعدادات الأداء
      httpclient:
        pool:
          max-connections: 100
          max-idle-time: 30s
        connect-timeout: 5000
        response-timeout: 30s

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

jwt:
  secret: mySecretKey123456789012345678901234567890

# Management and Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: true
    diskspace:
      enabled: true
