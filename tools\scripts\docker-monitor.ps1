# TecnoDrive Docker Monitor Script
# This script monitors the health and status of all TecnoDrive services

Write-Host "📊 TecnoDrive Docker Monitor" -ForegroundColor Blue
Write-Host "============================" -ForegroundColor Blue

# Navigate to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [int]$Port,
        [string]$HealthPath = "/health"
    )
    
    try {
        $url = "http://localhost:$Port$HealthPath"
        $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 3
        return @{
            Status = "Healthy"
            Color = "Green"
            Response = $response
        }
    } catch {
        return @{
            Status = "Unhealthy"
            Color = "Red"
            Response = $_.Exception.Message
        }
    }
}

function Show-ContainerStats {
    Write-Host ""
    Write-Host "🐳 Container Status:" -ForegroundColor Cyan
    docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Status}}\t{{.Ports}}"
}

function Show-ServiceHealth {
    Write-Host ""
    Write-Host "🏥 Service Health Check:" -ForegroundColor Cyan
    
    $services = @(
        @{Name="Eureka Server"; Port=8761}
        @{Name="API Gateway"; Port=8080}
        @{Name="Auth Service"; Port=8081}
        @{Name="User Service"; Port=8083}
        @{Name="Fleet Service"; Port=8084}
        @{Name="Location Service"; Port=8085}
        @{Name="Payment Service"; Port=8086}
        @{Name="Parcel Service"; Port=8087}
        @{Name="Notification Service"; Port=8088}
        @{Name="Analytics Service"; Port=8089}
        @{Name="HR Service"; Port=8097}
        @{Name="Financial Service"; Port=8098}
        @{Name="Wallet Service"; Port=8099}
        @{Name="Live Operations Service"; Port=8100}
        @{Name="Operations Management Service"; Port=8101}
        @{Name="Trip Tracking Service"; Port=8102}
        @{Name="Demand Analysis Service"; Port=8103}
    )

    $healthyCount = 0
    $totalCount = $services.Count

    foreach ($service in $services) {
        $health = Test-ServiceHealth -ServiceName $service.Name -Port $service.Port
        $status = if ($health.Status -eq "Healthy") { "✅" } else { "❌" }
        $color = $health.Color
        
        Write-Host "$status $($service.Name) (Port $($service.Port)) - $($health.Status)" -ForegroundColor $color
        
        if ($health.Status -eq "Healthy") {
            $healthyCount++
        }
    }

    Write-Host ""
    Write-Host "📈 Health Summary: $healthyCount/$totalCount services healthy" -ForegroundColor $(if ($healthyCount -eq $totalCount) { "Green" } else { "Yellow" })
}

function Show-ResourceUsage {
    Write-Host ""
    Write-Host "💻 Resource Usage:" -ForegroundColor Cyan
    
    try {
        $stats = docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" --filter "name=tecnodrive"
        $stats
    } catch {
        Write-Host "❌ Could not retrieve resource usage" -ForegroundColor Red
    }
}

function Show-Logs {
    param([string]$ServiceName)
    
    if ($ServiceName) {
        Write-Host "📋 Showing logs for ${ServiceName}:" -ForegroundColor Cyan
        docker-compose logs --tail=20 $ServiceName
    } else {
        Write-Host "📋 Recent logs from all services:" -ForegroundColor Cyan
        docker-compose logs --tail=5
    }
}

# Main monitoring loop
do {
    Clear-Host
    Write-Host "📊 TecnoDrive Docker Monitor - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Blue
    Write-Host "================================================================" -ForegroundColor Blue
    
    Show-ContainerStats
    Show-ServiceHealth
    Show-ResourceUsage
    
    Write-Host ""
    Write-Host "🔧 Commands:" -ForegroundColor Yellow
    Write-Host "1. Refresh (R)" -ForegroundColor White
    Write-Host "2. Show logs for specific service (L)" -ForegroundColor White
    Write-Host "3. Show all recent logs (A)" -ForegroundColor White
    Write-Host "4. Restart service (S)" -ForegroundColor White
    Write-Host "5. Exit (Q)" -ForegroundColor White
    
    Write-Host ""
    $choice = Read-Host "Enter your choice"
    
    switch ($choice.ToUpper()) {
        "R" {
            # Refresh - loop will continue
        }
        "L" {
            $serviceName = Read-Host "Enter service name (e.g., tecnodrive-auth)"
            Show-Logs -ServiceName $serviceName
            Read-Host "Press Enter to continue"
        }
        "A" {
            Show-Logs
            Read-Host "Press Enter to continue"
        }
        "S" {
            $serviceName = Read-Host "Enter service name to restart"
            Write-Host "🔄 Restarting $serviceName..." -ForegroundColor Yellow
            docker-compose restart $serviceName
            Write-Host "✅ Service restarted" -ForegroundColor Green
            Start-Sleep -Seconds 2
        }
        "Q" {
            Write-Host "👋 Exiting monitor..." -ForegroundColor Green
            exit
        }
        default {
            Start-Sleep -Seconds 5  # Auto-refresh every 5 seconds if no input
        }
    }
} while ($true)
