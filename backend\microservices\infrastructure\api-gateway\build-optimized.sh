#!/bin/bash

# =============================================================================
# TECNO DRIVE API Gateway - Advanced Build Script
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="tecno-drive/api-gateway"
VERSION=${1:-"latest"}
BUILD_TYPE=${2:-"optimized"} # optimized, distroless, or debug
REGISTRY=${DOCKER_REGISTRY:-"localhost:5000"}
PLATFORM=${DOCKER_PLATFORM:-"linux/amd64,linux/arm64"}

echo -e "${BLUE}🚀 Building TECNO DRIVE API Gateway${NC}"
echo -e "${BLUE}📦 Image: ${IMAGE_NAME}:${VERSION}${NC}"
echo -e "${BLUE}🏗️  Build Type: ${BUILD_TYPE}${NC}"
echo -e "${BLUE}🌐 Platform: ${PLATFORM}${NC}"

# Function to print step headers
print_step() {
    echo -e "\n${GREEN}==== $1 ====${NC}"
}

# Function to handle errors
handle_error() {
    echo -e "${RED}❌ Error: $1${NC}" >&2
    exit 1
}

# Validate build type
case $BUILD_TYPE in
    optimized|distroless|debug)
        ;;
    *)
        handle_error "Invalid build type: $BUILD_TYPE. Use: optimized, distroless, or debug"
        ;;
esac

# Check if Docker is running
print_step "Checking Docker"
if ! docker info >/dev/null 2>&1; then
    handle_error "Docker is not running or not accessible"
fi

# Check if buildx is available for multi-platform builds
if docker buildx version >/dev/null 2>&1; then
    BUILDX_AVAILABLE=true
    echo -e "${GREEN}✅ Docker Buildx available${NC}"
else
    BUILDX_AVAILABLE=false
    echo -e "${YELLOW}⚠️  Docker Buildx not available, building for current platform only${NC}"
fi

# Select Dockerfile based on build type
case $BUILD_TYPE in
    optimized)
        DOCKERFILE="Dockerfile.optimized"
        ;;
    distroless)
        DOCKERFILE="Dockerfile.distroless"
        ;;
    debug)
        DOCKERFILE="Dockerfile"
        ;;
esac

print_step "Building with $DOCKERFILE"

# Build arguments
BUILD_ARGS=(
    --build-arg "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
    --build-arg "VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
    --build-arg "VERSION=${VERSION}"
)

# Tags
TAGS=(
    "${IMAGE_NAME}:${VERSION}"
    "${IMAGE_NAME}:latest"
)

if [[ "$VERSION" != "latest" ]]; then
    TAGS+=("${REGISTRY}/${IMAGE_NAME}:${VERSION}")
fi

# Build command
if [[ "$BUILDX_AVAILABLE" == "true" ]] && [[ "$PLATFORM" == *","* ]]; then
    print_step "Multi-platform build with Buildx"
    
    # Create builder if it doesn't exist
    docker buildx create --name tecno-builder --use 2>/dev/null || docker buildx use tecno-builder
    
    # Build for multiple platforms
    docker buildx build \
        --platform "$PLATFORM" \
        --file "$DOCKERFILE" \
        "${BUILD_ARGS[@]}" \
        $(printf -- "--tag %s " "${TAGS[@]}") \
        --push \
        .
else
    print_step "Single platform build"
    
    # Build for current platform
    docker build \
        --file "$DOCKERFILE" \
        "${BUILD_ARGS[@]}" \
        $(printf -- "--tag %s " "${TAGS[@]}") \
        .
fi

print_step "Security Scanning"

# Security scan with Trivy (if available)
if command -v trivy >/dev/null 2>&1; then
    echo -e "${BLUE}🔍 Scanning image for vulnerabilities...${NC}"
    trivy image --severity HIGH,CRITICAL "${IMAGE_NAME}:${VERSION}" || {
        echo -e "${YELLOW}⚠️  Security scan found issues. Review before deploying.${NC}"
    }
else
    echo -e "${YELLOW}⚠️  Trivy not found. Install for security scanning.${NC}"
fi

print_step "Image Analysis"

# Show image size and layers
echo -e "${BLUE}📊 Image Information:${NC}"
docker images "${IMAGE_NAME}:${VERSION}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# Show image layers (if dive is available)
if command -v dive >/dev/null 2>&1; then
    echo -e "\n${BLUE}🔍 Analyzing image layers...${NC}"
    dive "${IMAGE_NAME}:${VERSION}" --ci
else
    echo -e "${YELLOW}💡 Install 'dive' for detailed layer analysis${NC}"
fi

print_step "Testing"

# Basic smoke test
echo -e "${BLUE}🧪 Running smoke test...${NC}"
CONTAINER_ID=$(docker run -d --rm -p 8080:8080 "${IMAGE_NAME}:${VERSION}")

# Wait for container to start
sleep 10

# Test health endpoint (if curl is available in the image)
if docker exec "$CONTAINER_ID" curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${YELLOW}⚠️  Health check failed or not available${NC}"
fi

# Cleanup test container
docker stop "$CONTAINER_ID" >/dev/null

print_step "Build Summary"

echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo -e "${BLUE}📦 Image: ${IMAGE_NAME}:${VERSION}${NC}"
echo -e "${BLUE}🏗️  Type: ${BUILD_TYPE}${NC}"
echo -e "${BLUE}📏 Size: $(docker images "${IMAGE_NAME}:${VERSION}" --format "{{.Size}}")${NC}"

if [[ "$BUILD_TYPE" == "distroless" ]]; then
    echo -e "\n${YELLOW}📝 Note: Distroless images require Kubernetes probes for health checks${NC}"
    echo -e "${YELLOW}   Health endpoint: /actuator/health/readiness${NC}"
fi

echo -e "\n${GREEN}🚀 Ready for deployment!${NC}"

# Optional: Push to registry
if [[ "${PUSH_TO_REGISTRY:-false}" == "true" ]]; then
    print_step "Pushing to Registry"
    for tag in "${TAGS[@]}"; do
        if [[ "$tag" == *"$REGISTRY"* ]]; then
            echo -e "${BLUE}📤 Pushing ${tag}...${NC}"
            docker push "$tag"
        fi
    done
fi

echo -e "\n${GREEN}🎉 All done!${NC}"
