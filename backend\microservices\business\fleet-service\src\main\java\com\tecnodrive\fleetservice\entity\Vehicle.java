package com.tecnodrive.fleetservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Vehicle Entity
 * 
 * Represents a vehicle in the fleet with comprehensive tracking capabilities.
 * Supports maintenance scheduling, driver assignment, and operational metrics.
 */
@Entity
@Table(name = "vehicles")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Vehicle {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Unique vehicle plate number
     */
    @Column(nullable = false, unique = true, length = 20)
    private String plateNumber;

    /**
     * Vehicle identification number (VIN)
     */
    @Column(unique = true, length = 50)
    private String vin;

    /**
     * Vehicle make/manufacturer
     */
    @Column(nullable = false, length = 50)
    private String make;

    /**
     * Vehicle model
     */
    @Column(nullable = false, length = 50)
    private String model;

    /**
     * Manufacturing year
     */
    @Column(nullable = false)
    private Integer year;

    /**
     * Vehicle color
     */
    @Column(length = 30)
    private String color;

    /**
     * Passenger capacity
     */
    @Column(nullable = false)
    private Integer capacity;

    /**
     * Vehicle type
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VehicleType vehicleType;

    /**
     * Fuel type
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FuelType fuelType;

    /**
     * Current vehicle status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private VehicleStatus status = VehicleStatus.AVAILABLE;

    /**
     * Current odometer reading (in kilometers)
     */
    @Column(precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal odometerReading = BigDecimal.ZERO;

    /**
     * Engine capacity (in liters)
     */
    @Column(precision = 4, scale = 2)
    private BigDecimal engineCapacity;

    /**
     * Transmission type
     */
    @Enumerated(EnumType.STRING)
    private TransmissionType transmissionType;

    /**
     * Currently assigned driver ID
     */
    private String assignedDriverId;

    /**
     * Company/Tenant ID
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * Vehicle registration date
     */
    private LocalDate registrationDate;

    /**
     * Registration expiry date
     */
    private LocalDate registrationExpiryDate;

    /**
     * Insurance policy number
     */
    private String insurancePolicyNumber;

    /**
     * Insurance expiry date
     */
    private LocalDate insuranceExpiryDate;

    /**
     * Last maintenance date
     */
    private LocalDate lastMaintenanceDate;

    /**
     * Next scheduled maintenance date
     */
    private LocalDate nextMaintenanceDate;

    /**
     * Odometer reading at last maintenance
     */
    @Column(precision = 10, scale = 2)
    private BigDecimal lastMaintenanceOdometer;

    /**
     * Purchase date
     */
    private LocalDate purchaseDate;

    /**
     * Purchase price
     */
    @Column(precision = 12, scale = 2)
    private BigDecimal purchasePrice;

    /**
     * Current market value
     */
    @Column(precision = 12, scale = 2)
    private BigDecimal currentValue;

    /**
     * Average fuel consumption (km per liter)
     */
    @Column(precision = 5, scale = 2)
    private BigDecimal fuelConsumption;

    /**
     * Vehicle notes/comments
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * GPS tracking device ID
     */
    private String gpsDeviceId;

    /**
     * Whether vehicle is active in the fleet
     */
    @Builder.Default
    private boolean isActive = true;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Vehicle Type Enum
     */
    public enum VehicleType {
        SEDAN,
        SUV,
        HATCHBACK,
        PICKUP_TRUCK,
        VAN,
        MINIBUS,
        BUS,
        MOTORCYCLE,
        SCOOTER,
        BICYCLE,
        TRUCK,
        OTHER
    }

    /**
     * Fuel Type Enum
     */
    public enum FuelType {
        GASOLINE,
        DIESEL,
        ELECTRIC,
        HYBRID,
        CNG,
        LPG,
        HYDROGEN,
        OTHER
    }

    /**
     * Vehicle Status Enum
     */
    public enum VehicleStatus {
        AVAILABLE,
        IN_USE,
        MAINTENANCE,
        OUT_OF_SERVICE,
        RESERVED,
        INSPECTION,
        REPAIR,
        RETIRED
    }

    /**
     * Transmission Type Enum
     */
    public enum TransmissionType {
        MANUAL,
        AUTOMATIC,
        CVT,
        SEMI_AUTOMATIC
    }

    /**
     * Check if vehicle needs maintenance
     */
    public boolean needsMaintenance() {
        if (nextMaintenanceDate != null) {
            return LocalDate.now().isAfter(nextMaintenanceDate) || 
                   LocalDate.now().isEqual(nextMaintenanceDate);
        }
        return false;
    }

    /**
     * Check if insurance is expiring soon
     */
    public boolean isInsuranceExpiringSoon(int warningDays) {
        if (insuranceExpiryDate != null) {
            return insuranceExpiryDate.isBefore(LocalDate.now().plusDays(warningDays));
        }
        return false;
    }

    /**
     * Check if registration is expiring soon
     */
    public boolean isRegistrationExpiringSoon(int warningDays) {
        if (registrationExpiryDate != null) {
            return registrationExpiryDate.isBefore(LocalDate.now().plusDays(warningDays));
        }
        return false;
    }

    /**
     * Check if vehicle is available for assignment
     */
    public boolean isAvailableForAssignment() {
        return isActive && 
               (status == VehicleStatus.AVAILABLE || status == VehicleStatus.RESERVED) &&
               assignedDriverId == null;
    }

    /**
     * Calculate vehicle age in years
     */
    public int getVehicleAge() {
        return LocalDate.now().getYear() - year;
    }

    /**
     * Get kilometers since last maintenance
     */
    public BigDecimal getKilometersSinceLastMaintenance() {
        if (lastMaintenanceOdometer != null && odometerReading != null) {
            return odometerReading.subtract(lastMaintenanceOdometer);
        }
        return BigDecimal.ZERO;
    }
}
