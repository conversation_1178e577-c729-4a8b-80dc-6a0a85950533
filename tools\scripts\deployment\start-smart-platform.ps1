# Smart TecnoDrive Platform Startup with Backend Integration
Write-Host "🚀 Starting TecnoDrive Platform with Smart Backend Integration" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to start a service if not running
function Start-ServiceIfNeeded {
    param([string]$ServiceName, [int]$Port, [string]$Path)
    
    if (Test-Port -Port $Port) {
        Write-Host "✅ $ServiceName already running on port $Port" -ForegroundColor Green
        return $true
    } else {
        Write-Host "🔄 Starting $ServiceName on port $Port..." -ForegroundColor Yellow
        if (Test-Path $Path) {
            try {
                Start-Process -FilePath "java" -ArgumentList "-jar", "$Path" -WindowStyle Hidden
                Start-Sleep -Seconds 3
                if (Test-Port -Port $Port) {
                    Write-Host "✅ $ServiceName started successfully" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "⚠️ $ServiceName may still be starting..." -ForegroundColor Yellow
                    return $false
                }
            } catch {
                Write-Host "❌ Failed to start $ServiceName" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ $ServiceName JAR not found at $Path" -ForegroundColor Red
            return $false
        }
    }
}

Write-Host "`n🔍 Checking Docker containers..." -ForegroundColor Yellow

# Check Docker containers
$dockerRunning = $false
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Select-String "tecnodrive"
    if ($containers) {
        Write-Host "✅ Docker containers are running:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
        $dockerRunning = $true
    } else {
        Write-Host "⚠️ No TecnoDrive Docker containers found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Docker is not available or not running" -ForegroundColor Red
}

# Start Docker containers if needed
if (-not $dockerRunning) {
    Write-Host "`n🐳 Starting Docker containers..." -ForegroundColor Yellow
    try {
        docker-compose up -d
        Start-Sleep -Seconds 10
        Write-Host "✅ Docker containers started" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to start Docker containers" -ForegroundColor Red
    }
}

Write-Host "`n🔍 Checking backend services..." -ForegroundColor Yellow

# Define services to check/start
$services = @(
    @{ Name = "Eureka Server"; Port = 8761; Path = "services/eureka-server/target/eureka-server-1.0.0.jar" },
    @{ Name = "API Gateway"; Port = 8080; Path = "services/api-gateway/target/api-gateway-1.0.0.jar" },
    @{ Name = "Auth Service"; Port = 8081; Path = "services/auth-service/target/auth-service-1.0.0.jar" },
    @{ Name = "Ride Service"; Port = 8082; Path = "services/ride-service/target/ride-service-1.0.0.jar" },
    @{ Name = "User Service"; Port = 8083; Path = "services/user-service/target/user-service-1.0.0.jar" },
    @{ Name = "Fleet Service"; Port = 8084; Path = "services/fleet-service/target/fleet-service-1.0.0.jar" },
    @{ Name = "Location Service"; Port = 8085; Path = "services/location-service/target/location-service-1.0.0.jar" },
    @{ Name = "Payment Service"; Port = 8086; Path = "services/payment-service/target/payment-service-1.0.0.jar" },
    @{ Name = "Parcel Service"; Port = 8087; Path = "services/parcel-service/target/parcel-service-1.0.0.jar" },
    @{ Name = "Notification Service"; Port = 8088; Path = "services/notification-service/target/notification-service-1.0.0.jar" },
    @{ Name = "Analytics Service"; Port = 8089; Path = "services/analytics-service/target/analytics-service-1.0.0.jar" },
    @{ Name = "HR Service"; Port = 8090; Path = "services/hr-service/target/hr-service-1.0.0.jar" },
    @{ Name = "Financial Service"; Port = 8091; Path = "services/financial-service/target/financial-service-1.0.0.jar" }
)

$runningServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    if (Test-Port -Port $service.Port) {
        Write-Host "✅ $($service.Name) is running on port $($service.Port)" -ForegroundColor Green
        $runningServices++
    } else {
        Write-Host "❌ $($service.Name) is not running on port $($service.Port)" -ForegroundColor Red
    }
}

Write-Host "`n📊 Service Status: $runningServices/$totalServices services running" -ForegroundColor Cyan

# Calculate service health percentage
$healthPercentage = [math]::Round(($runningServices / $totalServices) * 100, 1)

if ($healthPercentage -ge 80) {
    $statusColor = "Green"
    $statusText = "Excellent"
} elseif ($healthPercentage -ge 60) {
    $statusColor = "Yellow"
    $statusText = "Good"
} elseif ($healthPercentage -ge 40) {
    $statusColor = "DarkYellow"
    $statusText = "Fair"
} else {
    $statusColor = "Red"
    $statusText = "Poor"
}

Write-Host "🎯 Platform Health: $healthPercentage% - $statusText" -ForegroundColor $statusColor

Write-Host "`n🌐 Starting Frontend with Smart API Integration..." -ForegroundColor Yellow

# Navigate to frontend directory
$frontendPath = "frontend/admin-dashboard"
if (Test-Path $frontendPath) {
    Set-Location $frontendPath
    
    # Install dependencies if needed
    if (-not (Test-Path "node_modules")) {
        Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
        npm install
    }
    
    # Create or update .env file for smart integration
    $envContent = @"
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_WEBSOCKET_URL=ws://localhost:8085
REACT_APP_ENABLE_SMART_API=true
REACT_APP_HEALTH_CHECK_INTERVAL=30000
REACT_APP_AUTO_FALLBACK=true
"@
    
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "✅ Environment configured for smart API integration" -ForegroundColor Green
    
    # Start the frontend
    Write-Host "`n🚀 Starting frontend dashboard..." -ForegroundColor Green
    Write-Host "📱 Dashboard will be available at: http://localhost:3000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎯 Smart Features Enabled:" -ForegroundColor Yellow
    Write-Host "   • Automatic service health monitoring" -ForegroundColor White
    Write-Host "   • Smart fallback to mock data when services are down" -ForegroundColor White
    Write-Host "   • Real-time service status indicators" -ForegroundColor White
    Write-Host "   • Seamless switching between live and mock data" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 Service Integration Status:" -ForegroundColor Yellow
    Write-Host "   • Running Services: Use live backend data" -ForegroundColor Green
    Write-Host "   • Down Services: Automatically use mock data" -ForegroundColor Yellow
    Write-Host "   • Health checks: Every 30 seconds" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📊 Current Platform Status: $healthPercentage% healthy" -ForegroundColor $statusColor
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the frontend" -ForegroundColor Yellow
    
    npm start
} else {
    Write-Host "❌ Frontend directory not found: $frontendPath" -ForegroundColor Red
    exit 1
}
