"""
TECNO DRIVE Security Module
Comprehensive security implementation with OAuth2, encryption, and audit logging
"""

import os
import jwt
import bcrypt
import secrets
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, List
from functools import wraps
import asyncio
import hashlib
import hmac
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from fastapi import HTTPException, Depends, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import asyncpg
import aioredis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security Configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "tecnodrive_jwt_secret_key_2024_very_secure")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", Fernet.generate_key().decode())

# Initialize encryption
fernet = Fernet(ENCRYPTION_KEY.encode() if isinstance(ENCRYPTION_KEY, str) else ENCRYPTION_KEY)

class UserRole:
    ADMIN = "admin"
    MANAGER = "manager"
    OPERATOR = "operator"
    VIEWER = "viewer"

class Permission:
    # Vehicle permissions
    VIEW_VEHICLES = "view_vehicles"
    MANAGE_VEHICLES = "manage_vehicles"
    CONTROL_VEHICLES = "control_vehicles"
    
    # Ride permissions
    VIEW_RIDES = "view_rides"
    MANAGE_RIDES = "manage_rides"
    CANCEL_RIDES = "cancel_rides"
    
    # Analytics permissions
    VIEW_ANALYTICS = "view_analytics"
    EXPORT_DATA = "export_data"
    
    # Alert permissions
    VIEW_ALERTS = "view_alerts"
    MANAGE_ALERTS = "manage_alerts"
    
    # System permissions
    MANAGE_USERS = "manage_users"
    SYSTEM_CONFIG = "system_config"
    AUDIT_LOGS = "audit_logs"

# Role-Permission mapping
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        Permission.VIEW_VEHICLES, Permission.MANAGE_VEHICLES, Permission.CONTROL_VEHICLES,
        Permission.VIEW_RIDES, Permission.MANAGE_RIDES, Permission.CANCEL_RIDES,
        Permission.VIEW_ANALYTICS, Permission.EXPORT_DATA,
        Permission.VIEW_ALERTS, Permission.MANAGE_ALERTS,
        Permission.MANAGE_USERS, Permission.SYSTEM_CONFIG, Permission.AUDIT_LOGS
    ],
    UserRole.MANAGER: [
        Permission.VIEW_VEHICLES, Permission.MANAGE_VEHICLES,
        Permission.VIEW_RIDES, Permission.MANAGE_RIDES, Permission.CANCEL_RIDES,
        Permission.VIEW_ANALYTICS, Permission.EXPORT_DATA,
        Permission.VIEW_ALERTS, Permission.MANAGE_ALERTS
    ],
    UserRole.OPERATOR: [
        Permission.VIEW_VEHICLES, Permission.CONTROL_VEHICLES,
        Permission.VIEW_RIDES, Permission.MANAGE_RIDES,
        Permission.VIEW_ANALYTICS,
        Permission.VIEW_ALERTS, Permission.MANAGE_ALERTS
    ],
    UserRole.VIEWER: [
        Permission.VIEW_VEHICLES,
        Permission.VIEW_RIDES,
        Permission.VIEW_ANALYTICS,
        Permission.VIEW_ALERTS
    ]
}

class User(BaseModel):
    id: str
    username: str
    email: str
    role: str
    permissions: List[str]
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

class TokenData(BaseModel):
    user_id: str
    username: str
    role: str
    permissions: List[str]
    exp: datetime

class SecurityManager:
    """
    Comprehensive security manager for authentication, authorization, and encryption
    """
    
    def __init__(self, db_pool: asyncpg.Pool, redis_client: aioredis.Redis):
        self.db_pool = db_pool
        self.redis = redis_client
        self.security_bearer = HTTPBearer()
    
    # =====================================================
    # PASSWORD MANAGEMENT
    # =====================================================
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    # =====================================================
    # JWT TOKEN MANAGEMENT
    # =====================================================
    
    def create_access_token(self, user_data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        payload = {
            "user_id": user_data["id"],
            "username": user_data["username"],
            "role": user_data["role"],
            "permissions": ROLE_PERMISSIONS.get(user_data["role"], []),
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return token
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token"""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        
        payload = {
            "user_id": user_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return token
    
    def verify_token(self, token: str) -> Optional[TokenData]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            # Check if token is expired
            if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
                return None
            
            return TokenData(
                user_id=payload["user_id"],
                username=payload["username"],
                role=payload["role"],
                permissions=payload["permissions"],
                exp=datetime.fromtimestamp(payload["exp"])
            )
        except jwt.InvalidTokenError:
            return None
    
    async def revoke_token(self, token: str):
        """Add token to blacklist"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            exp = payload["exp"]
            
            # Store in Redis with expiration
            await self.redis.setex(
                f"blacklist:{token}",
                int(exp - datetime.utcnow().timestamp()),
                "revoked"
            )
        except jwt.InvalidTokenError:
            pass
    
    async def is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted"""
        return await self.redis.exists(f"blacklist:{token}")
    
    # =====================================================
    # USER AUTHENTICATION
    # =====================================================
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM users WHERE username = $1 AND is_active = true",
                    username
                )
                
                if not row:
                    return None
                
                if not self.verify_password(password, row["password_hash"]):
                    return None
                
                # Update last login
                await conn.execute(
                    "UPDATE users SET last_login = $1 WHERE id = $2",
                    datetime.utcnow(), row["id"]
                )
                
                return User(
                    id=str(row["id"]),
                    username=row["username"],
                    email=row["email"],
                    role=row["role"],
                    permissions=ROLE_PERMISSIONS.get(row["role"], []),
                    is_active=row["is_active"],
                    created_at=row["created_at"],
                    last_login=datetime.utcnow()
                )
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> User:
        """Get current authenticated user"""
        token = credentials.credentials
        
        # Check if token is blacklisted
        if await self.is_token_blacklisted(token):
            raise HTTPException(status_code=401, detail="Token has been revoked")
        
        # Verify token
        token_data = self.verify_token(token)
        if not token_data:
            raise HTTPException(status_code=401, detail="Invalid or expired token")
        
        # Get user from database
        async with self.db_pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM users WHERE id = $1 AND is_active = true",
                token_data.user_id
            )
            
            if not row:
                raise HTTPException(status_code=401, detail="User not found or inactive")
            
            return User(
                id=str(row["id"]),
                username=row["username"],
                email=row["email"],
                role=row["role"],
                permissions=token_data.permissions,
                is_active=row["is_active"],
                created_at=row["created_at"],
                last_login=row["last_login"]
            )
    
    # =====================================================
    # AUTHORIZATION
    # =====================================================
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Get current user from kwargs
                current_user = kwargs.get('current_user')
                if not current_user:
                    raise HTTPException(status_code=401, detail="Authentication required")
                
                if permission not in current_user.permissions:
                    raise HTTPException(status_code=403, detail="Insufficient permissions")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def require_role(self, required_role: str):
        """Decorator to require specific role"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                if not current_user:
                    raise HTTPException(status_code=401, detail="Authentication required")
                
                if current_user.role != required_role:
                    raise HTTPException(status_code=403, detail="Insufficient role")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    # =====================================================
    # DATA ENCRYPTION
    # =====================================================
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        encrypted = fernet.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            raise ValueError("Failed to decrypt data")
    
    def hash_sensitive_data(self, data: str, salt: str = None) -> str:
        """Hash sensitive data with salt"""
        if not salt:
            salt = secrets.token_hex(16)
        
        combined = f"{data}{salt}"
        hashed = hashlib.sha256(combined.encode()).hexdigest()
        return f"{salt}:{hashed}"
    
    # =====================================================
    # AUDIT LOGGING
    # =====================================================
    
    async def log_security_event(self, event_type: str, user_id: str = None, 
                                details: Dict[str, Any] = None, request: Request = None):
        """Log security-related events"""
        try:
            # Get client IP
            client_ip = "unknown"
            if request:
                client_ip = request.client.host
                if "x-forwarded-for" in request.headers:
                    client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
            
            # Create audit log entry
            log_entry = {
                "event_type": event_type,
                "user_id": user_id,
                "client_ip": client_ip,
                "user_agent": request.headers.get("user-agent") if request else None,
                "details": details or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Store in database
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO security_audit_log 
                    (event_type, user_id, client_ip, user_agent, details, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, 
                event_type, user_id, client_ip, 
                log_entry["user_agent"], log_entry["details"], datetime.utcnow())
            
            # Also log to application logs
            logger.info(f"Security event: {event_type} - User: {user_id} - IP: {client_ip}")
            
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
    
    # =====================================================
    # RATE LIMITING
    # =====================================================
    
    async def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Check if rate limit is exceeded"""
        try:
            current = await self.redis.get(f"rate_limit:{key}")
            
            if current is None:
                await self.redis.setex(f"rate_limit:{key}", window, 1)
                return True
            
            if int(current) >= limit:
                return False
            
            await self.redis.incr(f"rate_limit:{key}")
            return True
            
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            return True  # Allow on error
    
    # =====================================================
    # SESSION MANAGEMENT
    # =====================================================
    
    async def create_session(self, user_id: str, device_info: Dict[str, Any] = None) -> str:
        """Create user session"""
        session_id = self.generate_secure_token()
        
        session_data = {
            "user_id": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "device_info": device_info or {},
            "last_activity": datetime.utcnow().isoformat()
        }
        
        # Store session in Redis with expiration
        await self.redis.setex(
            f"session:{session_id}",
            ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            str(session_data)
        )
        
        return session_id
    
    async def invalidate_session(self, session_id: str):
        """Invalidate user session"""
        await self.redis.delete(f"session:{session_id}")
    
    async def invalidate_all_user_sessions(self, user_id: str):
        """Invalidate all sessions for a user"""
        pattern = f"session:*"
        async for key in self.redis.scan_iter(match=pattern):
            session_data = await self.redis.get(key)
            if session_data and user_id in session_data:
                await self.redis.delete(key)

# Global security manager instance
security_manager = None

def get_security_manager() -> SecurityManager:
    """Get security manager instance"""
    global security_manager
    if not security_manager:
        raise RuntimeError("Security manager not initialized")
    return security_manager

def init_security_manager(db_pool: asyncpg.Pool, redis_client: aioredis.Redis):
    """Initialize security manager"""
    global security_manager
    security_manager = SecurityManager(db_pool, redis_client)
    return security_manager

# Dependency functions for FastAPI
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
) -> User:
    """FastAPI dependency to get current user"""
    return await get_security_manager().get_current_user(credentials)

def require_permission(permission: str):
    """FastAPI dependency to require permission"""
    def dependency(current_user: User = Depends(get_current_user)):
        if permission not in current_user.permissions:
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        return current_user
    return dependency

def require_role(role: str):
    """FastAPI dependency to require role"""
    def dependency(current_user: User = Depends(get_current_user)):
        if current_user.role != role:
            raise HTTPException(status_code=403, detail="Insufficient role")
        return current_user
    return dependency
