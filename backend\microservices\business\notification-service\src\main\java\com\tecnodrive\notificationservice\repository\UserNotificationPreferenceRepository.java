package com.tecnodrive.notificationservice.repository;

import com.tecnodrive.notificationservice.entity.UserNotificationPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * User Notification Preference Repository
 * 
 * Data access layer for UserNotificationPreference entities
 */
@Repository
public interface UserNotificationPreferenceRepository extends JpaRepository<UserNotificationPreference, UUID> {

    /**
     * Find preferences by user ID
     */
    Optional<UserNotificationPreference> findByUserId(UUID userId);

    /**
     * Find preferences by tenant
     */
    List<UserNotificationPreference> findByTenantId(String tenantId);

    /**
     * Find users with email notifications enabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enableEmail = true AND p.enableNotifications = true")
    List<UserNotificationPreference> findUsersWithEmailEnabled();

    /**
     * Find users with SMS notifications enabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enableSms = true AND p.enableNotifications = true")
    List<UserNotificationPreference> findUsersWithSmsEnabled();

    /**
     * Find users with push notifications enabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enablePush = true AND p.enableNotifications = true")
    List<UserNotificationPreference> findUsersWithPushEnabled();

    /**
     * Find users with in-app notifications enabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enableInApp = true AND p.enableNotifications = true")
    List<UserNotificationPreference> findUsersWithInAppEnabled();

    /**
     * Find users with promotional notifications enabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enablePromotionalNotifications = true AND p.enableNotifications = true")
    List<UserNotificationPreference> findUsersWithPromotionalEnabled();

    /**
     * Find users by language preference
     */
    List<UserNotificationPreference> findByLanguage(String language);

    /**
     * Find users with notifications disabled
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.enableNotifications = false")
    List<UserNotificationPreference> findUsersWithNotificationsDisabled();

    /**
     * Count users by notification frequency
     */
    @Query("SELECT p.emailFrequency, COUNT(p) FROM UserNotificationPreference p GROUP BY p.emailFrequency")
    List<Object[]> countUsersByEmailFrequency();

    /**
     * Find users in quiet hours
     */
    @Query("SELECT p FROM UserNotificationPreference p WHERE " +
           "p.enableNightTimeNotifications = false AND " +
           "((p.quietHoursStart < p.quietHoursEnd AND :currentHour BETWEEN p.quietHoursStart AND p.quietHoursEnd) OR " +
           "(p.quietHoursStart > p.quietHoursEnd AND (:currentHour >= p.quietHoursStart OR :currentHour <= p.quietHoursEnd)))")
    List<UserNotificationPreference> findUsersInQuietHours(@Param("currentHour") int currentHour);

    /**
     * Check if user exists
     */
    boolean existsByUserId(UUID userId);
}
