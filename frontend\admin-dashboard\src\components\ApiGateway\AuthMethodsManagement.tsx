import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Security as SecurityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon,
  Key as KeyIcon,
  VpnKey as VpnKeyIcon,
  AccountCircle as AccountCircleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { apiGatewayService, AuthMethodDto, CreateAuthMethodRequest } from '../../services/apiGatewayService';

const AuthMethodsManagement: React.FC = () => {
  const [authMethods, setAuthMethods] = useState<AuthMethodDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [filterEnabled, setFilterEnabled] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openConfigDialog, setOpenConfigDialog] = useState(false);
  const [selectedAuthMethod, setSelectedAuthMethod] = useState<AuthMethodDto | null>(null);
  const [newAuthMethod, setNewAuthMethod] = useState<CreateAuthMethodRequest>({
    name: '',
    type: 'JWT',
    config: {},
    description: '',
  });

  // Load auth methods data
  const loadAuthMethods = async () => {
    try {
      setLoading(true);
      const response = await apiGatewayService.getAuthMethods();
      
      if (response.success && response.data) {
        setAuthMethods(response.data);
      }
    } catch (error) {
      console.error('Error loading auth methods:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAuthMethods();
  }, []);

  const getTypeChip = (type: string) => {
    const typeConfig = {
      JWT: { label: 'JWT', color: 'primary' as const, icon: <SecurityIcon fontSize="small" /> },
      OAUTH2: { label: 'OAuth2', color: 'info' as const, icon: <VpnKeyIcon fontSize="small" /> },
      API_KEY: { label: 'API Key', color: 'secondary' as const, icon: <KeyIcon fontSize="small" /> },
      BASIC: { label: 'Basic Auth', color: 'warning' as const, icon: <AccountCircleIcon fontSize="small" /> },
      LDAP: { label: 'LDAP', color: 'success' as const, icon: <AccountCircleIcon fontSize="small" /> },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      color: 'default' as const, 
      icon: <SecurityIcon fontSize="small" /> 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getStatusChip = (enabled: boolean) => {
    return (
      <Chip
        label={enabled ? 'مفعل' : 'معطل'}
        color={enabled ? 'success' : 'default'}
        size="small"
        variant="outlined"
        icon={enabled ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
      />
    );
  };

  const handleViewConfig = (authMethod: AuthMethodDto) => {
    setSelectedAuthMethod(authMethod);
    setOpenConfigDialog(true);
  };

  const handleDeleteAuthMethod = async (authMethodId: string) => {
    if (window.confirm('هل أنت متأكد من حذف طريقة المصادقة هذه؟')) {
      try {
        // TODO: Implement delete API call
        console.log('Delete auth method:', authMethodId);
        loadAuthMethods();
      } catch (error) {
        console.error('Error deleting auth method:', error);
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم الطريقة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <SecurityIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'type',
      headerName: 'النوع',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getTypeChip(params.value),
    },
    {
      field: 'description',
      headerName: 'الوصف',
      width: 300,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary">
          {params.value || 'لا يوجد وصف'}
        </Typography>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'updatedAt',
      headerName: 'آخر تحديث',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض التكوين">
              <CodeIcon />
            </Tooltip>
          }
          label="التكوين"
          onClick={() => handleViewConfig(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => console.log('Edit auth method:', params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteAuthMethod(params.id as string)}
        />,
      ],
    },
  ];

  const filteredAuthMethods = authMethods.filter(method => {
    const matchesSearch = method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         method.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'ALL' || method.type === filterType;
    const matchesEnabled = filterEnabled === 'ALL' || 
                          (filterEnabled === 'ENABLED' && method.enabled) ||
                          (filterEnabled === 'DISABLED' && !method.enabled);
    
    return matchesSearch && matchesType && matchesEnabled;
  });

  const handleAddAuthMethod = async () => {
    try {
      await apiGatewayService.createAuthMethod(newAuthMethod);
      setOpenAddDialog(false);
      setNewAuthMethod({
        name: '',
        type: 'JWT',
        config: {},
        description: '',
      });
      loadAuthMethods();
    } catch (error) {
      console.error('Error creating auth method:', error);
    }
  };

  const getDefaultConfig = (type: string) => {
    switch (type) {
      case 'JWT':
        return {
          issuer: 'https://auth.tecno-drive.com',
          audience: 'tecno-drive-api',
          algorithm: 'RS256',
          jwksUri: 'https://auth.tecno-drive.com/.well-known/jwks.json',
        };
      case 'OAUTH2':
        return {
          clientId: '',
          clientSecret: '',
          authorizationUri: '',
          tokenUri: '',
          scopes: ['read', 'write'],
        };
      case 'API_KEY':
        return {
          headerName: 'X-API-KEY',
          queryParamName: 'api_key',
          validateInDatabase: true,
        };
      case 'BASIC':
        return {
          realm: 'TECNO DRIVE API',
          encoding: 'UTF-8',
        };
      case 'LDAP':
        return {
          url: 'ldap://localhost:389',
          baseDn: 'dc=tecno-drive,dc=com',
          userDnPattern: 'uid={0},ou=users',
        };
      default:
        return {};
    }
  };

  const handleTypeChange = (type: string) => {
    setNewAuthMethod({
      ...newAuthMethod,
      type: type as any,
      config: getDefaultConfig(type),
    });
  };

  // Calculate stats
  const totalMethods = authMethods.length;
  const enabledMethods = authMethods.filter(m => m.enabled).length;
  const jwtMethods = authMethods.filter(m => m.type === 'JWT').length;
  const oauth2Methods = authMethods.filter(m => m.type === 'OAUTH2').length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة طرق المصادقة
        </Typography>
        <Typography variant="body1" color="text.secondary">
          تكوين وإدارة طرق المصادقة المختلفة لـ API Gateway
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalMethods}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الطرق
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {enabledMethods}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الطرق المفعلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {jwtMethods}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طرق JWT
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {oauth2Methods}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طرق OAuth2
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في طرق المصادقة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>النوع</InputLabel>
              <Select
                value={filterType}
                label="النوع"
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="ALL">جميع الأنواع</MenuItem>
                <MenuItem value="JWT">JWT</MenuItem>
                <MenuItem value="OAUTH2">OAuth2</MenuItem>
                <MenuItem value="API_KEY">API Key</MenuItem>
                <MenuItem value="BASIC">Basic Auth</MenuItem>
                <MenuItem value="LDAP">LDAP</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterEnabled}
                label="الحالة"
                onChange={(e) => setFilterEnabled(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ENABLED">مفعل</MenuItem>
                <MenuItem value="DISABLED">معطل</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة طريقة مصادقة
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredAuthMethods}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Auth Method Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة طريقة مصادقة جديدة</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="اسم الطريقة"
              value={newAuthMethod.name}
              onChange={(e) => setNewAuthMethod({ ...newAuthMethod, name: e.target.value })}
              fullWidth
              required
            />
            <FormControl fullWidth required>
              <InputLabel>نوع المصادقة</InputLabel>
              <Select
                value={newAuthMethod.type}
                label="نوع المصادقة"
                onChange={(e) => handleTypeChange(e.target.value)}
              >
                <MenuItem value="JWT">JWT</MenuItem>
                <MenuItem value="OAUTH2">OAuth2</MenuItem>
                <MenuItem value="API_KEY">API Key</MenuItem>
                <MenuItem value="BASIC">Basic Auth</MenuItem>
                <MenuItem value="LDAP">LDAP</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="الوصف"
              value={newAuthMethod.description}
              onChange={(e) => setNewAuthMethod({ ...newAuthMethod, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <Typography variant="h6" sx={{ mt: 2 }}>
              تكوين الطريقة
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
              <TextField
                label="التكوين (JSON)"
                value={JSON.stringify(newAuthMethod.config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setNewAuthMethod({ ...newAuthMethod, config });
                  } catch (error) {
                    // Invalid JSON, ignore
                  }
                }}
                fullWidth
                multiline
                rows={8}
                sx={{ fontFamily: 'monospace' }}
                helperText="تكوين JSON لطريقة المصادقة"
              />
            </Paper>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddAuthMethod} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* Config Dialog */}
      <Dialog open={openConfigDialog} onClose={() => setOpenConfigDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>تكوين طريقة المصادقة - {selectedAuthMethod?.name}</DialogTitle>
        <DialogContent>
          {selectedAuthMethod && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                معلومات عامة
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">النوع:</Typography>
                  <Typography variant="body1">{selectedAuthMethod.type}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">الحالة:</Typography>
                  {getStatusChip(selectedAuthMethod.enabled)}
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">الوصف:</Typography>
                  <Typography variant="body1">{selectedAuthMethod.description || 'لا يوجد وصف'}</Typography>
                </Grid>
              </Grid>
              
              <Typography variant="h6" sx={{ mb: 2 }}>
                التكوين
              </Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
                  {JSON.stringify(selectedAuthMethod.config, null, 2)}
                </pre>
              </Paper>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenConfigDialog(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AuthMethodsManagement;
