package com.tecnodrive.saasservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

/**
 * Tenant Entity
 * 
 * Represents a tenant (company or school) in the SaaS platform.
 * Each tenant has its own isolated environment and configuration.
 */
@Entity
@Table(name = "tenants")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Tenant {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Unique tenant name/identifier
     */
    @Column(nullable = false, unique = true, length = 100)
    private String name;

    /**
     * Display name for the tenant
     */
    @Column(nullable = false, length = 200)
    private String displayName;

    /**
     * Tenant type (COMPANY, SCHOOL, ENTERPRISE)
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TenantType type;

    /**
     * Tenant status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private TenantStatus status = TenantStatus.ACTIVE;

    /**
     * Contact person user ID
     */
    @Column(nullable = false)
    private String contactPersonId;

    /**
     * Primary contact email
     */
    @Column(nullable = false, length = 255)
    private String email;

    /**
     * Primary contact phone
     */
    @Column(length = 20)
    private String phone;

    /**
     * Physical address
     */
    @Column(length = 500)
    private String address;

    /**
     * Website URL
     */
    @Column(length = 255)
    private String website;

    /**
     * Service types enabled for this tenant
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ServiceType serviceType;

    /**
     * Current pricing plan ID
     */
    @Column(nullable = false)
    private String pricingPlanId;

    /**
     * Maximum number of users allowed
     */
    @Column(nullable = false)
    @Builder.Default
    private Integer maxUsers = 100;

    /**
     * Maximum number of vehicles allowed
     */
    @Column(nullable = false)
    @Builder.Default
    private Integer maxVehicles = 10;

    /**
     * Custom branding configuration (JSON)
     */
    @Column(columnDefinition = "TEXT")
    private String brandingConfig;

    /**
     * Feature flags configuration (JSON)
     */
    @Column(columnDefinition = "TEXT")
    private String featureFlags;

    /**
     * Additional metadata (JSON)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    /**
     * Subscription start date
     */
    private Instant subscriptionStartDate;

    /**
     * Subscription end date
     */
    private Instant subscriptionEndDate;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Tenant Type Enum
     */
    public enum TenantType {
        COMPANY,
        SCHOOL,
        ENTERPRISE,
        GOVERNMENT,
        NON_PROFIT
    }

    /**
     * Tenant Status Enum
     */
    public enum TenantStatus {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        TRIAL,
        EXPIRED
    }

    /**
     * Service Type Enum
     */
    public enum ServiceType {
        RIDE_ONLY,
        PARCEL_ONLY,
        BOTH,
        CUSTOM
    }

    /**
     * Check if tenant is active
     */
    public boolean isActive() {
        return status == TenantStatus.ACTIVE;
    }

    /**
     * Check if subscription is valid
     */
    public boolean isSubscriptionValid() {
        Instant now = Instant.now();
        return subscriptionStartDate != null && 
               subscriptionEndDate != null &&
               now.isAfter(subscriptionStartDate) && 
               now.isBefore(subscriptionEndDate);
    }
}
