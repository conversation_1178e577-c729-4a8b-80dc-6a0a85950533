@echo off
echo 🚀 TECNO DRIVE - Starting Monitoring Services
echo =============================================

echo.
echo 📊 Starting Prometheus on port 9090...
docker run -d --name prometheus-tecnodrive --restart unless-stopped -p 9090:9090 prom/prometheus:latest
if %errorlevel% neq 0 (
    echo ⚠️ Prometheus may already be running or failed to start
) else (
    echo ✅ Prometheus started successfully!
)

echo.
echo 📈 Starting Grafana on port 3001...
docker run -d --name grafana-tecnodrive --restart unless-stopped -p 3001:3000 -e GF_SECURITY_ADMIN_PASSWORD=admin123 grafana/grafana:latest
if %errorlevel% neq 0 (
    echo ⚠️ Grafana may already be running or failed to start
) else (
    echo ✅ Grafana started successfully!
)

echo.
echo ⏳ Waiting for services to start...
timeout /t 15 /nobreak > nul

echo.
echo 🔍 Checking service status...
echo.

echo Testing Prometheus...
curl -s http://localhost:9090 > nul
if %errorlevel% equ 0 (
    echo ✅ Prometheus is running: http://localhost:9090
) else (
    echo ❌ Prometheus is not responding
)

echo Testing Grafana...
curl -s http://localhost:3001 > nul
if %errorlevel% equ 0 (
    echo ✅ Grafana is running: http://localhost:3001 ^(admin/admin123^)
) else (
    echo ❌ Grafana is not responding
)

echo.
echo 🔗 Access URLs:
echo • Prometheus: http://localhost:9090
echo • Grafana: http://localhost:3001 (admin/admin123)
echo • Admin Dashboard: http://localhost:3000
echo • API Gateway: http://localhost:8080
echo • Eureka: http://localhost:8761

echo.
echo ✅ Monitoring services setup completed!
echo Press any key to exit...
pause > nul
