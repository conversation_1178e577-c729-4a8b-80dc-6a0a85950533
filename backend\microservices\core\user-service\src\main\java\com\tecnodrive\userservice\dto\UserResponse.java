package com.tecnodrive.userservice.dto;

import com.tecnodrive.userservice.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

/**
 * User Response DTOs
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserResponse {
    
    private UUID id;
    private String email;
    private String firstName;
    private String lastName;
    private String fullName;
    private String phoneNumber;
    private User.UserType userType;
    private User.UserStatus status;
    private UUID companyId;
    private String profileImageUrl;
    private String address;
    private String city;
    private String country;
    private String postalCode;
    private Instant dateOfBirth;
    private User.Gender gender;
    private String preferredLanguage;
    private String timezone;
    private boolean emailNotifications;
    private boolean smsNotifications;
    private boolean pushNotifications;
    private Instant lastLoginAt;
    private boolean emailVerified;
    private boolean phoneVerified;
    private Instant createdAt;
    private Instant updatedAt;

    /**
     * Summary response for lists
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Summary {
        private UUID id;
        private String email;
        private String firstName;
        private String lastName;
        private String fullName;
        private String phoneNumber;
        private User.UserType userType;
        private User.UserStatus status;
        private String city;
        private String country;
        private boolean emailVerified;
        private boolean phoneVerified;
        private Instant createdAt;
    }

    /**
     * Public profile response (limited information)
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PublicProfile {
        private UUID id;
        private String firstName;
        private String lastName;
        private String fullName;
        private User.UserType userType;
        private String profileImageUrl;
        private String city;
        private String country;
        private Instant createdAt;
    }

    /**
     * Statistics response
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Statistics {
        private long totalUsers;
        private long activeUsers;
        private long inactiveUsers;
        private long suspendedUsers;
        private long pendingVerificationUsers;
        private long bannedUsers;
        private long passengersCount;
        private long driversCount;
        private long adminsCount;
        private long fleetManagersCount;
        private long supportAgentsCount;
        private long verifiedUsers;
        private long unverifiedUsers;
        private long usersWithEmailNotifications;
        private long usersWithSmsNotifications;
        private long usersWithPushNotifications;
    }

    /**
     * User activity response
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Activity {
        private UUID userId;
        private String fullName;
        private String email;
        private Instant lastLoginAt;
        private boolean isActive;
        private long daysSinceLastLogin;
        private User.UserStatus status;
    }
}
