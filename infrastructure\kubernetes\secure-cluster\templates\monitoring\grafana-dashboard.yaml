{{- if .Values.monitoring.grafana.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: tecno-drive-security-dashboard
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: security-dashboard
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    grafana_dashboard: "1"
data:
  tecno-drive-security-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "TECNO DRIVE - Security & Compliance Dashboard",
        "tags": ["tecno-drive", "security", "gatekeeper", "compliance"],
        "style": "dark",
        "timezone": "Asia/Riyadh",
        "refresh": "30s",
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "🛡️ Security Overview",
            "type": "stat",
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total{constraint='centralsecurity'})",
                "legendFormat": "Total Violations",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 5},
                    {"color": "red", "value": 20}
                  ]
                },
                "unit": "short"
              }
            },
            "options": {
              "colorMode": "background",
              "graphMode": "area",
              "justifyMode": "center",
              "orientation": "horizontal"
            }
          },
          {
            "id": 2,
            "title": "📊 Violations by Type",
            "type": "piechart",
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0},
            "targets": [
              {
                "expr": "sum by (violation_kind) (gatekeeper_violations_total{constraint='centralsecurity'})",
                "legendFormat": "{{ "{{violation_kind}}" }}",
                "refId": "A"
              }
            ],
            "options": {
              "pieType": "donut",
              "tooltip": {"mode": "single"},
              "legend": {
                "displayMode": "table",
                "placement": "right",
                "values": ["value", "percent"]
              }
            }
          },
          {
            "id": 3,
            "title": "🚨 Critical Violations",
            "type": "stat",
            "gridPos": {"h": 4, "w": 3, "x": 12, "y": 0},
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total{constraint='centralsecurity', severity='critical'})",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "red", "value": 1}
                  ]
                }
              }
            }
          },
          {
            "id": 4,
            "title": "⚠️ High Violations",
            "type": "stat",
            "gridPos": {"h": 4, "w": 3, "x": 15, "y": 0},
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total{constraint='centralsecurity', severity='high'})",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 1},
                    {"color": "orange", "value": 5}
                  ]
                }
              }
            }
          },
          {
            "id": 5,
            "title": "🔧 Medium Violations",
            "type": "stat",
            "gridPos": {"h": 4, "w": 3, "x": 18, "y": 0},
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total{constraint='centralsecurity', severity='medium'})",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 5},
                    {"color": "orange", "value": 15}
                  ]
                }
              }
            }
          },
          {
            "id": 6,
            "title": "ℹ️ Low Violations",
            "type": "stat",
            "gridPos": {"h": 4, "w": 3, "x": 21, "y": 0},
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total{constraint='centralsecurity', severity='low'})",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "blue", "value": 10}
                  ]
                }
              }
            }
          },
          {
            "id": 7,
            "title": "📈 Violations Trend",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4},
            "targets": [
              {
                "expr": "sum by (severity) (rate(gatekeeper_violations_total{constraint='centralsecurity'}[5m]))",
                "legendFormat": "{{ "{{severity}}" }} violations/sec",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "palette-classic"},
                "custom": {
                  "drawStyle": "line",
                  "lineInterpolation": "linear",
                  "barAlignment": 0,
                  "lineWidth": 2,
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "spanNulls": false,
                  "insertNulls": false,
                  "showPoints": "never",
                  "pointSize": 5,
                  "stacking": {"mode": "none", "group": "A"},
                  "axisPlacement": "auto",
                  "axisLabel": "",
                  "scaleDistribution": {"type": "linear"},
                  "hideFrom": {"legend": false, "tooltip": false, "vis": false},
                  "thresholdsStyle": {"mode": "off"}
                }
              }
            }
          },
          {
            "id": 8,
            "title": "🏢 Violations by Namespace",
            "type": "table",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
            "targets": [
              {
                "expr": "sum by (resource_namespace) (gatekeeper_violations_total{constraint='centralsecurity'})",
                "format": "table",
                "refId": "A"
              }
            ],
            "transformations": [
              {
                "id": "organize",
                "options": {
                  "excludeByName": {"Time": true},
                  "indexByName": {},
                  "renameByName": {
                    "resource_namespace": "Namespace",
                    "Value": "Violations"
                  }
                }
              }
            ],
            "fieldConfig": {
              "defaults": {
                "custom": {
                  "align": "auto",
                  "displayMode": "auto"
                }
              },
              "overrides": [
                {
                  "matcher": {"id": "byName", "options": "Violations"},
                  "properties": [
                    {
                      "id": "custom.displayMode",
                      "value": "color-background"
                    },
                    {
                      "id": "color",
                      "value": {
                        "mode": "thresholds"
                      }
                    },
                    {
                      "id": "thresholds",
                      "value": {
                        "steps": [
                          {"color": "green", "value": null},
                          {"color": "yellow", "value": 1},
                          {"color": "red", "value": 10}
                        ]
                      }
                    }
                  ]
                }
              ]
            }
          },
          {
            "id": 9,
            "title": "🔍 Recent Violations Details",
            "type": "table",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
            "targets": [
              {
                "expr": "gatekeeper_violations_total{constraint='centralsecurity'}",
                "format": "table",
                "refId": "A"
              }
            ],
            "transformations": [
              {
                "id": "organize",
                "options": {
                  "excludeByName": {
                    "Time": true,
                    "__name__": true,
                    "constraint": true,
                    "instance": true,
                    "job": true
                  },
                  "indexByName": {},
                  "renameByName": {
                    "resource_namespace": "Namespace",
                    "resource_name": "Resource",
                    "resource_kind": "Kind",
                    "violation_kind": "Type",
                    "severity": "Severity",
                    "Value": "Count"
                  }
                }
              }
            ]
          },
          {
            "id": 10,
            "title": "🎯 Gatekeeper Performance",
            "type": "timeseries",
            "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16},
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(gatekeeper_webhook_request_duration_seconds_bucket[5m]))",
                "legendFormat": "95th percentile",
                "refId": "A"
              },
              {
                "expr": "histogram_quantile(0.50, rate(gatekeeper_webhook_request_duration_seconds_bucket[5m]))",
                "legendFormat": "50th percentile",
                "refId": "B"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "s",
                "custom": {
                  "drawStyle": "line",
                  "lineWidth": 2,
                  "fillOpacity": 10
                }
              }
            }
          },
          {
            "id": 11,
            "title": "📊 Webhook Success Rate",
            "type": "stat",
            "gridPos": {"h": 6, "w": 4, "x": 8, "y": 16},
            "targets": [
              {
                "expr": "rate(gatekeeper_webhook_request_total{response_code='200'}[5m]) / rate(gatekeeper_webhook_request_total[5m]) * 100",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": null},
                    {"color": "yellow", "value": 95},
                    {"color": "green", "value": 99}
                  ]
                }
              }
            }
          },
          {
            "id": 12,
            "title": "🔄 Audit Frequency",
            "type": "timeseries",
            "gridPos": {"h": 6, "w": 12, "x": 12, "y": 16},
            "targets": [
              {
                "expr": "rate(gatekeeper_audit_duration_seconds_count[5m])",
                "legendFormat": "Audits per second",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "ops",
                "custom": {
                  "drawStyle": "line",
                  "lineWidth": 2
                }
              }
            }
          }
        ],
        "templating": {
          "list": [
            {
              "name": "namespace",
              "type": "query",
              "query": "label_values(gatekeeper_violations_total, resource_namespace)",
              "refresh": 1,
              "includeAll": true,
              "allValue": ".*",
              "multi": true
            },
            {
              "name": "severity",
              "type": "query",
              "query": "label_values(gatekeeper_violations_total, severity)",
              "refresh": 1,
              "includeAll": true,
              "allValue": ".*",
              "multi": true
            }
          ]
        },
        "annotations": {
          "list": [
            {
              "name": "Security Incidents",
              "datasource": "Prometheus",
              "expr": "ALERTS{alertname=~'.*Security.*', alertstate='firing'}",
              "titleFormat": "{{ "{{alertname}}" }}",
              "textFormat": "{{ "{{description}}" }}",
              "iconColor": "red"
            }
          ]
        }
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tecno-drive-resilience-dashboard
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: resilience-dashboard
    app.kubernetes.io/instance: {{ .Release.Name }}
    grafana_dashboard: "1"
data:
  tecno-drive-resilience-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "TECNO DRIVE - Resilience & Performance Dashboard",
        "tags": ["tecno-drive", "resilience", "performance", "sla"],
        "style": "dark",
        "timezone": "Asia/Riyadh",
        "refresh": "30s",
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "🏥 Service Health Overview",
            "type": "stat",
            "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "up{job=~'.*tecno-drive.*'}",
                "legendFormat": "{{ "{{job}}" }}",
                "refId": "A"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "mappings": [
                  {"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"},
                  {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}
                ]
              }
            },
            "options": {
              "colorMode": "background"
            }
          },
          {
            "id": 2,
            "title": "📊 API Gateway Performance",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
            "targets": [
              {
                "expr": "rate(http_requests_total{service='api-gateway'}[5m])",
                "legendFormat": "Requests/sec",
                "refId": "A"
              },
              {
                "expr": "rate(http_requests_total{service='api-gateway', status=~'5..'}[5m])",
                "legendFormat": "Errors/sec",
                "refId": "B"
              }
            ]
          },
          {
            "id": 3,
            "title": "💾 Resource Utilization",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
            "targets": [
              {
                "expr": "avg by (container) (rate(container_cpu_usage_seconds_total{namespace=~'tecno-drive.*'}[5m]) * 100)",
                "legendFormat": "CPU % - {{ "{{container}}" }}",
                "refId": "A"
              },
              {
                "expr": "avg by (container) (container_memory_working_set_bytes{namespace=~'tecno-drive.*'} / 1024 / 1024)",
                "legendFormat": "Memory MB - {{ "{{container}}" }}",
                "refId": "B"
              }
            ]
          }
        ]
      }
    }
{{- end }}
