package com.tecnodrive.paymentservice.dto;

import com.tecnodrive.paymentservice.entity.Payment;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * Payment Update Request DTO
 * 
 * Used for updating payment status and other mutable fields
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentUpdateRequest {

    /**
     * Payment status
     */
    private Payment.PaymentStatus status;

    /**
     * External payment gateway transaction ID
     */
    private String gatewayTransactionId;

    /**
     * Payment description or notes
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /**
     * Additional metadata as JSON string
     */
    private String metadata;
}
