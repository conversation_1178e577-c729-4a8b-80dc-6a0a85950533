import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Code as CodeIcon,
  Storage as DatabaseIcon,
  Api as ApiIcon,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';

interface ApexApplication {
  id: string;
  name: string;
  workspace: string;
  version: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DEVELOPMENT';
  lastDeployed: string;
  connectedServices: string[];
}

interface RestDataSource {
  id: string;
  name: string;
  service: string;
  baseUrl: string;
  authMethod: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC';
  enabled: boolean;
  lastSync: string;
}

interface OrdsModule {
  id: string;
  name: string;
  basePath: string;
  schema: string;
  handlers: number;
  enabled: boolean;
  createdAt: string;
}

const OracleApexIntegration: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [applications, setApplications] = useState<ApexApplication[]>([]);
  const [dataSources, setDataSources] = useState<RestDataSource[]>([]);
  const [ordsModules, setOrdsModules] = useState<OrdsModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [openAppDialog, setOpenAppDialog] = useState(false);
  const [openDataSourceDialog, setOpenDataSourceDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Mock data
  const mockApplications: ApexApplication[] = [
    {
      id: 'app-1',
      name: 'TECNO DRIVE Admin Portal',
      workspace: 'APEX_TECHNODRIVE',
      version: '1.0.0',
      status: 'ACTIVE',
      lastDeployed: '2025-07-09T14:30:00Z',
      connectedServices: ['auth-service', 'fleet-service', 'rides-service'],
    },
    {
      id: 'app-2',
      name: 'Driver Mobile Interface',
      workspace: 'APEX_TECHNODRIVE',
      version: '2.1.0',
      status: 'ACTIVE',
      lastDeployed: '2025-07-08T10:15:00Z',
      connectedServices: ['auth-service', 'rides-service', 'location-service'],
    },
    {
      id: 'app-3',
      name: 'Fleet Analytics Dashboard',
      workspace: 'APEX_TECHNODRIVE',
      version: '1.5.0',
      status: 'DEVELOPMENT',
      lastDeployed: '2025-07-07T16:45:00Z',
      connectedServices: ['fleet-service', 'analytics-service'],
    },
  ];

  const mockDataSources: RestDataSource[] = [
    {
      id: 'ds-auth',
      name: 'Authentication Service',
      service: 'auth-service',
      baseUrl: 'https://api.tecno-drive.com/api/auth',
      authMethod: 'JWT',
      enabled: true,
      lastSync: '2025-07-09T14:30:00Z',
    },
    {
      id: 'ds-fleet',
      name: 'Fleet Management Service',
      service: 'fleet-service',
      baseUrl: 'https://api.tecno-drive.com/api/fleet',
      authMethod: 'JWT',
      enabled: true,
      lastSync: '2025-07-09T14:25:00Z',
    },
    {
      id: 'ds-rides',
      name: 'Ride Service',
      service: 'rides-service',
      baseUrl: 'https://api.tecno-drive.com/api/rides',
      authMethod: 'JWT',
      enabled: true,
      lastSync: '2025-07-09T14:20:00Z',
    },
  ];

  const mockOrdsModules: OrdsModule[] = [
    {
      id: 'ords-fleet',
      name: 'Fleet Module',
      basePath: '/fleet/',
      schema: 'APEX_TECHNODRIVE',
      handlers: 12,
      enabled: true,
      createdAt: '2025-01-15T08:00:00Z',
    },
    {
      id: 'ords-rides',
      name: 'Rides Module',
      basePath: '/rides/',
      schema: 'APEX_TECHNODRIVE',
      handlers: 8,
      enabled: true,
      createdAt: '2025-01-20T09:00:00Z',
    },
    {
      id: 'ords-auth',
      name: 'Authentication Module',
      basePath: '/auth/',
      schema: 'APEX_TECHNODRIVE',
      handlers: 6,
      enabled: true,
      createdAt: '2025-01-25T10:00:00Z',
    },
  ];

  useEffect(() => {
    setApplications(mockApplications);
    setDataSources(mockDataSources);
    setOrdsModules(mockOrdsModules);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      INACTIVE: { label: 'غير نشط', color: 'default' as const, icon: <ErrorIcon fontSize="small" /> },
      DEVELOPMENT: { label: 'تطوير', color: 'warning' as const, icon: <SettingsIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getAuthMethodChip = (method: string) => {
    const methodConfig = {
      JWT: { label: 'JWT', color: 'primary' as const },
      API_KEY: { label: 'API Key', color: 'secondary' as const },
      OAUTH2: { label: 'OAuth2', color: 'info' as const },
      BASIC: { label: 'Basic', color: 'warning' as const },
    };

    const config = methodConfig[method as keyof typeof methodConfig] || { 
      label: method, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const applicationColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم التطبيق',
      width: 250,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <DatabaseIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'workspace',
      headerName: 'مساحة العمل',
      width: 150,
    },
    {
      field: 'version',
      headerName: 'الإصدار',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" color="info" variant="outlined" />
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'connectedServices',
      headerName: 'الخدمات المتصلة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value.length} خدمة
        </Typography>
      ),
    },
    {
      field: 'lastDeployed',
      headerName: 'آخر نشر',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
  ];

  const dataSourceColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم مصدر البيانات',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'success.main' }}>
            <ApiIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'service',
      headerName: 'الخدمة',
      width: 150,
    },
    {
      field: 'baseUrl',
      headerName: 'الرابط الأساسي',
      width: 250,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'authMethod',
      headerName: 'طريقة المصادقة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => getAuthMethodChip(params.value),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'مفعل' : 'معطل'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
  ];

  const ordsColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم الوحدة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'info.main' }}>
            <CodeIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'basePath',
      headerName: 'المسار الأساسي',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'schema',
      headerName: 'المخطط',
      width: 150,
    },
    {
      field: 'handlers',
      headerName: 'عدد المعالجات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" color="primary" variant="filled" />
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'مفعل' : 'معطل'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
  ];

  // Calculate stats
  const totalApps = applications.length;
  const activeApps = applications.filter(app => app.status === 'ACTIVE').length;
  const totalDataSources = dataSources.length;
  const enabledDataSources = dataSources.filter(ds => ds.enabled).length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          دمج Oracle APEX
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة تكامل خدمات تكنو درايف مع Oracle APEX وORDS
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalApps}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                تطبيقات APEX
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {activeApps}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                التطبيقات النشطة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {totalDataSources}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مصادر البيانات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {enabledDataSources}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المصادر المفعلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<DatabaseIcon />} label="تطبيقات APEX" iconPosition="start" />
            <Tab icon={<ApiIcon />} label="مصادر البيانات" iconPosition="start" />
            <Tab icon={<CodeIcon />} label="وحدات ORDS" iconPosition="start" />
            <Tab icon={<SettingsIcon />} label="الإعدادات" iconPosition="start" />
          </Tabs>
        </Box>

        {/* APEX Applications Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">تطبيقات Oracle APEX</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenAppDialog(true)}
              >
                إضافة تطبيق
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={applications}
                columns={applicationColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* REST Data Sources Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">مصادر البيانات RESTful</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenDataSourceDialog(true)}
              >
                إضافة مصدر بيانات
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={dataSources}
                columns={dataSourceColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* ORDS Modules Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>وحدات Oracle REST Data Services</Typography>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={ordsModules}
                columns={ordsColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Settings Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>إعدادات التكامل</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>إعدادات ORDS</Typography>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Schema المفعل"
                          secondary="APEX_TECHNODRIVE"
                        />
                        <Chip label="نشط" color="success" size="small" />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="Base Path"
                          secondary="/tecno-drive"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="Authentication"
                          secondary="JWT Bearer Token"
                        />
                        <Chip label="مفعل" color="success" size="small" />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>إعدادات APEX</Typography>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Workspace"
                          secondary="APEX_TECHNODRIVE"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="Authentication Scheme"
                          secondary="JWT Bearer"
                        />
                        <Chip label="مفعل" color="success" size="small" />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="Auto Sync"
                          secondary="تحديث تلقائي كل 5 دقائق"
                        />
                        <Switch defaultChecked />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>أكواد التكامل</Typography>
                
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>إعداد ORDS Module</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
{`BEGIN
  ORDS.enable_schema(
    p_enabled => TRUE,
    p_schema  => 'APEX_TECHNODRIVE',
    p_url_mapping_type => 'BASE_PATH',
    p_url_mapping_pattern => 'tecno-drive'
  );
  
  ORDS.define_module(
    p_module_name => 'fleet',
    p_base_path   => '/fleet/',
    p_items_per_page => 0
  );
  
  COMMIT;
END;`}
                      </pre>
                    </Paper>
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>إعداد REST Data Source في APEX</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
{`-- في APEX Shared Components > REST Data Sources
Base URL: https://api.tecno-drive.com/api/fleet/vehicles
Authentication: JWT Bearer Token
Headers:
  Authorization: Bearer {{JWT_TOKEN}}
  Content-Type: application/json`}
                      </pre>
                    </Paper>
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>JavaScript للخرائط التفاعلية</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
{`// في APEX Page - Static Content Region
function initFleetMap() {
  window.map = new google.maps.Map(
    document.getElementById('fleetMap'), {
      center: {lat: 15.3694, lng: 44.1910},
      zoom: 12
    }
  );
}

// Dynamic Action على Interactive Grid Row Selection
var rec = apex.region("fleet_ig").widget()
  .interactiveGrid("getViews","grid").model.getRecord();
var lat = parseFloat(rec.LOCATION_LAT);
var lng = parseFloat(rec.LOCATION_LNG);
var marker = new google.maps.Marker({
  position: {lat: lat, lng: lng},
  map: window.map
});
window.map.panTo({lat: lat, lng: lng});`}
                      </pre>
                    </Paper>
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          </Box>
        )}
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default OracleApexIntegration;
