# Quick Diagnosis Script for TecnoDrive Platform
Write-Host "🔍 TecnoDrive Platform - Quick Diagnosis" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Check Java
Write-Host "`n☕ Checking Java..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java: $($javaVersion.Line)" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found or not working" -ForegroundColor Red
}

# Check Node.js
Write-Host "`n📦 Checking Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
}

# Check Docker
Write-Host "`n🐳 Checking Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker: $dockerVersion" -ForegroundColor Green
    
    # Check if Docker is running
    $dockerInfo = docker info 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is running" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker daemon is not running" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker not found" -ForegroundColor Red
}

# Check ports
Write-Host "`n🔌 Checking Ports..." -ForegroundColor Yellow
$ports = @(8080, 8081, 8085, 5432, 6379, 3000)
foreach ($port in $ports) {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ Port $port is open" -ForegroundColor Green
    } else {
        Write-Host "❌ Port $port is closed" -ForegroundColor Red
    }
}

# Check services
Write-Host "`n🌐 Checking Services..." -ForegroundColor Yellow

# API Gateway
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/actuator/health" -TimeoutSec 5
    Write-Host "✅ API Gateway: $($response.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ API Gateway: Not responding" -ForegroundColor Red
}

# Location Service
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8085/api/locations/health" -TimeoutSec 5
    Write-Host "✅ Location Service: $($response.status)" -ForegroundColor Green
    if ($response.websocket_sessions -ne $null) {
        Write-Host "   WebSocket Sessions: $($response.websocket_sessions)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Location Service: Not responding" -ForegroundColor Red
}

# Check frontend
Write-Host "`n🎨 Checking Frontend..." -ForegroundColor Yellow
$frontendPath = "frontend/admin-dashboard"
if (Test-Path $frontendPath) {
    Write-Host "✅ Frontend directory found" -ForegroundColor Green
    
    if (Test-Path "$frontendPath/node_modules") {
        Write-Host "✅ Dependencies installed" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Dependencies not installed" -ForegroundColor Yellow
    }
    
    if (Test-Path "$frontendPath/.env") {
        Write-Host "✅ Environment file exists" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Environment file missing" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Frontend directory not found" -ForegroundColor Red
}

# Check database files
Write-Host "`n🗄️  Checking Database..." -ForegroundColor Yellow
if (Test-Path "database") {
    Write-Host "✅ Database directory found" -ForegroundColor Green
} else {
    Write-Host "❌ Database directory not found" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Diagnosis Summary" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

# Check if main components are ready
$javaOk = $false
$nodeOk = $false
$dockerOk = $false

try { java -version 2>&1 | Out-Null; $javaOk = $true } catch { }
try { node --version | Out-Null; $nodeOk = $true } catch { }
try { docker --version | Out-Null; $dockerOk = $true } catch { }

if ($javaOk -and $nodeOk -and $dockerOk) {
    Write-Host "✅ All prerequisites are installed" -ForegroundColor Green
} else {
    Write-Host "❌ Some prerequisites are missing" -ForegroundColor Red
}

# Recommendations
Write-Host "`n💡 Recommendations:" -ForegroundColor Yellow
if (-not $javaOk) {
    Write-Host "- Install Java 17 or higher" -ForegroundColor White
}
if (-not $nodeOk) {
    Write-Host "- Install Node.js 16 or higher" -ForegroundColor White
}
if (-not $dockerOk) {
    Write-Host "- Install Docker Desktop" -ForegroundColor White
}

Write-Host "`n🚀 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Fix any issues mentioned above" -ForegroundColor White
Write-Host "2. Run: .\start-all-services.ps1" -ForegroundColor White
Write-Host "3. Run: .\start-frontend.ps1" -ForegroundColor White
Write-Host "4. Open: http://localhost:3000/live-operations" -ForegroundColor White

Write-Host "`n✅ Diagnosis Complete!" -ForegroundColor Green
