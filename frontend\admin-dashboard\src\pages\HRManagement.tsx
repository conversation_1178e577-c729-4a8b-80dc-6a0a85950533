import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  Rating,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Assessment as AssessmentIcon,
  Description as DocumentIcon,
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Work as WorkIcon,
  Group as GroupIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

interface Driver {
  id: string;
  employeeId: string;
  name: string;
  email: string;
  phone: string;
  status: string;
  employmentType: string;
  department: string;
  position: string;
  joinDate: string;
  salary: number;
  rating: number;
  totalTrips: number;
  totalEarnings: number;
  licenseNumber: string;
  licenseExpiry: string;
  vehicleAssigned: string;
}

interface HRStatistics {
  totalDrivers: number;
  activeDrivers: number;
  inactiveDrivers: number;
  onLeave: number;
  suspended: number;
  averageRating: number;
  totalPayroll: number;
  departmentDistribution: Record<string, number>;
  employmentTypeDistribution: Record<string, number>;
}

const HRManagement: React.FC = () => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [statistics, setStatistics] = useState<HRStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    department: '',
    employmentType: '',
    search: ''
  });

  useEffect(() => {
    loadDrivers();
    loadStatistics();
  }, [filters]);

  const loadDrivers = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(`http://localhost:8097/api/hr/drivers?${queryParams}`);
      const data = await response.json();
      
      if (data.success) {
        setDrivers(data.data);
      }
    } catch (error) {
      console.error('Failed to load drivers:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('http://localhost:8097/api/hr/statistics');
      const data = await response.json();
      
      if (data.success) {
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'active': 'success',
      'inactive': 'default',
      'on_leave': 'warning',
      'suspended': 'error'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'active': 'نشط',
      'inactive': 'غير نشط',
      'on_leave': 'في إجازة',
      'suspended': 'موقوف'
    };
    return labels[status] || status;
  };

  const getDepartmentLabel = (department: string) => {
    const labels: Record<string, string> = {
      'passenger_transport': 'نقل الركاب',
      'parcel_delivery': 'توصيل الطرود',
      'mixed_services': 'خدمات مختلطة'
    };
    return labels[department] || department;
  };

  const getEmploymentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'full_time': 'دوام كامل',
      'part_time': 'دوام جزئي',
      'contract': 'عقد'
    };
    return labels[type] || type;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const handleViewDetails = (driver: Driver) => {
    setSelectedDriver(driver);
    setDetailsDialogOpen(true);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <GroupIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          إدارة الموارد البشرية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة للسائقين والجداول والرواتب
        </Typography>
      </Box>

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PersonIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{statistics.totalDrivers}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي السائقين
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      نشط: {statistics.activeDrivers}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(statistics.totalPayroll)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الرواتب الشهرية
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      متوسط: {formatCurrency(statistics.totalPayroll / statistics.totalDrivers)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{statistics.averageRating.toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط التقييم
                    </Typography>
                    <Rating value={statistics.averageRating} readOnly size="small" />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{statistics.onLeave + statistics.suspended}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      يحتاج متابعة
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      إجازة: {statistics.onLeave} | موقوف: {statistics.suspended}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="البحث..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="inactive">غير نشط</MenuItem>
                <MenuItem value="on_leave">في إجازة</MenuItem>
                <MenuItem value="suspended">موقوف</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>القسم</InputLabel>
              <Select
                value={filters.department}
                onChange={(e) => handleFilterChange('department', e.target.value)}
                label="القسم"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="passenger_transport">نقل الركاب</MenuItem>
                <MenuItem value="parcel_delivery">توصيل الطرود</MenuItem>
                <MenuItem value="mixed_services">خدمات مختلطة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>نوع التوظيف</InputLabel>
              <Select
                value={filters.employmentType}
                onChange={(e) => handleFilterChange('employmentType', e.target.value)}
                label="نوع التوظيف"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="full_time">دوام كامل</MenuItem>
                <MenuItem value="part_time">دوام جزئي</MenuItem>
                <MenuItem value="contract">عقد</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadDrivers}
            >
              تحديث
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {/* Open add driver dialog */}}
            >
              إضافة
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Drivers Table */}
      <Paper>
        {loading && <LinearProgress />}
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>السائق</TableCell>
                <TableCell>رقم الموظف</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>القسم</TableCell>
                <TableCell>نوع التوظيف</TableCell>
                <TableCell>التقييم</TableCell>
                <TableCell>الراتب</TableCell>
                <TableCell>الرحلات</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {drivers
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((driver) => (
                <TableRow key={driver.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                        {driver.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {driver.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {driver.email}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {driver.employeeId}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(driver.status)}
                      color={getStatusColor(driver.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {getDepartmentLabel(driver.department)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {getEmploymentTypeLabel(driver.employmentType)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Rating value={driver.rating} readOnly size="small" />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        ({driver.rating.toFixed(1)})
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {formatCurrency(driver.salary)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatNumber(driver.totalTrips)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <IconButton size="small" onClick={() => handleViewDetails(driver)}>
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small">
                        <ScheduleIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          component="div"
          count={drivers.length}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
        />
      </Paper>

      {/* Driver Details Dialog */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل السائق</DialogTitle>
        <DialogContent>
          {selectedDriver && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">الاسم الكامل</Typography>
                <Typography variant="body1">{selectedDriver.name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">رقم الموظف</Typography>
                <Typography variant="body1">{selectedDriver.employeeId}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">البريد الإلكتروني</Typography>
                <Typography variant="body1">{selectedDriver.email}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">رقم الهاتف</Typography>
                <Typography variant="body1">{selectedDriver.phone}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">رقم الرخصة</Typography>
                <Typography variant="body1">{selectedDriver.licenseNumber}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">انتهاء الرخصة</Typography>
                <Typography variant="body1">
                  {new Date(selectedDriver.licenseExpiry).toLocaleDateString('ar-SA')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">المركبة المخصصة</Typography>
                <Typography variant="body1">{selectedDriver.vehicleAssigned}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">تاريخ الانضمام</Typography>
                <Typography variant="body1">
                  {new Date(selectedDriver.joinDate).toLocaleDateString('ar-SA')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">إجمالي الأرباح</Typography>
                <Typography variant="body1">{formatCurrency(selectedDriver.totalEarnings)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">إجمالي الرحلات</Typography>
                <Typography variant="body1">{formatNumber(selectedDriver.totalTrips)}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>إغلاق</Button>
          <Button variant="contained">تعديل</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default HRManagement;
