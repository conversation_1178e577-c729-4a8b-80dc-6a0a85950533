import uuid
import random
from datetime import datetime, timedelta
import json
import os

# قائمة أسماء يمنية
yemeni_first_names_male = [
    "أحمد", "محمد", "علي", "حسن", "يحيى", "عبدالله", "عبدالرحمن", "خالد", "عمر", "سالم",
    "ناصر", "فيصل", "عبدالعزيز", "مراد", "طارق", "وليد", "ماجد", "سعد", "فهد", "عادل"
]

yemeni_first_names_female = [
    "فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نور", "أمل", "هدى", "رقية",
    "سمية", "أسماء", "حفصة", "جميلة", "نادية", "ليلى", "سلمى", "دعاء", "إيمان", "وفاء"
]

yemeni_last_names = [
    "الحوثي", "الزبيدي", "الأحمر", "المخلافي", "الشامي", "الحضرمي", "العولقي", "الكندي",
    "المحضار", "باعشن", "بافضل", "الحميري", "السبئي", "الهمداني", "المرادي", "الحاشدي"
]

# قائمة مدن يمنية وإحداثياتها
yemen_cities = {
    "صنعاء": {"lat": 15.3520, "lon": 44.2065},
    "عدن": {"lat": 12.7937, "lon": 45.0292},
    "تعز": {"lat": 13.5772, "lon": 44.0270},
    "الحديدة": {"lat": 14.8068, "lon": 42.9461},
    "إب": {"lat": 13.9667, "lon": 44.1833},
    "المكلا": {"lat": 14.5385, "lon": 49.1238},
    "مأرب": {"lat": 15.4485, "lon": 45.3468},
    "ذمار": {"lat": 14.5422, "lon": 44.4079},
    "سيئون": {"lat": 15.9380, "lon": 48.7892},
    "عمران": {"lat": 15.6560, "lon": 43.9575},
}

# أنواع الشركات
company_types = [
    "شركة النقل اليمنية", "مؤسسة التوصيل السريع", "شركة المواصلات الحديثة",
    "مؤسسة النقل والخدمات", "شركة التنقل الذكي", "مؤسسة الخدمات اللوجستية"
]

def generate_phone():
    """توليد رقم هاتف يمني"""
    prefixes = ["777", "733", "770", "773", "714", "711"]
    return f"+967{random.choice(prefixes)}{random.randint(100000, 999999)}"

def generate_email(name):
    """توليد بريد إلكتروني"""
    domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com"]
    return f"{name.replace(' ', '').lower()}{random.randint(1, 999)}@{random.choice(domains)}"

def generate_simple_data():
    """توليد بيانات مبسطة لمنصة TecnoDrive"""
    
    print("🚀 بدء توليد البيانات الافتراضية لمنصة TecnoDrive - اليمن")
    print("=" * 60)
    
    # توليد بيانات الشركات
    print("📊 جاري توليد بيانات الشركات...")
    tenants = []
    for i in range(50):
        company_name = f"{random.choice(company_types)} {random.choice(list(yemen_cities.keys()))}"
        tenants.append({
            'id': str(uuid.uuid4()),
            'name': company_name,
            'type': 'company',
            'contact_email': generate_email(company_name),
            'phone_number': generate_phone(),
            'address': f"{random.choice(list(yemen_cities.keys()))}, شارع {random.choice(['الستين', 'الزبيري', 'الثورة', 'الجمهورية'])}",
            'status': random.choice(['active', 'inactive']),
            'created_at': (datetime.now() - timedelta(days=random.randint(30, 700))).strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    
    # توليد بيانات المستخدمين
    print("👥 جاري توليد بيانات المستخدمين...")
    users = []
    for i in range(100):
        first_name = random.choice(yemeni_first_names_male if random.random() < 0.6 else yemeni_first_names_female)
        last_name = random.choice(yemeni_last_names)
        full_name = f"{first_name} {last_name}"
        
        users.append({
            'id': str(uuid.uuid4()),
            'first_name': first_name,
            'last_name': last_name,
            'email': generate_email(full_name),
            'phone_number': generate_phone(),
            'created_at': (datetime.now() - timedelta(days=random.randint(90, 800))).strftime('%Y-%m-%d %H:%M:%S'),
        })
    
    # توليد بيانات السائقين
    print("🚗 جاري توليد بيانات السائقين...")
    drivers = []
    for i in range(100):
        first_name = random.choice(yemeni_first_names_male)
        last_name = random.choice(yemeni_last_names)
        full_name = f"{first_name} {last_name}"
        
        city_name = random.choice(list(yemen_cities.keys()))
        coords = yemen_cities[city_name]
        
        drivers.append({
            'id': str(uuid.uuid4()),
            'tenant_id': random.choice([t['id'] for t in tenants]) if random.random() < 0.4 else None,
            'first_name': first_name,
            'last_name': last_name,
            'email': generate_email(full_name),
            'phone_number': generate_phone(),
            'license_number': f"LIC{i+1:04d}",
            'rating': round(random.uniform(3.5, 5.0), 1),
            'status': random.choice(['on_duty', 'off_duty', 'active']),
            'current_lat': round(coords['lat'] + random.uniform(-0.05, 0.05), 6),
            'current_lon': round(coords['lon'] + random.uniform(-0.05, 0.05), 6),
            'last_location_update': (datetime.now() - timedelta(minutes=random.randint(1, 60))).strftime('%Y-%m-%d %H:%M:%S'),
            'created_at': (datetime.now() - timedelta(days=random.randint(60, 500))).strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    
    print(f"✅ تم توليد {len(tenants)} شركة")
    print(f"✅ تم توليد {len(users)} مستخدم")
    print(f"✅ تم توليد {len(drivers)} سائق")
    
    # حفظ البيانات
    output_dir = "generated_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print("\n💾 جاري حفظ البيانات...")
    
    datasets = [
        (tenants, "tenants_simple.json", "الشركات"),
        (users, "users_simple.json", "المستخدمين"),
        (drivers, "drivers_simple.json", "السائقين")
    ]
    
    for data, filename, description in datasets:
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"✅ تم حفظ بيانات {description} في {filepath}")
    
    print("\n🎉 اكتمل توليد البيانات المبسطة!")
    print(f"📁 الملفات محفوظة في مجلد: {output_dir}/")

if __name__ == "__main__":
    generate_simple_data()
