// Live Operations Service
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Driver, 
  Vehicle, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>er, 
  OperationsStats,
  WebSocketMessage 
} from '../types/operations';

class LiveOperationsService {
  private baseUrl = 'http://localhost:8085/api';
  private wsUrl = 'ws://localhost:8085/ws';
  private ws: WebSocket | null = null;
  private listeners: Map<string, Function[]> = new Map();

  // Initialize WebSocket connection
  initializeWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsUrl);
        
        this.ws.onopen = () => {
          console.log('✅ Live Operations WebSocket connected');
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        };
        
        this.ws.onclose = () => {
          console.log('🔌 Live Operations WebSocket disconnected');
          // Attempt to reconnect after 5 seconds
          setTimeout(() => this.initializeWebSocket(), 5000);
        };
        
        this.ws.onerror = (error) => {
          console.error('❌ Live Operations WebSocket error:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  // Handle incoming WebSocket messages
  private handleWebSocketMessage(message: WebSocketMessage) {
    const listeners = this.listeners.get(message.type) || [];
    listeners.forEach(listener => listener(message.data));
  }

  // Subscribe to WebSocket events
  subscribe(eventType: string, callback: Function) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(callback);
  }

  // Unsubscribe from WebSocket events
  unsubscribe(eventType: string, callback: Function) {
    const listeners = this.listeners.get(eventType) || [];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  // Get live operations data
  async getLiveOperationsData(): Promise<LiveOperationsData> {
    try {
      const response = await fetch(`${this.baseUrl}/operations/live`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          return data.data;
        }
      }
      
      throw new Error('Failed to fetch live operations data');
    } catch (error) {
      console.error('❌ Error fetching live operations data:', error);
      
      // Return mock data as fallback
      return this.getMockLiveOperationsData();
    }
  }

  // Get alerts
  async getAlerts(status?: string): Promise<Alert[]> {
    try {
      const url = status 
        ? `${this.baseUrl}/alerts?status=${status}`
        : `${this.baseUrl}/alerts`;
      
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          return data.data;
        }
      }
      
      throw new Error('Failed to fetch alerts');
    } catch (error) {
      console.error('❌ Error fetching alerts:', error);
      return this.getMockAlerts();
    }
  }

  // Update alert status
  async updateAlertStatus(alertId: string, status: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/alerts/${alertId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      
      return response.ok;
    } catch (error) {
      console.error('❌ Error updating alert status:', error);
      return false;
    }
  }

  // Get operations statistics
  async getOperationsStats(): Promise<OperationsStats> {
    try {
      const response = await fetch(`${this.baseUrl}/operations/stats`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          return data.data;
        }
      }
      
      throw new Error('Failed to fetch operations stats');
    } catch (error) {
      console.error('❌ Error fetching operations stats:', error);
      return this.getMockOperationsStats();
    }
  }

  // Mock data for development/fallback
  private getMockLiveOperationsData(): LiveOperationsData {
    return {
      trips: this.getMockTrips(),
      alerts: this.getMockAlerts(),
      drivers: this.getMockDrivers(),
      vehicles: this.getMockVehicles(),
      parcels: this.getMockParcels(),
      pickers: this.getMockPickers(),
      stats: this.getMockOperationsStats(),
      last_updated: new Date().toISOString()
    };
  }

  private getMockTrips(): Trip[] {
    return [
      {
        trip_id: 'trip_001',
        driver_id: 'driver_001',
        vehicle_id: 'vehicle_001',
        trip_type: 'passenger_ride',
        status: 'in_progress',
        current_location: { lat: 24.7136, lng: 46.6753 },
        estimated_arrival_time: new Date(Date.now() + 15 * 60000).toISOString(),
        start_time: new Date(Date.now() - 10 * 60000).toISOString(),
        origin_address: 'King Fahd Road, Riyadh',
        destination_address: 'Olaya District, Riyadh',
        fare_amount: 25.50
      },
      {
        trip_id: 'trip_002',
        driver_id: 'driver_002',
        vehicle_id: 'vehicle_002',
        trip_type: 'parcel_delivery',
        status: 'waiting_pickup',
        current_location: { lat: 24.7200, lng: 46.6800 },
        estimated_arrival_time: new Date(Date.now() + 20 * 60000).toISOString(),
        start_time: new Date().toISOString(),
        origin_address: 'Al Malaz, Riyadh',
        destination_address: 'Diplomatic Quarter, Riyadh',
        fare_amount: 15.00
      }
    ];
  }

  private getMockAlerts(): Alert[] {
    return [
      {
        alert_id: 'alert_001',
        trip_id: 'trip_001',
        driver_id: 'driver_001',
        alert_type: 'pickup_delay',
        timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
        description: 'Pickup delayed by 12 minutes due to traffic',
        status: 'new',
        location: { lat: 24.7136, lng: 46.6753 },
        severity: 'medium',
        estimated_delay: 12
      },
      {
        alert_id: 'alert_002',
        parcel_id: 'parcel_001',
        alert_type: 'parcel_delivery_delay',
        timestamp: new Date(Date.now() - 2 * 60000).toISOString(),
        description: 'Parcel delivery delayed - recipient not available',
        status: 'new',
        severity: 'high',
        estimated_delay: 30
      }
    ];
  }

  private getMockDrivers(): Driver[] {
    return [
      {
        driver_id: 'driver_001',
        name: 'Ahmed Mohamed',
        contact_info: { phone: '+966501234567', email: '<EMAIL>' },
        current_status: 'busy',
        current_location: { lat: 24.7136, lng: 46.6753 },
        vehicle_id: 'vehicle_001',
        rating: 4.8,
        can_deliver_parcels: true,
        total_trips_today: 8,
        total_earnings_today: 320.50
      },
      {
        driver_id: 'driver_002',
        name: 'Sara Ahmed',
        contact_info: { phone: '+966501234568', email: '<EMAIL>' },
        current_status: 'online',
        current_location: { lat: 24.7200, lng: 46.6800 },
        vehicle_id: 'vehicle_002',
        rating: 4.9,
        can_deliver_parcels: true,
        total_trips_today: 6,
        total_earnings_today: 280.00
      }
    ];
  }

  private getMockVehicles(): Vehicle[] {
    return [
      {
        vehicle_id: 'vehicle_001',
        plate_number: 'ABC-123',
        make: 'Toyota',
        model: 'Camry',
        capacity: { passengers: 4, parcels: 10 },
        current_status: 'busy',
        current_location: { lat: 24.7136, lng: 46.6753 },
        fuel_level: 75,
        odometer: 45230
      },
      {
        vehicle_id: 'vehicle_002',
        plate_number: 'XYZ-456',
        make: 'Honda',
        model: 'Accord',
        capacity: { passengers: 4, parcels: 8 },
        current_status: 'available',
        current_location: { lat: 24.7200, lng: 46.6800 },
        fuel_level: 60,
        odometer: 38750
      }
    ];
  }

  private getMockParcels(): Parcel[] {
    return [
      {
        parcel_id: 'parcel_001',
        sender_id: 'customer_001',
        receiver_id: 'customer_002',
        pickup_address: 'King Khalid Airport, Riyadh',
        delivery_address: 'Al Faisaliah Tower, Riyadh',
        status: 'out_for_delivery',
        weight: 2.5,
        dimensions: { length: 30, width: 20, height: 15 },
        item_description: 'Electronics package',
        tracking_number: 'TRK123456789',
        assigned_driver_id: 'driver_002',
        current_location: { lat: 24.7100, lng: 46.6700 }
      }
    ];
  }

  private getMockPickers(): Picker[] {
    return [
      {
        picker_id: 'picker_001',
        name: 'Mohamed Ali',
        contact_info: { phone: '+966501234569', email: '<EMAIL>' },
        current_status: 'available',
        current_location: { lat: 24.7100, lng: 46.6700 },
        rating: 4.7,
        total_pickups_today: 12
      }
    ];
  }

  private getMockOperationsStats(): OperationsStats {
    return {
      total_active_trips: 15,
      total_active_parcels: 8,
      total_online_drivers: 24,
      total_online_pickers: 6,
      total_available_vehicles: 18,
      total_alerts: 3,
      total_critical_alerts: 1,
      average_trip_duration: 28,
      average_delivery_time: 45,
      on_time_percentage: 87.5,
      revenue_today: 4250.75,
      trips_completed_today: 156,
      parcels_delivered_today: 89
    };
  }

  // Cleanup WebSocket connection
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }
}

export const liveOperationsService = new LiveOperationsService();
export default liveOperationsService;
