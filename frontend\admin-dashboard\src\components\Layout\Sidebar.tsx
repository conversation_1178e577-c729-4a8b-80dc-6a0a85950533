import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  DirectionsCar as RidesIcon,
  DirectionsCar as FleetIcon,
  People as UsersIcon,
  Payment as PaymentsIcon,
  Analytics as AnalyticsIcon,
  Notifications as NotificationsIcon,
  Work as HRIcon,
  LocalShipping as ParcelIcon,
  Settings as SettingsIcon,
  Storage as StorageIcon,
  HealthAndSafety as HealthIcon,
  Map as MapIcon,
  CloudQueue as SaaSIcon,
  Security as SecurityIcon,
  Star as StarIcon,
  IntegrationInstructions as IntegrationInstructionsIcon,
  AccountBalance as FinanceIcon,
  Backup as DataIcon,
} from '@mui/icons-material';

interface SidebarProps {
  onItemClick?: () => void;
}

const menuItems = [
  {
    text: 'لوحة المعلومات',
    icon: <DashboardIcon />,
    path: '/dashboard',
  },
  {
    text: 'لوحة التحكم',
    icon: <SettingsIcon />,
    path: '/control-panel',
  },
  {
    text: 'إدارة الرحلات',
    icon: <RidesIcon />,
    path: '/rides',
  },
  {
    text: 'إدارة الأسطول',
    icon: <FleetIcon />,
    path: '/fleet',
  },
  {
    text: 'خريطة الأسطول',
    icon: <MapIcon />,
    path: '/fleet-map',
  },
  {
    text: 'إدارة المستخدمين',
    icon: <UsersIcon />,
    path: '/users',
  },
  {
    text: 'إدارة المدفوعات',
    icon: <PaymentsIcon />,
    path: '/payments',
  },
  {
    text: 'الإدارة المالية',
    icon: <FinanceIcon />,
    path: '/finance',
  },
  {
    text: 'التحليلات والتقارير',
    icon: <AnalyticsIcon />,
    path: '/analytics',
  },
  {
    text: 'إدارة الإشعارات',
    icon: <NotificationsIcon />,
    path: '/notifications',
  },
  {
    text: 'الموارد البشرية',
    icon: <HRIcon />,
    path: '/hr',
  },
  {
    text: 'إدارة الطرود',
    icon: <ParcelIcon />,
    path: '/parcels',
  },
  {
    text: 'إدارة SaaS',
    icon: <SaaSIcon />,
    path: '/saas',
  },
  {
    text: 'API Gateway',
    icon: <SecurityIcon />,
    path: '/api-gateway',
  },
  {
    text: 'إدارة الإشعارات',
    icon: <NotificationsIcon />,
    path: '/notifications',
  },
  {
    text: 'تقييمات الرحلات',
    icon: <StarIcon />,
    path: '/ride-ratings',
  },
  {
    text: 'إدارة البيانات',
    icon: <StorageIcon />,
    path: '/data-management',
  },
  {
    text: 'تكامل Oracle APEX',
    icon: <IntegrationInstructionsIcon />,
    path: '/oracle-apex',
  },
  {
    text: 'دمج Oracle APEX',
    icon: <StorageIcon />,
    path: '/oracle-apex',
  },
  {
    text: 'نظام المصادقة المتعدد',
    icon: <SecurityIcon />,
    path: '/multi-auth',
  },
  {
    text: 'حالة الخدمات',
    icon: <HealthIcon />,
    path: '/services',
  },
  {
    text: 'إدارة البيانات',
    icon: <DataIcon />,
    path: '/data-management',
  },
  {
    text: 'الإعدادات',
    icon: <SettingsIcon />,
    path: '/settings',
  },
];

const Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleItemClick = (path: string) => {
    navigate(path);
    if (onItemClick) {
      onItemClick();
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and Title */}
      <Toolbar sx={{ px: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              bgcolor: 'primary.main',
              width: 40,
              height: 40,
            }}
          >
            T
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
              تكنو درايف
            </Typography>
            <Typography variant="caption" color="text.secondary">
              لوحة التحكم الإدارية
            </Typography>
          </Box>
        </Box>
      </Toolbar>

      <Divider />

      {/* Navigation Menu */}
      <List sx={{ flexGrow: 1, px: 1 }}>
        {menuItems.map((item) => {
          const isActive = location.pathname.startsWith(item.path);
          
          return (
            <ListItem key={item.path} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleItemClick(item.path)}
                selected={isActive}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'white',
                    },
                  },
                  '&:hover': {
                    backgroundColor: 'action.hover',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 40,
                    color: isActive ? 'white' : 'text.secondary',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                    fontWeight: isActive ? 600 : 400,
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Divider />

      {/* Footer */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          الإصدار 1.0.0
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;
