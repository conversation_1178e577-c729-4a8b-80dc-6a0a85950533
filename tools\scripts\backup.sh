#!/bin/bash

# TecnoDrive Database Backup Script
# This script creates backups of all TecnoDrive databases

set -e

# Configuration
BACKUP_DIR="/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
POSTGRES_HOST="postgres"
POSTGRES_USER="tecnodrive_admin"
POSTGRES_PASSWORD="TecnoDrive2025!Secure#Platform"

# Database list
DATABASES=(
    "tecnodrive_auth"
    "tecnodrive_users"
    "tecnodrive_rides"
    "tecnodrive_fleet"
    "tecnodrive_parcels"
    "tecnodrive_payments"
    "tecnodrive_notifications"
    "tecnodrive_financial"
    "tecnodrive_hr"
    "tecnodrive_analytics"
    "tecnodrive_saas"
    "tecnodrive_location"
    "tecnodrive_tracking"
)

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "Starting TecnoDrive database backup at $(date)"

# Export password for pg_dump
export PGPASSWORD="$POSTGRES_PASSWORD"

# Backup each database
for db in "${DATABASES[@]}"; do
    echo "Backing up database: $db"
    
    backup_file="$BACKUP_DIR/${db}_backup_${TIMESTAMP}.sql"
    
    if pg_dump -h "$POSTGRES_HOST" -U "$POSTGRES_USER" -d "$db" > "$backup_file"; then
        echo "✅ Successfully backed up $db"
        
        # Compress the backup
        gzip "$backup_file"
        echo "✅ Compressed backup: ${backup_file}.gz"
    else
        echo "❌ Failed to backup $db"
    fi
done

# Create a combined backup of all databases
echo "Creating combined backup..."
combined_backup="$BACKUP_DIR/tecnodrive_full_backup_${TIMESTAMP}.sql"

if pg_dumpall -h "$POSTGRES_HOST" -U "$POSTGRES_USER" > "$combined_backup"; then
    echo "✅ Successfully created combined backup"
    gzip "$combined_backup"
    echo "✅ Compressed combined backup: ${combined_backup}.gz"
else
    echo "❌ Failed to create combined backup"
fi

# Clean up old backups (keep last 7 days)
echo "Cleaning up old backups..."
find "$BACKUP_DIR" -name "*.gz" -type f -mtime +7 -delete
echo "✅ Cleanup completed"

# Display backup summary
echo ""
echo "=== Backup Summary ==="
echo "Backup completed at: $(date)"
echo "Backup location: $BACKUP_DIR"
echo "Files created:"
ls -lh "$BACKUP_DIR"/*"$TIMESTAMP"*.gz 2>/dev/null || echo "No backup files found"

echo "✅ TecnoDrive backup process completed successfully!"
