import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  IconButton,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Tooltip,
  Badge,
  Divider
} from '@mui/material';
import {
  Search,
  FilterList,
  Refresh,
  MoreVert,
  DirectionsCar,
  Person,
  Schedule,
  LocationOn,
  Payment,
  Star,
  Visibility,
  Edit,
  Delete
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridToolbar } from '@mui/x-data-grid';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { generateMockRides } from '../../utils/mockLiveData';

// Types
interface RideData {
  id: string;
  customerId: string;
  customerName: string;
  driverId: string;
  driverName: string;
  vehicleId: string;
  vehicleType: 'sedan' | 'suv' | 'van' | 'motorcycle';
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  pickupLocation: string;
  dropoffLocation: string;
  pickupTime: string;
  dropoffTime?: string;
  estimatedDuration: number; // minutes
  actualDuration?: number; // minutes
  distance: number; // km
  fare: number;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  rating?: number;
  createdAt: string;
  updatedAt: string;
}

interface FilterState {
  status: string;
  vehicleType: string;
  paymentStatus: string;
  dateFrom: Date | null;
  dateTo: Date | null;
  driverId: string;
  customerId: string;
}

// Use enhanced mock data from utils

const EnhancedRidesManagement: React.FC = () => {
  // State
  const [rides, setRides] = useState<RideData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterState>({
    status: '',
    vehicleType: '',
    paymentStatus: '',
    dateFrom: null,
    dateTo: null,
    driverId: '',
    customerId: ''
  });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRide, setSelectedRide] = useState<RideData | null>(null);

  // Load data
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setRides(generateMockRides(50));
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search logic
  const filteredRides = useMemo(() => {
    return rides.filter(ride => {
      // Search filter
      const searchMatch = !searchTerm || 
        ride.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ride.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ride.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ride.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ride.dropoffLocation.toLowerCase().includes(searchTerm.toLowerCase());

      // Status filter
      const statusMatch = !filters.status || ride.status === filters.status;
      
      // Vehicle type filter
      const vehicleTypeMatch = !filters.vehicleType || ride.vehicleType === filters.vehicleType;
      
      // Payment status filter
      const paymentStatusMatch = !filters.paymentStatus || ride.paymentStatus === filters.paymentStatus;
      
      // Date range filter
      const dateMatch = (!filters.dateFrom || new Date(ride.createdAt) >= filters.dateFrom) &&
                       (!filters.dateTo || new Date(ride.createdAt) <= filters.dateTo);
      
      // Driver filter
      const driverMatch = !filters.driverId || ride.driverId === filters.driverId;
      
      // Customer filter
      const customerMatch = !filters.customerId || ride.customerId === filters.customerId;

      return searchMatch && statusMatch && vehicleTypeMatch && paymentStatusMatch && 
             dateMatch && driverMatch && customerMatch;
    });
  }, [rides, searchTerm, filters]);

  // Statistics
  const stats = useMemo(() => {
    const total = filteredRides.length;
    const completed = filteredRides.filter(r => r.status === 'completed').length;
    const inProgress = filteredRides.filter(r => r.status === 'in_progress').length;
    const pending = filteredRides.filter(r => r.status === 'pending').length;
    const totalRevenue = filteredRides
      .filter(r => r.paymentStatus === 'paid')
      .reduce((sum, r) => sum + r.fare, 0);
    const avgRating = filteredRides
      .filter(r => r.rating)
      .reduce((sum, r, _, arr) => sum + (r.rating || 0) / arr.length, 0);

    return { total, completed, inProgress, pending, totalRevenue, avgRating };
  }, [filteredRides]);

  // Status color mapping
  const getStatusColor = (status: RideData['status']) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'primary';
      case 'assigned': return 'info';
      case 'pending': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  // Payment status color mapping
  const getPaymentStatusColor = (status: RideData['paymentStatus']) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'refunded': return 'info';
      default: return 'default';
    }
  };

  // Handle filter changes
  const handleFilterChange = (field: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      status: '',
      vehicleType: '',
      paymentStatus: '',
      dateFrom: null,
      dateTo: null,
      driverId: '',
      customerId: ''
    });
    setSearchTerm('');
  };

  // Handle menu actions
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, ride: RideData) => {
    setAnchorEl(event.currentTarget);
    setSelectedRide(ride);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRide(null);
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'Ride ID',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" fontWeight="bold">
          {params.value}
        </Typography>
      )
    },
    {
      field: 'customerName',
      headerName: 'Customer',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Person fontSize="small" />
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      )
    },
    {
      field: 'driverName',
      headerName: 'Driver',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DirectionsCar fontSize="small" />
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      )
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
          variant="outlined"
        />
      )
    },
    {
      field: 'vehicleType',
      headerName: 'Vehicle',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" variant="outlined" />
      )
    },
    {
      field: 'pickupLocation',
      headerName: 'Pickup',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationOn fontSize="small" color="primary" />
          <Typography variant="body2" noWrap>{params.value}</Typography>
        </Box>
      )
    },
    {
      field: 'dropoffLocation',
      headerName: 'Dropoff',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationOn fontSize="small" color="secondary" />
          <Typography variant="body2" noWrap>{params.value}</Typography>
        </Box>
      )
    },
    {
      field: 'fare',
      headerName: 'Fare',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" fontWeight="bold">
          ${params.value}
        </Typography>
      )
    },
    {
      field: 'paymentStatus',
      headerName: 'Payment',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          color={getPaymentStatusColor(params.value)}
          size="small"
        />
      )
    },
    {
      field: 'rating',
      headerName: 'Rating',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        params.value ? (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Star fontSize="small" color="warning" />
            <Typography variant="body2">{params.value}</Typography>
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary">-</Typography>
        )
      )
    },
    {
      field: 'createdAt',
      headerName: 'Created',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString()}
        </Typography>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 80,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuClick(e, params.row)}
        >
          <MoreVert />
        </IconButton>
      )
    }
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
          <Typography variant="h5" gutterBottom>
            Enhanced Rides Management
          </Typography>

          {/* Statistics Cards */}
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6} sm={3} md={2}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Typography variant="h6">{stats.total}</Typography>
                  <Typography variant="caption" color="text.secondary">Total Rides</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3} md={2}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Typography variant="h6">{stats.completed}</Typography>
                  <Typography variant="caption" color="text.secondary">Completed</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3} md={2}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Typography variant="h6">{stats.inProgress}</Typography>
                  <Typography variant="caption" color="text.secondary">In Progress</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3} md={2}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Typography variant="h6">${stats.totalRevenue.toFixed(2)}</Typography>
                  <Typography variant="caption" color="text.secondary">Revenue</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3} md={2}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Typography variant="h6">{stats.avgRating.toFixed(1)}</Typography>
                  <Typography variant="caption" color="text.secondary">Avg Rating</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Search and Filters */}
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search rides, customers, drivers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
                size="small"
              />
            </Grid>
            
            <Grid item xs={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e: SelectChangeEvent) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="assigned">Assigned</MenuItem>
                  <MenuItem value="in_progress">In Progress</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Vehicle Type</InputLabel>
                <Select
                  value={filters.vehicleType}
                  label="Vehicle Type"
                  onChange={(e: SelectChangeEvent) => handleFilterChange('vehicleType', e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="sedan">Sedan</MenuItem>
                  <MenuItem value="suv">SUV</MenuItem>
                  <MenuItem value="van">Van</MenuItem>
                  <MenuItem value="motorcycle">Motorcycle</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6} md={2}>
              <DatePicker
                label="From Date"
                value={filters.dateFrom}
                onChange={(date) => handleFilterChange('dateFrom', date)}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={6} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<FilterList />}
                  onClick={clearFilters}
                  size="small"
                >
                  Clear
                </Button>
                <Tooltip title="Refresh">
                  <IconButton onClick={() => setRides(generateMockRides(50))}>
                    <Refresh />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Data Grid */}
        <Paper elevation={1} sx={{ flex: 1 }}>
          <DataGrid
            rows={filteredRides}
            columns={columns}
            loading={loading}
            pageSizeOptions={[25, 50, 100]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } }
            }}
            disableRowSelectionOnClick
            slots={{ toolbar: GridToolbar }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                quickFilterProps: { debounceMs: 500 }
              }
            }}
          />
        </Paper>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleMenuClose}>
            <Visibility fontSize="small" sx={{ mr: 1 }} />
            View Details
          </MenuItem>
          <MenuItem onClick={handleMenuClose}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Ride
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            <Delete fontSize="small" sx={{ mr: 1 }} />
            Cancel Ride
          </MenuItem>
        </Menu>
      </Box>
    </LocalizationProvider>
  );
};

export default EnhancedRidesManagement;
