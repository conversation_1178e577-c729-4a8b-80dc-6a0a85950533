import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Slider,
  Alert,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Speed as SpeedIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Timer as TimerIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { apiGatewayService, RateLimitConfigDto } from '../../services/apiGatewayService';

const RateLimitingConfig: React.FC = () => {
  const [rateLimits, setRateLimits] = useState<RateLimitConfigDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterTier, setFilterTier] = useState('ALL');
  const [filterEnabled, setFilterEnabled] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedRateLimit, setSelectedRateLimit] = useState<RateLimitConfigDto | null>(null);
  const [newRateLimit, setNewRateLimit] = useState({
    name: '',
    tier: 'BASIC' as 'BASIC' | 'PREMIUM' | 'ENTERPRISE',
    requestsPerMinute: 100,
    requestsPerHour: 5000,
    requestsPerDay: 100000,
    burstCapacity: 200,
    enabled: true,
  });

  // Load rate limits data
  const loadRateLimits = async () => {
    try {
      setLoading(true);
      const response = await apiGatewayService.getRateLimitConfigs();
      
      if (response.success && response.data) {
        setRateLimits(response.data);
      }
    } catch (error) {
      console.error('Error loading rate limits:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRateLimits();
  }, []);

  const getTierChip = (tier: string) => {
    const tierConfig = {
      BASIC: { label: 'أساسي', color: 'default' as const, icon: <SpeedIcon fontSize="small" /> },
      PREMIUM: { label: 'متقدم', color: 'warning' as const, icon: <SpeedIcon fontSize="small" /> },
      ENTERPRISE: { label: 'مؤسسي', color: 'success' as const, icon: <SpeedIcon fontSize="small" /> },
    };

    const config = tierConfig[tier as keyof typeof tierConfig] || { 
      label: tier, 
      color: 'default' as const, 
      icon: <SpeedIcon fontSize="small" /> 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getStatusChip = (enabled: boolean) => {
    return (
      <Chip
        label={enabled ? 'مفعل' : 'معطل'}
        color={enabled ? 'success' : 'default'}
        size="small"
        variant="outlined"
        icon={enabled ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
      />
    );
  };

  const handleEditRateLimit = (rateLimit: RateLimitConfigDto) => {
    setSelectedRateLimit(rateLimit);
    setOpenEditDialog(true);
  };

  const handleDeleteRateLimit = async (rateLimitId: string) => {
    if (window.confirm('هل أنت متأكد من حذف تكوين التحديد هذا؟')) {
      try {
        // TODO: Implement delete API call
        console.log('Delete rate limit:', rateLimitId);
        loadRateLimits();
      } catch (error) {
        console.error('Error deleting rate limit:', error);
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم التكوين',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <SpeedIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'tier',
      headerName: 'المستوى',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getTierChip(params.value),
    },
    {
      field: 'requestsPerMinute',
      headerName: 'طلبات/دقيقة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'requestsPerHour',
      headerName: 'طلبات/ساعة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'requestsPerDay',
      headerName: 'طلبات/يوم',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'burstCapacity',
      headerName: 'السعة الانفجارية',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value.toLocaleString()}
          size="small"
          color="info"
          variant="filled"
        />
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEditRateLimit(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteRateLimit(params.id as string)}
        />,
      ],
    },
  ];

  const filteredRateLimits = rateLimits.filter(rateLimit => {
    const matchesSearch = rateLimit.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTier = filterTier === 'ALL' || rateLimit.tier === filterTier;
    const matchesEnabled = filterEnabled === 'ALL' || 
                          (filterEnabled === 'ENABLED' && rateLimit.enabled) ||
                          (filterEnabled === 'DISABLED' && !rateLimit.enabled);
    
    return matchesSearch && matchesTier && matchesEnabled;
  });

  const handleAddRateLimit = async () => {
    try {
      // TODO: Implement create API call
      console.log('Creating rate limit:', newRateLimit);
      setOpenAddDialog(false);
      setNewRateLimit({
        name: '',
        tier: 'BASIC',
        requestsPerMinute: 100,
        requestsPerHour: 5000,
        requestsPerDay: 100000,
        burstCapacity: 200,
        enabled: true,
      });
      loadRateLimits();
    } catch (error) {
      console.error('Error creating rate limit:', error);
    }
  };

  const getTierRecommendations = (tier: string) => {
    switch (tier) {
      case 'BASIC':
        return {
          requestsPerMinute: 100,
          requestsPerHour: 5000,
          requestsPerDay: 100000,
          burstCapacity: 200,
        };
      case 'PREMIUM':
        return {
          requestsPerMinute: 500,
          requestsPerHour: 25000,
          requestsPerDay: 500000,
          burstCapacity: 1000,
        };
      case 'ENTERPRISE':
        return {
          requestsPerMinute: 2000,
          requestsPerHour: 100000,
          requestsPerDay: 2000000,
          burstCapacity: 5000,
        };
      default:
        return newRateLimit;
    }
  };

  const handleTierChange = (tier: string) => {
    const recommendations = getTierRecommendations(tier);
    setNewRateLimit({
      ...newRateLimit,
      tier: tier as any,
      ...recommendations,
    });
  };

  // Calculate stats
  const totalConfigs = rateLimits.length;
  const enabledConfigs = rateLimits.filter(r => r.enabled).length;
  const basicTier = rateLimits.filter(r => r.tier === 'BASIC').length;
  const premiumTier = rateLimits.filter(r => r.tier === 'PREMIUM').length;
  const enterpriseTier = rateLimits.filter(r => r.tier === 'ENTERPRISE').length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          تكوين تحديد المعدل
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة وتكوين حدود المعدل لحماية API Gateway من الإفراط في الاستخدام
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalConfigs}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي التكوينات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {enabledConfigs}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                التكوينات المفعلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'default' }}>
                {basicTier}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المستوى الأساسي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {premiumTier}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المستوى المتقدم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {enterpriseTier}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المستوى المؤسسي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في التكوينات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>المستوى</InputLabel>
              <Select
                value={filterTier}
                label="المستوى"
                onChange={(e) => setFilterTier(e.target.value)}
              >
                <MenuItem value="ALL">جميع المستويات</MenuItem>
                <MenuItem value="BASIC">أساسي</MenuItem>
                <MenuItem value="PREMIUM">متقدم</MenuItem>
                <MenuItem value="ENTERPRISE">مؤسسي</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterEnabled}
                label="الحالة"
                onChange={(e) => setFilterEnabled(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ENABLED">مفعل</MenuItem>
                <MenuItem value="DISABLED">معطل</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة تكوين
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredRateLimits}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Rate Limit Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة تكوين تحديد معدل جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Alert severity="info">
              تحديد المعدل يساعد في حماية API من الإفراط في الاستخدام والهجمات
            </Alert>
            
            <TextField
              label="اسم التكوين"
              value={newRateLimit.name}
              onChange={(e) => setNewRateLimit({ ...newRateLimit, name: e.target.value })}
              fullWidth
              required
            />
            
            <FormControl fullWidth required>
              <InputLabel>المستوى</InputLabel>
              <Select
                value={newRateLimit.tier}
                label="المستوى"
                onChange={(e) => handleTierChange(e.target.value)}
              >
                <MenuItem value="BASIC">أساسي</MenuItem>
                <MenuItem value="PREMIUM">متقدم</MenuItem>
                <MenuItem value="ENTERPRISE">مؤسسي</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="طلبات في الدقيقة"
                  type="number"
                  value={newRateLimit.requestsPerMinute}
                  onChange={(e) => setNewRateLimit({ ...newRateLimit, requestsPerMinute: parseInt(e.target.value) || 0 })}
                  fullWidth
                  inputProps={{ min: 1, max: 10000 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="طلبات في الساعة"
                  type="number"
                  value={newRateLimit.requestsPerHour}
                  onChange={(e) => setNewRateLimit({ ...newRateLimit, requestsPerHour: parseInt(e.target.value) || 0 })}
                  fullWidth
                  inputProps={{ min: 1, max: 1000000 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="طلبات في اليوم"
                  type="number"
                  value={newRateLimit.requestsPerDay}
                  onChange={(e) => setNewRateLimit({ ...newRateLimit, requestsPerDay: parseInt(e.target.value) || 0 })}
                  fullWidth
                  inputProps={{ min: 1, max: 10000000 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="السعة الانفجارية"
                  type="number"
                  value={newRateLimit.burstCapacity}
                  onChange={(e) => setNewRateLimit({ ...newRateLimit, burstCapacity: parseInt(e.target.value) || 0 })}
                  fullWidth
                  inputProps={{ min: 1, max: 50000 }}
                  helperText="الحد الأقصى للطلبات المتتالية"
                />
              </Grid>
            </Grid>

            <FormControlLabel
              control={
                <Switch
                  checked={newRateLimit.enabled}
                  onChange={(e) => setNewRateLimit({ ...newRateLimit, enabled: e.target.checked })}
                />
              }
              label="تفعيل التكوين"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddRateLimit} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RateLimitingConfig;
