package com.tecnodrive.paymentservice.dto;

import com.tecnodrive.paymentservice.entity.Payment;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;

/**
 * Payment Request DTO
 * 
 * Used for creating new payment transactions
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentRequest {

    /**
     * ID of the entity being paid for (e.g., ride ID, delivery ID)
     */
    @NotBlank(message = "Entity ID is required")
    private String entityId;

    /**
     * Type of entity being paid for (e.g., RIDE, DELIVERY, SUBSCRIPTION)
     */
    @NotBlank(message = "Entity type is required")
    private String entityType;

    /**
     * ID of the user making the payment
     */
    @NotBlank(message = "Payer user ID is required")
    private String payerUserId;

    /**
     * ID of the user receiving the payment (optional, for peer-to-peer payments)
     */
    private String payeeUserId;

    /**
     * Payment amount
     */
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @Digits(integer = 17, fraction = 2, message = "Amount must have at most 2 decimal places")
    private BigDecimal amount;

    /**
     * Currency code (e.g., USD, EUR, SAR)
     */
    @NotBlank(message = "Currency is required")
    @Size(min = 3, max = 3, message = "Currency must be 3 characters")
    private String currency;

    /**
     * Payment method used
     */
    private Payment.PaymentMethod paymentMethod;

    /**
     * Payment description or notes
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /**
     * Additional metadata as JSON string
     */
    private String metadata;
}
