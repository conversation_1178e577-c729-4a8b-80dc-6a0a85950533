{{- if .Values.application.common.podDisruptionBudget.enabled }}
# API Gateway PodDisruptionBudget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-api-gateway-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: api-gateway-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tecno-drive.com/service: api-gateway
spec:
  minAvailable: {{ .Values.application.common.podDisruptionBudget.minAvailable | default 2 }}
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/instance: {{ .Release.Name }}

---
# Auth Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.authService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-auth-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: auth-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: auth-service
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: auth-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# User Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.userService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-user-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: user-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: user-service
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: user-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# Ride Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.rideService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-ride-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: ride-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: ride-service
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: ride-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# Fleet Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.fleetService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-fleet-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: fleet-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: fleet-service
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: fleet-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# Payment Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.paymentService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-payment-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: payment-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: payment-service
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: payment-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# Notification Service PodDisruptionBudget
{{- if .Values.application.tecnoDrive.notificationService.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tecno-drive-notification-service-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: notification-service-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/service: notification-service
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: notification-service
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

---
# Gatekeeper PodDisruptionBudget
{{- if .Values.gatekeeper.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: gatekeeper-controller-manager-pdb
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: gatekeeper-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    gatekeeper.sh/system: "yes"
spec:
  minAvailable: 1
  selector:
    matchLabels:
      control-plane: controller-manager
      gatekeeper.sh/operation: webhook
      gatekeeper.sh/system: "yes"
{{- end }}

---
# Prometheus PodDisruptionBudget
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: prometheus-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: prometheus-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: monitoring
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus
{{- end }}

---
# Grafana PodDisruptionBudget
{{- if .Values.monitoring.grafana.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: grafana-pdb
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: grafana-pdb
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: monitoring
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
{{- end }}

---
# ConfigMap with PDB Best Practices
apiVersion: v1
kind: ConfigMap
metadata:
  name: pdb-best-practices
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: pdb-best-practices
    app.kubernetes.io/instance: {{ .Release.Name }}
data:
  pdb-guidelines.md: |
    # PodDisruptionBudget Best Practices for TECNO DRIVE
    
    ## Overview
    PodDisruptionBudgets (PDBs) help ensure application availability during voluntary disruptions
    such as node maintenance, cluster upgrades, or application deployments.
    
    ## TECNO DRIVE PDB Strategy
    
    ### Critical Services (minAvailable: 2+)
    - **API Gateway**: 2 pods minimum (high traffic entry point)
    - **Ride Service**: 2 pods minimum (core business logic)
    
    ### Important Services (minAvailable: 1)
    - **Auth Service**: 1 pod minimum (authentication required)
    - **User Service**: 1 pod minimum (user management)
    - **Payment Service**: 1 pod minimum (financial operations)
    - **Fleet Service**: 1 pod minimum (fleet management)
    - **Notification Service**: 1 pod minimum (communication)
    
    ### Infrastructure Services
    - **Gatekeeper**: 1 pod minimum (security enforcement)
    - **Prometheus**: 1 pod minimum (monitoring)
    - **Grafana**: 1 pod minimum (observability)
    
    ## Configuration Guidelines
    
    ### Use minAvailable for Critical Services
    ```yaml
    spec:
      minAvailable: 2  # Ensures at least 2 pods always running
    ```
    
    ### Use maxUnavailable for Flexible Services
    ```yaml
    spec:
      maxUnavailable: 1  # Allows 1 pod to be unavailable
    ```
    
    ### Consider Replica Count
    - PDB should allow for at least 1 pod to be disrupted
    - For 3 replicas: minAvailable: 2 or maxUnavailable: 1
    - For 2 replicas: minAvailable: 1 or maxUnavailable: 1
    
    ## Monitoring PDBs
    
    ### Check PDB Status
    ```bash
    kubectl get pdb -n tecno-drive-system
    kubectl describe pdb <pdb-name> -n tecno-drive-system
    ```
    
    ### Common Issues
    1. **PDB too restrictive**: Prevents necessary updates
    2. **PDB too permissive**: Allows service outages
    3. **Mismatched selectors**: PDB doesn't apply to intended pods
    
    ### Alerts
    - Monitor `kube_poddisruptionbudget_status_current_healthy` metric
    - Alert when healthy pods < minAvailable
    - Alert when PDB blocks disruptions for too long
    
    ## Testing PDBs
    
    ### Simulate Disruption
    ```bash
    # Drain a node to test PDB behavior
    kubectl drain <node-name> --ignore-daemonsets --delete-emptydir-data
    
    # Check if services remain available
    kubectl get pods -n tecno-drive-system
    ```
    
    ### Validate Configuration
    ```bash
    # Check if PDB allows at least one disruption
    kubectl get pdb -o yaml | grep -A 5 -B 5 "disruptionsAllowed: 0"
    ```
    
    ## Emergency Procedures
    
    ### Temporarily Disable PDB
    ```bash
    # Scale down PDB (emergency only)
    kubectl patch pdb <pdb-name> -p '{"spec":{"minAvailable":0}}'
    
    # Or delete PDB temporarily
    kubectl delete pdb <pdb-name>
    ```
    
    ### Re-enable PDB
    ```bash
    # Restore original PDB configuration
    kubectl apply -f pdb.yaml
    ```

  pdb-monitoring.yaml: |
    # Prometheus rules for PDB monitoring
    groups:
      - name: pdb-monitoring
        rules:
          - alert: PDBViolation
            expr: kube_poddisruptionbudget_status_current_healthy < kube_poddisruptionbudget_status_desired_healthy
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "PodDisruptionBudget violation detected"
              description: "PDB {{ $labels.poddisruptionbudget }} has {{ $value }} healthy pods, below the desired {{ $labels.desired_healthy }}"
          
          - alert: PDBBlockingDisruptions
            expr: kube_poddisruptionbudget_status_disruptions_allowed == 0
            for: 30m
            labels:
              severity: warning
            annotations:
              summary: "PodDisruptionBudget blocking all disruptions"
              description: "PDB {{ $labels.poddisruptionbudget }} is blocking all disruptions for 30+ minutes"
{{- end }}
