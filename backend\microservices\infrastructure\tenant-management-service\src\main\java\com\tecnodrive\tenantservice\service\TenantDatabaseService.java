package com.tecnodrive.tenantservice.service;

import com.tecnodrive.tenantservice.model.Tenant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Service for managing tenant-specific databases
 * Creates isolated databases for each tenant in SaaS model
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TenantDatabaseService {

    private final DataSource masterDataSource;

    @Value("${app.database.host:localhost}")
    private String databaseHost;

    @Value("${app.database.port:5432}")
    private String databasePort;

    @Value("${app.database.admin-username:postgres}")
    private String adminUsername;

    @Value("${app.database.admin-password:password}")
    private String adminPassword;

    /**
     * Creates a new database for a tenant
     */
    public String createTenantDatabase(Tenant tenant) {
        String databaseName = generateDatabaseName(tenant.getTenantId());
        String username = generateUsername(tenant.getTenantId());
        String password = generatePassword();

        try {
            // Create database
            createDatabase(databaseName);
            
            // Create user
            createDatabaseUser(username, password);
            
            // Grant permissions
            grantDatabasePermissions(databaseName, username);
            
            // Initialize schema
            initializeTenantSchema(databaseName, username, password);
            
            log.info("Successfully created database {} for tenant {}", databaseName, tenant.getTenantId());
            
            return buildConnectionUrl(databaseName, username, password);
            
        } catch (SQLException e) {
            log.error("Failed to create database for tenant {}: {}", tenant.getTenantId(), e.getMessage());
            throw new RuntimeException("Database creation failed", e);
        }
    }

    /**
     * Deletes a tenant database (use with caution)
     */
    public void deleteTenantDatabase(String tenantId) {
        String databaseName = generateDatabaseName(tenantId);
        String username = generateUsername(tenantId);

        try (Connection connection = masterDataSource.getConnection()) {
            // Terminate active connections
            terminateConnections(connection, databaseName);
            
            // Drop database
            dropDatabase(connection, databaseName);
            
            // Drop user
            dropUser(connection, username);
            
            log.info("Successfully deleted database {} for tenant {}", databaseName, tenantId);
            
        } catch (SQLException e) {
            log.error("Failed to delete database for tenant {}: {}", tenantId, e.getMessage());
            throw new RuntimeException("Database deletion failed", e);
        }
    }

    /**
     * Checks if tenant database exists and is accessible
     */
    public boolean validateTenantDatabase(String databaseUrl, String username, String password) {
        try (Connection connection = java.sql.DriverManager.getConnection(databaseUrl, username, password)) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.warn("Database validation failed: {}", e.getMessage());
            return false;
        }
    }

    private void createDatabase(String databaseName) throws SQLException {
        try (Connection connection = masterDataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                 "CREATE DATABASE " + databaseName + " WITH ENCODING='UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8'")) {
            stmt.execute();
        }
    }

    private void createDatabaseUser(String username, String password) throws SQLException {
        try (Connection connection = masterDataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                 "CREATE USER " + username + " WITH PASSWORD '" + password + "'")) {
            stmt.execute();
        }
    }

    private void grantDatabasePermissions(String databaseName, String username) throws SQLException {
        try (Connection connection = masterDataSource.getConnection()) {
            // Grant database privileges
            try (PreparedStatement stmt = connection.prepareStatement(
                 "GRANT ALL PRIVILEGES ON DATABASE " + databaseName + " TO " + username)) {
                stmt.execute();
            }
            
            // Grant schema privileges
            try (PreparedStatement stmt = connection.prepareStatement(
                 "GRANT ALL ON SCHEMA public TO " + username)) {
                stmt.execute();
            }
        }
    }

    private void initializeTenantSchema(String databaseName, String username, String password) {
        String connectionUrl = buildConnectionUrl(databaseName, username, password);
        
        try (Connection connection = java.sql.DriverManager.getConnection(connectionUrl, username, password)) {
            // Create basic tables for tenant
            createTenantTables(connection);
            log.info("Initialized schema for database {}", databaseName);
        } catch (SQLException e) {
            log.error("Failed to initialize schema for database {}: {}", databaseName, e.getMessage());
            throw new RuntimeException("Schema initialization failed", e);
        }
    }

    private void createTenantTables(Connection connection) throws SQLException {
        String[] createTableStatements = {
            // Users table
            """
            CREATE TABLE users (
                id BIGSERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                username VARCHAR(255) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(255),
                last_name VARCHAR(255),
                phone VARCHAR(50),
                role VARCHAR(50) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            // Drivers table
            """
            CREATE TABLE drivers (
                id BIGSERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                user_id BIGINT REFERENCES users(id),
                license_number VARCHAR(255) UNIQUE NOT NULL,
                license_expiry DATE,
                vehicle_id BIGINT,
                status VARCHAR(50) DEFAULT 'AVAILABLE',
                rating DECIMAL(3,2) DEFAULT 0.0,
                total_trips INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            // Vehicles table
            """
            CREATE TABLE vehicles (
                id BIGSERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                license_plate VARCHAR(255) UNIQUE NOT NULL,
                make VARCHAR(255),
                model VARCHAR(255),
                year INTEGER,
                color VARCHAR(100),
                vehicle_type VARCHAR(100),
                status VARCHAR(50) DEFAULT 'AVAILABLE',
                current_driver_id BIGINT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            // Rides table
            """
            CREATE TABLE rides (
                id BIGSERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                customer_id BIGINT REFERENCES users(id),
                driver_id BIGINT REFERENCES drivers(id),
                vehicle_id BIGINT REFERENCES vehicles(id),
                pickup_address TEXT,
                pickup_latitude DECIMAL(10,8),
                pickup_longitude DECIMAL(11,8),
                destination_address TEXT,
                destination_latitude DECIMAL(10,8),
                destination_longitude DECIMAL(11,8),
                status VARCHAR(50) DEFAULT 'REQUESTED',
                fare_amount DECIMAL(10,2),
                distance_km DECIMAL(8,2),
                duration_minutes INTEGER,
                requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        };

        for (String sql : createTableStatements) {
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
            }
        }
    }

    private void terminateConnections(Connection connection, String databaseName) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(
             "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = ? AND pid <> pg_backend_pid()")) {
            stmt.setString(1, databaseName);
            stmt.execute();
        }
    }

    private void dropDatabase(Connection connection, String databaseName) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement("DROP DATABASE IF EXISTS " + databaseName)) {
            stmt.execute();
        }
    }

    private void dropUser(Connection connection, String username) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement("DROP USER IF EXISTS " + username)) {
            stmt.execute();
        }
    }

    private String generateDatabaseName(String tenantId) {
        return "tecnodrive_" + tenantId.toLowerCase().replaceAll("[^a-z0-9]", "_");
    }

    private String generateUsername(String tenantId) {
        return "user_" + tenantId.toLowerCase().replaceAll("[^a-z0-9]", "_");
    }

    private String generatePassword() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    private String buildConnectionUrl(String databaseName, String username, String password) {
        return String.format("**********************************************", 
                            databaseHost, databasePort, databaseName, username, password);
    }
}
