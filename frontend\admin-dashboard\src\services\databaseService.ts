import axios from 'axios';
import {
  DatabaseConnection,
  TableInfo,
  QueryResult,
  DatabaseStats,
  ColumnInfo
} from '../types/api';

// Database Service for TECNO DRIVE Platform
// Provides comprehensive database access and management capabilities

class DatabaseService {
  private baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080';

  // Database Connections Management
  async getDatabaseConnections(): Promise<DatabaseConnection[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/connections`);
      return response.data;
    } catch (error) {
      console.error('Error fetching database connections:', error);
      // Return mock data for development
      return this.getMockConnections();
    }
  }

  async testConnection(connectionId: string): Promise<boolean> {
    try {
      const response = await axios.post(`${this.baseURL}/api/database/test-connection`, {
        connectionId
      });
      return response.data.success;
    } catch (error) {
      console.error('Error testing connection:', error);
      return false;
    }
  }

  // Tables and Schema Information
  async getTables(database: string): Promise<TableInfo[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/${database}/tables`);
      return response.data;
    } catch (error) {
      console.error('Error fetching tables:', error);
      return this.getMockTables(database);
    }
  }

  async getTableSchema(database: string, tableName: string): Promise<ColumnInfo[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/${database}/tables/${tableName}/schema`);
      return response.data;
    } catch (error) {
      console.error('Error fetching table schema:', error);
      return this.getMockSchema(tableName);
    }
  }

  // Data Querying and Management
  async executeQuery(database: string, query: string, limit: number = 100): Promise<QueryResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/database/${database}/query`, {
        query,
        limit
      });
      return response.data;
    } catch (error) {
      console.error('Error executing query:', error);
      return this.getMockQueryResult(query);
    }
  }

  async getTableData(database: string, tableName: string, page: number = 1, limit: number = 50): Promise<QueryResult> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/${database}/tables/${tableName}/data`, {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching table data:', error);
      return this.getMockTableData(tableName, page, limit);
    }
  }

  async searchTableData(database: string, tableName: string, searchTerm: string, column?: string): Promise<QueryResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/database/${database}/tables/${tableName}/search`, {
        searchTerm,
        column
      });
      return response.data;
    } catch (error) {
      console.error('Error searching table data:', error);
      return this.getMockSearchResult(tableName, searchTerm);
    }
  }

  // CRUD Operations
  async insertRecord(database: string, tableName: string, data: Record<string, any>): Promise<boolean> {
    try {
      const response = await axios.post(`${this.baseURL}/api/database/${database}/tables/${tableName}/insert`, data);
      return response.data.success;
    } catch (error) {
      console.error('Error inserting record:', error);
      return false;
    }
  }

  async updateRecord(database: string, tableName: string, id: string, data: Record<string, any>): Promise<boolean> {
    try {
      const response = await axios.put(`${this.baseURL}/api/database/${database}/tables/${tableName}/${id}`, data);
      return response.data.success;
    } catch (error) {
      console.error('Error updating record:', error);
      return false;
    }
  }

  async deleteRecord(database: string, tableName: string, id: string): Promise<boolean> {
    try {
      const response = await axios.delete(`${this.baseURL}/api/database/${database}/tables/${tableName}/${id}`);
      return response.data.success;
    } catch (error) {
      console.error('Error deleting record:', error);
      return false;
    }
  }

  // Database Statistics
  async getDatabaseStats(): Promise<DatabaseStats> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching database stats:', error);
      return this.getMockStats();
    }
  }

  // Export/Import Operations
  async exportTable(database: string, tableName: string, format: 'csv' | 'json' | 'excel'): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/api/database/${database}/tables/${tableName}/export`, {
        params: { format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting table:', error);
      throw error;
    }
  }

  async importData(database: string, tableName: string, file: File): Promise<boolean> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${this.baseURL}/api/database/${database}/tables/${tableName}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data.success;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Mock Data for Development
  private getMockConnections(): DatabaseConnection[] {
    return [
      {
        id: 'conn-1',
        name: 'Main PostgreSQL',
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        database: 'tecnodrive_main',
        status: 'connected',
        lastChecked: new Date().toISOString()
      },
      {
        id: 'conn-2',
        name: 'Auth Database',
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        database: 'tecnodrive_auth',
        status: 'connected',
        lastChecked: new Date().toISOString()
      },
      {
        id: 'conn-3',
        name: 'Redis Cache',
        type: 'redis',
        host: 'localhost',
        port: 6379,
        database: 'cache',
        status: 'connected',
        lastChecked: new Date().toISOString()
      },
      {
        id: 'conn-4',
        name: 'MongoDB Logs',
        type: 'mongodb',
        host: 'localhost',
        port: 27017,
        database: 'tecnodrive_logs',
        status: 'connected',
        lastChecked: new Date().toISOString()
      }
    ];
  }

  private getMockTables(database: string): TableInfo[] {
    const baseTables = [
      {
        tableName: 'users',
        database,
        rowCount: 50000,
        columns: [],
        indexes: ['email', 'phone', 'tenant_id'],
        size: '25 MB',
        lastUpdated: new Date().toISOString()
      },
      {
        tableName: 'rides',
        database,
        rowCount: 200000,
        columns: [],
        indexes: ['passenger_id', 'driver_id', 'status'],
        size: '150 MB',
        lastUpdated: new Date().toISOString()
      },
      {
        tableName: 'vehicles',
        database,
        rowCount: 5000,
        columns: [],
        indexes: ['license_plate', 'status'],
        size: '5 MB',
        lastUpdated: new Date().toISOString()
      },
      {
        tableName: 'payments',
        database,
        rowCount: 180000,
        columns: [],
        indexes: ['transaction_id', 'user_id', 'status'],
        size: '80 MB',
        lastUpdated: new Date().toISOString()
      }
    ];

    return baseTables;
  }

  private getMockSchema(tableName: string): ColumnInfo[] {
    const schemas: Record<string, ColumnInfo[]> = {
      users: [
        { name: 'id', type: 'UUID', nullable: false, primaryKey: true },
        { name: 'email', type: 'VARCHAR(255)', nullable: false, primaryKey: false },
        { name: 'first_name', type: 'VARCHAR(100)', nullable: false, primaryKey: false },
        { name: 'last_name', type: 'VARCHAR(100)', nullable: false, primaryKey: false },
        { name: 'phone', type: 'VARCHAR(20)', nullable: true, primaryKey: false },
        { name: 'created_at', type: 'TIMESTAMP', nullable: false, primaryKey: false, defaultValue: 'NOW()' }
      ],
      rides: [
        { name: 'id', type: 'UUID', nullable: false, primaryKey: true },
        { name: 'passenger_id', type: 'UUID', nullable: false, primaryKey: false, foreignKey: 'users.id' },
        { name: 'driver_id', type: 'UUID', nullable: true, primaryKey: false, foreignKey: 'drivers.id' },
        { name: 'status', type: 'VARCHAR(50)', nullable: false, primaryKey: false },
        { name: 'fare_amount', type: 'DECIMAL(10,2)', nullable: true, primaryKey: false },
        { name: 'created_at', type: 'TIMESTAMP', nullable: false, primaryKey: false, defaultValue: 'NOW()' }
      ]
    };

    return schemas[tableName] || [];
  }

  private getMockQueryResult(query: string): QueryResult {
    return {
      columns: ['id', 'name', 'email', 'created_at'],
      rows: [
        ['1', 'John Doe', '<EMAIL>', '2024-01-15 10:30:00'],
        ['2', 'Jane Smith', '<EMAIL>', '2024-01-15 11:45:00'],
        ['3', 'Ahmed Ali', '<EMAIL>', '2024-01-15 12:15:00']
      ],
      totalRows: 3,
      executionTime: 45,
      query
    };
  }

  private getMockTableData(tableName: string, page: number, limit: number): QueryResult {
    const mockData = this.generateMockData(tableName, limit);
    return {
      columns: mockData.columns,
      rows: mockData.rows,
      totalRows: mockData.totalRows,
      executionTime: 25,
      query: `SELECT * FROM ${tableName} LIMIT ${limit} OFFSET ${(page - 1) * limit}`
    };
  }

  private getMockSearchResult(tableName: string, searchTerm: string): QueryResult {
    return {
      columns: ['id', 'name', 'email'],
      rows: [
        ['1', `${searchTerm} User`, `${searchTerm.toLowerCase()}@example.com`]
      ],
      totalRows: 1,
      executionTime: 15,
      query: `SELECT * FROM ${tableName} WHERE name ILIKE '%${searchTerm}%'`
    };
  }

  private getMockStats(): DatabaseStats {
    return {
      totalDatabases: 4,
      totalTables: 85,
      totalRows: 2500000,
      totalSize: '15.2 GB',
      connectionStatus: {
        'tecnodrive_main': 'connected',
        'tecnodrive_auth': 'connected',
        'tecnodrive_cache': 'connected',
        'tecnodrive_logs': 'connected'
      }
    };
  }

  private generateMockData(tableName: string, limit: number): { columns: string[], rows: any[][], totalRows: number } {
    const dataGenerators: Record<string, any> = {
      users: {
        columns: ['id', 'email', 'first_name', 'last_name', 'phone', 'created_at'],
        generator: (i: number) => [
          `user-${i}`,
          `user${i}@tecno-drive.com`,
          `FirstName${i}`,
          `LastName${i}`,
          `+966${String(i).padStart(8, '0')}`,
          new Date(Date.now() - i * 86400000).toISOString()
        ]
      },
      rides: {
        columns: ['id', 'passenger_id', 'driver_id', 'status', 'fare_amount', 'created_at'],
        generator: (i: number) => [
          `ride-${i}`,
          `user-${i % 100}`,
          `driver-${i % 50}`,
          ['pending', 'active', 'completed', 'cancelled'][i % 4],
          (Math.random() * 100 + 10).toFixed(2),
          new Date(Date.now() - i * 3600000).toISOString()
        ]
      }
    };

    const config = dataGenerators[tableName] || dataGenerators.users;
    const rows = Array.from({ length: limit }, (_, i) => config.generator(i + 1));

    return {
      columns: config.columns,
      rows,
      totalRows: Math.floor(Math.random() * 100000) + limit
    };
  }
}

export const databaseService = new DatabaseService();
export default databaseService;
