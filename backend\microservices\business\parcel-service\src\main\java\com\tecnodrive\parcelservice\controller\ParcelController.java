package com.tecnodrive.parcelservice.controller;

import com.tecnodrive.parcelservice.dto.DeliveryDto;
import com.tecnodrive.parcelservice.dto.ParcelRequestDto;
import com.tecnodrive.parcelservice.dto.ParcelResponseDto;
import com.tecnodrive.parcelservice.service.ParcelService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Parcel Controller
 */
@RestController
@RequestMapping("/api/v1/parcels")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ParcelController {

    private final ParcelService parcelService;

    @PostMapping
    public ResponseEntity<ParcelResponseDto> create(@RequestBody ParcelRequestDto dto) {
        ParcelResponseDto response = parcelService.createParcel(dto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/deliveries")
    public ResponseEntity<Map<String, String>> assign(@RequestBody DeliveryDto dto) {
        parcelService.assignDelivery(dto);
        Map<String, String> response = new HashMap<>();
        response.put("message", "Delivery assigned successfully");
        response.put("timestamp", LocalDateTime.now().toString());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "parcel-service");
        health.put("timestamp", LocalDateTime.now());
        health.put("version", "1.0.0");
        return ResponseEntity.ok(health);
    }

    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllParcels() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Parcel list endpoint");
        response.put("service", "parcel-service");
        response.put("timestamp", LocalDateTime.now());
        response.put("data", List.of()); // Empty list for now
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getParcelById(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Parcel details for ID: " + id);
        response.put("service", "parcel-service");
        response.put("timestamp", LocalDateTime.now());
        response.put("parcelId", id);
        return ResponseEntity.ok(response);
    }
}
