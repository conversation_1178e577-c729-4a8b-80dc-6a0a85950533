// Test Persistent Auth - Copy and paste in browser console
console.log('🔐 Testing Persistent Auth System');
console.log('=================================');

// Test functions
async function testPersistentAuth() {
    console.log('🧪 Starting Persistent Auth Tests...');
    
    // Clear any existing auth data
    console.log('🧹 Clearing existing auth data...');
    const keysToRemove = [
        'tecnodrive_auth_token', 'tecnodrive_user_data', 'tecnodrive_remember_me',
        'tecnodrive_login_time', 'tecnodrive_expires_at', 'tecnodrive_auto_login',
        'authToken', 'token', 'user', 'currentUser', 'isAuthenticated'
    ];
    
    keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
    });
    
    console.log('✅ Auth data cleared');
    
    // Test 1: Regular login (no remember me)
    console.log('\n🧪 Test 1: Regular Login (Session Only)');
    await testRegularLogin();
    
    // Test 2: Persistent login (remember me)
    console.log('\n🧪 Test 2: Persistent Login (Remember Me)');
    await testPersistentLogin();
    
    // Test 3: Auto-login
    console.log('\n🧪 Test 3: Auto-Login');
    await testAutoLogin();
    
    // Test 4: Session extension
    console.log('\n🧪 Test 4: Session Extension');
    await testSessionExtension();
    
    console.log('\n🎉 All Persistent Auth Tests Completed!');
}

async function testRegularLogin() {
    try {
        // Simulate regular login
        const loginData = {
            email: '<EMAIL>',
            password: 'password123',
            rememberMe: false
        };
        
        console.log('📝 Login data:', loginData);
        
        // Mock the persistent login function
        const mockUser = {
            id: '1',
            email: '<EMAIL>',
            name: 'Azal Mohamed',
            role: 'ADMIN',
            permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
        };
        
        const now = Date.now();
        const expiresIn = 24 * 60 * 60 * 1000; // 24 hours
        
        const tokenData = {
            id: mockUser.id,
            email: mockUser.email,
            role: mockUser.role,
            iat: now,
            exp: now + expiresIn,
            rememberMe: false
        };
        
        const token = btoa(JSON.stringify(tokenData));
        
        // Store in sessionStorage (not persistent)
        sessionStorage.setItem('tecnodrive_auth_token', token);
        sessionStorage.setItem('tecnodrive_user_data', JSON.stringify(mockUser));
        sessionStorage.setItem('tecnodrive_remember_me', 'false');
        sessionStorage.setItem('tecnodrive_login_time', now.toString());
        sessionStorage.setItem('tecnodrive_expires_at', (now + expiresIn).toString());
        
        console.log('✅ Regular login successful');
        console.log('💾 Data stored in sessionStorage (will be lost on browser close)');
        console.log('⏰ Expires at:', new Date(now + expiresIn).toLocaleString());
        
        // Verify storage
        const storedToken = sessionStorage.getItem('tecnodrive_auth_token');
        const storedUser = sessionStorage.getItem('tecnodrive_user_data');
        
        console.log('🔍 Verification:');
        console.log('- Token in sessionStorage:', !!storedToken);
        console.log('- User in sessionStorage:', !!storedUser);
        console.log('- Token in localStorage:', !!localStorage.getItem('tecnodrive_auth_token'));
        
    } catch (error) {
        console.error('❌ Regular login test failed:', error);
    }
}

async function testPersistentLogin() {
    try {
        // Clear previous test data
        sessionStorage.clear();
        
        // Simulate persistent login
        const loginData = {
            email: '<EMAIL>',
            password: 'password123',
            rememberMe: true
        };
        
        console.log('📝 Login data:', loginData);
        
        const mockUser = {
            id: '1',
            email: '<EMAIL>',
            name: 'Azal Mohamed',
            role: 'ADMIN',
            permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
        };
        
        const now = Date.now();
        const expiresIn = 30 * 24 * 60 * 60 * 1000; // 30 days
        
        const tokenData = {
            id: mockUser.id,
            email: mockUser.email,
            role: mockUser.role,
            iat: now,
            exp: now + expiresIn,
            rememberMe: true
        };
        
        const token = btoa(JSON.stringify(tokenData));
        
        // Store in localStorage (persistent)
        localStorage.setItem('tecnodrive_auth_token', token);
        localStorage.setItem('tecnodrive_user_data', JSON.stringify(mockUser));
        localStorage.setItem('tecnodrive_remember_me', 'true');
        localStorage.setItem('tecnodrive_login_time', now.toString());
        localStorage.setItem('tecnodrive_expires_at', (now + expiresIn).toString());
        localStorage.setItem('tecnodrive_auto_login', 'true');
        
        console.log('✅ Persistent login successful');
        console.log('💾 Data stored in localStorage (will persist across browser sessions)');
        console.log('⏰ Expires at:', new Date(now + expiresIn).toLocaleString());
        console.log('🔄 Auto-login enabled');
        
        // Verify storage
        const storedToken = localStorage.getItem('tecnodrive_auth_token');
        const storedUser = localStorage.getItem('tecnodrive_user_data');
        const autoLogin = localStorage.getItem('tecnodrive_auto_login');
        
        console.log('🔍 Verification:');
        console.log('- Token in localStorage:', !!storedToken);
        console.log('- User in localStorage:', !!storedUser);
        console.log('- Auto-login enabled:', autoLogin === 'true');
        
    } catch (error) {
        console.error('❌ Persistent login test failed:', error);
    }
}

async function testAutoLogin() {
    try {
        console.log('🔄 Testing auto-login functionality...');
        
        // Check if auto-login should work
        const autoLogin = localStorage.getItem('tecnodrive_auto_login');
        const token = localStorage.getItem('tecnodrive_auth_token');
        const userData = localStorage.getItem('tecnodrive_user_data');
        const expiresAt = localStorage.getItem('tecnodrive_expires_at');
        
        console.log('🔍 Auto-login prerequisites:');
        console.log('- Auto-login flag:', autoLogin === 'true');
        console.log('- Token exists:', !!token);
        console.log('- User data exists:', !!userData);
        console.log('- Expires at:', expiresAt ? new Date(parseInt(expiresAt)).toLocaleString() : 'Not set');
        
        if (autoLogin === 'true' && token && userData && expiresAt) {
            const expirationTime = parseInt(expiresAt);
            const isExpired = Date.now() > expirationTime;
            
            if (!isExpired) {
                const user = JSON.parse(userData);
                
                console.log('✅ Auto-login should work!');
                console.log('👤 User:', user.email);
                console.log('⏰ Valid until:', new Date(expirationTime).toLocaleString());
                
                // Simulate auto-login success
                console.log('🎉 Auto-login simulation successful');
                
                return {
                    success: true,
                    user: user,
                    token: token
                };
            } else {
                console.log('❌ Token expired, auto-login not possible');
                
                // Clear expired data
                localStorage.removeItem('tecnodrive_auth_token');
                localStorage.removeItem('tecnodrive_user_data');
                localStorage.removeItem('tecnodrive_auto_login');
                
                return { success: false, message: 'Token expired' };
            }
        } else {
            console.log('❌ Auto-login prerequisites not met');
            return { success: false, message: 'Prerequisites not met' };
        }
        
    } catch (error) {
        console.error('❌ Auto-login test failed:', error);
        return { success: false, message: error.message };
    }
}

async function testSessionExtension() {
    try {
        console.log('🔄 Testing session extension...');
        
        const token = localStorage.getItem('tecnodrive_auth_token');
        const expiresAt = localStorage.getItem('tecnodrive_expires_at');
        
        if (token && expiresAt) {
            const currentExpiration = parseInt(expiresAt);
            const now = Date.now();
            const newExpiration = now + (30 * 24 * 60 * 60 * 1000); // 30 days from now
            
            console.log('⏰ Current expiration:', new Date(currentExpiration).toLocaleString());
            console.log('⏰ New expiration:', new Date(newExpiration).toLocaleString());
            
            // Update expiration time
            localStorage.setItem('tecnodrive_expires_at', newExpiration.toString());
            
            console.log('✅ Session extended successfully');
            console.log('🔄 New expiration time set');
            
        } else {
            console.log('❌ No active session to extend');
        }
        
    } catch (error) {
        console.error('❌ Session extension test failed:', error);
    }
}

// Quick login function for immediate testing
function quickPersistentLogin() {
    console.log('⚡ Quick Persistent Login');
    
    const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Azal Mohamed',
        role: 'ADMIN',
        permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
    };
    
    const now = Date.now();
    const expiresIn = 30 * 24 * 60 * 60 * 1000; // 30 days
    
    const token = btoa(JSON.stringify({
        id: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        iat: now,
        exp: now + expiresIn,
        rememberMe: true
    }));
    
    // Store persistent auth data
    localStorage.setItem('tecnodrive_auth_token', token);
    localStorage.setItem('tecnodrive_user_data', JSON.stringify(mockUser));
    localStorage.setItem('tecnodrive_remember_me', 'true');
    localStorage.setItem('tecnodrive_login_time', now.toString());
    localStorage.setItem('tecnodrive_expires_at', (now + expiresIn).toString());
    localStorage.setItem('tecnodrive_auto_login', 'true');
    
    console.log('✅ Quick persistent login completed!');
    console.log('🔄 Auto-login enabled for 30 days');
    console.log('🔗 Redirecting to dashboard...');
    
    // Redirect to dashboard
    window.location.href = '/dashboard';
}

// Expose functions globally
window.testPersistentAuth = testPersistentAuth;
window.quickPersistentLogin = quickPersistentLogin;
window.testAutoLogin = testAutoLogin;

// Auto-execute
console.log('🚀 Persistent Auth Test loaded!');
console.log('💡 Available functions:');
console.log('- testPersistentAuth() - Run all tests');
console.log('- quickPersistentLogin() - Quick login with remember me');
console.log('- testAutoLogin() - Test auto-login functionality');

console.log('\n⚡ For quick testing, run: quickPersistentLogin()');
console.log('🧪 For full testing, run: testPersistentAuth()');

// Auto-execute quick login after 3 seconds
setTimeout(() => {
    console.log('⚡ Auto-executing quick persistent login...');
    quickPersistentLogin();
}, 3000);
