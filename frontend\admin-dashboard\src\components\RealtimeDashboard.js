import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON>, 
  CardContent,
  CardHeader,
  Grid, 
  Typography,
  Box,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Alert,
  Button
} from '@mui/material';
import { 
  Wifi as ConnectionIcon,
  Speed as PerformanceIcon,
  Analytics as AnalyticsIcon,
  Warning as AlertIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingIcon
} from '@mui/icons-material';
import './RealtimeDashboard.css';

const RealtimeDashboard = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [analytics, setAnalytics] = useState({});
  const [metrics, setMetrics] = useState({});
  const [sessions, setSessions] = useState({});
  const [subscriptionStats, setSubscriptionStats] = useState({});
  const [alerts, setAlerts] = useState([]);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  
  const wsRef = useRef(null);
  const intervalRef = useRef(null);

  // WebSocket connection for real-time monitoring
  useEffect(() => {
    const connectWebSocket = () => {
      const wsUrl = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8085';
      wsRef.current = new WebSocket(`${wsUrl}/ws`);

      wsRef.current.onopen = () => {
        console.log('Connected to monitoring WebSocket');
        setConnectionStatus('connected');
        
        // Subscribe to monitoring updates
        wsRef.current.send(JSON.stringify({
          type: 'subscribe',
          subscriptions: [
            'monitoring:all',
            'analytics:all',
            'performance:all',
            'alerts:all'
          ]
        }));
      };

      wsRef.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      wsRef.current.onclose = () => {
        console.log('Disconnected from monitoring WebSocket');
        setConnectionStatus('disconnected');
        // Reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      wsRef.current.onerror = (error) => {
        console.error('Monitoring WebSocket error:', error);
        setConnectionStatus('error');
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Fetch analytics data periodically
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8085';
        
        // Fetch analytics
        const analyticsResponse = await fetch(`${baseUrl}/api/websocket/analytics`);
        if (analyticsResponse.ok) {
          const analyticsData = await analyticsResponse.json();
          setAnalytics(analyticsData.analytics || {});
        }

        // Fetch metrics
        const metricsResponse = await fetch(`${baseUrl}/api/websocket/metrics`);
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setMetrics(metricsData.metrics || {});
        }

        // Fetch sessions
        const sessionsResponse = await fetch(`${baseUrl}/api/websocket/sessions`);
        if (sessionsResponse.ok) {
          const sessionsData = await sessionsResponse.json();
          setSessions(sessionsData.sessions || {});
        }

        // Fetch subscription stats
        const subscriptionResponse = await fetch(`${baseUrl}/api/websocket/subscriptions/stats`);
        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setSubscriptionStats(subscriptionData.subscriptionStats || {});
        }

        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error fetching analytics:', error);
        setAlerts(prev => [...prev, {
          id: Date.now(),
          type: 'error',
          message: 'Failed to fetch analytics data',
          timestamp: new Date()
        }]);
      }
    };

    fetchAnalytics();
    intervalRef.current = setInterval(fetchAnalytics, 10000); // Update every 10 seconds

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'monitoring_update':
        setAnalytics(prev => ({ ...prev, ...message.data }));
        break;
      case 'performance_update':
        setMetrics(prev => ({ ...prev, ...message.data }));
        break;
      case 'alert':
        setAlerts(prev => [...prev, {
          id: Date.now(),
          type: message.data.type || 'info',
          message: message.data.message,
          timestamp: new Date()
        }]);
        break;
      default:
        console.log('Unknown monitoring message:', message.type);
    }
  };

  // Clear alerts
  const clearAlerts = () => {
    setAlerts([]);
  };

  // Manual refresh
  const handleRefresh = () => {
    window.location.reload();
  };

  // Get connection status color
  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'disconnected': return 'error';
      case 'error': return 'warning';
      default: return 'default';
    }
  };

  // Format uptime
  const formatUptime = (uptime) => {
    if (!uptime) return 'N/A';
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  // Format memory usage
  const formatMemory = (bytes) => {
    if (!bytes) return 'N/A';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          لوحة المراقبة في الوقت الفعلي
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Chip 
            icon={<ConnectionIcon />}
            label={connectionStatus === 'connected' ? 'متصل' : 'غير متصل'}
            color={getConnectionColor()}
            variant="outlined"
          />
          <Typography variant="caption" color="text.secondary">
            آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
          </Typography>
          <Button 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            variant="outlined"
            size="small"
          >
            تحديث
          </Button>
        </Box>
      </Box>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Box sx={{ mb: 3 }}>
          {alerts.slice(-3).map(alert => (
            <Alert 
              key={alert.id}
              severity={alert.type}
              sx={{ mb: 1 }}
              onClose={clearAlerts}
            >
              {alert.message} - {alert.timestamp.toLocaleTimeString('ar-SA')}
            </Alert>
          ))}
        </Box>
      )}

      {/* Main Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ConnectionIcon sx={{ fontSize: 40, color: '#1976d2' }} />
                <Box>
                  <Typography variant="h4" component="div" color="#1976d2">
                    {sessions.totalSessions || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    الجلسات النشطة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PerformanceIcon sx={{ fontSize: 40, color: '#2e7d32' }} />
                <Box>
                  <Typography variant="h4" component="div" color="#2e7d32">
                    {metrics.messagesPerSecond?.toFixed(0) || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    رسالة/ثانية
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingIcon sx={{ fontSize: 40, color: '#ed6c02' }} />
                <Box>
                  <Typography variant="h4" component="div" color="#ed6c02">
                    {metrics.averageLatency?.toFixed(0) || 0}ms
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط الاستجابة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AlertIcon sx={{ fontSize: 40, color: '#d32f2f' }} />
                <Box>
                  <Typography variant="h4" component="div" color="#d32f2f">
                    {metrics.errorRate?.toFixed(1) || 0}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل الأخطاء
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Analytics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="تحليلات الجلسات" />
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  وقت التشغيل: {formatUptime(analytics.uptime)}
                </Typography>
              </Box>
              
              {analytics.sessionsByType && (
                <Box>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    توزيع المستخدمين:
                  </Typography>
                  {Object.entries(analytics.sessionsByType).map(([type, count]) => (
                    <Box key={type} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{type}:</Typography>
                      <Typography variant="body2" fontWeight="bold">{count}</Typography>
                    </Box>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="استخدام الذاكرة" />
            <CardContent>
              {metrics.memoryUsage && (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">المستخدم:</Typography>
                    <Typography variant="body2">{formatMemory(metrics.memoryUsage.used)}</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={(metrics.memoryUsage.used / metrics.memoryUsage.total) * 100}
                    sx={{ mb: 2 }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">الإجمالي:</Typography>
                    <Typography variant="body2">{formatMemory(metrics.memoryUsage.total)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">الحد الأقصى:</Typography>
                    <Typography variant="body2">{formatMemory(metrics.memoryUsage.max)}</Typography>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardHeader title="أهم الاشتراكات" />
            <CardContent>
              {subscriptionStats.topSubscriptions && (
                <List>
                  {subscriptionStats.topSubscriptions.slice(0, 10).map((subscription, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={subscription.key || subscription[0]}
                        secondary={`${subscription.value || subscription[1]} مشترك`}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RealtimeDashboard;
