package com.tecnodrive.hrservice.repository;

import com.tecnodrive.hrservice.entity.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Employee Repository
 * 
 * Data access layer for Employee entities
 */
@Repository
public interface EmployeeRepository extends JpaRepository<Employee, UUID> {

    /**
     * Find employee by email
     */
    Optional<Employee> findByEmail(String email);

    /**
     * Find employee by employee number
     */
    Optional<Employee> findByEmployeeNumber(String employeeNumber);

    /**
     * Find employees by company
     */
    List<Employee> findByCompanyId(String companyId);

    /**
     * Find employees by company with pagination
     */
    Page<Employee> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find employees by status
     */
    List<Employee> findByStatus(Employee.EmployeeStatus status);

    /**
     * Find employees by company and status
     */
    List<Employee> findByCompanyIdAndStatus(String companyId, Employee.EmployeeStatus status);

    /**
     * Find employees by department
     */
    List<Employee> findByDepartment(String department);

    /**
     * Find employees by company and department
     */
    List<Employee> findByCompanyIdAndDepartment(String companyId, String department);

    /**
     * Find employees by position
     */
    List<Employee> findByPosition(String position);

    /**
     * Find employees by manager
     */
    List<Employee> findByManagerId(String managerId);

    /**
     * Find active employees
     */
    List<Employee> findByIsActiveTrue();

    /**
     * Find active employees by company
     */
    List<Employee> findByCompanyIdAndIsActiveTrue(String companyId);

    /**
     * Find employees on probation
     */
    @Query("SELECT e FROM Employee e WHERE e.probationEndDate IS NOT NULL AND e.probationEndDate > :currentDate")
    List<Employee> findEmployeesOnProbation(@Param("currentDate") LocalDate currentDate);

    /**
     * Find employees with due performance reviews
     */
    @Query("SELECT e FROM Employee e WHERE e.nextPerformanceReview IS NOT NULL AND e.nextPerformanceReview <= :currentDate")
    List<Employee> findEmployeesWithDuePerformanceReviews(@Param("currentDate") LocalDate currentDate);

    /**
     * Find employees by hire date range
     */
    @Query("SELECT e FROM Employee e WHERE e.hireDate BETWEEN :startDate AND :endDate")
    List<Employee> findByHireDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find employees by employment type
     */
    List<Employee> findByEmploymentType(Employee.EmploymentType employmentType);

    /**
     * Find employees by company and employment type
     */
    List<Employee> findByCompanyIdAndEmploymentType(String companyId, Employee.EmploymentType employmentType);

    /**
     * Search employees by name, email, or employee number
     */
    @Query("SELECT e FROM Employee e WHERE " +
           "LOWER(CONCAT(e.firstName, ' ', e.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.employeeNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Employee> searchEmployees(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Search employees by company
     */
    @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId AND (" +
           "LOWER(CONCAT(e.firstName, ' ', e.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.employeeNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Employee> searchEmployeesByCompany(@Param("companyId") String companyId, @Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Count employees by status
     */
    long countByStatus(Employee.EmployeeStatus status);

    /**
     * Count employees by company and status
     */
    long countByCompanyIdAndStatus(String companyId, Employee.EmployeeStatus status);

    /**
     * Count active employees by company
     */
    long countByCompanyIdAndIsActiveTrue(String companyId);

    /**
     * Count employees by department
     */
    long countByDepartment(String department);

    /**
     * Count employees by company and department
     */
    long countByCompanyIdAndDepartment(String companyId, String department);

    /**
     * Calculate average salary by company
     */
    @Query("SELECT AVG(e.salary) FROM Employee e WHERE e.companyId = :companyId AND e.isActive = true AND e.salary IS NOT NULL")
    BigDecimal calculateAverageSalaryByCompany(@Param("companyId") String companyId);

    /**
     * Calculate total payroll by company
     */
    @Query("SELECT COALESCE(SUM(e.salary), 0) FROM Employee e WHERE e.companyId = :companyId AND e.isActive = true AND e.salary IS NOT NULL")
    BigDecimal calculateTotalPayrollByCompany(@Param("companyId") String companyId);

    /**
     * Calculate average years of service by company
     */
    @Query("SELECT AVG(YEAR(CURRENT_DATE) - YEAR(e.hireDate)) FROM Employee e WHERE e.companyId = :companyId AND e.isActive = true")
    Double calculateAverageYearsOfServiceByCompany(@Param("companyId") String companyId);

    /**
     * Get department statistics
     */
    @Query("SELECT e.department, COUNT(e), AVG(e.salary), SUM(e.salary) FROM Employee e " +
           "WHERE e.companyId = :companyId AND e.isActive = true " +
           "GROUP BY e.department")
    List<Object[]> getDepartmentStatistics(@Param("companyId") String companyId);

    /**
     * Get employment type statistics
     */
    @Query("SELECT e.employmentType, COUNT(e) FROM Employee e " +
           "WHERE e.companyId = :companyId AND e.isActive = true " +
           "GROUP BY e.employmentType")
    List<Object[]> getEmploymentTypeStatistics(@Param("companyId") String companyId);

    /**
     * Get status statistics
     */
    @Query("SELECT e.status, COUNT(e) FROM Employee e WHERE e.companyId = :companyId GROUP BY e.status")
    List<Object[]> getStatusStatistics(@Param("companyId") String companyId);

    /**
     * Find employees with birthdays in date range
     */
    @Query("SELECT e FROM Employee e WHERE " +
           "MONTH(e.dateOfBirth) = MONTH(:startDate) AND DAY(e.dateOfBirth) BETWEEN DAY(:startDate) AND DAY(:endDate) " +
           "OR MONTH(e.dateOfBirth) = MONTH(:endDate) AND DAY(e.dateOfBirth) BETWEEN DAY(:startDate) AND DAY(:endDate)")
    List<Employee> findEmployeesWithBirthdaysInRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find employees with work anniversaries in date range
     */
    @Query("SELECT e FROM Employee e WHERE " +
           "MONTH(e.hireDate) = MONTH(:startDate) AND DAY(e.hireDate) BETWEEN DAY(:startDate) AND DAY(:endDate) " +
           "OR MONTH(e.hireDate) = MONTH(:endDate) AND DAY(e.hireDate) BETWEEN DAY(:startDate) AND DAY(:endDate)")
    List<Employee> findEmployeesWithAnniversariesInRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find employees by salary range
     */
    @Query("SELECT e FROM Employee e WHERE e.salary BETWEEN :minSalary AND :maxSalary")
    List<Employee> findBySalaryRange(@Param("minSalary") BigDecimal minSalary, @Param("maxSalary") BigDecimal maxSalary);

    /**
     * Find employees eligible for benefits
     */
    @Query("SELECT e FROM Employee e WHERE e.employmentType = 'FULL_TIME' AND e.status = 'ACTIVE' AND " +
           "(e.probationEndDate IS NULL OR e.probationEndDate < :currentDate)")
    List<Employee> findEmployeesEligibleForBenefits(@Param("currentDate") LocalDate currentDate);

    /**
     * Find employees by manager and status
     */
    List<Employee> findByManagerIdAndStatus(String managerId, Employee.EmployeeStatus status);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Check if employee number exists
     */
    boolean existsByEmployeeNumber(String employeeNumber);

    /**
     * Count employees by company
     */
    long countByCompanyId(String companyId);

    /**
     * Find employees by pay frequency
     */
    List<Employee> findByPayFrequency(Employee.PayFrequency payFrequency);

    /**
     * Find employees by company and pay frequency
     */
    List<Employee> findByCompanyIdAndPayFrequency(String companyId, Employee.PayFrequency payFrequency);

    /**
     * Find employees with missing performance reviews
     */
    @Query("SELECT e FROM Employee e WHERE e.lastPerformanceReview IS NULL AND e.hireDate < :cutoffDate")
    List<Employee> findEmployeesWithMissingPerformanceReviews(@Param("cutoffDate") LocalDate cutoffDate);

    /**
     * Find employees by gender
     */
    List<Employee> findByGender(Employee.Gender gender);

    /**
     * Find employees by company and gender
     */
    List<Employee> findByCompanyIdAndGender(String companyId, Employee.Gender gender);
}
