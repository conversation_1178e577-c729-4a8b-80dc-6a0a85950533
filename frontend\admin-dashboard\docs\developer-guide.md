# دليل المطور - نظام تكنو درايف

## نظرة عامة
هذا الدليل يوضح كيفية تطوير وصيانة نظام تكنو درايف الإداري.

## هيكل المشروع

```
admin-dashboard/
├── src/
│   ├── components/          # مكونات React
│   │   ├── Auth/           # نظام المصادقة
│   │   ├── Dashboard/      # لوحة التحكم الرئيسية
│   │   ├── Rides/          # إدارة الرحلات
│   │   ├── Fleet/          # إدارة الأسطول
│   │   ├── Users/          # إدارة المستخدمين
│   │   ├── Payments/       # إدارة المدفوعات
│   │   ├── Finance/        # الشؤون المالية
│   │   ├── HR/             # الموارد البشرية
│   │   ├── Technical/      # الإدارة التقنية
│   │   ├── Settings/       # الإعدادات
│   │   └── Layout/         # تخطيط الصفحة
│   ├── services/           # خدمات API
│   ├── store/              # Redux store
│   ├── types/              # TypeScript types
│   └── utils/              # أدوات مساعدة
├── docs/                   # الوثائق
└── public/                 # الملفات العامة
```

## إضافة مكون جديد

### 1. إنشاء المكون
```typescript
// src/components/NewFeature/NewFeature.tsx
import React, { useState } from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

interface NewFeatureProps {
  title: string;
  data?: any[];
}

const NewFeature: React.FC<NewFeatureProps> = ({ title, data = [] }) => {
  const [loading, setLoading] = useState(false);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        {title}
      </Typography>
      
      <Card>
        <CardContent>
          {/* محتوى المكون */}
        </CardContent>
      </Card>
    </Box>
  );
};

export default NewFeature;
```

### 2. إضافة إلى Sidebar
```typescript
// src/components/Layout/Sidebar.tsx
import { NewFeature as NewFeatureIcon } from '@mui/icons-material';

const menuItems = [
  // ... العناصر الموجودة
  {
    text: 'الميزة الجديدة',
    icon: <NewFeatureIcon />,
    path: '/new-feature',
  },
];
```

### 3. إضافة Route
```typescript
// src/components/Layout/Layout.tsx
import NewFeature from '../NewFeature/NewFeature';

// في Routes
<Route path="/new-feature" element={<NewFeature title="الميزة الجديدة" />} />
```

## إضافة خدمة API جديدة

### 1. إنشاء Service
```typescript
// src/services/newFeatureService.ts
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8090';

export interface NewFeatureData {
  id: string;
  name: string;
  description: string;
  createdAt: string;
}

class NewFeatureService {
  private baseURL = `${API_BASE_URL}/api/new-feature`;

  async getAll(): Promise<NewFeatureData[]> {
    try {
      const response = await axios.get(`${this.baseURL}/items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching new feature data:', error);
      throw error;
    }
  }

  async create(data: Omit<NewFeatureData, 'id' | 'createdAt'>): Promise<NewFeatureData> {
    try {
      const response = await axios.post(`${this.baseURL}/items`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating new feature item:', error);
      throw error;
    }
  }

  async update(id: string, data: Partial<NewFeatureData>): Promise<NewFeatureData> {
    try {
      const response = await axios.put(`${this.baseURL}/items/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating new feature item:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await axios.delete(`${this.baseURL}/items/${id}`);
    } catch (error) {
      console.error('Error deleting new feature item:', error);
      throw error;
    }
  }
}

export default new NewFeatureService();
```

### 2. إضافة Redux Slice
```typescript
// src/store/slices/newFeatureSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import newFeatureService, { NewFeatureData } from '../../services/newFeatureService';

interface NewFeatureState {
  items: NewFeatureData[];
  loading: boolean;
  error: string | null;
}

const initialState: NewFeatureState = {
  items: [],
  loading: false,
  error: null,
};

export const fetchNewFeatureItems = createAsyncThunk(
  'newFeature/fetchItems',
  async () => {
    return await newFeatureService.getAll();
  }
);

export const createNewFeatureItem = createAsyncThunk(
  'newFeature/createItem',
  async (data: Omit<NewFeatureData, 'id' | 'createdAt'>) => {
    return await newFeatureService.create(data);
  }
);

const newFeatureSlice = createSlice({
  name: 'newFeature',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNewFeatureItems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNewFeatureItems.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
      })
      .addCase(fetchNewFeatureItems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch items';
      });
  },
});

export const { clearError } = newFeatureSlice.actions;
export default newFeatureSlice.reducer;
```

### 3. إضافة إلى Store
```typescript
// src/store/store.ts
import newFeatureReducer from './slices/newFeatureSlice';

export const store = configureStore({
  reducer: {
    // ... الـ reducers الموجودة
    newFeature: newFeatureReducer,
  },
});
```

## إضافة استيراد/تصدير Excel

### 1. إضافة وظائف Excel Service
```typescript
// src/services/excelService.ts
export class ExcelService {
  static exportNewFeatureData(data: NewFeatureData[]): void {
    const exportData = data.map(item => ({
      'المعرف': item.id,
      'الاسم': item.name,
      'الوصف': item.description,
      'تاريخ الإنشاء': item.createdAt,
    }));

    this.exportToExcel([{
      sheetName: 'البيانات الجديدة',
      data: exportData
    }], 'new_feature_data.xlsx');
  }

  static async importNewFeatureData(file: File): Promise<NewFeatureData[]> {
    try {
      const data = await this.importFromExcel(file);
      const dataSheet = data['البيانات الجديدة'] || data['NewFeature'] || Object.values(data)[0];
      
      if (!dataSheet || dataSheet.length === 0) {
        throw new Error('لم يتم العثور على البيانات في الملف');
      }

      return dataSheet.map((row: any) => ({
        name: row['الاسم'] || row['Name'],
        description: row['الوصف'] || row['Description'],
      }));
    } catch (error) {
      console.error('Error importing new feature data:', error);
      throw error;
    }
  }
}
```

### 2. إضافة أزرار الاستيراد/التصدير
```typescript
// في المكون
const handleExport = () => {
  try {
    ExcelService.exportNewFeatureData(items);
  } catch (error) {
    console.error('Export failed:', error);
  }
};

const handleImport = async (file: File) => {
  try {
    const importedData = await ExcelService.importNewFeatureData(file);
    // معالجة البيانات المستوردة
  } catch (error) {
    console.error('Import failed:', error);
  }
};
```

## إضافة خدمة جديدة إلى Service Health

```typescript
// src/components/ServiceHealth/ServiceHealth.tsx
const services = [
  // ... الخدمات الموجودة
  {
    name: 'الخدمة الجديدة',
    url: 'http://localhost:8091/api/new-service',
    status: 'healthy',
    lastChecked: new Date(),
  },
];
```

## أفضل الممارسات

### 1. TypeScript Types
```typescript
// src/types/newFeature.ts
export interface NewFeatureData {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface NewFeatureFilters {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}
```

### 2. Error Handling
```typescript
// معالجة الأخطاء في المكونات
const [error, setError] = useState<string | null>(null);

try {
  await someAsyncOperation();
} catch (error) {
  setError(error instanceof Error ? error.message : 'حدث خطأ غير متوقع');
}

// عرض الأخطاء
{error && (
  <Alert severity="error" sx={{ mb: 2 }}>
    {error}
  </Alert>
)}
```

### 3. Loading States
```typescript
// حالات التحميل
const [loading, setLoading] = useState(false);

const handleSubmit = async () => {
  setLoading(true);
  try {
    await submitData();
  } finally {
    setLoading(false);
  }
};

// عرض حالة التحميل
<Button disabled={loading}>
  {loading ? 'جاري المعالجة...' : 'حفظ'}
</Button>
```

### 4. Form Validation
```typescript
// التحقق من صحة النماذج
const [formErrors, setFormErrors] = useState<Record<string, string>>({});

const validateForm = (data: any) => {
  const errors: Record<string, string> = {};
  
  if (!data.name) {
    errors.name = 'الاسم مطلوب';
  }
  
  if (!data.email || !/\S+@\S+\.\S+/.test(data.email)) {
    errors.email = 'البريد الإلكتروني غير صحيح';
  }
  
  return errors;
};
```

### 5. Responsive Design
```typescript
// استخدام breakpoints
import { useTheme, useMediaQuery } from '@mui/material';

const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down('md'));

// تخطيط متجاوب
<Grid container spacing={isMobile ? 2 : 3}>
  <Grid item xs={12} md={6}>
    {/* محتوى */}
  </Grid>
</Grid>
```

## اختبار المكونات

### 1. Unit Tests
```typescript
// src/components/NewFeature/__tests__/NewFeature.test.tsx
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../../../store/store';
import NewFeature from '../NewFeature';

test('renders new feature component', () => {
  render(
    <Provider store={store}>
      <NewFeature title="Test Feature" />
    </Provider>
  );
  
  expect(screen.getByText('Test Feature')).toBeInTheDocument();
});
```

### 2. Integration Tests
```typescript
// اختبار التكامل مع API
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/new-feature/items', (req, res, ctx) => {
    return res(ctx.json([
      { id: '1', name: 'Test Item', description: 'Test Description' }
    ]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

## نشر التطبيق

### 1. Build للإنتاج
```bash
npm run build
```

### 2. متغيرات البيئة
```bash
# .env.production
REACT_APP_API_BASE_URL=https://api.tecno-drive.com
REACT_APP_ENVIRONMENT=production
```

### 3. Docker
```dockerfile
# Dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

هذا الدليل يوفر إطار عمل شامل لتطوير وصيانة نظام تكنو درايف.
