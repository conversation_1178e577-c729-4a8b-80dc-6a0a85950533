server:
  port: 8086

spring:
  application:
    name: payment-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_payments
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus

# Logging
logging:
  level:
    com.tecnodrive.paymentservice: DEBUG
