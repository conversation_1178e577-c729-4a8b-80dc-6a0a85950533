package com.tecnodrive.financialservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Financial Transaction Log Entity
 * 
 * Records all financial transactions in the system for audit and tracking purposes.
 * Provides complete financial audit trail.
 */
@Entity
@Table(name = "financial_transactions_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class FinancialTransactionLog {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Type of transaction
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransactionType transactionType;

    /**
     * Source entity type (RIDE, PARCEL, SUBSCRIPTION, etc.)
     */
    @Column(nullable = false, length = 50)
    private String sourceEntityType;

    /**
     * Source entity ID
     */
    @Column(nullable = false)
    private String sourceEntityId;

    /**
     * Transaction amount
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;

    /**
     * Currency code
     */
    @Column(nullable = false, length = 3)
    @Builder.Default
    private String currency = "USD";

    /**
     * Transaction status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private TransactionStatus status = TransactionStatus.PENDING;

    /**
     * Transaction description
     */
    @Column(length = 500)
    private String description;

    /**
     * Reference number for external systems
     */
    @Column(length = 100)
    private String referenceNumber;

    /**
     * Payment method used
     */
    @Column(length = 50)
    private String paymentMethod;

    /**
     * Tax amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * Fee amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * Net amount (amount - tax - fees)
     */
    @Column(precision = 19, scale = 2)
    private BigDecimal netAmount;

    /**
     * Company/Tenant ID
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * User ID who initiated the transaction
     */
    private String userId;

    /**
     * Additional metadata (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant transactionDate;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Transaction Type Enum
     */
    public enum TransactionType {
        PAYMENT_RECEIVED,
        PAYMENT_SENT,
        REFUND_ISSUED,
        REFUND_RECEIVED,
        FEE_CHARGED,
        COMMISSION_EARNED,
        SUBSCRIPTION_PAYMENT,
        PENALTY_CHARGED,
        BONUS_PAID,
        EXPENSE_RECORDED,
        REVENUE_RECOGNIZED,
        ADJUSTMENT
    }

    /**
     * Transaction Status Enum
     */
    public enum TransactionStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED,
        CANCELLED,
        REFUNDED,
        DISPUTED
    }

    /**
     * Calculate net amount
     */
    @PrePersist
    @PreUpdate
    private void calculateNetAmount() {
        if (amount != null) {
            BigDecimal tax = taxAmount != null ? taxAmount : BigDecimal.ZERO;
            BigDecimal fee = feeAmount != null ? feeAmount : BigDecimal.ZERO;
            this.netAmount = amount.subtract(tax).subtract(fee);
        }
    }
}
