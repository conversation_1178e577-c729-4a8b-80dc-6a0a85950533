import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  ButtonGroup,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Map as MapIcon,
  Layers as LayersIcon,
  MyLocation as MyLocationIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Traffic as TrafficIcon,
  Satellite as SatelliteIcon
} from '@mui/icons-material';

// Leaflet imports
import { Map<PERSON>ontainer, TileLayer, Marker, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

interface MapConfig {
  defaultProvider: string;
  defaultCenter: { lat: number; lng: number };
  defaultZoom: number;
  maxZoom: number;
  minZoom: number;
  providers: Array<{
    id: string;
    name: string;
    type: string;
    url: string;
    attribution: string;
    maxZoom: number;
    subdomains?: string[];
  }>;
  controls: {
    zoom: boolean;
    fullscreen: boolean;
    layers: boolean;
    search: boolean;
    geolocation: boolean;
    traffic: boolean;
    satellite: boolean;
  };
}

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface EnhancedMapProps {
  height?: string;
  showControls?: boolean;
  showVehicles?: boolean;
  onVehicleClick?: (vehicle: Vehicle) => void;
}

const EnhancedMap: React.FC<EnhancedMapProps> = ({
  height = '600px',
  showControls = true,
  showVehicles = true,
  onVehicleClick
}) => {
  const [mapConfig, setMapConfig] = useState<MapConfig | null>(null);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');
  const [showTraffic, setShowTraffic] = useState(false);
  const [showSatellite, setShowSatellite] = useState(false);
  const mapRef = useRef<L.Map | null>(null);

  // Load map configuration
  useEffect(() => {
    loadMapConfig();
    if (showVehicles) {
      loadVehicles();
    }
  }, [showVehicles]);

  const loadMapConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/config/enhanced');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.data) {
        setMapConfig(data.data);
        setSelectedProvider(data.data.defaultProvider);
      } else {
        throw new Error('Invalid map configuration response');
      }
    } catch (err) {
      console.error('Failed to load map config:', err);
      setError('فشل في تحميل إعدادات الخريطة');
      
      // Fallback configuration
      setMapConfig({
        defaultProvider: 'openstreetmap',
        defaultCenter: { lat: 24.7136, lng: 46.6753 },
        defaultZoom: 12,
        maxZoom: 19,
        minZoom: 3,
        providers: [
          {
            id: 'openstreetmap',
            name: 'OpenStreetMap',
            type: 'tile',
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© OpenStreetMap contributors',
            maxZoom: 19,
            subdomains: ['a', 'b', 'c']
          }
        ],
        controls: {
          zoom: true,
          fullscreen: true,
          layers: true,
          search: true,
          geolocation: true,
          traffic: true,
          satellite: true
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const loadVehicles = async () => {
    try {
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
        }
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Mock vehicles for demo
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'أحمد محمد',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'سارة أحمد',
          lastUpdate: new Date().toISOString()
        }
      ]);
    }
  };

  const getCurrentProvider = () => {
    if (!mapConfig) return null;
    return mapConfig.providers.find(p => p.id === selectedProvider);
  };

  const handleProviderChange = (providerId: string) => {
    setSelectedProvider(providerId);
  };

  const handleRefresh = () => {
    loadMapConfig();
    if (showVehicles) {
      loadVehicles();
    }
  };

  const createVehicleIcon = (vehicle: Vehicle) => {
    const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
    
    return L.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: white;
          font-weight: bold;
        ">
          🚗
        </div>
      `,
      className: 'vehicle-marker',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={height}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          جاري تحميل الخريطة...
        </Typography>
      </Box>
    );
  }

  if (error || !mapConfig) {
    return (
      <Alert severity="error" sx={{ height }}>
        <Typography variant="h6">خطأ في تحميل الخريطة</Typography>
        <Typography variant="body2">{error || 'فشل في تحميل إعدادات الخريطة'}</Typography>
        <Button onClick={handleRefresh} sx={{ mt: 1 }}>
          إعادة المحاولة
        </Button>
      </Alert>
    );
  }

  const currentProvider = getCurrentProvider();

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Map Controls */}
      {showControls && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            gap: 1
          }}
        >
          {/* Provider Selection */}
          <Card sx={{ minWidth: 200 }}>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <FormControl fullWidth size="small">
                <InputLabel>مزود الخريطة</InputLabel>
                <Select
                  value={selectedProvider}
                  onChange={(e) => handleProviderChange(e.target.value)}
                  label="مزود الخريطة"
                >
                  {mapConfig.providers.map(provider => (
                    <MenuItem key={provider.id} value={provider.id}>
                      {provider.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </CardContent>
          </Card>

          {/* Map Options */}
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showSatellite}
                    onChange={(e) => setShowSatellite(e.target.checked)}
                    size="small"
                  />
                }
                label="صور الأقمار الصناعية"
                sx={{ mb: 0.5 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={showTraffic}
                    onChange={(e) => setShowTraffic(e.target.checked)}
                    size="small"
                  />
                }
                label="حركة المرور"
              />
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <ButtonGroup orientation="vertical" variant="contained" size="small">
            <Tooltip title="تحديث">
              <IconButton onClick={handleRefresh}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="موقعي">
              <IconButton>
                <MyLocationIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="ملء الشاشة">
              <IconButton>
                <FullscreenIcon />
              </IconButton>
            </Tooltip>
          </ButtonGroup>
        </Box>
      )}

      {/* Vehicle Stats */}
      {showVehicles && vehicles.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <Typography variant="subtitle2" gutterBottom>
                إحصائيات المركبات
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`المجموع: ${vehicles.length}`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                  size="small"
                  color="success"
                />
                <Chip
                  label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                  size="small"
                  color="warning"
                />
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Map Container */}
      <MapContainer
        center={[mapConfig.defaultCenter.lat, mapConfig.defaultCenter.lng]}
        zoom={mapConfig.defaultZoom}
        style={{ height: '100%', width: '100%' }}
        zoomControl={false}
      >
        {/* Base Tile Layer */}
        {currentProvider && (
          <TileLayer
            url={showSatellite 
              ? "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
              : currentProvider.url
            }
            attribution={showSatellite 
              ? '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
              : currentProvider.attribution
            }
            maxZoom={currentProvider.maxZoom}
            subdomains={currentProvider.subdomains}
          />
        )}

        {/* Vehicle Markers */}
        {showVehicles && vehicles.map(vehicle => (
          <Marker
            key={vehicle.id}
            position={[vehicle.lat, vehicle.lng]}
            icon={createVehicleIcon(vehicle)}
            eventHandlers={{
              click: () => onVehicleClick?.(vehicle)
            }}
          >
            <Popup>
              <Card sx={{ minWidth: 200 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    مركبة {vehicle.id}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>السائق:</strong> {vehicle.driver}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>السرعة:</strong> {vehicle.speed} كم/س
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>الحالة:</strong> {vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>آخر تحديث:</strong> {new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}
                  </Typography>
                  <Chip
                    label={vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                    color={vehicle.status === 'active' ? 'success' : 'warning'}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </Card>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </Paper>
  );
};

export default EnhancedMap;
