import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Ta<PERSON>,
  Tab,
  Badge,
  Card,
  CardContent,
  IconButton,
  Button,
  Chip,
  Avatar,
  Divider,
  Tooltip,
  Menu,
  MenuItem,
  Alert as MuiAlert
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as <PERSON>rrorIcon,
  Info as InfoIcon,
  Dangerous as DangerousIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { Alert, ALERT_SEVERITY_CONFIG } from '../../types/operations';
import liveOperationsService from '../../services/liveOperationsService';

interface AlertsPanelProps {
  alerts: Alert[];
  onAlertAction: (alertId: string, action: string) => void;
  onRefresh: () => void;
}

const AlertsPanel: React.FC<AlertsPanelProps> = ({ alerts, onAlertAction, onRefresh }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [loading, setLoading] = useState(false);

  // Filter alerts by status
  const newAlerts = alerts.filter(alert => alert.status === 'new');
  const inProgressAlerts = alerts.filter(alert => alert.status === 'in_progress');
  const resolvedAlerts = alerts.filter(alert => alert.status === 'resolved');
  const dismissedAlerts = alerts.filter(alert => alert.status === 'dismissed');

  const tabData = [
    { label: 'Events', count: newAlerts.length + inProgressAlerts.length, alerts: [...newAlerts, ...inProgressAlerts] },
    { label: 'Alerts', count: newAlerts.length, alerts: newAlerts },
    { label: 'Dismissed', count: dismissedAlerts.length, alerts: dismissedAlerts }
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, alert: Alert) => {
    setAnchorEl(event.currentTarget);
    setSelectedAlert(alert);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAlert(null);
  };

  const handleAlertAction = async (action: string) => {
    if (!selectedAlert) return;

    setLoading(true);
    try {
      await onAlertAction(selectedAlert.alert_id, action);
      handleMenuClose();
    } catch (error) {
      console.error('Error handling alert action:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low':
        return <InfoIcon sx={{ color: ALERT_SEVERITY_CONFIG.low.color }} />;
      case 'medium':
        return <WarningIcon sx={{ color: ALERT_SEVERITY_CONFIG.medium.color }} />;
      case 'high':
        return <ErrorIcon sx={{ color: ALERT_SEVERITY_CONFIG.high.color }} />;
      case 'critical':
        return <DangerousIcon sx={{ color: ALERT_SEVERITY_CONFIG.critical.color }} />;
      default:
        return <InfoIcon />;
    }
  };

  const getAlertTypeLabel = (alertType: string) => {
    const labels: { [key: string]: string } = {
      pickup_delay: 'تأخير الالتقاط',
      off_route: 'خارج المسار',
      cancellation: 'إلغاء',
      parcel_delivery_delay: 'تأخير تسليم طرد',
      parcel_lost: 'طرد مفقود',
      accident: 'حادث',
      vehicle_breakdown: 'عطل مركبة',
      traffic_jam: 'ازدحام مروري',
      fuel_low: 'وقود منخفض',
      maintenance_due: 'صيانة مستحقة'
    };
    return labels[alertType] || alertType;
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const alertTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - alertTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `${diffInMinutes} د`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} س`;
    return `${Math.floor(diffInMinutes / 1440)} ي`;
  };

  const renderAlert = (alert: Alert) => (
    <Card 
      key={alert.alert_id} 
      sx={{ 
        mb: 1, 
        border: `1px solid ${ALERT_SEVERITY_CONFIG[alert.severity].color}20`,
        backgroundColor: ALERT_SEVERITY_CONFIG[alert.severity].bgColor
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box display="flex" alignItems="flex-start" flex={1}>
            <Avatar 
              sx={{ 
                width: 32, 
                height: 32, 
                mr: 1.5,
                backgroundColor: ALERT_SEVERITY_CONFIG[alert.severity].color
              }}
            >
              {getSeverityIcon(alert.severity)}
            </Avatar>
            
            <Box flex={1}>
              <Box display="flex" alignItems="center" mb={0.5}>
                <Typography variant="subtitle2" fontWeight="bold">
                  {getAlertTypeLabel(alert.alert_type)}
                </Typography>
                {alert.estimated_delay && (
                  <Chip 
                    label={`+${alert.estimated_delay} د`}
                    size="small"
                    color="warning"
                    sx={{ ml: 1, height: 20 }}
                  />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {alert.description}
              </Typography>
              
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box display="flex" alignItems="center" gap={1}>
                  {alert.trip_id && (
                    <Chip 
                      label={`رحلة ${alert.trip_id.slice(-6)}`}
                      size="small"
                      variant="outlined"
                    />
                  )}
                  {alert.parcel_id && (
                    <Chip 
                      label={`طرد ${alert.parcel_id.slice(-6)}`}
                      size="small"
                      variant="outlined"
                    />
                  )}
                  {alert.driver_id && (
                    <Chip 
                      label={`سائق ${alert.driver_id.slice(-6)}`}
                      size="small"
                      variant="outlined"
                    />
                  )}
                </Box>
                
                <Typography variant="caption" color="text.secondary">
                  {getTimeAgo(alert.timestamp)}
                </Typography>
              </Box>
            </Box>
          </Box>
          
          <IconButton 
            size="small" 
            onClick={(e) => handleMenuOpen(e, alert)}
            sx={{ ml: 1 }}
          >
            <MoreVertIcon />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Paper 
      sx={{ 
        width: 400, 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        position: 'relative'
      }}
    >
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight="bold">
            التنبيهات والأحداث
          </Typography>
          <Box>
            <Tooltip title="تحديث">
              <IconButton onClick={onRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="فلترة">
              <IconButton size="small">
                <FilterIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        {/* Tabs */}
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ minHeight: 40 }}
        >
          {tabData.map((tab, index) => (
            <Tab
              key={index}
              label={
                <Badge badgeContent={tab.count} color="error" max={99}>
                  {tab.label}
                </Badge>
              }
              sx={{ minHeight: 40, fontSize: '0.875rem' }}
            />
          ))}
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {tabData[activeTab].alerts.length === 0 ? (
          <Box 
            display="flex" 
            flexDirection="column" 
            alignItems="center" 
            justifyContent="center" 
            height="200px"
          >
            <CheckCircleIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="body1" color="text.secondary">
              لا توجد تنبيهات
            </Typography>
            <Typography variant="body2" color="text.secondary">
              جميع العمليات تسير بسلاسة
            </Typography>
          </Box>
        ) : (
          tabData[activeTab].alerts.map(renderAlert)
        )}
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 200 }
        }}
      >
        {selectedAlert?.status === 'new' && (
          <MenuItem onClick={() => handleAlertAction('in_progress')}>
            <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
            بدء المعالجة
          </MenuItem>
        )}
        
        {(selectedAlert?.status === 'new' || selectedAlert?.status === 'in_progress') && (
          <MenuItem onClick={() => handleAlertAction('resolved')}>
            <CheckCircleIcon sx={{ mr: 1, color: 'success.main' }} />
            تم الحل
          </MenuItem>
        )}
        
        <MenuItem onClick={() => handleAlertAction('dismissed')}>
          <CloseIcon sx={{ mr: 1, color: 'text.secondary' }} />
          تجاهل
        </MenuItem>
        
        <Divider />
        
        <MenuItem onClick={handleMenuClose}>
          <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
          عرض التفاصيل
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default AlertsPanel;
