terraform {
  required_version = ">= 1.0"
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
  }
}

variable "cluster_name" {
  description = "Name of the Kubernetes cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for TECNO DRIVE"
  type        = string
  default     = "tecnodrive"
}

variable "environment" {
  description = "Environment (staging/production)"
  type        = string
}

# Create namespace
resource "kubernetes_namespace" "tecnodrive" {
  metadata {
    name = var.namespace
    labels = {
      environment = var.environment
      app         = "tecnodrive"
    }
  }
}

# PostgreSQL with PostGIS
resource "helm_release" "postgresql" {
  name       = "postgresql"
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "postgresql"
  namespace  = kubernetes_namespace.tecnodrive.metadata[0].name

  values = [
    yamlencode({
      auth = {
        postgresPassword = var.postgres_password
        database         = "tecnodrive"
      }
      image = {
        repository = "postgis/postgis"
        tag        = "15-3.3"
      }
      primary = {
        persistence = {
          enabled = true
          size    = "20Gi"
        }
        resources = {
          limits = {
            memory = "2Gi"
            cpu    = "1000m"
          }
          requests = {
            memory = "1Gi"
            cpu    = "500m"
          }
        }
        configuration = <<-EOT
          max_connections = 200
          shared_buffers = 256MB
          effective_cache_size = 1GB
          maintenance_work_mem = 64MB
          checkpoint_completion_target = 0.9
          wal_buffers = 16MB
          default_statistics_target = 100
        EOT
      }
    })
  ]
}

# Redis
resource "helm_release" "redis" {
  name       = "redis"
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "redis"
  namespace  = kubernetes_namespace.tecnodrive.metadata[0].name

  values = [
    yamlencode({
      auth = {
        enabled  = false
      }
      master = {
        persistence = {
          enabled = true
          size    = "8Gi"
        }
        resources = {
          limits = {
            memory = "512Mi"
            cpu    = "500m"
          }
          requests = {
            memory = "256Mi"
            cpu    = "250m"
          }
        }
      }
    })
  ]
}

# Kafka
resource "helm_release" "kafka" {
  name       = "kafka"
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "kafka"
  namespace  = kubernetes_namespace.tecnodrive.metadata[0].name

  values = [
    yamlencode({
      replicaCount = 3
      persistence = {
        enabled = true
        size    = "10Gi"
      }
      resources = {
        limits = {
          memory = "1Gi"
          cpu    = "1000m"
        }
        requests = {
          memory = "512Mi"
          cpu    = "500m"
        }
      }
      config = {
        "default.replication.factor" = "3"
        "min.insync.replicas"         = "2"
        "num.partitions"              = "3"
      }
    })
  ]
}

# Jaeger for distributed tracing
resource "helm_release" "jaeger" {
  name       = "jaeger"
  repository = "https://jaegertracing.github.io/helm-charts"
  chart      = "jaeger"
  namespace  = kubernetes_namespace.tecnodrive.metadata[0].name

  values = [
    yamlencode({
      provisionDataStore = {
        cassandra = false
        elasticsearch = true
      }
      elasticsearch = {
        deploy = true
        replicas = 1
      }
      agent = {
        enabled = true
      }
      collector = {
        enabled = true
        service = {
          type = "ClusterIP"
        }
      }
      query = {
        enabled = true
        service = {
          type = "LoadBalancer"
        }
      }
    })
  ]
}

# Prometheus
resource "helm_release" "prometheus" {
  name       = "prometheus"
  repository = "https://prometheus-community.github.io/helm-charts"
  chart      = "kube-prometheus-stack"
  namespace  = kubernetes_namespace.tecnodrive.metadata[0].name

  values = [
    yamlencode({
      prometheus = {
        prometheusSpec = {
          retention = "30d"
          storageSpec = {
            volumeClaimTemplate = {
              spec = {
                storageClassName = "standard"
                accessModes      = ["ReadWriteOnce"]
                resources = {
                  requests = {
                    storage = "20Gi"
                  }
                }
              }
            }
          }
        }
      }
      grafana = {
        enabled = true
        service = {
          type = "LoadBalancer"
        }
        persistence = {
          enabled = true
          size    = "10Gi"
        }
      }
    })
  ]
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

output "namespace" {
  value = kubernetes_namespace.tecnodrive.metadata[0].name
}

output "postgresql_service" {
  value = "${helm_release.postgresql.name}-postgresql"
}

output "redis_service" {
  value = "${helm_release.redis.name}-redis-master"
}

output "kafka_service" {
  value = "${helm_release.kafka.name}-kafka"
}
