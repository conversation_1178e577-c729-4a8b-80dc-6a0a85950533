import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Paper,
} from '@mui/material';
import {
  ArrowBack,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  Domain as DomainIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Storage as StorageIcon,
  Api as ApiIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { saasService, TenantDto, SubscriptionDto, BillingInvoiceDto, UsageAnalyticsDto } from '../../services/saasService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenant-tabpanel-${index}`}
      aria-labelledby={`tenant-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const TenantDetail: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  const navigate = useNavigate();
  
  const [tenant, setTenant] = useState<TenantDto | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionDto[]>([]);
  const [invoices, setInvoices] = useState<BillingInvoiceDto[]>([]);
  const [usage, setUsage] = useState<UsageAnalyticsDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  // Load tenant data
  const loadTenantData = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);
      
      // Load tenant details
      const tenantResponse = await saasService.getTenantById(tenantId);
      if (tenantResponse.success && tenantResponse.data) {
        setTenant(tenantResponse.data);
      }

      // Load subscriptions
      const subscriptionsResponse = await saasService.getSubscriptions(tenantId);
      if (subscriptionsResponse.success && subscriptionsResponse.data) {
        setSubscriptions(subscriptionsResponse.data);
      }

      // Load billing invoices
      const invoicesResponse = await saasService.getBillingInvoices(tenantId);
      if (invoicesResponse.success && invoicesResponse.data) {
        setInvoices(invoicesResponse.data);
      }

      // Load usage analytics
      const usageResponse = await saasService.getUsageAnalytics(tenantId, 'last_30_days');
      if (usageResponse.success && usageResponse.data) {
        setUsage(usageResponse.data);
      }

    } catch (error) {
      console.error('Error loading tenant data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTenantData();
  }, [tenantId]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const },
      INACTIVE: { label: 'غير نشط', color: 'default' as const },
      SUSPENDED: { label: 'معلق', color: 'warning' as const },
      PENDING: { label: 'في الانتظار', color: 'info' as const },
      PAID: { label: 'مدفوع', color: 'success' as const },
      OVERDUE: { label: 'متأخر', color: 'error' as const },
      CANCELLED: { label: 'ملغي', color: 'default' as const },
      EXPIRED: { label: 'منتهي', color: 'warning' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  // Mock data for charts
  const usageChartData = [
    { month: 'يناير', users: 25, apiCalls: 8000 },
    { month: 'فبراير', users: 30, apiCalls: 9500 },
    { month: 'مارس', users: 35, apiCalls: 11000 },
    { month: 'أبريل', users: 40, apiCalls: 13000 },
    { month: 'مايو', users: 42, apiCalls: 15000 },
  ];

  const featureUsageData = [
    { name: 'إدارة المستخدمين', value: 1250, color: '#8884d8' },
    { name: 'التقارير', value: 850, color: '#82ca9d' },
    { name: 'التحليلات', value: 650, color: '#ffc658' },
    { name: 'API', value: 15000, color: '#ff7300' },
  ];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>جاري تحميل بيانات العميل...</Typography>
      </Box>
    );
  }

  if (!tenant) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">
          لم يتم العثور على العميل
        </Typography>
        <Button onClick={() => navigate('/saas/tenants')} sx={{ mt: 2 }}>
          العودة إلى قائمة العملاء
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate('/saas/tenants')}>
            <ArrowBack />
          </IconButton>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            <BusinessIcon />
          </Avatar>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
              {tenant.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {tenant.domain}
            </Typography>
          </Box>
          {getStatusChip(tenant.status)}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/saas/tenants/${tenantId}/edit`)}
          >
            تعديل
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
          >
            حذف
          </Button>
        </Box>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PeopleIcon color="primary" />
                <Box>
                  <Typography variant="h6">{tenant.totalUsers || 0}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المستخدمين
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MoneyIcon color="success" />
                <Box>
                  <Typography variant="h6">
                    {tenant.monthlyRevenue?.toLocaleString() || 0} ريال
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    الإيرادات الشهرية
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <StorageIcon color="info" />
                <Box>
                  <Typography variant="h6">
                    {tenant.dataUsage?.toFixed(1) || 0} GB
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    استهلاك البيانات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ApiIcon color="warning" />
                <Box>
                  <Typography variant="h6">
                    {tenant.apiCalls?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    استدعاءات API
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="معلومات العميل" />
            <Tab label="الاشتراكات" />
            <Tab label="الفوترة" />
            <Tab label="تحليلات الاستخدام" />
          </Tabs>
        </Box>

        {/* Tab 1: Tenant Info */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  معلومات الاتصال
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <DomainIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="النطاق"
                      secondary={tenant.domain}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <EmailIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="البريد الإلكتروني"
                      secondary={tenant.email}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <PhoneIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="الهاتف"
                      secondary={tenant.phone}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <CalendarIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="تاريخ الإنشاء"
                      secondary={new Date(tenant.createdAt).toLocaleDateString('ar-SA')}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  إحصائيات سريعة
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>عدد الاشتراكات:</Typography>
                    <Typography sx={{ fontWeight: 'bold' }}>
                      {tenant.subscriptionsCount || 0}
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>الرصيد المستحق:</Typography>
                    <Typography sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                      {tenant.billingBalance?.toLocaleString() || 0} ريال
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>آخر نشاط:</Typography>
                    <Typography sx={{ fontWeight: 'bold' }}>
                      {tenant.lastActivity ? new Date(tenant.lastActivity).toLocaleDateString('ar-SA') : 'غير محدد'}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab 2: Subscriptions */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">الاشتراكات</Typography>
            <Button variant="contained" startIcon={<EditIcon />}>
              إضافة اشتراك
            </Button>
          </Box>
          <Grid container spacing={2}>
            {subscriptions.map((subscription) => (
              <Grid item xs={12} md={6} key={subscription.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">{subscription.planName}</Typography>
                      {getStatusChip(subscription.status)}
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      النوع: {subscription.planType}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      المقاعد: {subscription.seats}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      السعر الشهري: {subscription.monthlyPrice.toLocaleString()} ريال
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      من {new Date(subscription.startDate).toLocaleDateString('ar-SA')} 
                      إلى {new Date(subscription.endDate).toLocaleDateString('ar-SA')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Tab 3: Billing */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">الفواتير</Typography>
            <Button variant="contained" startIcon={<EditIcon />}>
              إنشاء فاتورة
            </Button>
          </Box>
          <List>
            {invoices.map((invoice) => (
              <React.Fragment key={invoice.id}>
                <ListItem>
                  <ListItemText
                    primary={`فاتورة ${invoice.invoiceNumber}`}
                    secondary={`تاريخ الإصدار: ${new Date(invoice.issueDate).toLocaleDateString('ar-SA')}`}
                  />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h6">
                      {invoice.amount.toLocaleString()} {invoice.currency}
                    </Typography>
                    {getStatusChip(invoice.status)}
                  </Box>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        </TabPanel>

        {/* Tab 4: Usage Analytics */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  استخدام المستخدمين واستدعاءات API
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={usageChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="users" stroke="#8884d8" name="المستخدمين" />
                    <Line type="monotone" dataKey="apiCalls" stroke="#82ca9d" name="استدعاءات API" />
                  </LineChart>
                </ResponsiveContainer>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  استخدام المميزات
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={featureUsageData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {featureUsageData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default TenantDetail;
