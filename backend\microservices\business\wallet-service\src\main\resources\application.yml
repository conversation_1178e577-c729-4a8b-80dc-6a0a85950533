server:
  port: 8093

spring:
  application:
    name: wallet-service
  
  profiles:
    active: dev
  
  datasource:
    url: **************************************************
    username: tecnodrive_user
    password: tecnodrive_pass
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10 minutes
      cache-null-values: false
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8081/auth/realms/tecnodrive

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.tecnodrive.walletservice: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/wallet-service.log

# Wallet Service Configuration
wallet:
  default:
    currency: SAR
    daily-limit: 5000.00
    monthly-limit: 50000.00
    verification-level: BASIC
  
  limits:
    max-daily-limit: 10000.00
    max-monthly-limit: 100000.00
    min-topup-amount: 10.00
    max-topup-amount: 5000.00
  
  security:
    pin-length: 4
    max-failed-attempts: 3
    lock-duration-minutes: 30
  
  transaction:
    timeout-minutes: 30
    batch-size: 100
    retry-attempts: 3

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.tecnodrive.walletservice.controller

---
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: **************************************************_dev
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

logging:
  level:
    root: INFO
    com.tecnodrive.walletservice: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: *******************************************************
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.tecnodrive.walletservice: INFO
