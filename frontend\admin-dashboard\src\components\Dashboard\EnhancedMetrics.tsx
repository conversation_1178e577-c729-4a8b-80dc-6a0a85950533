import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  AvatarGroup,
} from '@mui/material';
import {
  Refresh,
  TrendingUp,
  Speed,
  Security,
  CloudDone,
  NetworkCheck,
  Storage,
  Memory,
} from '@mui/icons-material';

interface MetricData {
  label: string;
  value: number;
  max: number;
  unit: string;
  color: string;
  icon: React.ReactNode;
  status: 'excellent' | 'good' | 'warning' | 'critical';
}

const EnhancedMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState<MetricData[]>([
    {
      label: 'أداء النظام',
      value: 94,
      max: 100,
      unit: '%',
      color: '#48bb78',
      icon: <Speed />,
      status: 'excellent',
    },
    {
      label: 'استخدام الذاكرة',
      value: 68,
      max: 100,
      unit: '%',
      color: '#4299e1',
      icon: <Memory />,
      status: 'good',
    },
    {
      label: 'مساحة التخزين',
      value: 45,
      max: 100,
      unit: '%',
      color: '#ed8936',
      icon: <Storage />,
      status: 'good',
    },
    {
      label: 'حالة الشبكة',
      value: 98,
      max: 100,
      unit: '%',
      color: '#48bb78',
      icon: <NetworkCheck />,
      status: 'excellent',
    },
    {
      label: 'الأمان',
      value: 100,
      max: 100,
      unit: '%',
      color: '#48bb78',
      icon: <Security />,
      status: 'excellent',
    },
    {
      label: 'النسخ الاحتياطي',
      value: 85,
      max: 100,
      unit: '%',
      color: '#4299e1',
      icon: <CloudDone />,
      status: 'good',
    },
  ]);

  const [lastUpdate, setLastUpdate] = useState(new Date());

  const refreshMetrics = () => {
    // Simulate real-time data updates
    setMetrics(prev => prev.map(metric => ({
      ...metric,
      value: Math.max(0, Math.min(100, metric.value + (Math.random() - 0.5) * 10)),
    })));
    setLastUpdate(new Date());
  };

  useEffect(() => {
    const interval = setInterval(refreshMetrics, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return '#48bb78';
      case 'good': return '#4299e1';
      case 'warning': return '#ed8936';
      case 'critical': return '#f56565';
      default: return '#4299e1';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'excellent': return 'ممتاز';
      case 'good': return 'جيد';
      case 'warning': return 'تحذير';
      case 'critical': return 'حرج';
      default: return 'عادي';
    }
  };

  return (
    <Card className="enhanced-card" sx={{ mb: 3 }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                mr: 2,
              }}
            >
              <TrendingUp />
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
                مؤشرات الأداء المتقدمة
              </Typography>
              <Typography variant="body2" color="text.secondary">
                آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
          <Tooltip title="تحديث البيانات">
            <IconButton 
              onClick={refreshMetrics}
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8, #6a4190)',
                },
              }}
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        <Grid container spacing={3}>
          {metrics.map((metric, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Box
                className="scale-in"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  background: 'rgba(255, 255, 255, 0.5)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  animationDelay: `${index * 0.1}s`,
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        background: `${metric.color}20`,
                        color: metric.color,
                        mr: 1.5,
                      }}
                    >
                      {metric.icon}
                    </Avatar>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {metric.label}
                    </Typography>
                  </Box>
                  <Chip
                    label={getStatusLabel(metric.status)}
                    size="small"
                    sx={{
                      background: getStatusColor(metric.status),
                      color: 'white',
                      fontWeight: 600,
                    }}
                  />
                </Box>

                <Box sx={{ mb: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: metric.color }}>
                      {metric.value.toFixed(0)}{metric.unit}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {metric.max}{metric.unit}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(metric.value / metric.max) * 100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: `${metric.color}20`,
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        background: `linear-gradient(90deg, ${metric.color}, ${metric.color}cc)`,
                      },
                    }}
                  />
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* System Status Summary */}
        <Box sx={{ mt: 3, p: 2, borderRadius: 2, background: 'rgba(72, 187, 120, 0.1)' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ background: '#48bb78', mr: 2 }}>
                ✅
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 700, color: '#48bb78' }}>
                  النظام يعمل بكفاءة عالية
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  جميع المؤشرات ضمن المعدل الطبيعي
                </Typography>
              </Box>
            </Box>
            <AvatarGroup max={4}>
              <Avatar sx={{ width: 32, height: 32, background: '#48bb78' }}>🚀</Avatar>
              <Avatar sx={{ width: 32, height: 32, background: '#4299e1' }}>⚡</Avatar>
              <Avatar sx={{ width: 32, height: 32, background: '#ed8936' }}>🔒</Avatar>
              <Avatar sx={{ width: 32, height: 32, background: '#9f7aea' }}>📊</Avatar>
            </AvatarGroup>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EnhancedMetrics;
