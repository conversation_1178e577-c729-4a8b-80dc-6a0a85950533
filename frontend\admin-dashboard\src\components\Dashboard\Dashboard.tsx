import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Paper,
  Button,
  Chip,
} from '@mui/material';
import {
  DirectionsCar,
  People,
  AttachMoney,
  TrendingUp,
  LocalTaxi,
  PersonAdd,
  Dashboard as DashboardIcon,
  Settings,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../../store/store';
import { fetchAnalytics } from '../../store/slices/analyticsSlice';
import StatsCard from './StatsCard';
import RecentRides from './RecentRides';
import RevenueChart from './RevenueChart';
import ActiveDriversMap from './ActiveDriversMap';
import SimplifiedMetrics from './SimplifiedMetrics';
import DashboardActions from './DashboardActions';
import ServiceStatusIndicator from '../ServiceHealth/ServiceStatusIndicator';

const Dashboard: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { data: analytics, loading } = useSelector((state: RootState) => state.analytics);

  useEffect(() => {
    dispatch(fetchAnalytics() as any);
  }, [dispatch]);

  // Mock data for demonstration
  const mockStats = {
    totalRides: 1250,
    totalRevenue: 45000,
    activeDrivers: 85,
    activePassengers: 320,
    ridesGrowth: 12.5,
    revenueGrowth: 8.3,
  };

  const statsCards = [
    {
      title: 'إجمالي الرحلات',
      value: analytics?.totalRides || mockStats.totalRides,
      growth: analytics?.ridesGrowth || mockStats.ridesGrowth,
      icon: <DirectionsCar />,
      color: '#1976d2',
    },
    {
      title: 'إجمالي الإيرادات',
      value: `${analytics?.totalRevenue || mockStats.totalRevenue} ريال`,
      growth: analytics?.revenueGrowth || mockStats.revenueGrowth,
      icon: <AttachMoney />,
      color: '#2e7d32',
    },
    {
      title: 'السائقون النشطون',
      value: analytics?.activeDrivers || mockStats.activeDrivers,
      growth: 5.2,
      icon: <LocalTaxi />,
      color: '#ed6c02',
    },
    {
      title: 'الركاب النشطون',
      value: analytics?.activePassengers || mockStats.activePassengers,
      growth: 15.8,
      icon: <People />,
      color: '#9c27b0',
    },
  ];

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2, textAlign: 'center' }}>
          جاري تحميل البيانات...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh' }}>
      {/* Simplified Welcome Section */}
      <Paper
        className="enhanced-card gradient-primary"
        sx={{
          p: 4,
          mb: 4,
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  background: 'rgba(255, 255, 255, 0.2)',
                  mr: 3,
                  fontSize: '2rem',
                }}
              >
                📊
              </Avatar>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 800, mb: 1 }}>
                  لوحة المعلومات الرئيسية
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 400 }}>
                  نظرة شاملة على أداء النظام والإحصائيات
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Settings />}
                onClick={() => navigate('/control-panel')}
                sx={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              >
                لوحة التحكم
              </Button>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 4, mt: 3 }}>
            <Box>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                آخر تحديث
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {new Date().toLocaleString('ar-SA')}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                حالة النظام
              </Typography>
              <Chip
                label="نشط ومستقر"
                sx={{
                  background: 'rgba(72, 187, 120, 0.2)',
                  color: '#4ade80',
                  fontWeight: 600,
                }}
              />
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Box className="scale-in" sx={{ animationDelay: `${index * 0.1}s` }}>
              <StatsCard {...stat} />
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* Simplified Metrics */}
      <SimplifiedMetrics />

      {/* Service Status Indicator */}
      <ServiceStatusIndicator />

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Revenue Chart */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                الإيرادات الشهرية
              </Typography>
              <RevenueChart />
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                إحصائيات سريعة
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: '#1976d2' }}>
                    <DirectionsCar />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      رحلات اليوم
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      127
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: '#2e7d32' }}>
                    <AttachMoney />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      إيرادات اليوم
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      3,450 ريال
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: '#ed6c02' }}>
                    <PersonAdd />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      مستخدمون جدد
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      23
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: '#9c27b0' }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      معدل النمو
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                      +12.5%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Rides */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                الرحلات الأخيرة
              </Typography>
              <RecentRides />
            </CardContent>
          </Card>
        </Grid>

        {/* Active Drivers Map */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                السائقون النشطون
              </Typography>
              <ActiveDriversMap />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dashboard Actions */}
      <Box sx={{ mt: 4 }}>
        <DashboardActions />
      </Box>
    </Box>
  );
};

export default Dashboard;
