import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON>ton,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Slider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Shield as ShieldIcon,
  Block as BlockIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
} from 'recharts';

interface RateLimitRule {
  id: string;
  name: string;
  path: string;
  method: string;
  limit: number;
  window: number;
  enabled: boolean;
  violations: number;
  lastViolation?: string;
}

interface CircuitBreakerConfig {
  id: string;
  service: string;
  failureThreshold: number;
  timeout: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failures: number;
  lastFailure?: string;
  enabled: boolean;
}

interface SecurityEvent {
  id: string;
  type: 'RATE_LIMIT_EXCEEDED' | 'SUSPICIOUS_ACTIVITY' | 'UNAUTHORIZED_ACCESS' | 'DDoS_ATTEMPT';
  source: string;
  target: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: string;
  details: string;
  blocked: boolean;
}

interface MetricData {
  timestamp: string;
  requests: number;
  errors: number;
  responseTime: number;
  rateLimitViolations: number;
  circuitBreakerTrips: number;
}

const AdvancedMonitoring: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [rateLimitRules, setRateLimitRules] = useState<RateLimitRule[]>([]);
  const [circuitBreakers, setCircuitBreakers] = useState<CircuitBreakerConfig[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [metricsData, setMetricsData] = useState<MetricData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSeverity, setFilterSeverity] = useState('ALL');
  const [openRuleDialog, setOpenRuleDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Mock data
  const mockRateLimitRules: RateLimitRule[] = [
    {
      id: 'rule-1',
      name: 'API العام',
      path: '/api/**',
      method: 'ALL',
      limit: 1000,
      window: 60,
      enabled: true,
      violations: 45,
      lastViolation: '2025-07-09T14:25:00Z',
    },
    {
      id: 'rule-2',
      name: 'تسجيل الدخول',
      path: '/api/auth/login',
      method: 'POST',
      limit: 5,
      window: 60,
      enabled: true,
      violations: 12,
      lastViolation: '2025-07-09T13:45:00Z',
    },
    {
      id: 'rule-3',
      name: 'إنشاء الرحلات',
      path: '/api/rides',
      method: 'POST',
      limit: 100,
      window: 60,
      enabled: true,
      violations: 8,
      lastViolation: '2025-07-09T12:30:00Z',
    },
  ];

  const mockCircuitBreakers: CircuitBreakerConfig[] = [
    {
      id: 'cb-1',
      service: 'auth-service',
      failureThreshold: 5,
      timeout: 30000,
      state: 'CLOSED',
      failures: 2,
      enabled: true,
    },
    {
      id: 'cb-2',
      service: 'rides-service',
      failureThreshold: 10,
      timeout: 60000,
      state: 'HALF_OPEN',
      failures: 7,
      lastFailure: '2025-07-09T14:20:00Z',
      enabled: true,
    },
    {
      id: 'cb-3',
      service: 'notification-service',
      failureThreshold: 3,
      timeout: 15000,
      state: 'OPEN',
      failures: 3,
      lastFailure: '2025-07-09T14:15:00Z',
      enabled: true,
    },
  ];

  const mockSecurityEvents: SecurityEvent[] = [
    {
      id: 'event-1',
      type: 'RATE_LIMIT_EXCEEDED',
      source: '192.168.1.100',
      target: '/api/auth/login',
      severity: 'MEDIUM',
      timestamp: '2025-07-09T14:30:00Z',
      details: 'تجاوز حد معدل تسجيل الدخول (5 طلبات/دقيقة)',
      blocked: true,
    },
    {
      id: 'event-2',
      type: 'SUSPICIOUS_ACTIVITY',
      source: '10.0.0.50',
      target: '/api/fleet/vehicles',
      severity: 'HIGH',
      timestamp: '2025-07-09T14:25:00Z',
      details: 'محاولة وصول مشبوهة إلى بيانات الأسطول',
      blocked: true,
    },
    {
      id: 'event-3',
      type: 'DDoS_ATTEMPT',
      source: '***********/24',
      target: '/api/**',
      severity: 'CRITICAL',
      timestamp: '2025-07-09T14:20:00Z',
      details: 'محاولة هجوم DDoS مكتشفة من شبكة فرعية',
      blocked: true,
    },
  ];

  const mockMetricsData: MetricData[] = [
    { timestamp: '14:00', requests: 8500, errors: 45, responseTime: 140, rateLimitViolations: 12, circuitBreakerTrips: 0 },
    { timestamp: '14:05', requests: 9200, errors: 52, responseTime: 135, rateLimitViolations: 8, circuitBreakerTrips: 1 },
    { timestamp: '14:10', requests: 8800, errors: 38, responseTime: 150, rateLimitViolations: 15, circuitBreakerTrips: 0 },
    { timestamp: '14:15', requests: 9500, errors: 61, responseTime: 145, rateLimitViolations: 20, circuitBreakerTrips: 2 },
    { timestamp: '14:20', requests: 9100, errors: 43, responseTime: 142, rateLimitViolations: 18, circuitBreakerTrips: 1 },
    { timestamp: '14:25', requests: 9800, errors: 55, responseTime: 138, rateLimitViolations: 10, circuitBreakerTrips: 0 },
    { timestamp: '14:30', requests: 10200, errors: 48, responseTime: 145, rateLimitViolations: 14, circuitBreakerTrips: 0 },
  ];

  useEffect(() => {
    setRateLimitRules(mockRateLimitRules);
    setCircuitBreakers(mockCircuitBreakers);
    setSecurityEvents(mockSecurityEvents);
    setMetricsData(mockMetricsData);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getSeverityChip = (severity: string) => {
    const severityConfig = {
      LOW: { label: 'منخفض', color: 'info' as const },
      MEDIUM: { label: 'متوسط', color: 'warning' as const },
      HIGH: { label: 'عالي', color: 'error' as const },
      CRITICAL: { label: 'حرج', color: 'error' as const },
    };

    const config = severityConfig[severity as keyof typeof severityConfig] || { 
      label: severity, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const getCircuitBreakerStateChip = (state: string) => {
    const stateConfig = {
      CLOSED: { label: 'مغلق', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      OPEN: { label: 'مفتوح', color: 'error' as const, icon: <ErrorIcon fontSize="small" /> },
      HALF_OPEN: { label: 'نصف مفتوح', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
    };

    const config = stateConfig[state as keyof typeof stateConfig] || { 
      label: state, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const rateLimitColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم القاعدة',
      width: 150,
    },
    {
      field: 'path',
      headerName: 'المسار',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'method',
      headerName: 'الطريقة',
      width: 100,
    },
    {
      field: 'limit',
      headerName: 'الحد',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value}/{params.row.window}ث
        </Typography>
      ),
    },
    {
      field: 'violations',
      headerName: 'المخالفات',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color={params.value > 0 ? 'error.main' : 'text.primary'}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Switch checked={params.value} size="small" />
      ),
    },
  ];

  const circuitBreakerColumns: GridColDef[] = [
    {
      field: 'service',
      headerName: 'الخدمة',
      width: 150,
    },
    {
      field: 'state',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getCircuitBreakerStateChip(params.value),
    },
    {
      field: 'failures',
      headerName: 'الإخفاقات',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value}/{params.row.failureThreshold}
        </Typography>
      ),
    },
    {
      field: 'timeout',
      headerName: 'المهلة الزمنية',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value / 1000}ث
        </Typography>
      ),
    },
    {
      field: 'enabled',
      headerName: 'مفعل',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Switch checked={params.value} size="small" />
      ),
    },
  ];

  const securityEventColumns: GridColDef[] = [
    {
      field: 'type',
      headerName: 'نوع الحدث',
      width: 180,
      renderCell: (params: GridRenderCellParams) => {
        const typeLabels = {
          'RATE_LIMIT_EXCEEDED': 'تجاوز حد المعدل',
          'SUSPICIOUS_ACTIVITY': 'نشاط مشبوه',
          'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
          'DDoS_ATTEMPT': 'محاولة DDoS',
        };
        return typeLabels[params.value as keyof typeof typeLabels] || params.value;
      },
    },
    {
      field: 'source',
      headerName: 'المصدر',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'severity',
      headerName: 'الخطورة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getSeverityChip(params.value),
    },
    {
      field: 'blocked',
      headerName: 'محجوب',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'نعم' : 'لا'}
          color={params.value ? 'success' : 'error'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'timestamp',
      headerName: 'الوقت',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleString('ar-SA'),
    },
  ];

  // Calculate stats
  const totalViolations = rateLimitRules.reduce((sum, rule) => sum + rule.violations, 0);
  const openCircuitBreakers = circuitBreakers.filter(cb => cb.state === 'OPEN').length;
  const criticalEvents = securityEvents.filter(event => event.severity === 'CRITICAL').length;
  const blockedEvents = securityEvents.filter(event => event.blocked).length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          المراقبة والأمان المتقدم
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة Rate Limiting وCircuit Breaker والأمان
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <SpeedIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalViolations}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    مخالفات المعدل
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'error.main' }}>
                  <ShieldIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {openCircuitBreakers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Circuit Breakers مفتوحة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'error.main' }}>
                  <SecurityIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {criticalEvents}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    أحداث حرجة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <BlockIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {blockedEvents}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    أحداث محجوبة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<SpeedIcon />} label="Rate Limiting" iconPosition="start" />
            <Tab icon={<ShieldIcon />} label="Circuit Breakers" iconPosition="start" />
            <Tab icon={<SecurityIcon />} label="أحداث الأمان" iconPosition="start" />
            <Tab icon={<AssessmentIcon />} label="المقاييس" iconPosition="start" />
          </Tabs>
        </Box>

        {/* Rate Limiting Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">قواعد Rate Limiting</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenRuleDialog(true)}
              >
                إضافة قاعدة
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={rateLimitRules}
                columns={rateLimitColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Circuit Breakers Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>تكوين Circuit Breakers</Typography>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={circuitBreakers}
                columns={circuitBreakerColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Security Events Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">أحداث الأمان</Typography>
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>الخطورة</InputLabel>
                <Select
                  value={filterSeverity}
                  label="الخطورة"
                  onChange={(e) => setFilterSeverity(e.target.value)}
                >
                  <MenuItem value="ALL">جميع المستويات</MenuItem>
                  <MenuItem value="LOW">منخفض</MenuItem>
                  <MenuItem value="MEDIUM">متوسط</MenuItem>
                  <MenuItem value="HIGH">عالي</MenuItem>
                  <MenuItem value="CRITICAL">حرج</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={securityEvents}
                columns={securityEventColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Metrics Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>مقاييس الأمان والأداء</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      مقاييس الأمان في الوقت الفعلي
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={metricsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="timestamp" />
                        <YAxis />
                        <RechartsTooltip />
                        <Line 
                          type="monotone" 
                          dataKey="rateLimitViolations" 
                          stroke="#ff9800" 
                          strokeWidth={3}
                          name="مخالفات المعدل"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="circuitBreakerTrips" 
                          stroke="#f44336" 
                          strokeWidth={3}
                          name="Circuit Breaker Trips"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="errors" 
                          stroke="#9c27b0" 
                          strokeWidth={2}
                          name="الأخطاء"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default AdvancedMonitoring;
