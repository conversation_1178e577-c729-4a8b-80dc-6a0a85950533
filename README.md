# 🚗 TecnoDrive Platform - منصة تكنو درايف

## نظرة عامة
منصة TecnoDrive هي نظام شامل لإدارة النقل والتوصيل يدعم:
- **خدمات النقل**: حجز الرحلات وتتبع المركبات
- **إدارة الأسطول**: مراقبة وصيانة المركبات
- **نظام الطرود**: توصيل وتتبع الطرود
- **إدارة المدفوعات**: محافظ رقمية ومعالجة المدفوعات
- **تحليلات متقدمة**: ذكاء اصطناعي وتحليل البيانات
- **نظام SaaS**: دعم متعدد المستأجرين

## 🏗️ هيكل المشروع

```
tecno-drive-platform/
├── backend/                    # الخدمات الخلفية
│   ├── microservices/         # الخدمات المصغرة
│   │   ├── core/              # خدمات أساسية (User, Auth, Payment, Ride)
│   │   ├── business/          # خدمات الأعمال (Fleet, Location, Analytics)
│   │   └── infrastructure/    # خدمات البنية التحتية (Gateway, Eureka)
│   ├── shared/                # مكتبات مشتركة
│   ├── comprehensive-system/  # النظام الشامل (Python FastAPI)
│   └── api-docs/             # وثائق API
├── frontend/                  # الواجهات الأمامية
│   ├── admin-dashboard/       # لوحة تحكم الإدارة
│   ├── user-apps/            # تطبيقات المستخدمين
│   │   ├── driver-app/       # تطبيق السائقين
│   │   └── passenger-app/    # تطبيق الركاب
│   └── shared-components/    # مكونات مشتركة
├── infrastructure/           # البنية التحتية
│   ├── docker/              # ملفات Docker
│   ├── kubernetes/          # ملفات Kubernetes
│   ├── monitoring/          # مراقبة النظام
│   └── database/           # قواعد البيانات
├── tools/                   # الأدوات
│   ├── scripts/            # سكريبتات التشغيل
│   ├── generators/         # مولدات البيانات
│   └── testing/           # أدوات الاختبار
├── docs/                   # الوثائق
└── config/                # ملفات التكوين
```

## 🚀 البدء السريع

### المتطلبات الأساسية
- **Java 17+** (للخدمات المصغرة)
- **Node.js 18+** (للواجهات الأمامية)
- **Python 3.12+** (للنظام الشامل)
- **PostgreSQL 15+** (قاعدة البيانات)
- **Redis 7+** (التخزين المؤقت)
- **Docker & Docker Compose** (للنشر)

### 1. تشغيل الخدمات الأساسية

```powershell
# تشغيل قاعدة البيانات والتخزين المؤقت
cd infrastructure/docker
docker-compose up -d postgres redis

# تشغيل الخدمات المصغرة
cd ../../tools/scripts
./start-microservices.ps1
```

### 2. تشغيل النظام الشامل

```powershell
# إعداد البيئة الافتراضية
cd backend/comprehensive-system
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements-basic.txt

# تشغيل النظام
python main.py
```

### 3. تشغيل لوحة التحكم

```powershell
cd frontend/admin-dashboard
npm install
npm start
```

## 📊 الخدمات والمنافذ

| الخدمة | المنفذ | الوصف |
|--------|--------|-------|
| Eureka Server | 8761 | خدمة اكتشاف الخدمات |
| API Gateway | 8080 | بوابة API الرئيسية |
| User Service | 8083 | إدارة المستخدمين |
| Auth Service | 8081 | المصادقة والتفويض |
| Ride Service | 8082 | إدارة الرحلات |
| Fleet Service | 8084 | إدارة الأسطول |
| Location Service | 8085 | خدمات الموقع |
| Payment Service | 8086 | المدفوعات |
| Parcel Service | 8087 | إدارة الطرود |
| Notification Service | 8088 | الإشعارات |
| Analytics Service | 8089 | التحليلات |
| HR Service | 8090 | الموارد البشرية |
| Financial Service | 8091 | الخدمات المالية |
| SaaS Management | 8092 | إدارة SaaS |
| Admin Dashboard | 3000 | لوحة تحكم الإدارة |
| Comprehensive System | 8000 | النظام الشامل |

## 🔧 التكوين

### متغيرات البيئة
```env
# قاعدة البيانات
DB_HOST=localhost
DB_PORT=5433
DB_NAME=tecnodrive
DB_USER=tecnodrive_user
DB_PASSWORD=tecnodrive_secure_2024

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=tecnodrive_redis_2024

# JWT
JWT_SECRET=TecnoDriveSecretKey2024
JWT_EXPIRATION=86400

# API Gateway
GATEWAY_PORT=8080
EUREKA_URL=http://localhost:8761/eureka
```

## 📚 الوثائق

- [دليل النشر](docs/DEPLOYMENT.md)
- [هيكل المشروع](docs/PROJECT_STRUCTURE_FINAL.md)
- [دليل التطوير](docs/development/)
- [وثائق API](backend/api-docs/)
- [دليل الاستكشاف](docs/deployment/TROUBLESHOOTING_GUIDE.md)

## 🧪 الاختبار

```powershell
# اختبار الخدمات
cd tools/testing
./test-complete-system.ps1

# اختبار التكامل
./test-final-integration.ps1
```

## 🐳 النشر باستخدام Docker

```powershell
# النشر الكامل
cd infrastructure/docker
docker-compose -f docker-compose-comprehensive.yml up -d

# مراقبة الحالة
docker-compose ps
```

## 🔒 الأمان

- **مصادقة JWT** لجميع الخدمات
- **تشفير كلمات المرور** باستخدام bcrypt
- **HTTPS** في بيئة الإنتاج
- **Rate Limiting** في API Gateway
- **مراجعة الأمان** المستمرة

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs/](docs/)
- **المشاكل**: [GitHub Issues](https://github.com/tecnodrive/platform/issues)

---

**TecnoDrive Platform** - منصة النقل الذكي للمستقبل 🚗✨
