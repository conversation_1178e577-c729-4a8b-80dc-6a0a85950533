package com.tecnodrive.locationservice.controller;

import com.tecnodrive.locationservice.websocket.LocationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * WebSocket Analytics and Monitoring Controller
 */
@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketAnalyticsController {

    private static final Logger log = LoggerFactory.getLogger(WebSocketAnalyticsController.class);

    @Autowired
    private LocationWebSocketHandler webSocketHandler;

    /**
     * Get WebSocket session analytics
     */
    @GetMapping("/analytics")
    public ResponseEntity<Map<String, Object>> getSessionAnalytics() {
        try {
            Map<String, Object> analytics = webSocketHandler.getAdvancedSessionAnalytics();

            return ResponseEntity.ok(Map.of(
                "success", true,
                "analytics", analytics,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting session analytics: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get analytics: " + e.getMessage()
            ));
        }
    }

    /**
     * Get WebSocket performance metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = webSocketHandler.getPerformanceMetrics();

            return ResponseEntity.ok(Map.of(
                "success", true,
                "metrics", metrics,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting performance metrics: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get metrics: " + e.getMessage()
            ));
        }
    }

    /**
     * Get active sessions information
     */
    @GetMapping("/sessions")
    public ResponseEntity<Map<String, Object>> getActiveSessions() {
        try {
            Map<String, Object> sessionInfo = Map.of(
                "totalSessions", webSocketHandler.getActiveSessions().size(),
                "sessionSubscriptions", webSocketHandler.getSessionSubscriptions(),
                "activeConnections", webSocketHandler.getActiveSessions().stream()
                    .mapToLong(session -> session.isOpen() ? 1 : 0).sum()
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "sessions", sessionInfo,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting active sessions: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get sessions: " + e.getMessage()
            ));
        }
    }

    /**
     * Send filtered broadcast message
     */
    @PostMapping("/broadcast/filtered")
    public ResponseEntity<Map<String, Object>> sendFilteredBroadcast(@RequestBody Map<String, Object> request) {
        try {
            String messageType = (String) request.get("messageType");
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) request.get("data");
            @SuppressWarnings("unchecked")
            Map<String, Object> filters = (Map<String, Object>) request.getOrDefault("filters", Map.of());

            webSocketHandler.broadcastFilteredMessage(messageType, data, filters);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Filtered broadcast sent successfully",
                "messageType", messageType,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error sending filtered broadcast: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to send broadcast: " + e.getMessage()
            ));
        }
    }

    /**
     * Send batch messages
     */
    @PostMapping("/broadcast/batch")
    public ResponseEntity<Map<String, Object>> sendBatchBroadcast(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> messages = (List<Map<String, Object>>) request.get("messages");

            webSocketHandler.broadcastBatchMessages(messages);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Batch broadcast sent successfully",
                "messageCount", messages.size(),
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error sending batch broadcast: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to send batch: " + e.getMessage()
            ));
        }
    }

    /**
     * Send emergency broadcast
     */
    @PostMapping("/broadcast/emergency")
    public ResponseEntity<Map<String, Object>> sendEmergencyBroadcast(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> emergencyData = (Map<String, Object>) request.get("emergencyData");

            webSocketHandler.broadcastEmergency(emergencyData);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Emergency broadcast sent to all sessions",
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error sending emergency broadcast: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to send emergency: " + e.getMessage()
            ));
        }
    }

    /**
     * Test WebSocket connectivity
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testWebSocketConnectivity(@RequestBody Map<String, Object> request) {
        try {
            String testMessage = (String) request.getOrDefault("message", "WebSocket connectivity test");
            
            Map<String, Object> testData = Map.of(
                "type", "connectivity_test",
                "message", testMessage,
                "timestamp", System.currentTimeMillis()
            );

            webSocketHandler.broadcastAlert(testData);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Test message sent successfully",
                "activeSessions", webSocketHandler.getActiveSessions().size(),
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error testing WebSocket connectivity: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to test connectivity: " + e.getMessage()
            ));
        }
    }

    /**
     * Get WebSocket health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getWebSocketHealth() {
        try {
            long activeSessions = webSocketHandler.getActiveSessions().size();
            long healthySessions = webSocketHandler.getActiveSessions().stream()
                .mapToLong(session -> session.isOpen() ? 1 : 0).sum();

            String status = "healthy";
            if (activeSessions == 0) {
                status = "no_connections";
            } else if (healthySessions < activeSessions * 0.8) {
                status = "degraded";
            }

            Map<String, Object> health = Map.of(
                "status", status,
                "activeSessions", activeSessions,
                "healthySessions", healthySessions,
                "healthPercentage", activeSessions > 0 ? (healthySessions * 100.0 / activeSessions) : 100,
                "uptime", System.currentTimeMillis() - webSocketHandler.getStartTime()
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "health", health,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting WebSocket health: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get health status: " + e.getMessage()
            ));
        }
    }

    /**
     * Get subscription statistics
     */
    @GetMapping("/subscriptions/stats")
    public ResponseEntity<Map<String, Object>> getSubscriptionStats() {
        try {
            Map<String, Object> analytics = webSocketHandler.getAdvancedSessionAnalytics();
            
            Map<String, Object> subscriptionStats = Map.of(
                "totalSubscriptions", analytics.get("subscriptionCounts"),
                "topSubscriptions", analytics.get("topSubscriptions"),
                "sessionsByType", analytics.get("sessionsByType"),
                "averageSubscriptionsPerSession", calculateAverageSubscriptions()
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "subscriptionStats", subscriptionStats,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting subscription stats: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get subscription stats: " + e.getMessage()
            ));
        }
    }

    /**
     * Calculate average subscriptions per session
     */
    private double calculateAverageSubscriptions() {
        Map<String, ?> sessionSubscriptions = webSocketHandler.getSessionSubscriptions();
        if (sessionSubscriptions.isEmpty()) {
            return 0.0;
        }

        int totalSubscriptions = sessionSubscriptions.values().stream()
            .mapToInt(subscriptions -> ((java.util.Set<?>) subscriptions).size())
            .sum();

        return (double) totalSubscriptions / sessionSubscriptions.size();
    }
}
