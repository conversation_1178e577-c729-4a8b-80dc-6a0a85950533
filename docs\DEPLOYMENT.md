# دليل نشر منصة TECNO DRIVE

## نظرة عامة
هذا الدليل يوضح كيفية نشر منصة TECNO DRIVE الكاملة باستخدام Docker Compose أو Kubernetes.

## متطلبات النظام

### الحد الأدنى للمتطلبات
- **المعالج**: 4 cores
- **الذاكرة**: 8 GB RAM
- **التخزين**: 50 GB مساحة فارغة
- **نظام التشغيل**: Linux/macOS/Windows

### البرامج المطلوبة
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (للتطبيقات الأمامية)
- Java 17+ (للخدمات الخلفية)
- Git

## النشر السريع باستخدام Docker Compose

### 1. استنساخ المشروع
```bash
git clone https://github.com/tecno-drive/platform.git
cd tecno-drive
```

### 2. إعداد متغيرات البيئة
```bash
# نسخ ملف البيئة النموذجي
cp .env.example .env

# تحرير المتغيرات حسب الحاجة
nano .env
```

### 3. بناء وتشغيل الخدمات
```bash
# بناء جميع الخدمات
docker-compose build

# تشغيل النظام الكامل
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

### 4. التحقق من حالة الخدمات
```bash
# فحص حالة الحاويات
docker-compose ps

# فحص صحة الخدمات
curl http://localhost:8080/actuator/health
```

## الوصول للخدمات

### الخدمات الأساسية
- **API Gateway**: http://localhost:8080
- **Eureka Dashboard**: http://localhost:8761
- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090

### قواعد البيانات
- **PostgreSQL**: localhost:5432 (postgres/postgres)
- **Redis**: localhost:6379
- **Kafka**: localhost:9092

### التطبيقات
- **Admin Portal**: http://localhost:3001
- **Employee Portal**: http://localhost:3002

## اختبار النظام

### 1. اختبار خدمة المصادقة
```bash
# تسجيل مستخدم جديد
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+967771234567",
    "password": "SecurePass123!",
    "confirmPassword": "SecurePass123!",
    "type": "PASSENGER",
    "acceptTerms": true
  }'

# تسجيل الدخول
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

### 2. اختبار خدمة الرحلات
```bash
# إنشاء طلب رحلة (يتطلب token)
curl -X POST http://localhost:8080/api/rides/request \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "pickupLatitude": 15.3694,
    "pickupLongitude": 44.1910,
    "destinationLatitude": 15.3794,
    "destinationLongitude": 44.2010,
    "rideType": "STANDARD",
    "passengerCount": 1
  }'
```

### 3. اختبار الإشعارات
```bash
# إرسال إشعار تجريبي
curl -X POST http://localhost:8080/api/notifications/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "userId": "USER_ID",
    "title": "إشعار تجريبي",
    "message": "هذا إشعار تجريبي من منصة تكنو درايف",
    "type": "INFO"
  }'
```

## النشر على Kubernetes

### 1. إعداد Kubernetes
```bash
# تطبيق ملفات Kubernetes
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmaps/
kubectl apply -f k8s/secrets/
kubectl apply -f k8s/services/
kubectl apply -f k8s/deployments/
```

### 2. مراقبة النشر
```bash
# فحص حالة الـ pods
kubectl get pods -n tecno-drive

# فحص الخدمات
kubectl get services -n tecno-drive

# مراقبة السجلات
kubectl logs -f deployment/auth-service -n tecno-drive
```

## المراقبة والصيانة

### 1. مراقبة الأداء
- **Grafana Dashboards**: http://localhost:3000
  - TECNO DRIVE Overview
  - Microservices Health
  - Database Performance
  - Redis Metrics

### 2. فحص السجلات
```bash
# سجلات جميع الخدمات
docker-compose logs

# سجلات خدمة محددة
docker-compose logs auth-service

# متابعة السجلات المباشرة
docker-compose logs -f ride-service
```

### 3. النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
docker exec tecno-drive-postgres pg_dumpall -U postgres > backup.sql

# نسخ احتياطي لـ Redis
docker exec tecno-drive-redis-master redis-cli -a tecnodrive2024 --rdb /data/backup.rdb
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل في الاتصال بقاعدة البيانات
```bash
# فحص حالة PostgreSQL
docker-compose logs postgres

# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres
```

#### 2. مشاكل في خدمة الاكتشاف
```bash
# فحص Eureka Server
curl http://localhost:8761/eureka/apps

# إعادة تسجيل الخدمات
docker-compose restart auth-service ride-service
```

#### 3. مشاكل في الذاكرة
```bash
# فحص استخدام الذاكرة
docker stats

# زيادة حدود الذاكرة في docker-compose.yml
# أضف تحت كل خدمة:
# deploy:
#   resources:
#     limits:
#       memory: 1G
```

## التحديث والصيانة

### 1. تحديث الخدمات
```bash
# سحب آخر التحديثات
git pull origin main

# إعادة بناء الخدمات المحدثة
docker-compose build --no-cache

# إعادة تشغيل الخدمات
docker-compose up -d
```

### 2. تنظيف النظام
```bash
# إزالة الحاويات المتوقفة
docker container prune

# إزالة الصور غير المستخدمة
docker image prune

# إزالة الشبكات غير المستخدمة
docker network prune
```

## الأمان

### 1. تغيير كلمات المرور الافتراضية
```bash
# تحديث كلمات مرور قواعد البيانات
# في ملف .env
POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
```

### 2. تفعيل HTTPS
```bash
# إضافة شهادات SSL في
# infra/config/ssl/
# وتحديث nginx.conf
```

### 3. تقييد الوصول للشبكة
```bash
# تحديث docker-compose.yml لإزالة المنافذ غير الضرورية
# واستخدام شبكة داخلية فقط
```

## الدعم الفني

للحصول على المساعدة:
1. راجع ملف [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
2. تحقق من [Issues](https://github.com/tecno-drive/platform/issues)
3. اتصل بفريق التطوير: <EMAIL>

---

**ملاحظة**: هذا الدليل يغطي النشر الأساسي. للنشر في بيئة الإنتاج، يرجى مراجعة [دليل الإنتاج](PRODUCTION.md).
