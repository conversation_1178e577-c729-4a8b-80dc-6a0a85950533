/// <reference types="react-scripts" />

// Suppress TypeScript library errors
declare module 'aria-query' {
  const content: any;
  export = content;
}

declare module 'babel__core' {
  const content: any;
  export = content;
}

declare module 'babel__generator' {
  const content: any;
  export = content;
}

declare module 'babel__template' {
  const content: any;
  export = content;
}

declare module 'babel__traverse' {
  const content: any;
  export = content;
}

declare module 'body-parser' {
  const content: any;
  export = content;
}

declare module 'bonjour' {
  const content: any;
  export = content;
}

declare module 'connect' {
  const content: any;
  export = content;
}

declare module 'connect-history-api-fallback' {
  const content: any;
  export = content;
}

declare module 'd3-array' {
  const content: any;
  export = content;
}

declare module 'd3-color' {
  const content: any;
  export = content;
}

declare module 'd3-delaunay' {
  const content: any;
  export = content;
}

declare module 'd3-interpolate' {
  const content: any;
  export = content;
}

declare module 'd3-path' {
  const content: any;
  export = content;
}

declare module 'd3-scale' {
  const content: any;
  export = content;
}

declare module 'd3-shape' {
  const content: any;
  export = content;
}

declare module 'd3-time' {
  const content: any;
  export = content;
}

declare module 'd3-timer' {
  const content: any;
  export = content;
}

declare module 'file-saver' {
  const content: any;
  export = content;
}

declare module 'google.maps' {
  const content: any;
  export = content;
}

declare module 'html-minifier-terser' {
  const content: any;
  export = content;
}

declare module 'http-errors' {
  const content: any;
  export = content;
}

declare module 'http-proxy' {
  const content: any;
  export = content;
}

declare module 'jest' {
  const content: any;
  export = content;
}

declare module 'json5' {
  const content: any;
  export = content;
}

declare module 'mime' {
  const content: any;
  export = content;
}

declare module 'node' {
  const content: any;
  export = content;
}

declare module 'parse-json' {
  const content: any;
  export = content;
}

declare module 'prettier' {
  const content: any;
  export = content;
}

declare module 'prop-types' {
  const content: any;
  export = content;
}

declare module 'range-parser' {
  const content: any;
  export = content;
}

declare module 'retry' {
  const content: any;
  export = content;
}

declare module 'semver' {
  const content: any;
  export = content;
}

declare module 'send' {
  const content: any;
  export = content;
}

declare module 'serve-static' {
  const content: any;
  export = content;
}

declare module 'use-sync-external-store' {
  const content: any;
  export = content;
}

declare module 'ws' {
  const content: any;
  export = content;
}

declare module 'yargs' {
  const content: any;
  export = content;
}
