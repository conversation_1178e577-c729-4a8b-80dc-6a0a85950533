package com.tecnodrive.walletservice.controller;

import com.tecnodrive.walletservice.dto.WalletDTO;
import com.tecnodrive.walletservice.entity.Wallet;
import com.tecnodrive.walletservice.entity.WalletTransaction;
import com.tecnodrive.walletservice.service.WalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Wallet Management Controller
 */
@RestController
@RequestMapping("/api/wallets")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Wallet Management", description = "Digital wallet management operations")
@CrossOrigin(origins = "*")
public class WalletController {

    private final WalletService walletService;

    /**
     * Create a new wallet
     */
    @PostMapping
    @Operation(summary = "Create a new wallet", description = "Create a digital wallet for a user")
    public ResponseEntity<Map<String, Object>> createWallet(@RequestBody CreateWalletRequest request) {
        try {
            WalletDTO wallet = walletService.createWallet(
                    request.getUserId(),
                    request.getPhoneNumber(),
                    request.getCreatedBy()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of(
                    "success", true,
                    "message", "Wallet created successfully",
                    "data", wallet
            ));
        } catch (Exception e) {
            log.error("Error creating wallet: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to create wallet: " + e.getMessage()
            ));
        }
    }

    /**
     * Get wallet by ID
     */
    @GetMapping("/{walletId}")
    @Operation(summary = "Get wallet by ID", description = "Retrieve wallet information by wallet ID")
    public ResponseEntity<Map<String, Object>> getWalletById(
            @Parameter(description = "Wallet ID") @PathVariable UUID walletId) {
        try {
            return walletService.getWalletById(walletId)
                    .map(wallet -> ResponseEntity.ok(Map.of(
                            "success", true,
                            "data", wallet
                    )))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting wallet: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get wallet: " + e.getMessage()
            ));
        }
    }

    /**
     * Get wallet by user ID
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get wallet by user ID", description = "Retrieve wallet information by user ID")
    public ResponseEntity<Map<String, Object>> getWalletByUserId(
            @Parameter(description = "User ID") @PathVariable UUID userId) {
        try {
            return walletService.getWalletByUserId(userId)
                    .map(wallet -> ResponseEntity.ok(Map.of(
                            "success", true,
                            "data", wallet
                    )))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting wallet by user ID: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get wallet: " + e.getMessage()
            ));
        }
    }

    /**
     * Get wallet by phone number
     */
    @GetMapping("/phone/{phoneNumber}")
    @Operation(summary = "Get wallet by phone number", description = "Retrieve wallet information by phone number")
    public ResponseEntity<Map<String, Object>> getWalletByPhoneNumber(
            @Parameter(description = "Phone number") @PathVariable String phoneNumber) {
        try {
            return walletService.getWalletByPhoneNumber(phoneNumber)
                    .map(wallet -> ResponseEntity.ok(Map.of(
                            "success", true,
                            "data", wallet
                    )))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting wallet by phone: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get wallet: " + e.getMessage()
            ));
        }
    }

    /**
     * Get wallet balance
     */
    @GetMapping("/{walletId}/balance")
    @Operation(summary = "Get wallet balance", description = "Get current balance of a wallet")
    public ResponseEntity<Map<String, Object>> getWalletBalance(
            @Parameter(description = "Wallet ID") @PathVariable UUID walletId) {
        try {
            BigDecimal balance = walletService.getWalletBalance(walletId);
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", Map.of(
                            "walletId", walletId,
                            "balance", balance,
                            "currency", "SAR"
                    )
            ));
        } catch (Exception e) {
            log.error("Error getting wallet balance: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get balance: " + e.getMessage()
            ));
        }
    }

    /**
     * Update wallet status
     */
    @PutMapping("/{walletId}/status")
    @Operation(summary = "Update wallet status", description = "Update the status of a wallet")
    public ResponseEntity<Map<String, Object>> updateWalletStatus(
            @Parameter(description = "Wallet ID") @PathVariable UUID walletId,
            @RequestBody UpdateStatusRequest request) {
        try {
            WalletDTO wallet = walletService.updateWalletStatus(
                    walletId,
                    request.getStatus(),
                    request.getUpdatedBy()
            );

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Wallet status updated successfully",
                    "data", wallet
            ));
        } catch (Exception e) {
            log.error("Error updating wallet status: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to update status: " + e.getMessage()
            ));
        }
    }

    /**
     * Update wallet limits
     */
    @PutMapping("/{walletId}/limits")
    @Operation(summary = "Update wallet limits", description = "Update daily and monthly limits for a wallet")
    public ResponseEntity<Map<String, Object>> updateWalletLimits(
            @Parameter(description = "Wallet ID") @PathVariable UUID walletId,
            @RequestBody UpdateLimitsRequest request) {
        try {
            WalletDTO wallet = walletService.updateWalletLimits(
                    walletId,
                    request.getDailyLimit(),
                    request.getMonthlyLimit(),
                    request.getUpdatedBy()
            );

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Wallet limits updated successfully",
                    "data", wallet
            ));
        } catch (Exception e) {
            log.error("Error updating wallet limits: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to update limits: " + e.getMessage()
            ));
        }
    }

    /**
     * Add balance to wallet (Top-up)
     */
    @PostMapping("/{walletId}/topup")
    @Operation(summary = "Top-up wallet", description = "Add balance to a wallet")
    public ResponseEntity<Map<String, Object>> topupWallet(
            @Parameter(description = "Wallet ID") @PathVariable UUID walletId,
            @RequestBody TopupRequest request) {
        try {
            WalletDTO wallet = walletService.addBalance(
                    walletId,
                    request.getAmount(),
                    request.getSource(),
                    request.getDescription(),
                    request.getAgentId(),
                    request.getAgentName(),
                    request.getCreatedBy()
            );

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Wallet topped up successfully",
                    "data", wallet
            ));
        } catch (Exception e) {
            log.error("Error topping up wallet: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to top up wallet: " + e.getMessage()
            ));
        }
    }

    /**
     * Cash top-up by phone number
     */
    @PostMapping("/topup/cash")
    @Operation(summary = "Cash top-up by phone", description = "Add cash balance to wallet by phone number")
    public ResponseEntity<Map<String, Object>> cashTopupByPhone(@RequestBody CashTopupRequest request) {
        try {
            // Find wallet by phone number
            WalletDTO wallet = walletService.getWalletByPhoneNumber(request.getPhoneNumber())
                    .orElseThrow(() -> new IllegalArgumentException("Wallet not found for phone: " + request.getPhoneNumber()));

            // Add balance
            WalletDTO updatedWallet = walletService.addBalance(
                    wallet.getId(),
                    request.getAmount(),
                    WalletTransaction.TransactionSource.CASH_TOPUP,
                    request.getDescription(),
                    request.getAgentId(),
                    request.getAgentName(),
                    "AGENT_" + request.getAgentId()
            );

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Cash top-up completed successfully",
                    "data", updatedWallet
            ));
        } catch (Exception e) {
            log.error("Error processing cash top-up: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to process cash top-up: " + e.getMessage()
            ));
        }
    }

    /**
     * Get all wallets with pagination
     */
    @GetMapping
    @Operation(summary = "Get all wallets", description = "Retrieve all wallets with pagination")
    public ResponseEntity<Map<String, Object>> getAllWallets(Pageable pageable) {
        try {
            Page<WalletDTO> wallets = walletService.getAllWallets(pageable);
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", wallets.getContent(),
                    "pagination", Map.of(
                            "page", wallets.getNumber(),
                            "size", wallets.getSize(),
                            "totalElements", wallets.getTotalElements(),
                            "totalPages", wallets.getTotalPages()
                    )
            ));
        } catch (Exception e) {
            log.error("Error getting all wallets: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get wallets: " + e.getMessage()
            ));
        }
    }

    /**
     * Get active wallets
     */
    @GetMapping("/active")
    @Operation(summary = "Get active wallets", description = "Retrieve all active wallets")
    public ResponseEntity<Map<String, Object>> getActiveWallets() {
        try {
            List<WalletDTO> wallets = walletService.getActiveWallets();
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", wallets,
                    "count", wallets.size()
            ));
        } catch (Exception e) {
            log.error("Error getting active wallets: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to get active wallets: " + e.getMessage()
            ));
        }
    }

    /**
     * Search wallets
     */
    @GetMapping("/search")
    @Operation(summary = "Search wallets", description = "Search wallets by various criteria")
    public ResponseEntity<Map<String, Object>> searchWallets(
            @RequestParam(required = false) String phoneNumber,
            @RequestParam(required = false) Wallet.WalletStatus status,
            @RequestParam(required = false) Wallet.VerificationLevel verificationLevel,
            @RequestParam(required = false) BigDecimal minBalance,
            @RequestParam(required = false) BigDecimal maxBalance,
            Pageable pageable) {
        try {
            Page<WalletDTO> wallets = walletService.searchWallets(
                    phoneNumber, status, verificationLevel, minBalance, maxBalance, pageable
            );

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", wallets.getContent(),
                    "pagination", Map.of(
                            "page", wallets.getNumber(),
                            "size", wallets.getSize(),
                            "totalElements", wallets.getTotalElements(),
                            "totalPages", wallets.getTotalPages()
                    )
            ));
        } catch (Exception e) {
            log.error("Error searching wallets: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Failed to search wallets: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CreateWalletRequest {
        private UUID userId;
        private String phoneNumber;
        private String createdBy;
    }

    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UpdateStatusRequest {
        private Wallet.WalletStatus status;
        private String updatedBy;
    }

    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UpdateLimitsRequest {
        private BigDecimal dailyLimit;
        private BigDecimal monthlyLimit;
        private String updatedBy;
    }

    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TopupRequest {
        private BigDecimal amount;
        private WalletTransaction.TransactionSource source;
        private String description;
        private UUID agentId;
        private String agentName;
        private String createdBy;
    }

    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CashTopupRequest {
        private String phoneNumber;
        private BigDecimal amount;
        private String description;
        private UUID agentId;
        private String agentName;
    }
}
