# Docker Containers Setup for TecnoDrive Platform
Write-Host "🐳 Setting up Docker Containers for TecnoDrive Platform" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Gray

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        $null = docker version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

# Function to start Docker Desktop
function Start-DockerDesktop {
    Write-Host "🔄 Starting Docker Desktop..." -ForegroundColor Cyan
    
    $dockerPaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    foreach ($path in $dockerPaths) {
        if (Test-Path $path) {
            Write-Host "  📍 Found Docker Desktop at: $path" -ForegroundColor Yellow
            Start-Process -FilePath $path -WindowStyle Hidden
            
            # Wait for Docker to start
            $timeout = 120
            $elapsed = 0
            
            while (-not (Test-DockerRunning) -and $elapsed -lt $timeout) {
                Write-Host "  ⏳ Waiting for Docker... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
                Start-Sleep -Seconds 5
                $elapsed += 5
            }
            
            if (Test-DockerRunning) {
                Write-Host "  ✅ Docker Desktop is ready!" -ForegroundColor Green
                return $true
            }
        }
    }
    return $false
}

# Check Docker status
if (-not (Test-DockerRunning)) {
    Write-Host "⚠️  Docker is not running. Attempting to start..." -ForegroundColor Yellow
    if (-not (Start-DockerDesktop)) {
        Write-Host "❌ Could not start Docker Desktop automatically" -ForegroundColor Red
        Write-Host "💡 Please start Docker Desktop manually and run this script again" -ForegroundColor Cyan
        exit 1
    }
} else {
    Write-Host "✅ Docker is running" -ForegroundColor Green
}

# Create docker-compose file for real containers
Write-Host "`n📝 Creating Docker Compose configuration..." -ForegroundColor Cyan

$dockerComposeContent = @"
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tecnodrive-postgres
    environment:
      POSTGRES_DB: tecnodrive
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - tecnodrive-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tecnodrive-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tecnodrive-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Eureka Server
  eureka-server:
    image: steeltoeoss/eureka-server:latest
    container_name: tecnodrive-eureka
    ports:
      - "8761:8761"
    environment:
      - EUREKA_CLIENT_REGISTER_WITH_EUREKA=false
      - EUREKA_CLIENT_FETCH_REGISTRY=false
    networks:
      - tecnodrive-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # API Gateway (using nginx as proxy)
  api-gateway:
    image: nginx:alpine
    container_name: tecnodrive-gateway
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - tecnodrive-network
    depends_on:
      - eureka-server
    restart: unless-stopped

  # Auth Service (Spring Boot app simulation)
  auth-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-auth
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=tecnodrive
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - redis
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting Auth Service on port 8081...' &&
        while true; do
          echo 'Auth Service is running - $$(date)'
          sleep 30
        done
      "

  # User Service
  user-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-user
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
      - REDIS_HOST=redis
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - redis
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting User Service on port 8083...' &&
        while true; do
          echo 'User Service is running - $$(date)'
          sleep 30
        done
      "

  # Ride Service
  ride-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-ride
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting Ride Service on port 8082...' &&
        while true; do
          echo 'Ride Service is running - $$(date)'
          sleep 30
        done
      "

  # Fleet Service
  fleet-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-fleet
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting Fleet Service on port 8084...' &&
        while true; do
          echo 'Fleet Service is running - $$(date)'
          sleep 30
        done
      "

  # Location Service
  location-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-location
    ports:
      - "8085:8085"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting Location Service on port 8085...' &&
        while true; do
          echo 'Location Service is running - $$(date)'
          sleep 30
        done
      "

  # Payment Service
  payment-service:
    image: openjdk:17-jdk-slim
    container_name: tecnodrive-payment
    ports:
      - "8086:8086"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
      - eureka-server
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Starting Payment Service on port 8086...' &&
        while true; do
          echo 'Payment Service is running - $$(date)'
          sleep 30
        done
      "

networks:
  tecnodrive-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
"@

# Write docker-compose file
$dockerComposeContent | Out-File -FilePath "docker-compose.containers.yml" -Encoding UTF8

Write-Host "✅ Docker Compose file created: docker-compose.containers.yml" -ForegroundColor Green

# Create nginx configuration
Write-Host "`n📝 Creating Nginx configuration..." -ForegroundColor Cyan

$nginxConfig = @"
events {
    worker_connections 1024;
}

http {
    upstream eureka {
        server eureka-server:8761;
    }
    
    upstream auth {
        server auth-service:8081;
    }
    
    upstream user {
        server user-service:8083;
    }
    
    server {
        listen 80;
        
        location / {
            return 200 'TecnoDrive API Gateway - All services are running!';
            add_header Content-Type text/plain;
        }
        
        location /eureka/ {
            proxy_pass http://eureka/;
        }
        
        location /auth/ {
            proxy_pass http://auth/;
        }
        
        location /user/ {
            proxy_pass http://user/;
        }
        
        location /health {
            return 200 '{"status":"UP","gateway":"nginx","timestamp":"$(date)"}';
            add_header Content-Type application/json;
        }
    }
}
"@

$nginxConfig | Out-File -FilePath "nginx.conf" -Encoding UTF8
Write-Host "✅ Nginx configuration created: nginx.conf" -ForegroundColor Green

# Pull required Docker images
Write-Host "`n📥 Pulling Docker images..." -ForegroundColor Cyan

$images = @(
    "postgres:15-alpine",
    "redis:7-alpine", 
    "steeltoeoss/eureka-server:latest",
    "nginx:alpine",
    "openjdk:17-jdk-slim"
)

foreach ($image in $images) {
    Write-Host "  🔄 Pulling $image..." -ForegroundColor Yellow
    try {
        docker pull $image
        Write-Host "  ✅ $image pulled successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "  ❌ Failed to pull $image" -ForegroundColor Red
    }
}

# Start infrastructure services first
Write-Host "`n🚀 Starting infrastructure services..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose.containers.yml up -d postgres redis
    Write-Host "✅ Infrastructure services started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start infrastructure services" -ForegroundColor Red
}

Write-Host "⏳ Waiting for infrastructure to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Start Eureka server
Write-Host "`n🔧 Starting Eureka server..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose.containers.yml up -d eureka-server
    Write-Host "✅ Eureka server started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Eureka server" -ForegroundColor Red
}

Write-Host "⏳ Waiting for Eureka to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Start API Gateway
Write-Host "`n🌐 Starting API Gateway..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose.containers.yml up -d api-gateway
    Write-Host "✅ API Gateway started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start API Gateway" -ForegroundColor Red
}

# Start application services
Write-Host "`n🎯 Starting application services..." -ForegroundColor Cyan
$appServices = @("auth-service", "user-service", "ride-service", "fleet-service", "location-service", "payment-service")

foreach ($service in $appServices) {
    Write-Host "  🔄 Starting $service..." -ForegroundColor Yellow
    try {
        docker-compose -f docker-compose.containers.yml up -d $service
        Write-Host "  ✅ $service started" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to start $service" -ForegroundColor Red
    }
    Start-Sleep -Seconds 5
}

# Final status check
Write-Host "`n📊 Checking container status..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host "✅ Running containers:" -ForegroundColor Green
    Write-Host $containers -ForegroundColor White
} catch {
    Write-Host "❌ Error checking container status" -ForegroundColor Red
}

# Service URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • API Gateway:     http://localhost:8080" -ForegroundColor White
Write-Host "  • Eureka Server:   http://localhost:8761" -ForegroundColor White
Write-Host "  • Auth Service:    http://localhost:8081" -ForegroundColor White
Write-Host "  • User Service:    http://localhost:8083" -ForegroundColor White
Write-Host "  • Ride Service:    http://localhost:8082" -ForegroundColor White
Write-Host "  • Fleet Service:   http://localhost:8084" -ForegroundColor White
Write-Host "  • Location Service: http://localhost:8085" -ForegroundColor White
Write-Host "  • Payment Service: http://localhost:8086" -ForegroundColor White
Write-Host "  • PostgreSQL:      localhost:5432" -ForegroundColor White
Write-Host "  • Redis:           localhost:6379" -ForegroundColor White

Write-Host "`n💡 Management commands:" -ForegroundColor Cyan
Write-Host "  • Check status:    docker ps" -ForegroundColor White
Write-Host "  • View logs:       docker logs [container-name]" -ForegroundColor White
Write-Host "  • Stop all:        docker-compose -f docker-compose.containers.yml down" -ForegroundColor White
Write-Host "  • Restart service: docker-compose -f docker-compose.containers.yml restart [service]" -ForegroundColor White

Write-Host "`n🎉 Docker containers setup completed!" -ForegroundColor Green
