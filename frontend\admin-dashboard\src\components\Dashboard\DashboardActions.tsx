import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  But<PERSON>,
  Avatar,
  <PERSON>,
  IconButton,
  Toolt<PERSON>,
} from '@mui/material';
import {
  Add,
  DirectionsCar,
  People,
  LocalShipping,
  Analytics,
  Settings,
  Launch,
  Refresh,
  Map,
  Assessment,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const DashboardActions: React.FC = () => {
  const navigate = useNavigate();

  const primaryActions = [
    {
      title: 'إضافة رحلة جديدة',
      description: 'إنشاء رحلة جديدة في النظام',
      icon: <Add />,
      color: '#667eea',
      action: () => navigate('/rides/new'),
      badge: 'سريع',
    },
    {
      title: 'إدارة الأسطول',
      description: 'عرض وإدارة المركبات',
      icon: <DirectionsCar />,
      color: '#4299e1',
      action: () => navigate('/fleet'),
    },
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل المستخدمين',
      icon: <People />,
      color: '#48bb78',
      action: () => navigate('/users'),
    },
    {
      title: 'تتبع الطرود',
      description: 'مراقبة حالة الطرود',
      icon: <LocalShipping />,
      color: '#ed8936',
      action: () => navigate('/parcels'),
    },
  ];

  const secondaryActions = [
    {
      title: 'التحليلات',
      icon: <Analytics />,
      action: () => navigate('/analytics'),
    },
    {
      title: 'خريطة الأسطول',
      icon: <Map />,
      action: () => navigate('/fleet-map'),
    },
    {
      title: 'التقارير',
      icon: <Assessment />,
      action: () => navigate('/reports'),
    },
    {
      title: 'لوحة التحكم',
      icon: <Settings />,
      action: () => navigate('/control-panel'),
    },
  ];

  const externalLinks = [
    {
      title: 'Eureka Dashboard',
      url: 'http://localhost:8761',
      color: '#4299e1',
    },
    {
      title: 'HR System',
      url: 'http://localhost:3002',
      color: '#48bb78',
    },
    {
      title: 'Prometheus',
      url: 'http://localhost:9090',
      color: '#ed8936',
    },
    {
      title: 'Grafana',
      url: 'http://localhost:3001',
      color: '#9f7aea',
    },
  ];

  return (
    <Box>
      {/* Primary Actions */}
      <Card className="enhanced-card" sx={{ mb: 3 }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                mr: 2,
              }}
            >
              ⚡
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>
                إجراءات سريعة
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الوصول السريع للمهام الأساسية
              </Typography>
            </Box>
          </Box>

          <Grid container spacing={2}>
            {primaryActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  className="scale-in interactive-hover"
                  sx={{
                    cursor: 'pointer',
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    animationDelay: `${index * 0.1}s`,
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 24px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                  onClick={action.action}
                >
                  <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          background: `${action.color}20`,
                          color: action.color,
                        }}
                      >
                        {action.icon}
                      </Avatar>
                      {action.badge && (
                        <Chip
                          label={action.badge}
                          size="small"
                          sx={{
                            background: action.color,
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.7rem',
                          }}
                        />
                      )}
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1, textAlign: 'right' }}>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'right' }}>
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Secondary Actions & External Links */}
      <Grid container spacing={3}>
        {/* Secondary Actions */}
        <Grid item xs={12} md={6}>
          <Card className="enhanced-card">
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ background: '#48bb78', mr: 2 }}>
                  🔧
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  أدوات إضافية
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1.5 }}>
                {secondaryActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="contained"
                    startIcon={action.icon}
                    onClick={action.action}
                    sx={{
                      background: 'linear-gradient(135deg, #667eea, #764ba2)',
                      color: 'white',
                      fontWeight: 600,
                      borderRadius: 2,
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8, #6a4190)',
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    {action.title}
                  </Button>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* External Links */}
        <Grid item xs={12} md={6}>
          <Card className="enhanced-card">
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ background: '#4299e1', mr: 2 }}>
                  🔗
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  روابط خارجية
                </Typography>
              </Box>
              
              <Grid container spacing={1.5}>
                {externalLinks.map((link, index) => (
                  <Grid item xs={6} key={index}>
                    <Button
                      fullWidth
                      variant="outlined"
                      endIcon={<Launch />}
                      onClick={() => window.open(link.url, '_blank')}
                      sx={{
                        borderColor: link.color,
                        color: link.color,
                        fontWeight: 500,
                        borderRadius: 2,
                        '&:hover': {
                          background: `${link.color}10`,
                          borderColor: link.color,
                          transform: 'translateY(-1px)',
                        },
                      }}
                    >
                      {link.title}
                    </Button>
                  </Grid>
                ))}
              </Grid>

              <Box sx={{ mt: 3, p: 2, borderRadius: 2, background: 'rgba(66, 153, 225, 0.1)' }}>
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                  💡 نصيحة: استخدم Ctrl+Click لفتح الروابط في تبويب جديد
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardActions;
