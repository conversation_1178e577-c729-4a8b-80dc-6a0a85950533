// Mock Data Manager
// This utility manages mock data when real services are not available

export const MOCK_DATA_ENABLED = process.env.REACT_APP_ENABLE_MOCK_DATA === 'true';

// Mock Users
export const mockUsers = [
  {
    id: '1',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+967771234567',
    userType: 'PASSENGER' as const,
    status: 'ACTIVE' as const,
    rating: 4.8,
    totalRides: 25,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'علي أحمد',
    email: '<EMAIL>',
    phone: '+967771234568',
    userType: 'DRIVER' as const,
    status: 'ACTIVE' as const,
    rating: 4.9,
    totalRides: 150,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'فاطمة علي',
    email: '<EMAIL>',
    phone: '+967771234569',
    userType: 'PASSENGER' as const,
    status: 'ACTIVE' as const,
    rating: 4.7,
    totalRides: 12,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock Vehicles
export const mockVehicles = [
  {
    id: '1',
    plateNumber: 'أ ب ج 123',
    make: 'تويوتا',
    model: 'كامري',
    year: 2022,
    color: 'أبيض',
    status: 'ACTIVE' as const,
    driverId: '2',
    driverName: 'علي أحمد',
    lastLocation: { latitude: 15.3694, longitude: 44.1910, timestamp: new Date().toISOString() },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    plateNumber: 'د هـ و 456',
    make: 'نيسان',
    model: 'التيما',
    year: 2021,
    color: 'أسود',
    status: 'MAINTENANCE' as const,
    driverId: undefined,
    driverName: undefined,
    lastLocation: undefined,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock Rides
export const mockRides = [
  {
    id: '1',
    passengerId: '1',
    driverId: '2',
    pickupLocation: {
      latitude: 15.3694,
      longitude: 44.1910,
      address: 'شارع الزبيري',
    },
    destination: {
      latitude: 15.3500,
      longitude: 44.2000,
      address: 'جامعة صنعاء',
    },
    status: 'COMPLETED' as const,
    fare: 150,
    distance: 5.2,
    duration: 15,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    passengerId: '3',
    driverId: '2',
    pickupLocation: {
      latitude: 15.3500,
      longitude: 44.2000,
      address: 'السبعين',
    },
    destination: {
      latitude: 15.3600,
      longitude: 44.2100,
      address: 'شارع هائل',
    },
    status: 'IN_PROGRESS' as const,
    fare: 200,
    distance: 8.1,
    duration: 25,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock Payments
export const mockPayments = [
  {
    id: '1',
    rideId: '1',
    amount: 150,
    currency: 'ريال',
    method: 'CASH' as const,
    status: 'COMPLETED' as const,
    transactionId: 'TXN-001',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    rideId: '2',
    amount: 200,
    currency: 'ريال',
    method: 'CARD' as const,
    status: 'PENDING' as const,
    transactionId: 'TXN-002',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock Analytics Data
export const mockAnalyticsData = {
  totalRides: 1250,
  totalRevenue: 45000,
  activeDrivers: 85,
  activePassengers: 320,
  averageRating: 4.8,
  ridesGrowth: 12.5,
  revenueGrowth: 8.3,
  dailyRides: [
    { date: '2024-01-01', count: 45 },
    { date: '2024-01-02', count: 52 },
    { date: '2024-01-03', count: 38 },
    { date: '2024-01-04', count: 61 },
    { date: '2024-01-05', count: 55 },
    { date: '2024-01-06', count: 67 },
    { date: '2024-01-07', count: 43 },
  ],
  monthlyRevenue: [
    { month: 'يناير', revenue: 25000 },
    { month: 'فبراير', revenue: 28000 },
    { month: 'مارس', revenue: 32000 },
    { month: 'أبريل', revenue: 35000 },
    { month: 'مايو', revenue: 38000 },
    { month: 'يونيو', revenue: 42000 },
  ],
  topRoutes: [
    { route: 'الزبيري - جامعة صنعاء', count: 45 },
    { route: 'السبعين - شارع هائل', count: 38 },
    { route: 'الحصبة - المطار', count: 32 },
    { route: 'الستين - التحرير', count: 28 },
    { route: 'الثورة - الزراعة', count: 25 },
  ],
  driverPerformance: [
    { driverId: '2', name: 'علي أحمد', rating: 4.9, totalRides: 150 },
    { driverId: '4', name: 'محمد سالم', rating: 4.7, totalRides: 120 },
    { driverId: '5', name: 'سعد محمد', rating: 4.8, totalRides: 135 },
  ],
};

// Utility functions
export const createMockResponse = <T>(data: T, success: boolean = true, message?: string) => {
  return {
    success,
    message,
    data,
    total: Array.isArray(data) ? data.length : undefined,
  };
};

export const simulateApiDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const generateMockId = () => {
  return Math.random().toString(36).substr(2, 9);
};

// Mock API responses
export const mockApiResponses = {
  // Rides
  createRide: async (rideData: any) => {
    await simulateApiDelay();
    const newRide = {
      id: generateMockId(),
      ...rideData,
      status: 'REQUESTED',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return createMockResponse(newRide);
  },

  updateRideStatus: async (rideId: string, status: string) => {
    await simulateApiDelay();
    const ride = mockRides.find(r => r.id === rideId);
    if (ride) {
      const updatedRide = { ...ride, status, updatedAt: new Date().toISOString() };
      return createMockResponse(updatedRide);
    }
    throw new Error('الرحلة غير موجودة');
  },

  // SaaS Management
  createTenant: async (tenantData: any) => {
    await simulateApiDelay();
    const newTenant = {
      id: generateMockId(),
      ...tenantData,
      status: tenantData.status || 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subscriptionsCount: 0,
      totalUsers: 0,
      billingBalance: 0,
    };
    return createMockResponse(newTenant);
  },

  updateTenant: async (tenantId: string, tenantData: any) => {
    await simulateApiDelay();
    const updatedTenant = {
      id: tenantId,
      ...tenantData,
      updatedAt: new Date().toISOString(),
    };
    return createMockResponse(updatedTenant);
  },
  // Auth
  login: async (credentials: any) => {
    await simulateApiDelay();
    if (credentials.email && credentials.password) {
      return createMockResponse({
        user: {
          id: '1',
          name: 'مدير النظام',
          email: credentials.email,
          role: 'ADMIN',
          avatar: '',
        },
        token: 'mock-jwt-token-' + Date.now(),
        expiresIn: 3600,
      });
    }
    throw new Error('بيانات تسجيل الدخول غير صحيحة');
  },

  // Users
  getUsers: async (filters: any = {}) => {
    await simulateApiDelay();
    let filteredUsers = [...mockUsers];
    
    if (filters.userType) {
      filteredUsers = filteredUsers.filter(u => u.userType === filters.userType);
    }
    
    if (filters.status) {
      filteredUsers = filteredUsers.filter(u => u.status === filters.status);
    }
    
    return createMockResponse(filteredUsers);
  },

  // Vehicles
  getVehicles: async (filters: any = {}) => {
    await simulateApiDelay();
    let filteredVehicles = [...mockVehicles];
    
    if (filters.status) {
      filteredVehicles = filteredVehicles.filter(v => v.status === filters.status);
    }
    
    return createMockResponse(filteredVehicles);
  },

  // Rides
  getRides: async (filters: any = {}) => {
    await simulateApiDelay();
    let filteredRides = [...mockRides];
    
    if (filters.status) {
      filteredRides = filteredRides.filter(r => r.status === filters.status);
    }
    
    return createMockResponse(filteredRides);
  },

  // Payments
  getPayments: async (filters: any = {}) => {
    await simulateApiDelay();
    let filteredPayments = [...mockPayments];
    
    if (filters.status) {
      filteredPayments = filteredPayments.filter(p => p.status === filters.status);
    }
    
    return createMockResponse(filteredPayments);
  },

  // Analytics
  getAnalytics: async (filters: any = {}) => {
    await simulateApiDelay();
    return createMockResponse(mockAnalyticsData);
  },
};

export default mockApiResponses;
