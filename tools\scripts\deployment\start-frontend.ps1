# Start Frontend Dashboard for TecnoDrive
Write-Host "🚀 Starting TecnoDrive Frontend Dashboard..." -ForegroundColor Cyan

# Check if we're in the right directory
$frontendPath = "frontend/admin-dashboard"
if (-not (Test-Path $frontendPath)) {
    Write-Host "❌ Frontend directory not found. Please run this script from the project root." -ForegroundColor Red
    exit 1
}

# Navigate to frontend directory
Set-Location $frontendPath

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env file not found. Creating from .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ .env file created. Please update it with your API keys." -ForegroundColor Green
    } else {
        Write-Host "❌ .env.example not found. Please create .env manually." -ForegroundColor Red
    }
}

# Start the development server
Write-Host "🌐 Starting development server..." -ForegroundColor Green
Write-Host "📱 Dashboard will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🗺️  Live Operations: http://localhost:3000/live-operations" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow

npm start
