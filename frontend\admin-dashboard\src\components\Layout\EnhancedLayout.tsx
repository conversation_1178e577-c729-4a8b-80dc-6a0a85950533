import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import {
  Box,
  useTheme,
  useMediaQuery,
  ThemeProvider,
  createTheme,
} from '@mui/material';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { initializeWebSocket, cleanupWebSocket } from '../../services/websocketService';

// Enhanced Components
import EnhancedSidebar from './EnhancedSidebar';
import EnhancedHeader from './EnhancedHeader';

// Page Components
import Dashboard from '../Dashboard/Dashboard';
import RidesManagement from '../Rides/RidesManagement';
import FleetManagement from '../Fleet/FleetManagement';
import UsersManagement from '../Users/<USER>';
import PaymentsManagement from '../Payments/PaymentsManagement';
import Analytics from '../Analytics/Analytics';
import ServiceHealth from '../ServiceHealth/ServiceHealth';
import ParcelsManagement from '../Parcels/ParcelsManagement';
import FleetMap from '../Fleet/FleetMap';
import SimpleInteractiveMapPage from '../../pages/SimpleInteractiveMapPage';
import MapTestPage from '../../pages/MapTestPage';
import RealMapPage from '../../pages/RealMapPage';
import SimpleMapPage from '../../pages/SimpleMapPage';
import EnhancedTripManagement from '../../pages/EnhancedTripManagement';
import AnalyticsDashboard from '../../pages/AnalyticsDashboard';
import HRManagement from '../../pages/HRManagement';
import FinancialManagement from '../../pages/FinancialManagement';
import WalletManagement from '../../pages/WalletManagement';
import LiveOperationsDashboard from '../../pages/LiveOperationsDashboard';
import OperationsManagement from '../../pages/OperationsManagement';
import TripTracking from '../../pages/TripTracking';
import DemandAnalysis from '../../pages/DemandAnalysis';
import TripPlanning from '../../pages/TripPlanning';
import InvoicingManagement from '../../pages/InvoicingManagement';
import MapRouteGuard from '../Auth/MapRouteGuard';
import AuthGuard from '../Auth/AuthGuard';
import RealtimeDashboard from '../RealtimeDashboard';
// import InteractiveMapPage from '../../pages/InteractiveMapPage'; // Will be enabled after libraries are installed
import SaaSManagement from '../SaaS/SaaSManagement';
import ApiGatewayManagement from '../ApiGateway/ApiGatewayManagement';
import NotificationManagement from '../Notifications/NotificationManagement';
import RideRatings from '../Rides/RideRatings';

// Enhanced New Components
import RiskDashboard from '../RiskManagement/RiskDashboard';
import CRMDashboard from '../CRM/CRMDashboard';
import MaintenanceDashboard from '../Maintenance/MaintenanceDashboard';
import EnhancedRealTimeDashboard from '../Dashboard/EnhancedRealTimeDashboard';
import SOCDashboard from '../Security/SOCDashboard';
import PredictiveAnalyticsDashboard from '../Analytics/PredictiveAnalyticsDashboard';
import DatabaseExplorer from '../Database/DatabaseExplorer';
import DatabaseDashboard from '../Database/DatabaseDashboard';
import DataImportExport from '../DataManagement/DataImportExport';
import OracleApexIntegration from '../Integration/OracleApexIntegration';
// import FinanceManagement from '../Finance/FinanceManagement'; // Using pages version instead
// import HRManagement from '../HR/HRManagement'; // Using pages version instead
import Settings from '../Settings/Settings';
import LoginPage from '../Auth/LoginPage';
import MultiAuthSystem from '../Auth/MultiAuthSystem';
import Login from '../Auth/Login';
import ControlPanel from '../ControlPanel/ControlPanel';

// Live Tracking Components
import EnhancedRidesManagement from '../LiveTracking/EnhancedRidesManagement';

// Theme
import { lightTheme, darkTheme } from '../../theme/theme';

const EnhancedLayout: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [sidebarCollapsed, setSidebarCollapsed] = useState(isMobile);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Redux state
  const isAuthenticated = useSelector((state: RootState) => state.auth?.isAuthenticated) ?? false;

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    setSidebarCollapsed(isMobile);
  }, [isMobile]);

  // WebSocket initialization
  useEffect(() => {
    if (isAuthenticated) {
      initializeWebSocket();
      return () => {
        cleanupWebSocket();
      };
    }
  }, [isAuthenticated]);

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleToggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const handleSidebarItemClick = () => {
    if (isMobile) {
      setSidebarCollapsed(true);
    }
  };

  const getMainContentStyles = () => ({
    marginLeft: sidebarCollapsed ? (isMobile ? 0 : 80) : (isMobile ? 0 : 280),
    marginTop: 64, // Header height
    transition: theme.transitions.create(['margin'], {
      duration: theme.transitions.duration.standard,
    }),
    minHeight: 'calc(100vh - 64px)',
    backgroundColor: theme.palette.background.default,
    position: 'relative' as const,
  });

  const getOverlayStyles = () => ({
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: theme.zIndex.drawer - 1,
    display: isMobile && !sidebarCollapsed ? 'block' : 'none',
    transition: theme.transitions.create(['opacity'], {
      duration: theme.transitions.duration.standard,
    }),
  });

  // Theme selection
  const currentTheme = createTheme(isDarkMode ? darkTheme : lightTheme);

  if (!isAuthenticated) {
    return (
      <ThemeProvider theme={currentTheme}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/auth" element={<MultiAuthSystem />} />
          <Route path="/auth/login" element={<LoginPage />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={currentTheme}>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* Sidebar */}
        <EnhancedSidebar
          collapsed={sidebarCollapsed}
          onToggleCollapse={handleToggleSidebar}
          onItemClick={handleSidebarItemClick}
        />

        {/* Mobile Overlay */}
        <Box
          sx={getOverlayStyles()}
          onClick={() => setSidebarCollapsed(true)}
        />

        {/* Header */}
        <EnhancedHeader
          sidebarCollapsed={sidebarCollapsed}
          onToggleSidebar={handleToggleSidebar}
          onToggleTheme={handleToggleTheme}
          isDarkMode={isDarkMode}
        />

        {/* Main Content */}
        <Box component="main" sx={getMainContentStyles()}>
          <Box sx={{ p: 3 }}>
            <Routes>
              {/* Dashboard */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/control-panel" element={<ControlPanel />} />

              {/* Live Operations Dashboard - Phase 1 MVP */}
              <Route path="/live-operations" element={<LiveOperationsDashboard />} />
              <Route path="/operations/live" element={<LiveOperationsDashboard />} />

              {/* Rides Management */}
              <Route path="/rides" element={<RidesManagement />} />
              <Route path="/rides/enhanced" element={<EnhancedRidesManagement />} />
              <Route path="/rides/ratings" element={<RideRatings />} />

              {/* Enhanced Operations Management */}
              <Route path="/operations" element={<AuthGuard><OperationsManagement /></AuthGuard>} />
              <Route path="/operations/enhanced" element={<AuthGuard><EnhancedTripManagement /></AuthGuard>} />
              <Route path="/operations/management" element={<AuthGuard><OperationsManagement /></AuthGuard>} />

              {/* Fleet Management */}
              <Route path="/fleet" element={<FleetManagement />} />
              <Route path="/fleet/map" element={<FleetMap />} />

              {/* Interactive Maps */}
              <Route path="/map" element={<MapRouteGuard><SimpleMapPage /></MapRouteGuard>} />
              <Route path="/map/simple" element={<MapRouteGuard><SimpleMapPage /></MapRouteGuard>} />
              <Route path="/map/street" element={<MapRouteGuard><SimpleInteractiveMapPage /></MapRouteGuard>} />
              <Route path="/map/test" element={<MapRouteGuard><MapTestPage /></MapRouteGuard>} />
              <Route path="/map/real" element={<MapRouteGuard><RealMapPage /></MapRouteGuard>} />

              {/* Real-time Monitoring */}
              <Route path="/monitoring" element={<RealtimeDashboard />} />
              <Route path="/monitoring/realtime" element={<RealtimeDashboard />} />
              <Route path="/monitoring/enhanced" element={<EnhancedRealTimeDashboard />} />

              {/* Risk Management */}
              <Route path="/risk" element={<RiskDashboard />} />
              <Route path="/risk/management" element={<RiskDashboard />} />
              <Route path="/risk/dashboard" element={<RiskDashboard />} />

              {/* Customer Relationship Management */}
              <Route path="/crm" element={<CRMDashboard />} />
              <Route path="/crm/dashboard" element={<CRMDashboard />} />
              <Route path="/customers" element={<CRMDashboard />} />

              {/* Maintenance Management */}
              <Route path="/maintenance" element={<MaintenanceDashboard />} />
              <Route path="/maintenance/dashboard" element={<MaintenanceDashboard />} />
              <Route path="/maintenance/management" element={<MaintenanceDashboard />} />

              {/* Security Operations Center */}
              <Route path="/security" element={<SOCDashboard />} />
              <Route path="/security/soc" element={<SOCDashboard />} />
              <Route path="/security/dashboard" element={<SOCDashboard />} />

              {/* Predictive Analytics */}
              <Route path="/analytics/predictive" element={<PredictiveAnalyticsDashboard />} />
              <Route path="/predictive" element={<PredictiveAnalyticsDashboard />} />
              <Route path="/ai/analytics" element={<PredictiveAnalyticsDashboard />} />

              {/* Database Explorer */}
              <Route path="/database" element={<DatabaseDashboard />} />
              <Route path="/database/dashboard" element={<DatabaseDashboard />} />
              <Route path="/database/explorer" element={<DatabaseExplorer />} />
              <Route path="/admin/database" element={<DatabaseDashboard />} />

              {/* User Management */}
              <Route path="/users" element={<UsersManagement />} />

              {/* Payments */}
              <Route path="/payments" element={<PaymentsManagement />} />

              {/* Wallets */}
              <Route path="/wallets" element={<WalletManagement />} />
              <Route path="/wallets/management" element={<WalletManagement />} />

              {/* Trip Tracking */}
              <Route path="/tracking" element={<TripTracking />} />
              <Route path="/tracking/trip" element={<TripTracking />} />

              {/* Parcels */}
              <Route path="/parcels" element={<ParcelsManagement />} />

              {/* Analytics */}
              <Route path="/analytics" element={<AnalyticsDashboard />} />
              <Route path="/analytics/dashboard" element={<AnalyticsDashboard />} />

              {/* Demand Analysis */}
              <Route path="/demand" element={<DemandAnalysis />} />
              <Route path="/demand/analysis" element={<DemandAnalysis />} />

              {/* Trip Planning */}
              <Route path="/planning" element={<TripPlanning />} />
              <Route path="/planning/trips" element={<TripPlanning />} />

              {/* Invoicing Management */}
              <Route path="/invoicing" element={<InvoicingManagement />} />
              <Route path="/invoices" element={<InvoicingManagement />} />

              {/* Notifications */}
              <Route path="/notifications" element={<NotificationManagement />} />

              {/* Finance */}
              <Route path="/finance" element={<FinancialManagement />} />
              <Route path="/finances" element={<FinancialManagement />} />

              {/* HR */}
              <Route path="/hr" element={<HRManagement />} />

              {/* SaaS */}
              <Route path="/saas" element={<SaaSManagement />} />

              {/* API Gateway */}
              <Route path="/api-gateway" element={<ApiGatewayManagement />} />

              {/* System Health */}
              <Route path="/health" element={<ServiceHealth />} />

              {/* Integration */}
              <Route path="/integration/oracle" element={<OracleApexIntegration />} />

              {/* Data Management */}
              <Route path="/data" element={<DataImportExport />} />

              {/* Auth */}
              <Route path="/auth" element={<MultiAuthSystem />} />

              {/* Settings */}
              <Route path="/settings" element={<Settings />} />

              {/* Fallback */}
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default EnhancedLayout;
