import React, { useState } from 'react';
import {
  Box,
  List,
  Divider,
  Typography,
  useTheme,
  Avatar,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  DirectionsCar as RidesIcon,
  LocalShipping as FleetIcon,
  Map as MapIcon,
  Monitor as MonitorIcon,
  People as UsersIcon,
  Payment as PaymentsIcon,
  Analytics as AnalyticsIcon,
  HealthAndSafety as HealthIcon,
  Inventory as ParcelsIcon,
  Business as SaaSIcon,
  Router as ApiGatewayIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  AccountBalance as FinanceIcon,
  Work as HRIcon,
  Link as IntegrationIcon,
  DataUsage as DataIcon,
  Security as AuthIcon,
  Dashboard as ControlIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Star as StarIcon,
  TrackChanges as LiveTrackingIcon,
  Timeline as EnhancedIcon,
  Warning as RiskIcon,
  Support as CRMIcon,
  Build as MaintenanceIcon,
  Speed as PerformanceIcon,
  Shield as SecurityIcon,
  Psychology as AIIcon,
  Storage as DatabaseIcon,
} from '@mui/icons-material';
import NavigationItem from '../UI/Navigation/NavigationItem';

interface EnhancedSidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  onItemClick?: () => void;
}

const EnhancedSidebar: React.FC<EnhancedSidebarProps> = ({
  collapsed = false,
  onToggleCollapse,
  onItemClick,
}) => {
  const theme = useTheme();

  const navigationItems = [
    {
      id: 'dashboard',
      title: 'لوحة المعلومات',
      icon: <DashboardIcon />,
      path: '/dashboard',
    },
    {
      id: 'control',
      title: 'لوحة التحكم',
      icon: <ControlIcon />,
      path: '/control-panel',
    },
    {
      id: 'live-operations',
      title: 'العمليات المباشرة',
      icon: <LiveTrackingIcon />,
      path: '/live-operations',
      badge: 'NEW',
      badgeColor: 'success' as const,
    },
    {
      id: 'rides',
      title: 'إدارة الرحلات',
      icon: <RidesIcon />,
      children: [
        { id: 'rides-list', title: 'قائمة الرحلات', path: '/rides' },
        { id: 'rides-enhanced', title: 'إدارة محسنة', path: '/rides/enhanced', badge: 'NEW', badgeColor: 'primary' as const },
        { id: 'rides-ratings', title: 'تقييمات الرحلات', path: '/rides/ratings' },
        { id: 'rides-analytics', title: 'تحليلات الرحلات', path: '/rides/analytics' },
      ],
    },
    {
      id: 'fleet',
      title: 'إدارة الأسطول',
      icon: <FleetIcon />,
      children: [
        { id: 'fleet-list', title: 'قائمة المركبات', path: '/fleet' },
        { id: 'fleet-map', title: 'خريطة الأسطول', path: '/fleet/map' },
        { id: 'fleet-maintenance', title: 'الصيانة', path: '/fleet/maintenance' },
      ],
    },
    {
      id: 'maps',
      title: 'الخرائط التفاعلية',
      icon: <MapIcon />,
      children: [
        { id: 'map-street', title: 'خريطة الشوارع', path: '/map/street' },
        { id: 'map-realtime', title: 'التتبع المباشر', path: '/map' },
        { id: 'map-traffic', title: 'حركة المرور', path: '/map/traffic' },
        { id: 'map-geofence', title: 'المناطق المحددة', path: '/map/geofence' },
      ],
    },
    {
      id: 'monitoring',
      title: 'المراقبة في الوقت الفعلي',
      icon: <MonitorIcon />,
      children: [
        { id: 'monitoring-dashboard', title: 'لوحة المراقبة', path: '/monitoring' },
        { id: 'monitoring-realtime', title: 'المراقبة المباشرة', path: '/monitoring/realtime' },
        { id: 'monitoring-enhanced', title: 'المراقبة المحسنة', path: '/monitoring/enhanced', badge: 'NEW', badgeColor: 'success' as const },
        { id: 'monitoring-analytics', title: 'تحليلات الأداء', path: '/monitoring/analytics' },
        { id: 'monitoring-alerts', title: 'التنبيهات', path: '/monitoring/alerts' },
      ],
    },
    {
      id: 'risk',
      title: 'إدارة المخاطر',
      icon: <RiskIcon />,
      badge: 'NEW',
      badgeColor: 'error' as const,
      children: [
        { id: 'risk-dashboard', title: 'لوحة المخاطر', path: '/risk' },
        { id: 'risk-events', title: 'أحداث المخاطر', path: '/risk/events' },
        { id: 'risk-alerts', title: 'تنبيهات الأمان', path: '/risk/alerts' },
        { id: 'risk-reports', title: 'تقارير المخاطر', path: '/risk/reports' },
      ],
    },
    {
      id: 'crm',
      title: 'إدارة علاقات العملاء',
      icon: <CRMIcon />,
      badge: 'NEW',
      badgeColor: 'info' as const,
      children: [
        { id: 'crm-dashboard', title: 'لوحة CRM', path: '/crm' },
        { id: 'crm-customers', title: 'العملاء', path: '/customers' },
        { id: 'crm-support', title: 'الدعم الفني', path: '/crm/support' },
        { id: 'crm-analytics', title: 'تحليلات العملاء', path: '/crm/analytics' },
      ],
    },
    {
      id: 'maintenance',
      title: 'إدارة الصيانة',
      icon: <MaintenanceIcon />,
      badge: 'NEW',
      badgeColor: 'warning' as const,
      children: [
        { id: 'maintenance-dashboard', title: 'لوحة الصيانة', path: '/maintenance' },
        { id: 'maintenance-schedules', title: 'جدولة الصيانة', path: '/maintenance/schedules' },
        { id: 'maintenance-predictive', title: 'الصيانة التنبؤية', path: '/maintenance/predictive' },
        { id: 'maintenance-reports', title: 'تقارير الصيانة', path: '/maintenance/reports' },
      ],
    },
    {
      id: 'security',
      title: 'مركز العمليات الأمنية',
      icon: <SecurityIcon />,
      badge: 'SOC',
      badgeColor: 'error' as const,
      children: [
        { id: 'security-dashboard', title: 'لوحة الأمان', path: '/security' },
        { id: 'security-siem', title: 'SIEM Alerts', path: '/security/siem' },
        { id: 'security-incidents', title: 'الحوادث الأمنية', path: '/security/incidents' },
        { id: 'security-compliance', title: 'الامتثال', path: '/security/compliance' },
      ],
    },
    {
      id: 'ai-analytics',
      title: 'التحليلات التنبؤية',
      icon: <AIIcon />,
      badge: 'AI',
      badgeColor: 'secondary' as const,
      children: [
        { id: 'predictive-dashboard', title: 'لوحة التنبؤات', path: '/analytics/predictive' },
        { id: 'predictive-maintenance', title: 'صيانة تنبؤية', path: '/predictive/maintenance' },
        { id: 'demand-forecasting', title: 'توقع الطلب', path: '/predictive/demand' },
        { id: 'customer-insights', title: 'رؤى العملاء', path: '/predictive/customers' },
      ],
    },
    {
      id: 'database',
      title: 'مستكشف قواعد البيانات',
      icon: <DatabaseIcon />,
      badge: 'DB',
      badgeColor: 'info' as const,
      children: [
        { id: 'database-explorer', title: 'مستكشف البيانات', path: '/database' },
        { id: 'database-connections', title: 'الاتصالات', path: '/database/connections' },
        { id: 'database-queries', title: 'الاستعلامات', path: '/database/queries' },
        { id: 'database-backup', title: 'النسخ الاحتياطي', path: '/database/backup' },
      ],
    },
    {
      id: 'users',
      title: 'إدارة المستخدمين',
      icon: <UsersIcon />,
      path: '/users',
      badge: '12',
    },
    {
      id: 'payments',
      title: 'إدارة المدفوعات',
      icon: <PaymentsIcon />,
      path: '/payments',
    },
    {
      id: 'parcels',
      title: 'إدارة الطرود',
      icon: <ParcelsIcon />,
      path: '/parcels',
    },
    {
      id: 'analytics',
      title: 'التحليلات والتقارير',
      icon: <AnalyticsIcon />,
      children: [
        { id: 'analytics-overview', title: 'نظرة عامة', path: '/analytics' },
        { id: 'analytics-revenue', title: 'تحليل الإيرادات', path: '/analytics/revenue' },
        { id: 'analytics-performance', title: 'تحليل الأداء', path: '/analytics/performance' },
      ],
    },
    {
      id: 'notifications',
      title: 'إدارة الإشعارات',
      icon: <NotificationsIcon />,
      path: '/notifications',
      badge: 5,
    },
    {
      id: 'finance',
      title: 'الإدارة المالية',
      icon: <FinanceIcon />,
      children: [
        { id: 'finance-overview', title: 'نظرة عامة', path: '/finance' },
        { id: 'finance-invoices', title: 'الفواتير', path: '/finance/invoices' },
        { id: 'finance-reports', title: 'التقارير المالية', path: '/finance/reports' },
      ],
    },
    {
      id: 'hr',
      title: 'الموارد البشرية',
      icon: <HRIcon />,
      path: '/hr',
    },
    {
      id: 'saas',
      title: 'إدارة SaaS',
      icon: <SaaSIcon />,
      path: '/saas',
    },
    {
      id: 'api-gateway',
      title: 'بوابة API',
      icon: <ApiGatewayIcon />,
      path: '/api-gateway',
    },
    {
      id: 'health',
      title: 'صحة النظام',
      icon: <HealthIcon />,
      path: '/health',
    },
    {
      id: 'integration',
      title: 'التكامل',
      icon: <IntegrationIcon />,
      children: [
        { id: 'integration-oracle', title: 'Oracle APEX', path: '/integration/oracle' },
        { id: 'integration-apis', title: 'APIs خارجية', path: '/integration/apis' },
      ],
    },
    {
      id: 'data',
      title: 'إدارة البيانات',
      icon: <DataIcon />,
      path: '/data',
    },
    {
      id: 'auth',
      title: 'نظام المصادقة',
      icon: <AuthIcon />,
      path: '/auth',
    },
    {
      id: 'settings',
      title: 'الإعدادات',
      icon: <SettingsIcon />,
      path: '/settings',
    },
  ];

  const getSidebarStyles = () => ({
    width: collapsed ? 80 : 280,
    transition: theme.transitions.create(['width'], {
      duration: theme.transitions.duration.standard,
    }),
    backgroundColor: theme.palette.background.paper,
    borderRight: `1px solid ${theme.palette.divider}`,
    height: '100vh',
    position: 'fixed' as const,
    top: 0,
    left: 0,
    zIndex: theme.zIndex.drawer,
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden',
  });

  const getHeaderStyles = () => ({
    p: 2,
    display: 'flex',
    alignItems: 'center',
    justifyContent: collapsed ? 'center' : 'space-between',
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: alpha(theme.palette.primary.main, 0.02),
    minHeight: 64,
  });

  return (
    <Box sx={getSidebarStyles()}>
      {/* Header */}
      <Box sx={getHeaderStyles()}>
        {!collapsed && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{
                width: 40,
                height: 40,
                backgroundColor: theme.palette.primary.main,
                fontSize: '1.2rem',
                fontWeight: 'bold',
              }}
            >
              T
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, lineHeight: 1.2 }}>
                TECNO DRIVE
              </Typography>
              <Typography variant="caption" color="text.secondary">
                لوحة التحكم الإدارية
              </Typography>
            </Box>
          </Box>
        )}
        
        {collapsed && (
          <Avatar
            sx={{
              width: 32,
              height: 32,
              backgroundColor: theme.palette.primary.main,
              fontSize: '1rem',
              fontWeight: 'bold',
            }}
          >
            T
          </Avatar>
        )}
        
        <Tooltip title={collapsed ? 'توسيع الشريط الجانبي' : 'طي الشريط الجانبي'} arrow>
          <IconButton
            onClick={onToggleCollapse}
            size="small"
            sx={{
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
              },
            }}
          >
            {collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        <List component="nav" sx={{ px: 0 }}>
          {navigationItems.map((item, index) => (
            <NavigationItem
              key={item.id}
              item={item}
              collapsed={collapsed}
              onItemClick={onItemClick}
            />
          ))}
        </List>
      </Box>

      {/* Footer */}
      {!collapsed && (
        <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
              }}
            />
            <Typography variant="caption" color="text.secondary">
              النظام متصل
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary">
            الإصدار 2.1.0
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default EnhancedSidebar;
