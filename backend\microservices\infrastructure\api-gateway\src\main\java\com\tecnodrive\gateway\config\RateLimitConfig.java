package com.tecnodrive.gateway.config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Mono;

/**
 * Rate Limiting Configuration
 */
@Configuration
public class RateLimitConfig {

    /**
     * Key resolver for rate limiting based on IP address
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            // First priority: X-Forwarded-For header (for load balancers/proxies)
            String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.trim().isEmpty()) {
                // Take the first IP in case of multiple IPs
                String clientIp = xForwardedFor.split(",")[0].trim();
                return Mono.just(clientIp);
            }

            // Second priority: X-Real-IP header (for nginx proxy)
            String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
            if (xRealIp != null && !xRealIp.trim().isEmpty()) {
                return Mono.just(xRealIp.trim());
            }

            // Third priority: Remote address from request
            try {
                var remoteAddress = exchange.getRequest().getRemoteAddress();
                if (remoteAddress != null && remoteAddress.getAddress() != null) {
                    String hostAddress = remoteAddress.getAddress().getHostAddress();
                    return Mono.just(hostAddress != null ? hostAddress : "unknown");
                }
            } catch (Exception e) {
                // Log the exception if needed for debugging
                // logger.warn("Failed to get remote address: {}", e.getMessage());
            }

            // Fallback: return unknown for rate limiting
            return Mono.just("unknown");
        };
    }

    /**
     * Alternative: Key resolver based on user ID from JWT token
     * Commented out to avoid bean conflict - use ipKeyResolver for now
     */
    // @Bean
    // public KeyResolver userKeyResolver() {
    //     return exchange -> {
    //         // Extract user ID from JWT token if available
    //         String authHeader = exchange.getRequest().getHeaders().getFirst("Authorization");
    //         if (authHeader != null && authHeader.startsWith("Bearer ")) {
    //             // In a real implementation, you would decode the JWT and extract user ID
    //             // For now, we'll use IP-based rate limiting
    //             return ipKeyResolver().resolve(exchange);
    //         }
    //         return ipKeyResolver().resolve(exchange);
    //     };
    // }
}
