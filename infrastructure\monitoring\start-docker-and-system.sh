#!/bin/bash

# TECNO DRIVE - Docker Startup and System Launch Script

echo "🐳 TECNO DRIVE Platform - Docker Startup Script"
echo "================================================"

# Function to check if Docker is running
check_docker() {
    if docker info > /dev/null 2>&1; then
        echo "✅ Docker is running"
        return 0
    else
        echo "❌ Docker is not running"
        return 1
    fi
}

# Function to start Docker Desktop on Windows
start_docker_windows() {
    echo "🚀 Starting Docker Desktop..."
    
    # Try to start Docker Desktop
    if [ -f "/c/Program Files/Docker/Docker/Docker Desktop.exe" ]; then
        "/c/Program Files/Docker/Docker/Docker Desktop.exe" &
        echo "⏳ Waiting for Docker Desktop to start..."
        
        # Wait up to 60 seconds for Docker to start
        for i in {1..60}; do
            if check_docker; then
                echo "✅ Docker Desktop started successfully!"
                return 0
            fi
            echo -n "."
            sleep 1
        done
        
        echo ""
        echo "❌ Docker Desktop failed to start within 60 seconds"
        return 1
    else
        echo "❌ Docker Desktop not found at expected location"
        echo "Please start Docker Desktop manually"
        return 1
    fi
}

# Main execution
echo ""
echo "🔍 Checking Docker status..."

if ! check_docker; then
    echo ""
    echo "🚀 Attempting to start Docker Desktop..."
    
    if start_docker_windows; then
        echo "✅ Docker is now running!"
    else
        echo ""
        echo "❌ Failed to start Docker automatically"
        echo ""
        echo "📋 Manual steps:"
        echo "1. Open Docker Desktop manually"
        echo "2. Wait for it to start completely"
        echo "3. Run this script again"
        echo ""
        echo "🔍 Or check Docker status with: docker info"
        exit 1
    fi
fi

echo ""
echo "🏗️  Starting TECNO DRIVE Platform..."
echo ""

# Change to infra directory
cd "$(dirname "$0")"

# Start the system
echo "📦 Building and starting services..."
docker-compose up -d --build

# Check if services started successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ TECNO DRIVE Platform started successfully!"
    echo ""
    echo "🌐 Services are available at:"
    echo "  - Eureka Server:  http://localhost:8761"
    echo "  - Auth Service:   http://localhost:8081/actuator/health"
    echo "  - Ride Service:   http://localhost:8082/actuator/health"
    echo "  - Parcel Service: http://localhost:8084/actuator/health"
    echo "  - API Gateway:    http://localhost:8080/actuator/health"
    echo ""
    echo "🔍 Check service status:"
    echo "  docker-compose ps"
    echo ""
    echo "📋 View logs:"
    echo "  docker-compose logs -f [service-name]"
    echo ""
    echo "🛑 Stop services:"
    echo "  docker-compose down"
else
    echo ""
    echo "❌ Failed to start TECNO DRIVE Platform"
    echo ""
    echo "🔍 Check logs for errors:"
    echo "  docker-compose logs"
    echo ""
    echo "🔧 Try rebuilding:"
    echo "  docker-compose down"
    echo "  docker-compose up -d --build"
fi
