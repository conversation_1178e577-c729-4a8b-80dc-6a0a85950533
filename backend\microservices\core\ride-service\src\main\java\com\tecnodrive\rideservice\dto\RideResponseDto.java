package com.tecnodrive.rideservice.dto;

import com.tecnodrive.rideservice.entity.RideStatus;
import com.tecnodrive.rideservice.entity.RideType;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Ride Response DTO
 */
public record RideResponseDto(
    UUID id,
    UUID passengerId,
    UUID driverId,
    VehicleTypeDto vehicleType,
    LocationDto pickupLocation,
    String pickupAddress,
    LocationDto destinationLocation,
    String destinationAddress,
    RideStatus status,
    RideType rideType,
    BigDecimal estimatedFare,
    BigDecimal finalFare,
    BigDecimal surgeMultiplier,
    Instant requestedAt,
    Instant scheduledAt,
    Instant driverAssignedAt,
    Instant driverArrivedAt,
    Instant startedAt,
    Instant completedAt,
    Instant cancelledAt,
    BigDecimal estimatedDistanceKm,
    BigDecimal actualDistanceKm,
    Integer estimatedDurationMinutes,
    Integer actualDurationMinutes,
    String passengerNotes,
    String cancellationReason,
    Integer ratingByPassenger,
    Integer ratingByDriver,
    UUID companyId,
    UUID schoolId,
    Instant createdAt,
    Instant updatedAt
) {
    
    public static record LocationDto(
        double latitude,
        double longitude
    ) {}
    
    public static record VehicleTypeDto(
        UUID id,
        String name,
        String nameAr,
        String description,
        BigDecimal baseFare,
        BigDecimal perKmRate,
        BigDecimal perMinuteRate,
        Integer capacity
    ) {}
}
