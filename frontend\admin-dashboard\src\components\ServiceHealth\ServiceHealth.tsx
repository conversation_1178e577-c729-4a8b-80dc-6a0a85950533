import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  <PERSON>,
  But<PERSON>,
  CircularP<PERSON>ress,
  <PERSON><PERSON>,
  Switch,
  FormControlLabel,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Refresh,
  Warning,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { SERVICE_URLS } from '../../services/api';

interface ServiceStatus {
  name: string;
  url: string;
  status: 'healthy' | 'unhealthy' | 'checking';
  lastChecked: Date;
  error?: string;
}

const ServiceHealth: React.FC = () => {
  const [mockMode, setMockMode] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: 'خدمة الرحلات', url: SERVICE_URLS.RIDE_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة الأسطول', url: SERVICE_URLS.FLEET_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة المصادقة', url: SERVICE_URLS.AUTH_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة المدفوعات', url: SERVICE_URLS.PAYMENT_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة التحليلات', url: SERVICE_URLS.ANALYTICS_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة الإشعارات', url: SERVICE_URLS.NOTIFICATION_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة الموارد البشرية', url: SERVICE_URLS.HR_SERVICE, status: 'unhealthy', lastChecked: new Date(), error: 'Service unavailable' },
    { name: 'خدمة الطرود', url: SERVICE_URLS.PARCEL_SERVICE, status: 'healthy', lastChecked: new Date() },
    { name: 'خدمة المستخدمين', url: SERVICE_URLS.USER_SERVICE, status: 'unhealthy', lastChecked: new Date(), error: 'Connection refused' },
  ]);

  const toggleMockMode = () => {
    setMockMode(!mockMode);
  };

  const checkAllServices = async () => {
    if (mockMode) return;
    setIsChecking(true);
    setTimeout(() => {
      setIsChecking(false);
    }, 2000);
  };

  const healthyCount = services.filter(s => s.status === 'healthy').length;
  const unhealthyCount = services.filter(s => s.status === 'unhealthy').length;
  const uptime = healthyCount / services.length * 100;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle sx={{ color: 'success.main' }} />;
      case 'unhealthy':
        return <Error sx={{ color: 'error.main' }} />;
      case 'checking':
        return <CircularProgress size={20} />;
      default:
        return <Warning sx={{ color: 'warning.main' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'unhealthy':
        return 'error';
      case 'checking':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'متاح';
      case 'unhealthy':
        return 'غير متاح';
      case 'checking':
        return 'جاري الفحص';
      default:
        return 'غير معروف';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
          حالة الخدمات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Switch
                checked={mockMode}
                onChange={toggleMockMode}
                color="primary"
              />
            }
            label="وضع المحاكاة"
          />
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => setSettingsOpen(true)}
          >
            إعدادات الخدمات
          </Button>
          <Button
            variant="contained"
            startIcon={isChecking ? <CircularProgress size={20} /> : <Refresh />}
            onClick={checkAllServices}
            disabled={isChecking || mockMode}
          >
            {isChecking ? 'جاري الفحص...' : 'فحص الخدمات'}
          </Button>
        </Box>
      </Box>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {healthyCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                خدمات متاحة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                {unhealthyCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                خدمات غير متاحة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {uptime.toFixed(0)}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                معدل التوفر
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2}>
        {services.map((service, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  {getStatusIcon(service.status)}
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" sx={{ fontSize: '1rem' }}>
                      {service.name}
                    </Typography>
                    <Chip
                      label={getStatusText(service.status)}
                      color={getStatusColor(service.status) as any}
                      size="small"
                    />
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  URL: {service.url}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  آخر فحص: {service.lastChecked.toLocaleTimeString('ar-SA')}
                </Typography>
                {service.error && (
                  <Alert severity="error" sx={{ mt: 1 }}>
                    {service.error}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            تعليمات الاستخدام
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            • تأكد من تشغيل جميع الخدمات الخلفية قبل استخدام لوحة التحكم
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            • يمكنك تغيير عناوين الخدمات في ملف .env أو من خلال إعدادات الخدمات
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            • استخدم وضع المحاكاة للتطوير والاختبار
          </Typography>
        </CardContent>
      </Card>

      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>إعدادات الخدمات</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 3 }}>
            يمكنك تعديل عناوين الخدمات هنا. التغييرات ستؤثر على الجلسة الحالية فقط.
          </Alert>
          
          <Grid container spacing={2}>
            {services.map((service, index) => (
              <Grid item xs={12} key={index}>
                <TextField
                  fullWidth
                  label={service.name}
                  value={service.url}
                  onChange={(e) => {
                    const updatedServices = [...services];
                    updatedServices[index].url = e.target.value;
                    setServices(updatedServices);
                  }}
                  variant="outlined"
                  size="small"
                />
              </Grid>
            ))}
          </Grid>
          
          <Box sx={{ mt: 3 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={mockMode}
                  onChange={toggleMockMode}
                  color="primary"
                />
              }
              label="تفعيل وضع المحاكاة (للتطوير والاختبار)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>إغلاق</Button>
          <Button 
            variant="contained" 
            onClick={() => {
              setSettingsOpen(false);
              if (!mockMode) {
                checkAllServices();
              }
            }}
          >
            حفظ وفحص الخدمات
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceHealth;
