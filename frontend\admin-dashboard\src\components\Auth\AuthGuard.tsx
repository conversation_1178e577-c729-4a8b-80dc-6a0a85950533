import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Box, CircularProgress, Typography } from '@mui/material';
import { RootState } from '../../store/store';
import { setCredentials, clearCredentials } from '../../store/slices/authSlice';
import { 
  isAuthenticated, 
  getCurrentUser, 
  getAuthToken, 
  autoLogin, 
  shouldAutoLogin,
  extendSession 
} from '../../services/persistentAuthService';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requireAuth = true 
}) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  const [loading, setLoading] = useState(true);
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      console.log('🔍 AuthGuard: Checking authentication...');
      
      try {
        // Check if user is already authenticated in Redux
        if (user && token) {
          console.log('✅ AuthGuard: User already authenticated in Redux');
          setLoading(false);
          setAuthChecked(true);
          return;
        }

        // Check persistent storage
        if (isAuthenticated()) {
          const currentUser = getCurrentUser();
          const authToken = getAuthToken();
          
          if (currentUser && authToken) {
            console.log('✅ AuthGuard: Found valid auth data in storage');
            
            // Update Redux state
            dispatch(setCredentials({
              user: currentUser,
              token: authToken
            }));
            
            // Extend session if needed
            await extendSession();
            
            setLoading(false);
            setAuthChecked(true);
            return;
          }
        }

        // Try auto-login if enabled
        if (shouldAutoLogin()) {
          console.log('🔄 AuthGuard: Attempting auto-login...');
          
          const autoLoginResult = await autoLogin();
          
          if (autoLoginResult && autoLoginResult.success && autoLoginResult.data) {
            console.log('✅ AuthGuard: Auto-login successful');
            
            dispatch(setCredentials({
              user: autoLoginResult.data.user,
              token: autoLoginResult.data.token
            }));
            
            setLoading(false);
            setAuthChecked(true);
            return;
          }
        }

        // No valid authentication found
        console.log('❌ AuthGuard: No valid authentication found');
        dispatch(clearCredentials());
        
      } catch (error) {
        console.error('❌ AuthGuard: Authentication check failed:', error);
        dispatch(clearCredentials());
      } finally {
        setLoading(false);
        setAuthChecked(true);
      }
    };

    if (!authChecked) {
      checkAuthentication();
    }
  }, [dispatch, user, token, authChecked]);

  // Session extension effect
  useEffect(() => {
    if (!user || !token) return;

    const extendSessionInterval = setInterval(async () => {
      if (isAuthenticated()) {
        await extendSession();
        console.log('🔄 AuthGuard: Session extended');
      }
    }, 15 * 60 * 1000); // Extend every 15 minutes

    return () => clearInterval(extendSessionInterval);
  }, [user, token]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <CircularProgress size={60} sx={{ color: 'white', mb: 2 }} />
        <Typography variant="h6" sx={{ color: 'white', textAlign: 'center' }}>
          جاري التحقق من حالة تسجيل الدخول...
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)', mt: 1 }}>
          يرجى الانتظار
        </Typography>
      </Box>
    );
  }

  // Check if authentication is required
  if (requireAuth) {
    // Check if user is authenticated
    const userAuthenticated = user && token && isAuthenticated();
    
    if (!userAuthenticated) {
      console.log('❌ AuthGuard: User not authenticated, redirecting to login');
      
      // Redirect to login with return URL
      return (
        <Navigate 
          to="/login" 
          state={{ from: location.pathname }} 
          replace 
        />
      );
    }
  }

  // If we reach here, user is authenticated or auth is not required
  console.log('✅ AuthGuard: Access granted');
  return <>{children}</>;
};

// Higher-order component for protecting routes
export const withAuthGuard = (
  Component: React.ComponentType<any>,
  requireAuth: boolean = true
) => {
  return (props: any) => (
    <AuthGuard requireAuth={requireAuth}>
      <Component {...props} />
    </AuthGuard>
  );
};

// Hook for checking auth status
export const useAuthStatus = () => {
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  return {
    isAuthenticated: user && token && isAuthenticated(),
    user: getCurrentUser(),
    token: getAuthToken(),
    shouldAutoLogin: shouldAutoLogin()
  };
};

export default AuthGuard;
