// TECNO DRIVE - TypeScript Fix Utilities
// Utility functions and type guards to fix TypeScript issues

import { UserRole, ParcelStatus, PaymentStatus, RideStatus } from '../types/api';

// Type Guards for safe type conversion
export const isValidUserRole = (role: string): role is UserRole => {
  return ['ADMIN', 'MANAGER', 'OPERATOR', 'DRIVER', 'CUSTOMER'].includes(role);
};

export const isValidParcelStatus = (status: string): status is ParcelStatus => {
  return ['PENDING', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'RETURNED'].includes(status);
};

export const isValidPaymentStatus = (status: string): status is PaymentStatus => {
  return ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED', 'CANCELLED'].includes(status);
};

export const isValidRideStatus = (status: string): status is RideStatus => {
  return ['COMPLETED', 'IN_PROGRESS', 'REQUESTED', 'ACCEPTED', 'CANCELLED'].includes(status);
};

// Safe converters
export const toUserRole = (role: string): UserRole => {
  return isValidUserRole(role) ? role : 'CUSTOMER';
};

export const toParcelStatus = (status: string): ParcelStatus => {
  return isValidParcelStatus(status) ? status : 'PENDING';
};

export const toPaymentStatus = (status: string): PaymentStatus => {
  return isValidPaymentStatus(status) ? status : 'PENDING';
};

export const toRideStatus = (status: string): RideStatus => {
  return isValidRideStatus(status) ? status : 'REQUESTED';
};

// Mock data generators with proper types
export const generateMockUser = (overrides: Partial<any> = {}) => ({
  id: 'user-1',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'ADMIN' as UserRole,
  avatar: '/default-avatar.png',
  permissions: ['read', 'write'],
  ...overrides
});

export const generateMockLoginResponse = (overrides: Partial<any> = {}) => ({
  user: generateMockUser(),
  token: 'mock-jwt-token',
  expiresIn: 3600,
  refreshToken: 'mock-refresh-token',
  ...overrides
});

// Request configuration with proper typing
export interface SafeRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  // Remove timeout from here as it's not part of standard RequestInit
}

export const createSafeRequestConfig = (config: SafeRequestConfig): RequestInit => {
  const { method = 'GET', headers = {}, body } = config;
  
  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...headers
    }
  };

  if (body && method !== 'GET') {
    requestInit.body = typeof body === 'string' ? body : JSON.stringify(body);
  }

  return requestInit;
};

// Timeout wrapper for fetch
export const fetchWithTimeout = async (
  url: string, 
  config: SafeRequestConfig = {}, 
  timeoutMs: number = 5000
): Promise<Response> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const requestConfig = createSafeRequestConfig(config);
    requestConfig.signal = controller.signal;
    
    const response = await fetch(url, requestConfig);
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

// Grid props helper for Material-UI compatibility
export interface SafeGridProps {
  container?: boolean;
  item?: boolean;
  xs?: number | boolean;
  sm?: number | boolean;
  md?: number | boolean;
  lg?: number | boolean;
  xl?: number | boolean;
  spacing?: number;
  children?: React.ReactNode;
  sx?: any;
}

export const createGridProps = (props: SafeGridProps) => {
  // Ensure proper Grid usage
  if (props.item && !props.container) {
    // This is a Grid item
    return {
      item: true,
      xs: props.xs,
      sm: props.sm,
      md: props.md,
      lg: props.lg,
      xl: props.xl,
      sx: props.sx,
      children: props.children
    };
  } else if (props.container) {
    // This is a Grid container
    return {
      container: true,
      spacing: props.spacing,
      sx: props.sx,
      children: props.children
    };
  }
  
  return props;
};

// API Response helpers
export const createApiResponse = <T>(data: T, overrides: Partial<any> = {}) => ({
  success: true,
  message: 'Success',
  data,
  total: Array.isArray(data) ? data.length : 1,
  totalParcels: 0,
  inTransitParcels: 0,
  deliveredParcels: 0,
  totalPayments: 0,
  pendingPayments: 0,
  completedPayments: 0,
  totalUsers: 0,
  activeUsers: 0,
  inactiveUsers: 0,
  stats: {},
  metadata: {},
  ...overrides
});

// Filter helpers with proper typing
export const createParcelFilters = (filters: Record<string, any> = {}) => ({
  page: filters.page || 1,
  limit: filters.limit || 10,
  status: filters.status ? toParcelStatus(filters.status) : undefined,
  senderId: filters.senderId,
  receiverId: filters.receiverId,
  dateFrom: filters.dateFrom,
  dateTo: filters.dateTo
});

export const createPaymentFilters = (filters: Record<string, any> = {}) => ({
  page: filters.page || 1,
  limit: filters.limit || 10,
  status: filters.status ? toPaymentStatus(filters.status) : undefined,
  userId: filters.userId,
  method: filters.method,
  dateFrom: filters.dateFrom,
  dateTo: filters.dateTo
});

export const createUserFilters = (filters: Record<string, any> = {}) => ({
  page: filters.page || 1,
  limit: filters.limit || 10,
  status: filters.status as "ACTIVE" | "INACTIVE" | "SUSPENDED" | undefined,
  userType: filters.userType ? toUserRole(filters.userType) : undefined,
  role: filters.role ? toUserRole(filters.role) : undefined,
  dateFrom: filters.dateFrom,
  dateTo: filters.dateTo
});

// Error boundary helper
export const handleTypeError = (error: any, fallback: any = null) => {
  console.warn('Type conversion error:', error);
  return fallback;
};

// Component prop validators
export const validateGridProps = (props: any) => {
  if (props.item && !props.xs && !props.sm && !props.md && !props.lg && !props.xl) {
    console.warn('Grid item should have at least one size prop (xs, sm, md, lg, xl)');
  }
  
  if (props.container && props.item) {
    console.warn('Grid cannot be both container and item');
  }
  
  return props;
};

// Export utility functions
export default {
  isValidUserRole,
  isValidParcelStatus,
  isValidPaymentStatus,
  isValidRideStatus,
  toUserRole,
  toParcelStatus,
  toPaymentStatus,
  toRideStatus,
  generateMockUser,
  generateMockLoginResponse,
  createSafeRequestConfig,
  fetchWithTimeout,
  createGridProps,
  createApiResponse,
  createParcelFilters,
  createPaymentFilters,
  createUserFilters,
  handleTypeError,
  validateGridProps
};
