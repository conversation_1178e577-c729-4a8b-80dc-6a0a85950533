import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Refresh,
  DirectionsCar,
  People,
  AttachMoney,
  LocalShipping,
  Star,
  Speed,
} from '@mui/icons-material';

interface QuickStat {
  id: string;
  label: string;
  value: number;
  change: number;
  unit: string;
  icon: React.ReactNode;
  color: string;
}

const SimplifiedMetrics: React.FC = () => {
  const [stats, setStats] = useState<QuickStat[]>([
    {
      id: 'daily_rides',
      label: 'رحلات اليوم',
      value: 127,
      change: 8.5,
      unit: 'رحلة',
      icon: <DirectionsCar />,
      color: '#667eea',
    },
    {
      id: 'active_users',
      label: 'المستخدمون النشطون',
      value: 342,
      change: 12.3,
      unit: 'مستخدم',
      icon: <People />,
      color: '#48bb78',
    },
    {
      id: 'daily_revenue',
      label: 'إيرادات اليوم',
      value: 8450,
      change: 5.7,
      unit: 'ريال',
      icon: <AttachMoney />,
      color: '#4299e1',
    },
    {
      id: 'deliveries',
      label: 'الطرود المسلمة',
      value: 89,
      change: 15.2,
      unit: 'طرد',
      icon: <LocalShipping />,
      color: '#ed8936',
    },
    {
      id: 'avg_rating',
      label: 'متوسط التقييم',
      value: 4.8,
      change: 2.1,
      unit: '⭐',
      icon: <Star />,
      color: '#9f7aea',
    },
    {
      id: 'response_time',
      label: 'زمن الاستجابة',
      value: 1.8,
      change: -12.5,
      unit: 'دقيقة',
      icon: <Speed />,
      color: '#f56565',
    },
  ]);

  const [lastUpdate, setLastUpdate] = useState(new Date());

  const refreshStats = () => {
    setStats(prev => prev.map(stat => ({
      ...stat,
      value: stat.value + (Math.random() - 0.5) * (stat.value * 0.1),
      change: (Math.random() - 0.5) * 20,
    })));
    setLastUpdate(new Date());
  };

  useEffect(() => {
    const interval = setInterval(refreshStats, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  return (
    <Card className="enhanced-card" sx={{ mb: 3 }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                mr: 2,
              }}
            >
              📈
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>
                الإحصائيات السريعة
              </Typography>
              <Typography variant="body2" color="text.secondary">
                آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
          <Tooltip title="تحديث البيانات">
            <IconButton 
              onClick={refreshStats}
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8, #6a4190)',
                },
              }}
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        <Grid container spacing={2}>
          {stats.map((stat, index) => {
            const isPositive = stat.change > 0;
            
            return (
              <Grid item xs={12} sm={6} md={4} lg={2} key={stat.id}>
                <Box
                  className="scale-in interactive-hover"
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    textAlign: 'center',
                    animationDelay: `${index * 0.1}s`,
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      background: stat.color,
                    },
                  }}
                >
                  <Avatar
                    sx={{
                      width: 48,
                      height: 48,
                      background: `${stat.color}20`,
                      color: stat.color,
                      mx: 'auto',
                      mb: 1.5,
                    }}
                  >
                    {stat.icon}
                  </Avatar>

                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      mb: 0.5,
                      color: stat.color,
                    }}
                  >
                    {stat.value.toFixed(stat.unit === '⭐' || stat.unit === 'دقيقة' ? 1 : 0)}
                  </Typography>

                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    {stat.label}
                  </Typography>

                  <Chip
                    icon={isPositive ? <TrendingUp /> : <TrendingDown />}
                    label={`${isPositive ? '+' : ''}${stat.change.toFixed(1)}%`}
                    size="small"
                    sx={{
                      background: isPositive 
                        ? 'linear-gradient(135deg, #48bb78, #38a169)' 
                        : 'linear-gradient(135deg, #f56565, #e53e3e)',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.7rem',
                      '& .MuiChip-icon': {
                        color: 'white',
                        fontSize: '0.9rem',
                      },
                    }}
                  />
                </Box>
              </Grid>
            );
          })}
        </Grid>

        {/* Quick Summary */}
        <Box sx={{ mt: 3, p: 2, borderRadius: 2, background: 'rgba(102, 126, 234, 0.1)' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#667eea', mb: 0.5 }}>
                ملخص الأداء اليومي
              </Typography>
              <Typography variant="body2" color="text.secondary">
                النظام يعمل بكفاءة عالية مع نمو إيجابي في معظم المؤشرات
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label="ممتاز"
                sx={{
                  background: '#48bb78',
                  color: 'white',
                  fontWeight: 600,
                }}
              />
              <Chip
                label="نشط"
                sx={{
                  background: '#4299e1',
                  color: 'white',
                  fontWeight: 600,
                }}
              />
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SimplifiedMetrics;
