import axios from 'axios';
import { io, Socket } from 'socket.io-client';

// Types for Maintenance Management
export interface Vehicle {
  id: string;
  tenantId: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  vin: string;
  status: 'active' | 'maintenance' | 'out_of_service' | 'retired';
  mileage: number;
  fuelType: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  nextMaintenanceDue: string;
  lastMaintenanceDate?: string;
  sensorConfiguration: SensorConfig[];
  maintenanceHistory: MaintenanceRecord[];
  healthScore: number;
}

export interface SensorConfig {
  id: string;
  type: 'engine' | 'brake' | 'tire' | 'battery' | 'transmission' | 'fuel' | 'temperature';
  location: string;
  calibrationStatus: 'calibrated' | 'needs_calibration' | 'faulty';
  lastReading: SensorReading;
}

export interface SensorReading {
  timestamp: string;
  value: number;
  unit: string;
  status: 'normal' | 'warning' | 'critical';
}

export interface MaintenanceSchedule {
  id: string;
  tenantId: string;
  vehicleId: string;
  type: 'preventive' | 'corrective' | 'predictive' | 'emergency';
  title: string;
  description: string;
  dueDate: string;
  estimatedDuration: number; // in hours
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';
  assignedMechanic?: string;
  requiredParts: RequiredPart[];
  requiredCertifications: string[];
  estimatedCost: number;
  createdAt: string;
  scheduledBy: string;
}

export interface RequiredPart {
  partId: string;
  partName: string;
  quantity: number;
  estimatedCost: number;
  availability: 'in_stock' | 'low_stock' | 'out_of_stock' | 'on_order';
  supplier?: string;
}

export interface MaintenanceRecord {
  id: string;
  scheduleId: string;
  tenantId: string;
  vehicleId: string;
  performedAt: string;
  performedBy: string;
  actualDuration: number;
  actualCost: number;
  workPerformed: string;
  partsUsed: UsedPart[];
  beforePhotos: string[];
  afterPhotos: string[];
  mechanicNotes: string;
  qualityRating: number;
  nextMaintenanceRecommendation?: string;
  issuesFound: string[];
  status: 'completed' | 'partially_completed' | 'failed';
}

export interface UsedPart {
  partId: string;
  partName: string;
  quantity: number;
  actualCost: number;
  condition: 'new' | 'refurbished' | 'used';
}

export interface PredictiveMaintenanceAlert {
  id: string;
  vehicleId: string;
  component: string;
  alertType: 'wear_prediction' | 'failure_prediction' | 'performance_degradation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  predictedFailureDate: string;
  confidence: number;
  recommendations: string[];
  sensorData: SensorReading[];
  createdAt: string;
}

export interface MaintenanceAnalytics {
  totalVehicles: number;
  vehiclesInMaintenance: number;
  overdueMaintenances: number;
  averageMaintenanceCost: number;
  maintenanceEfficiency: number;
  predictiveAccuracy: number;
  costSavings: number;
  maintenanceByType: Record<string, number>;
  topMaintenanceIssues: Array<{ issue: string; frequency: number }>;
}

class MaintenanceService {
  private socket: Socket | null = null;
  private baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    this.socket = io(`${this.baseURL}/maintenance`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to Maintenance WebSocket');
    });
  }

  // Real-time subscriptions
  subscribeToSensorData(vehicleId: string, callback: (data: SensorReading) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-sensor-data', { vehicleId });
      this.socket.on('sensor-data-update', callback);
    }
  }

  subscribeToPredictiveAlerts(tenantId: string, callback: (alert: PredictiveMaintenanceAlert) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-predictive-alerts', { tenantId });
      this.socket.on('new-predictive-alert', callback);
    }
  }

  subscribeToMaintenanceUpdates(tenantId: string, callback: (schedule: MaintenanceSchedule) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-maintenance-updates', { tenantId });
      this.socket.on('maintenance-updated', callback);
    }
  }

  // Vehicle Management
  async getVehicles(tenantId: string, filters?: {
    status?: string;
    maintenanceDue?: boolean;
    search?: string;
  }): Promise<Vehicle[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/vehicles`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      throw error;
    }
  }

  async getVehicle(vehicleId: string): Promise<Vehicle> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/vehicles/${vehicleId}`, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicle:', error);
      throw error;
    }
  }

  async updateVehicleStatus(vehicleId: string, status: Vehicle['status']): Promise<Vehicle> {
    try {
      const response = await axios.put(`${this.baseURL}/api/maintenance/vehicles/${vehicleId}/status`, 
        { status }, 
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error updating vehicle status:', error);
      throw error;
    }
  }

  // Maintenance Scheduling
  async getMaintenanceSchedules(tenantId: string, filters?: {
    vehicleId?: string;
    status?: string;
    type?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<MaintenanceSchedule[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/schedules`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance schedules:', error);
      throw error;
    }
  }

  async createMaintenanceSchedule(schedule: Omit<MaintenanceSchedule, 'id' | 'createdAt'>): Promise<MaintenanceSchedule> {
    try {
      const response = await axios.post(`${this.baseURL}/api/maintenance/schedules`, schedule, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error creating maintenance schedule:', error);
      throw error;
    }
  }

  async updateMaintenanceSchedule(scheduleId: string, updates: Partial<MaintenanceSchedule>): Promise<MaintenanceSchedule> {
    try {
      const response = await axios.put(`${this.baseURL}/api/maintenance/schedules/${scheduleId}`, updates, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating maintenance schedule:', error);
      throw error;
    }
  }

  // Maintenance Records
  async getMaintenanceRecords(vehicleId: string): Promise<MaintenanceRecord[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/records`, {
        params: { vehicleId },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      throw error;
    }
  }

  async completeMaintenance(
    scheduleId: string, 
    record: Omit<MaintenanceRecord, 'id' | 'scheduleId' | 'performedAt'>
  ): Promise<MaintenanceRecord> {
    try {
      const response = await axios.post(`${this.baseURL}/api/maintenance/schedules/${scheduleId}/complete`, record, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error completing maintenance:', error);
      throw error;
    }
  }

  // Predictive Maintenance
  async getPredictiveAlerts(tenantId: string, filters?: {
    vehicleId?: string;
    severity?: string;
    component?: string;
  }): Promise<PredictiveMaintenanceAlert[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/predictive-alerts`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching predictive alerts:', error);
      throw error;
    }
  }

  async runPredictiveAnalysis(vehicleId: string): Promise<PredictiveMaintenanceAlert[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/maintenance/predict`, { vehicleId }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error running predictive analysis:', error);
      throw error;
    }
  }

  // Analytics
  async getMaintenanceAnalytics(tenantId: string, timeframe: string = '30d'): Promise<MaintenanceAnalytics> {
    try {
      const response = await axios.get(`${this.baseURL}/api/maintenance/analytics`, {
        params: { tenantId, timeframe },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance analytics:', error);
      throw error;
    }
  }

  // File uploads
  async uploadMaintenancePhotos(files: File[]): Promise<string[]> {
    try {
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });

      const response = await axios.post(`${this.baseURL}/api/maintenance/upload-photos`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
      });
      return response.data.urls;
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw error;
    }
  }

  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

export const maintenanceService = new MaintenanceService();
export default maintenanceService;
