-- TECNODRIVE Fleet Service Database Schema
-- Database: tecnodrive_fleet

\c tecnodrive_fleet;

-- Vehicle types and categories
CREATE TABLE IF NOT EXISTS vehicle_types (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('CAR', 'MOTORCYCLE', 'TRUCK', 'VAN', 'BUS')),
    capacity INTEGER NOT NULL,
    description TEXT,
    base_fare DECIMAL(10,2),
    per_km_rate DECIMAL(10,2),
    per_minute_rate DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicles table
CREATE TABLE IF NOT EXISTS vehicles (
    id BIGSERIAL PRIMARY KEY,
    vehicle_type_id BIGINT REFERENCES vehicle_types(id),
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50) NOT NULL,
    model VARCHAR(50) NOT NULL,
    year INTEGER NOT NULL,
    color VARCHAR(30),
    vin <PERSON>(50) UNIQUE,
    engine_number VARCHAR(50),
    fuel_type VARCHAR(20) CHECK (fuel_type IN ('PETROL', 'DIESEL', 'ELECTRIC', 'HYBRID', 'CNG')),
    transmission VARCHAR(20) CHECK (transmission IN ('MANUAL', 'AUTOMATIC', 'CVT')),
    seating_capacity INTEGER,
    current_driver_id BIGINT, -- Reference to user service
    owner_id BIGINT, -- Reference to user service (for owner-operator model)
    status VARCHAR(20) DEFAULT 'AVAILABLE' CHECK (status IN ('AVAILABLE', 'BUSY', 'MAINTENANCE', 'OUT_OF_SERVICE', 'RETIRED')),
    current_location POINT,
    last_location_update TIMESTAMP,
    odometer_reading INTEGER DEFAULT 0, -- in kilometers
    fuel_level DECIMAL(5,2), -- percentage
    battery_level DECIMAL(5,2), -- for electric vehicles
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle documents and certifications
CREATE TABLE IF NOT EXISTS vehicle_documents (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL,
    document_number VARCHAR(100),
    issued_date DATE,
    expiry_date DATE,
    issuing_authority VARCHAR(100),
    document_url TEXT,
    status VARCHAR(20) DEFAULT 'VALID' CHECK (status IN ('VALID', 'EXPIRED', 'PENDING', 'REJECTED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle maintenance records
CREATE TABLE IF NOT EXISTS vehicle_maintenance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL,
    description TEXT,
    scheduled_date DATE,
    completed_date DATE,
    odometer_at_service INTEGER,
    cost DECIMAL(10,2),
    service_provider VARCHAR(100),
    next_service_due INTEGER, -- odometer reading
    next_service_date DATE,
    status VARCHAR(20) DEFAULT 'SCHEDULED' CHECK (status IN ('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle inspections
CREATE TABLE IF NOT EXISTS vehicle_inspections (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    inspector_id BIGINT, -- Reference to user service
    inspection_type VARCHAR(50) NOT NULL,
    inspection_date DATE NOT NULL,
    odometer_reading INTEGER,
    overall_condition VARCHAR(20) CHECK (overall_condition IN ('EXCELLENT', 'GOOD', 'FAIR', 'POOR', 'FAILED')),
    inspection_items JSONB, -- detailed inspection checklist
    issues_found JSONB,
    recommendations TEXT,
    next_inspection_due DATE,
    certificate_number VARCHAR(100),
    status VARCHAR(20) DEFAULT 'PASSED' CHECK (status IN ('PASSED', 'FAILED', 'CONDITIONAL')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle tracking and telematics
CREATE TABLE IF NOT EXISTS vehicle_tracking (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    location POINT NOT NULL,
    speed DECIMAL(5,2), -- km/h
    heading DECIMAL(5,2), -- degrees
    altitude DECIMAL(8,2), -- meters
    accuracy DECIMAL(8,2), -- meters
    fuel_level DECIMAL(5,2),
    engine_status VARCHAR(20),
    odometer_reading INTEGER,
    diagnostic_codes JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle assignments to drivers
CREATE TABLE IF NOT EXISTS vehicle_assignments (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT NOT NULL, -- Reference to user service
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP,
    assignment_type VARCHAR(20) DEFAULT 'TEMPORARY' CHECK (assignment_type IN ('PERMANENT', 'TEMPORARY', 'SHIFT')),
    shift_start TIME,
    shift_end TIME,
    is_active BOOLEAN DEFAULT true,
    notes TEXT
);

-- Vehicle fuel/charging records
CREATE TABLE IF NOT EXISTS vehicle_fuel_records (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT, -- Reference to user service
    fuel_type VARCHAR(20) NOT NULL,
    quantity DECIMAL(8,2) NOT NULL, -- liters or kWh
    cost DECIMAL(10,2),
    odometer_reading INTEGER,
    fuel_station VARCHAR(100),
    location POINT,
    receipt_url TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle insurance records
CREATE TABLE IF NOT EXISTS vehicle_insurance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    insurance_company VARCHAR(100) NOT NULL,
    policy_number VARCHAR(100) UNIQUE NOT NULL,
    policy_type VARCHAR(50) NOT NULL,
    coverage_amount DECIMAL(12,2),
    premium_amount DECIMAL(10,2),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    deductible DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'EXPIRED', 'CANCELLED', 'SUSPENDED')),
    documents JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_vehicles_license_plate ON vehicles(license_plate);
CREATE INDEX IF NOT EXISTS idx_vehicles_status ON vehicles(status);
CREATE INDEX IF NOT EXISTS idx_vehicles_driver ON vehicles(current_driver_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_location ON vehicles USING GIST(current_location);
CREATE INDEX IF NOT EXISTS idx_vehicles_type ON vehicles(vehicle_type_id);

CREATE INDEX IF NOT EXISTS idx_vehicle_documents_vehicle ON vehicle_documents(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_documents_type ON vehicle_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_vehicle_documents_expiry ON vehicle_documents(expiry_date);

CREATE INDEX IF NOT EXISTS idx_vehicle_maintenance_vehicle ON vehicle_maintenance(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_maintenance_date ON vehicle_maintenance(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_vehicle_maintenance_status ON vehicle_maintenance(status);

CREATE INDEX IF NOT EXISTS idx_vehicle_tracking_vehicle ON vehicle_tracking(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_tracking_timestamp ON vehicle_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_vehicle_tracking_location ON vehicle_tracking USING GIST(location);

CREATE INDEX IF NOT EXISTS idx_vehicle_assignments_vehicle ON vehicle_assignments(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_assignments_driver ON vehicle_assignments(driver_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_assignments_active ON vehicle_assignments(is_active);

-- Triggers for updated_at
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicle_documents_updated_at BEFORE UPDATE ON vehicle_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicle_maintenance_updated_at BEFORE UPDATE ON vehicle_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicle_insurance_updated_at BEFORE UPDATE ON vehicle_insurance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default vehicle types
INSERT INTO vehicle_types (type_name, category, capacity, base_fare, per_km_rate, per_minute_rate) VALUES 
    ('Economy Car', 'CAR', 4, 5.00, 1.50, 0.25),
    ('Standard Car', 'CAR', 4, 7.00, 2.00, 0.30),
    ('Premium Car', 'CAR', 4, 12.00, 3.00, 0.50),
    ('SUV', 'CAR', 6, 15.00, 3.50, 0.60),
    ('Motorcycle', 'MOTORCYCLE', 2, 3.00, 1.00, 0.15),
    ('Delivery Van', 'VAN', 2, 10.00, 2.50, 0.40)
ON CONFLICT (type_name) DO NOTHING;
