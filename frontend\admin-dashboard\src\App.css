/* TECNO DRIVE Admin Dashboard - Enhanced Styles */

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Cairo', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Enhanced Card Styles */
.enhanced-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.enhanced-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.gradient-error {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.gradient-info {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status Indicators */
.status-online {
  color: #48bb78;
  font-weight: 600;
}

.status-offline {
  color: #f56565;
  font-weight: 600;
}

.status-warning {
  color: #ed8936;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a6fd8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-card {
    margin: 8px;
    border-radius: 12px;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .MuiDrawer-paper {
  right: 0;
  left: auto;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #e2e8f0;
  }

  .enhanced-card {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Additional Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
  }
}

/* Interactive Elements */
.interactive-hover {
  transition: all 0.3s ease;
}

.interactive-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Notification Badge Animation */
.notification-badge {
  animation: bounce 2s infinite;
}

/* Success State */
.success-state {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Loading States */
.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Focus States */
.MuiButton-root:focus,
.MuiIconButton-root:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth Transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease;
}
