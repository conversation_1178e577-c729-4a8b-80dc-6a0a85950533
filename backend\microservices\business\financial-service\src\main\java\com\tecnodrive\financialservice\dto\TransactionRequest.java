package com.tecnodrive.financialservice.dto;

import com.tecnodrive.financialservice.entity.FinancialTransactionLog;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Transaction Request DTO
 * 
 * Used for creating financial transactions
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionRequest {

    /**
     * Type of transaction
     */
    @NotNull(message = "Transaction type is required")
    private FinancialTransactionLog.TransactionType transactionType;

    /**
     * Source entity type (RIDE, PARCEL, SUBSCRIPTION, etc.)
     */
    @NotBlank(message = "Source entity type is required")
    @Size(max = 50, message = "Source entity type cannot exceed 50 characters")
    private String sourceEntityType;

    /**
     * Source entity ID
     */
    @NotBlank(message = "Source entity ID is required")
    private String sourceEntityId;

    /**
     * Transaction amount
     */
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount must be greater than 0")
    @Digits(integer = 17, fraction = 2, message = "Amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal amount;

    /**
     * Currency code
     */
    @Size(min = 3, max = 3, message = "Currency must be 3 characters")
    private String currency;

    /**
     * Transaction status
     */
    private FinancialTransactionLog.TransactionStatus status;

    /**
     * Transaction description
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /**
     * Reference number for external systems
     */
    @Size(max = 100, message = "Reference number cannot exceed 100 characters")
    private String referenceNumber;

    /**
     * Payment method used
     */
    @Size(max = 50, message = "Payment method cannot exceed 50 characters")
    private String paymentMethod;

    /**
     * Tax amount
     */
    @DecimalMin(value = "0.0", message = "Tax amount cannot be negative")
    @Digits(integer = 17, fraction = 2, message = "Tax amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal taxAmount;

    /**
     * Fee amount
     */
    @DecimalMin(value = "0.0", message = "Fee amount cannot be negative")
    @Digits(integer = 17, fraction = 2, message = "Fee amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal feeAmount;

    /**
     * Company/Tenant ID
     */
    @NotBlank(message = "Company ID is required")
    private String companyId;

    /**
     * User ID who initiated the transaction
     */
    private String userId;

    /**
     * Additional metadata
     */
    private Map<String, Object> metadata;
}
