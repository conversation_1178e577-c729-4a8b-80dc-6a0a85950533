# Multi-stage build for User Service
FROM maven:3.9.4-openjdk-21-slim AS builder

# Set working directory
WORKDIR /app

# Copy parent pom first for better caching
COPY ../../pom.xml ./
COPY ../../shared/common/pom.xml ./shared/common/
COPY ../../shared/common/src ./shared/common/src

# Copy user service files
COPY pom.xml ./services/user-service/
COPY src ./services/user-service/src

# Build the application
RUN mvn clean package -DskipTests -f services/user-service/pom.xml

# Runtime stage
FROM openjdk:21-jre-slim

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy the built jar
COPY --from=builder /app/services/user-service/target/user-service-*.jar app.jar

# Change ownership
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8083

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8083/user-service/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]
