import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Alert,
  CircularProgress,

  Paper,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import {
  ArrowBack,
  LocalShipping,
  CheckCircle,
  Schedule,
  LocationOn,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { websocketService, ParcelUpdate, LocationUpdate } from '../../services/websocketService';

interface ParcelData {
  trackingNumber: string;
  senderName: string;
  recipientName: string;
  senderAddress: string;
  recipientAddress: string;
  weight: number;
  status: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  timeline: Array<{
    timestamp: string;
    status: string;
    location: string;
    description: string;
  }>;
}

const LiveParcelTracker: React.FC = () => {
  const { trackingNumber } = useParams<{ trackingNumber: string }>();
  const navigate = useNavigate();
  
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const parcelMarkerRef = useRef<any>(null);
  
  const [parcelData, setParcelData] = useState<ParcelData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  // Initialize Google Maps
  const initializeMap = () => {
    if (!mapRef.current || !window.google || !parcelData?.currentLocation) return;

    const map = new window.google.maps.Map(mapRef.current, {
      center: {
        lat: parcelData.currentLocation.latitude,
        lng: parcelData.currentLocation.longitude,
      },
      zoom: 14,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    });

    mapInstanceRef.current = map;

    // Create parcel marker
    parcelMarkerRef.current = new window.google.maps.Marker({
      map,
      position: {
        lat: parcelData.currentLocation.latitude,
        lng: parcelData.currentLocation.longitude,
      },
      icon: {
        url: '/icons/parcel-marker.png',
        scaledSize: new window.google.maps.Size(40, 40),
      },
      title: 'موقع الطرد',
    });

    setIsMapLoaded(true);
  };

  // Update parcel location on map
  const updateParcelLocation = (location: LocationUpdate) => {
    if (!parcelMarkerRef.current) return;

    const newPosition = {
      lat: location.lat,
      lng: location.lng,
    };

    parcelMarkerRef.current.setPosition(newPosition);

    // Center map on parcel
    if (mapInstanceRef.current) {
      mapInstanceRef.current.panTo(newPosition);
    }

    // Update parcel data
    setParcelData(prev => prev ? {
      ...prev,
      currentLocation: {
        latitude: location.lat,
        longitude: location.lng,
        address: prev.currentLocation?.address || 'موقع محدث',
      }
    } : null);
  };

  // Load parcel data
  const loadParcelData = async () => {
    if (!trackingNumber) return;

    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockParcelData: ParcelData = {
        trackingNumber,
        senderName: 'أحمد محمد',
        recipientName: 'فاطمة علي',
        senderAddress: 'صنعاء، شارع الزبيري',
        recipientAddress: 'عدن، كريتر',
        weight: 2.5,
        status: 'IN_TRANSIT',
        currentLocation: {
          latitude: 15.3694,
          longitude: 44.1910,
          address: 'على الطريق إلى عدن',
        },
        timeline: [
          {
            timestamp: '2025-07-07T08:00:00Z',
            status: 'CREATED',
            location: 'صنعاء',
            description: 'تم إنشاء الطرد',
          },
          {
            timestamp: '2025-07-07T09:00:00Z',
            status: 'PICKED_UP',
            location: 'صنعاء، شارع الزبيري',
            description: 'تم استلام الطرد',
          },
          {
            timestamp: '2025-07-07T10:30:00Z',
            status: 'IN_TRANSIT',
            location: 'على الطريق',
            description: 'الطرد في الطريق إلى الوجهة',
          },
        ],
      };

      setParcelData(mockParcelData);
    } catch (err) {
      setError('حدث خطأ في جلب بيانات الطرد');
      console.error('Error loading parcel data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle parcel updates from WebSocket
  const handleParcelUpdate = (update: ParcelUpdate) => {
    console.log('Parcel update received:', update);
    
    if (update.location) {
      updateParcelLocation(update.location);
    }

    // Update parcel status if changed
    if (update.status && parcelData) {
      setParcelData(prev => prev ? { ...prev, status: update.status } : null);
    }

    // Add new timeline entry if there's a message
    if (update.message && parcelData) {
      const newTimelineEntry = {
        timestamp: new Date().toISOString(),
        status: update.status,
        location: update.location ? 'موقع محدث' : 'غير محدد',
        description: update.message,
      };

      setParcelData(prev => prev ? {
        ...prev,
        timeline: [...prev.timeline, newTimelineEntry],
      } : null);
    }
  };

  // Load Google Maps script
  useEffect(() => {
    const loadGoogleMaps = () => {
      if (window.google) {
        initializeMap();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&libraries=geometry,places`;
      script.async = true;
      script.defer = true;
      script.onload = initializeMap;
      document.head.appendChild(script);
    };

    if (parcelData?.currentLocation) {
      loadGoogleMaps();
    }
  }, [parcelData]);

  // Initialize WebSocket and load data
  useEffect(() => {
    loadParcelData();

    return () => {
      if (trackingNumber) {
        websocketService.unsubscribeFromParcelUpdates(trackingNumber);
      }
    };
  }, [trackingNumber]);

  // Subscribe to WebSocket updates
  useEffect(() => {
    if (trackingNumber && parcelData) {
      websocketService.subscribeToParcelUpdates(trackingNumber, handleParcelUpdate);
    }

    return () => {
      if (trackingNumber) {
        websocketService.unsubscribeFromParcelUpdates(trackingNumber);
      }
    };
  }, [trackingNumber, parcelData]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CREATED': return 'info';
      case 'PICKED_UP': return 'primary';
      case 'IN_TRANSIT': return 'warning';
      case 'DELIVERED': return 'success';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'CREATED': return 'تم الإنشاء';
      case 'PICKED_UP': return 'تم الاستلام';
      case 'IN_TRANSIT': return 'في الطريق';
      case 'DELIVERED': return 'تم التسليم';
      case 'CANCELLED': return 'ملغي';
      default: return status;
    }
  };

  const getTimelineIcon = (status: string) => {
    switch (status) {
      case 'CREATED': return <Schedule />;
      case 'PICKED_UP': return <LocalShipping />;
      case 'IN_TRANSIT': return <LocationOn />;
      case 'DELIVERED': return <CheckCircle />;
      default: return <Schedule />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>جاري تحميل بيانات الطرد...</Typography>
      </Box>
    );
  }

  if (error || !parcelData) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'لم يتم العثور على الطرد'}</Alert>
        <Button onClick={() => navigate('/parcels')} sx={{ mt: 2 }}>
          العودة إلى قائمة الطرود
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/parcels')}>
                <ArrowBack />
              </IconButton>
              <Typography variant="h6">
                تتبع الطرد {trackingNumber}
              </Typography>
              <Chip
                label={getStatusLabel(parcelData.status)}
                color={getStatusColor(parcelData.status) as any}
                variant="outlined"
              />
            </Box>
          </Box>
          
          <Box sx={{ mt: 2, display: 'flex', gap: 4 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">المرسل</Typography>
              <Typography variant="body1">{parcelData.senderName}</Typography>
              <Typography variant="caption">{parcelData.senderAddress}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">المستقبل</Typography>
              <Typography variant="body1">{parcelData.recipientName}</Typography>
              <Typography variant="caption">{parcelData.recipientAddress}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">الوزن</Typography>
              <Typography variant="body1">{parcelData.weight} كيلو</Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Map */}
        <Card sx={{ flex: 2 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>الموقع الحالي</Typography>
            <Box
              ref={mapRef}
              sx={{
                width: '100%',
                height: 400,
                borderRadius: 1,
                position: 'relative',
              }}
            />
            {!isMapLoaded && parcelData.currentLocation && (
              <Box sx={{ 
                position: 'absolute', 
                top: '50%', 
                left: '50%', 
                transform: 'translate(-50%, -50%)',
                display: 'flex',
                alignItems: 'center',
                gap: 2
              }}>
                <CircularProgress />
                <Typography>جاري تحميل الخريطة...</Typography>
              </Box>
            )}
            {parcelData.currentLocation && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                {parcelData.currentLocation.address}
              </Typography>
            )}
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>مسار الطرد</Typography>
            <Timeline>
              {parcelData.timeline.map((event, index) => (
                <TimelineItem key={index}>
                  <TimelineSeparator>
                    <TimelineDot color={getStatusColor(event.status) as any}>
                      {getTimelineIcon(event.status)}
                    </TimelineDot>
                    {index < parcelData.timeline.length - 1 && <TimelineConnector />}
                  </TimelineSeparator>
                  <TimelineContent>
                    <Paper sx={{ p: 2, mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {getStatusLabel(event.status)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(event.timestamp).toLocaleString('ar-SA')}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {event.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        📍 {event.location}
                      </Typography>
                    </Paper>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default LiveParcelTracker;
