import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Api as ApiIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Business as BusinessIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
  Upload as UploadIcon,
  Star as StarIcon,
  Assessment as AssessmentIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from 'recharts';
import { saasService, TenantDto, UsageAnalyticsDto } from '../../services/saasService';

const UsageAnalytics: React.FC = () => {
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [selectedTenant, setSelectedTenant] = useState<string>('ALL');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('last_30_days');
  const [usageData, setUsageData] = useState<{ [key: string]: UsageAnalyticsDto }>({});
  const [loading, setLoading] = useState(false);

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load tenants
      const tenantsResponse = await saasService.getTenants();
      if (tenantsResponse.success && tenantsResponse.data) {
        setTenants(tenantsResponse.data);
        
        // Load usage data for each tenant
        const usageDataMap: { [key: string]: UsageAnalyticsDto } = {};
        for (const tenant of tenantsResponse.data) {
          const usageResponse = await saasService.getUsageAnalytics(tenant.id, selectedPeriod);
          if (usageResponse.success && usageResponse.data) {
            usageDataMap[tenant.id] = usageResponse.data;
          }
        }
        setUsageData(usageDataMap);
      }

    } catch (error) {
      console.error('Error loading usage analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedPeriod]);

  // Mock data for charts
  const usageOverTimeData = [
    { date: '2025-06-01', users: 120, apiCalls: 8500, storage: 85.2 },
    { date: '2025-06-08', users: 135, apiCalls: 9200, storage: 88.1 },
    { date: '2025-06-15', users: 142, apiCalls: 10100, storage: 92.5 },
    { date: '2025-06-22', users: 158, apiCalls: 11800, storage: 96.8 },
    { date: '2025-06-29', users: 165, apiCalls: 12500, storage: 101.2 },
    { date: '2025-07-06', users: 172, apiCalls: 13200, storage: 105.6 },
  ];

  const featureUsageData = [
    { feature: 'إدارة المستخدمين', usage: 85, color: '#8884d8' },
    { feature: 'التقارير', usage: 72, color: '#82ca9d' },
    { feature: 'التحليلات', usage: 58, color: '#ffc658' },
    { feature: 'API المخصص', usage: 91, color: '#ff7300' },
    { feature: 'التكامل', usage: 45, color: '#00ff88' },
  ];

  const tenantComparisonData = tenants.slice(0, 5).map(tenant => {
    const usage = usageData[tenant.id];
    return {
      name: tenant.name,
      users: usage?.activeUsers || 0,
      apiCalls: usage?.totalApiCalls || 0,
      storage: usage?.dataStorage || 0,
    };
  });

  const performanceMetrics = [
    { metric: 'متوسط وقت الاستجابة', value: '245ms', trend: 'down', color: 'success' },
    { metric: 'معدل الأخطاء', value: '0.12%', trend: 'down', color: 'success' },
    { metric: 'وقت التشغيل', value: '99.98%', trend: 'up', color: 'success' },
    { metric: 'رضا المستخدمين', value: '4.8/5', trend: 'up', color: 'success' },
  ];

  // Calculate aggregate stats
  const totalUsers = Object.values(usageData).reduce((sum, data) => sum + (data.activeUsers || 0), 0);
  const totalApiCalls = Object.values(usageData).reduce((sum, data) => sum + (data.totalApiCalls || 0), 0);
  const totalStorage = Object.values(usageData).reduce((sum, data) => sum + (data.dataStorage || 0), 0);
  const avgBandwidth = Object.values(usageData).length > 0 ? 
    Object.values(usageData).reduce((sum, data) => sum + (data.bandwidth || 0), 0) / Object.values(usageData).length : 0;

  const filteredUsageData = selectedTenant === 'ALL' ? 
    Object.values(usageData) : 
    usageData[selectedTenant] ? [usageData[selectedTenant]] : [];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          تحليلات الاستخدام
        </Typography>
        <Typography variant="body1" color="text.secondary">
          مراقبة وتحليل استخدام النظام والمميزات
        </Typography>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>العميل</InputLabel>
              <Select
                value={selectedTenant}
                label="العميل"
                onChange={(e) => setSelectedTenant(e.target.value)}
              >
                <MenuItem value="ALL">جميع العملاء</MenuItem>
                {tenants.map((tenant) => (
                  <MenuItem key={tenant.id} value={tenant.id}>
                    {tenant.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الفترة الزمنية</InputLabel>
              <Select
                value={selectedPeriod}
                label="الفترة الزمنية"
                onChange={(e) => setSelectedPeriod(e.target.value)}
              >
                <MenuItem value="last_7_days">آخر 7 أيام</MenuItem>
                <MenuItem value="last_30_days">آخر 30 يوم</MenuItem>
                <MenuItem value="last_90_days">آخر 90 يوم</MenuItem>
                <MenuItem value="last_year">آخر سنة</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
              disabled={loading}
            >
              تحديث
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => console.log('Export analytics report')}
            >
              تصدير التقرير
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <PeopleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {totalUsers.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المستخدمين النشطين
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <ApiIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {totalApiCalls.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي استدعاءات API
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <StorageIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {totalStorage.toFixed(1)} GB
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي استهلاك التخزين
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <SpeedIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {avgBandwidth.toFixed(1)} GB
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط استهلاك البيانات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Usage Over Time Chart */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, lg: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                اتجاه الاستخدام عبر الوقت
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={usageOverTimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="users" stackId="1" stroke="#8884d8" fill="#8884d8" name="المستخدمين" />
                  <Area type="monotone" dataKey="apiCalls" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="استدعاءات API" />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                مؤشرات الأداء
              </Typography>
              <List>
                {performanceMetrics.map((metric, index) => (
                  <ListItem key={index}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: `${metric.color}.main` }}>
                        <TrendingUpIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={metric.metric}
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="h6" component="span">
                            {metric.value}
                          </Typography>
                          <Chip
                            label={metric.trend === 'up' ? '↗️' : '↘️'}
                            size="small"
                            color={metric.color as any}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Feature Usage and Tenant Comparison */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                استخدام المميزات
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={featureUsageData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="feature" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar
                    name="الاستخدام %"
                    dataKey="usage"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                مقارنة العملاء
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={tenantComparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="users" fill="#8884d8" name="المستخدمين" />
                  <Bar dataKey="apiCalls" fill="#82ca9d" name="استدعاءات API" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Usage Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            تفاصيل الاستخدام حسب العميل
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>العميل</TableCell>
                  <TableCell align="right">المستخدمين النشطين</TableCell>
                  <TableCell align="right">استدعاءات API</TableCell>
                  <TableCell align="right">التخزين (GB)</TableCell>
                  <TableCell align="right">البيانات (GB)</TableCell>
                  <TableCell align="right">معدل الاستخدام</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tenants.map((tenant) => {
                  const usage = usageData[tenant.id];
                  const usageRate = usage ? 
                    Math.round(((usage.activeUsers || 0) / (tenant.totalUsers || 1)) * 100) : 0;
                  
                  return (
                    <TableRow key={tenant.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                            <BusinessIcon fontSize="small" />
                          </Avatar>
                          {tenant.name}
                        </Box>
                      </TableCell>
                      <TableCell align="right">{usage?.activeUsers || 0}</TableCell>
                      <TableCell align="right">{usage?.totalApiCalls?.toLocaleString() || 0}</TableCell>
                      <TableCell align="right">{usage?.dataStorage?.toFixed(1) || 0}</TableCell>
                      <TableCell align="right">{usage?.bandwidth?.toFixed(1) || 0}</TableCell>
                      <TableCell align="right">
                        <Chip
                          label={`${usageRate}%`}
                          color={usageRate > 80 ? 'success' : usageRate > 50 ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default UsageAnalytics;
