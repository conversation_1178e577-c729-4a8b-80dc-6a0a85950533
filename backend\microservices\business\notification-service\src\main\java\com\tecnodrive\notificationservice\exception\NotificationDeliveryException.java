package com.tecnodrive.notificationservice.exception;

/**
 * Notification Delivery Exception
 * 
 * Thrown when notification delivery fails
 */
public class NotificationDeliveryException extends RuntimeException {

    public NotificationDeliveryException() {
        super("Failed to deliver notification");
    }

    public NotificationDeliveryException(String message) {
        super(message);
    }

    public NotificationDeliveryException(String message, Throwable cause) {
        super(message, cause);
    }
}
