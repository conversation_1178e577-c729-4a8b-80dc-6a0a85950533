package com.tecnodrive.parcelservice.entity;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.UUID;

/**
 * Pa<PERSON>el Entity (Simplified)
 */
@Entity
@Table(name = "parcels")
public class Parcel {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(nullable = false)
    private UUID senderId;

    @Column(nullable = false)
    private UUID receiverId;

    @Column(nullable = false)
    private double weight;

    private String dimensions;

    @Column(nullable = false)
    private String status = "CREATED";

    @Column(nullable = false, unique = true)
    private String trackingNumber;

    private Instant createdAt = Instant.now();

    // Constructors
    public Parcel() {}

    public Parcel(UUID senderId, UUID receiverId, double weight, String dimensions) {
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.weight = weight;
        this.dimensions = dimensions;
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public UUID getSenderId() { return senderId; }
    public void setSenderId(UUID senderId) { this.senderId = senderId; }

    public UUID getReceiverId() { return receiverId; }
    public void setReceiverId(UUID receiverId) { this.receiverId = receiverId; }

    public double getWeight() { return weight; }
    public void setWeight(double weight) { this.weight = weight; }

    public String getDimensions() { return dimensions; }
    public void setDimensions(String dimensions) { this.dimensions = dimensions; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getTrackingNumber() { return trackingNumber; }
    public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    @Override
    public String toString() {
        return "Parcel{" +
                "id=" + id +
                ", trackingNumber='" + trackingNumber + '\'' +
                ", status='" + status + '\'' +
                ", weight=" + weight +
                '}';
    }
}
