import React from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Avatar,
  <PERSON>,
  IconButton,
  Toolt<PERSON>,
} from '@mui/material';
import {
  Add,
  DirectionsCar,
  People,
  LocalShipping,
  Analytics,
  Settings,
  Refresh,
  Launch,
  Speed,
} from '@mui/icons-material';

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
  badge?: string;
  disabled?: boolean;
}

const QuickActions: React.FC = () => {
  const quickActions: QuickAction[] = [
    {
      title: 'إضافة رحلة جديدة',
      description: 'إنشاء رحلة جديدة في النظام',
      icon: <Add />,
      color: '#667eea',
      action: () => console.log('Add new ride'),
      badge: 'جديد',
    },
    {
      title: 'إدارة الأسطول',
      description: 'عرض وإدارة المركبات',
      icon: <DirectionsCar />,
      color: '#4299e1',
      action: () => console.log('Manage fleet'),
    },
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل المستخدمين',
      icon: <People />,
      color: '#48bb78',
      action: () => console.log('Manage users'),
    },
    {
      title: 'تتبع الطرود',
      description: 'مراقبة حالة الطرود',
      icon: <LocalShipping />,
      color: '#ed8936',
      action: () => console.log('Track parcels'),
      badge: 'مهم',
    },
    {
      title: 'التحليلات',
      description: 'عرض تقارير الأداء',
      icon: <Analytics />,
      color: '#9f7aea',
      action: () => console.log('View analytics'),
    },
    {
      title: 'الإعدادات',
      description: 'تكوين النظام',
      icon: <Settings />,
      color: '#718096',
      action: () => console.log('Open settings'),
    },
  ];

  const systemActions = [
    {
      title: 'تحديث البيانات',
      icon: <Refresh />,
      color: '#4299e1',
      action: () => window.location.reload(),
    },
    {
      title: 'فتح المراقبة',
      icon: <Launch />,
      color: '#48bb78',
      action: () => window.open('http://localhost:8761', '_blank'),
    },
    {
      title: 'اختبار الأداء',
      icon: <Speed />,
      color: '#ed8936',
      action: () => console.log('Performance test'),
    },
  ];

  return (
    <Box>
      {/* Quick Actions Grid */}
      <Card className="enhanced-card" sx={{ mb: 3 }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #667eea, #764ba2)',
                  mr: 2,
                }}
              >
                ⚡
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  إجراءات سريعة
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الوصول السريع للمهام الأساسية
                </Typography>
              </Box>
            </Box>
          </Box>

          <Grid container spacing={2}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  className="scale-in"
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    animationDelay: `${index * 0.1}s`,
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 24px rgba(0, 0, 0, 0.15)',
                      background: 'rgba(255, 255, 255, 0.95)',
                    },
                  }}
                  onClick={action.action}
                >
                  <CardContent sx={{ p: 2.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          background: `${action.color}20`,
                          color: action.color,
                        }}
                      >
                        {action.icon}
                      </Avatar>
                      {action.badge && (
                        <Chip
                          label={action.badge}
                          size="small"
                          sx={{
                            background: action.color,
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.75rem',
                          }}
                        />
                      )}
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* System Actions */}
      <Card className="enhanced-card">
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #48bb78, #38a169)',
                mr: 2,
              }}
            >
              🔧
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>
                أدوات النظام
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إدارة وصيانة النظام
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {systemActions.map((action, index) => (
              <Tooltip key={index} title={action.title}>
                <Button
                  variant="contained"
                  startIcon={action.icon}
                  onClick={action.action}
                  sx={{
                    background: `linear-gradient(135deg, ${action.color}, ${action.color}cc)`,
                    color: 'white',
                    fontWeight: 600,
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    '&:hover': {
                      background: `linear-gradient(135deg, ${action.color}dd, ${action.color}aa)`,
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)',
                    },
                  }}
                >
                  {action.title}
                </Button>
              </Tooltip>
            ))}
          </Box>

          {/* Quick Links */}
          <Box sx={{ mt: 3, p: 2, borderRadius: 2, background: 'rgba(102, 126, 234, 0.1)' }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#667eea' }}>
              روابط سريعة
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                size="small"
                onClick={() => window.open('http://localhost:8761', '_blank')}
                sx={{ color: '#667eea', fontWeight: 500 }}
              >
                Eureka Dashboard
              </Button>
              <Button
                size="small"
                onClick={() => window.open('http://localhost:9090', '_blank')}
                sx={{ color: '#667eea', fontWeight: 500 }}
              >
                Prometheus
              </Button>
              <Button
                size="small"
                onClick={() => window.open('http://localhost:3001', '_blank')}
                sx={{ color: '#667eea', fontWeight: 500 }}
              >
                Grafana
              </Button>
              <Button
                size="small"
                onClick={() => window.open('http://localhost:3002', '_blank')}
                sx={{ color: '#667eea', fontWeight: 500 }}
              >
                HR System
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default QuickActions;
