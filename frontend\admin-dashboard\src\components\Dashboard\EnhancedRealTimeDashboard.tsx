import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Alert,
  Button,
  IconButton,
  Tooltip,
  Badge,
  Switch,
  FormControlLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Dashboard,
  TrendingUp,
  Warning,
  CheckCircle,
  Error,
  Info,
  Refresh,
  Pause,
  PlayArrow,
  Notifications,
  Speed,
  Memory,
  NetworkCheck,
  Timeline,
  Map,
  Event,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import realTimeDashboardService, { 
  DashboardMetrics, 
  Alert as DashboardAlert, 
  LiveEvent, 
  PerformanceMetrics,
  GeospatialEvent,
  DomainEvent 
} from '../../services/realTimeDashboardService';

const EnhancedRealTimeDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [alerts, setAlerts] = useState<DashboardAlert[]>([]);
  const [liveEvents, setLiveEvents] = useState<LiveEvent[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics[]>([]);
  const [geospatialEvents, setGeospatialEvents] = useState<GeospatialEvent[]>([]);
  const [domainEvents, setDomainEvents] = useState<DomainEvent[]>([]);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState(realTimeDashboardService.getConnectionStatus());
  const [eventBuffer, setEventBuffer] = useState<LiveEvent[]>([]);

  const tenantId = localStorage.getItem('tenantId') || 'default';
  const metricsHistoryRef = useRef<DashboardMetrics[]>([]);
  const performanceHistoryRef = useRef<PerformanceMetrics[]>([]);

  useEffect(() => {
    setupRealTimeSubscriptions();
    startRealTimeUpdates();

    return () => {
      realTimeDashboardService.disconnect();
    };
  }, []);

  const setupRealTimeSubscriptions = () => {
    // Dashboard metrics subscription
    const unsubscribeMetrics = realTimeDashboardService.subscribe('metrics', (newMetrics: DashboardMetrics) => {
      setMetrics(newMetrics);
      
      // Keep history for trends
      metricsHistoryRef.current = [...metricsHistoryRef.current, newMetrics].slice(-50);
    });

    // Live events subscription
    const unsubscribeEvents = realTimeDashboardService.subscribe('events', (event: LiveEvent) => {
      setLiveEvents(prev => [event, ...prev.slice(0, 19)]); // Keep only 20 latest
      
      // Add to event buffer for analysis
      setEventBuffer(prev => [event, ...prev.slice(0, 99)]); // Keep 100 events
    });

    // Performance metrics subscription
    const unsubscribePerformance = realTimeDashboardService.subscribe('performance', (perfMetrics: PerformanceMetrics) => {
      setPerformanceMetrics(prev => [perfMetrics, ...prev.slice(0, 29)]); // Keep 30 data points
      performanceHistoryRef.current = [...performanceHistoryRef.current, perfMetrics].slice(-30);
    });

    // Geospatial events subscription
    const unsubscribeGeospatial = realTimeDashboardService.subscribe('geospatial', (geoEvent: GeospatialEvent) => {
      setGeospatialEvents(prev => [geoEvent, ...prev.slice(0, 49)]); // Keep 50 events
    });

    // Alerts subscription
    const unsubscribeAlerts = realTimeDashboardService.subscribe('alerts', (alert: DashboardAlert) => {
      setAlerts(prev => [alert, ...prev.slice(0, 9)]); // Keep 10 alerts
    });

    // Domain events subscription (Event Sourcing)
    const unsubscribeDomainEvents = realTimeDashboardService.subscribe('domain-events', (domainEvent: DomainEvent) => {
      setDomainEvents(prev => [domainEvent, ...prev.slice(0, 19)]); // Keep 20 events
    });

    // Connection status updates
    realTimeDashboardService.on('connected', () => {
      setConnectionStatus(realTimeDashboardService.getConnectionStatus());
    });

    realTimeDashboardService.on('disconnected', () => {
      setConnectionStatus(realTimeDashboardService.getConnectionStatus());
    });

    realTimeDashboardService.on('reconnecting', (attemptNumber: number) => {
      setConnectionStatus(realTimeDashboardService.getConnectionStatus());
    });

    return () => {
      unsubscribeMetrics();
      unsubscribeEvents();
      unsubscribePerformance();
      unsubscribeGeospatial();
      unsubscribeAlerts();
      unsubscribeDomainEvents();
    };
  };

  const startRealTimeUpdates = () => {
    if (isRealTimeEnabled) {
      realTimeDashboardService.subscribeToDashboardMetrics(tenantId);
      realTimeDashboardService.subscribeToLiveEvents(tenantId);
      realTimeDashboardService.subscribeToPerformanceMetrics();
      realTimeDashboardService.subscribeToGeospatialEvents(tenantId);
      realTimeDashboardService.subscribeToAlerts(tenantId);
      realTimeDashboardService.subscribeToDomainEvents(tenantId);
    }
  };

  const handleRealTimeToggle = (enabled: boolean) => {
    setIsRealTimeEnabled(enabled);
    if (enabled) {
      startRealTimeUpdates();
    }
  };

  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      await realTimeDashboardService.acknowledgeAlert(alertId);
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'info': return 'info';
      default: return 'default';
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'trip_started': return <Timeline color="primary" />;
      case 'trip_completed': return <CheckCircle color="success" />;
      case 'driver_online': return <CheckCircle color="info" />;
      case 'vehicle_maintenance': return <Warning color="warning" />;
      case 'payment_processed': return <TrendingUp color="success" />;
      case 'risk_detected': return <Error color="error" />;
      default: return <Event />;
    }
  };

  const getConnectionStatusColor = () => {
    if (connectionStatus.connected) return 'success';
    if (connectionStatus.reconnectAttempts > 0) return 'warning';
    return 'error';
  };

  const formatMetricValue = (value: number, type: string) => {
    switch (type) {
      case 'currency':
        return `$${value.toLocaleString()}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'time':
        return `${value.toFixed(1)}min`;
      default:
        return value.toLocaleString();
    }
  };

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Enhanced Real-time Dashboard
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <FormControlLabel
            control={
              <Switch
                checked={isRealTimeEnabled}
                onChange={(e) => handleRealTimeToggle(e.target.checked)}
                color="primary"
              />
            }
            label="Real-time Updates"
          />
          <Chip
            icon={<NetworkCheck />}
            label={connectionStatus.connected ? 'Connected' : 'Disconnected'}
            color={getConnectionStatusColor() as any}
            variant={connectionStatus.connected ? 'filled' : 'outlined'}
          />
          {connectionStatus.reconnectAttempts > 0 && (
            <Chip
              label={`Reconnecting... (${connectionStatus.reconnectAttempts})`}
              color="warning"
              size="small"
            />
          )}
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Timeline color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Trips
                  </Typography>
                  <Typography variant="h5">
                    {metrics?.activeTrips || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Revenue Today
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {formatMetricValue(metrics?.totalRevenue || 0, 'currency')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Speed color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Wait Time
                  </Typography>
                  <Typography variant="h5" color="info.main">
                    {formatMetricValue(metrics?.averageWaitTime || 0, 'time')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <CheckCircle color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    System Health
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {formatMetricValue(metrics?.systemHealth || 0, 'percentage')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Performance Metrics Chart */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Performance
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceHistoryRef.current}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                  <YAxis />
                  <RechartsTooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number, name: string) => [
                      name === 'cpu' || name === 'memory' ? `${value.toFixed(1)}%` : value.toFixed(2),
                      name.toUpperCase()
                    ]}
                  />
                  <Area type="monotone" dataKey="cpu" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="memory" stackId="1" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="responseTime" stackId="2" stroke="#ffc658" fill="#ffc658" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Performance
              </Typography>
              {performanceMetrics[0] && (
                <Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">CPU Usage</Typography>
                    <Typography variant="body2" color="primary">
                      {performanceMetrics[0].cpu.toFixed(1)}%
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">Memory Usage</Typography>
                    <Typography variant="body2" color="secondary">
                      {performanceMetrics[0].memory.toFixed(1)}%
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">Response Time</Typography>
                    <Typography variant="body2" color="warning.main">
                      {performanceMetrics[0].responseTime.toFixed(0)}ms
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">Throughput</Typography>
                    <Typography variant="body2" color="success.main">
                      {performanceMetrics[0].throughput.toFixed(0)} req/s
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body2">Error Rate</Typography>
                    <Typography variant="body2" color="error.main">
                      {performanceMetrics[0].errorRate.toFixed(2)}%
                    </Typography>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alerts and Live Events */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Active Alerts
                </Typography>
                <Badge badgeContent={alerts.filter(a => !a.acknowledged).length} color="error">
                  <Notifications />
                </Badge>
              </Box>
              <List>
                {alerts.slice(0, 5).map((alert) => (
                  <React.Fragment key={alert.id}>
                    <ListItem>
                      <ListItemIcon>
                        {alert.type === 'critical' ? <Error color="error" /> :
                         alert.type === 'warning' ? <Warning color="warning" /> :
                         alert.type === 'info' ? <Info color="info" /> :
                         <CheckCircle color="success" />}
                      </ListItemIcon>
                      <ListItemText
                        primary={alert.title}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              {alert.message}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {new Date(alert.timestamp).toLocaleString()} - {alert.source}
                            </Typography>
                          </Box>
                        }
                      />
                      {!alert.acknowledged && (
                        <Button
                          size="small"
                          onClick={() => handleAcknowledgeAlert(alert.id)}
                          color="primary"
                        >
                          Acknowledge
                        </Button>
                      )}
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Live Events Stream
              </Typography>
              <List>
                {liveEvents.slice(0, 8).map((event) => (
                  <React.Fragment key={event.id}>
                    <ListItem>
                      <ListItemIcon>
                        {getEventTypeIcon(event.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2">
                              {event.type.replace('_', ' ').toUpperCase()}
                            </Typography>
                            <Chip
                              label={event.severity}
                              size="small"
                              color={getAlertSeverityColor(event.severity) as any}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              {JSON.stringify(event.data).substring(0, 100)}...
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {new Date(event.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Domain Events (Event Sourcing) */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Domain Events Stream
              </Typography>
              <List>
                {domainEvents.slice(0, 6).map((event) => (
                  <React.Fragment key={event.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Event color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2">
                              {event.eventType}
                            </Typography>
                            <Chip
                              label={event.aggregateType}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Aggregate: {event.aggregateId} | Version: {event.version}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {new Date(event.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Geospatial Events
              </Typography>
              <List>
                {geospatialEvents.slice(0, 6).map((event) => (
                  <React.Fragment key={event.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Map color="secondary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2">
                            {event.type.replace('_', ' ').toUpperCase()}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Coordinates: [{event.coordinates[0].toFixed(4)}, {event.coordinates[1].toFixed(4)}]
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {new Date(event.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Event Buffer Analytics */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Event Analytics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {eventBuffer.length}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Events in Buffer
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {eventBuffer.filter(e => e.severity === 'high' || e.severity === 'critical').length}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  High Priority Events
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {eventBuffer.filter(e => e.type === 'trip_completed').length}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Completed Trips
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default EnhancedRealTimeDashboard;
