package com.tecnodrive.parcelservice.dto;

import com.tecnodrive.parcelservice.entity.Delivery;
import com.tecnodrive.parcelservice.entity.Parcel;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ParcelMapperImpl implements ParcelMapper {

    @Override
    public ParcelEntity toEntity(ParcelDto dto) {
        if ( dto == null ) {
            return null;
        }

        ParcelEntity.ParcelEntityBuilder parcelEntity = ParcelEntity.builder();

        parcelEntity.actualDeliveryDate( dto.getActualDeliveryDate() );
        parcelEntity.barcode( dto.getBarcode() );
        parcelEntity.dimensions( dimensionsDtoToDimensions( dto.getDimensions() ) );
        parcelEntity.estimatedCost( dto.getEstimatedCost() );
        parcelEntity.estimatedDeliveryDate( dto.getEstimatedDeliveryDate() );
        parcelEntity.fragile( dto.getFragile() );
        parcelEntity.insuranceValue( dto.getInsuranceValue() );
        parcelEntity.notes( dto.getNotes() );
        if ( dto.getPriority() != null ) {
            parcelEntity.priority( Enum.valueOf( ParcelEntity.ParcelPriority.class, dto.getPriority() ) );
        }
        parcelEntity.receiverAddress( dto.getReceiverAddress() );
        parcelEntity.receiverName( dto.getReceiverName() );
        parcelEntity.senderAddress( dto.getSenderAddress() );
        parcelEntity.senderName( dto.getSenderName() );
        parcelEntity.userId( dto.getUserId() );
        parcelEntity.weightKg( dto.getWeightKg() );

        return parcelEntity.build();
    }

    @Override
    public ParcelDto toDto(ParcelEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ParcelDto.ParcelDtoBuilder parcelDto = ParcelDto.builder();

        parcelDto.actualDeliveryDate( entity.getActualDeliveryDate() );
        parcelDto.barcode( entity.getBarcode() );
        parcelDto.createdAt( entity.getCreatedAt() );
        parcelDto.dimensions( dimensionsToDimensionsDto( entity.getDimensions() ) );
        parcelDto.estimatedDeliveryDate( entity.getEstimatedDeliveryDate() );
        parcelDto.fragile( entity.getFragile() );
        parcelDto.insuranceValue( entity.getInsuranceValue() );
        parcelDto.notes( entity.getNotes() );
        parcelDto.parcelId( entity.getParcelId() );
        if ( entity.getPriority() != null ) {
            parcelDto.priority( entity.getPriority().name() );
        }
        parcelDto.receiverAddress( entity.getReceiverAddress() );
        parcelDto.receiverName( entity.getReceiverName() );
        parcelDto.senderAddress( entity.getSenderAddress() );
        parcelDto.senderName( entity.getSenderName() );
        if ( entity.getStatus() != null ) {
            parcelDto.status( entity.getStatus().name() );
        }
        parcelDto.updatedAt( entity.getUpdatedAt() );
        parcelDto.userId( entity.getUserId() );
        parcelDto.weightKg( entity.getWeightKg() );

        return parcelDto.build();
    }

    @Override
    public Parcel toLegacyEntity(ParcelRequestDto dto) {
        if ( dto == null ) {
            return null;
        }

        Parcel parcel = new Parcel();

        parcel.setSenderId( dto.getSenderId() );
        parcel.setReceiverId( dto.getReceiverId() );
        parcel.setWeight( dto.getWeight() );
        parcel.setDimensions( dto.getDimensions() );

        return parcel;
    }

    @Override
    public ParcelResponseDto toLegacyDto(Parcel parcel) {
        if ( parcel == null ) {
            return null;
        }

        ParcelResponseDto parcelResponseDto = new ParcelResponseDto();

        parcelResponseDto.setId( parcel.getId() );
        parcelResponseDto.setStatus( parcel.getStatus() );
        parcelResponseDto.setTrackingNumber( parcel.getTrackingNumber() );
        parcelResponseDto.setCreatedAt( parcel.getCreatedAt() );

        return parcelResponseDto;
    }

    @Override
    public Delivery toDeliveryEntity(DeliveryDto dto) {
        if ( dto == null ) {
            return null;
        }

        Delivery delivery = new Delivery();

        delivery.setParcelId( dto.getParcelId() );
        delivery.setDriverId( dto.getDriverId() );

        return delivery;
    }

    protected ParcelEntity.Dimensions dimensionsDtoToDimensions(ParcelDto.DimensionsDto dimensionsDto) {
        if ( dimensionsDto == null ) {
            return null;
        }

        ParcelEntity.Dimensions.DimensionsBuilder dimensions = ParcelEntity.Dimensions.builder();

        dimensions.heightCm( dimensionsDto.getHeightCm() );
        dimensions.lengthCm( dimensionsDto.getLengthCm() );
        dimensions.widthCm( dimensionsDto.getWidthCm() );

        return dimensions.build();
    }

    protected ParcelDto.DimensionsDto dimensionsToDimensionsDto(ParcelEntity.Dimensions dimensions) {
        if ( dimensions == null ) {
            return null;
        }

        ParcelDto.DimensionsDto.DimensionsDtoBuilder dimensionsDto = ParcelDto.DimensionsDto.builder();

        dimensionsDto.heightCm( dimensions.getHeightCm() );
        dimensionsDto.lengthCm( dimensions.getLengthCm() );
        dimensionsDto.widthCm( dimensions.getWidthCm() );

        return dimensionsDto.build();
    }
}
