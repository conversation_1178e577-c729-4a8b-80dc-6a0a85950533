# Start Missing TecnoDrive Services
Write-Host "🚀 Starting Missing TecnoDrive Services" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to start a service
function Start-Service {
    param([string]$ServiceName, [int]$Port, [string]$JarPath)
    
    if (Test-Port -Port $Port) {
        Write-Host "✅ $ServiceName already running on port $Port" -ForegroundColor Green
        return $true
    }
    
    if (-not (Test-Path $JarPath)) {
        Write-Host "❌ $ServiceName JAR not found: $JarPath" -ForegroundColor Red
        return $false
    }
    
    Write-Host "🔄 Starting $ServiceName on port $Port..." -ForegroundColor Yellow
    
    try {
        # Start the service in background
        $process = Start-Process -FilePath "java" -ArgumentList @(
            "-jar", 
            $JarPath,
            "--server.port=$Port",
            "--spring.profiles.active=dev"
        ) -WindowStyle Hidden -PassThru
        
        # Wait a moment for startup
        Start-Sleep -Seconds 5
        
        if (Test-Port -Port $Port) {
            Write-Host "✅ $ServiceName started successfully (PID: $($process.Id))" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $ServiceName may still be starting..." -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Failed to start ${ServiceName}: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n🔍 Checking current service status..." -ForegroundColor Yellow

# Define services to start
$services = @(
    @{ 
        Name = "Parcel Service"
        Port = 8087
        JarPath = "services/parcel-service/target/parcel-service-1.0.0.jar"
    },
    @{ 
        Name = "Notification Service"
        Port = 8088
        JarPath = "services/notification-service/target/notification-service-1.0.0.jar"
    },
    @{ 
        Name = "Analytics Service"
        Port = 8089
        JarPath = "services/analytics-service/target/analytics-service-1.0.0.jar"
    },
    @{ 
        Name = "HR Service"
        Port = 8090
        JarPath = "services/hr-service/target/hr-service-1.0.0.jar"
    },
    @{ 
        Name = "Financial Service"
        Port = 8091
        JarPath = "services/financial-service/target/financial-service-1.0.0.jar"
    },
    @{ 
        Name = "SaaS Management Service"
        Port = 8092
        JarPath = "services/saas-management-service/target/saas-management-service-1.0.0.jar"
    }
)

$startedServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    if (Start-Service -ServiceName $service.Name -Port $service.Port -JarPath $service.JarPath) {
        $startedServices++
    }
}

Write-Host "`n📊 Service Startup Summary" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host "Started: $startedServices/$totalServices services" -ForegroundColor White

if ($startedServices -eq $totalServices) {
    Write-Host "🎉 All services started successfully!" -ForegroundColor Green
} elseif ($startedServices -gt 0) {
    Write-Host "⚠️ Some services started. Check logs for failed services." -ForegroundColor Yellow
} else {
    Write-Host "❌ No services could be started. Check JAR files and ports." -ForegroundColor Red
}

Write-Host "`n🔍 Testing Service Health..." -ForegroundColor Yellow

# Wait a bit more for services to fully start
Start-Sleep -Seconds 10

foreach ($service in $services) {
    $healthUrl = "http://localhost:$($service.Port)/actuator/health"
    try {
        $response = Invoke-RestMethod -Uri $healthUrl -Method GET -TimeoutSec 5
        if ($response.status -eq "UP") {
            Write-Host "✅ $($service.Name): Health check passed" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name): Health check returned: $($response.status)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name): Health check failed" -ForegroundColor Red
    }
}

Write-Host "`n🌐 Service URLs:" -ForegroundColor Yellow
foreach ($service in $services) {
    Write-Host "   • $($service.Name): http://localhost:$($service.Port)/actuator/health" -ForegroundColor White
}

Write-Host "`n💡 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Wait 1-2 minutes for all services to fully start" -ForegroundColor White
Write-Host "   2. Run: .\test-smart-integration.ps1" -ForegroundColor White
Write-Host "   3. Check frontend dashboard for updated service status" -ForegroundColor White
Write-Host "   4. Navigate to http://localhost:3000 to see the improvements" -ForegroundColor White

Write-Host "`n🎯 Expected Improvements:" -ForegroundColor Green
Write-Host "   • Service health should improve from ~58% to ~100%" -ForegroundColor White
Write-Host "   • More pages will use real data instead of mock data" -ForegroundColor White
Write-Host "   • Service status indicator will show more green indicators" -ForegroundColor White

Write-Host "`n🚀 Services Starting Complete!" -ForegroundColor Green
