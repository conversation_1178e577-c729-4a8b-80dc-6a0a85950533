{{- if .Values.gatekeeper.enabled }}
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: centralsecurity
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
    description: "TECNO DRIVE Central Security Policy - Comprehensive security and resilience checks"
  labels:
    app.kubernetes.io/name: central-security
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tecno-drive.com/policy-type: "security"
spec:
  crd:
    spec:
      names:
        kind: CentralSecurity
      validation:
        type: object
        properties:
          enforcement:
            type: string
            enum: ["warn", "deny"]
            default: "warn"
          exemptNamespaces:
            type: array
            items:
              type: string
          securityChecks:
            type: object
            properties:
              privileged:
                type: boolean
                default: true
              hostNetwork:
                type: boolean
                default: true
              hostPID:
                type: boolean
                default: true
              hostIPC:
                type: boolean
                default: true
              capabilities:
                type: boolean
                default: true
              securityContext:
                type: boolean
                default: true
              resources:
                type: boolean
                default: true
              probes:
                type: boolean
                default: true
              networkPolicies:
                type: boolean
                default: true
              podDisruptionBudgets:
                type: boolean
                default: true
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package centralsecurity
        
        # Helper functions
        is_exempt_namespace {
          input.review.object.metadata.namespace
          exempt_namespaces := input.parameters.exemptNamespaces
          input.review.object.metadata.namespace == exempt_namespaces[_]
        }
        
        is_exempt_namespace {
          input.review.object.metadata.namespace == "kube-system"
        }
        
        is_exempt_namespace {
          input.review.object.metadata.namespace == "kube-public"
        }
        
        is_exempt_namespace {
          input.review.object.metadata.namespace == "gatekeeper-system"
        }
        
        # 1. Security Violations
        violation[{"msg": msg, "type": "security", "severity": "critical"}] {
          input.parameters.securityChecks.privileged
          not is_exempt_namespace
          input.review.object.kind == "Pod"
          input.review.object.spec.securityContext.privileged == true
          msg := sprintf("Pod %s: تشغيل في وضع Privileged محظور", [input.review.object.metadata.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "critical"}] {
          input.parameters.securityChecks.privileged
          not is_exempt_namespace
          input.review.object.kind == "Deployment"
          container := input.review.object.spec.template.spec.containers[_]
          container.securityContext.privileged == true
          msg := sprintf("Deployment %s: الحاوية %s تعمل في وضع Privileged", [input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "high"}] {
          input.parameters.securityChecks.hostNetwork
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          spec.hostNetwork == true
          msg := sprintf("%s %s: استخدام Host Network محظور", [input.review.object.kind, input.review.object.metadata.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "high"}] {
          input.parameters.securityChecks.hostPID
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          spec.hostPID == true
          msg := sprintf("%s %s: استخدام Host PID محظور", [input.review.object.kind, input.review.object.metadata.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "high"}] {
          input.parameters.securityChecks.hostIPC
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          spec.hostIPC == true
          msg := sprintf("%s %s: استخدام Host IPC محظور", [input.review.object.kind, input.review.object.metadata.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "medium"}] {
          input.parameters.securityChecks.capabilities
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.securityContext.capabilities.drop
          msg := sprintf("%s %s: الحاوية %s لا تحذف Capabilities", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "medium"}] {
          input.parameters.securityChecks.securityContext
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.securityContext.runAsNonRoot
          msg := sprintf("%s %s: الحاوية %s لا تعمل كـ Non-Root", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "security", "severity": "medium"}] {
          input.parameters.securityChecks.securityContext
          not is_exempt_namespace
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          container.securityContext.allowPrivilegeEscalation != false
          msg := sprintf("%s %s: الحاوية %s تسمح بـ Privilege Escalation", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        # 2. Resource Management Violations
        violation[{"msg": msg, "type": "resources", "severity": "medium"}] {
          input.parameters.securityChecks.resources
          not is_exempt_namespace
          input.review.object.kind in ["Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.resources.requests
          msg := sprintf("%s %s: الحاوية %s تفتقد Resource Requests", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "resources", "severity": "medium"}] {
          input.parameters.securityChecks.resources
          not is_exempt_namespace
          input.review.object.kind in ["Deployment", "StatefulSet", "DaemonSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.resources.limits
          msg := sprintf("%s %s: الحاوية %s تفتقد Resource Limits", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        # 3. Resilience Violations
        violation[{"msg": msg, "type": "resilience", "severity": "medium"}] {
          input.parameters.securityChecks.probes
          not is_exempt_namespace
          input.review.object.kind in ["Deployment", "StatefulSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.livenessProbe
          msg := sprintf("%s %s: الحاوية %s تفتقد Liveness Probe", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "resilience", "severity": "medium"}] {
          input.parameters.securityChecks.probes
          not is_exempt_namespace
          input.review.object.kind in ["Deployment", "StatefulSet"]
          spec := get_pod_spec(input.review.object)
          container := spec.containers[_]
          not container.readinessProbe
          msg := sprintf("%s %s: الحاوية %s تفتقد Readiness Probe", [input.review.object.kind, input.review.object.metadata.name, container.name])
        }
        
        violation[{"msg": msg, "type": "resilience", "severity": "low"}] {
          input.parameters.securityChecks.podDisruptionBudgets
          not is_exempt_namespace
          input.review.object.kind == "Deployment"
          input.review.object.spec.replicas > 1
          # Check if PDB exists for this deployment
          not has_pdb_for_deployment
          msg := sprintf("Deployment %s: يحتوي على عدة نسخ بدون PodDisruptionBudget", [input.review.object.metadata.name])
        }
        
        # 4. Network Security Violations
        violation[{"msg": msg, "type": "network", "severity": "medium"}] {
          input.parameters.securityChecks.networkPolicies
          not is_exempt_namespace
          input.review.object.kind == "Namespace"
          not has_network_policy_for_namespace
          msg := sprintf("Namespace %s: يفتقد NetworkPolicy", [input.review.object.metadata.name])
        }
        
        # Helper functions
        get_pod_spec(obj) = spec {
          obj.kind == "Pod"
          spec := obj.spec
        }
        
        get_pod_spec(obj) = spec {
          obj.kind in ["Deployment", "StatefulSet", "DaemonSet"]
          spec := obj.spec.template.spec
        }
        
        has_pdb_for_deployment {
          # This would need to be implemented with data.inventory
          # For now, we'll assume false to encourage PDB creation
          false
        }
        
        has_network_policy_for_namespace {
          # This would need to be implemented with data.inventory
          # For now, we'll assume false to encourage NetworkPolicy creation
          false
        }
{{- end }}
