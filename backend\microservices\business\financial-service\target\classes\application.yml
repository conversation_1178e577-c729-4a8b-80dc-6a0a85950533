server:
  port: 8089

spring:
  application:
    name: financial-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_financial
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  cache:
    type: simple
eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics

# Logging
logging:
  level:
    com.tecnodrive.financialservice: DEBUG

# Financial Configuration
financial:
  currency:
    default: "RY"
    supported: ["RY", "EUR", "SAR", "USD"]
  tax:
    default-rate: 0.15
    vat-rate: 0.15
  invoice:
    number-prefix: "INV"
    auto-generate: true
  budget:
    alert-threshold: 0.80
  reporting:
    fiscal-year-start: "01-01"
    retention-years: 7

# Async Configuration
async:
  core-pool-size: 5
  max-pool-size: 10
  queue-capacity: 100

# Scheduling Configuration
scheduling:
  budget-check-cron: "0 0 9 * * MON-FRI"
  invoice-reminder-cron: "0 0 10 * * *"
  financial-report-cron: "0 0 6 1 * *"
