import { createTheme, ThemeOptions } from '@mui/material/styles';
import { colors, typography, shadows, borderRadius, transitions } from './designSystem';

// إنشاء theme احترافي ومتطور
export const createTecnoTheme = (mode: 'light' | 'dark' = 'light') => {
  const isLight = mode === 'light';
  
  const themeOptions: ThemeOptions = {
    palette: {
      mode,
      primary: {
        main: colors.primary[500],
        light: colors.primary[400],
        dark: colors.primary[600],
        contrastText: '#ffffff',
        50: colors.primary[50],
        100: colors.primary[100],
        200: colors.primary[200],
        300: colors.primary[300],
        400: colors.primary[400],
        500: colors.primary[500],
        600: colors.primary[600],
        700: colors.primary[700],
        800: colors.primary[800],
        900: colors.primary[900],
      } as any,
      
      secondary: {
        main: colors.secondary[500],
        light: colors.secondary[400],
        dark: colors.secondary[600],
        contrastText: '#ffffff',
      },
      
      success: {
        main: colors.success[500],
        light: colors.success[400],
        dark: colors.success[600],
        contrastText: '#ffffff',
      },
      
      warning: {
        main: colors.warning[500],
        light: colors.warning[400],
        dark: colors.warning[600],
        contrastText: '#ffffff',
      },
      
      error: {
        main: colors.error[500],
        light: colors.error[400],
        dark: colors.error[600],
        contrastText: '#ffffff',
      },
      
      info: {
        main: colors.tecno.blue,
        light: colors.tecno.lightBlue,
        dark: colors.tecno.darkBlue,
        contrastText: '#ffffff',
      },
      
      background: {
        default: isLight ? colors.gray[50] : colors.gray[900],
        paper: isLight ? '#ffffff' : colors.gray[800],
      },
      
      text: {
        primary: isLight ? colors.gray[900] : colors.gray[50],
        secondary: isLight ? colors.gray[600] : colors.gray[300],
        disabled: isLight ? colors.gray[400] : colors.gray[500],
      },
      
      divider: isLight ? colors.gray[200] : colors.gray[700],
      
      // ألوان مخصصة
      grey: colors.gray,
    },
    
    typography: {
      fontFamily: typography.fontFamily.primary,
      h1: {
        fontSize: typography.fontSize['4xl'],
        fontWeight: typography.fontWeight.bold,
        lineHeight: 1.2,
        letterSpacing: '-0.025em',
      },
      h2: {
        fontSize: typography.fontSize['3xl'],
        fontWeight: typography.fontWeight.bold,
        lineHeight: 1.3,
        letterSpacing: '-0.025em',
      },
      h3: {
        fontSize: typography.fontSize['2xl'],
        fontWeight: typography.fontWeight.semibold,
        lineHeight: 1.4,
      },
      h4: {
        fontSize: typography.fontSize.xl,
        fontWeight: typography.fontWeight.semibold,
        lineHeight: 1.4,
      },
      h5: {
        fontSize: typography.fontSize.lg,
        fontWeight: typography.fontWeight.medium,
        lineHeight: 1.5,
      },
      h6: {
        fontSize: typography.fontSize.base,
        fontWeight: typography.fontWeight.medium,
        lineHeight: 1.5,
      },
      body1: {
        fontSize: typography.fontSize.base,
        lineHeight: 1.6,
        fontWeight: typography.fontWeight.normal,
      },
      body2: {
        fontSize: typography.fontSize.sm,
        lineHeight: 1.5,
        fontWeight: typography.fontWeight.normal,
      },
      subtitle1: {
        fontSize: typography.fontSize.lg,
        lineHeight: 1.5,
        fontWeight: typography.fontWeight.medium,
      },
      subtitle2: {
        fontSize: typography.fontSize.base,
        lineHeight: 1.5,
        fontWeight: typography.fontWeight.medium,
      },
      caption: {
        fontSize: typography.fontSize.xs,
        lineHeight: 1.4,
        fontWeight: typography.fontWeight.normal,
      },
      overline: {
        fontSize: typography.fontSize.xs,
        lineHeight: 1.4,
        fontWeight: typography.fontWeight.medium,
        textTransform: 'uppercase',
        letterSpacing: '0.1em',
      },
      button: {
        fontSize: typography.fontSize.sm,
        fontWeight: typography.fontWeight.medium,
        textTransform: 'none',
        letterSpacing: '0.025em',
      },
    },
    
    shape: {
      borderRadius: borderRadius.base,
    },
    
    spacing: 8, // 8px base unit
    
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 960,
        lg: 1280,
        xl: 1920,
      },
    },
    
    transitions: {
      duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        enteringScreen: 225,
        leavingScreen: 195,
      },
      easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      },
    },
    
    zIndex: {
      mobileStepper: 1000,
      fab: 1050,
      speedDial: 1050,
      appBar: 1100,
      drawer: 1200,
      modal: 1300,
      snackbar: 1400,
      tooltip: 1500,
    },
  };
  
  return createTheme(themeOptions);
};

// Theme افتراضي
export const lightTheme = createTecnoTheme('light');
export const darkTheme = createTecnoTheme('dark');

export default lightTheme;
