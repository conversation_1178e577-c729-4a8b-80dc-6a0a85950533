package com.tecnodrive.hrservice.exception;

/**
 * Employee Not Found Exception
 * 
 * Thrown when a requested employee cannot be found
 */
public class EmployeeNotFoundException extends RuntimeException {

    public EmployeeNotFoundException() {
        super("Employee not found");
    }

    public EmployeeNotFoundException(String message) {
        super(message);
    }

    public EmployeeNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
