"""
TECNO DRIVE Comprehensive Dashboard - FastAPI Backend
Main application entry point with all API endpoints
"""

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from contextlib import asynccontextmanager
import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import asyncpg
import aioredis
from pydantic import BaseModel, Field
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database connection
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "tecnodrive")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "tecnodrive_secure_2024")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
REDIS_URL = "redis://localhost:6379"

# Global connections
db_pool = None
redis_client = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global db_pool, redis_client
    
    # Startup
    logger.info("Starting TECNO DRIVE API...")
    
    # Initialize database pool
    db_pool = await asyncpg.create_pool(DATABASE_URL, min_size=5, max_size=20)
    logger.info("Database pool created")
    
    # Initialize Redis
    redis_client = aioredis.from_url(REDIS_URL, decode_responses=True)
    logger.info("Redis client created")
    
    yield
    
    # Shutdown
    logger.info("Shutting down TECNO DRIVE API...")
    if db_pool:
        await db_pool.close()
    if redis_client:
        await redis_client.close()

# Create FastAPI app
app = FastAPI(
    title="TECNO DRIVE Comprehensive API",
    description="Advanced API for TECNO DRIVE platform with real-time analytics",
    version="1.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class KPIResponse(BaseModel):
    total_vehicles: int
    active_rides: int
    completed_rides_today: int
    total_revenue_today: float
    average_rating: float
    fleet_utilization: float
    active_drivers: int
    pending_deliveries: int

class VehicleLocation(BaseModel):
    id: str
    plate_number: str
    lat: float
    lng: float
    status: str
    speed: Optional[float] = 0
    heading: Optional[float] = 0
    last_update: datetime

class SmartEvent(BaseModel):
    id: str
    event_type: str
    severity: str
    description: str
    zone_id: Optional[str]
    vehicle_id: Optional[str]
    timestamp: datetime
    ai_confidence: Optional[float]
    auto_resolved: bool

class HeatmapData(BaseModel):
    lat: float
    lng: float
    intensity: float
    zone_name: Optional[str]

class ZoneAnalytics(BaseModel):
    zone_id: str
    zone_name: str
    active_vehicles: int
    pending_rides: int
    completed_rides: int
    average_wait_time: float
    demand_level: str

# =====================================================
# DEPENDENCY FUNCTIONS
# =====================================================

async def get_db():
    """Get database connection from pool"""
    async with db_pool.acquire() as connection:
        yield connection

async def get_redis():
    """Get Redis client"""
    return redis_client

# =====================================================
# API ENDPOINTS
# =====================================================

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "TECNO DRIVE Comprehensive API", "status": "healthy", "timestamp": datetime.now()}

@app.get("/api/kpis", response_model=KPIResponse)
async def get_kpis(db: asyncpg.Connection = Depends(get_db)):
    """Get real-time KPIs for dashboard"""
    try:
        # Get total vehicles
        total_vehicles = await db.fetchval(
            "SELECT COUNT(*) FROM operations.vehicles WHERE is_active = true"
        )
        
        # Get active rides
        active_rides = await db.fetchval(
            "SELECT COUNT(*) FROM operations.rides WHERE status IN ('accepted', 'in_progress')"
        )
        
        # Get completed rides today
        today = datetime.now().date()
        completed_rides_today = await db.fetchval(
            "SELECT COUNT(*) FROM operations.rides WHERE status = 'completed' AND DATE(created_at) = $1",
            today
        )
        
        # Get total revenue today
        total_revenue_today = await db.fetchval(
            "SELECT COALESCE(SUM(actual_fare), 0) FROM operations.rides WHERE status = 'completed' AND DATE(created_at) = $1",
            today
        ) or 0.0
        
        # Get average rating
        average_rating = await db.fetchval(
            "SELECT COALESCE(AVG(rating), 0) FROM operations.rides WHERE rating IS NOT NULL"
        ) or 0.0
        
        # Calculate fleet utilization
        busy_vehicles = await db.fetchval(
            "SELECT COUNT(*) FROM operations.vehicles WHERE status = 'busy' AND is_active = true"
        )
        fleet_utilization = (busy_vehicles / total_vehicles * 100) if total_vehicles > 0 else 0
        
        # Get active drivers (vehicles with drivers)
        active_drivers = await db.fetchval(
            "SELECT COUNT(*) FROM operations.vehicles WHERE driver_id IS NOT NULL AND is_active = true"
        )
        
        # Get pending deliveries
        pending_deliveries = await db.fetchval(
            "SELECT COUNT(*) FROM operations.parcels WHERE status IN ('pending', 'picked_up', 'in_transit')"
        )
        
        return KPIResponse(
            total_vehicles=total_vehicles,
            active_rides=active_rides,
            completed_rides_today=completed_rides_today,
            total_revenue_today=float(total_revenue_today),
            average_rating=float(average_rating),
            fleet_utilization=float(fleet_utilization),
            active_drivers=active_drivers,
            pending_deliveries=pending_deliveries
        )
        
    except Exception as e:
        logger.error(f"Error fetching KPIs: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch KPIs")

@app.get("/api/vehicles/live", response_model=List[VehicleLocation])
async def get_live_vehicles(db: asyncpg.Connection = Depends(get_db)):
    """Get real-time vehicle locations"""
    try:
        query = """
        SELECT 
            v.id,
            v.plate_number,
            ST_Y(v.current_location) as lat,
            ST_X(v.current_location) as lng,
            v.status,
            t.speed_kmh,
            t.heading,
            COALESCE(t.time, v.updated_at) as last_update
        FROM operations.vehicles v
        LEFT JOIN LATERAL (
            SELECT speed_kmh, heading, time
            FROM analytics.vehicle_telemetry vt
            WHERE vt.vehicle_id = v.id
            ORDER BY time DESC
            LIMIT 1
        ) t ON true
        WHERE v.is_active = true AND v.current_location IS NOT NULL
        """
        
        rows = await db.fetch(query)
        
        vehicles = []
        for row in rows:
            vehicles.append(VehicleLocation(
                id=str(row['id']),
                plate_number=row['plate_number'],
                lat=float(row['lat']),
                lng=float(row['lng']),
                status=row['status'],
                speed=float(row['speed_kmh']) if row['speed_kmh'] else 0,
                heading=float(row['heading']) if row['heading'] else 0,
                last_update=row['last_update']
            ))
        
        return vehicles
        
    except Exception as e:
        logger.error(f"Error fetching live vehicles: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch vehicle locations")

@app.get("/api/events/smart", response_model=List[SmartEvent])
async def get_smart_events(
    limit: int = 50,
    severity: Optional[str] = None,
    db: asyncpg.Connection = Depends(get_db)
):
    """Get AI-generated smart events"""
    try:
        query = """
        SELECT 
            id, event_type, severity, description, zone_id, vehicle_id,
            time as timestamp, ai_confidence, auto_resolved
        FROM analytics.smart_events
        WHERE ($1::text IS NULL OR severity = $1)
        ORDER BY time DESC
        LIMIT $2
        """
        
        rows = await db.fetch(query, severity, limit)
        
        events = []
        for row in rows:
            events.append(SmartEvent(
                id=str(row['id']),
                event_type=row['event_type'],
                severity=row['severity'],
                description=row['description'],
                zone_id=str(row['zone_id']) if row['zone_id'] else None,
                vehicle_id=str(row['vehicle_id']) if row['vehicle_id'] else None,
                timestamp=row['timestamp'],
                ai_confidence=float(row['ai_confidence']) if row['ai_confidence'] else None,
                auto_resolved=row['auto_resolved']
            ))
        
        return events
        
    except Exception as e:
        logger.error(f"Error fetching smart events: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch smart events")

@app.get("/api/heatmap/demand", response_model=List[HeatmapData])
async def get_demand_heatmap(
    hours: int = 24,
    db: asyncpg.Connection = Depends(get_db)
):
    """Get demand heatmap data"""
    try:
        query = """
        SELECT 
            ST_Y(pickup_location) as lat,
            ST_X(pickup_location) as lng,
            COUNT(*) as intensity,
            z.name as zone_name
        FROM operations.rides r
        LEFT JOIN operations.zones z ON r.pickup_zone_id = z.id
        WHERE r.created_at >= NOW() - INTERVAL '%s hours'
        AND r.pickup_location IS NOT NULL
        GROUP BY ST_Y(pickup_location), ST_X(pickup_location), z.name
        HAVING COUNT(*) > 1
        ORDER BY intensity DESC
        LIMIT 1000
        """ % hours
        
        rows = await db.fetch(query)
        
        heatmap_data = []
        max_intensity = max([row['intensity'] for row in rows]) if rows else 1
        
        for row in rows:
            heatmap_data.append(HeatmapData(
                lat=float(row['lat']),
                lng=float(row['lng']),
                intensity=float(row['intensity']) / max_intensity,  # Normalize 0-1
                zone_name=row['zone_name']
            ))
        
        return heatmap_data
        
    except Exception as e:
        logger.error(f"Error fetching heatmap data: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch heatmap data")

@app.get("/api/zones/analytics", response_model=List[ZoneAnalytics])
async def get_zone_analytics(db: asyncpg.Connection = Depends(get_db)):
    """Get analytics for each zone"""
    try:
        query = """
        SELECT 
            z.id as zone_id,
            z.name as zone_name,
            COUNT(DISTINCT v.id) as active_vehicles,
            COUNT(DISTINCT CASE WHEN r.status IN ('requested', 'accepted') THEN r.id END) as pending_rides,
            COUNT(DISTINCT CASE WHEN r.status = 'completed' AND DATE(r.created_at) = CURRENT_DATE THEN r.id END) as completed_rides,
            COALESCE(AVG(CASE WHEN r.status = 'completed' THEN EXTRACT(EPOCH FROM (r.pickup_time - r.created_at))/60 END), 0) as avg_wait_time
        FROM operations.zones z
        LEFT JOIN operations.vehicles v ON z.id = v.current_zone_id AND v.is_active = true
        LEFT JOIN operations.rides r ON z.id = r.pickup_zone_id
        WHERE z.is_active = true
        GROUP BY z.id, z.name
        ORDER BY z.name
        """
        
        rows = await db.fetch(query)
        
        analytics = []
        for row in rows:
            # Determine demand level based on pending rides
            pending = row['pending_rides']
            if pending >= 10:
                demand_level = "high"
            elif pending >= 5:
                demand_level = "medium"
            else:
                demand_level = "low"
            
            analytics.append(ZoneAnalytics(
                zone_id=str(row['zone_id']),
                zone_name=row['zone_name'],
                active_vehicles=row['active_vehicles'],
                pending_rides=row['pending_rides'],
                completed_rides=row['completed_rides'],
                average_wait_time=float(row['avg_wait_time']),
                demand_level=demand_level
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error fetching zone analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch zone analytics")

# =====================================================
# WEBSOCKET FOR REAL-TIME UPDATES
# =====================================================

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message, default=str))
            except:
                # Remove disconnected clients
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.websocket("/ws/live-updates")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Send live updates every 5 seconds
            await asyncio.sleep(5)
            
            # Get fresh data
            async with db_pool.acquire() as db:
                # Get vehicle count
                vehicle_count = await db.fetchval(
                    "SELECT COUNT(*) FROM operations.vehicles WHERE is_active = true"
                )
                
                # Get active rides
                active_rides = await db.fetchval(
                    "SELECT COUNT(*) FROM operations.rides WHERE status IN ('accepted', 'in_progress')"
                )
            
            # Send update
            await manager.broadcast({
                "type": "live_update",
                "data": {
                    "vehicle_count": vehicle_count,
                    "active_rides": active_rides,
                    "timestamp": datetime.now().isoformat()
                }
            })
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
