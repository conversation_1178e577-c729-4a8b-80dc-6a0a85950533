# Test Login with API Gateway
Write-Host "🧪 Testing Login with API Gateway" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Test data
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
}

$jsonBody = $loginData | ConvertTo-Json

Write-Host "`n📝 Login Data:" -ForegroundColor Yellow
Write-Host "Email: $($loginData.email)" -ForegroundColor White
Write-Host "Password: $($loginData.password)" -ForegroundColor White

# Test different endpoints
$endpoints = @(
    @{ Name = "API Gateway"; Url = "http://localhost:8080/api/auth/login" },
    @{ Name = "Auth Service"; Url = "http://localhost:8081/api/auth/login" },
    @{ Name = "Location Service"; Url = "http://localhost:8085/api/auth/login" }
)

foreach ($endpoint in $endpoints) {
    Write-Host "`n🔍 Testing: $($endpoint.Name)" -ForegroundColor Cyan
    Write-Host "URL: $($endpoint.Url)" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $endpoint.Url -Method POST -Body $jsonBody -ContentType "application/json" -TimeoutSec 10
        
        Write-Host "✅ Success!" -ForegroundColor Green
        
        if ($response.success) {
            Write-Host "📊 Response: Success = $($response.success)" -ForegroundColor Green
            Write-Host "💬 Message: $($response.message)" -ForegroundColor Green
            
            if ($response.data -and $response.data.token) {
                Write-Host "🔑 Token: $($response.data.token.Substring(0, 20))..." -ForegroundColor Cyan
            }
            
            if ($response.data -and $response.data.user) {
                Write-Host "👤 User: $($response.data.user.name) ($($response.data.user.role))" -ForegroundColor Cyan
            }
        } else {
            Write-Host "📄 Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Yellow
        }
        
    } catch {
        $statusCode = "Unknown"
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
        }
        
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "📄 Status: $statusCode" -ForegroundColor Yellow
    }
}

# Test Frontend connectivity
Write-Host "`n🌐 Testing Frontend Connectivity" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "✅ Frontend: Ready (Status: $($frontendResponse.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
}

# Test API Gateway health
Write-Host "`n🏥 Testing API Gateway Health" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

try {
    $gatewayHealth = Invoke-RestMethod -Uri "http://localhost:8080/actuator/health" -TimeoutSec 5
    Write-Host "✅ API Gateway: $($gatewayHealth.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ API Gateway: Not Ready" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Test Summary" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "🌐 Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "🚪 API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "🔐 Auth Endpoint: http://localhost:8080/api/auth/login" -ForegroundColor White

Write-Host "`n💡 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open browser to http://localhost:3000/login" -ForegroundColor White
Write-Host "2. Use credentials: <EMAIL> / password123" -ForegroundColor White
Write-Host "3. Check browser console for any errors" -ForegroundColor White
