import React from 'react';
import {
  Box,
  Typography,
  useTheme,
  alpha,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
} from '@mui/icons-material';
import TecnoCard from '../Card/TecnoCard';

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    label?: string;
    period?: string;
  };
  progress?: {
    value: number;
    max?: number;
    label?: string;
  };
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  variant?: 'default' | 'gradient' | 'outlined';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  onClick?: () => void;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  progress,
  color = 'primary',
  variant = 'default',
  size = 'medium',
  loading = false,
  onClick,
}) => {
  const theme = useTheme();

  const getColorValue = () => {
    const colorMap = {
      primary: theme.palette.primary.main,
      secondary: theme.palette.secondary.main,
      success: theme.palette.success.main,
      warning: theme.palette.warning.main,
      error: theme.palette.error.main,
      info: theme.palette.info.main,
    };
    return colorMap[color];
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend.value > 0) {
      return <TrendingUpIcon sx={{ color: theme.palette.success.main, fontSize: 16 }} />;
    } else if (trend.value < 0) {
      return <TrendingDownIcon sx={{ color: theme.palette.error.main, fontSize: 16 }} />;
    } else {
      return <TrendingFlatIcon sx={{ color: theme.palette.grey[500], fontSize: 16 }} />;
    }
  };

  const getTrendColor = () => {
    if (!trend) return theme.palette.text.secondary;
    
    if (trend.value > 0) return theme.palette.success.main;
    if (trend.value < 0) return theme.palette.error.main;
    return theme.palette.grey[500];
  };

  const getCardVariant = () => {
    if (variant === 'gradient') return 'gradient';
    if (variant === 'outlined') return 'outlined';
    return 'elevated';
  };

  const getSizeStyles = () => {
    const sizeMap = {
      small: {
        padding: theme.spacing(2),
        iconSize: 32,
        valueSize: theme.typography.h5.fontSize,
        titleSize: theme.typography.body2.fontSize,
      },
      medium: {
        padding: theme.spacing(3),
        iconSize: 40,
        valueSize: theme.typography.h4.fontSize,
        titleSize: theme.typography.body1.fontSize,
      },
      large: {
        padding: theme.spacing(4),
        iconSize: 48,
        valueSize: theme.typography.h3.fontSize,
        titleSize: theme.typography.h6.fontSize,
      },
    };
    return sizeMap[size];
  };

  const styles = getSizeStyles();
  const mainColor = getColorValue();

  return (
    <TecnoCard
      variant={getCardVariant()}
      onClick={onClick}
      hoverable={!!onClick}
      loading={loading}
    >
      <Box sx={{ p: styles.padding }}>
        {/* Header with Icon */}
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontSize: styles.titleSize,
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
                mb: 1,
              }}
            >
              {title}
            </Typography>
            
            <Typography
              variant="h4"
              sx={{
                fontSize: styles.valueSize,
                fontWeight: 700,
                color: variant === 'gradient' ? 'inherit' : mainColor,
                lineHeight: 1.2,
              }}
            >
              {value}
            </Typography>
            
            {subtitle && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: 0.5 }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
          
          {icon && (
            <Box
              sx={{
                width: styles.iconSize,
                height: styles.iconSize,
                borderRadius: 2,
                backgroundColor: alpha(mainColor, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: mainColor,
                flexShrink: 0,
              }}
            >
              {icon}
            </Box>
          )}
        </Box>

        {/* Progress Bar */}
        {progress && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              {progress.label && (
                <Typography variant="caption" color="text.secondary">
                  {progress.label}
                </Typography>
              )}
              <Typography variant="caption" color="text.secondary">
                {progress.value}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progress.value}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: alpha(mainColor, 0.1),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: mainColor,
                  borderRadius: 3,
                },
              }}
            />
          </Box>
        )}

        {/* Trend */}
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getTrendIcon()}
            <Typography
              variant="body2"
              sx={{
                color: getTrendColor(),
                fontWeight: 500,
              }}
            >
              {trend.value > 0 ? '+' : ''}{trend.value}%
            </Typography>
            {trend.label && (
              <Typography variant="body2" color="text.secondary">
                {trend.label}
              </Typography>
            )}
            {trend.period && (
              <Chip
                label={trend.period}
                size="small"
                variant="outlined"
                sx={{
                  height: 20,
                  fontSize: '0.7rem',
                  ml: 'auto',
                }}
              />
            )}
          </Box>
        )}
      </Box>
    </TecnoCard>
  );
};

export default StatsCard;
