# Multi-stage build for API Gateway
FROM maven:3.9.9-eclipse-temurin-17-alpine AS builder

# Set working directory
WORKDIR /app

# Copy pom.xml first for better caching
COPY pom.xml .

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests

# Runtime stage - Use JRE instead of JDK for smaller attack surface
FROM eclipse-temurin:17-jre-alpine

# Install curl for health checks and update packages for security
RUN apk update && \
    apk upgrade && \
    apk add --no-cache curl && \
    rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy the built JAR from builder stage
COPY --from=builder /app/target/*.jar app.jar

# Create non-root user for security (Alpine syntax)
RUN addgroup -g 1001 -S spring && \
    adduser -u 1001 -S spring -G spring

# Change ownership of the app directory to spring user
RUN chown -R spring:spring /app

USER spring:spring

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM optimization for containers with security enhancements
ENV JAVA_OPTS="-Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseContainerSupport -Dspring.main.allow-bean-definition-overriding=true -Djava.security.egd=file:/dev/./urandom"

# Run the application with exec form for better signal handling
ENTRYPOINT ["java", "-jar", "app.jar"]
CMD []
