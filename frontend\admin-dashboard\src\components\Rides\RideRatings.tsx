import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Rating,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Star as StarIcon,
  Person as PersonIcon,
  DirectionsCar as CarIcon,
  Comment as CommentIcon,
  TrendingUp as TrendingUpIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ian<PERSON><PERSON>,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from 'recharts';

interface RideRating {
  id: string;
  rideId: string;
  passengerId: string;
  passengerName: string;
  driverId: string;
  driverName: string;
  vehiclePlate: string;
  rating: number;
  comment?: string;
  categories: {
    punctuality: number;
    cleanliness: number;
    driving: number;
    courtesy: number;
  };
  createdAt: string;
}

interface DriverRatingStats {
  driverId: string;
  driverName: string;
  totalRatings: number;
  averageRating: number;
  ratingDistribution: { [key: number]: number };
  categoryAverages: {
    punctuality: number;
    cleanliness: number;
    driving: number;
    courtesy: number;
  };
  totalRides: number;
  positiveComments: number;
  negativeComments: number;
}

const RideRatings: React.FC = () => {
  const [ratings, setRatings] = useState<RideRating[]>([]);
  const [driverStats, setDriverStats] = useState<DriverRatingStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRating, setFilterRating] = useState('ALL');
  const [filterDriver, setFilterDriver] = useState('ALL');
  const [selectedTimeRange, setSelectedTimeRange] = useState('last_30_days');

  // Mock data
  const mockRatings: RideRating[] = [
    {
      id: 'rating-1',
      rideId: 'ride-123',
      passengerId: 'passenger-1',
      passengerName: 'أحمد محمد',
      driverId: 'driver-1',
      driverName: 'علي سالم',
      vehiclePlate: 'أ ب ج 123',
      rating: 5,
      comment: 'سائق ممتاز، وصل في الوقت المحدد والسيارة نظيفة',
      categories: {
        punctuality: 5,
        cleanliness: 5,
        driving: 4,
        courtesy: 5,
      },
      createdAt: '2025-07-09T10:30:00Z',
    },
    {
      id: 'rating-2',
      rideId: 'ride-124',
      passengerId: 'passenger-2',
      passengerName: 'فاطمة أحمد',
      driverId: 'driver-2',
      driverName: 'محمد حسن',
      vehiclePlate: 'د هـ و 456',
      rating: 4,
      comment: 'رحلة جيدة بشكل عام، لكن السائق تأخر قليلاً',
      categories: {
        punctuality: 3,
        cleanliness: 4,
        driving: 5,
        courtesy: 4,
      },
      createdAt: '2025-07-09T09:15:00Z',
    },
    {
      id: 'rating-3',
      rideId: 'ride-125',
      passengerId: 'passenger-3',
      passengerName: 'خالد عبدالله',
      driverId: 'driver-1',
      driverName: 'علي سالم',
      vehiclePlate: 'أ ب ج 123',
      rating: 5,
      comment: 'ممتاز كالعادة!',
      categories: {
        punctuality: 5,
        cleanliness: 5,
        driving: 5,
        courtesy: 5,
      },
      createdAt: '2025-07-09T08:45:00Z',
    },
  ];

  const mockDriverStats: DriverRatingStats[] = [
    {
      driverId: 'driver-1',
      driverName: 'علي سالم',
      totalRatings: 156,
      averageRating: 4.8,
      ratingDistribution: { 5: 120, 4: 25, 3: 8, 2: 2, 1: 1 },
      categoryAverages: {
        punctuality: 4.7,
        cleanliness: 4.9,
        driving: 4.8,
        courtesy: 4.9,
      },
      totalRides: 160,
      positiveComments: 145,
      negativeComments: 11,
    },
    {
      driverId: 'driver-2',
      driverName: 'محمد حسن',
      totalRatings: 89,
      averageRating: 4.3,
      ratingDistribution: { 5: 45, 4: 30, 3: 10, 2: 3, 1: 1 },
      categoryAverages: {
        punctuality: 4.1,
        cleanliness: 4.4,
        driving: 4.5,
        courtesy: 4.2,
      },
      totalRides: 95,
      positiveComments: 75,
      negativeComments: 14,
    },
  ];

  useEffect(() => {
    setRatings(mockRatings);
    setDriverStats(mockDriverStats);
  }, []);

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'success';
    if (rating >= 3.5) return 'warning';
    return 'error';
  };

  const getRatingChip = (rating: number) => {
    return (
      <Chip
        icon={<StarIcon fontSize="small" />}
        label={rating.toFixed(1)}
        color={getRatingColor(rating)}
        size="small"
        variant="filled"
      />
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'rideId',
      headerName: 'رقم الرحلة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'passengerName',
      headerName: 'الراكب',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <PersonIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      ),
    },
    {
      field: 'driverName',
      headerName: 'السائق',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'success.main' }}>
            <CarIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      ),
    },
    {
      field: 'rating',
      headerName: 'التقييم',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Rating value={params.value} readOnly size="small" />
          <Typography variant="body2">({params.value})</Typography>
        </Box>
      ),
    },
    {
      field: 'comment',
      headerName: 'التعليق',
      width: 300,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary" sx={{ 
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {params.value || 'لا يوجد تعليق'}
        </Typography>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'التاريخ',
      width: 130,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
  ];

  const filteredRatings = ratings.filter(rating => {
    const matchesSearch = rating.passengerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rating.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rating.rideId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRating = filterRating === 'ALL' || 
                         (filterRating === '5' && rating.rating === 5) ||
                         (filterRating === '4' && rating.rating === 4) ||
                         (filterRating === '3' && rating.rating <= 3);
    
    const matchesDriver = filterDriver === 'ALL' || rating.driverId === filterDriver;
    
    return matchesSearch && matchesRating && matchesDriver;
  });

  // Calculate overall stats
  const totalRatings = ratings.length;
  const averageRating = ratings.reduce((sum, r) => sum + r.rating, 0) / totalRatings;
  const fiveStarCount = ratings.filter(r => r.rating === 5).length;
  const positiveRatings = ratings.filter(r => r.rating >= 4).length;

  // Rating distribution data for chart
  const ratingDistributionData = [
    { rating: '5 نجوم', count: ratings.filter(r => r.rating === 5).length },
    { rating: '4 نجوم', count: ratings.filter(r => r.rating === 4).length },
    { rating: '3 نجوم', count: ratings.filter(r => r.rating === 3).length },
    { rating: '2 نجوم', count: ratings.filter(r => r.rating === 2).length },
    { rating: '1 نجمة', count: ratings.filter(r => r.rating === 1).length },
  ];

  // Category averages
  const categoryAverages = {
    punctuality: ratings.reduce((sum, r) => sum + r.categories.punctuality, 0) / totalRatings,
    cleanliness: ratings.reduce((sum, r) => sum + r.categories.cleanliness, 0) / totalRatings,
    driving: ratings.reduce((sum, r) => sum + r.categories.driving, 0) / totalRatings,
    courtesy: ratings.reduce((sum, r) => sum + r.categories.courtesy, 0) / totalRatings,
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          تقييمات الرحلات والسائقين
        </Typography>
        <Typography variant="body1" color="text.secondary">
          مراقبة وتحليل تقييمات الركاب لتحسين جودة الخدمة
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <StarIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {averageRating.toFixed(1)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط التقييم
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <ThumbUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {((positiveRatings / totalRatings) * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تقييمات إيجابية
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <CommentIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalRatings}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي التقييمات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {((fiveStarCount / totalRatings) * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تقييمات 5 نجوم
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزيع التقييمات
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={ratingDistributionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="rating" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#1976d2" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                متوسط التقييم حسب الفئة
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {Object.entries(categoryAverages).map(([category, average]) => (
                  <Box key={category}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">
                        {category === 'punctuality' ? 'الالتزام بالوقت' :
                         category === 'cleanliness' ? 'النظافة' :
                         category === 'driving' ? 'القيادة' : 'الأدب'}
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {average.toFixed(1)}/5
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={(average / 5) * 100} 
                      color={getRatingColor(average)}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في التقييمات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>التقييم</InputLabel>
              <Select
                value={filterRating}
                label="التقييم"
                onChange={(e) => setFilterRating(e.target.value)}
              >
                <MenuItem value="ALL">جميع التقييمات</MenuItem>
                <MenuItem value="5">5 نجوم</MenuItem>
                <MenuItem value="4">4 نجوم</MenuItem>
                <MenuItem value="3">3 نجوم أو أقل</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>السائق</InputLabel>
              <Select
                value={filterDriver}
                label="السائق"
                onChange={(e) => setFilterDriver(e.target.value)}
              >
                <MenuItem value="ALL">جميع السائقين</MenuItem>
                {driverStats.map((driver) => (
                  <MenuItem key={driver.driverId} value={driver.driverId}>
                    {driver.driverName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
            >
              تصدير التقرير
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredRatings}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>
    </Box>
  );
};

export default RideRatings;
