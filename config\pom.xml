<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>

    <groupId>com.tecnodrive</groupId>
    <artifactId>tecno-drive</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>TECNO DRIVE Platform</name>
    <description>Complete ride-sharing and parcel delivery platform for Yemen</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot and Cloud versions -->
        <spring.boot.version>3.2.0</spring.boot.version>
        <spring.cloud.version>2023.0.0</spring.cloud.version>
        
        <!-- Database versions -->
        <postgresql.version>42.7.1</postgresql.version>
        <flyway.version>9.22.3</flyway.version>
        <postgis.version>2023.1.0</postgis.version>
        
        <!-- Security and JWT -->
        <jwt.version>0.12.3</jwt.version>
        <spring.security.version>6.2.0</spring.security.version>
        
        <!-- Messaging and Caching -->
        <kafka.version>3.6.0</kafka.version>
        <redis.version>3.2.0</redis.version>
        
        <!-- Monitoring and Observability -->
        <micrometer.version>1.12.0</micrometer.version>
        <openapi.version>2.2.0</openapi.version>

        <!-- Resilience and Circuit Breaker -->
        <resilience4j.version>2.1.0</resilience4j.version>
        <micrometer-tracing.version>1.2.0</micrometer-tracing.version>
        <zipkin-reporter.version>2.16.4</zipkin-reporter.version>
        
        <!-- Testing -->
        <testcontainers.version>1.19.3</testcontainers.version>
        <pact.version>4.6.2</pact.version>
        
        <!-- Utilities -->
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <commons.lang3.version>3.13.0</commons.lang3.version>
        <jackson.version>2.16.0</jackson.version>
    </properties>

    <modules>
        <!-- Shared Libraries -->
        <module>shared/common</module>
        <module>libs/shared/common</module>

        <!-- Core Services -->
        <module>services/auth-service</module>
        <module>services/parcel-service</module>
        <module>services/location-service</module>
        <module>services/payment-service</module>
        <module>services/saas-management-service</module>
        <module>services/notification-service</module>
        <module>services/financial-service</module>
        <module>services/analytics-service</module>
        <module>services/fleet-service</module>
        <module>services/simple-service</module>
        <module>services/hr-service</module>

        <!-- Gateway and Discovery -->
        <module>services/api-gateway</module>
        <module>services/eureka-server</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Database Dependencies -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            
            <dependency>
                <groupId>net.postgis</groupId>
                <artifactId>postgis-jdbc</artifactId>
                <version>${postgis.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            
            <!-- JWT Dependencies -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            
            <!-- MapStruct for DTO mapping -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            
            <!-- Utilities -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            
            <!-- OpenAPI Documentation -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${openapi.version}</version>
            </dependency>
            
            <!-- Testing Dependencies -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <dependency>
                <groupId>au.com.dius.pact.provider</groupId>
                <artifactId>junit5</artifactId>
                <version>${pact.version}</version>
                <scope>test</scope>
            </dependency>
            
            <dependency>
                <groupId>au.com.dius.pact.consumer</groupId>
                <artifactId>junit5</artifactId>
                <version>${pact.version}</version>
                <scope>test</scope>
            </dependency>
            
            <!-- JTS for Spatial Data -->
            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>1.19.0</version>
            </dependency>

            <!-- Hibernate Spatial -->
            <dependency>
                <groupId>org.hibernate.orm</groupId>
                <artifactId>hibernate-spatial</artifactId>
                <version>6.4.0.Final</version>
            </dependency>

            <!-- Resilience4j Dependencies -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot3</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-retry</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-bulkhead</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- Distributed Tracing -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-tracing-bridge-brave</artifactId>
                <version>${micrometer-tracing.version}</version>
            </dependency>

            <dependency>
                <groupId>io.zipkin.reporter2</groupId>
                <artifactId>zipkin-reporter-brave</artifactId>
                <version>${zipkin-reporter.version}</version>
            </dependency>

            <!-- Internal Dependencies -->
            <dependency>
                <groupId>com.tecnodrive</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.tecnodrive</groupId>
                <artifactId>security</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.tecnodrive</groupId>
                <artifactId>messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>${flyway.version}</version>
                </plugin>
                
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.10</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>1.4.13</version>
                    <configuration>
                        <repository>tecnodrive/${project.artifactId}</repository>
                        <tag>${project.version}</tag>
                        <buildArgs>
                            <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
