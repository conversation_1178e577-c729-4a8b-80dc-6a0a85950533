# TecnoDrive Docker Build Script
# This script builds and starts all TecnoDrive services using Docker Compose

Write-Host "🚀 TecnoDrive Docker Build & Deploy Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Check if Docker is running
Write-Host "🔍 Checking Docker status..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if docker-compose is available
Write-Host "🔍 Checking Docker Compose..." -ForegroundColor Yellow
try {
    docker-compose version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please install Docker Compose." -ForegroundColor Red
    exit 1
}

# Navigate to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

Write-Host "📁 Working directory: $projectRoot" -ForegroundColor Cyan

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose down --remove-orphans

# Remove old images (optional - uncomment if you want to rebuild from scratch)
# Write-Host "🗑️ Removing old images..." -ForegroundColor Yellow
# docker-compose down --rmi all

# Build and start services
Write-Host "🔨 Building and starting services..." -ForegroundColor Yellow
docker-compose up --build -d

# Wait for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service health
Write-Host "🏥 Checking service health..." -ForegroundColor Yellow

$services = @(
    @{Name="Eureka Server"; Port=8761; Path="/health"}
    @{Name="API Gateway"; Port=8080; Path="/health"}
    @{Name="Auth Service"; Port=8081; Path="/health"}
    @{Name="User Service"; Port=8083; Path="/health"}
    @{Name="Fleet Service"; Port=8084; Path="/health"}
    @{Name="Location Service"; Port=8085; Path="/health"}
    @{Name="Payment Service"; Port=8086; Path="/health"}
    @{Name="Parcel Service"; Port=8087; Path="/health"}
    @{Name="Notification Service"; Port=8088; Path="/health"}
    @{Name="Analytics Service"; Port=8089; Path="/health"}
    @{Name="HR Service"; Port=8097; Path="/health"}
    @{Name="Financial Service"; Port=8098; Path="/health"}
    @{Name="Wallet Service"; Port=8099; Path="/health"}
    @{Name="Live Operations Service"; Port=8100; Path="/health"}
    @{Name="Operations Management Service"; Port=8101; Path="/health"}
    @{Name="Trip Tracking Service"; Port=8102; Path="/health"}
    @{Name="Demand Analysis Service"; Port=8103; Path="/health"}
)

$healthyServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    try {
        $url = "http://localhost:$($service.Port)$($service.Path)"
        Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 5 | Out-Null
        Write-Host "✅ $($service.Name) - Healthy" -ForegroundColor Green
        $healthyServices++
    } catch {
        Write-Host "❌ $($service.Name) - Not responding" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Health Check Summary:" -ForegroundColor Cyan
Write-Host "Healthy Services: $healthyServices/$totalServices" -ForegroundColor $(if ($healthyServices -eq $totalServices) { "Green" } else { "Yellow" })

if ($healthyServices -eq $totalServices) {
    Write-Host "🎉 All services are running successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some services are not responding. Check logs with: docker-compose logs [service-name]" -ForegroundColor Yellow
}

# Show running containers
Write-Host ""
Write-Host "🐳 Running Containers:" -ForegroundColor Cyan
docker-compose ps

Write-Host ""
Write-Host "🌐 Service URLs:" -ForegroundColor Cyan
Write-Host "Eureka Dashboard: http://localhost:8761" -ForegroundColor White
Write-Host "API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "Admin Dashboard: http://localhost:3000" -ForegroundColor White

Write-Host ""
Write-Host "📝 Useful Commands:" -ForegroundColor Cyan
Write-Host "View logs: docker-compose logs -f [service-name]" -ForegroundColor White
Write-Host "Stop services: docker-compose down" -ForegroundColor White
Write-Host "Restart service: docker-compose restart [service-name]" -ForegroundColor White
Write-Host "View all logs: docker-compose logs -f" -ForegroundColor White

Write-Host ""
Write-Host "✨ TecnoDrive Platform is ready!" -ForegroundColor Green
