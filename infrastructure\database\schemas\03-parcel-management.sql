-- =====================================================
-- TecnoDrive Platform - Advanced Parcel Management Schema
-- =====================================================

-- =====================================================
-- 1. PARCEL CATEGORIES TABLE
-- =====================================================
CREATE TABLE parcel_categories (
    category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    category_name VARCHAR(100) NOT NULL,
    category_code VARCHAR(20) NOT NULL,
    description TEXT,
    max_weight_kg DECIMAL(8,2),
    max_dimensions_cm VARCHAR(50), -- "L x W x H"
    base_price DECIMAL(10,2),
    price_per_kg DECIMAL(10,2),
    price_per_km DECIMAL(10,2),
    handling_instructions TEXT,
    requires_signature BOOLEAN DEFAULT TRUE,
    requires_id_verification BOOLEAN DEFAULT FALSE,
    fragile_handling BOOLEAN DEFAULT FALSE,
    temperature_controlled BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, category_code)
);

-- Insert default parcel categories
INSERT INTO parcel_categories (tenant_id, category_name, category_code, max_weight_kg, max_dimensions_cm, base_price, price_per_kg, price_per_km, requires_signature, fragile_handling) 
SELECT 
    t.tenant_id,
    unnest(ARRAY['وثائق', 'طرود صغيرة', 'طرود متوسطة', 'طرود كبيرة', 'أجهزة إلكترونية', 'مواد غذائية', 'أدوية']),
    unnest(ARRAY['DOC', 'SMALL', 'MEDIUM', 'LARGE', 'ELECTRONICS', 'FOOD', 'MEDICINE']),
    unnest(ARRAY[0.5, 5.0, 15.0, 50.0, 10.0, 20.0, 5.0]),
    unnest(ARRAY['30x20x5', '40x30x20', '60x40x40', '100x80x60', '50x40x30', '40x30x25', '30x20x15']),
    unnest(ARRAY[500.00, 1000.00, 1500.00, 3000.00, 2000.00, 1200.00, 800.00]),
    unnest(ARRAY[0.00, 100.00, 80.00, 60.00, 150.00, 50.00, 200.00]),
    unnest(ARRAY[50.00, 75.00, 100.00, 150.00, 120.00, 80.00, 100.00]),
    unnest(ARRAY[false, true, true, true, true, false, true]),
    unnest(ARRAY[false, false, false, false, true, false, false])
FROM tenants t;

-- =====================================================
-- 2. PARCELS TABLE (Enhanced)
-- =====================================================
CREATE TABLE parcels (
    parcel_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    tracking_number VARCHAR(50) NOT NULL UNIQUE,
    category_id UUID REFERENCES parcel_categories(category_id),
    
    -- Sender Information
    sender_id UUID REFERENCES customers(customer_id),
    sender_name VARCHAR(255) NOT NULL,
    sender_phone VARCHAR(20) NOT NULL,
    sender_email VARCHAR(255),
    sender_address TEXT NOT NULL,
    sender_location POINT,
    
    -- Receiver Information
    receiver_id UUID REFERENCES customers(customer_id),
    receiver_name VARCHAR(255) NOT NULL,
    receiver_phone VARCHAR(20) NOT NULL,
    receiver_email VARCHAR(255),
    receiver_address TEXT NOT NULL,
    receiver_location POINT,
    
    -- Parcel Details
    item_description TEXT NOT NULL,
    declared_value DECIMAL(12,2),
    weight_kg DECIMAL(8,2),
    dimensions_cm VARCHAR(50), -- "L x W x H"
    volume_cm3 DECIMAL(12,2),
    special_instructions TEXT,
    fragile BOOLEAN DEFAULT FALSE,
    urgent BOOLEAN DEFAULT FALSE,
    requires_signature BOOLEAN DEFAULT TRUE,
    requires_id_verification BOOLEAN DEFAULT FALSE,
    
    -- Pricing
    base_cost DECIMAL(10,2),
    weight_cost DECIMAL(10,2),
    distance_cost DECIMAL(10,2),
    additional_fees DECIMAL(10,2) DEFAULT 0.00,
    total_cost DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'YER',
    
    -- Status and Tracking
    status VARCHAR(50) DEFAULT 'created' CHECK (status IN (
        'created', 'pickup_scheduled', 'pickup_in_progress', 'picked_up', 
        'in_transit', 'at_warehouse', 'out_for_delivery', 'delivered', 
        'failed_delivery', 'returned', 'cancelled', 'lost'
    )),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- Assignment
    assigned_picker_id UUID,
    assigned_driver_id UUID,
    assigned_vehicle_id UUID,
    current_warehouse_id UUID,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    pickup_scheduled_at TIMESTAMP WITH TIME ZONE,
    pickup_started_at TIMESTAMP WITH TIME ZONE,
    picked_up_at TIMESTAMP WITH TIME ZONE,
    warehouse_arrived_at TIMESTAMP WITH TIME ZONE,
    delivery_started_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    estimated_delivery_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional Info
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'cod', 'failed')),
    cod_amount DECIMAL(10,2), -- Cash on Delivery amount
    insurance_amount DECIMAL(10,2),
    photos JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. PARCEL EVENTS TABLE (Enhanced)
-- =====================================================
CREATE TABLE parcel_events (
    event_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    parcel_id UUID NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_status VARCHAR(50),
    description TEXT,
    location_name VARCHAR(255),
    location_coordinates POINT,
    actor_type VARCHAR(50), -- 'customer', 'picker', 'driver', 'warehouse_staff', 'system'
    actor_id UUID,
    actor_name VARCHAR(255),
    photos JSONB DEFAULT '[]',
    signature_data TEXT, -- Base64 encoded signature
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. WAREHOUSES TABLE
-- =====================================================
CREATE TABLE warehouses (
    warehouse_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    location POINT NOT NULL,
    city VARCHAR(100),
    capacity_cubic_meters DECIMAL(10,2),
    current_utilization_percent DECIMAL(5,2) DEFAULT 0.00,
    operating_hours JSONB DEFAULT '{}', -- {"monday": {"open": "08:00", "close": "18:00"}, ...}
    contact_person VARCHAR(255),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    warehouse_type VARCHAR(50) DEFAULT 'standard' CHECK (warehouse_type IN ('standard', 'cold_storage', 'secure', 'express')),
    facilities JSONB DEFAULT '[]', -- ["loading_dock", "security_cameras", "climate_control"]
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, code)
);

-- =====================================================
-- 5. WAREHOUSE INVENTORY TABLE
-- =====================================================
CREATE TABLE warehouse_inventory (
    inventory_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    warehouse_id UUID NOT NULL REFERENCES warehouses(warehouse_id) ON DELETE CASCADE,
    parcel_id UUID NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    location_in_warehouse VARCHAR(100), -- "Zone A, Shelf 3, Position 15"
    arrival_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    departure_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'stored' CHECK (status IN ('stored', 'sorting', 'ready_for_dispatch', 'dispatched')),
    handling_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(warehouse_id, parcel_id)
);

-- =====================================================
-- 6. SORTING OPERATIONS TABLE
-- =====================================================
CREATE TABLE sorting_operations (
    operation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    warehouse_id UUID NOT NULL REFERENCES warehouses(warehouse_id) ON DELETE CASCADE,
    parcel_id UUID NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    sorted_by_staff_id UUID,
    sorted_by_name VARCHAR(255),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'error', 'cancelled')),
    sorted_to_route VARCHAR(255),
    sorted_to_zone VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. SHAREABLE LINKS TABLE
-- =====================================================
CREATE TABLE shareable_links (
    link_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('parcel', 'trip')),
    entity_id UUID NOT NULL,
    share_token VARCHAR(255) NOT NULL UNIQUE,
    created_by_user_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    password_protected BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255),
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    allowed_actions JSONB DEFAULT '["view"]', -- ["view", "update_status", "add_notes"]
    metadata JSONB DEFAULT '{}'
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Parcel categories indexes
CREATE INDEX idx_parcel_categories_tenant ON parcel_categories(tenant_id);
CREATE INDEX idx_parcel_categories_active ON parcel_categories(tenant_id, is_active);

-- Parcels indexes
CREATE INDEX idx_parcels_tenant ON parcels(tenant_id);
CREATE INDEX idx_parcels_tracking ON parcels(tracking_number);
CREATE INDEX idx_parcels_status ON parcels(tenant_id, status);
CREATE INDEX idx_parcels_sender ON parcels(sender_id);
CREATE INDEX idx_parcels_receiver ON parcels(receiver_id);
CREATE INDEX idx_parcels_picker ON parcels(assigned_picker_id);
CREATE INDEX idx_parcels_driver ON parcels(assigned_driver_id);
CREATE INDEX idx_parcels_warehouse ON parcels(current_warehouse_id);
CREATE INDEX idx_parcels_created_date ON parcels(created_at);
CREATE INDEX idx_parcels_delivery_date ON parcels(estimated_delivery_at);
CREATE INDEX idx_parcels_location_sender ON parcels USING GIST(sender_location);
CREATE INDEX idx_parcels_location_receiver ON parcels USING GIST(receiver_location);

-- Parcel events indexes
CREATE INDEX idx_parcel_events_tenant ON parcel_events(tenant_id);
CREATE INDEX idx_parcel_events_parcel ON parcel_events(parcel_id);
CREATE INDEX idx_parcel_events_timestamp ON parcel_events(timestamp);
CREATE INDEX idx_parcel_events_type ON parcel_events(event_type);

-- Warehouses indexes
CREATE INDEX idx_warehouses_tenant ON warehouses(tenant_id);
CREATE INDEX idx_warehouses_active ON warehouses(tenant_id, is_active);
CREATE INDEX idx_warehouses_location ON warehouses USING GIST(location);

-- Warehouse inventory indexes
CREATE INDEX idx_warehouse_inventory_tenant ON warehouse_inventory(tenant_id);
CREATE INDEX idx_warehouse_inventory_warehouse ON warehouse_inventory(warehouse_id);
CREATE INDEX idx_warehouse_inventory_parcel ON warehouse_inventory(parcel_id);
CREATE INDEX idx_warehouse_inventory_status ON warehouse_inventory(status);

-- Sorting operations indexes
CREATE INDEX idx_sorting_operations_tenant ON sorting_operations(tenant_id);
CREATE INDEX idx_sorting_operations_warehouse ON sorting_operations(warehouse_id);
CREATE INDEX idx_sorting_operations_parcel ON sorting_operations(parcel_id);
CREATE INDEX idx_sorting_operations_status ON sorting_operations(status);

-- Shareable links indexes
CREATE INDEX idx_shareable_links_tenant ON shareable_links(tenant_id);
CREATE INDEX idx_shareable_links_token ON shareable_links(share_token);
CREATE INDEX idx_shareable_links_entity ON shareable_links(entity_type, entity_id);
CREATE INDEX idx_shareable_links_active ON shareable_links(is_active);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE parcel_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE parcels ENABLE ROW LEVEL SECURITY;
ALTER TABLE parcel_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE warehouses ENABLE ROW LEVEL SECURITY;
ALTER TABLE warehouse_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE sorting_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE shareable_links ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- TRIGGERS
-- =====================================================

CREATE TRIGGER update_parcel_categories_updated_at BEFORE UPDATE ON parcel_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_parcels_updated_at BEFORE UPDATE ON parcels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_warehouses_updated_at BEFORE UPDATE ON warehouses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_warehouse_inventory_updated_at BEFORE UPDATE ON warehouse_inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
