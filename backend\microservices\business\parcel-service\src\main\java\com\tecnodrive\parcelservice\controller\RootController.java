package com.tecnodrive.parcelservice.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/")
public class RootController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> root() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "TECNO DRIVE - Parcel Service");
        response.put("version", "1.0.0");
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "Parcel management service for TECNO DRIVE platform");
        
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("health", "/api/v1/parcels/health");
        endpoints.put("actuator", "/actuator/health");
        endpoints.put("parcels", "/api/v1/parcels");
        endpoints.put("create_parcel", "POST /api/v1/parcels");
        endpoints.put("assign_delivery", "POST /api/v1/parcels/deliveries");
        
        response.put("available_endpoints", endpoints);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("app", "Parcel Service");
        info.put("description", "Handles parcel creation, tracking, and delivery management");
        info.put("version", "1.0.0");
        info.put("build_time", LocalDateTime.now());
        info.put("java_version", System.getProperty("java.version"));
        info.put("spring_profiles", "default");
        
        return ResponseEntity.ok(info);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("service", "parcel-service");
        status.put("uptime", "Running");
        status.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(status);
    }
}
