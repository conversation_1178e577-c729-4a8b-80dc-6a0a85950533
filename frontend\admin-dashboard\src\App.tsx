import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import { ApolloProvider } from '@apollo/client';
import { store } from './store/store';
import { apolloClient } from './services/graphqlClient';
import EnhancedLayout from './components/Layout/EnhancedLayout';
import AuthGuard from './components/Auth/AuthGuard';
import CircuitBreaker from './components/Common/CircuitBreaker';
import RateLimiter from './components/Common/RateLimiter';
import { lightTheme } from './theme/theme';
import './App.css';

function App() {
  const handleError = (error: Error) => {
    console.error('Circuit breaker triggered:', error);
    // You can add error reporting here (e.g., Sentry, LogRocket)
  };

  return (
    <Provider store={store}>
      <ApolloProvider client={apolloClient}>
        <ThemeProvider theme={lightTheme}>
          <CssBaseline />
          <CircuitBreaker
            maxFailures={5}
            resetTimeout={60000}
            onError={handleError}
            fallback={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <h2>Service Temporarily Unavailable</h2>
                <p>We're experiencing technical difficulties. Please try again later.</p>
              </div>
            }
          >
            <RateLimiter
              maxRequests={100}
              windowMs={60000} // 1 minute
              showStatus={process.env.NODE_ENV === 'development'}
              onLimitExceeded={() => {
                console.warn('Rate limit exceeded');
              }}
            >
              <Router>
                <EnhancedLayout />
              </Router>
            </RateLimiter>
          </CircuitBreaker>
        </ThemeProvider>
      </ApolloProvider>
    </Provider>
  );
}

export default App;
