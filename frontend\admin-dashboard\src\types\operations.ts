// Operations Data Models for Live Dashboard

export interface Trip {
  trip_id: string;
  driver_id: string;
  vehicle_id: string;
  trip_type: 'passenger_ride' | 'parcel_delivery' | 'mixed';
  status: 'in_progress' | 'completed' | 'cancelled' | 'delayed' | 'off_route' | 'waiting_pickup' | 'waiting_delivery';
  current_location: {
    lat: number;
    lng: number;
  };
  estimated_arrival_time: string;
  actual_arrival_time?: string;
  start_time: string;
  end_time?: string;
  origin_address: string;
  destination_address: string;
  fare_amount: number;
  route_distance?: number;
  corporate_client_id?: string;
}

export interface Alert {
  alert_id: string;
  trip_id?: string;
  driver_id?: string;
  parcel_id?: string;
  alert_type: 'pickup_delay' | 'off_route' | 'cancellation' | 'parcel_delivery_delay' | 'parcel_lost' | 'accident' | 'vehicle_breakdown' | 'traffic_jam' | 'fuel_low' | 'maintenance_due';
  timestamp: string;
  description: string;
  status: 'new' | 'in_progress' | 'resolved' | 'dismissed';
  location?: {
    lat: number;
    lng: number;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  estimated_delay?: number; // in minutes
}

export interface Driver {
  driver_id: string;
  name: string;
  contact_info: {
    phone: string;
    email: string;
  };
  current_status: 'online' | 'offline' | 'busy' | 'break';
  current_location: {
    lat: number;
    lng: number;
  };
  vehicle_id: string;
  rating: number;
  can_deliver_parcels: boolean;
  profile_picture?: string;
  total_trips_today: number;
  total_earnings_today: number;
}

export interface Vehicle {
  vehicle_id: string;
  plate_number: string;
  make: string;
  model: string;
  capacity: {
    passengers: number;
    parcels: number;
  };
  current_status: 'available' | 'busy' | 'maintenance' | 'offline';
  current_location: {
    lat: number;
    lng: number;
  };
  fuel_level?: number;
  last_maintenance?: string;
  odometer?: number;
}

export interface Parcel {
  parcel_id: string;
  trip_id?: string;
  sender_id: string;
  receiver_id: string;
  pickup_address: string;
  delivery_address: string;
  status: 'order_created' | 'pickup_pending' | 'picked_up' | 'in_warehouse' | 'out_for_delivery' | 'delivered' | 'cancelled' | 'delivery_failed';
  pickup_time?: string;
  delivery_time?: string;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  item_description: string;
  tracking_number: string;
  assigned_picker_id?: string;
  assigned_driver_id?: string;
  estimated_pickup_time?: string;
  estimated_delivery_time?: string;
  current_location?: {
    lat: number;
    lng: number;
  };
}

export interface Picker {
  picker_id: string;
  name: string;
  contact_info: {
    phone: string;
    email: string;
  };
  current_status: 'available' | 'busy' | 'offline';
  current_location: {
    lat: number;
    lng: number;
  };
  rating: number;
  total_pickups_today: number;
  profile_picture?: string;
}

export interface RealtimeTracking {
  tracking_id: string;
  trip_id?: string;
  parcel_id?: string;
  timestamp: string;
  latitude: number;
  longitude: number;
  speed: number;
  heading: number;
  accuracy?: number;
}

export interface OperationsStats {
  total_active_trips: number;
  total_active_parcels: number;
  total_online_drivers: number;
  total_online_pickers: number;
  total_available_vehicles: number;
  total_alerts: number;
  total_critical_alerts: number;
  average_trip_duration: number;
  average_delivery_time: number;
  on_time_percentage: number;
  revenue_today: number;
  trips_completed_today: number;
  parcels_delivered_today: number;
}

export interface LiveOperationsData {
  trips: Trip[];
  alerts: Alert[];
  drivers: Driver[];
  vehicles: Vehicle[];
  parcels: Parcel[];
  pickers: Picker[];
  stats: OperationsStats;
  last_updated: string;
}

// Alert severity colors and icons
export const ALERT_SEVERITY_CONFIG = {
  low: {
    color: '#4CAF50',
    bgColor: '#E8F5E8',
    icon: 'info'
  },
  medium: {
    color: '#FF9800',
    bgColor: '#FFF3E0',
    icon: 'warning'
  },
  high: {
    color: '#F44336',
    bgColor: '#FFEBEE',
    icon: 'error'
  },
  critical: {
    color: '#D32F2F',
    bgColor: '#FFCDD2',
    icon: 'dangerous'
  }
};

// Trip status colors
export const TRIP_STATUS_CONFIG = {
  in_progress: { color: '#2196F3', label: 'قيد التنفيذ' },
  completed: { color: '#4CAF50', label: 'مكتملة' },
  cancelled: { color: '#F44336', label: 'ملغاة' },
  delayed: { color: '#FF9800', label: 'متأخرة' },
  off_route: { color: '#E91E63', label: 'خارج المسار' },
  waiting_pickup: { color: '#9C27B0', label: 'في انتظار الالتقاط' },
  waiting_delivery: { color: '#FF5722', label: 'في انتظار التسليم' }
};

// Parcel status colors
export const PARCEL_STATUS_CONFIG = {
  order_created: { color: '#607D8B', label: 'تم إنشاء الطلب' },
  pickup_pending: { color: '#9C27B0', label: 'قيد الالتقاط' },
  picked_up: { color: '#2196F3', label: 'تم الالتقاط' },
  in_warehouse: { color: '#795548', label: 'في المستودع' },
  out_for_delivery: { color: '#FF9800', label: 'قيد التسليم' },
  delivered: { color: '#4CAF50', label: 'تم التسليم' },
  cancelled: { color: '#F44336', label: 'ملغاة' },
  delivery_failed: { color: '#E91E63', label: 'فشل التسليم' }
};

// Driver/Picker status colors
export const DRIVER_STATUS_CONFIG = {
  online: { color: '#4CAF50', label: 'متصل' },
  offline: { color: '#9E9E9E', label: 'غير متصل' },
  busy: { color: '#FF9800', label: 'مشغول' },
  break: { color: '#2196F3', label: 'استراحة' }
};

// Vehicle status colors
export const VEHICLE_STATUS_CONFIG = {
  available: { color: '#4CAF50', label: 'متاح' },
  busy: { color: '#FF9800', label: 'مشغول' },
  maintenance: { color: '#F44336', label: 'صيانة' },
  offline: { color: '#9E9E9E', label: 'غير متصل' }
};

export interface AlertAction {
  action_id: string;
  label: string;
  type: 'primary' | 'secondary' | 'danger';
  handler: (alert: Alert) => void;
}

export interface MapMarker {
  id: string;
  type: 'vehicle' | 'parcel' | 'driver' | 'picker' | 'alert';
  position: {
    lat: number;
    lng: number;
  };
  data: Vehicle | Parcel | Driver | Picker | Alert;
  icon: string;
  color: string;
  popup?: string;
}

export interface WebSocketMessage {
  type: 'location_update' | 'status_change' | 'new_alert' | 'alert_resolved' | 'trip_update' | 'parcel_update';
  data: any;
  timestamp: string;
}
