{"name": "tecnodrive", "version": "1.0.0", "license": "MIT", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx workspace-lint && nx lint", "e2e": "nx e2e", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help", "start:auth": "nx serve auth-service", "start:booking": "nx serve booking-service", "start:location": "nx serve location-service", "start:notification": "nx serve notification-service", "start:mobile": "nx serve mobile-app", "build:all": "nx run-many --target=build --all", "test:all": "nx run-many --target=test --all", "docker:build": "nx run-many --target=docker-build --all", "k8s:deploy": "nx run-many --target=k8s-deploy --all", "start:complete": "START_COMPLETE_SYSTEM.bat", "stop:complete": "STOP_COMPLETE_SYSTEM.bat", "integration": "node FRONTEND_BACKEND_INTEGRATION.js", "extract-data": "node TECNO_DRIVE_DATABASE_EXTRACTION.js", "install-frontend": "cd frontend/admin-dashboard && npm install", "build-frontend": "cd frontend/admin-dashboard && npm run build", "setup-integration": "npm install axios && npm run integration"}, "private": true, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "express": "^4.18.0", "typeorm": "^0.3.0", "pg": "^8.11.0", "redis": "^4.6.0", "kafkajs": "^2.2.4", "opentelemetry": "^1.0.0", "@opentelemetry/api": "^1.4.1", "@opentelemetry/sdk-node": "^0.41.0", "@opentelemetry/exporter-jaeger": "^1.15.0", "prom-client": "^14.2.0", "helmet": "^7.0.0", "rate-limiter-flexible": "^2.4.1", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "rxjs": "^7.8.0", "reflect-metadata": "^0.1.13", "tslib": "^2.3.0", "axios": "^1.6.2"}, "devDependencies": {"@nx/workspace": "16.5.1", "@nx/node": "16.5.1", "@nx/nest": "16.5.1", "@nx/jest": "16.5.1", "@nx/linter": "16.5.1", "@nx/eslint-plugin": "16.5.1", "@types/node": "18.14.2", "@types/express": "^4.17.17", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint": "~8.46.0", "eslint-config-prettier": "8.1.0", "jest": "^29.4.1", "nx": "16.5.1", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.1.3"}, "workspaces": ["apps/*", "libs/*"]}