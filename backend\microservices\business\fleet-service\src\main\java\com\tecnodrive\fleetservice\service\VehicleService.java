package com.tecnodrive.fleetservice.service;

import com.tecnodrive.fleetservice.dto.VehicleRequest;
import com.tecnodrive.fleetservice.dto.VehicleResponse;
import com.tecnodrive.fleetservice.entity.Vehicle;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Vehicle Service Interface
 * 
 * Defines business logic operations for vehicle fleet management
 */
public interface VehicleService {

    /**
     * Create a new vehicle
     */
    VehicleResponse createVehicle(VehicleRequest request);

    /**
     * Get vehicle by ID
     */
    VehicleResponse getVehicle(String id);

    /**
     * Get vehicle by plate number
     */
    VehicleResponse getVehicleByPlateNumber(String plateNumber);

    /**
     * Get all vehicles
     */
    List<VehicleResponse> getAllVehicles();

    /**
     * Get vehicles with pagination
     */
    Page<VehicleResponse> getVehicles(Pageable pageable);

    /**
     * Get vehicles by company
     */
    List<VehicleResponse> getVehiclesByCompany(String companyId);

    /**
     * Get vehicles by company with pagination
     */
    Page<VehicleResponse> getVehiclesByCompany(String companyId, Pageable pageable);

    /**
     * Update vehicle
     */
    VehicleResponse updateVehicle(String id, VehicleRequest request);

    /**
     * Delete vehicle
     */
    void deleteVehicle(String id);

    /**
     * Get vehicles by status
     */
    List<VehicleResponse> getVehiclesByStatus(Vehicle.VehicleStatus status);

    /**
     * Get vehicles by company and status
     */
    List<VehicleResponse> getVehiclesByCompanyAndStatus(String companyId, Vehicle.VehicleStatus status);

    /**
     * Get available vehicles
     */
    List<VehicleResponse> getAvailableVehicles();

    /**
     * Get available vehicles by company
     */
    List<VehicleResponse> getAvailableVehiclesByCompany(String companyId);

    /**
     * Assign driver to vehicle
     */
    VehicleResponse assignDriver(String vehicleId, String driverId);

    /**
     * Unassign driver from vehicle
     */
    VehicleResponse unassignDriver(String vehicleId);

    /**
     * Update vehicle status
     */
    VehicleResponse updateVehicleStatus(String vehicleId, Vehicle.VehicleStatus status);

    /**
     * Update odometer reading
     */
    VehicleResponse updateOdometerReading(String vehicleId, BigDecimal odometerReading);

    /**
     * Schedule maintenance
     */
    VehicleResponse scheduleMaintenance(String vehicleId, LocalDate maintenanceDate);

    /**
     * Complete maintenance
     */
    VehicleResponse completeMaintenance(String vehicleId, BigDecimal odometerReading, LocalDate nextMaintenanceDate);

    /**
     * Get vehicles needing maintenance
     */
    List<VehicleResponse> getVehiclesNeedingMaintenance();

    /**
     * Get vehicles needing maintenance by company
     */
    List<VehicleResponse> getVehiclesNeedingMaintenanceByCompany(String companyId);

    /**
     * Get vehicles with expiring insurance
     */
    List<VehicleResponse> getVehiclesWithExpiringInsurance(int warningDays);

    /**
     * Get vehicles with expiring registration
     */
    List<VehicleResponse> getVehiclesWithExpiringRegistration(int warningDays);

    /**
     * Search vehicles
     */
    Page<VehicleResponse> searchVehicles(String searchTerm, Pageable pageable);

    /**
     * Search vehicles by company
     */
    Page<VehicleResponse> searchVehiclesByCompany(String companyId, String searchTerm, Pageable pageable);

    /**
     * Get vehicle statistics
     */
    VehicleResponse.VehicleStatistics getVehicleStatistics(String companyId);

    /**
     * Get vehicle summary list
     */
    List<VehicleResponse.VehicleSummary> getVehicleSummaries(String companyId);

    /**
     * Get vehicles by type
     */
    List<VehicleResponse> getVehiclesByType(String companyId, Vehicle.VehicleType vehicleType);

    /**
     * Get vehicles by fuel type
     */
    List<VehicleResponse> getVehiclesByFuelType(String companyId, Vehicle.FuelType fuelType);

    /**
     * Get unassigned vehicles
     */
    List<VehicleResponse> getUnassignedVehicles(String companyId);

    /**
     * Get vehicles by driver
     */
    List<VehicleResponse> getVehiclesByDriver(String driverId);

    /**
     * Validate vehicle request
     */
    void validateVehicleRequest(VehicleRequest request);

    /**
     * Check if plate number is available
     */
    boolean isPlateNumberAvailable(String plateNumber);

    /**
     * Check if VIN is available
     */
    boolean isVinAvailable(String vin);
}
