import React, { useState } from 'react';
import {
  <PERSON>I<PERSON>,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Badge,
  Box,
  useTheme,
  alpha,
  Tooltip,
} from '@mui/material';
import {
  ExpandLess,
  ExpandMore,
  Circle as CircleIcon,
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';

interface NavigationItemProps {
  item: {
    id: string;
    title: string;
    icon?: React.ReactNode;
    path?: string;
    badge?: string | number;
    children?: Array<{
      id: string;
      title: string;
      path: string;
      badge?: string | number;
    }>;
    disabled?: boolean;
  };
  level?: number;
  collapsed?: boolean;
  onItemClick?: (item: any) => void;
}

const NavigationItem: React.FC<NavigationItemProps> = ({
  item,
  level = 0,
  collapsed = false,
  onItemClick,
}) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);

  const hasChildren = item.children && item.children.length > 0;
  const isActive = item.path ? location.pathname === item.path : false;
  const hasActiveChild = hasChildren && item.children?.some(child => location.pathname === child.path);

  const handleClick = () => {
    if (item.disabled) return;

    if (hasChildren) {
      setOpen(!open);
    } else if (item.path) {
      navigate(item.path);
      onItemClick?.(item);
    }
  };

  const getItemStyles = () => {
    const baseStyles = {
      borderRadius: theme.shape.borderRadius,
      margin: `${theme.spacing(0.5)} ${theme.spacing(1)}`,
      transition: theme.transitions.create(['all'], {
        duration: theme.transitions.duration.short,
      }),
      position: 'relative' as const,
      overflow: 'hidden' as const,
    };

    if (isActive || hasActiveChild) {
      return {
        ...baseStyles,
        backgroundColor: alpha(theme.palette.primary.main, 0.1),
        color: theme.palette.primary.main,
        '&::before': {
          content: '""',
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: 3,
          backgroundColor: theme.palette.primary.main,
          borderRadius: '0 2px 2px 0',
        },
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary.main, 0.15),
        },
      };
    }

    return {
      ...baseStyles,
      '&:hover': {
        backgroundColor: alpha(theme.palette.text.primary, 0.04),
        transform: 'translateX(4px)',
      },
    };
  };

  const getIconStyles = () => ({
    minWidth: collapsed ? 'auto' : 40,
    color: isActive || hasActiveChild ? theme.palette.primary.main : theme.palette.text.secondary,
    transition: theme.transitions.create(['color'], {
      duration: theme.transitions.duration.short,
    }),
  });

  const getTextStyles = () => ({
    '& .MuiListItemText-primary': {
      fontSize: level > 0 ? '0.875rem' : '0.95rem',
      fontWeight: isActive || hasActiveChild ? 600 : 400,
      color: isActive || hasActiveChild ? theme.palette.primary.main : theme.palette.text.primary,
    },
  });

  const renderBadge = (badgeValue?: string | number) => {
    if (!badgeValue) return null;

    return (
      <Badge
        badgeContent={badgeValue}
        color="error"
        sx={{
          '& .MuiBadge-badge': {
            fontSize: '0.7rem',
            height: 16,
            minWidth: 16,
            padding: '0 4px',
          },
        }}
      >
        <Box />
      </Badge>
    );
  };

  const renderMainItem = () => (
    <ListItem disablePadding sx={{ display: 'block' }}>
      <Tooltip
        title={collapsed ? item.title : ''}
        placement="right"
        arrow
        disableHoverListener={!collapsed}
      >
        <ListItemButton
          onClick={handleClick}
          disabled={item.disabled}
          sx={getItemStyles()}
        >
          {item.icon && (
            <ListItemIcon sx={getIconStyles()}>
              {item.icon}
            </ListItemIcon>
          )}
          
          {!collapsed && (
            <>
              <ListItemText
                primary={item.title}
                sx={getTextStyles()}
              />
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {renderBadge(item.badge)}
                
                {hasChildren && (
                  open ? <ExpandLess /> : <ExpandMore />
                )}
              </Box>
            </>
          )}
        </ListItemButton>
      </Tooltip>
    </ListItem>
  );

  const renderChildren = () => {
    if (!hasChildren || collapsed) return null;

    return (
      <Collapse in={open} timeout="auto" unmountOnExit>
        <Box sx={{ pl: level > 0 ? 2 : 4 }}>
          {item.children?.map((child) => (
            <ListItem key={child.id} disablePadding>
              <ListItemButton
                onClick={() => {
                  if (child.path) {
                    navigate(child.path);
                    onItemClick?.(child);
                  }
                }}
                disabled={child.disabled}
                sx={{
                  borderRadius: theme.shape.borderRadius,
                  margin: `${theme.spacing(0.25)} 0`,
                  minHeight: 36,
                  backgroundColor: location.pathname === child.path 
                    ? alpha(theme.palette.primary.main, 0.08)
                    : 'transparent',
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.04),
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 32 }}>
                  <CircleIcon 
                    sx={{ 
                      fontSize: 6,
                      color: location.pathname === child.path 
                        ? theme.palette.primary.main 
                        : theme.palette.text.disabled,
                    }} 
                  />
                </ListItemIcon>
                
                <ListItemText
                  primary={child.title}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '0.8rem',
                      fontWeight: location.pathname === child.path ? 500 : 400,
                      color: location.pathname === child.path 
                        ? theme.palette.primary.main 
                        : theme.palette.text.secondary,
                    },
                  }}
                />
                
                {renderBadge(child.badge)}
              </ListItemButton>
            </ListItem>
          ))}
        </Box>
      </Collapse>
    );
  };

  return (
    <>
      {renderMainItem()}
      {renderChildren()}
    </>
  );
};

export default NavigationItem;
