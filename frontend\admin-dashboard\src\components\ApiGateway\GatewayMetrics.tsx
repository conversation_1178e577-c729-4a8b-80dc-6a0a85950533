import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Timeline as TimelineIcon,
  Api as ApiIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';
import { apiGatewayService, GatewayMetricsDto } from '../../services/apiGatewayService';

const GatewayMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState<GatewayMetricsDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('last_24_hours');
  const [granularity, setGranularity] = useState<'minute' | 'hour' | 'day'>('hour');

  // Load metrics data
  const loadMetrics = async () => {
    try {
      setLoading(true);
      const response = await apiGatewayService.getGatewayMetrics({
        granularity,
      });
      
      if (response.success && response.data) {
        setMetrics(response.data);
      }
    } catch (error) {
      console.error('Error loading metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadMetrics, 30000);
    return () => clearInterval(interval);
  }, [timeRange, granularity]);

  // Mock real-time data for demonstration
  const realtimeData = [
    { time: '14:00', requests: 120, responseTime: 245 },
    { time: '14:05', requests: 135, responseTime: 230 },
    { time: '14:10', requests: 142, responseTime: 255 },
    { time: '14:15', requests: 128, responseTime: 240 },
    { time: '14:20', requests: 156, responseTime: 220 },
    { time: '14:25', requests: 148, responseTime: 235 },
    { time: '14:30', requests: 162, responseTime: 210 },
  ];

  const errorDistributionData = metrics ? Object.entries(metrics.errorsByCode).map(([code, count]) => ({
    code,
    count,
    percentage: (count / metrics.failedRequests) * 100,
  })) : [];

  const topEndpointsData = metrics?.topEndpoints || [];

  const successRate = metrics ? ((metrics.successfulRequests / metrics.totalRequests) * 100) : 0;

  const getStatusColor = (rate: number) => {
    if (rate >= 99) return 'success';
    if (rate >= 95) return 'warning';
    return 'error';
  };

  const getResponseTimeColor = (time: number) => {
    if (time <= 200) return 'success';
    if (time <= 500) return 'warning';
    return 'error';
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            مراقبة API Gateway
          </Typography>
          <Typography variant="body1" color="text.secondary">
            إحصائيات ومراقبة أداء API Gateway في الوقت الفعلي
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>الفترة الزمنية</InputLabel>
            <Select
              value={timeRange}
              label="الفترة الزمنية"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="last_hour">آخر ساعة</MenuItem>
              <MenuItem value="last_24_hours">آخر 24 ساعة</MenuItem>
              <MenuItem value="last_7_days">آخر 7 أيام</MenuItem>
              <MenuItem value="last_30_days">آخر 30 يوم</MenuItem>
            </Select>
          </FormControl>
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>التفصيل</InputLabel>
            <Select
              value={granularity}
              label="التفصيل"
              onChange={(e) => setGranularity(e.target.value as any)}
            >
              <MenuItem value="minute">دقيقة</MenuItem>
              <MenuItem value="hour">ساعة</MenuItem>
              <MenuItem value="day">يوم</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadMetrics}
            disabled={loading}
          >
            تحديث
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <ApiIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.totalRequests.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: getStatusColor(successRate) + '.main' }}>
                  <CheckCircleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {successRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل النجاح
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: getResponseTimeColor(metrics?.averageResponseTime || 0) + '.main' }}>
                  <SpeedIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.averageResponseTime || 0}ms
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط زمن الاستجابة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.requestsPerSecond.toFixed(1) || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    طلبات/ثانية
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Real-time Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                الطلبات في الوقت الفعلي
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={realtimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="requests" 
                    stroke="#1976d2" 
                    fill="#1976d2" 
                    fillOpacity={0.3}
                    name="عدد الطلبات"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزيع رموز الأخطاء
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={errorDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ code, percentage }) => `${code} (${percentage.toFixed(1)}%)`}
                  >
                    {errorDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={['#f44336', '#ff9800', '#2196f3', '#4caf50', '#9c27b0'][index % 5]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Response Time Chart */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                زمن الاستجابة
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={realtimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="responseTime" 
                    stroke="#ff9800" 
                    strokeWidth={3}
                    name="زمن الاستجابة (ms)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Endpoints and Error Details */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                أكثر المسارات استخداماً
              </Typography>
              <List>
                {topEndpointsData.map((endpoint, index) => (
                  <ListItem key={index}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <ApiIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={endpoint.path}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {endpoint.requests.toLocaleString()} طلب - متوسط الاستجابة: {endpoint.averageResponseTime}ms
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={(endpoint.requests / (metrics?.totalRequests || 1)) * 100} 
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                تفاصيل الأخطاء
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>رمز الخطأ</TableCell>
                      <TableCell align="right">العدد</TableCell>
                      <TableCell align="right">النسبة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {errorDistributionData.map((error) => (
                      <TableRow key={error.code}>
                        <TableCell>
                          <Chip 
                            label={error.code} 
                            size="small" 
                            color="error" 
                            variant="outlined" 
                          />
                        </TableCell>
                        <TableCell align="right">{error.count.toLocaleString()}</TableCell>
                        <TableCell align="right">{error.percentage.toFixed(1)}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Health Status */}
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                حالة الخدمات
              </Typography>
              <Grid container spacing={2}>
                {[
                  { name: 'Auth Service', status: 'UP', responseTime: 45 },
                  { name: 'Fleet Service', status: 'UP', responseTime: 67 },
                  { name: 'Rides Service', status: 'UP', responseTime: 52 },
                  { name: 'Notifications Service', status: 'UP', responseTime: 38 },
                  { name: 'SaaS Service', status: 'UP', responseTime: 71 },
                ].map((service, index) => (
                  <Grid item xs={12} sm={6} md={2.4} key={index}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Avatar 
                        sx={{ 
                          bgcolor: service.status === 'UP' ? 'success.main' : 'error.main',
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        {service.status === 'UP' ? <CheckCircleIcon /> : <ErrorIcon />}
                      </Avatar>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {service.name}
                      </Typography>
                      <Chip 
                        label={service.status} 
                        size="small" 
                        color={service.status === 'UP' ? 'success' : 'error'}
                        sx={{ mt: 1 }}
                      />
                      <Typography variant="caption" display="block" color="text.secondary">
                        {service.responseTime}ms
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GatewayMetrics;
