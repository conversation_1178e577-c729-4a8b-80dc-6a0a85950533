package com.tecnodrive.saasservice.controller;

import com.tecnodrive.common.dto.common.ApiResponse;
import com.tecnodrive.saasservice.dto.TenantRequest;
import com.tecnodrive.saasservice.dto.TenantResponse;
import com.tecnodrive.saasservice.dto.TenantUpdateRequest;
import com.tecnodrive.saasservice.entity.Tenant;
import com.tecnodrive.saasservice.service.TenantService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

/**
 * Tenant Controller
 * 
 * REST API endpoints for tenant management
 */
@RestController
@RequestMapping("/api/saas/tenants")
@RequiredArgsConstructor
@Slf4j
public class TenantController {

    private final TenantService tenantService;

    /**
     * Create a new tenant
     */
    @PostMapping
    public ResponseEntity<ApiResponse<TenantResponse>> createTenant(
            @Valid @RequestBody TenantRequest request) {
        log.info("Creating tenant: {}", request.getName());
        
        TenantResponse response = tenantService.createTenant(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Tenant created successfully", response));
    }

    /**
     * Get all tenants (simple list)
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getAllTenants() {
        log.debug("Retrieving all tenants");
        
        List<TenantResponse> responses = tenantService.getAllTenants();
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get tenants with pagination
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<TenantResponse>>> getTenants(
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("Retrieving tenants with pagination");
        
        Page<TenantResponse> responses = tenantService.getTenants(pageable);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get tenant by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<TenantResponse>> getTenant(@PathVariable String id) {
        log.debug("Retrieving tenant with ID: {}", id);
        
        TenantResponse response = tenantService.getTenant(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Get tenant by name
     */
    @GetMapping("/name/{name}")
    public ResponseEntity<ApiResponse<TenantResponse>> getTenantByName(@PathVariable String name) {
        log.debug("Retrieving tenant with name: {}", name);
        
        TenantResponse response = tenantService.getTenantByName(name);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Update tenant
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<TenantResponse>> updateTenant(
            @PathVariable String id,
            @Valid @RequestBody TenantUpdateRequest request) {
        log.info("Updating tenant with ID: {}", id);
        
        TenantResponse response = tenantService.updateTenant(id, request);
        return ResponseEntity.ok(ApiResponse.success("Tenant updated successfully", response));
    }

    /**
     * Delete tenant
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteTenant(@PathVariable String id) {
        log.info("Deleting tenant with ID: {}", id);
        
        tenantService.deleteTenant(id);
        return ResponseEntity.ok(ApiResponse.success("Tenant deleted successfully", null));
    }

    /**
     * Get tenants by type
     */
    @GetMapping("/type/{type}")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getTenantsByType(
            @PathVariable String type) {
        log.debug("Retrieving tenants by type: {}", type);

        Tenant.TenantType tenantType;
        try {
            tenantType = Tenant.TenantType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid tenant type: {}", type);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Invalid tenant type: " + type));
        }

        List<TenantResponse> responses = tenantService.getTenantsByType(tenantType);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get tenants by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getTenantsByStatus(
            @PathVariable Tenant.TenantStatus status) {
        log.debug("Retrieving tenants by status: {}", status);
        
        List<TenantResponse> responses = tenantService.getTenantsByStatus(status);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get active tenants
     */
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getActiveTenants() {
        log.debug("Retrieving active tenants");
        
        List<TenantResponse> responses = tenantService.getActiveTenants();
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Search tenants
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<TenantResponse>>> searchTenants(
            @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("Searching tenants with query: {}", q);
        
        Page<TenantResponse> responses = tenantService.searchTenants(q, pageable);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Activate tenant
     */
    @PostMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<TenantResponse>> activateTenant(@PathVariable String id) {
        log.info("Activating tenant with ID: {}", id);
        
        TenantResponse response = tenantService.activateTenant(id);
        return ResponseEntity.ok(ApiResponse.success("Tenant activated successfully", response));
    }

    /**
     * Deactivate tenant
     */
    @PostMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<TenantResponse>> deactivateTenant(@PathVariable String id) {
        log.info("Deactivating tenant with ID: {}", id);
        
        TenantResponse response = tenantService.deactivateTenant(id);
        return ResponseEntity.ok(ApiResponse.success("Tenant deactivated successfully", response));
    }

    /**
     * Suspend tenant
     */
    @PostMapping("/{id}/suspend")
    public ResponseEntity<ApiResponse<TenantResponse>> suspendTenant(@PathVariable String id) {
        log.info("Suspending tenant with ID: {}", id);
        
        TenantResponse response = tenantService.suspendTenant(id);
        return ResponseEntity.ok(ApiResponse.success("Tenant suspended successfully", response));
    }

    /**
     * Extend subscription
     */
    @PostMapping("/{id}/extend-subscription")
    public ResponseEntity<ApiResponse<TenantResponse>> extendSubscription(
            @PathVariable String id,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant newEndDate) {
        log.info("Extending subscription for tenant with ID: {} until {}", id, newEndDate);
        
        TenantResponse response = tenantService.extendSubscription(id, newEndDate);
        return ResponseEntity.ok(ApiResponse.success("Subscription extended successfully", response));
    }

    /**
     * Get tenants with expiring subscriptions
     */
    @GetMapping("/expiring")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getTenantsWithExpiringSubscriptions(
            @RequestParam(defaultValue = "30") int daysAhead) {
        log.debug("Retrieving tenants with subscriptions expiring in {} days", daysAhead);
        
        List<TenantResponse> responses = tenantService.getTenantsWithExpiringSubscriptions(daysAhead);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get expired tenants
     */
    @GetMapping("/expired")
    public ResponseEntity<ApiResponse<List<TenantResponse>>> getExpiredTenants() {
        log.debug("Retrieving expired tenants");
        
        List<TenantResponse> responses = tenantService.getExpiredTenants();
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Update tenant branding
     */
    @PutMapping("/{id}/branding")
    public ResponseEntity<ApiResponse<TenantResponse>> updateTenantBranding(
            @PathVariable String id,
            @RequestBody String brandingConfig) {
        log.info("Updating branding for tenant with ID: {}", id);
        
        TenantResponse response = tenantService.updateTenantBranding(id, brandingConfig);
        return ResponseEntity.ok(ApiResponse.success("Tenant branding updated successfully", response));
    }

    /**
     * Update tenant features
     */
    @PutMapping("/{id}/features")
    public ResponseEntity<ApiResponse<TenantResponse>> updateTenantFeatures(
            @PathVariable String id,
            @RequestBody String featureFlags) {
        log.info("Updating features for tenant with ID: {}", id);
        
        TenantResponse response = tenantService.updateTenantFeatures(id, featureFlags);
        return ResponseEntity.ok(ApiResponse.success("Tenant features updated successfully", response));
    }

    /**
     * Get tenant analytics
     */
    @GetMapping("/analytics")
    public ResponseEntity<ApiResponse<TenantService.TenantAnalytics>> getTenantAnalytics() {
        log.debug("Retrieving tenant analytics");
        
        TenantService.TenantAnalytics analytics = tenantService.getTenantAnalytics();
        return ResponseEntity.ok(ApiResponse.success(analytics));
    }

    /**
     * Check tenant name availability
     */
    @GetMapping("/check-name/{name}")
    public ResponseEntity<ApiResponse<Boolean>> checkTenantNameAvailability(@PathVariable String name) {
        log.debug("Checking availability for tenant name: {}", name);
        
        boolean available = tenantService.isTenantNameAvailable(name);
        return ResponseEntity.ok(ApiResponse.success(available));
    }
}
