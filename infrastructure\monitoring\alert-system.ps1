#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Advanced Alert System
# This script monitors services and sends alerts when issues are detected

param(
    [Parameter(Mandatory=$false)]
    [int]$CheckInterval = 30,
    
    [Parameter(Mandatory=$false)]
    [string]$LogFile = "alerts.log"
)

Write-Host "🚨 TECNO DRIVE Alert System" -ForegroundColor Red
Write-Host "===========================" -ForegroundColor Cyan
Write-Host "Check Interval: $CheckInterval seconds" -ForegroundColor Yellow
Write-Host "Log File: $LogFile" -ForegroundColor Yellow

# Alert configuration
$AlertConfig = @{
    Services = @(
        @{Name="PostgreSQL"; Container="infra-postgres-1"; Critical=$true},
        @{Name="Redis"; Container="infra-redis-1"; Critical=$true},
        @{Name="Eureka Server"; Container="infra-eureka-server-1"; Critical=$true},
        @{Name="Auth Service"; Container="auth-service-fixed"; Critical=$true},
        @{Name="API Gateway"; Container="api-gateway-fixed"; Critical=$true}
    )
    Thresholds = @{
        CPUUsage = 80
        MemoryUsage = 85
        DiskUsage = 90
        ResponseTime = 5000
    }
}

function Write-Alert {
    param(
        [string]$Level,
        [string]$Message,
        [string]$Service = ""
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Service - $Message"
    
    # Write to console with colors
    switch ($Level) {
        "CRITICAL" { Write-Host "🔴 $logEntry" -ForegroundColor Red }
        "WARNING" { Write-Host "🟡 $logEntry" -ForegroundColor Yellow }
        "INFO" { Write-Host "🟢 $logEntry" -ForegroundColor Green }
        default { Write-Host "ℹ️ $logEntry" -ForegroundColor White }
    }
    
    # Write to log file
    $logEntry | Add-Content -Path $LogFile
}

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Container,
        [bool]$Critical = $false
    )
    
    try {
        $status = docker inspect --format='{{.State.Status}}' $Container 2>$null
        
        if ($status -eq "running") {
            Write-Alert -Level "INFO" -Service $ServiceName -Message "Service is running normally"
            return $true
        } else {
            $alertLevel = if ($Critical) { "CRITICAL" } else { "WARNING" }
            Write-Alert -Level $alertLevel -Service $ServiceName -Message "Service is not running (Status: $status)"
            return $false
        }
    }
    catch {
        $alertLevel = if ($Critical) { "CRITICAL" } else { "WARNING" }
        Write-Alert -Level $alertLevel -Service $ServiceName -Message "Unable to check service status"
        return $false
    }
}

function Test-SystemResources {
    try {
        # Check CPU usage
        $cpuUsage = (Get-WmiObject Win32_Processor | Measure-Object -Property LoadPercentage -Average).Average
        if ($cpuUsage -gt $AlertConfig.Thresholds.CPUUsage) {
            Write-Alert -Level "WARNING" -Service "System" -Message "High CPU usage: $cpuUsage%"
        }
        
        # Check memory usage
        $totalMemory = (Get-WmiObject Win32_ComputerSystem).TotalPhysicalMemory
        $freeMemory = (Get-WmiObject Win32_OperatingSystem).FreePhysicalMemory * 1024
        $memoryUsage = [math]::Round(((($totalMemory - $freeMemory) / $totalMemory) * 100), 2)
        
        if ($memoryUsage -gt $AlertConfig.Thresholds.MemoryUsage) {
            Write-Alert -Level "WARNING" -Service "System" -Message "High memory usage: $memoryUsage%"
        }
        
        # Check disk usage
        $disk = Get-WmiObject Win32_LogicalDisk -Filter "DeviceID='C:'"
        $diskUsage = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)
        
        if ($diskUsage -gt $AlertConfig.Thresholds.DiskUsage) {
            Write-Alert -Level "CRITICAL" -Service "System" -Message "High disk usage: $diskUsage%"
        }
        
        Write-Alert -Level "INFO" -Service "System" -Message "Resources OK - CPU: $cpuUsage%, Memory: $memoryUsage%, Disk: $diskUsage%"
    }
    catch {
        Write-Alert -Level "WARNING" -Service "System" -Message "Unable to check system resources"
    }
}

function Test-EndpointResponse {
    param(
        [string]$ServiceName,
        [string]$Url
    )
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
        $stopwatch.Stop()
        
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        if ($response.StatusCode -eq 200) {
            if ($responseTime -gt $AlertConfig.Thresholds.ResponseTime) {
                Write-Alert -Level "WARNING" -Service $ServiceName -Message "Slow response time: ${responseTime}ms"
            } else {
                Write-Alert -Level "INFO" -Service $ServiceName -Message "Endpoint responding normally (${responseTime}ms)"
            }
            return $true
        } else {
            Write-Alert -Level "WARNING" -Service $ServiceName -Message "Unexpected response code: $($response.StatusCode)"
            return $false
        }
    }
    catch {
        Write-Alert -Level "CRITICAL" -Service $ServiceName -Message "Endpoint not responding: $($_.Exception.Message)"
        return $false
    }
}

function Send-EmailAlert {
    param(
        [string]$Subject,
        [string]$Body
    )
    
    # Email configuration (configure as needed)
    $EmailConfig = @{
        SMTPServer = "smtp.gmail.com"
        Port = 587
        Username = "<EMAIL>"
        Password = "your-app-password"
        To = @("<EMAIL>", "<EMAIL>")
        From = "<EMAIL>"
    }
    
    # Uncomment and configure for actual email sending
    # try {
    #     $credential = New-Object System.Management.Automation.PSCredential($EmailConfig.Username, (ConvertTo-SecureString $EmailConfig.Password -AsPlainText -Force))
    #     Send-MailMessage -SmtpServer $EmailConfig.SMTPServer -Port $EmailConfig.Port -UseSsl -Credential $credential -From $EmailConfig.From -To $EmailConfig.To -Subject $Subject -Body $Body
    #     Write-Alert -Level "INFO" -Service "Email" -Message "Alert email sent successfully"
    # }
    # catch {
    #     Write-Alert -Level "WARNING" -Service "Email" -Message "Failed to send alert email: $($_.Exception.Message)"
    # }
}

function Start-HealthCheck {
    Write-Alert -Level "INFO" -Service "AlertSystem" -Message "Starting health check cycle"
    
    $failedServices = @()
    
    # Check all configured services
    foreach ($service in $AlertConfig.Services) {
        $isHealthy = Test-ServiceHealth -ServiceName $service.Name -Container $service.Container -Critical $service.Critical
        if (-not $isHealthy) {
            $failedServices += $service.Name
        }
    }
    
    # Check system resources
    Test-SystemResources
    
    # Check critical endpoints
    $endpoints = @(
        @{Name="Admin Dashboard"; Url="http://localhost:3000"},
        @{Name="API Gateway"; Url="http://localhost:8080/actuator/health"},
        @{Name="Auth Service"; Url="http://localhost:8081/actuator/health"}
    )
    
    foreach ($endpoint in $endpoints) {
        Test-EndpointResponse -ServiceName $endpoint.Name -Url $endpoint.Url
    }
    
    # Send summary alert if there are failed services
    if ($failedServices.Count -gt 0) {
        $subject = "TECNO DRIVE Alert: $($failedServices.Count) Service(s) Down"
        $body = "The following services are experiencing issues:`n`n" + ($failedServices -join "`n")
        Send-EmailAlert -Subject $subject -Body $body
    }
    
    Write-Alert -Level "INFO" -Service "AlertSystem" -Message "Health check cycle completed"
}

# Main monitoring loop
Write-Alert -Level "INFO" -Service "AlertSystem" -Message "Alert system started"

try {
    while ($true) {
        Start-HealthCheck
        Write-Host "`n⏳ Waiting $CheckInterval seconds for next check..." -ForegroundColor Cyan
        Start-Sleep -Seconds $CheckInterval
    }
}
catch {
    Write-Alert -Level "CRITICAL" -Service "AlertSystem" -Message "Alert system stopped unexpectedly: $($_.Exception.Message)"
}
finally {
    Write-Alert -Level "INFO" -Service "AlertSystem" -Message "Alert system stopped"
}
