import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Download as DownloadIcon,
  AttachMoney,
  TrendingUp,
  AccountBalance,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchPayments } from '../../store/slices/paymentsSlice';

const PaymentsManagement: React.FC = () => {
  const dispatch = useDispatch();
  const { payments, loading, totalRevenue, todayRevenue, pendingPayments } = useSelector(
    (state: RootState) => state.payments
  );

  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    dispatch(fetchPayments() as any);
  }, [dispatch]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'معلق', color: 'warning' as const },
      COMPLETED: { label: 'مكتمل', color: 'success' as const },
      FAILED: { label: 'فاشل', color: 'error' as const },
      REFUNDED: { label: 'مسترد', color: 'info' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getMethodChip = (method: string) => {
    const methodConfig = {
      CASH: { label: 'نقدي', color: 'success' as const },
      CARD: { label: 'بطاقة', color: 'primary' as const },
      WALLET: { label: 'محفظة', color: 'secondary' as const },
      BANK_TRANSFER: { label: 'تحويل بنكي', color: 'info' as const },
    };

    const config = methodConfig[method as keyof typeof methodConfig] || { label: method, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
      />
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'رقم المعاملة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          #{params.value.slice(-8)}
        </Typography>
      ),
    },
    {
      field: 'rideId',
      headerName: 'رقم الرحلة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          #{params.value.slice(-6)}
        </Typography>
      ),
    },
    {
      field: 'amount',
      headerName: 'المبلغ',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
          {params.value} {params.row.currency}
        </Typography>
      ),
    },
    {
      field: 'method',
      headerName: 'طريقة الدفع',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getMethodChip(params.value),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'transactionId',
      headerName: 'رقم المرجع',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value || 'غير متوفر'}
        </Typography>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ المعاملة',
      width: 150,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
  ];

  // Mock data for demonstration
  const mockPayments = [
    {
      id: '1',
      rideId: 'ride-001',
      amount: 150,
      currency: 'ريال',
      method: 'CASH',
      status: 'COMPLETED',
      transactionId: 'TXN-001',
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      rideId: 'ride-002',
      amount: 200,
      currency: 'ريال',
      method: 'CARD',
      status: 'COMPLETED',
      transactionId: 'TXN-002',
      createdAt: new Date().toISOString(),
    },
    {
      id: '3',
      rideId: 'ride-003',
      amount: 350,
      currency: 'ريال',
      method: 'WALLET',
      status: 'PENDING',
      transactionId: null,
      createdAt: new Date().toISOString(),
    },
    {
      id: '4',
      rideId: 'ride-004',
      amount: 120,
      currency: 'ريال',
      method: 'BANK_TRANSFER',
      status: 'FAILED',
      transactionId: 'TXN-004',
      createdAt: new Date().toISOString(),
    },
  ];

  const filteredPayments = mockPayments.filter(payment => {
    return payment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
           payment.rideId.toLowerCase().includes(searchTerm.toLowerCase()) ||
           (payment.transactionId && payment.transactionId.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  const mockTotalRevenue = mockPayments
    .filter(p => p.status === 'COMPLETED')
    .reduce((sum, p) => sum + p.amount, 0);

  const mockTodayRevenue = mockTotalRevenue * 0.1; // Mock today's revenue as 10% of total

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة المدفوعات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          عرض وإدارة جميع المعاملات المالية
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney sx={{ fontSize: 40, color: 'success.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {totalRevenue || mockTotalRevenue}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الإيرادات (ريال)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUp sx={{ fontSize: 40, color: 'primary.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {todayRevenue || Math.round(mockTodayRevenue)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إيرادات اليوم (ريال)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AccountBalance sx={{ fontSize: 40, color: 'warning.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                    {pendingPayments || mockPayments.filter(p => p.status === 'PENDING').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معاملات معلقة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney sx={{ fontSize: 40, color: 'info.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {mockPayments.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المعاملات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Actions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في المعاملات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
            >
              تصدير التقرير
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredPayments}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>
    </Box>
  );
};

export default PaymentsManagement;
