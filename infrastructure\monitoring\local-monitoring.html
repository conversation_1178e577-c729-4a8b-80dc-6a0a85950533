<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TECNO DRIVE - مراقبة محلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .service-icon {
            font-size: 2em;
            margin-left: 15px;
        }
        
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .service-url {
            display: block;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin: 10px 0;
            padding: 10px 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        
        .service-url:hover {
            background: rgba(102, 126, 234, 0.2);
        }
        
        .status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin: 5px;
        }
        
        .status.online {
            background: #4CAF50;
            color: white;
        }
        
        .status.offline {
            background: #f44336;
            color: white;
        }
        
        .status.checking {
            background: #ff9800;
            color: white;
        }
        
        .metrics {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 TECNO DRIVE - مراقبة محلية</h1>
        <p>نظام مراقبة الخدمات المحلي</p>
    </div>
    
    <div class="container">
        <div class="service-grid">
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🎯</span>
                    <span class="service-title">لوحة الإدارة الرئيسية</span>
                </div>
                <a href="http://localhost:3000" class="service-url" target="_blank">
                    http://localhost:3000
                </a>
                <span class="status checking" id="status-3000">جاري التحقق...</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">👥</span>
                    <span class="service-title">إدارة الموارد البشرية</span>
                </div>
                <a href="http://localhost:3002" class="service-url" target="_blank">
                    http://localhost:3002
                </a>
                <span class="status checking" id="status-3002">جاري التحقق...</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🌐</span>
                    <span class="service-title">بوابة API</span>
                </div>
                <a href="http://localhost:8080" class="service-url" target="_blank">
                    http://localhost:8080
                </a>
                <span class="status checking" id="status-8080">جاري التحقق...</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🔍</span>
                    <span class="service-title">Eureka Discovery</span>
                </div>
                <a href="http://localhost:8761" class="service-url" target="_blank">
                    http://localhost:8761
                </a>
                <span class="status checking" id="status-8761">جاري التحقق...</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🔐</span>
                    <span class="service-title">خدمة المصادقة</span>
                </div>
                <a href="http://localhost:8081" class="service-url" target="_blank">
                    http://localhost:8081
                </a>
                <span class="status checking" id="status-8081">جاري التحقق...</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">📊</span>
                    <span class="service-title">Prometheus (محلي)</span>
                </div>
                <a href="http://localhost:9090" class="service-url" target="_blank">
                    http://localhost:9090
                </a>
                <span class="status checking" id="status-9090">جاري التحقق...</span>
                <button class="refresh-btn" onclick="startPrometheus()">تشغيل Prometheus</button>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">📈</span>
                    <span class="service-title">Grafana (محلي)</span>
                </div>
                <a href="http://localhost:3001" class="service-url" target="_blank">
                    http://localhost:3001
                </a>
                <span class="status checking" id="status-3001">جاري التحقق...</span>
                <button class="refresh-btn" onclick="startGrafana()">تشغيل Grafana</button>
            </div>
        </div>
        
        <div class="metrics">
            <h2>📊 معلومات النظام</h2>
            <div class="metric-item">
                <span>وقت آخر تحديث:</span>
                <span class="metric-value" id="last-update">-</span>
            </div>
            <div class="metric-item">
                <span>الخدمات النشطة:</span>
                <span class="metric-value" id="active-services">0/7</span>
            </div>
            <div class="metric-item">
                <span>حالة النظام:</span>
                <span class="metric-value" id="system-status">جاري التحقق...</span>
            </div>
            
            <button class="refresh-btn" onclick="checkAllServices()">تحديث الحالة</button>
            <button class="refresh-btn" onclick="location.reload()">إعادة تحميل</button>
        </div>
    </div>
    
    <script>
        const services = [
            {port: 3000, id: 'status-3000'},
            {port: 3002, id: 'status-3002'},
            {port: 8080, id: 'status-8080'},
            {port: 8761, id: 'status-8761'},
            {port: 8081, id: 'status-8081'},
            {port: 9090, id: 'status-9090'},
            {port: 3001, id: 'status-3001'}
        ];
        
        async function checkService(port, statusId) {
            try {
                const response = await fetch(`http://localhost:${port}`, {
                    method: 'GET',
                    mode: 'no-cors',
                    timeout: 5000
                });
                
                document.getElementById(statusId).textContent = 'متصل';
                document.getElementById(statusId).className = 'status online';
                return true;
            } catch (error) {
                document.getElementById(statusId).textContent = 'غير متصل';
                document.getElementById(statusId).className = 'status offline';
                return false;
            }
        }
        
        async function checkAllServices() {
            let activeCount = 0;
            
            for (const service of services) {
                const isActive = await checkService(service.port, service.id);
                if (isActive) activeCount++;
            }
            
            document.getElementById('active-services').textContent = `${activeCount}/${services.length}`;
            document.getElementById('last-update').textContent = new Date().toLocaleString('ar-SA');
            
            if (activeCount === services.length) {
                document.getElementById('system-status').textContent = 'ممتاز - جميع الخدمات تعمل';
                document.getElementById('system-status').style.color = '#4CAF50';
            } else if (activeCount > services.length / 2) {
                document.getElementById('system-status').textContent = 'جيد - معظم الخدمات تعمل';
                document.getElementById('system-status').style.color = '#ff9800';
            } else {
                document.getElementById('system-status').textContent = 'يحتاج انتباه - خدمات متوقفة';
                document.getElementById('system-status').style.color = '#f44336';
            }
        }
        
        function startPrometheus() {
            alert('لتشغيل Prometheus، قم بتشغيل الأمر التالي في PowerShell:\ndocker run -d --name prometheus-local -p 9090:9090 prom/prometheus');
        }
        
        function startGrafana() {
            alert('لتشغيل Grafana، قم بتشغيل الأمر التالي في PowerShell:\ndocker run -d --name grafana-local -p 3001:3000 -e GF_SECURITY_ADMIN_PASSWORD=admin123 grafana/grafana');
        }
        
        // Check services on page load
        checkAllServices();
        
        // Auto-refresh every 30 seconds
        setInterval(checkAllServices, 30000);
    </script>
</body>
</html>
