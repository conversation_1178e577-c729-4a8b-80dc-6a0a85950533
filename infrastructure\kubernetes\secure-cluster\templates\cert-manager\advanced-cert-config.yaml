{{- if .Values.certManager.enabled }}
# Advanced Certificate Management Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cert-manager-config
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: cert-manager-config
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-management"
data:
  cert-manager.yaml: |
    # Cert-Manager Configuration for TECNO DRIVE
    global:
      leaderElection:
        namespace: cert-manager
      
    webhook:
      timeoutSeconds: 30
      
    cainjector:
      enabled: true
      
    startupapicheck:
      enabled: true
      timeout: 5m

---
# Certificate Monitoring ServiceMonitor
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager-metrics
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: cert-manager-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-monitoring"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  endpoints:
    - port: tcp-prometheus-servicemonitor
      interval: 60s
      path: /metrics
      scrapeTimeout: 30s
      metricRelabelings:
        - sourceLabels: [__name__]
          regex: "cert_manager_.*"
          action: keep
        - sourceLabels: [__name__]
          targetLabel: tecno_drive_component
          replacement: "cert-manager"
{{- end }}

---
# Certificate Expiry Monitoring Rules
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: certificate-expiry-rules
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: certificate-expiry-rules
    app.kubernetes.io/instance: {{ .Release.Name }}
    prometheus: kube-prometheus
    role: alert-rules
    tecno-drive.com/component: "certificate-monitoring"
spec:
  groups:
    - name: certificate-expiry
      interval: 60s
      rules:
        # Certificate expiry tracking
        - record: cert:expiry_days_remaining
          expr: |
            (cert_manager_certificate_expiration_timestamp_seconds - time()) / 86400
        
        # Certificate renewal success rate
        - record: cert:renewal_success_rate_24h
          expr: |
            sum(rate(cert_manager_certificate_renewal_success_total[24h]))
            /
            sum(rate(cert_manager_certificate_renewal_total[24h]))
        
        # Certificates expiring soon (30 days)
        - alert: CertificateExpiringSoon
          expr: cert:expiry_days_remaining < 30 and cert:expiry_days_remaining > 7
          for: 1h
          labels:
            severity: warning
            category: infrastructure
            component: cert-manager
          annotations:
            summary: "🔐 Certificate expiring in {{ \"{{ $value | humanize }}\" }} days"
            description: |
              Certificate {{ "{{ $labels.name }}" }} in namespace {{ "{{ $labels.namespace }}" }} 
              will expire in {{ "{{ $value | humanize }}" }} days.
              
              Certificate Details:
              - Name: {{ "{{ $labels.name }}" }}
              - Namespace: {{ "{{ $labels.namespace }}" }}
              - Issuer: {{ "{{ $labels.issuer_name }}" }}
              - Days Remaining: {{ "{{ $value | humanize }}" }}
              
              Actions Required:
              1. Check cert-manager controller logs
              2. Verify ACME challenge configuration
              3. Ensure DNS/HTTP01 challenge accessibility
              4. Manual renewal if automatic fails
            runbook_url: "https://docs.tecnodrive.com/runbooks/certificate-renewal"
        
        # Certificates expiring very soon (7 days)
        - alert: CertificateExpiringCritical
          expr: cert:expiry_days_remaining < 7 and cert:expiry_days_remaining > 0
          for: 15m
          labels:
            severity: critical
            category: infrastructure
            component: cert-manager
          annotations:
            summary: "🚨 Certificate expiring in {{ \"{{ $value | humanize }}\" }} days - URGENT"
            description: |
              URGENT: Certificate {{ "{{ $labels.name }}" }} will expire in {{ "{{ $value | humanize }}" }} days!
              
              This may cause service disruption if not renewed immediately.
              
              Immediate Actions:
              1. Check certificate status: kubectl describe certificate {{ "{{ $labels.name }}" }} -n {{ "{{ $labels.namespace }}" }}
              2. Force renewal: kubectl cert-manager renew {{ "{{ $labels.name }}" }} -n {{ "{{ $labels.namespace }}" }}
              3. Check issuer status: kubectl describe issuer {{ "{{ $labels.issuer_name }}" }} -n {{ "{{ $labels.namespace }}" }}
              4. Escalate to platform team if renewal fails
            runbook_url: "https://docs.tecnodrive.com/runbooks/certificate-emergency"
        
        # Certificate renewal failures
        - alert: CertificateRenewalFailed
          expr: |
            increase(cert_manager_certificate_renewal_failure_total[1h]) > 0
          for: 5m
          labels:
            severity: high
            category: infrastructure
            component: cert-manager
          annotations:
            summary: "🔐 Certificate renewal failed"
            description: |
              Certificate renewal has failed for {{ "{{ $labels.name }}" }} in namespace {{ "{{ $labels.namespace }}" }}.
              
              Failure Details:
              - Certificate: {{ "{{ $labels.name }}" }}
              - Namespace: {{ "{{ $labels.namespace }}" }}
              - Issuer: {{ "{{ $labels.issuer_name }}" }}
              - Failures in last hour: {{ "{{ $value }}" }}
              
              Troubleshooting Steps:
              1. Check certificate events: kubectl describe certificate {{ "{{ $labels.name }}" }} -n {{ "{{ $labels.namespace }}" }}
              2. Check cert-manager logs: kubectl logs -n cert-manager -l app=cert-manager
              3. Verify issuer configuration
              4. Check ACME challenge status
        
        # Low certificate renewal success rate
        - alert: CertificateRenewalSuccessRateLow
          expr: cert:renewal_success_rate_24h < 0.9
          for: 30m
          labels:
            severity: warning
            category: infrastructure
            component: cert-manager
          annotations:
            summary: "🔐 Low certificate renewal success rate"
            description: |
              Certificate renewal success rate is {{ "{{ $value | humanizePercentage }}" }} over the last 24 hours.
              
              This indicates potential issues with the certificate management system.
              
              Investigation Required:
              1. Review cert-manager controller logs
              2. Check issuer configurations
              3. Verify ACME challenge setup
              4. Review DNS/HTTP01 challenge accessibility

---
# Certificate Emergency Procedures
apiVersion: v1
kind: ConfigMap
metadata:
  name: certificate-emergency-procedures
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: certificate-emergency
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-emergency"
data:
  emergency-renewal.sh: |
    #!/bin/bash
    # Emergency Certificate Renewal Script
    
    CERT_NAME="$1"
    NAMESPACE="$2"
    
    if [ -z "$CERT_NAME" ] || [ -z "$NAMESPACE" ]; then
        echo "Usage: $0 <certificate-name> <namespace>"
        exit 1
    fi
    
    echo "🚨 EMERGENCY: Renewing certificate $CERT_NAME in namespace $NAMESPACE"
    
    # Backup current certificate
    kubectl get certificate $CERT_NAME -n $NAMESPACE -o yaml > "cert-backup-$CERT_NAME-$(date +%Y%m%d-%H%M%S).yaml"
    
    # Force renewal
    kubectl cert-manager renew $CERT_NAME -n $NAMESPACE
    
    # Wait for renewal
    echo "⏳ Waiting for certificate renewal..."
    kubectl wait --for=condition=Ready certificate/$CERT_NAME -n $NAMESPACE --timeout=300s
    
    if [ $? -eq 0 ]; then
        echo "✅ Certificate renewed successfully"
        # Restart affected deployments
        kubectl rollout restart deployment -n $NAMESPACE -l cert-manager.io/certificate-name=$CERT_NAME
    else
        echo "❌ Certificate renewal failed"
        echo "📋 Certificate status:"
        kubectl describe certificate $CERT_NAME -n $NAMESPACE
        echo "📋 Recent events:"
        kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -10
        exit 1
    fi
  
  manual-certificate-creation.sh: |
    #!/bin/bash
    # Manual Certificate Creation for Emergency
    
    CERT_NAME="$1"
    NAMESPACE="$2"
    DNS_NAME="$3"
    
    if [ -z "$CERT_NAME" ] || [ -z "$NAMESPACE" ] || [ -z "$DNS_NAME" ]; then
        echo "Usage: $0 <certificate-name> <namespace> <dns-name>"
        exit 1
    fi
    
    echo "🔐 Creating emergency self-signed certificate"
    
    # Create self-signed certificate
    openssl req -x509 -newkey rsa:2048 -keyout temp-key.pem -out temp-cert.pem -days 30 -nodes \
        -subj "/CN=$DNS_NAME/O=TECNO DRIVE Emergency/C=SA"
    
    # Create Kubernetes secret
    kubectl create secret tls $CERT_NAME-emergency \
        --cert=temp-cert.pem \
        --key=temp-key.pem \
        -n $NAMESPACE
    
    # Cleanup temporary files
    rm temp-key.pem temp-cert.pem
    
    echo "✅ Emergency certificate created as $CERT_NAME-emergency"
    echo "⚠️  Remember to replace with proper certificate ASAP"
  
  certificate-health-check.sh: |
    #!/bin/bash
    # Certificate Health Check Script
    
    echo "🔍 TECNO DRIVE Certificate Health Check"
    echo "======================================"
    
    # Check all certificates
    echo "📋 Certificate Status:"
    kubectl get certificates -A -o custom-columns="NAMESPACE:.metadata.namespace,NAME:.metadata.name,READY:.status.conditions[?(@.type=='Ready')].status,AGE:.metadata.creationTimestamp"
    
    echo ""
    echo "⏰ Certificate Expiry Times:"
    kubectl get certificates -A -o custom-columns="NAMESPACE:.metadata.namespace,NAME:.metadata.name,EXPIRES:.status.notAfter"
    
    echo ""
    echo "🔄 Recent Certificate Events:"
    kubectl get events -A --field-selector involvedObject.kind=Certificate --sort-by='.lastTimestamp' | tail -20
    
    echo ""
    echo "📊 Cert-Manager Controller Status:"
    kubectl get pods -n cert-manager -l app=cert-manager
    
    echo ""
    echo "📈 Certificate Metrics (if available):"
    if command -v curl &> /dev/null; then
        PROMETHEUS_URL="http://prometheus.monitoring.svc.cluster.local:9090"
        curl -s "$PROMETHEUS_URL/api/v1/query?query=cert_manager_certificate_expiration_timestamp_seconds" | jq -r '.data.result[] | "\(.metric.name) in \(.metric.namespace): \((.value[1] | tonumber - now) / 86400 | floor) days"' 2>/dev/null || echo "Prometheus not accessible"
    fi

  contact-info.yaml: |
    emergency_contacts:
      certificate_team:
        - name: "Platform Security Team"
          email: "<EMAIL>"
          phone: "+966-11-XXX-XXXX"
          role: "Certificate Management"
          escalation_time: "15 minutes"
        
        - name: "Infrastructure Team"
          email: "<EMAIL>"
          phone: "+966-11-XXX-XXXX"
          role: "Infrastructure Support"
          escalation_time: "30 minutes"
      
      external_vendors:
        - name: "Let's Encrypt Support"
          contact: "community.letsencrypt.org"
          role: "ACME CA Support"
        
        - name: "DNS Provider Support"
          contact: "Based on DNS provider"
          role: "DNS Challenge Support"
    
    escalation_procedures:
      certificate_expiry_critical:
        - "Immediate notification to platform security team"
        - "Create emergency self-signed certificate if needed"
        - "Investigate renewal failure root cause"
        - "Implement permanent fix"
      
      certificate_renewal_failure:
        - "Check cert-manager controller logs"
        - "Verify issuer configuration"
        - "Test ACME challenge manually"
        - "Escalate to infrastructure team if needed"
      
      mass_certificate_failure:
        - "Activate incident response team"
        - "Check cert-manager cluster-wide status"
        - "Consider cert-manager restart/upgrade"
        - "Prepare emergency certificates for critical services"

---
# Certificate Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: certificate-backup
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: certificate-backup
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-backup"
spec:
  schedule: "{{ .Values.certManager.backup.schedule | default "0 2 * * *" }}" # Daily at 2 AM
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/name: certificate-backup-job
            tecno-drive.com/component: "certificate-backup"
        spec:
          serviceAccountName: certificate-backup
          securityContext:
            runAsUser: 1000
            runAsGroup: 3000
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          containers:
            - name: backup
              image: bitnami/kubectl:{{ .Values.certManager.backup.kubectlVersion | default "latest" }}
              imagePullPolicy: IfNotPresent
              securityContext:
                allowPrivilegeEscalation: false
                readOnlyRootFilesystem: true
                capabilities:
                  drop: ["ALL"]
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  
                  BACKUP_DIR="/tmp/cert-backup-$(date +%Y%m%d-%H%M%S)"
                  mkdir -p "$BACKUP_DIR"
                  
                  echo "🔐 Starting certificate backup..."
                  
                  # Backup all certificates
                  kubectl get certificates -A -o yaml > "$BACKUP_DIR/certificates.yaml"
                  
                  # Backup all certificate secrets
                  kubectl get secrets -A -l cert-manager.io/certificate-name -o yaml > "$BACKUP_DIR/certificate-secrets.yaml"
                  
                  # Backup issuers
                  kubectl get issuers,clusterissuers -A -o yaml > "$BACKUP_DIR/issuers.yaml"
                  
                  # Create summary report
                  cat > "$BACKUP_DIR/backup-summary.txt" << EOF
                  TECNO DRIVE Certificate Backup Summary
                  =====================================
                  Backup Date: $(date)
                  Backup Location: $BACKUP_DIR
                  
                  Certificates Backed Up:
                  $(kubectl get certificates -A --no-headers | wc -l) certificates
                  
                  Certificate Secrets Backed Up:
                  $(kubectl get secrets -A -l cert-manager.io/certificate-name --no-headers | wc -l) secrets
                  
                  Issuers Backed Up:
                  $(kubectl get issuers,clusterissuers -A --no-headers | wc -l) issuers
                  EOF
                  
                  echo "✅ Certificate backup completed"
                  cat "$BACKUP_DIR/backup-summary.txt"
                  
                  # TODO: Upload to external storage (S3, GCS, etc.)
                  # aws s3 cp "$BACKUP_DIR" s3://tecno-drive-backups/certificates/ --recursive
              volumeMounts:
                - name: tmp
                  mountPath: /tmp
              resources:
                requests:
                  memory: 64Mi
                  cpu: 50m
                limits:
                  memory: 128Mi
                  cpu: 100m
          volumes:
            - name: tmp
              emptyDir: {}
          restartPolicy: OnFailure

---
# ServiceAccount for Certificate Backup
apiVersion: v1
kind: ServiceAccount
metadata:
  name: certificate-backup
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: certificate-backup-sa
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-backup"

---
# ClusterRole for Certificate Backup
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: certificate-backup
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: certificate-backup-role
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-backup"
rules:
  - apiGroups: ["cert-manager.io"]
    resources: ["certificates", "issuers", "clusterissuers"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list"]

---
# ClusterRoleBinding for Certificate Backup
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: certificate-backup
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: certificate-backup-binding
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "certificate-backup"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: certificate-backup
subjects:
  - kind: ServiceAccount
    name: certificate-backup
    namespace: {{ .Release.Namespace }}
{{- end }}
