# مولد البيانات الافتراضية لمنصة TecnoDrive - اليمن
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   مولد البيانات الافتراضية - TecnoDrive" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "جاري التحقق من وجود Python..." -ForegroundColor Green

try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python متاح: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: Python غير مثبت أو غير متاح في PATH" -ForegroundColor Red
    Write-Host "يرجى تثبيت Python من https://python.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "جاري التحقق من مكتبة Faker..." -ForegroundColor Green

try {
    python -c "import faker" 2>$null
    Write-Host "✅ مكتبة Faker متاحة" -ForegroundColor Green
    Write-Host "جاري تشغيل مولد البيانات الكامل..." -ForegroundColor Cyan
    python generate_yemen_data.py
} catch {
    Write-Host "⚠️ مكتبة Faker غير مثبتة" -ForegroundColor Yellow
    Write-Host "جاري محاولة تثبيتها..." -ForegroundColor Green
    
    try {
        python -m pip install faker
        Write-Host "✅ تم تثبيت Faker بنجاح" -ForegroundColor Green
        Write-Host "جاري تشغيل مولد البيانات الكامل..." -ForegroundColor Cyan
        python generate_yemen_data.py
    } catch {
        Write-Host "❌ فشل في تثبيت Faker" -ForegroundColor Red
        Write-Host "سيتم استخدام النسخة المبسطة..." -ForegroundColor Yellow
        Write-Host "جاري تشغيل مولد البيانات المبسط..." -ForegroundColor Cyan
        python simple_yemen_data.py
    }
}

Write-Host ""
Write-Host "🎉 تم الانتهاء! تحقق من مجلد generated_data" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
