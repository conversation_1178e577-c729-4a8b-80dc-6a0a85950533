-- Index Optimization for TecnoDrive Platform
-- Implements comprehensive indexing strategy for optimal query performance

-- =====================================================
-- 1. CORE BUSINESS INDEXES
-- =====================================================

-- Users table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users (email) WHERE active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone_verified 
ON users (phone_number) WHERE phone_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role_tenant 
ON users (role, tenant_id) WHERE active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at 
ON users (created_at DESC);

-- Rides table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_passenger_status 
ON rides_partitioned (passenger_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_driver_status 
ON rides_partitioned (driver_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_status_requested_at 
ON rides_partitioned (status, requested_at DESC) 
WHERE status IN ('REQUESTED', 'ACCEPTED');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_pickup_location 
ON rides_partitioned USING GIST (pickup_location);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_dropoff_location 
ON rides_partitioned USING GIST (dropoff_location);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_fare_distance 
ON rides_partitioned (fare, distance) WHERE status = 'COMPLETED';

-- Vehicles table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_driver_active 
ON vehicles (driver_id) WHERE active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_type_status 
ON vehicles (vehicle_type, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_location 
ON vehicles USING GIST (current_location) WHERE active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_maintenance 
ON vehicles (last_maintenance_date) WHERE active = true;

-- Payments table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status 
ON payments_partitioned (user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_ride_id 
ON payments_partitioned (ride_id) WHERE ride_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_gateway_transaction 
ON payments_partitioned (gateway_transaction_id) WHERE gateway_transaction_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_amount_currency 
ON payments_partitioned (amount, currency) WHERE status = 'COMPLETED';

-- Location data indexes (for partitioned table)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_location_driver_timestamp 
ON location_data_partitioned (driver_id, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_location_vehicle_timestamp 
ON location_data_partitioned (vehicle_id, timestamp DESC) WHERE vehicle_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_location_coordinates 
ON location_data_partitioned USING GIST (ST_Point(longitude, latitude));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_location_speed_accuracy 
ON location_data_partitioned (speed, accuracy) WHERE speed > 0;

-- =====================================================
-- 2. COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Multi-column indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_passenger_date_status 
ON rides_partitioned (passenger_id, created_at DESC, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_driver_date_fare 
ON rides_partitioned (driver_id, created_at DESC, fare) WHERE status = 'COMPLETED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_date_amount 
ON payments_partitioned (user_id, created_at DESC, amount) WHERE status = 'COMPLETED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_location_driver_time_coords 
ON location_data_partitioned (driver_id, timestamp DESC, latitude, longitude);

-- =====================================================
-- 3. PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- =====================================================

-- Active/pending records only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_active_requests 
ON rides_partitioned (requested_at DESC, pickup_location) 
WHERE status = 'REQUESTED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_in_progress 
ON rides_partitioned (started_at DESC, driver_id) 
WHERE status = 'IN_PROGRESS';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_pending 
ON payments_partitioned (created_at DESC, user_id) 
WHERE status = 'PENDING';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_available 
ON vehicles (current_location, vehicle_type) 
WHERE status = 'AVAILABLE' AND active = true;

-- =====================================================
-- 4. FUNCTIONAL INDEXES
-- =====================================================

-- Case-insensitive email search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_lower 
ON users (LOWER(email));

-- Date-based functional indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_date_only 
ON rides_partitioned (DATE(created_at));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_month_year 
ON payments_partitioned (EXTRACT(YEAR FROM created_at), EXTRACT(MONTH FROM created_at));

-- JSON field indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_metadata_rating 
ON rides_partitioned USING GIN ((metadata->'rating')) 
WHERE metadata ? 'rating';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_metadata_features 
ON vehicles USING GIN ((metadata->'features')) 
WHERE metadata ? 'features';

-- =====================================================
-- 5. FULL-TEXT SEARCH INDEXES
-- =====================================================

-- Add tsvector columns for full-text search
ALTER TABLE users ADD COLUMN IF NOT EXISTS search_vector tsvector;
ALTER TABLE rides_partitioned ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Create GIN indexes for full-text search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search 
ON users USING GIN (search_vector);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_search 
ON rides_partitioned USING GIN (search_vector);

-- Functions to update search vectors
CREATE OR REPLACE FUNCTION update_users_search_vector() RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', COALESCE(NEW.first_name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.last_name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.email, '')), 'B') ||
        setweight(to_tsvector('english', COALESCE(NEW.phone_number, '')), 'C');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_rides_search_vector() RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', COALESCE(NEW.pickup_address, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.dropoff_address, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.status, '')), 'B');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic search vector updates
DROP TRIGGER IF EXISTS trigger_users_search_vector ON users;
CREATE TRIGGER trigger_users_search_vector 
    BEFORE INSERT OR UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_users_search_vector();

DROP TRIGGER IF EXISTS trigger_rides_search_vector ON rides_partitioned;
CREATE TRIGGER trigger_rides_search_vector 
    BEFORE INSERT OR UPDATE ON rides_partitioned 
    FOR EACH ROW EXECUTE FUNCTION update_rides_search_vector();

-- =====================================================
-- 6. INDEX MONITORING AND MAINTENANCE
-- =====================================================

-- View to monitor index usage
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MODERATE_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_category,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- View to identify missing indexes
CREATE OR REPLACE VIEW potential_missing_indexes AS
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    CASE 
        WHEN seq_scan > 1000 AND seq_tup_read > 100000 THEN 'HIGH_PRIORITY'
        WHEN seq_scan > 500 AND seq_tup_read > 50000 THEN 'MEDIUM_PRIORITY'
        WHEN seq_scan > 100 AND seq_tup_read > 10000 THEN 'LOW_PRIORITY'
        ELSE 'MONITOR'
    END as index_recommendation,
    pg_size_pretty(pg_relation_size(relid)) as table_size
FROM pg_stat_user_tables
WHERE seq_scan > 0
ORDER BY seq_scan * seq_tup_read DESC;

-- Function to analyze index effectiveness
CREATE OR REPLACE FUNCTION analyze_index_effectiveness()
RETURNS TABLE(
    table_name TEXT,
    index_name TEXT,
    effectiveness_score NUMERIC,
    recommendation TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.tablename::TEXT,
        i.indexname::TEXT,
        CASE 
            WHEN i.idx_scan = 0 THEN 0
            ELSE ROUND((i.idx_tup_fetch::NUMERIC / NULLIF(i.idx_scan, 0))::NUMERIC, 2)
        END as effectiveness_score,
        CASE 
            WHEN i.idx_scan = 0 THEN 'Consider dropping - unused index'
            WHEN i.idx_scan < 10 THEN 'Low usage - monitor or consider dropping'
            WHEN (i.idx_tup_fetch::NUMERIC / NULLIF(i.idx_scan, 0)) < 1 THEN 'Low selectivity - review index design'
            WHEN (i.idx_tup_fetch::NUMERIC / NULLIF(i.idx_scan, 0)) > 100 THEN 'High selectivity - very effective'
            ELSE 'Normal usage - keep index'
        END as recommendation
    FROM pg_stat_user_indexes i
    ORDER BY effectiveness_score DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to rebuild fragmented indexes
CREATE OR REPLACE FUNCTION rebuild_fragmented_indexes(fragmentation_threshold NUMERIC DEFAULT 20.0)
RETURNS VOID AS $$
DECLARE
    index_record RECORD;
    fragmentation NUMERIC;
BEGIN
    FOR index_record IN
        SELECT schemaname, tablename, indexname
        FROM pg_stat_user_indexes
        WHERE idx_scan > 0
    LOOP
        -- Calculate fragmentation (simplified approach)
        SELECT 
            CASE 
                WHEN pg_relation_size(indexrelid) > 0 
                THEN (100.0 * (pg_relation_size(indexrelid) - pg_relation_size(indexrelid, 'main')) / pg_relation_size(indexrelid))
                ELSE 0 
            END INTO fragmentation
        FROM pg_stat_user_indexes 
        WHERE schemaname = index_record.schemaname 
        AND tablename = index_record.tablename 
        AND indexname = index_record.indexname;
        
        IF fragmentation > fragmentation_threshold THEN
            EXECUTE format('REINDEX INDEX CONCURRENTLY %I.%I', 
                          index_record.schemaname, index_record.indexname);
            RAISE NOTICE 'Rebuilt fragmented index: %.% (fragmentation: %)', 
                        index_record.tablename, index_record.indexname, fragmentation;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. AUTOMATED INDEX MAINTENANCE
-- =====================================================

-- Function for daily index maintenance
CREATE OR REPLACE FUNCTION daily_index_maintenance()
RETURNS VOID AS $$
BEGIN
    -- Update statistics for all tables
    ANALYZE;
    
    -- Rebuild highly fragmented indexes
    PERFORM rebuild_fragmented_indexes(30.0);
    
    -- Log maintenance completion
    INSERT INTO maintenance_log (operation, status, completed_at)
    VALUES ('daily_index_maintenance', 'completed', NOW());
    
    RAISE NOTICE 'Daily index maintenance completed';
END;
$$ LANGUAGE plpgsql;

-- Create maintenance log table if it doesn't exist
CREATE TABLE IF NOT EXISTS maintenance_log (
    id SERIAL PRIMARY KEY,
    operation VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    details TEXT,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. PERFORMANCE MONITORING QUERIES
-- =====================================================

-- Query to find slow queries that might benefit from indexes
CREATE OR REPLACE VIEW slow_queries_needing_indexes AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 100  -- queries taking more than 100ms on average
AND calls > 10         -- called more than 10 times
ORDER BY mean_time DESC;

-- Grant permissions for monitoring
GRANT SELECT ON index_usage_stats TO application_readonly;
GRANT SELECT ON potential_missing_indexes TO application_readonly;
GRANT SELECT ON slow_queries_needing_indexes TO application_readonly;
GRANT EXECUTE ON FUNCTION analyze_index_effectiveness() TO application_admin;

-- Comments for documentation
COMMENT ON VIEW index_usage_stats IS 'Monitor index usage patterns and identify unused indexes';
COMMENT ON VIEW potential_missing_indexes IS 'Identify tables that might benefit from additional indexes';
COMMENT ON FUNCTION analyze_index_effectiveness IS 'Analyze the effectiveness of existing indexes';
COMMENT ON FUNCTION rebuild_fragmented_indexes IS 'Rebuild indexes with high fragmentation';
COMMENT ON FUNCTION daily_index_maintenance IS 'Perform daily index maintenance tasks';
