server:
  port: 8761

spring:
  application:
    name: eureka-server
  security:
    user:
      name: ${EUREKA_USERNAME:eureka}
      password: ${EUREKA_PASSWORD:eureka123}

eureka:
  instance:
    hostname: ${EUREKA_HOSTNAME:localhost}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30
    metadata-map:
      zone: ${EUREKA_ZONE:zone1}
      version: ${spring.application.version:1.0.0}
  client:
    register-with-eureka: false
    fetch-registry: false
    service-url:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/
    registry-fetch-interval-seconds: 5
  server:
    enable-self-preservation: ${EUREKA_SELF_PRESERVATION:true}
    eviction-interval-timer-in-ms: 15000
    renewal-percent-threshold: 0.85
    renewal-threshold-update-interval-ms: 15000
    response-cache-auto-expiration-in-seconds: 180
    response-cache-update-interval-ms: 30000
    use-read-only-response-cache: true
    delta-retention-timer-interval-in-ms: 30000
    retention-time-in-m-s-in-delta-queue: 180000

# Security Configuration
security:
  basic:
    enabled: true
  user:
    name: ${EUREKA_USERNAME:eureka}
    password: ${EUREKA_PASSWORD:eureka123}

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics,env,configprops,eureka
  endpoint:
    health:
      show-details: always
  health:
    eureka:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.netflix.eureka: INFO
    com.netflix.discovery: INFO
    org.springframework.cloud.netflix.eureka: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
