import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  LinearProgress,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Memory as CacheIcon,
  Speed as PerformanceIcon,
  Storage as StorageIcon,
  CloudSync as CdnIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
} from 'recharts';

interface CacheEntry {
  id: string;
  key: string;
  service: string;
  size: number;
  hitCount: number;
  missCount: number;
  lastAccessed: string;
  expiresAt: string;
  status: 'ACTIVE' | 'EXPIRED' | 'EVICTED';
}

interface CacheConfig {
  id: string;
  name: string;
  service: string;
  type: 'REDIS' | 'MEMORY' | 'CDN' | 'DATABASE';
  maxSize: number;
  ttl: number;
  enabled: boolean;
  hitRatio: number;
}

interface CacheMetrics {
  totalHits: number;
  totalMisses: number;
  hitRatio: number;
  totalSize: number;
  evictions: number;
  avgResponseTime: number;
}

const CachingSystem: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [cacheEntries, setCacheEntries] = useState<CacheEntry[]>([]);
  const [cacheConfigs, setCacheConfigs] = useState<CacheConfig[]>([]);
  const [metrics, setMetrics] = useState<CacheMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterService, setFilterService] = useState('ALL');
  const [filterType, setFilterType] = useState('ALL');
  const [openConfigDialog, setOpenConfigDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Mock data
  const mockCacheEntries: CacheEntry[] = [
    {
      id: 'cache-1',
      key: 'vehicles:active',
      service: 'fleet-service',
      size: 2048,
      hitCount: 1250,
      missCount: 45,
      lastAccessed: '2025-07-09T14:30:00Z',
      expiresAt: '2025-07-09T15:30:00Z',
      status: 'ACTIVE',
    },
    {
      id: 'cache-2',
      key: 'rides:pending',
      service: 'rides-service',
      size: 1024,
      hitCount: 890,
      missCount: 120,
      lastAccessed: '2025-07-09T14:25:00Z',
      expiresAt: '2025-07-09T15:25:00Z',
      status: 'ACTIVE',
    },
    {
      id: 'cache-3',
      key: 'users:profile:123',
      service: 'auth-service',
      size: 512,
      hitCount: 340,
      missCount: 15,
      lastAccessed: '2025-07-09T14:20:00Z',
      expiresAt: '2025-07-09T16:20:00Z',
      status: 'ACTIVE',
    },
  ];

  const mockCacheConfigs: CacheConfig[] = [
    {
      id: 'config-redis',
      name: 'Redis Cache',
      service: 'redis-cluster',
      type: 'REDIS',
      maxSize: 1024 * 1024 * 1024, // 1GB
      ttl: 3600, // 1 hour
      enabled: true,
      hitRatio: 94.5,
    },
    {
      id: 'config-memory',
      name: 'In-Memory Cache',
      service: 'application-cache',
      type: 'MEMORY',
      maxSize: 512 * 1024 * 1024, // 512MB
      ttl: 1800, // 30 minutes
      enabled: true,
      hitRatio: 87.2,
    },
    {
      id: 'config-cdn',
      name: 'CDN Cache',
      service: 'cloudflare-cdn',
      type: 'CDN',
      maxSize: 10 * 1024 * 1024 * 1024, // 10GB
      ttl: 86400, // 24 hours
      enabled: true,
      hitRatio: 98.1,
    },
  ];

  const mockMetrics: CacheMetrics = {
    totalHits: 15420,
    totalMisses: 1250,
    hitRatio: 92.5,
    totalSize: 2.1 * 1024 * 1024 * 1024, // 2.1GB
    evictions: 45,
    avgResponseTime: 12.5,
  };

  // Performance data for charts
  const performanceData = [
    { time: '14:00', hits: 1200, misses: 80, responseTime: 15 },
    { time: '14:05', hits: 1350, misses: 95, responseTime: 12 },
    { time: '14:10', hits: 1180, misses: 70, responseTime: 18 },
    { time: '14:15', hits: 1420, misses: 110, responseTime: 10 },
    { time: '14:20', hits: 1380, misses: 85, responseTime: 14 },
    { time: '14:25', hits: 1450, misses: 75, responseTime: 11 },
    { time: '14:30', hits: 1520, misses: 90, responseTime: 13 },
  ];

  const cacheDistribution = [
    { name: 'Redis', value: 45, color: '#ff6b6b' },
    { name: 'Memory', value: 30, color: '#4ecdc4' },
    { name: 'CDN', value: 20, color: '#45b7d1' },
    { name: 'Database', value: 5, color: '#96ceb4' },
  ];

  useEffect(() => {
    setCacheEntries(mockCacheEntries);
    setCacheConfigs(mockCacheConfigs);
    setMetrics(mockMetrics);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      EXPIRED: { label: 'منتهي الصلاحية', color: 'warning' as const, icon: <ErrorIcon fontSize="small" /> },
      EVICTED: { label: 'محذوف', color: 'error' as const, icon: <ErrorIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getTypeChip = (type: string) => {
    const typeConfig = {
      REDIS: { label: 'Redis', color: 'error' as const },
      MEMORY: { label: 'Memory', color: 'info' as const },
      CDN: { label: 'CDN', color: 'success' as const },
      DATABASE: { label: 'Database', color: 'warning' as const },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}ث`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}د`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}س`;
    return `${Math.floor(seconds / 86400)}ي`;
  };

  const handleClearCache = async (cacheId: string) => {
    if (window.confirm('هل أنت متأكد من مسح هذا الكاش؟')) {
      try {
        // TODO: Implement actual API call
        console.log('Clearing cache:', cacheId);
        setSnackbarMessage('تم مسح الكاش بنجاح');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error clearing cache:', error);
      }
    }
  };

  const handleRefreshCache = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSnackbarMessage('تم تحديث بيانات الكاش');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error refreshing cache:', error);
    } finally {
      setLoading(false);
    }
  };

  const cacheEntryColumns: GridColDef[] = [
    {
      field: 'key',
      headerName: 'مفتاح الكاش',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'service',
      headerName: 'الخدمة',
      width: 150,
    },
    {
      field: 'size',
      headerName: 'الحجم',
      width: 100,
      renderCell: (params: GridRenderCellParams) => formatBytes(params.value),
    },
    {
      field: 'hitCount',
      headerName: 'النجاحات',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="success.main">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'missCount',
      headerName: 'الإخفاقات',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="error.main">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'hitRatio',
      headerName: 'معدل النجاح',
      width: 120,
      valueGetter: (params) => {
        if (!params || !params.row) return '0%';
        const hits = params.row.hitCount || 0;
        const misses = params.row.missCount || 0;
        const total = hits + misses;
        return total > 0 ? ((hits / total) * 100).toFixed(1) + '%' : '0%';
      },
      renderCell: (params: GridRenderCellParams) => {
        const ratio = parseFloat(params.value.replace('%', ''));
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LinearProgress
              variant="determinate"
              value={ratio}
              sx={{ width: 60, height: 8 }}
              color={ratio > 90 ? 'success' : ratio > 70 ? 'warning' : 'error'}
            />
            <Typography variant="body2">{params.value}</Typography>
          </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="مسح"><DeleteIcon /></Tooltip>}
          label="مسح"
          onClick={() => handleClearCache(params.id as string)}
        />,
      ],
    },
  ];

  const configColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم التكوين',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <CacheIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'type',
      headerName: 'النوع',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getTypeChip(params.value),
    },
    {
      field: 'maxSize',
      headerName: 'الحد الأقصى',
      width: 120,
      renderCell: (params: GridRenderCellParams) => formatBytes(params.value),
    },
    {
      field: 'ttl',
      headerName: 'مدة البقاء',
      width: 120,
      renderCell: (params: GridRenderCellParams) => formatDuration(params.value),
    },
    {
      field: 'hitRatio',
      headerName: 'معدل النجاح',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LinearProgress
            variant="determinate"
            value={params.value}
            sx={{ width: 80, height: 8 }}
            color={params.value > 90 ? 'success' : params.value > 70 ? 'warning' : 'error'}
          />
          <Typography variant="body2">{params.value}%</Typography>
        </Box>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Switch checked={params.value} size="small" />
      ),
    },
  ];

  const filteredEntries = cacheEntries.filter(entry => {
    const matchesSearch = entry.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.service.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesService = filterService === 'ALL' || entry.service === filterService;
    return matchesSearch && matchesService;
  });

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          نظام التخزين المؤقت
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة ومراقبة أنظمة التخزين المؤقت وCDN Integration
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.hitRatio.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل النجاح
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <StorageIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics ? formatBytes(metrics.totalSize) : '0 GB'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الحجم
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <PerformanceIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.avgResponseTime.toFixed(1)}ms
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    متوسط زمن الاستجابة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <CacheIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {metrics?.evictions || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    عمليات الإخلاء
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<CacheIcon />} label="إدخالات الكاش" iconPosition="start" />
            <Tab icon={<SettingsIcon />} label="التكوينات" iconPosition="start" />
            <Tab icon={<TrendingUpIcon />} label="الأداء" iconPosition="start" />
            <Tab icon={<CdnIcon />} label="CDN" iconPosition="start" />
          </Tabs>
        </Box>

        {/* Cache Entries Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  placeholder="البحث في الكاش..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>الخدمة</InputLabel>
                  <Select
                    value={filterService}
                    label="الخدمة"
                    onChange={(e) => setFilterService(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الخدمات</MenuItem>
                    <MenuItem value="fleet-service">Fleet Service</MenuItem>
                    <MenuItem value="rides-service">Rides Service</MenuItem>
                    <MenuItem value="auth-service">Auth Service</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={handleRefreshCache}
                disabled={loading}
              >
                تحديث
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={filteredEntries}
                columns={cacheEntryColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Cache Configurations Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">تكوينات التخزين المؤقت</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenConfigDialog(true)}
              >
                إضافة تكوين
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={cacheConfigs}
                columns={configColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Performance Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>مراقبة الأداء</Typography>
            
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, md: 8 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      أداء الكاش في الوقت الفعلي
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={performanceData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="time" />
                        <YAxis />
                        <RechartsTooltip />
                        <Line 
                          type="monotone" 
                          dataKey="hits" 
                          stroke="#4caf50" 
                          strokeWidth={3}
                          name="النجاحات"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="misses" 
                          stroke="#f44336" 
                          strokeWidth={3}
                          name="الإخفاقات"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid size={{ xs: 12, md: 4 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      توزيع أنواع الكاش
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={cacheDistribution}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                        >
                          {cacheDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* CDN Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>إدارة CDN</Typography>
            <Alert severity="info">
              قريباً - إدارة وتكوين شبكة توصيل المحتوى (CDN)
            </Alert>
          </Box>
        )}
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default CachingSystem;
