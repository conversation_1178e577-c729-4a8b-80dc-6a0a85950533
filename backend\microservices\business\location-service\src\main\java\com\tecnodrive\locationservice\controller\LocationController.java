package com.tecnodrive.locationservice.controller;

import com.tecnodrive.locationservice.entity.Location;
import com.tecnodrive.locationservice.service.LocationService;
import com.tecnodrive.locationservice.service.ServiceIntegrationService;
import com.tecnodrive.locationservice.service.MapService;
import com.tecnodrive.locationservice.websocket.LocationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/locations")
@CrossOrigin(origins = "*")
public class LocationController {
    
    @Autowired
    private LocationService locationService;

    @Autowired
    private LocationWebSocketHandler webSocketHandler;

    @Autowired
    private ServiceIntegrationService serviceIntegrationService;

    @Autowired
    private MapService mapService;
    
    // Create or update location
    @PostMapping
    public ResponseEntity<Location> createLocation(@RequestBody Location location) {
        Location savedLocation = locationService.saveLocation(location);
        return ResponseEntity.ok(savedLocation);
    }
    
    // Get location by ID
    @GetMapping("/{id}")
    public ResponseEntity<Location> getLocationById(@PathVariable UUID id) {
        return locationService.getLocationById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    // Get latest location for entity
    @GetMapping("/entity/{entityId}/latest")
    public ResponseEntity<Location> getLatestLocation(@PathVariable String entityId) {
        return locationService.getLatestLocationForEntity(entityId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    // Get all locations for entity
    @GetMapping("/entity/{entityId}")
    public ResponseEntity<List<Location>> getLocationsByEntity(@PathVariable String entityId) {
        List<Location> locations = locationService.getLocationsByEntity(entityId);
        return ResponseEntity.ok(locations);
    }
    
    // Get locations by entity type
    @GetMapping("/type/{entityType}")
    public ResponseEntity<List<Location>> getLocationsByType(@PathVariable String entityType) {
        List<Location> locations = locationService.getLocationsByEntityType(entityType);
        return ResponseEntity.ok(locations);
    }
    
    // Get locations within radius
    @GetMapping("/nearby")
    public ResponseEntity<List<Location>> getNearbyLocations(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam double radiusMeters) {
        List<Location> locations = locationService.getLocationsWithinRadius(latitude, longitude, radiusMeters);
        return ResponseEntity.ok(locations);
    }
    
    // Get nearest locations
    @GetMapping("/nearest")
    public ResponseEntity<List<Location>> getNearestLocations(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "10") int limit) {
        List<Location> locations = locationService.getNearestLocations(latitude, longitude, limit);
        return ResponseEntity.ok(locations);
    }
    
    // Calculate distance between two points
    @GetMapping("/distance")
    public ResponseEntity<Map<String, Double>> calculateDistance(
            @RequestParam double lat1,
            @RequestParam double lon1,
            @RequestParam double lat2,
            @RequestParam double lon2) {
        Double distance = locationService.calculateDistance(lat1, lon1, lat2, lon2);
        return ResponseEntity.ok(Map.of("distance_meters", distance));
    }
    
    // Get locations within time range
    @GetMapping("/entity/{entityId}/history")
    public ResponseEntity<List<Location>> getLocationHistory(
            @PathVariable String entityId,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        LocalDateTime start = LocalDateTime.parse(startTime);
        LocalDateTime end = LocalDateTime.parse(endTime);
        List<Location> locations = locationService.getLocationHistory(entityId, start, end);
        return ResponseEntity.ok(locations);
    }
    
    // Get locations by city
    @GetMapping("/city/{city}")
    public ResponseEntity<List<Location>> getLocationsByCity(@PathVariable String city) {
        List<Location> locations = locationService.getLocationsByCity(city);
        return ResponseEntity.ok(locations);
    }
    
    // Get location statistics
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getLocationStatistics() {
        Map<String, Object> stats = locationService.getLocationStatistics();
        return ResponseEntity.ok(stats);
    }
    
    // Update location
    @PutMapping("/{id}")
    public ResponseEntity<Location> updateLocation(@PathVariable UUID id, @RequestBody Location location) {
        location.setId(id);
        Location updatedLocation = locationService.saveLocation(location);
        return ResponseEntity.ok(updatedLocation);
    }
    
    // Deactivate location
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deactivateLocation(@PathVariable UUID id) {
        locationService.deactivateLocation(id);
        return ResponseEntity.noContent().build();
    }
    
    // Bulk location update
    @PostMapping("/bulk")
    public ResponseEntity<List<Location>> bulkUpdateLocations(@RequestBody List<Location> locations) {
        List<Location> savedLocations = locationService.bulkSaveLocations(locations);
        return ResponseEntity.ok(savedLocations);
    }
    
    // Get locations along route
    @PostMapping("/route")
    public ResponseEntity<List<Location>> getLocationsAlongRoute(
            @RequestBody Map<String, Object> routeData) {
        String lineString = (String) routeData.get("lineString");
        Double bufferMeters = (Double) routeData.get("bufferMeters");
        List<Location> locations = locationService.getLocationsAlongRoute(lineString, bufferMeters);
        return ResponseEntity.ok(locations);
    }
    
    // Get locations within polygon (delivery zone)
    @PostMapping("/zone")
    public ResponseEntity<List<Location>> getLocationsInZone(
            @RequestBody Map<String, Object> zoneData) {
        String polygon = (String) zoneData.get("polygon");
        List<Location> locations = locationService.getLocationsWithinPolygon(polygon);
        return ResponseEntity.ok(locations);
    }
    
    // Health check endpoint
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "location-service");
        response.put("timestamp", LocalDateTime.now().toString());
        response.put("websocket_sessions", webSocketHandler.getActiveSessionsCount());

        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for receiving location updates from vehicles/drivers
     */
    @PostMapping("/update")
    public ResponseEntity<Map<String, Object>> updateLocation(@RequestBody Map<String, Object> locationData) {
        try {
            // Validate required fields
            if (!locationData.containsKey("vehicleId") ||
                !locationData.containsKey("lat") ||
                !locationData.containsKey("lng")) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Missing required fields: vehicleId, lat, lng"
                ));
            }

            // Add timestamp if not provided
            locationData.putIfAbsent("timestamp", System.currentTimeMillis());

            // Process location update through integration service
            // This will handle WebSocket broadcasting and service notifications
            serviceIntegrationService.processLocationUpdate(locationData);

            // Update vehicle position on interactive map
            if (locationData.containsKey("vehicleId")) {
                String vehicleId = (String) locationData.get("vehicleId");
                double lat = ((Number) locationData.get("lat")).doubleValue();
                double lng = ((Number) locationData.get("lng")).doubleValue();
                double heading = ((Number) locationData.getOrDefault("heading", 0)).doubleValue();
                double speed = ((Number) locationData.getOrDefault("speed", 0)).doubleValue();
                String status = (String) locationData.getOrDefault("status", "active");

                mapService.updateVehiclePosition(vehicleId, lat, lng, heading, speed, status);
            }

            // Save to database
            // locationService.saveLocationUpdate(locationData);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Location updated successfully",
                "timestamp", locationData.get("timestamp")
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Error updating location: " + e.getMessage()
            ));
        }
    }

    /**
     * Endpoint for sending alerts
     */
    @PostMapping("/alert")
    public ResponseEntity<Map<String, Object>> sendAlert(@RequestBody Map<String, Object> alertData) {
        try {
            alertData.putIfAbsent("timestamp", System.currentTimeMillis());
            alertData.putIfAbsent("id", "alert_" + System.currentTimeMillis());

            // Broadcast alert to all connected sessions
            webSocketHandler.broadcastAlert(alertData);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Alert sent successfully",
                "alertId", alertData.get("id")
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Error sending alert: " + e.getMessage()
            ));
        }
    }

    /**
     * Get WebSocket connection statistics
     */
    @GetMapping("/websocket/stats")
    public ResponseEntity<Map<String, Object>> getWebSocketStats() {
        return ResponseEntity.ok(Map.of(
            "active_sessions", webSocketHandler.getActiveSessionsCount(),
            "subscriptions", webSocketHandler.getSessionSubscriptions().size(),
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * Get service health status
     */
    @GetMapping("/service-health")
    public ResponseEntity<Map<String, Object>> getServiceHealth() {
        try {
            Map<String, Object> healthStatus = serviceIntegrationService.getServiceHealthStatus();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", healthStatus,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to get service health: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            ));
        }
    }
}
