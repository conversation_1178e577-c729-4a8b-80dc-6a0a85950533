#!/usr/bin/env powershell
# =============================================================================
# TecnoDrive Platform - Service Testing Script
# =============================================================================

Write-Host "🔍 Testing TecnoDrive Services..." -ForegroundColor Cyan

# Test Infrastructure Services
Write-Host "`n📊 Infrastructure Services:" -ForegroundColor Yellow

# Test PostgreSQL
Write-Host "  🔍 Testing PostgreSQL..." -NoNewline
try {
    $pgResult = docker exec postgres-tecno pg_isready -U postgres 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host " ✅ Running" -ForegroundColor Green
    } else {
        Write-Host " ❌ Failed" -ForegroundColor Red
    }
} catch {
    Write-Host " ❌ Error" -ForegroundColor Red
}

# Test Redis
Write-Host "  🔍 Testing Redis..." -NoNewline
try {
    $redisResult = docker exec redis-tecno redis-cli ping 2>$null
    if ($redisResult -eq "PONG") {
        Write-Host " ✅ Running" -ForegroundColor Green
    } else {
        Write-Host " ❌ Failed" -ForegroundColor Red
    }
} catch {
    Write-Host " ❌ Error" -ForegroundColor Red
}

# Test Application Services
Write-Host "`n🚀 Application Services:" -ForegroundColor Yellow

$services = @(
    @{Name="Eureka Server"; Port=8761; Path="/actuator/health"},
    @{Name="API Gateway"; Port=8080; Path="/actuator/health"},
    @{Name="Auth Service"; Port=8081; Path="/actuator/health"}
)

foreach ($service in $services) {
    Write-Host "  🔍 Testing $($service.Name)..." -NoNewline
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)$($service.Path)" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host " ✅ Running" -ForegroundColor Green
        } else {
            Write-Host " ⚠️  Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host " ❌ Failed" -ForegroundColor Red
        Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test Container Status
Write-Host "`n📦 Container Status:" -ForegroundColor Yellow
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "tecno" }

# Show logs for failed services
Write-Host "`n📋 Recent Logs for Failed Services:" -ForegroundColor Yellow

$failedServices = @("api-gateway-tecno", "auth-service-tecno")
foreach ($service in $failedServices) {
    Write-Host "`n🔍 Logs for $service (last 10 lines):" -ForegroundColor Cyan
    docker logs $service --tail 10 2>$null
}

Write-Host "`n✅ Service testing completed!" -ForegroundColor Green
