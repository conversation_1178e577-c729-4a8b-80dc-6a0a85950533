package com.tecnodrive.gateway.config;

import org.springframework.context.annotation.Configuration;

/**
 * WebSocket Configuration for API Gateway
 * Spring Cloud Gateway handles WebSocket routing automatically
 * No additional configuration needed for basic WebSocket proxying
 */
@Configuration
public class WebSocketConfig {

    // Spring Cloud Gateway automatically handles WebSocket routing
    // WebSocket connections will be proxied to backend services
    // No additional beans needed for basic WebSocket support
}
