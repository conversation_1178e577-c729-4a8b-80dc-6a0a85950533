import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip as MuiTooltip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  Download as DownloadIcon,
  Email as EmailIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from 'recharts';
import { saasService, BillingInvoiceDto, TenantDto } from '../../services/saasService';

const BillingManagement: React.FC = () => {
  const [invoices, setInvoices] = useState<BillingInvoiceDto[]>([]);
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [selectedInvoice, setSelectedInvoice] = useState<BillingInvoiceDto | null>(null);
  const [openInvoiceDialog, setOpenInvoiceDialog] = useState(false);

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load tenants
      const tenantsResponse = await saasService.getTenants();
      if (tenantsResponse.success && tenantsResponse.data) {
        setTenants(tenantsResponse.data);
      }

      // Load all invoices
      const allInvoices: BillingInvoiceDto[] = [];
      for (const tenant of tenantsResponse.data || []) {
        const invoicesResponse = await saasService.getBillingInvoices(tenant.id);
        if (invoicesResponse.success && invoicesResponse.data) {
          allInvoices.push(...invoicesResponse.data);
        }
      }
      setInvoices(allInvoices);

    } catch (error) {
      console.error('Error loading billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'في الانتظار', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
      PAID: { label: 'مدفوع', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      OVERDUE: { label: 'متأخر', color: 'error' as const, icon: <WarningIcon fontSize="small" /> },
      CANCELLED: { label: 'ملغي', color: 'default' as const, icon: null },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const handleGenerateInvoice = async (tenantId: string) => {
    try {
      await saasService.generateInvoice(tenantId);
      loadData();
    } catch (error) {
      console.error('Error generating invoice:', error);
    }
  };

  const handleViewInvoice = (invoice: BillingInvoiceDto) => {
    setSelectedInvoice(invoice);
    setOpenInvoiceDialog(true);
  };

  const handleDownloadInvoice = (invoiceId: string) => {
    // TODO: Implement PDF download
    console.log('Download invoice:', invoiceId);
  };

  const handleSendInvoice = (invoiceId: string) => {
    // TODO: Implement email sending
    console.log('Send invoice:', invoiceId);
  };

  const columns: GridColDef[] = [
    {
      field: 'invoiceNumber',
      headerName: 'رقم الفاتورة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'tenantName',
      headerName: 'العميل',
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const tenant = tenants.find(t => t.id === params.row.tenantId);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              <BusinessIcon fontSize="small" />
            </Avatar>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {tenant?.name || 'غير محدد'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'amount',
      headerName: 'المبلغ',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <MoneyIcon fontSize="small" color="action" />
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {params.value.toLocaleString()} {params.row.currency}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'issueDate',
      headerName: 'تاريخ الإصدار',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'dueDate',
      headerName: 'تاريخ الاستحقاق',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <MuiTooltip title="عرض الفاتورة">
              <ReceiptIcon />
            </MuiTooltip>
          }
          label="عرض"
          onClick={() => handleViewInvoice(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <MuiTooltip title="تحميل PDF">
              <DownloadIcon />
            </MuiTooltip>
          }
          label="تحميل"
          onClick={() => handleDownloadInvoice(params.id as string)}
        />,
        <GridActionsCellItem
          icon={
            <MuiTooltip title="إرسال بالبريد">
              <EmailIcon />
            </MuiTooltip>
          }
          label="إرسال"
          onClick={() => handleSendInvoice(params.id as string)}
        />,
      ],
    },
  ];

  const filteredInvoices = invoices.filter(invoice => {
    const tenant = tenants.find(t => t.id === invoice.tenantId);
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'ALL' || invoice.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  // Calculate stats
  const totalInvoices = invoices.length;
  const paidInvoices = invoices.filter(i => i.status === 'PAID').length;
  const pendingInvoices = invoices.filter(i => i.status === 'PENDING').length;
  const overdueInvoices = invoices.filter(i => i.status === 'OVERDUE').length;
  const totalRevenue = invoices.filter(i => i.status === 'PAID').reduce((sum, i) => sum + i.amount, 0);
  const pendingAmount = invoices.filter(i => i.status === 'PENDING').reduce((sum, i) => sum + i.amount, 0);

  // Mock data for charts
  const revenueChartData = [
    { month: 'يناير', revenue: 45000, invoices: 12 },
    { month: 'فبراير', revenue: 52000, invoices: 15 },
    { month: 'مارس', revenue: 48000, invoices: 13 },
    { month: 'أبريل', revenue: 61000, invoices: 18 },
    { month: 'مايو', revenue: 58000, invoices: 16 },
    { month: 'يونيو', revenue: 67000, invoices: 20 },
  ];

  const statusDistribution = [
    { name: 'مدفوع', value: paidInvoices, color: '#4caf50' },
    { name: 'في الانتظار', value: pendingInvoices, color: '#ff9800' },
    { name: 'متأخر', value: overdueInvoices, color: '#f44336' },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الفوترة
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة الفواتير والمدفوعات لجميع العملاء
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalInvoices}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الفواتير
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإيرادات (ريال)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {pendingAmount.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المبالغ المعلقة (ريال)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                {overdueInvoices}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الفواتير المتأخرة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                الإيرادات الشهرية
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={revenueChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="revenue" stroke="#1976d2" name="الإيرادات" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزيع حالة الفواتير
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            إجراءات سريعة
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                // TODO: Open bulk invoice generation dialog
                console.log('Generate bulk invoices');
              }}
            >
              إنشاء فواتير جماعية
            </Button>
            <Button
              variant="outlined"
              startIcon={<EmailIcon />}
              onClick={() => {
                // TODO: Send reminder emails
                console.log('Send payment reminders');
              }}
            >
              إرسال تذكيرات الدفع
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => {
                // TODO: Export billing report
                console.log('Export billing report');
              }}
            >
              تصدير تقرير الفوترة
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterStatus}
                label="الحالة"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="PENDING">في الانتظار</MenuItem>
                <MenuItem value="PAID">مدفوع</MenuItem>
                <MenuItem value="OVERDUE">متأخر</MenuItem>
                <MenuItem value="CANCELLED">ملغي</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredInvoices}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Invoice Detail Dialog */}
      <Dialog 
        open={openInvoiceDialog} 
        onClose={() => setOpenInvoiceDialog(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          تفاصيل الفاتورة {selectedInvoice?.invoiceNumber}
        </DialogTitle>
        <DialogContent>
          {selectedInvoice && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ mb: 2 }}>معلومات الفاتورة</Typography>
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="رقم الفاتورة"
                        secondary={selectedInvoice.invoiceNumber}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="المبلغ"
                        secondary={`${selectedInvoice.amount.toLocaleString()} ${selectedInvoice.currency}`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="تاريخ الإصدار"
                        secondary={new Date(selectedInvoice.issueDate).toLocaleDateString('ar-SA')}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="تاريخ الاستحقاق"
                        secondary={new Date(selectedInvoice.dueDate).toLocaleDateString('ar-SA')}
                      />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ mb: 2 }}>تفاصيل البنود</Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>الوصف</TableCell>
                          <TableCell align="right">الكمية</TableCell>
                          <TableCell align="right">السعر</TableCell>
                          <TableCell align="right">المجموع</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedInvoice.items.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell>{item.description}</TableCell>
                            <TableCell align="right">{item.quantity}</TableCell>
                            <TableCell align="right">{item.unitPrice.toLocaleString()}</TableCell>
                            <TableCell align="right">{item.totalPrice.toLocaleString()}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenInvoiceDialog(false)}>إغلاق</Button>
          <Button variant="outlined" startIcon={<DownloadIcon />}>
            تحميل PDF
          </Button>
          <Button variant="contained" startIcon={<EmailIcon />}>
            إرسال بالبريد
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BillingManagement;
