// Mock Service for TecnoDrive Platform
import { 
  mockRides, 
  mockUsers, 
  mockVehicles, 
  mockParcels, 
  mockPayments, 
  mockAnalytics 
} from '../data/mockData';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class MockService {
  // Rides Service
  static async getRides(params?: { page?: number; limit?: number; status?: string }) {
    await delay(500); // Simulate network delay
    
    let filteredRides = [...mockRides];
    
    if (params?.status) {
      filteredRides = filteredRides.filter(ride => ride.status === params.status);
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      success: true,
      data: filteredRides.slice(startIndex, endIndex),
      total: filteredRides.length,
      page,
      limit,
      totalPages: Math.ceil(filteredRides.length / limit)
    };
  }

  static async getRideById(rideId: string) {
    await delay(300);
    
    const ride = mockRides.find(r => r.id === rideId);
    
    if (!ride) {
      return {
        success: false,
        message: 'الرحلة غير موجودة'
      };
    }
    
    return {
      success: true,
      data: ride
    };
  }

  // Users Service
  static async getUsers(params?: { page?: number; limit?: number; userType?: string }) {
    await delay(400);
    
    let filteredUsers = [...mockUsers];
    
    if (params?.userType) {
      filteredUsers = filteredUsers.filter(user => user.userType === params.userType);
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      success: true,
      data: filteredUsers.slice(startIndex, endIndex),
      total: filteredUsers.length,
      totalUsers: mockUsers.length,
      activeUsers: mockUsers.filter(u => u.status === 'ACTIVE').length,
      drivers: mockUsers.filter(u => u.userType === 'DRIVER').length,
      passengers: mockUsers.filter(u => u.userType === 'PASSENGER').length,
      page,
      limit
    };
  }

  // Fleet Service
  static async getVehicles(params?: { page?: number; limit?: number; status?: string }) {
    await delay(350);
    
    let filteredVehicles = [...mockVehicles];
    
    if (params?.status) {
      filteredVehicles = filteredVehicles.filter(vehicle => vehicle.status === params.status);
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      success: true,
      data: filteredVehicles.slice(startIndex, endIndex),
      total: filteredVehicles.length,
      totalVehicles: mockVehicles.length,
      activeVehicles: mockVehicles.filter(v => v.status === 'ACTIVE').length,
      maintenanceVehicles: mockVehicles.filter(v => v.status === 'MAINTENANCE').length,
      page,
      limit
    };
  }

  // Parcels Service
  static async getParcels(params?: { page?: number; limit?: number; status?: string }) {
    await delay(450);
    
    let filteredParcels = [...mockParcels];
    
    if (params?.status) {
      filteredParcels = filteredParcels.filter(parcel => parcel.status === params.status);
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      success: true,
      data: filteredParcels.slice(startIndex, endIndex),
      total: filteredParcels.length,
      totalParcels: mockParcels.length,
      inTransitParcels: mockParcels.filter(p => p.status === 'IN_TRANSIT').length,
      deliveredParcels: mockParcels.filter(p => p.status === 'DELIVERED').length,
      page,
      limit
    };
  }

  // Payments Service
  static async getPayments(params?: { page?: number; limit?: number; status?: string }) {
    await delay(300);
    
    let filteredPayments = [...mockPayments];
    
    if (params?.status) {
      filteredPayments = filteredPayments.filter(payment => payment.status === params.status);
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      success: true,
      data: filteredPayments.slice(startIndex, endIndex),
      total: filteredPayments.length,
      totalPayments: mockPayments.length,
      completedPayments: mockPayments.filter(p => p.status === 'COMPLETED').length,
      pendingPayments: mockPayments.filter(p => p.status === 'PENDING').length,
      totalAmount: mockPayments.reduce((sum, p) => sum + p.amount, 0),
      page,
      limit
    };
  }

  // Analytics Service
  static async getAnalytics() {
    await delay(600);
    
    return {
      success: true,
      data: mockAnalytics
    };
  }

  // Dashboard Stats
  static async getDashboardStats() {
    await delay(400);
    
    return {
      success: true,
      data: {
        ...mockAnalytics,
        recentRides: mockRides.slice(0, 5),
        activeVehicles: mockVehicles.filter(v => v.status === 'ACTIVE'),
        pendingParcels: mockParcels.filter(p => p.status === 'IN_TRANSIT' || p.status === 'PICKED_UP')
      }
    };
  }

  // Health Check
  static async healthCheck() {
    await delay(100);
    
    return {
      success: true,
      data: {
        status: 'UP',
        timestamp: new Date().toISOString(),
        services: {
          database: 'UP',
          redis: 'UP',
          websocket: 'UP',
          apiGateway: 'UP'
        }
      }
    };
  }
}
