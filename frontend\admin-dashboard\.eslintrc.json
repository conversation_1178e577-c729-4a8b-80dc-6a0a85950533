{"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-inferrable-types": "off", "react-hooks/exhaustive-deps": "warn", "no-unused-vars": "off", "no-console": "off", "prefer-const": "warn"}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "rules": {"@typescript-eslint/no-unused-vars": "off"}}], "ignorePatterns": ["node_modules/", "build/", "public/", "*.config.js", "*.config.ts"]}