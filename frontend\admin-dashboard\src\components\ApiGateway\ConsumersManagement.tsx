import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Paper,
  Divider,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Apps as AppsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Key as KeyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Block as BlockIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { apiGatewayService, ConsumerDto, CreateConsumerRequest } from '../../services/apiGatewayService';

const ConsumersManagement: React.FC = () => {
  const [consumers, setConsumers] = useState<ConsumerDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterAuthType, setFilterAuthType] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openCredentialsDialog, setOpenCredentialsDialog] = useState(false);
  const [selectedConsumer, setSelectedConsumer] = useState<ConsumerDto | null>(null);
  const [showCredentials, setShowCredentials] = useState<{ [key: string]: boolean }>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [newConsumer, setNewConsumer] = useState<CreateConsumerRequest>({
    appName: '',
    authType: 'JWT',
    scopes: [],
    rateLimitTier: 'BASIC',
    metadata: {},
  });

  // Available scopes
  const availableScopes = [
    'rides.read', 'rides.write',
    'fleet.read', 'fleet.write',
    'auth.read', 'auth.write',
    'notifications.read', 'notifications.write',
    'saas.read', 'saas.write',
    'analytics.read',
    '*' // All permissions
  ];

  // Load consumers data
  const loadConsumers = async () => {
    try {
      setLoading(true);
      const response = await apiGatewayService.getConsumers({
        status: filterStatus === 'ALL' ? undefined : filterStatus,
        authType: filterAuthType === 'ALL' ? undefined : filterAuthType,
        search: searchTerm || undefined,
      });
      
      if (response.success && response.data) {
        setConsumers(response.data);
      }
    } catch (error) {
      console.error('Error loading consumers:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConsumers();
  }, [searchTerm, filterStatus, filterAuthType]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      SUSPENDED: { label: 'معلق', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
      PENDING: { label: 'في الانتظار', color: 'info' as const, icon: <WarningIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getAuthTypeChip = (authType: string) => {
    const authConfig = {
      JWT: { label: 'JWT', color: 'primary' as const },
      API_KEY: { label: 'API Key', color: 'secondary' as const },
      OAUTH2: { label: 'OAuth2', color: 'info' as const },
    };

    const config = authConfig[authType as keyof typeof authConfig] || { 
      label: authType, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const getRateLimitChip = (tier: string) => {
    const tierConfig = {
      BASIC: { label: 'أساسي', color: 'default' as const },
      PREMIUM: { label: 'متقدم', color: 'warning' as const },
      ENTERPRISE: { label: 'مؤسسي', color: 'success' as const },
    };

    const config = tierConfig[tier as keyof typeof tierConfig] || { 
      label: tier, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleViewCredentials = (consumer: ConsumerDto) => {
    setSelectedConsumer(consumer);
    setOpenCredentialsDialog(true);
  };

  const handleRegenerateCredentials = async (consumerId: string) => {
    if (window.confirm('هل أنت متأكد من إعادة توليد بيانات الاعتماد؟ سيؤدي هذا إلى إبطال البيانات الحالية.')) {
      try {
        const response = await apiGatewayService.regenerateCredentials(consumerId);
        if (response.success) {
          setSnackbarMessage('تم إعادة توليد بيانات الاعتماد بنجاح');
          setSnackbarOpen(true);
          loadConsumers();
        }
      } catch (error) {
        console.error('Error regenerating credentials:', error);
      }
    }
  };

  const handleToggleCredentialVisibility = (field: string) => {
    setShowCredentials(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSnackbarMessage('تم نسخ النص إلى الحافظة');
    setSnackbarOpen(true);
  };

  const handleUpdateConsumerStatus = async (consumerId: string, status: 'ACTIVE' | 'SUSPENDED') => {
    try {
      await apiGatewayService.updateConsumer(consumerId, { status });
      loadConsumers();
    } catch (error) {
      console.error('Error updating consumer status:', error);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'appName',
      headerName: 'اسم التطبيق',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <AppsIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'clientId',
      headerName: 'معرف العميل',
      width: 180,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'authType',
      headerName: 'نوع المصادقة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getAuthTypeChip(params.value),
    },
    {
      field: 'rateLimitTier',
      headerName: 'مستوى التحديد',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getRateLimitChip(params.value),
    },
    {
      field: 'scopes',
      headerName: 'الصلاحيات',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {params.value.slice(0, 2).map((scope: string, index: number) => (
            <Chip
              key={index}
              label={scope}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          ))}
          {params.value.length > 2 && (
            <Chip
              label={`+${params.value.length - 2}`}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'totalRequests',
      headerName: 'إجمالي الطلبات',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value?.toLocaleString() || 0}
        </Typography>
      ),
    },
    {
      field: 'lastUsed',
      headerName: 'آخر استخدام',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'لم يستخدم',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض بيانات الاعتماد">
              <KeyIcon />
            </Tooltip>
          }
          label="بيانات الاعتماد"
          onClick={() => handleViewCredentials(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => console.log('Edit consumer:', params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title={params.row.status === 'ACTIVE' ? "تعليق" : "تفعيل"}>
              {params.row.status === 'ACTIVE' ? <BlockIcon /> : <CheckCircleIcon />}
            </Tooltip>
          }
          label={params.row.status === 'ACTIVE' ? "تعليق" : "تفعيل"}
          onClick={() => handleUpdateConsumerStatus(
            params.id as string, 
            params.row.status === 'ACTIVE' ? 'SUSPENDED' : 'ACTIVE'
          )}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="إعادة توليد بيانات الاعتماد">
              <RefreshIcon />
            </Tooltip>
          }
          label="إعادة توليد"
          onClick={() => handleRegenerateCredentials(params.id as string)}
        />,
      ],
    },
  ];

  const filteredConsumers = consumers;

  const handleAddConsumer = async () => {
    try {
      await apiGatewayService.createConsumer(newConsumer);
      setOpenAddDialog(false);
      setNewConsumer({
        appName: '',
        authType: 'JWT',
        scopes: [],
        rateLimitTier: 'BASIC',
        metadata: {},
      });
      loadConsumers();
    } catch (error) {
      console.error('Error creating consumer:', error);
    }
  };

  // Calculate stats
  const totalConsumers = consumers.length;
  const activeConsumers = consumers.filter(c => c.status === 'ACTIVE').length;
  const suspendedConsumers = consumers.filter(c => c.status === 'SUSPENDED').length;
  const totalRequests = consumers.reduce((sum, c) => sum + (c.totalRequests || 0), 0);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة المستهلكين
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة التطبيقات والخدمات المستهلكة لـ API Gateway
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalConsumers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي المستهلكين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {activeConsumers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المستهلكين النشطين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {suspendedConsumers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المستهلكين المعلقين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {totalRequests.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الطلبات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في المستهلكين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterStatus}
                label="الحالة"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ACTIVE">نشط</MenuItem>
                <MenuItem value="SUSPENDED">معلق</MenuItem>
                <MenuItem value="PENDING">في الانتظار</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>نوع المصادقة</InputLabel>
              <Select
                value={filterAuthType}
                label="نوع المصادقة"
                onChange={(e) => setFilterAuthType(e.target.value)}
              >
                <MenuItem value="ALL">جميع الأنواع</MenuItem>
                <MenuItem value="JWT">JWT</MenuItem>
                <MenuItem value="API_KEY">API Key</MenuItem>
                <MenuItem value="OAUTH2">OAuth2</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة مستهلك
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredConsumers}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Consumer Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة مستهلك جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="اسم التطبيق"
              value={newConsumer.appName}
              onChange={(e) => setNewConsumer({ ...newConsumer, appName: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="رابط الاستدعاء (Callback URL)"
              value={newConsumer.callbackUrl || ''}
              onChange={(e) => setNewConsumer({ ...newConsumer, callbackUrl: e.target.value })}
              fullWidth
              placeholder="https://your-app.com/callback"
            />
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>نوع المصادقة</InputLabel>
                  <Select
                    value={newConsumer.authType}
                    label="نوع المصادقة"
                    onChange={(e) => setNewConsumer({ ...newConsumer, authType: e.target.value as any })}
                  >
                    <MenuItem value="JWT">JWT</MenuItem>
                    <MenuItem value="API_KEY">API Key</MenuItem>
                    <MenuItem value="OAUTH2">OAuth2</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>مستوى التحديد</InputLabel>
                  <Select
                    value={newConsumer.rateLimitTier}
                    label="مستوى التحديد"
                    onChange={(e) => setNewConsumer({ ...newConsumer, rateLimitTier: e.target.value as any })}
                  >
                    <MenuItem value="BASIC">أساسي</MenuItem>
                    <MenuItem value="PREMIUM">متقدم</MenuItem>
                    <MenuItem value="ENTERPRISE">مؤسسي</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            <FormControl fullWidth>
              <InputLabel>الصلاحيات</InputLabel>
              <Select
                multiple
                value={newConsumer.scopes}
                label="الصلاحيات"
                onChange={(e) => setNewConsumer({ ...newConsumer, scopes: e.target.value as string[] })}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableScopes.map((scope) => (
                  <MenuItem key={scope} value={scope}>
                    {scope}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddConsumer} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* Credentials Dialog */}
      <Dialog open={openCredentialsDialog} onClose={() => setOpenCredentialsDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>بيانات الاعتماد - {selectedConsumer?.appName}</DialogTitle>
        <DialogContent>
          {selectedConsumer && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <Alert severity="warning">
                احتفظ بهذه البيانات في مكان آمن. لن تتمكن من رؤيتها مرة أخرى.
              </Alert>
              
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>معرف العميل:</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', flexGrow: 1 }}>
                    {selectedConsumer.clientId}
                  </Typography>
                  <IconButton size="small" onClick={() => handleCopyToClipboard(selectedConsumer.clientId)}>
                    <CopyIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Paper>

              {selectedConsumer.clientSecret && (
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>سر العميل:</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', flexGrow: 1 }}>
                      {showCredentials.clientSecret ? selectedConsumer.clientSecret : '••••••••••••••••'}
                    </Typography>
                    <IconButton size="small" onClick={() => handleToggleCredentialVisibility('clientSecret')}>
                      {showCredentials.clientSecret ? <VisibilityOffIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                    </IconButton>
                    <IconButton size="small" onClick={() => handleCopyToClipboard(selectedConsumer.clientSecret || '')}>
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Paper>
              )}

              {selectedConsumer.apiKey && (
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>مفتاح API:</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', flexGrow: 1 }}>
                      {showCredentials.apiKey ? selectedConsumer.apiKey : '••••••••••••••••'}
                    </Typography>
                    <IconButton size="small" onClick={() => handleToggleCredentialVisibility('apiKey')}>
                      {showCredentials.apiKey ? <VisibilityOffIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                    </IconButton>
                    <IconButton size="small" onClick={() => handleCopyToClipboard(selectedConsumer.apiKey || '')}>
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Paper>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCredentialsDialog(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default ConsumersManagement;
