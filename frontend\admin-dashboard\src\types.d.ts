// Global type definitions for TecnoDrive Platform

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.svg' {
  const content: string;
  export default content;
}

// Leaflet types (if not installed)
declare module 'leaflet' {
  export * from 'leaflet';
}

declare module 'react-leaflet' {
  export * from 'react-leaflet';
}

// Ant Design types (if not installed)
declare module 'antd' {
  export * from 'antd';
}

declare module '@ant-design/icons' {
  export * from '@ant-design/icons';
}

// Global window extensions
declare global {
  interface Window {
    google?: any;
    L?: any;
  }
}

// WebSocket message types
interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

// Vehicle data types
interface VehicleData {
  vehicleId: string;
  lat: number;
  lng: number;
  heading?: number;
  speed?: number;
  status: 'active' | 'busy' | 'offline' | 'idle';
  timestamp?: number;
}

// Route data types
interface RouteData {
  routeId: string;
  vehicleId: string;
  waypoints: Array<{
    lat: number;
    lng: number;
    type?: string;
  }>;
  routeInfo?: {
    distance: number;
    duration: number;
    trafficLevel?: string;
  };
}

// Traffic data types
interface TrafficData {
  streetId: string;
  trafficLevel: 'HEAVY' | 'MODERATE' | 'LIGHT' | 'FREE_FLOW';
  avgSpeed: number;
  description?: string;
  lat?: number;
  lng?: number;
}

// Map analytics types
interface MapAnalytics {
  totalSessions: number;
  activeConnections: number;
  uptime: number;
  subscriptionCounts: Record<string, number>;
  topSubscriptions: Array<[string, number]>;
  sessionsByType: Record<string, number>;
}

// Performance metrics types
interface PerformanceMetrics {
  messagesPerSecond: number;
  averageLatency: number;
  errorRate: number;
  memoryUsage: {
    used: number;
    free: number;
    total: number;
    max: number;
  };
  connectionHealth: {
    healthy: number;
    total: number;
    healthPercentage: number;
  };
}

// Session info types
interface SessionInfo {
  totalSessions: number;
  sessionSubscriptions: Record<string, string[]>;
  activeConnections: number;
}

// Subscription stats types
interface SubscriptionStats {
  totalSubscriptions: Record<string, number>;
  topSubscriptions: Array<[string, number]>;
  sessionsByType: Record<string, number>;
  averageSubscriptionsPerSession: number;
}

// Alert types
interface Alert {
  id: number;
  type: 'error' | 'warning' | 'info' | 'success';
  message: string;
  timestamp: Date;
}

// Map layer types
interface MapLayer {
  id: string;
  name: string;
  visible: boolean;
  data: any[];
}

// Geofence types
interface GeofenceData {
  geofenceId: string;
  name: string;
  type: 'polygon' | 'circle';
  coordinates: Array<{ lat: number; lng: number }>;
  rules?: {
    alertOnEntry?: boolean;
    alertOnExit?: boolean;
    maxSpeed?: number;
  };
}

// Driver performance types
interface DriverPerformance {
  driverId: string;
  vehicleId: string;
  location: {
    lat: number;
    lng: number;
    heading: number;
    speed: number;
  };
  metrics: {
    averageSpeed: number;
    fuelEfficiency: number;
    safetyScore: number;
    customerRating?: number;
    onTimePerformance?: number;
  };
  score: number;
  alerts: string[];
  recommendations: string[];
  timestamp: number;
}

// Heatmap types
interface HeatmapData {
  center: { lat: number; lng: number };
  radius: number;
  timeWindow: string;
  demandType: string;
  points: Array<{
    lat: number;
    lng: number;
    intensity: number;
    demandType: string;
    value: number;
  }>;
  intensity: 'high' | 'medium' | 'low';
  timestamp: number;
}

// Route optimization types
interface RouteOptimization {
  routeId: string;
  vehicleId: string;
  originalWaypoints: Array<{ lat: number; lng: number }>;
  optimizedRoute: {
    optimizedWaypoints: Array<{ lat: number; lng: number }>;
    estimatedTime: number;
    estimatedDistance: number;
    fuelEfficiency: number;
    trafficAvoidance: string;
  };
  optimizationType: string;
  constraints: Record<string, any>;
  improvementMetrics: {
    timeImprovement: string;
    distanceReduction: string;
    fuelSavings: string;
    trafficAvoidance: string;
  };
  timestamp: number;
}

// Export types for use in components
export {
  WebSocketMessage,
  VehicleData,
  RouteData,
  TrafficData,
  MapAnalytics,
  PerformanceMetrics,
  SessionInfo,
  SubscriptionStats,
  Alert,
  MapLayer,
  GeofenceData,
  DriverPerformance,
  HeatmapData,
  RouteOptimization
};
