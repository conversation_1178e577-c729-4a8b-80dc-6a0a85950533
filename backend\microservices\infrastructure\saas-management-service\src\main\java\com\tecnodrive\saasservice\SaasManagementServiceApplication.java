package com.tecnodrive.saasservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE SaaS Management Service Application
 *
 * This service handles:
 * - Multi-tenant management for companies and schools
 * - Tenant onboarding and configuration
 * - Service plan management and billing
 * - Tenant-specific feature toggles
 * - Usage analytics and reporting
 * - Tenant isolation and security
 * - Subscription management
 * - Custom branding and configuration
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
public class SaasManagementServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SaasManagementServiceApplication.class, args);
    }
}
