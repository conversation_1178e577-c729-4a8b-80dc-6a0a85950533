package com.tecnodrive.hrservice.service;

import com.tecnodrive.hrservice.dto.EmployeeRequest;
import com.tecnodrive.hrservice.dto.EmployeeResponse;
import com.tecnodrive.hrservice.entity.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Employee Service Interface
 * 
 * Defines business logic operations for employee management
 */
public interface EmployeeService {

    /**
     * Create a new employee
     */
    EmployeeResponse createEmployee(EmployeeRequest request);

    /**
     * Get employee by ID
     */
    EmployeeResponse getEmployee(String id);

    /**
     * Get employee by email
     */
    EmployeeResponse getEmployeeByEmail(String email);

    /**
     * Get employee by employee number
     */
    EmployeeResponse getEmployeeByEmployeeNumber(String employeeNumber);

    /**
     * Get all employees
     */
    List<EmployeeResponse> getAllEmployees();

    /**
     * Get employees with pagination
     */
    Page<EmployeeResponse> getEmployees(Pageable pageable);

    /**
     * Get employees by company
     */
    List<EmployeeResponse> getEmployeesByCompany(String companyId);

    /**
     * Get employees by company with pagination
     */
    Page<EmployeeResponse> getEmployeesByCompany(String companyId, Pageable pageable);

    /**
     * Update employee
     */
    EmployeeResponse updateEmployee(String id, EmployeeRequest request);

    /**
     * Delete employee
     */
    void deleteEmployee(String id);

    /**
     * Get employees by status
     */
    List<EmployeeResponse> getEmployeesByStatus(Employee.EmployeeStatus status);

    /**
     * Get employees by company and status
     */
    List<EmployeeResponse> getEmployeesByCompanyAndStatus(String companyId, Employee.EmployeeStatus status);

    /**
     * Get employees by department
     */
    List<EmployeeResponse> getEmployeesByDepartment(String companyId, String department);

    /**
     * Get employees by position
     */
    List<EmployeeResponse> getEmployeesByPosition(String position);

    /**
     * Get employees by manager
     */
    List<EmployeeResponse> getEmployeesByManager(String managerId);

    /**
     * Get active employees
     */
    List<EmployeeResponse> getActiveEmployees();

    /**
     * Get active employees by company
     */
    List<EmployeeResponse> getActiveEmployeesByCompany(String companyId);

    /**
     * Update employee status
     */
    EmployeeResponse updateEmployeeStatus(String employeeId, Employee.EmployeeStatus status);

    /**
     * Assign manager to employee
     */
    EmployeeResponse assignManager(String employeeId, String managerId);

    /**
     * Update employee salary
     */
    EmployeeResponse updateEmployeeSalary(String employeeId, BigDecimal salary);

    /**
     * Update performance rating
     */
    EmployeeResponse updatePerformanceRating(String employeeId, BigDecimal rating, String notes);

    /**
     * Schedule performance review
     */
    EmployeeResponse schedulePerformanceReview(String employeeId, LocalDate reviewDate);

    /**
     * Complete performance review
     */
    EmployeeResponse completePerformanceReview(String employeeId, BigDecimal rating, String notes, LocalDate nextReviewDate);

    /**
     * Get employees on probation
     */
    List<EmployeeResponse> getEmployeesOnProbation();

    /**
     * Get employees with due performance reviews
     */
    List<EmployeeResponse> getEmployeesWithDuePerformanceReviews();

    /**
     * Get employees by employment type
     */
    List<EmployeeResponse> getEmployeesByEmploymentType(String companyId, Employee.EmploymentType employmentType);

    /**
     * Search employees
     */
    Page<EmployeeResponse> searchEmployees(String searchTerm, Pageable pageable);

    /**
     * Search employees by company
     */
    Page<EmployeeResponse> searchEmployeesByCompany(String companyId, String searchTerm, Pageable pageable);

    /**
     * Get employee statistics
     */
    EmployeeResponse.EmployeeStatistics getEmployeeStatistics(String companyId);

    /**
     * Get employee summaries
     */
    List<EmployeeResponse.EmployeeSummary> getEmployeeSummaries(String companyId);

    /**
     * Get department statistics
     */
    List<EmployeeResponse.DepartmentStatistics> getDepartmentStatistics(String companyId);

    /**
     * Get employees with birthdays in date range
     */
    List<EmployeeResponse> getEmployeesWithBirthdaysInRange(LocalDate startDate, LocalDate endDate);

    /**
     * Get employees with work anniversaries in date range
     */
    List<EmployeeResponse> getEmployeesWithAnniversariesInRange(LocalDate startDate, LocalDate endDate);

    /**
     * Get employees by salary range
     */
    List<EmployeeResponse> getEmployeesBySalaryRange(BigDecimal minSalary, BigDecimal maxSalary);

    /**
     * Get employees eligible for benefits
     */
    List<EmployeeResponse> getEmployeesEligibleForBenefits();

    /**
     * Get payroll summary
     */
    List<EmployeeResponse.PayrollSummary> getPayrollSummary(String companyId, Employee.PayFrequency payFrequency);

    /**
     * Update leave balance
     */
    EmployeeResponse updateLeaveBalance(String employeeId, Integer usedAnnualLeave, Integer usedSickLeave);

    /**
     * Terminate employee
     */
    EmployeeResponse terminateEmployee(String employeeId, LocalDate terminationDate, String reason);

    /**
     * Reactivate employee
     */
    EmployeeResponse reactivateEmployee(String employeeId);

    /**
     * Validate employee request
     */
    void validateEmployeeRequest(EmployeeRequest request);

    /**
     * Check if email is available
     */
    boolean isEmailAvailable(String email);

    /**
     * Check if employee number is available
     */
    boolean isEmployeeNumberAvailable(String employeeNumber);

    /**
     * Generate employee number
     */
    String generateEmployeeNumber(String companyId);
}
