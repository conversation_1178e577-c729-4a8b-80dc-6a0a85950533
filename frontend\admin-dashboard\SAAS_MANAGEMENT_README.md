# خدمة إدارة SaaS - TECNO DRIVE

خدمة شاملة لإدارة العملاء والاشتراكات والفوترة في نظام SaaS متعدد العملاء.

## 🚀 المميزات الرئيسية

### 👥 إدارة العملاء (Tenant Management)
- **قائمة العملاء الشاملة**: عرض جميع العملاء مع فلترة وبحث متقدم
- **إضافة/تعديل العملاء**: واجهة سهلة لإدارة بيانات العملاء
- **لوحة تحكم العميل**: عرض تفصيلي لكل عميل مع الإحصائيات
- **إدارة الحالات**: نشط، غير نشط، معلق
- **تتبع الاستخدام**: مراقبة استهلاك كل عميل

### 📋 إدارة الاشتراكات (Subscription Management)
- **الخطط المتعددة**: الأساسية، المتقدمة، المؤسسات
- **إدارة المقاعد**: تحديد عدد المستخدمين لكل اشتراك
- **تواريخ الاشتراك**: إدارة فترات الاشتراك والتجديد
- **حالات الاشتراك**: نشط، منتهي، ملغي، في الانتظار
- **التسعير المرن**: أسعار مختلفة حسب الخطة والمقاعد

### 💰 إدارة الفوترة (Billing Management)
- **إنشاء الفواتير**: تلقائي وجماعي
- **تتبع المدفوعات**: حالات الدفع والمتأخرات
- **تفاصيل الفواتير**: عرض مفصل لبنود كل فاتورة
- **تصدير PDF**: تحميل الفواتير بصيغة PDF
- **إرسال بالبريد**: إرسال الفواتير والتذكيرات
- **تقارير الإيرادات**: مخططات بيانية للإيرادات

### 📊 تحليلات الاستخدام (Usage Analytics)
- **مراقبة الاستخدام**: المستخدمين النشطين، استدعاءات API، التخزين
- **مخططات بيانية**: اتجاهات الاستخدام عبر الوقت
- **مقارنة العملاء**: تحليل الأداء بين العملاء
- **استخدام المميزات**: تحليل المميزات الأكثر استخداماً
- **مؤشرات الأداء**: وقت الاستجابة، معدل الأخطاء، وقت التشغيل

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Material-UI (MUI)** للتصميم
- **Recharts** للمخططات البيانية
- **MUI X Data Grid** للجداول المتقدمة

### Backend APIs
- **RESTful APIs** مع Spring Boot
- **Multi-tenant Architecture** 
- **JWT Authentication**
- **Role-based Access Control**

## 📋 المسارات والواجهات

### API Endpoints
```
# Tenant Management
GET    /api/saas/tenants
POST   /api/saas/tenants
GET    /api/saas/tenants/{id}
PUT    /api/saas/tenants/{id}
DELETE /api/saas/tenants/{id}

# Subscription Management
GET    /api/saas/tenants/{id}/subscriptions
POST   /api/saas/tenants/{id}/subscriptions
PUT    /api/saas/subscriptions/{id}
DELETE /api/saas/subscriptions/{id}

# Billing Management
GET    /api/saas/tenants/{id}/billing/invoices
POST   /api/saas/tenants/{id}/billing/invoices
PUT    /api/saas/billing/invoices/{id}/pay

# Usage Analytics
GET    /api/saas/tenants/{id}/usage?period={period}
```

### Frontend Routes
```
/saas                    - لوحة التحكم الرئيسية
/saas/tenants           - إدارة العملاء
/saas/tenants/{id}      - تفاصيل العميل
/saas/subscriptions     - إدارة الاشتراكات
/saas/billing          - إدارة الفوترة
/saas/analytics        - تحليلات الاستخدام
```

## 🎯 الخطط والأسعار

### الخطة الأساسية (BASIC)
- **السعر**: 500 ريال/شهر (أساسي)
- **المميزات**:
  - إدارة المستخدمين
  - التقارير الأساسية
  - الدعم الفني
- **المقاعد**: قابلة للتخصيص

### الخطة المتقدمة (PREMIUM)
- **السعر**: 1000 ريال/شهر (أساسي)
- **المميزات**:
  - جميع مميزات الأساسية
  - التحليلات المتقدمة
  - API مخصص
  - دعم أولوية
- **المقاعد**: قابلة للتخصيص

### خطة المؤسسات (ENTERPRISE)
- **السعر**: 2000 ريال/شهر (أساسي)
- **المميزات**:
  - جميع المميزات
  - دعم مخصص
  - SLA مضمون
  - تكامل مخصص
- **المقاعد**: غير محدودة

## 📊 لوحة التحكم الرئيسية

### الإحصائيات السريعة
- إجمالي العملاء
- الاشتراكات النشطة
- الإيرادات الشهرية
- إجمالي المستخدمين

### المخططات البيانية
- **اتجاه الإيرادات**: مخطط خطي للإيرادات الشهرية
- **توزيع العملاء**: مخطط دائري لحالات العملاء
- **الأنشطة الأخيرة**: قائمة بآخر الأنشطة في النظام

### الإجراءات السريعة
- إضافة عميل جديد
- إدارة الاشتراكات
- إنشاء فاتورة
- عرض التحليلات

## 🔧 التكوين والإعداد

### متغيرات البيئة
```env
# SaaS Service Configuration
REACT_APP_SAAS_SERVICE_URL=http://localhost:8080/api/saas

# Mock Data (للتطوير)
REACT_APP_ENABLE_MOCK_DATA=true
```

### البيانات التجريبية
عند تعيين `REACT_APP_ENABLE_MOCK_DATA=true`:
- بيانات تجريبية شاملة لجميع المكونات
- 4 عملاء تجريبيين مع بيانات متنوعة
- اشتراكات متعددة بخطط مختلفة
- فواتير بحالات مختلفة
- تحليلات استخدام واقعية

## 🎮 كيفية الاستخدام

### إدارة العملاء
1. انتقل إلى "إدارة SaaS" → "العملاء"
2. استخدم البحث والفلاتر للعثور على العملاء
3. انقر على "إضافة عميل" لإنشاء عميل جديد
4. انقر على اسم العميل لعرض التفاصيل

### إدارة الاشتراكات
1. انتقل إلى "الاشتراكات"
2. عرض جميع الاشتراكات مع إمكانية الفلترة
3. إضافة اشتراك جديد لعميل محدد
4. تعديل أو إلغاء الاشتراكات الموجودة

### إدارة الفوترة
1. انتقل إلى "الفوترة"
2. عرض جميع الفواتير وحالاتها
3. إنشاء فواتير جديدة أو جماعية
4. تحميل الفواتير أو إرسالها بالبريد

### تحليلات الاستخدام
1. انتقل إلى "التحليلات"
2. اختر العميل والفترة الزمنية
3. عرض المخططات والإحصائيات
4. تصدير التقارير

## 🔐 الأمان والصلاحيات

### الأدوار المدعومة
- **Super Admin**: إدارة كاملة لجميع العملاء
- **SaaS Manager**: إدارة العملاء والاشتراكات
- **Billing Manager**: إدارة الفوترة والمدفوعات
- **Analyst**: عرض التحليلات والتقارير

### الحماية
- JWT Authentication
- Role-based Access Control
- Multi-tenant Data Isolation
- Audit Trail للعمليات الحساسة

## 🚀 المميزات المتقدمة

### الفوترة التلقائية
- إنشاء فواتير شهرية تلقائياً
- تذكيرات الدفع عبر البريد والـ SMS
- معالجة المدفوعات المتأخرة

### التكامل مع بوابات الدفع
- دعم Stripe و PayPal
- معالجة المدفوعات الآمنة
- تحديث حالة الفواتير تلقائياً

### التنبيهات والإشعارات
- تنبيهات انتهاء الاشتراكات
- إشعارات تجاوز الحدود المسموحة
- تقارير الاستخدام الدورية

### التصدير والتقارير
- تصدير البيانات بصيغة CSV/Excel
- تقارير PDF مخصصة
- جدولة التقارير الدورية

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع ملف `BACKEND_INTEGRATION.md`
2. تحقق من console المتصفح للأخطاء
3. راجع logs الخدمات الخلفية
4. تأكد من صحة متغيرات البيئة

---

**TECNO DRIVE SaaS Management** - حل شامل لإدارة العملاء والاشتراكات 🏢💼✨
