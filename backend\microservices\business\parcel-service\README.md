# 📦 خدمة الطرود - TECNO DRIVE

## نظرة عامة

خدمة الطرود هي جزء أساسي من منصة TECNO DRIVE المخصصة لإدارة وتتبع الطرود بشكل شامل ومتطور.

## 🎯 المزايا الرئيسية

### ✅ **إدارة شاملة للطرود**
- إنشاء وتحديث الطرود
- تتبع متقدم مع GPS
- إدارة الحالات والأولويات
- حساب التكلفة التلقائي

### ✅ **نظام دفع متطور**
- دعم طرق دفع متعددة
- إدارة المدفوعات والاسترداد
- تتبع الرسوم والضرائب
- نظام التأمين

### ✅ **تتبع متقدم**
- تتبع GPS في الوقت الفعلي
- تحديثات الحالة التلقائية
- إشعارات ذكية
- سجل مفصل للأحداث

### ✅ **API شامل**
- 15+ endpoint متقدم
- دعم Pagination و Sorting
- البحث والتصفية المتقدمة
- إحصائيات مفصلة

## 🏗️ هيكل المشروع

```
parcel-service/
├── src/main/java/com/tecnodrive/parcelservice/
│   ├── controller/          # REST Controllers
│   ├── service/            # Business Logic
│   ├── repository/         # Data Access Layer
│   ├── entity/            # JPA Entities
│   ├── dto/               # Data Transfer Objects
│   └── config/            # Configuration Classes
├── src/main/resources/
│   ├── application.yml    # Configuration
│   └── db/migration/      # Database Migrations
└── src/test/              # Unit & Integration Tests
```

## 📊 نماذج البيانات

### **ParcelEntity - الطرد الرئيسي**
```java
- parcelId: String          // معرف فريد
- barcode: String           // الباركود
- senderName: String        // اسم المرسل
- receiverName: String      // اسم المستقبل
- weightKg: Double          // الوزن بالكيلوجرام
- dimensions: Dimensions    // الأبعاد
- status: ParcelStatus      // الحالة
- priority: ParcelPriority  // الأولوية
- estimatedCost: BigDecimal // التكلفة المقدرة
```

### **ParcelTrackingEntity - التتبع**
```java
- trackingId: String        // معرف التتبع
- parcelId: String          // معرف الطرد
- status: ParcelStatus      // الحالة
- location: String          // الموقع
- latitude: Double          // خط العرض
- longitude: Double         // خط الطول
- timestamp: LocalDateTime  // الوقت
```

### **ParcelPaymentEntity - المدفوعات**
```java
- paymentId: String         // معرف الدفع
- parcelId: String          // معرف الطرد
- amount: BigDecimal        // المبلغ
- method: PaymentMethod     // طريقة الدفع
- status: PaymentStatus     // حالة الدفع
- currency: String          // العملة
```

## 🔄 حالات الطرد

```java
public enum ParcelStatus {
    CREATED,           // تم الإنشاء
    PICKED_UP,         // تم الاستلام
    IN_TRANSIT,        // في الطريق
    OUT_FOR_DELIVERY,  // خارج للتوصيل
    DELIVERED,         // تم التوصيل
    RETURNED,          // تم الإرجاع
    CANCELLED,         // ملغي
    LOST,              // مفقود
    DAMAGED            // تالف
}
```

## 🎯 أولويات الطرد

```java
public enum ParcelPriority {
    LOW,      // منخفضة
    MEDIUM,   // متوسطة
    HIGH,     // عالية
    URGENT    // عاجل
}
```

## 🌐 API Endpoints

### **إدارة الطرود**
```http
POST   /api/v1/parcels              # إنشاء طرد جديد
GET    /api/v1/parcels/{id}         # الحصول على طرد
PUT    /api/v1/parcels/{id}         # تحديث طرد
DELETE /api/v1/parcels/{id}         # حذف طرد
GET    /api/v1/parcels              # جميع الطرود
```

### **البحث والتصفية**
```http
GET /api/v1/parcels/search?query=...     # البحث النصي
GET /api/v1/parcels/user/{userId}        # طرود المستخدم
GET /api/v1/parcels/status/{status}      # طرود بحالة معينة
GET /api/v1/parcels/barcode/{barcode}    # البحث بالباركود
```

### **إدارة الحالات**
```http
PATCH /api/v1/parcels/{id}/status        # تحديث الحالة
POST  /api/v1/parcels/{id}/cancel        # إلغاء الطرد
```

### **الإحصائيات والمعلومات**
```http
GET /api/v1/parcels/statistics           # إحصائيات شاملة
GET /api/v1/parcels/statuses             # الحالات المتاحة
GET /api/v1/parcels/priorities           # الأولويات المتاحة
GET /api/v1/parcels/health               # فحص صحة الخدمة
```

## 🚀 التشغيل والتطوير

### **المتطلبات**
- Java 17+
- Maven 3.8+
- PostgreSQL 15+
- Redis 7+

### **تشغيل قاعدة البيانات**
```bash
docker-compose up -d postgres redis
```

### **تطبيق Migration**
```bash
psql -d tecnodrive -f database/migrations/V2024_01_15__enhanced_database_schema.sql
```

### **تشغيل الخدمة**
```bash
cd services/parcel-service
mvn spring-boot:run
```

### **تشغيل الاختبارات**
```bash
mvn test
```

## 📝 أمثلة الاستخدام

### **إنشاء طرد جديد**
```bash
curl -X POST http://localhost:8083/api/v1/parcels \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "barcode": "123456789012",
    "senderName": "أحمد محمد",
    "receiverName": "خالد علي",
    "senderAddress": "شارع بغداد، صنعاء",
    "receiverAddress": "شارع الزبيري، صنعاء",
    "weightKg": 2.5,
    "dimensions": {
      "lengthCm": 30,
      "widthCm": 20,
      "heightCm": 10
    },
    "priority": "MEDIUM",
    "fragile": false
  }'
```

### **البحث عن طرود**
```bash
curl "http://localhost:8083/api/v1/parcels/search?query=أحمد&page=0&size=10"
```

### **تحديث حالة الطرد**
```bash
curl -X PATCH http://localhost:8083/api/v1/parcels/PCL-123/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "IN_TRANSIT",
    "updatedBy": "driver123"
  }'
```

### **الحصول على إحصائيات**
```bash
curl http://localhost:8083/api/v1/parcels/statistics
```

## 🔧 الإعدادات

### **application.yml**
```yaml
server:
  port: 8083
  servlet:
    context-path: /api/v1/parcels

spring:
  datasource:
    url: ******************************************
    username: tecnodrive_user
    password: tecnodrive_pass
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

app:
  parcel:
    max-weight-kg: 50.0
    base-price: 15.0
    price-per-kg: 10.0
    fragile-fee: 20.0
```

## 🧪 الاختبارات

### **اختبارات الوحدة**
```bash
mvn test -Dtest=ParcelServiceTest
```

### **اختبارات التكامل**
```bash
mvn test -Dtest=ParcelIntegrationTest
```

### **تغطية الاختبارات**
```bash
mvn jacoco:report
```

## 📊 المراقبة والصحة

### **Health Check**
```bash
curl http://localhost:8083/api/v1/parcels/health
```

### **Metrics (Prometheus)**
```bash
curl http://localhost:8083/actuator/prometheus
```

### **Application Info**
```bash
curl http://localhost:8083/actuator/info
```

## 🔒 الأمان

- **Input Validation**: تحقق شامل من المدخلات
- **Error Handling**: معالجة آمنة للأخطاء
- **Logging**: تسجيل مفصل للعمليات
- **CORS**: دعم Cross-Origin Requests

## 🐛 استكشاف الأخطاء

### **مشاكل شائعة**

1. **خطأ الاتصال بقاعدة البيانات**
   ```bash
   # التحقق من حالة PostgreSQL
   docker logs postgres-tecno
   ```

2. **خطأ في Redis**
   ```bash
   # التحقق من حالة Redis
   docker logs redis-tecno
   ```

3. **مشاكل Migration**
   ```bash
   # إعادة تطبيق Migration
   psql -d tecnodrive -f database/migrations/V2024_01_15__enhanced_database_schema.sql
   ```

## 📞 الدعم

للمساعدة أو الاستفسارات:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +967-1-234567
- 💬 الدردشة: support.tecnodrive.com

---

**آخر تحديث**: 15 يناير 2024  
**الإصدار**: 2.1.0  
**المطور**: فريق TECNO DRIVE
