# Final Integration Test for TecnoDrive Platform
Write-Host "🧪 Final Integration Test for TecnoDrive Platform" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to test API endpoint
function Test-ApiEndpoint {
    param([string]$Name, [string]$Url, [string]$Method = "GET", [string]$Body = $null)
    
    try {
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json" -TimeoutSec 5
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 5
        }
        
        if ($response) {
            Write-Host "✅ ${Name}: Working" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ ${Name}: Failed - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check port
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

Write-Host "`n🔍 Testing Core Services..." -ForegroundColor Yellow

# Test core services
$coreServices = @(
    @{ Name = "Eureka Server"; Port = 8761; Url = "http://localhost:8761" },
    @{ Name = "API Gateway"; Port = 8080; Url = "http://localhost:8080/actuator/health" },
    @{ Name = "Auth Service"; Port = 8081; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "Ride Service"; Port = 8082; Url = "http://localhost:8082/actuator/health" },
    @{ Name = "User Service"; Port = 8083; Url = "http://localhost:8083/actuator/health" },
    @{ Name = "Fleet Service"; Port = 8084; Url = "http://localhost:8084/actuator/health" },
    @{ Name = "Location Service"; Port = 8085; Url = "http://localhost:8085/actuator/health" },
    @{ Name = "Payment Service"; Port = 8086; Url = "http://localhost:8086/actuator/health" }
)

$runningServices = 0
foreach ($service in $coreServices) {
    if (Test-Port -Port $service.Port) {
        Write-Host "✅ $($service.Name) is running on port $($service.Port)" -ForegroundColor Green
        $runningServices++
        
        # Test health endpoint
        if (Test-ApiEndpoint -Name "$($service.Name) Health" -Url $service.Url) {
            Write-Host "   ✓ Health check passed" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ $($service.Name) is not running on port $($service.Port)" -ForegroundColor Red
    }
}

$healthPercentage = [math]::Round(($runningServices / $coreServices.Count) * 100, 1)
Write-Host "`n📊 Core Services: $runningServices/$($coreServices.Count) running ($healthPercentage%)" -ForegroundColor Cyan

Write-Host "`n🔍 Testing Data APIs..." -ForegroundColor Yellow

# Test data APIs
$dataApis = @(
    @{ Name = "Rides API"; Url = "http://localhost:8082/api/rides" },
    @{ Name = "Users API"; Url = "http://localhost:8083/api/users" },
    @{ Name = "Fleet API"; Url = "http://localhost:8084/api/fleet/vehicles" },
    @{ Name = "Payments API"; Url = "http://localhost:8086/api/payments" }
)

$workingApis = 0
foreach ($api in $dataApis) {
    if (Test-ApiEndpoint -Name $api.Name -Url $api.Url) {
        $workingApis++
    }
}

Write-Host "`n📊 Data APIs: $workingApis/$($dataApis.Count) working" -ForegroundColor Cyan

Write-Host "`n🔍 Testing Location Service Integration..." -ForegroundColor Yellow

# Test location service specifically
if (Test-Port -Port 8085) {
    Write-Host "✅ Location Service is running" -ForegroundColor Green
    
    # Test location update
    $locationData = @{
        vehicleId = "test_vehicle_001"
        lat = 24.7136
        lng = 46.6753
        speed = 45.5
        heading = 180
        status = "busy"
        driverName = "Test Driver"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8085/api/locations/update" -Method POST -Body $locationData -ContentType "application/json" -TimeoutSec 5
        Write-Host "✅ Location Update: Working" -ForegroundColor Green
    } catch {
        Write-Host "❌ Location Update: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test alert
    $alertData = @{
        type = "test_alert"
        severity = "info"
        vehicleId = "test_vehicle_001"
        message = "Integration test alert"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8085/api/locations/alert" -Method POST -Body $alertData -ContentType "application/json" -TimeoutSec 5
        Write-Host "✅ Alert System: Working" -ForegroundColor Green
    } catch {
        Write-Host "❌ Alert System: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ Location Service is not running" -ForegroundColor Red
}

Write-Host "`n🔍 Testing Frontend..." -ForegroundColor Yellow

# Test frontend
if (Test-Port -Port 3000) {
    Write-Host "✅ Frontend is running on port 3000" -ForegroundColor Green
    
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
        if ($frontendResponse.StatusCode -eq 200) {
            Write-Host "✅ Frontend: Accessible" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Frontend: Not accessible" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Frontend is not running" -ForegroundColor Red
}

Write-Host "`n🧪 Testing Smart API Integration..." -ForegroundColor Yellow

# Test smart API fallback
$smartApiTests = @(
    @{ Name = "Smart Rides API"; Url = "http://localhost:8082/api/rides"; Fallback = "Mock Data" },
    @{ Name = "Smart Users API"; Url = "http://localhost:8083/api/users"; Fallback = "Mock Data" },
    @{ Name = "Smart Fleet API"; Url = "http://localhost:8084/api/fleet/vehicles"; Fallback = "Mock Data" }
)

$smartApiWorking = 0
foreach ($test in $smartApiTests) {
    if (Test-ApiEndpoint -Name $test.Name -Url $test.Url) {
        Write-Host "   ✓ Using real data from service" -ForegroundColor Green
        $smartApiWorking++
    } else {
        Write-Host "   ⚠️ Service down - will use $($test.Fallback)" -ForegroundColor Yellow
    }
}

Write-Host "`n📊 Smart API Status: $smartApiWorking/$($smartApiTests.Count) services using real data" -ForegroundColor Cyan

Write-Host "`n🎯 Final Integration Summary" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

$overallHealth = [math]::Round((($runningServices + $workingApis + $smartApiWorking) / ($coreServices.Count + $dataApis.Count + $smartApiTests.Count)) * 100, 1)

if ($overallHealth -ge 90) {
    $statusColor = "Green"
    $statusText = "Excellent"
    $recommendation = "🎉 Platform is ready for production!"
} elseif ($overallHealth -ge 75) {
    $statusColor = "Yellow"
    $statusText = "Good"
    $recommendation = "✅ Platform is working well with smart fallbacks."
} elseif ($overallHealth -ge 50) {
    $statusColor = "DarkYellow"
    $statusText = "Fair"
    $recommendation = "⚠️ Some services down but smart API provides continuity."
} else {
    $statusColor = "Red"
    $statusText = "Poor"
    $recommendation = "❌ Many services down. Check infrastructure."
}

Write-Host "🎯 Overall Platform Health: $overallHealth% - $statusText" -ForegroundColor $statusColor
Write-Host "💡 Status: $recommendation" -ForegroundColor White

Write-Host "`n🌐 Access URLs:" -ForegroundColor Yellow
Write-Host "   • Frontend Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host "   • Live Operations: http://localhost:3000/live-operations" -ForegroundColor White
Write-Host "   • API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "   • Eureka Dashboard: http://localhost:8761" -ForegroundColor White

Write-Host "`n✨ Enhanced Features Available:" -ForegroundColor Yellow
Write-Host "   ✅ Smart API with automatic fallback" -ForegroundColor White
Write-Host "   ✅ Real-time WebSocket integration" -ForegroundColor White
Write-Host "   ✅ Cross-service communication" -ForegroundColor White
Write-Host "   ✅ Service health monitoring" -ForegroundColor White
Write-Host "   ✅ Emergency alert system" -ForegroundColor White

Write-Host "`n🎮 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Open http://localhost:3000 to access the dashboard" -ForegroundColor White
Write-Host "   2. Check service status indicator in the dashboard" -ForegroundColor White
Write-Host "   3. Navigate to Live Operations for real-time tracking" -ForegroundColor White
Write-Host "   4. Test location updates: .\generate-demo-data.ps1" -ForegroundColor White

if ($overallHealth -ge 75) {
    Write-Host "`n🎉 Integration Test Complete - Platform Ready!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ Integration Test Complete - Some Issues Found" -ForegroundColor Yellow
}

Write-Host "`n📚 Documentation:" -ForegroundColor Yellow
Write-Host "   • Enhanced Integration Guide: ENHANCED_INTEGRATION_SUMMARY.md" -ForegroundColor White
Write-Host "   • Smart API Guide: SMART_BACKEND_INTEGRATION_GUIDE.md" -ForegroundColor White
Write-Host "   • Frontend Guide: COMPLETED_PAGES_GUIDE.md" -ForegroundColor White
