# Simple Frontend Starter - TecnoDrive Platform
Write-Host "🚀 Starting TecnoDrive Frontend" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Navigate to frontend directory
$frontendPath = "frontend/admin-dashboard"
if (Test-Path $frontendPath) {
    Set-Location $frontendPath
    Write-Host "✅ Found frontend directory" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend directory not found" -ForegroundColor Red
    exit 1
}

# Kill existing processes
Write-Host "`n🛑 Stopping existing processes..." -ForegroundColor Yellow
try {
    Get-Process | Where-Object {$_.ProcessName -eq "node" -and $_.MainWindowTitle -like "*react*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Existing processes stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️ No existing processes found" -ForegroundColor Yellow
}

# Set environment variables
Write-Host "`n🔧 Setting environment variables..." -ForegroundColor Yellow
$env:BROWSER = "none"
$env:GENERATE_SOURCEMAP = "false"
$env:TSC_COMPILE_ON_ERROR = "true"
$env:ESLINT_NO_DEV_ERRORS = "true"
$env:REACT_APP_API_BASE_URL = "http://localhost:8085"
$env:REACT_APP_WEBSOCKET_URL = "ws://localhost:8085/ws"
Write-Host "✅ Environment variables set" -ForegroundColor Green

# Check if package.json exists
if (Test-Path "package.json") {
    Write-Host "✅ package.json found" -ForegroundColor Green
} else {
    Write-Host "❌ package.json not found" -ForegroundColor Red
    exit 1
}

# Check if node_modules exists
if (Test-Path "node_modules") {
    Write-Host "✅ node_modules found" -ForegroundColor Green
} else {
    Write-Host "⚠️ node_modules not found, installing..." -ForegroundColor Yellow
    npm install
}

Write-Host "`n🚀 Starting React development server..." -ForegroundColor Yellow
Write-Host "This will open in a new window. Please wait..." -ForegroundColor Gray

# Start the development server
npm start

Write-Host "`n🎉 Frontend startup completed!" -ForegroundColor Green
