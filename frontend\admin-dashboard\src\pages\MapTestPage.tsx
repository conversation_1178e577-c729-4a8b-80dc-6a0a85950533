import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  <PERSON>ton,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import {
  Map as MapIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import EnhancedMap from '../components/Maps/EnhancedMap';

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

const MapTestPage: React.FC = () => {
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [apiStatus, setApiStatus] = useState<{
    mapConfig: boolean;
    vehicles: boolean;
    tiles: boolean;
  }>({
    mapConfig: false,
    vehicles: false,
    tiles: false
  });

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
  };

  const testMapAPIs = async () => {
    const endpoints = [
      { name: 'mapConfig', url: 'http://localhost:8085/api/map/config/enhanced' },
      { name: 'vehicles', url: 'http://localhost:8085/api/map/vehicles' },
      { name: 'tiles', url: 'http://localhost:8085/api/map/tiles' }
    ];

    const newStatus = { mapConfig: false, vehicles: false, tiles: false };

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.url);
        newStatus[endpoint.name as keyof typeof newStatus] = response.ok;
      } catch (error) {
        console.error(`Failed to test ${endpoint.name}:`, error);
      }
    }

    setApiStatus(newStatus);
  };

  React.useEffect(() => {
    testMapAPIs();
  }, []);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <MapIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          اختبار خرائط MAP STREET API
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          اختبار وتشخيص خدمات الخرائط والموقع
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* API Status Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة APIs
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">Map Configuration</Typography>
                  <Chip
                    icon={apiStatus.mapConfig ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.mapConfig ? 'متصل' : 'غير متصل'}
                    color={apiStatus.mapConfig ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">Vehicle Data</Typography>
                  <Chip
                    icon={apiStatus.vehicles ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.vehicles ? 'متصل' : 'غير متصل'}
                    color={apiStatus.vehicles ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">Tile Servers</Typography>
                  <Chip
                    icon={apiStatus.tiles ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.tiles ? 'متصل' : 'غير متصل'}
                    color={apiStatus.tiles ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Box>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={testMapAPIs}
                sx={{ mt: 2 }}
              >
                اختبار APIs
              </Button>
            </CardContent>
          </Card>

          {/* Selected Vehicle Info */}
          {selectedVehicle && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  معلومات المركبة المحددة
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>ID:</strong> {selectedVehicle.id}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>السائق:</strong> {selectedVehicle.driver}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>السرعة:</strong> {selectedVehicle.speed} كم/س
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>الموقع:</strong> {selectedVehicle.lat.toFixed(4)}, {selectedVehicle.lng.toFixed(4)}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>الحالة:</strong> {selectedVehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                </Typography>
                
                <Chip
                  label={selectedVehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                  color={selectedVehicle.status === 'active' ? 'success' : 'warning'}
                  size="small"
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تعليمات الاختبار
              </Typography>
              
              <Typography variant="body2" paragraph>
                1. تأكد من أن جميع APIs متصلة (أخضر)
              </Typography>
              <Typography variant="body2" paragraph>
                2. جرب تغيير مزود الخريطة من القائمة
              </Typography>
              <Typography variant="body2" paragraph>
                3. اضغط على المركبات لرؤية التفاصيل
              </Typography>
              <Typography variant="body2" paragraph>
                4. جرب تفعيل/إلغاء صور الأقمار الصناعية
              </Typography>
              <Typography variant="body2">
                5. استخدم أزرار التحكم للتنقل
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Map Panel */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              خريطة تفاعلية محسنة
            </Typography>
            
            {!apiStatus.mapConfig ? (
              <Alert severity="warning" sx={{ mb: 2 }}>
                تحذير: Map Service غير متصل. قد لا تعمل الخريطة بشكل صحيح.
              </Alert>
            ) : (
              <Alert severity="success" sx={{ mb: 2 }}>
                Map Service متصل ويعمل بشكل صحيح.
              </Alert>
            )}

            <EnhancedMap
              height="500px"
              showControls={true}
              showVehicles={true}
              onVehicleClick={handleVehicleClick}
            />
          </Paper>
        </Grid>

        {/* API Endpoints Info */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Map API Endpoints المتاحة
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Configuration APIs:
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                    GET /api/map/config/enhanced
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                    GET /api/map/tiles
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    GET /health
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Data APIs:
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                    GET /api/map/vehicles
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                    GET /api/locations/stats
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    POST /api/locations/update
                  </Typography>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Map Providers المدعومة:
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip label="OpenStreetMap" color="primary" size="small" />
                <Chip label="CartoDB Light" color="secondary" size="small" />
                <Chip label="CartoDB Dark" color="secondary" size="small" />
                <Chip label="Satellite View" color="info" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MapTestPage;
