import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Avatar,
  Container,
  Paper,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Lock as LockIcon,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { loginUser, setCredentials } from '../../store/slices/authSlice';
import { persistentLogin, autoLogin, shouldAutoLogin } from '../../services/persistentAuthService';
import { useNavigate } from 'react-router-dom';

const Login: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state: RootState) => state.auth);

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [autoLoginAttempted, setAutoLoginAttempted] = useState(false);

  // Auto-login effect (only on login page)
  useEffect(() => {
    const attemptAutoLogin = async () => {
      if (autoLoginAttempted) return;

      // Only auto-login if we're on the login page
      const currentPath = window.location.pathname;
      if (currentPath !== '/login') {
        setAutoLoginAttempted(true);
        return;
      }

      console.log('🔄 Checking for auto-login on login page...');

      if (shouldAutoLogin()) {
        console.log('✅ Auto-login enabled, attempting...');

        try {
          const result = await autoLogin();

          if (result && result.success && result.data) {
            console.log('✅ Auto-login successful:', result.data.user.email);

            // Update Redux state
            dispatch(setCredentials({
              user: result.data.user,
              token: result.data.token
            }));

            // Redirect to dashboard only from login page
            navigate('/dashboard');
            return;
          }
        } catch (error) {
          console.error('❌ Auto-login failed:', error);
        }
      }

      setAutoLoginAttempted(true);
    };

    attemptAutoLogin();
  }, [dispatch, navigate, autoLoginAttempted]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      console.log('🔐 Login attempt with persistent auth:', {
        email: formData.email,
        rememberMe: formData.rememberMe
      });

      // Use persistent login service
      const result = await persistentLogin({
        email: formData.email,
        password: formData.password,
        rememberMe: formData.rememberMe
      });

      if (result.success && result.data) {
        console.log('✅ Persistent login successful:', result.data.user.email);

        // Update Redux state
        dispatch(setCredentials({
          user: result.data.user,
          token: result.data.token
        }));

        // Redirect to dashboard
        navigate('/dashboard');
      } else {
        console.error('❌ Persistent login failed:', result.message);

        // Fallback to regular login
        const fallbackResult = await dispatch(loginUser({
          email: formData.email,
          password: formData.password
        }) as any);

        if (loginUser.fulfilled.match(fallbackResult)) {
          console.log('✅ Fallback login successful');
        } else {
          console.error('❌ Fallback login failed:', fallbackResult.error);
        }
      }
    } catch (error) {
      console.error('❌ Login error:', error);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <Paper
          elevation={10}
          sx={{
            p: 4,
            borderRadius: 3,
            width: '100%',
            maxWidth: 400,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Avatar
              sx={{
                m: 1,
                bgcolor: 'primary.main',
                width: 56,
                height: 56,
              }}
            >
              <LockIcon fontSize="large" />
            </Avatar>
            <Typography component="h1" variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              تكنو درايف
            </Typography>
            <Typography variant="h6" color="text.secondary">
              لوحة التحكم الإدارية
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="البريد الإلكتروني"
              name="email"
              autoComplete="email"
              autoFocus
              value={formData.email}
              onChange={handleChange}
              variant="outlined"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="كلمة المرور"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={formData.password}
              onChange={handleChange}
              variant="outlined"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <FormControlLabel
              control={
                <Checkbox
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                  color="primary"
                />
              }
              label="تذكرني (البقاء مسجلاً لمدة 30 يوماً)"
              sx={{ mt: 1, mb: 1 }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                mt: 3,
                mb: 2,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'bold',
              }}
              disabled={loading}
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </Button>
          </Box>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              للاختبار: استخدم أي بريد إلكتروني وكلمة مرور
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;
