"""
TECNO DRIVE Smart Alert System
Advanced alert management with state machine and AI-driven insights
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import asyncpg
import aioredis
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertState(Enum):
    NEW = "new"
    ACKNOWLEDGED = "acknowledged"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    ESCALATED = "escalated"
    DISMISSED = "dismissed"

class AlertType(Enum):
    VEHICLE_BREAKDOWN = "vehicle_breakdown"
    TRAFFIC_CONGESTION = "traffic_congestion"
    DEMAND_SPIKE = "demand_spike"
    ROUTE_DEVIATION = "route_deviation"
    FUEL_LOW = "fuel_low"
    MAINTENANCE_DUE = "maintenance_due"
    SPEED_VIOLATION = "speed_violation"
    GEOFENCE_VIOLATION = "geofence_violation"
    CUSTOMER_COMPLAINT = "customer_complaint"
    SYSTEM_ANOMALY = "system_anomaly"

@dataclass
class SmartAlert:
    id: str
    alert_type: AlertType
    severity: AlertSeverity
    state: AlertState
    title: str
    description: str
    entity_id: Optional[str] = None
    entity_type: Optional[str] = None
    zone_id: Optional[str] = None
    vehicle_id: Optional[str] = None
    ride_id: Optional[str] = None
    ai_confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None
    resolution_notes: Optional[str] = None
    escalation_level: int = 0
    auto_resolve_at: Optional[datetime] = None

class AlertStateMachine:
    """
    State machine for managing alert lifecycle
    """
    
    VALID_TRANSITIONS = {
        AlertState.NEW: [AlertState.ACKNOWLEDGED, AlertState.DISMISSED, AlertState.ESCALATED],
        AlertState.ACKNOWLEDGED: [AlertState.IN_PROGRESS, AlertState.DISMISSED, AlertState.ESCALATED],
        AlertState.IN_PROGRESS: [AlertState.RESOLVED, AlertState.ESCALATED],
        AlertState.ESCALATED: [AlertState.ACKNOWLEDGED, AlertState.IN_PROGRESS, AlertState.RESOLVED],
        AlertState.RESOLVED: [],  # Terminal state
        AlertState.DISMISSED: []  # Terminal state
    }
    
    @classmethod
    def can_transition(cls, from_state: AlertState, to_state: AlertState) -> bool:
        """Check if transition is valid"""
        return to_state in cls.VALID_TRANSITIONS.get(from_state, [])
    
    @classmethod
    def get_valid_transitions(cls, current_state: AlertState) -> List[AlertState]:
        """Get all valid transitions from current state"""
        return cls.VALID_TRANSITIONS.get(current_state, [])

class AlertManager:
    """
    Advanced alert management system with AI integration
    """
    
    def __init__(self, db_pool: asyncpg.Pool, redis_client: aioredis.Redis):
        self.db_pool = db_pool
        self.redis = redis_client
        self.state_machine = AlertStateMachine()
        
        # Alert escalation rules
        self.escalation_rules = {
            AlertSeverity.CRITICAL: timedelta(minutes=5),
            AlertSeverity.HIGH: timedelta(minutes=15),
            AlertSeverity.MEDIUM: timedelta(hours=1),
            AlertSeverity.LOW: timedelta(hours=4)
        }
        
        # Auto-resolution rules
        self.auto_resolution_rules = {
            AlertType.TRAFFIC_CONGESTION: timedelta(hours=2),
            AlertType.DEMAND_SPIKE: timedelta(hours=1),
            AlertType.SYSTEM_ANOMALY: timedelta(minutes=30)
        }
    
    async def create_alert(self, alert_data: Dict[str, Any]) -> SmartAlert:
        """Create a new smart alert"""
        try:
            alert = SmartAlert(
                id=alert_data.get('id'),
                alert_type=AlertType(alert_data['alert_type']),
                severity=AlertSeverity(alert_data['severity']),
                state=AlertState.NEW,
                title=alert_data['title'],
                description=alert_data['description'],
                entity_id=alert_data.get('entity_id'),
                entity_type=alert_data.get('entity_type'),
                zone_id=alert_data.get('zone_id'),
                vehicle_id=alert_data.get('vehicle_id'),
                ride_id=alert_data.get('ride_id'),
                ai_confidence=alert_data.get('ai_confidence'),
                metadata=alert_data.get('metadata', {}),
                created_at=datetime.now()
            )
            
            # Set auto-resolution time if applicable
            if alert.alert_type in self.auto_resolution_rules:
                alert.auto_resolve_at = alert.created_at + self.auto_resolution_rules[alert.alert_type]
            
            # Save to database
            await self._save_alert(alert)
            
            # Cache in Redis for fast access
            await self._cache_alert(alert)
            
            # Send notifications
            await self._send_notifications(alert)
            
            # Schedule escalation if needed
            await self._schedule_escalation(alert)
            
            logger.info(f"Created alert {alert.id} with severity {alert.severity.value}")
            return alert
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            raise
    
    async def update_alert_state(self, alert_id: str, new_state: AlertState, 
                                user_id: str = None, notes: str = None) -> SmartAlert:
        """Update alert state with validation"""
        try:
            # Get current alert
            alert = await self.get_alert(alert_id)
            if not alert:
                raise ValueError(f"Alert {alert_id} not found")
            
            # Validate state transition
            if not self.state_machine.can_transition(alert.state, new_state):
                raise ValueError(f"Invalid transition from {alert.state.value} to {new_state.value}")
            
            # Update alert
            alert.state = new_state
            alert.updated_at = datetime.now()
            
            if new_state == AlertState.ACKNOWLEDGED:
                alert.acknowledged_by = user_id
                alert.acknowledged_at = datetime.now()
            elif new_state == AlertState.RESOLVED:
                alert.resolved_by = user_id
                alert.resolved_at = datetime.now()
                alert.resolution_notes = notes
            
            # Save changes
            await self._save_alert(alert)
            await self._cache_alert(alert)
            
            # Log state change
            await self._log_state_change(alert, new_state, user_id, notes)
            
            # Send notifications for important state changes
            if new_state in [AlertState.ESCALATED, AlertState.RESOLVED]:
                await self._send_notifications(alert)
            
            logger.info(f"Updated alert {alert_id} state to {new_state.value}")
            return alert
            
        except Exception as e:
            logger.error(f"Error updating alert state: {e}")
            raise
    
    async def escalate_alert(self, alert_id: str, escalation_reason: str = None) -> SmartAlert:
        """Escalate alert to higher priority"""
        try:
            alert = await self.get_alert(alert_id)
            if not alert:
                raise ValueError(f"Alert {alert_id} not found")
            
            # Increase escalation level
            alert.escalation_level += 1
            
            # Upgrade severity if not already critical
            if alert.severity != AlertSeverity.CRITICAL:
                if alert.severity == AlertSeverity.LOW:
                    alert.severity = AlertSeverity.MEDIUM
                elif alert.severity == AlertSeverity.MEDIUM:
                    alert.severity = AlertSeverity.HIGH
                elif alert.severity == AlertSeverity.HIGH:
                    alert.severity = AlertSeverity.CRITICAL
            
            # Update state
            alert.state = AlertState.ESCALATED
            alert.updated_at = datetime.now()
            
            # Add escalation metadata
            if not alert.metadata:
                alert.metadata = {}
            alert.metadata['escalation_history'] = alert.metadata.get('escalation_history', [])
            alert.metadata['escalation_history'].append({
                'level': alert.escalation_level,
                'reason': escalation_reason,
                'timestamp': datetime.now().isoformat()
            })
            
            # Save changes
            await self._save_alert(alert)
            await self._cache_alert(alert)
            
            # Send escalation notifications
            await self._send_escalation_notifications(alert)
            
            logger.warning(f"Escalated alert {alert_id} to level {alert.escalation_level}")
            return alert
            
        except Exception as e:
            logger.error(f"Error escalating alert: {e}")
            raise
    
    async def get_alert(self, alert_id: str) -> Optional[SmartAlert]:
        """Get alert by ID"""
        try:
            # Try cache first
            cached = await self.redis.get(f"alert:{alert_id}")
            if cached:
                data = json.loads(cached)
                return self._dict_to_alert(data)
            
            # Fallback to database
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM analytics.smart_events WHERE id = $1",
                    alert_id
                )
                if row:
                    return self._row_to_alert(row)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting alert {alert_id}: {e}")
            return None
    
    async def get_active_alerts(self, filters: Dict[str, Any] = None) -> List[SmartAlert]:
        """Get all active alerts with optional filters"""
        try:
            query = """
            SELECT * FROM analytics.smart_events 
            WHERE state NOT IN ('resolved', 'dismissed')
            """
            params = []
            
            if filters:
                if 'severity' in filters:
                    query += " AND severity = $" + str(len(params) + 1)
                    params.append(filters['severity'])
                
                if 'alert_type' in filters:
                    query += " AND event_type = $" + str(len(params) + 1)
                    params.append(filters['alert_type'])
                
                if 'zone_id' in filters:
                    query += " AND zone_id = $" + str(len(params) + 1)
                    params.append(filters['zone_id'])
            
            query += " ORDER BY created_at DESC LIMIT 100"
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                return [self._row_to_alert(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting active alerts: {e}")
            return []
    
    async def process_auto_escalations(self):
        """Process alerts that need automatic escalation"""
        try:
            current_time = datetime.now()
            
            # Get alerts that need escalation
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM analytics.smart_events 
                    WHERE state IN ('new', 'acknowledged') 
                    AND created_at + INTERVAL '1 hour' * 
                        CASE severity 
                            WHEN 'critical' THEN 0.083  -- 5 minutes
                            WHEN 'high' THEN 0.25       -- 15 minutes
                            WHEN 'medium' THEN 1         -- 1 hour
                            WHEN 'low' THEN 4            -- 4 hours
                        END < $1
                """, current_time)
                
                for row in rows:
                    alert = self._row_to_alert(row)
                    await self.escalate_alert(alert.id, "Automatic escalation due to timeout")
                    
        except Exception as e:
            logger.error(f"Error processing auto escalations: {e}")
    
    async def process_auto_resolutions(self):
        """Process alerts that can be auto-resolved"""
        try:
            current_time = datetime.now()
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM analytics.smart_events 
                    WHERE auto_resolve_at IS NOT NULL 
                    AND auto_resolve_at <= $1
                    AND state NOT IN ('resolved', 'dismissed')
                """, current_time)
                
                for row in rows:
                    alert = self._row_to_alert(row)
                    await self.update_alert_state(
                        alert.id, 
                        AlertState.RESOLVED, 
                        "system", 
                        "Auto-resolved based on time-based rules"
                    )
                    
        except Exception as e:
            logger.error(f"Error processing auto resolutions: {e}")
    
    async def _save_alert(self, alert: SmartAlert):
        """Save alert to database"""
        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO analytics.smart_events 
                (id, time, event_type, severity, description, zone_id, vehicle_id, 
                 ai_confidence, metadata, state, acknowledged_by, acknowledged_at,
                 resolved_by, resolved_at, resolution_notes, escalation_level, auto_resolve_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                ON CONFLICT (id) DO UPDATE SET
                    time = EXCLUDED.time,
                    event_type = EXCLUDED.event_type,
                    severity = EXCLUDED.severity,
                    description = EXCLUDED.description,
                    zone_id = EXCLUDED.zone_id,
                    vehicle_id = EXCLUDED.vehicle_id,
                    ai_confidence = EXCLUDED.ai_confidence,
                    metadata = EXCLUDED.metadata,
                    state = EXCLUDED.state,
                    acknowledged_by = EXCLUDED.acknowledged_by,
                    acknowledged_at = EXCLUDED.acknowledged_at,
                    resolved_by = EXCLUDED.resolved_by,
                    resolved_at = EXCLUDED.resolved_at,
                    resolution_notes = EXCLUDED.resolution_notes,
                    escalation_level = EXCLUDED.escalation_level,
                    auto_resolve_at = EXCLUDED.auto_resolve_at
            """, 
            alert.id, alert.created_at or datetime.now(), alert.alert_type.value,
            alert.severity.value, alert.description, alert.zone_id, alert.vehicle_id,
            alert.ai_confidence, json.dumps(alert.metadata) if alert.metadata else None,
            alert.state.value, alert.acknowledged_by, alert.acknowledged_at,
            alert.resolved_by, alert.resolved_at, alert.resolution_notes,
            alert.escalation_level, alert.auto_resolve_at)
    
    async def _cache_alert(self, alert: SmartAlert):
        """Cache alert in Redis"""
        await self.redis.setex(
            f"alert:{alert.id}",
            3600,  # 1 hour TTL
            json.dumps(asdict(alert), default=str)
        )
    
    async def _send_notifications(self, alert: SmartAlert):
        """Send notifications for alert"""
        # Implementation would depend on notification channels
        # (email, SMS, push notifications, Slack, etc.)
        logger.info(f"Sending notifications for alert {alert.id}")
        
        # Example: Send to notification queue
        notification_data = {
            'alert_id': alert.id,
            'type': 'alert_notification',
            'severity': alert.severity.value,
            'title': alert.title,
            'description': alert.description,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.redis.lpush('notification_queue', json.dumps(notification_data))
    
    async def _send_escalation_notifications(self, alert: SmartAlert):
        """Send escalation notifications"""
        logger.warning(f"Sending escalation notifications for alert {alert.id}")
        
        notification_data = {
            'alert_id': alert.id,
            'type': 'alert_escalation',
            'severity': alert.severity.value,
            'escalation_level': alert.escalation_level,
            'title': f"ESCALATED: {alert.title}",
            'description': alert.description,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.redis.lpush('escalation_queue', json.dumps(notification_data))
    
    async def _schedule_escalation(self, alert: SmartAlert):
        """Schedule automatic escalation"""
        if alert.severity in self.escalation_rules:
            escalation_time = alert.created_at + self.escalation_rules[alert.severity]
            
            # Store escalation schedule in Redis
            await self.redis.zadd(
                'escalation_schedule',
                {alert.id: escalation_time.timestamp()}
            )
    
    async def _log_state_change(self, alert: SmartAlert, new_state: AlertState, 
                               user_id: str = None, notes: str = None):
        """Log alert state changes for audit trail"""
        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO analytics.alert_state_log 
                (alert_id, old_state, new_state, changed_by, change_notes, changed_at)
                VALUES ($1, $2, $3, $4, $5, $6)
            """, alert.id, alert.state.value, new_state.value, user_id, notes, datetime.now())
    
    def _row_to_alert(self, row) -> SmartAlert:
        """Convert database row to SmartAlert object"""
        return SmartAlert(
            id=str(row['id']),
            alert_type=AlertType(row['event_type']),
            severity=AlertSeverity(row['severity']),
            state=AlertState(row.get('state', 'new')),
            title=row.get('title', ''),
            description=row['description'],
            entity_id=str(row['entity_id']) if row.get('entity_id') else None,
            entity_type=row.get('entity_type'),
            zone_id=str(row['zone_id']) if row.get('zone_id') else None,
            vehicle_id=str(row['vehicle_id']) if row.get('vehicle_id') else None,
            ride_id=str(row['ride_id']) if row.get('ride_id') else None,
            ai_confidence=float(row['ai_confidence']) if row.get('ai_confidence') else None,
            metadata=json.loads(row['metadata']) if row.get('metadata') else None,
            created_at=row.get('time', row.get('created_at')),
            acknowledged_by=row.get('acknowledged_by'),
            acknowledged_at=row.get('acknowledged_at'),
            resolved_by=row.get('resolved_by'),
            resolved_at=row.get('resolved_at'),
            resolution_notes=row.get('resolution_notes'),
            escalation_level=row.get('escalation_level', 0),
            auto_resolve_at=row.get('auto_resolve_at')
        )
    
    def _dict_to_alert(self, data: Dict) -> SmartAlert:
        """Convert dictionary to SmartAlert object"""
        return SmartAlert(**data)

# Background task for processing alerts
async def alert_processor_task(alert_manager: AlertManager):
    """Background task to process alert escalations and auto-resolutions"""
    while True:
        try:
            await alert_manager.process_auto_escalations()
            await alert_manager.process_auto_resolutions()
            await asyncio.sleep(60)  # Check every minute
        except Exception as e:
            logger.error(f"Error in alert processor task: {e}")
            await asyncio.sleep(60)
