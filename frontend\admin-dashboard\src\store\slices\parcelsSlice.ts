import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Parcel, ParcelFilters } from '../../types/api';
import { createParcelFilters } from '../../utils/typeFixUtils';

interface ParcelsState {
  parcels: Parcel[];
  currentParcel: Parcel | null;
  loading: boolean;
  error: string | null;
  totalParcels: number;
  inTransitParcels: number;
  deliveredParcels: number;
}

const initialState: ParcelsState = {
  parcels: [],
  currentParcel: null,
  loading: false,
  error: null,
  totalParcels: 0,
  inTransitParcels: 0,
  deliveredParcels: 0,
};

// Async thunks
export const fetchParcels = createAsyncThunk(
  'parcels/fetchParcels',
  async (params?: ParcelFilters) => {
    const { parcelsService } = await import('../../services/parcelsService');
    const safeParams = createParcelFilters(params);
    const response = await parcelsService.getParcels(safeParams);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب الطرود');
    }

    return response;
  }
);

export const fetchParcelById = createAsyncThunk(
  'parcels/fetchParcelById',
  async (parcelId: string) => {
    const { parcelsService } = await import('../../services/parcelsService');
    const response = await parcelsService.getParcelById(parcelId);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب تفاصيل الطرد');
    }

    return response;
  }
);

export const updateParcelStatus = createAsyncThunk(
  'parcels/updateParcelStatus',
  async ({ parcelId, status }: { parcelId: string; status: string }) => {
    const { parcelsService } = await import('../../services/parcelsService');
    const response = await parcelsService.updateParcelStatus(parcelId, status);

    if (!response.success) {
      throw new Error(response.message || 'فشل في تحديث حالة الطرد');
    }

    return response;
  }
);

const parcelsSlice = createSlice({
  name: 'parcels',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentParcel: (state, action: PayloadAction<Parcel | null>) => {
      state.currentParcel = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchParcels.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchParcels.fulfilled, (state, action) => {
        state.loading = false;
        state.parcels = action.payload.data;
        // استخدام total بدلاً من totalParcels إذا لم تكن متاحة
        state.totalParcels = action.payload.totalParcels || action.payload.total || action.payload.data.length;
        state.inTransitParcels = action.payload.inTransitParcels || 0;
        state.deliveredParcels = action.payload.deliveredParcels || 0;
      })
      .addCase(fetchParcels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب الطرود';
      })
      .addCase(fetchParcelById.fulfilled, (state, action) => {
        state.currentParcel = action.payload.data;
      })
      .addCase(updateParcelStatus.fulfilled, (state, action) => {
        const updatedParcel = action.payload.data;
        const index = state.parcels.findIndex(p => p.id === updatedParcel.id);
        if (index !== -1) {
          state.parcels[index] = updatedParcel;
        }
        if (state.currentParcel?.id === updatedParcel.id) {
          state.currentParcel = updatedParcel;
        }
      });
  },
});

export const { clearError, setCurrentParcel } = parcelsSlice.actions;
export default parcelsSlice.reducer;
