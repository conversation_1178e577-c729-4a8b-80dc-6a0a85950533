import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert,
  Badge,
  LinearProgress,
  CircularProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  DirectionsCar as CarIcon,
  LocalShipping as TripIcon,
  Warning as WarningIcon,
  Emergency as EmergencyIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Visibility as ViewIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Map as MapIcon,
  Notifications as NotificationIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

interface LiveVehicle {
  id: string;
  plateNumber: string;
  driverName: string;
  status: string;
  location: { lat: number; lng: number };
  speed: number;
  fuel: number;
  currentTripId?: string;
  eta?: number;
  totalTripsToday: number;
  earningsToday: number;
}

interface ActiveTrip {
  id: string;
  type: string;
  status: string;
  priority: string;
  driverName?: string;
  customerName: string;
  origin: string;
  destination: string;
  progress: number;
  estimatedArrival: string;
  fare: number;
  isEmergency: boolean;
}

interface Alert {
  id: string;
  type: string;
  severity: string;
  title: string;
  message: string;
  timestamp: string;
  vehicleId?: string;
  isResolved: boolean;
  actionRequired: string;
}

interface SystemMetrics {
  activeVehicles: number;
  totalVehicles: number;
  activeTrips: number;
  completedTripsToday: number;
  averageResponseTime: number;
  systemUptime: number;
  errorRate: number;
  lastUpdated: string;
}

interface OperationalStats {
  totalRevenue: number;
  tripsInProgress: number;
  driversOnline: number;
  customersActive: number;
  averageTripDuration: number;
  customerSatisfaction: number;
  onTimeDeliveryRate: number;
  emergencyIncidents: number;
}

const LiveOperationsDashboard: React.FC = () => {
  const [vehicles, setVehicles] = useState<LiveVehicle[]>([]);
  const [trips, setTrips] = useState<ActiveTrip[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [stats, setStats] = useState<OperationalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadLiveData();
    
    // Set up real-time updates every 10 seconds
    const interval = setInterval(() => {
      loadLiveData();
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const loadLiveData = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8100/api/live/dashboard');
      const data = await response.json();
      
      if (data.success) {
        setVehicles(data.data.vehicles);
        setTrips(data.data.trips);
        setAlerts(data.data.alerts);
        setMetrics(data.data.metrics);
        setStats(data.data.stats);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Failed to load live data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`http://localhost:8100/api/live/alerts/${alertId}/resolve`, {
        method: 'PUT'
      });
      
      if (response.ok) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, isResolved: true } : alert
        ));
      }
    } catch (error) {
      console.error('Failed to resolve alert:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'available': 'success',
      'busy': 'primary',
      'offline': 'default',
      'maintenance': 'warning',
      'assigned': 'info',
      'pickup_in_progress': 'warning',
      'in_transit': 'primary',
      'delivery_in_progress': 'warning',
      'completed': 'success'
    };
    return colors[status] || 'default';
  };

  const getSeverityColor = (severity: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'low': 'info',
      'medium': 'warning',
      'high': 'error',
      'critical': 'error'
    };
    return colors[severity] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#f44336';
      case 'high': return '#ff9800';
      case 'normal': return '#4caf50';
      default: return '#9e9e9e';
    }
  };

  if (loading && !metrics) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل البيانات المباشرة...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" gutterBottom>
              <DashboardIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              لوحة العمليات المباشرة
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              مراقبة العمليات والتنبيهات في الوقت الفعلي
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body2" color="text.secondary">
              آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadLiveData}
              disabled={loading}
            >
              تحديث
            </Button>
          </Box>
        </Box>
      </Box>

      {/* System Status Cards */}
      {metrics && stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CarIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.activeVehicles}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      مركبات نشطة
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      من أصل {metrics.totalVehicles}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TripIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.tripsInProgress}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      رحلات جارية
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      مكتمل اليوم: {metrics.completedTripsToday}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {alerts.filter(a => !a.isResolved).length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      تنبيهات نشطة
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      طوارئ: {stats.emergencyIncidents}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SpeedIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.systemUptime.toFixed(1)}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      وقت تشغيل النظام
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      استجابة: {metrics.averageResponseTime.toFixed(1)} دقيقة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Performance Metrics */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(stats.totalRevenue)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إيرادات اليوم
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PeopleIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.driversOnline}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      سائقون متصلون
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      عملاء نشطون: {stats.customersActive}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <ScheduleIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.averageTripDuration.toFixed(0)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط مدة الرحلة (دقيقة)
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      في الوقت: {stats.onTimeDeliveryRate.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.customerSatisfaction.toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      رضا العملاء (من 5)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs for Different Views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="التنبيهات النشطة" icon={<WarningIcon />} />
          <Tab label="الرحلات الجارية" icon={<TripIcon />} />
          <Tab label="المركبات المباشرة" icon={<CarIcon />} />
          <Tab label="الخريطة التفاعلية" icon={<MapIcon />} />
        </Tabs>
      </Paper>

      {/* Active Alerts Tab */}
      {selectedTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  التنبيهات النشطة
                </Typography>
                <List>
                  {alerts.filter(a => !a.isResolved).slice(0, 10).map((alert) => (
                    <React.Fragment key={alert.id}>
                      <ListItem>
                        <ListItemIcon>
                          <Badge
                            color={getSeverityColor(alert.severity)}
                            variant="dot"
                          >
                            <WarningIcon color={getSeverityColor(alert.severity)} />
                          </Badge>
                        </ListItemIcon>
                        <ListItemText
                          primary={alert.title}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {alert.message}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {new Date(alert.timestamp).toLocaleString('ar-SA')}
                              </Typography>
                              <Box mt={1}>
                                <Chip
                                  label={alert.actionRequired}
                                  size="small"
                                  color="warning"
                                  sx={{ mr: 1 }}
                                />
                                {alert.vehicleId && (
                                  <Chip
                                    label={alert.vehicleId}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            </Box>
                          }
                        />
                        <Box>
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleResolveAlert(alert.id)}
                          >
                            <CheckIcon />
                          </IconButton>
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                        </Box>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  إحصائيات التنبيهات
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">التنبيهات الحرجة</Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={alerts.filter(a => a.severity === 'critical' && !a.isResolved).length * 10}
                    color="error"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption">
                    {alerts.filter(a => a.severity === 'critical' && !a.isResolved).length} تنبيه
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">التنبيهات العالية</Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={alerts.filter(a => a.severity === 'high' && !a.isResolved).length * 5}
                    color="warning"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption">
                    {alerts.filter(a => a.severity === 'high' && !a.isResolved).length} تنبيه
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2">التنبيهات المتوسطة</Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={alerts.filter(a => a.severity === 'medium' && !a.isResolved).length * 2}
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption">
                    {alerts.filter(a => a.severity === 'medium' && !a.isResolved).length} تنبيه
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Active Trips Tab */}
      {selectedTab === 1 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              الرحلات الجارية
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>معرف الرحلة</TableCell>
                    <TableCell>النوع</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>الأولوية</TableCell>
                    <TableCell>العميل</TableCell>
                    <TableCell>المنشأ</TableCell>
                    <TableCell>الوجهة</TableCell>
                    <TableCell>التقدم</TableCell>
                    <TableCell>الوصول المتوقع</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {trips.slice(0, 10).map((trip) => (
                    <TableRow key={trip.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {trip.id}
                        </Typography>
                        {trip.isEmergency && (
                          <Chip label="طوارئ" color="error" size="small" />
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {trip.type === 'passenger_ride' ? 'ركاب' : 
                           trip.type === 'parcel_delivery' ? 'طرد' : 'مختلط'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trip.status}
                          color={getStatusColor(trip.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getPriorityColor(trip.priority),
                            display: 'inline-block',
                            mr: 1
                          }}
                        />
                        {trip.priority}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {trip.customerName}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap>
                          {trip.origin}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap>
                          {trip.destination}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: 100 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={trip.progress}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                          <Typography variant="caption">
                            {trip.progress.toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(trip.estimatedArrival).toLocaleTimeString('ar-SA')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Live Vehicles Tab */}
      {selectedTab === 2 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              المركبات المباشرة
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>رقم اللوحة</TableCell>
                    <TableCell>السائق</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>السرعة</TableCell>
                    <TableCell>الوقود</TableCell>
                    <TableCell>الرحلات اليوم</TableCell>
                    <TableCell>الأرباح اليوم</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {vehicles.slice(0, 15).map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {vehicle.plateNumber}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {vehicle.driverName}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={vehicle.status}
                          color={getStatusColor(vehicle.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {vehicle.speed.toFixed(0)} كم/س
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: 80 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={vehicle.fuel}
                            color={vehicle.fuel < 20 ? 'error' : vehicle.fuel < 50 ? 'warning' : 'success'}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption">
                            {vehicle.fuel.toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {vehicle.totalTripsToday}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="success.main">
                          {formatCurrency(vehicle.earningsToday)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Interactive Map Tab */}
      {selectedTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              الخريطة التفاعلية
            </Typography>
            <Box 
              sx={{ 
                height: 500, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: 'grey.100',
                borderRadius: 1
              }}
            >
              <Typography variant="h6" color="text.secondary">
                خريطة تفاعلية للمركبات والرحلات المباشرة
                <br />
                (يتطلب تكامل مع خدمة الخرائط)
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}
    </Container>
  );
};

export default LiveOperationsDashboard;
