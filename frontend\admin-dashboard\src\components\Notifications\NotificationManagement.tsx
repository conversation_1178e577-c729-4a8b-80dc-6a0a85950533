import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Notifications as NotificationsIcon,
  Send as SendIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  PhoneAndroid as PushIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';

interface NotificationTemplate {
  id: string;
  name: string;
  type: 'EMAIL' | 'SMS' | 'PUSH';
  subject?: string;
  content: string;
  variables: string[];
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NotificationCampaign {
  id: string;
  name: string;
  templateId: string;
  templateName: string;
  type: 'EMAIL' | 'SMS' | 'PUSH';
  status: 'DRAFT' | 'SCHEDULED' | 'SENDING' | 'SENT' | 'FAILED';
  recipients: number;
  sentCount: number;
  failedCount: number;
  scheduledAt?: string;
  sentAt?: string;
  createdAt: string;
}

const NotificationManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [campaigns, setCampaigns] = useState<NotificationCampaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [openTemplateDialog, setOpenTemplateDialog] = useState(false);
  const [openCampaignDialog, setOpenCampaignDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    type: 'EMAIL' as 'EMAIL' | 'SMS' | 'PUSH',
    subject: '',
    content: '',
    variables: [] as string[],
  });
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    templateId: '',
    recipients: 'ALL_USERS',
    scheduledAt: '',
  });

  // Mock data
  const mockTemplates: NotificationTemplate[] = [
    {
      id: 'template-1',
      name: 'رسالة ترحيب',
      type: 'EMAIL',
      subject: 'مرحباً بك في تكنو درايف',
      content: 'مرحباً {{userName}}، نحن سعداء بانضمامك إلى منصة تكنو درايف.',
      variables: ['userName'],
      enabled: true,
      createdAt: '2025-01-15T08:00:00Z',
      updatedAt: '2025-07-09T14:30:00Z',
    },
    {
      id: 'template-2',
      name: 'تأكيد الرحلة',
      type: 'SMS',
      content: 'تم تأكيد رحلتك رقم {{rideId}}. السائق: {{driverName}}، المركبة: {{vehiclePlate}}',
      variables: ['rideId', 'driverName', 'vehiclePlate'],
      enabled: true,
      createdAt: '2025-02-01T09:00:00Z',
      updatedAt: '2025-07-09T14:30:00Z',
    },
    {
      id: 'template-3',
      name: 'وصول السائق',
      type: 'PUSH',
      content: 'السائق {{driverName}} وصل إلى موقعك',
      variables: ['driverName'],
      enabled: true,
      createdAt: '2025-02-15T10:00:00Z',
      updatedAt: '2025-07-09T14:30:00Z',
    },
  ];

  const mockCampaigns: NotificationCampaign[] = [
    {
      id: 'campaign-1',
      name: 'حملة ترويجية - خصم 20%',
      templateId: 'template-1',
      templateName: 'رسالة ترحيب',
      type: 'EMAIL',
      status: 'SENT',
      recipients: 1500,
      sentCount: 1485,
      failedCount: 15,
      sentAt: '2025-07-08T10:00:00Z',
      createdAt: '2025-07-07T15:00:00Z',
    },
    {
      id: 'campaign-2',
      name: 'تذكير بالصيانة',
      templateId: 'template-2',
      templateName: 'تأكيد الرحلة',
      type: 'SMS',
      status: 'SCHEDULED',
      recipients: 50,
      sentCount: 0,
      failedCount: 0,
      scheduledAt: '2025-07-10T09:00:00Z',
      createdAt: '2025-07-09T12:00:00Z',
    },
  ];

  useEffect(() => {
    setTemplates(mockTemplates);
    setCampaigns(mockCampaigns);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getTypeChip = (type: string) => {
    const typeConfig = {
      EMAIL: { label: 'بريد إلكتروني', color: 'primary' as const, icon: <EmailIcon fontSize="small" /> },
      SMS: { label: 'رسالة نصية', color: 'success' as const, icon: <SmsIcon fontSize="small" /> },
      PUSH: { label: 'إشعار فوري', color: 'info' as const, icon: <PushIcon fontSize="small" /> },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      color: 'default' as const, 
      icon: <NotificationsIcon fontSize="small" /> 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      DRAFT: { label: 'مسودة', color: 'default' as const },
      SCHEDULED: { label: 'مجدولة', color: 'warning' as const },
      SENDING: { label: 'جاري الإرسال', color: 'info' as const },
      SENT: { label: 'تم الإرسال', color: 'success' as const },
      FAILED: { label: 'فشل', color: 'error' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const templateColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم القالب',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <NotificationsIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'type',
      headerName: 'النوع',
      width: 150,
      renderCell: (params: GridRenderCellParams) => getTypeChip(params.value),
    },
    {
      field: 'subject',
      headerName: 'الموضوع',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary">
          {params.value || 'لا يوجد موضوع'}
        </Typography>
      ),
    },
    {
      field: 'variables',
      headerName: 'المتغيرات',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {params.value.slice(0, 3).map((variable: string, index: number) => (
            <Chip key={index} label={`{{${variable}}}`} size="small" variant="outlined" sx={{ fontSize: '0.7rem' }} />
          ))}
          {params.value.length > 3 && (
            <Chip label={`+${params.value.length - 3}`} size="small" variant="outlined" sx={{ fontSize: '0.7rem' }} />
          )}
        </Box>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'مفعل' : 'معطل'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="تعديل"><EditIcon /></Tooltip>}
          label="تعديل"
          onClick={() => console.log('Edit template:', params.id)}
        />,
        <GridActionsCellItem
          icon={<Tooltip title="حذف"><DeleteIcon /></Tooltip>}
          label="حذف"
          onClick={() => console.log('Delete template:', params.id)}
        />,
      ],
    },
  ];

  const campaignColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم الحملة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'success.main' }}>
            <SendIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'templateName',
      headerName: 'القالب',
      width: 150,
    },
    {
      field: 'type',
      headerName: 'النوع',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getTypeChip(params.value),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'recipients',
      headerName: 'المستلمين',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'sentCount',
      headerName: 'تم الإرسال',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="success.main">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'failedCount',
      headerName: 'فشل',
      width: 80,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="error.main">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="تعديل"><EditIcon /></Tooltip>}
          label="تعديل"
          onClick={() => console.log('Edit campaign:', params.id)}
        />,
        <GridActionsCellItem
          icon={<Tooltip title="إرسال"><SendIcon /></Tooltip>}
          label="إرسال"
          onClick={() => console.log('Send campaign:', params.id)}
          disabled={params.row.status === 'SENT'}
        />,
      ],
    },
  ];

  const handleCreateTemplate = () => {
    const template: NotificationTemplate = {
      id: `template-${Date.now()}`,
      ...newTemplate,
      variables: newTemplate.content.match(/\{\{(\w+)\}\}/g)?.map(v => v.slice(2, -2)) || [],
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setTemplates(prev => [...prev, template]);
    setOpenTemplateDialog(false);
    setNewTemplate({ name: '', type: 'EMAIL', subject: '', content: '', variables: [] });
    setSnackbarMessage('تم إنشاء القالب بنجاح');
    setSnackbarOpen(true);
  };

  const handleCreateCampaign = () => {
    const selectedTemplate = templates.find(t => t.id === newCampaign.templateId);
    if (!selectedTemplate) return;

    const campaign: NotificationCampaign = {
      id: `campaign-${Date.now()}`,
      name: newCampaign.name,
      templateId: newCampaign.templateId,
      templateName: selectedTemplate.name,
      type: selectedTemplate.type,
      status: newCampaign.scheduledAt ? 'SCHEDULED' : 'DRAFT',
      recipients: 100, // Mock number
      sentCount: 0,
      failedCount: 0,
      scheduledAt: newCampaign.scheduledAt || undefined,
      createdAt: new Date().toISOString(),
    };

    setCampaigns(prev => [...prev, campaign]);
    setOpenCampaignDialog(false);
    setNewCampaign({ name: '', templateId: '', recipients: 'ALL_USERS', scheduledAt: '' });
    setSnackbarMessage('تم إنشاء الحملة بنجاح');
    setSnackbarOpen(true);
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'ALL' || template.type === filterType;
    return matchesSearch && matchesType;
  });

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'ALL' || campaign.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Calculate stats
  const totalTemplates = templates.length;
  const enabledTemplates = templates.filter(t => t.enabled).length;
  const totalCampaigns = campaigns.length;
  const sentCampaigns = campaigns.filter(c => c.status === 'SENT').length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الإشعارات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة قوالب الإشعارات والحملات الإعلانية
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalTemplates}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي القوالب
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {enabledTemplates}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                القوالب المفعلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {totalCampaigns}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الحملات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {sentCampaigns}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الحملات المرسلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<NotificationsIcon />} label="القوالب" iconPosition="start" />
            <Tab icon={<SendIcon />} label="الحملات" iconPosition="start" />
            <Tab icon={<ScheduleIcon />} label="المجدولة" iconPosition="start" />
          </Tabs>
        </Box>

        {/* Templates Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  placeholder="البحث في القوالب..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>النوع</InputLabel>
                  <Select
                    value={filterType}
                    label="النوع"
                    onChange={(e) => setFilterType(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الأنواع</MenuItem>
                    <MenuItem value="EMAIL">بريد إلكتروني</MenuItem>
                    <MenuItem value="SMS">رسالة نصية</MenuItem>
                    <MenuItem value="PUSH">إشعار فوري</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenTemplateDialog(true)}
              >
                إنشاء قالب
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={filteredTemplates}
                columns={templateColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Campaigns Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  placeholder="البحث في الحملات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    label="الحالة"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الحالات</MenuItem>
                    <MenuItem value="DRAFT">مسودة</MenuItem>
                    <MenuItem value="SCHEDULED">مجدولة</MenuItem>
                    <MenuItem value="SENT">تم الإرسال</MenuItem>
                    <MenuItem value="FAILED">فشل</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenCampaignDialog(true)}
              >
                إنشاء حملة
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={filteredCampaigns}
                columns={campaignColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Scheduled Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>الحملات المجدولة</Typography>
            <Alert severity="info">
              قريباً - عرض وإدارة الحملات المجدولة للإرسال
            </Alert>
          </Box>
        )}
      </Card>

      {/* Create Template Dialog */}
      <Dialog open={openTemplateDialog} onClose={() => setOpenTemplateDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إنشاء قالب إشعار جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="اسم القالب"
              value={newTemplate.name}
              onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
              fullWidth
              required
            />
            <FormControl fullWidth required>
              <InputLabel>نوع الإشعار</InputLabel>
              <Select
                value={newTemplate.type}
                label="نوع الإشعار"
                onChange={(e) => setNewTemplate({ ...newTemplate, type: e.target.value as any })}
              >
                <MenuItem value="EMAIL">بريد إلكتروني</MenuItem>
                <MenuItem value="SMS">رسالة نصية</MenuItem>
                <MenuItem value="PUSH">إشعار فوري</MenuItem>
              </Select>
            </FormControl>
            {newTemplate.type === 'EMAIL' && (
              <TextField
                label="موضوع الرسالة"
                value={newTemplate.subject}
                onChange={(e) => setNewTemplate({ ...newTemplate, subject: e.target.value })}
                fullWidth
                required
              />
            )}
            <TextField
              label="محتوى الرسالة"
              value={newTemplate.content}
              onChange={(e) => setNewTemplate({ ...newTemplate, content: e.target.value })}
              fullWidth
              multiline
              rows={4}
              required
              helperText="استخدم {{variableName}} لإدراج المتغيرات"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenTemplateDialog(false)}>إلغاء</Button>
          <Button onClick={handleCreateTemplate} variant="contained">إنشاء</Button>
        </DialogActions>
      </Dialog>

      {/* Create Campaign Dialog */}
      <Dialog open={openCampaignDialog} onClose={() => setOpenCampaignDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إنشاء حملة إشعارات جديدة</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="اسم الحملة"
              value={newCampaign.name}
              onChange={(e) => setNewCampaign({ ...newCampaign, name: e.target.value })}
              fullWidth
              required
            />
            <FormControl fullWidth required>
              <InputLabel>القالب</InputLabel>
              <Select
                value={newCampaign.templateId}
                label="القالب"
                onChange={(e) => setNewCampaign({ ...newCampaign, templateId: e.target.value })}
              >
                {templates.filter(t => t.enabled).map((template) => (
                  <MenuItem key={template.id} value={template.id}>
                    {template.name} ({template.type})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>المستلمين</InputLabel>
              <Select
                value={newCampaign.recipients}
                label="المستلمين"
                onChange={(e) => setNewCampaign({ ...newCampaign, recipients: e.target.value })}
              >
                <MenuItem value="ALL_USERS">جميع المستخدمين</MenuItem>
                <MenuItem value="ACTIVE_USERS">المستخدمين النشطين</MenuItem>
                <MenuItem value="DRIVERS">السائقين</MenuItem>
                <MenuItem value="PASSENGERS">الركاب</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="موعد الإرسال (اختياري)"
              type="datetime-local"
              value={newCampaign.scheduledAt}
              onChange={(e) => setNewCampaign({ ...newCampaign, scheduledAt: e.target.value })}
              fullWidth
              InputLabelProps={{ shrink: true }}
              helperText="اتركه فارغاً للإرسال الفوري"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCampaignDialog(false)}>إلغاء</Button>
          <Button onClick={handleCreateCampaign} variant="contained">إنشاء</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default NotificationManagement;
