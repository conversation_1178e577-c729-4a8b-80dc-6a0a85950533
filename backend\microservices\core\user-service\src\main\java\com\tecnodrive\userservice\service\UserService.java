package com.tecnodrive.userservice.service;

import com.tecnodrive.userservice.dto.UserRequest;
import com.tecnodrive.userservice.dto.UserResponse;
import com.tecnodrive.userservice.entity.User;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.UUID;

/**
 * User Service Interface
 */
public interface UserService {

    /**
     * Create a new user
     */
    UserResponse createUser(UserRequest.Create request);

    /**
     * Get user by ID
     */
    UserResponse getUserById(UUID userId);

    /**
     * Get user by email
     */
    UserResponse getUserByEmail(String email);

    /**
     * Get user by phone number
     */
    UserResponse getUserByPhoneNumber(String phoneNumber);

    /**
     * Update user
     */
    UserResponse updateUser(UUID userId, UserRequest.Update request);

    /**
     * Update user status
     */
    UserResponse updateUserStatus(UUID userId, UserRequest.StatusUpdate request);

    /**
     * Update notification preferences
     */
    UserResponse updateNotificationPreferences(UUID userId, UserRequest.NotificationPreferences request);

    /**
     * Delete user (soft delete)
     */
    void deleteUser(UUID userId);

    /**
     * Search users with filters
     */
    Page<UserResponse.Summary> searchUsers(UserRequest.Search request);

    /**
     * Get users by status
     */
    List<UserResponse.Summary> getUsersByStatus(User.UserStatus status);

    /**
     * Get users by type
     */
    List<UserResponse.Summary> getUsersByType(User.UserType userType);

    /**
     * Get users by company
     */
    List<UserResponse.Summary> getUsersByCompany(UUID companyId);

    /**
     * Get public profile
     */
    UserResponse.PublicProfile getPublicProfile(UUID userId);

    /**
     * Verify email
     */
    UserResponse verifyEmail(UUID userId);

    /**
     * Verify phone
     */
    UserResponse verifyPhone(UUID userId);

    /**
     * Update last login
     */
    void updateLastLogin(UUID userId);

    /**
     * Check if email exists
     */
    boolean emailExists(String email);

    /**
     * Check if phone exists
     */
    boolean phoneExists(String phoneNumber);

    /**
     * Get user statistics
     */
    UserResponse.Statistics getUserStatistics();

    /**
     * Get inactive users
     */
    List<UserResponse.Activity> getInactiveUsers(int daysSinceLastLogin);

    /**
     * Get users for notification
     */
    List<UserResponse.Summary> getUsersForEmailNotification();

    /**
     * Get users for SMS notification
     */
    List<UserResponse.Summary> getUsersForSmsNotification();

    /**
     * Get users for push notification
     */
    List<UserResponse.Summary> getUsersForPushNotification();

    /**
     * Bulk update user status
     */
    void bulkUpdateUserStatus(List<UUID> userIds, User.UserStatus status, String reason);

    /**
     * Export users data
     */
    List<UserResponse> exportUsers(UserRequest.Search filters);

    /**
     * Get users created in date range
     */
    List<UserResponse.Summary> getUsersCreatedBetween(java.time.Instant startDate, java.time.Instant endDate);
}
