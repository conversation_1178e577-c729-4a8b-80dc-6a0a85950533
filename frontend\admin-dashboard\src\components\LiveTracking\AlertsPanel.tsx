import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Badge,
  Box,
  Divider,
  Collapse,
  Alert,
  Button,
  Tooltip
} from '@mui/material';
import {
  Warning,
  Error,
  Info,
  CheckCircle,
  ExpandMore,
  ExpandLess,
  Clear,
  Refresh,
  NotificationsActive,
  Route,
  Schedule,
  LocationOff,
  Speed,
  BatteryAlert
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

// Alert types and interfaces
export type AlertSeverity = 'critical' | 'warning' | 'info' | 'success';
export type AlertType = 'delay' | 'off_route' | 'vehicle_breakdown' | 'low_battery' | 'speeding' | 'no_signal' | 'delivery_failed';

export interface AlertItem {
  id: string;
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  description: string;
  entityId: string; // Vehicle ID, Parcel ID, etc.
  entityType: 'vehicle' | 'parcel' | 'driver';
  location?: { lat: number; lng: number };
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
}

interface AlertsPanelProps {
  alerts: AlertItem[];
  onAlertClick?: (alert: AlertItem) => void;
  onAlertAcknowledge?: (alertId: string) => void;
  onAlertResolve?: (alertId: string) => void;
  onRefresh?: () => void;
  maxHeight?: number;
}

// Alert icon mapping
const getAlertIcon = (type: AlertType) => {
  switch (type) {
    case 'delay':
      return <Schedule />;
    case 'off_route':
      return <Route />;
    case 'vehicle_breakdown':
      return <Error />;
    case 'low_battery':
      return <BatteryAlert />;
    case 'speeding':
      return <Speed />;
    case 'no_signal':
      return <LocationOff />;
    case 'delivery_failed':
      return <Warning />;
    default:
      return <Info />;
  }
};

// Alert color mapping
const getAlertColor = (severity: AlertSeverity) => {
  switch (severity) {
    case 'critical':
      return '#f44336';
    case 'warning':
      return '#ff9800';
    case 'info':
      return '#2196f3';
    case 'success':
      return '#4caf50';
    default:
      return '#757575';
  }
};

// Individual alert item component
const AlertListItem: React.FC<{
  alert: AlertItem;
  onAlertClick?: (alert: AlertItem) => void;
  onAlertAcknowledge?: (alertId: string) => void;
  onAlertResolve?: (alertId: string) => void;
}> = ({ alert, onAlertClick, onAlertAcknowledge, onAlertResolve }) => {
  const [expanded, setExpanded] = useState(false);

  const handleClick = () => {
    setExpanded(!expanded);
    onAlertClick?.(alert);
  };

  const handleAcknowledge = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAlertAcknowledge?.(alert.id);
  };

  const handleResolve = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAlertResolve?.(alert.id);
  };

  return (
    <>
      <ListItem
        button
        onClick={handleClick}
        sx={{
          borderLeft: `4px solid ${getAlertColor(alert.severity)}`,
          mb: 1,
          backgroundColor: alert.acknowledged ? 'rgba(0,0,0,0.02)' : 'inherit',
          opacity: alert.resolved ? 0.6 : 1
        }}
      >
        <ListItemIcon sx={{ color: getAlertColor(alert.severity) }}>
          {getAlertIcon(alert.type)}
        </ListItemIcon>
        
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: alert.acknowledged ? 'normal' : 'bold' }}>
                {alert.title}
              </Typography>
              <Chip
                label={alert.severity}
                size="small"
                sx={{
                  backgroundColor: getAlertColor(alert.severity),
                  color: 'white',
                  fontSize: '0.7rem',
                  height: 20
                }}
              />
              {alert.acknowledged && (
                <Chip label="Acknowledged" size="small" color="default" sx={{ fontSize: '0.7rem', height: 20 }} />
              )}
              {alert.resolved && (
                <Chip label="Resolved" size="small" color="success" sx={{ fontSize: '0.7rem', height: 20 }} />
              )}
            </Box>
          }
          secondary={
            <Box>
              <Typography variant="body2" color="text.secondary">
                {alert.entityType}: {alert.entityId}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatDistanceToNow(new Date(alert.timestamp), { addSuffix: true })}
              </Typography>
            </Box>
          }
        />
        
        <ListItemSecondaryAction>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {!alert.acknowledged && (
              <Tooltip title="Acknowledge">
                <IconButton size="small" onClick={handleAcknowledge}>
                  <CheckCircle />
                </IconButton>
              </Tooltip>
            )}
            {alert.acknowledged && !alert.resolved && (
              <Tooltip title="Mark as Resolved">
                <IconButton size="small" onClick={handleResolve}>
                  <Clear />
                </IconButton>
              </Tooltip>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          </Box>
        </ListItemSecondaryAction>
      </ListItem>
      
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box sx={{ pl: 4, pr: 2, pb: 2 }}>
          <Typography variant="body2" color="text.secondary" paragraph>
            {alert.description}
          </Typography>
          {alert.location && (
            <Typography variant="caption" color="text.secondary">
              Location: {alert.location.lat.toFixed(6)}, {alert.location.lng.toFixed(6)}
            </Typography>
          )}
        </Box>
      </Collapse>
      <Divider />
    </>
  );
};

// Main alerts panel component
const AlertsPanel: React.FC<AlertsPanelProps> = ({
  alerts = [],
  onAlertClick,
  onAlertAcknowledge,
  onAlertResolve,
  onRefresh,
  maxHeight = 600
}) => {
  const [filter, setFilter] = useState<'all' | 'unacknowledged' | 'critical'>('all');

  // Filter alerts based on current filter
  const filteredAlerts = alerts.filter(alert => {
    switch (filter) {
      case 'unacknowledged':
        return !alert.acknowledged && !alert.resolved;
      case 'critical':
        return alert.severity === 'critical' && !alert.resolved;
      default:
        return !alert.resolved;
    }
  });

  // Count alerts by severity
  const alertCounts = {
    critical: alerts.filter(a => a.severity === 'critical' && !a.resolved).length,
    warning: alerts.filter(a => a.severity === 'warning' && !a.resolved).length,
    unacknowledged: alerts.filter(a => !a.acknowledged && !a.resolved).length,
    total: alerts.filter(a => !a.resolved).length
  };

  return (
    <Paper elevation={3} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationsActive />
            Live Alerts
            {alertCounts.total > 0 && (
              <Badge badgeContent={alertCounts.total} color="error" />
            )}
          </Typography>
          <Tooltip title="Refresh Alerts">
            <IconButton onClick={onRefresh} size="small">
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Alert summary */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          <Chip
            label={`Critical: ${alertCounts.critical}`}
            size="small"
            color={alertCounts.critical > 0 ? 'error' : 'default'}
            onClick={() => setFilter('critical')}
            variant={filter === 'critical' ? 'filled' : 'outlined'}
          />
          <Chip
            label={`Unacknowledged: ${alertCounts.unacknowledged}`}
            size="small"
            color={alertCounts.unacknowledged > 0 ? 'warning' : 'default'}
            onClick={() => setFilter('unacknowledged')}
            variant={filter === 'unacknowledged' ? 'filled' : 'outlined'}
          />
          <Chip
            label={`All: ${alertCounts.total}`}
            size="small"
            color="primary"
            onClick={() => setFilter('all')}
            variant={filter === 'all' ? 'filled' : 'outlined'}
          />
        </Box>

        {/* Quick actions */}
        {alertCounts.unacknowledged > 0 && (
          <Alert severity="warning" sx={{ mb: 1 }}>
            You have {alertCounts.unacknowledged} unacknowledged alerts requiring attention.
          </Alert>
        )}
      </Box>

      {/* Alerts list */}
      <Box sx={{ flex: 1, overflow: 'auto', maxHeight }}>
        {filteredAlerts.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <CheckCircle sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              No alerts to display
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {filter === 'all' ? 'All systems are running normally' : 
               filter === 'critical' ? 'No critical alerts' : 
               'All alerts have been acknowledged'}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 1 }}>
            {filteredAlerts.map((alert) => (
              <AlertListItem
                key={alert.id}
                alert={alert}
                onAlertClick={onAlertClick}
                onAlertAcknowledge={onAlertAcknowledge}
                onAlertResolve={onAlertResolve}
              />
            ))}
          </List>
        )}
      </Box>

      {/* Footer with actions */}
      {filteredAlerts.length > 0 && (
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between' }}>
            <Typography variant="caption" color="text.secondary">
              Showing {filteredAlerts.length} of {alertCounts.total} alerts
            </Typography>
            {alertCounts.unacknowledged > 0 && (
              <Button
                size="small"
                variant="outlined"
                onClick={() => {
                  alerts
                    .filter(a => !a.acknowledged && !a.resolved)
                    .forEach(a => onAlertAcknowledge?.(a.id));
                }}
              >
                Acknowledge All
              </Button>
            )}
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default AlertsPanel;
export type { AlertsPanelProps };
