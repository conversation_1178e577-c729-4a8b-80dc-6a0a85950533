package com.tecnodrive.saasservice.service.impl;

import com.tecnodrive.saasservice.dto.TenantRequest;
import com.tecnodrive.saasservice.dto.TenantResponse;
import com.tecnodrive.saasservice.dto.TenantUpdateRequest;
import com.tecnodrive.saasservice.entity.Tenant;
import com.tecnodrive.saasservice.exception.TenantNotFoundException;
import com.tecnodrive.saasservice.exception.TenantAlreadyExistsException;
import com.tecnodrive.saasservice.mapper.TenantMapper;
import com.tecnodrive.saasservice.repository.TenantRepository;
import com.tecnodrive.saasservice.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Tenant Service Implementation
 * 
 * Implements business logic for tenant management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TenantServiceImpl implements TenantService {

    private final TenantRepository tenantRepository;
    private final TenantMapper tenantMapper;

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse createTenant(TenantRequest request) {
        log.info("Creating tenant: {}", request.getName());

        // Check if tenant name already exists
        if (tenantRepository.existsByNameIgnoreCase(request.getName())) {
            throw new TenantAlreadyExistsException("Tenant with name '" + request.getName() + "' already exists");
        }

        Tenant tenant = tenantMapper.toEntity(request);
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);

        // Set default subscription dates if not provided
        if (tenant.getSubscriptionStartDate() == null) {
            tenant.setSubscriptionStartDate(Instant.now());
        }
        if (tenant.getSubscriptionEndDate() == null) {
            // Default to 1 year subscription
            tenant.setSubscriptionEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        }

        Tenant savedTenant = tenantRepository.save(tenant);
        
        log.info("Tenant created with ID: {}", savedTenant.getId());
        return tenantMapper.toResponse(savedTenant);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "tenants", key = "'all'")
    public List<TenantResponse> getAllTenants() {
        log.debug("Retrieving all tenants");
        
        List<Tenant> tenants = tenantRepository.findAll();
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantResponse> getTenants(Pageable pageable) {
        log.debug("Retrieving tenants with pagination: {}", pageable);
        
        Page<Tenant> tenants = tenantRepository.findAll(pageable);
        return tenants.map(tenantMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "tenants", key = "#id")
    public TenantResponse getTenant(String id) {
        log.debug("Retrieving tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        return tenantMapper.toResponse(tenant);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "tenants", key = "'name:' + #name")
    public TenantResponse getTenantByName(String name) {
        log.debug("Retrieving tenant with name: {}", name);
        
        Tenant tenant = tenantRepository.findByNameIgnoreCase(name)
                .orElseThrow(() -> new TenantNotFoundException("Tenant not found with name: " + name));
        
        return tenantMapper.toResponse(tenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse updateTenant(String id, TenantUpdateRequest request) {
        log.info("Updating tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenantMapper.updateEntity(tenant, request);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Tenant updated: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public void deleteTenant(String id) {
        log.info("Deleting tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenantRepository.delete(tenant);
        
        log.info("Tenant deleted: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TenantResponse> getTenantsByType(Tenant.TenantType type) {
        log.debug("Retrieving tenants by type: {}", type);
        
        List<Tenant> tenants = tenantRepository.findByType(type);
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TenantResponse> getTenantsByStatus(Tenant.TenantStatus status) {
        log.debug("Retrieving tenants by status: {}", status);
        
        List<Tenant> tenants = tenantRepository.findByStatus(status);
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "tenants", key = "'active'")
    public List<TenantResponse> getActiveTenants() {
        log.debug("Retrieving active tenants");
        
        List<Tenant> tenants = tenantRepository.findActiveTenants();
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantResponse> searchTenants(String searchTerm, Pageable pageable) {
        log.debug("Searching tenants with term: {}", searchTerm);
        
        Page<Tenant> tenants = tenantRepository.searchTenants(searchTerm, pageable);
        return tenants.map(tenantMapper::toResponse);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse activateTenant(String id) {
        log.info("Activating tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Tenant activated: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse deactivateTenant(String id) {
        log.info("Deactivating tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenant.setStatus(Tenant.TenantStatus.INACTIVE);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Tenant deactivated: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse suspendTenant(String id) {
        log.info("Suspending tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenant.setStatus(Tenant.TenantStatus.SUSPENDED);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Tenant suspended: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse extendSubscription(String id, Instant newEndDate) {
        log.info("Extending subscription for tenant with ID: {} until {}", id, newEndDate);
        
        Tenant tenant = findTenantById(id);
        tenant.setSubscriptionEndDate(newEndDate);
        
        // Reactivate if expired
        if (tenant.getStatus() == Tenant.TenantStatus.EXPIRED) {
            tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        }
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Subscription extended for tenant: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TenantResponse> getTenantsWithExpiringSubscriptions(int daysAhead) {
        log.debug("Retrieving tenants with subscriptions expiring in {} days", daysAhead);
        
        Instant now = Instant.now();
        Instant futureDate = now.plus(daysAhead, ChronoUnit.DAYS);
        
        List<Tenant> tenants = tenantRepository.findTenantsWithExpiringSubscriptions(now, futureDate);
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TenantResponse> getExpiredTenants() {
        log.debug("Retrieving expired tenants");
        
        List<Tenant> tenants = tenantRepository.findExpiredTenants(Instant.now());
        return tenantMapper.toResponseList(tenants);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse updateTenantBranding(String id, String brandingConfig) {
        log.info("Updating branding for tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenant.setBrandingConfig(brandingConfig);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Branding updated for tenant: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @CacheEvict(value = "tenants", allEntries = true)
    public TenantResponse updateTenantFeatures(String id, String featureFlags) {
        log.info("Updating features for tenant with ID: {}", id);
        
        Tenant tenant = findTenantById(id);
        tenant.setFeatureFlags(featureFlags);
        
        Tenant updatedTenant = tenantRepository.save(tenant);
        
        log.info("Features updated for tenant: {}", updatedTenant.getId());
        return tenantMapper.toResponse(updatedTenant);
    }

    @Override
    @Transactional(readOnly = true)
    public TenantAnalytics getTenantAnalytics() {
        log.debug("Generating tenant analytics");
        
        long totalTenants = tenantRepository.count();
        long activeTenants = tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE);
        long inactiveTenants = tenantRepository.countByStatus(Tenant.TenantStatus.INACTIVE);
        long suspendedTenants = tenantRepository.countByStatus(Tenant.TenantStatus.SUSPENDED);
        long trialTenants = tenantRepository.countByStatus(Tenant.TenantStatus.TRIAL);
        long expiredTenants = tenantRepository.countByStatus(Tenant.TenantStatus.EXPIRED);
        
        // Get tenant counts by type
        Map<String, Long> tenantsByType = new HashMap<>();
        List<Object[]> typeResults = tenantRepository.getTenantCountByType();
        for (Object[] result : typeResults) {
            tenantsByType.put(result[0].toString(), (Long) result[1]);
        }
        
        // Get tenant counts by service type
        Map<String, Long> tenantsByServiceType = new HashMap<>();
        for (Tenant.ServiceType serviceType : Tenant.ServiceType.values()) {
            long count = tenantRepository.findByServiceType(serviceType).size();
            tenantsByServiceType.put(serviceType.toString(), count);
        }
        
        // Calculate monthly statistics
        Instant startOfMonth = Instant.now().truncatedTo(ChronoUnit.DAYS).minus(30, ChronoUnit.DAYS);
        Instant endOfMonth = Instant.now();
        
        long tenantsCreatedThisMonth = tenantRepository.findTenantsCreatedBetween(startOfMonth, endOfMonth).size();
        long tenantsExpiringThisMonth = tenantRepository.findTenantsWithExpiringSubscriptions(Instant.now(), endOfMonth).size();
        
        return new TenantAnalytics(
                totalTenants,
                activeTenants,
                inactiveTenants,
                suspendedTenants,
                trialTenants,
                expiredTenants,
                tenantsByType,
                tenantsByServiceType,
                tenantsCreatedThisMonth,
                tenantsExpiringThisMonth
        );
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isTenantNameAvailable(String name) {
        return !tenantRepository.existsByNameIgnoreCase(name);
    }

    private Tenant findTenantById(String id) {
        try {
            UUID uuid = UUID.fromString(id);
            return tenantRepository.findById(uuid)
                    .orElseThrow(() -> new TenantNotFoundException("Tenant not found with ID: " + id));
        } catch (IllegalArgumentException e) {
            throw new TenantNotFoundException("Invalid tenant ID format: " + id);
        }
    }
}
