import { io, Socket } from 'socket.io-client';
import { smartApiService } from './smartApiService';

// Enhanced WebSocket Configuration with Backend Integration
const WS_CONFIG = {
  BASE_URL: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080',
  RECONNECTION_ATTEMPTS: 5,
  RECONNECTION_DELAY: 1000,
};

// Event types
export interface LocationUpdate {
  lat: number;
  lng: number;
  timestamp: string;
  speed?: number;
  heading?: number;
}

export interface VehicleLocationUpdate {
  vehicleId: string;
  driverId: string;
  driverName: string;
  vehicleType: 'passenger' | 'delivery';
  status: 'available' | 'busy' | 'offline';
  location: LocationUpdate;
  batteryLevel?: number;
  fuelLevel?: number;
}

export interface RideUpdate {
  rideId: string;
  status: string;
  driverLocation?: LocationUpdate;
  eta?: number;
  message?: string;
}

export interface ParcelUpdate {
  trackingNumber: string;
  status: string;
  location?: LocationUpdate;
  message?: string;
  estimatedDelivery?: string;
  recipientName?: string;
}

export interface AlertUpdate {
  id: string;
  type: 'delay' | 'off_route' | 'vehicle_breakdown' | 'low_battery' | 'speeding' | 'no_signal' | 'delivery_failed';
  severity: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  description: string;
  entityId: string;
  entityType: 'vehicle' | 'parcel' | 'driver';
  location?: { lat: number; lng: number };
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
}

export interface NotificationUpdate {
  id: string;
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  title: string;
  message: string;
  timestamp: string;
  userId?: string;
}

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private isConnected = false;

  // Initialize WebSocket connection
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(WS_CONFIG.BASE_URL, {
          transports: ['websocket'],
          autoConnect: true,
          reconnection: true,
          reconnectionAttempts: WS_CONFIG.RECONNECTION_ATTEMPTS,
          reconnectionDelay: WS_CONFIG.RECONNECTION_DELAY,
        });

        this.socket.on('connect', () => {
          console.log('✅ WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket.on('disconnect', (reason) => {
          console.log('❌ WebSocket disconnected:', reason);
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ WebSocket connection error:', error);
          this.reconnectAttempts++;
          
          if (this.reconnectAttempts >= WS_CONFIG.RECONNECTION_ATTEMPTS) {
            reject(new Error('Failed to connect to WebSocket after multiple attempts'));
          }
        });

        this.socket.on('reconnect', (attemptNumber) => {
          console.log(`🔄 WebSocket reconnected after ${attemptNumber} attempts`);
          this.isConnected = true;
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  // Disconnect WebSocket
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Check if connected
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Subscribe to ride updates
  subscribeToRideUpdates(rideId: string, callback: (update: RideUpdate) => void): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    const channel = `ride:${rideId}`;
    this.socket.emit('join', channel);
    this.socket.on(`ride_update:${rideId}`, callback);

    console.log(`📡 Subscribed to ride updates: ${rideId}`);
  }

  // Unsubscribe from ride updates
  unsubscribeFromRideUpdates(rideId: string): void {
    if (!this.socket) return;

    const channel = `ride:${rideId}`;
    this.socket.emit('leave', channel);
    this.socket.off(`ride_update:${rideId}`);

    console.log(`📡 Unsubscribed from ride updates: ${rideId}`);
  }

  // Subscribe to parcel updates
  subscribeToParcelUpdates(trackingNumber: string, callback: (update: ParcelUpdate) => void): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    const channel = `parcel:${trackingNumber}`;
    this.socket.emit('join', channel);
    this.socket.on(`parcel_update:${trackingNumber}`, callback);

    console.log(`📦 Subscribed to parcel updates: ${trackingNumber}`);
  }

  // Unsubscribe from parcel updates
  unsubscribeFromParcelUpdates(trackingNumber: string): void {
    if (!this.socket) return;

    const channel = `parcel:${trackingNumber}`;
    this.socket.emit('leave', channel);
    this.socket.off(`parcel_update:${trackingNumber}`);

    console.log(`📦 Unsubscribed from parcel updates: ${trackingNumber}`);
  }

  // Subscribe to fleet location updates
  subscribeToFleetUpdates(callback: (updates: VehicleLocationUpdate[]) => void): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    this.socket.emit('join', 'fleet:locations');
    this.socket.on('fleet_location_update', callback);

    console.log('🚗 Subscribed to fleet location updates');
  }

  // Unsubscribe from fleet updates
  unsubscribeFromFleetUpdates(): void {
    if (!this.socket) return;

    this.socket.emit('leave', 'fleet:locations');
    this.socket.off('fleet_location_update');

    console.log('🚗 Unsubscribed from fleet location updates');
  }

  // Subscribe to live alerts
  subscribeToLiveAlerts(callback: (alert: AlertUpdate) => void): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    this.socket.emit('join', 'alerts:live');
    this.socket.on('alert_update', callback);

    console.log('🚨 Subscribed to live alerts');
  }

  // Unsubscribe from live alerts
  unsubscribeFromLiveAlerts(): void {
    if (!this.socket) return;

    this.socket.emit('leave', 'alerts:live');
    this.socket.off('alert_update');

    console.log('🚨 Unsubscribed from live alerts');
  }

  // Subscribe to all live tracking data (vehicles + parcels + alerts)
  subscribeToLiveTracking(callbacks: {
    onVehicleUpdate?: (updates: VehicleLocationUpdate[]) => void;
    onParcelUpdate?: (update: ParcelUpdate) => void;
    onAlertUpdate?: (alert: AlertUpdate) => void;
  }): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    // Join live tracking room
    this.socket.emit('join', 'live:tracking');

    // Set up event listeners
    if (callbacks.onVehicleUpdate) {
      this.socket.on('live_vehicle_updates', callbacks.onVehicleUpdate);
    }

    if (callbacks.onParcelUpdate) {
      this.socket.on('live_parcel_update', callbacks.onParcelUpdate);
    }

    if (callbacks.onAlertUpdate) {
      this.socket.on('live_alert_update', callbacks.onAlertUpdate);
    }

    console.log('📍 Subscribed to live tracking updates');
  }

  // Unsubscribe from all live tracking
  unsubscribeFromLiveTracking(): void {
    if (!this.socket) return;

    this.socket.emit('leave', 'live:tracking');
    this.socket.off('live_vehicle_updates');
    this.socket.off('live_parcel_update');
    this.socket.off('live_alert_update');

    console.log('📍 Unsubscribed from live tracking updates');
  }

  // Subscribe to notifications
  subscribeToNotifications(userId: string, callback: (notification: NotificationUpdate) => void): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    const channel = `notifications:${userId}`;
    this.socket.emit('join', channel);
    this.socket.on(`notification:${userId}`, callback);

    console.log(`🔔 Subscribed to notifications: ${userId}`);
  }

  // Unsubscribe from notifications
  unsubscribeFromNotifications(userId: string): void {
    if (!this.socket) return;

    const channel = `notifications:${userId}`;
    this.socket.emit('leave', channel);
    this.socket.off(`notification:${userId}`);

    console.log(`🔔 Unsubscribed from notifications: ${userId}`);
  }

  // Send location update (for drivers/delivery personnel)
  sendLocationUpdate(vehicleId: string, location: LocationUpdate): void {
    if (!this.socket) {
      console.warn('WebSocket not connected');
      return;
    }

    this.socket.emit('location_update', {
      vehicleId,
      ...location,
    });
  }

  // Generic event listener
  on(event: string, callback: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // Generic event emitter
  emit(event: string, data: any): void {
    if (this.socket) {
      this.socket.emit(event, data);
    }
  }

  // Remove event listener
  off(event: string, callback?: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();

// Auto-connect when token is available
export const initializeWebSocket = async (): Promise<void> => {
  const token = localStorage.getItem('token');
  
  if (token && !websocketService.isSocketConnected()) {
    try {
      await websocketService.connect();
      console.log('🚀 WebSocket service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
    }
  }
};

// Cleanup on logout
export const cleanupWebSocket = (): void => {
  websocketService.disconnect();
  console.log('🧹 WebSocket service cleaned up');
};

export default websocketService;
