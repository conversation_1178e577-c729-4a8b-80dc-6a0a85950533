server:
  port: 8081

spring:
  application:
    name: auth-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:postgres}:${DB_PORT:5432}/tecnodrive_auth
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10 # تقليل حجم المجمع
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 10000 # تقليل timeout
      leak-detection-threshold: 30000
      max-lifetime: 900000
      validation-timeout: 5000

  jpa:
    hibernate:
      ddl-auto: validate # تغيير من update إلى validate لتجنب مشاكل المخطط
    show-sql: false
    open-in-view: false # إصلاح تحذير Open-in-View
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          time_zone: UTC
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  redis:
    host: ${REDIS_HOST:redis} # تغيير من localhost إلى redis
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:} # إزالة كلمة المرور للتطوير
    timeout: 5000ms # زيادة timeout
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 2
        max-wait: 2000ms # إضافة timeout للانتظار

eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus

# Enhanced Security Configuration
security:
  # JWT Configuration
  jwt:
    secret: ${JWT_SECRET:TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2024}
    access-token-expiration: ${JWT_ACCESS_EXPIRATION:********}  # 24 hours
    refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days
    issuer: ${JWT_ISSUER:tecnodrive-platform}
    audience: ${JWT_AUDIENCE:tecnodrive-users}
    algorithm: HS512

    # JWT Revocation (Blacklist) Configuration
    revocation:
      enabled: true
      redis-key-prefix: "jwt:revoked:"
      cleanup-interval: 3600000 # 1 hour

  # Password Policy Configuration
  password:
    min-length: 12
    require-uppercase: true
    require-lowercase: true
    require-digits: true
    require-special-chars: true
    special-chars: "!@#$%^&*()_+-=[]{}|;:,.<>?"
    max-attempts: 5
    lockout-duration: 900000 # 15 minutes
    history-size: 5 # Remember last 5 passwords

  # Two-Factor Authentication Configuration
  two-factor:
    enabled: true
    issuer: "TecnoDrive Platform"
    totp:
      enabled: true
      window: 1
      period: 30
      digits: 6
    sms:
      enabled: true
      provider: twilio
      template: "Your TecnoDrive verification code is: {code}"
      expiration: 300000 # 5 minutes

  # Account Security Configuration
  account:
    max-login-attempts: 5
    lockout-duration: 900000 # 15 minutes
    session-timeout: 1800000 # 30 minutes
    concurrent-sessions: 3

    # Account verification
    email-verification:
      enabled: true
      token-expiration: ******** # 24 hours

    # Password reset
    password-reset:
      enabled: true
      token-expiration: 3600000 # 1 hour
      max-attempts-per-day: 3

  # Rate Limiting Configuration
  rate-limiting:
    login:
      requests-per-minute: 5
      burst-capacity: 10
    register:
      requests-per-minute: 2
      burst-capacity: 5
    password-reset:
      requests-per-hour: 3
      burst-capacity: 5

  # Encryption Configuration
  encryption:
    algorithm: AES-256-GCM
    key-derivation: PBKDF2
    iterations: 100000
    salt-length: 32

# Resilience4j Configuration for Auth Service
resilience4j:
  circuitbreaker:
    instances:
      database:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50

      redis:
        registerHealthIndicator: true
        slidingWindowSize: 15
        minimumNumberOfCalls: 8
        permittedNumberOfCallsInHalfOpenState: 4
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 3s
        failureRateThreshold: 40

      notification-service:
        registerHealthIndicator: true
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 60

  retry:
    instances:
      database:
        maxAttempts: 3
        waitDuration: 1s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2

      redis:
        maxAttempts: 3
        waitDuration: 500ms
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2

# Monitoring and Observability
monitoring:
  security-events:
    enabled: true
    log-failed-logins: true
    log-successful-logins: false
    log-password-changes: true
    log-account-lockouts: true
    log-2fa-events: true

  metrics:
    enabled: true
    custom-metrics:
      - login-attempts
      - failed-logins
      - account-lockouts
      - password-resets
      - 2fa-activations

# Logging Configuration
logging:
  level:
    com.tecnodrive.authservice: DEBUG
    org.springframework.security: INFO
    io.github.resilience4j: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] [%thread] %-5level %logger{36} - %msg%n"
