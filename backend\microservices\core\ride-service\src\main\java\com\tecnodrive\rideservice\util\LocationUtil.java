package com.tecnodrive.rideservice.util;

/**
 * Location Utility for distance and duration calculations
 */
public class LocationUtil {

    private static final double EARTH_RADIUS_KM = 6371.0;
    private static final double AVERAGE_SPEED_KMH = 30.0; // Average city speed

    /**
     * Calculate distance between two points using Haversine formula
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return EARTH_RADIUS_KM * c;
    }

    /**
     * Estimate duration based on distance
     */
    public static int estimateDuration(double distanceKm) {
        return (int) Math.ceil((distanceKm / AVERAGE_SPEED_KMH) * 60); // Convert to minutes
    }

    /**
     * Check if point is within radius of center
     */
    public static boolean isWithinRadius(double centerLat, double centerLon, 
                                       double pointLat, double pointLon, double radiusKm) {
        double distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);
        return distance <= radiusKm;
    }
}
