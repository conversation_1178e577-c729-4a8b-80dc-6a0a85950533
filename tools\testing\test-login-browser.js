// Test Login in Browser Console
// Copy and paste this code in browser console (F12)

console.log('🧪 Testing Login with API Gateway');
console.log('=================================');

// Test login function
async function testLogin() {
    const loginData = {
        email: '<EMAIL>',
        password: 'password123'
    };
    
    console.log('📝 Login Data:', loginData);
    
    try {
        console.log('🔍 Testing API Gateway endpoint...');
        
        const response = await fetch('http://localhost:8080/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });
        
        console.log('📊 Response Status:', response.status);
        console.log('📊 Response Headers:', [...response.headers.entries()]);
        
        const data = await response.text();
        console.log('📄 Response Data:', data);
        
        // Try to parse as <PERSON><PERSON><PERSON>
        try {
            const jsonData = JSON.parse(data);
            console.log('✅ JSON Response:', jsonData);
            
            if (jsonData.success && jsonData.data && jsonData.data.token) {
                console.log('🎉 Login Successful!');
                console.log('🔑 Token:', jsonData.data.token);
                console.log('👤 User:', jsonData.data.user);
                
                // Store token in localStorage
                localStorage.setItem('authToken', jsonData.data.token);
                localStorage.setItem('user', JSON.stringify(jsonData.data.user));
                
                console.log('💾 Token stored in localStorage');
                
                // Redirect to dashboard
                window.location.href = '/dashboard';
                
            } else {
                console.log('❌ Login Failed:', jsonData.message || 'Unknown error');
            }
            
        } catch (parseError) {
            console.log('⚠️ Response is not JSON:', data);
            
            // If response is just a string, it might be a simple service
            if (data.includes('TecnoDrive') || data.includes('Auth')) {
                console.log('🔄 Trying alternative auth endpoints...');
                
                // Try auth service directly
                try {
                    const authResponse = await fetch('http://localhost:8081/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(loginData)
                    });
                    
                    const authData = await authResponse.text();
                    console.log('🔍 Auth Service Response:', authData);
                    
                } catch (authError) {
                    console.log('❌ Auth Service Error:', authError);
                }
            }
        }
        
    } catch (error) {
        console.log('❌ Network Error:', error);
        console.log('💡 This might be the "Network Error" you see in the UI');
        
        // Check if it's a CORS issue
        if (error.message.includes('CORS') || error.message.includes('fetch')) {
            console.log('🚨 CORS Issue Detected!');
            console.log('💡 Solutions:');
            console.log('   1. Check if backend has CORS enabled');
            console.log('   2. Verify API endpoint is correct');
            console.log('   3. Check if backend service is running');
        }
    }
}

// Test different endpoints
async function testAllEndpoints() {
    const endpoints = [
        'http://localhost:8080/api/auth/login',
        'http://localhost:8081/api/auth/login',
        'http://localhost:8085/api/auth/login'
    ];
    
    const loginData = {
        email: '<EMAIL>',
        password: 'password123'
    };
    
    for (const endpoint of endpoints) {
        console.log(`\n🔍 Testing: ${endpoint}`);
        
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(loginData)
            });
            
            const data = await response.text();
            console.log(`✅ ${endpoint}: ${response.status} - ${data.substring(0, 100)}...`);
            
        } catch (error) {
            console.log(`❌ ${endpoint}: ${error.message}`);
        }
    }
}

// Check current environment
function checkEnvironment() {
    console.log('\n🔍 Environment Check:');
    console.log('Current URL:', window.location.href);
    console.log('API Base URL:', process.env.REACT_APP_API_BASE_URL || 'Not set');
    console.log('LocalStorage Token:', localStorage.getItem('authToken') || 'None');
    console.log('LocalStorage User:', localStorage.getItem('user') || 'None');
}

// Run tests
console.log('🚀 Starting Login Tests...');
checkEnvironment();
testLogin();

// Also test all endpoints
setTimeout(() => {
    console.log('\n🔄 Testing All Endpoints...');
    testAllEndpoints();
}, 2000);
