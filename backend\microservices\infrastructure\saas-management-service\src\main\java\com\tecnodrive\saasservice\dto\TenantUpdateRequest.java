package com.tecnodrive.saasservice.dto;

import com.tecnodrive.saasservice.entity.Tenant;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;

/**
 * Tenant Update Request DTO
 * 
 * Used for updating tenant information (partial updates allowed)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantUpdateRequest {

    /**
     * Display name for the tenant
     */
    @Size(max = 200, message = "Display name cannot exceed 200 characters")
    private String displayName;

    /**
     * Tenant status
     */
    private Tenant.TenantStatus status;

    /**
     * Contact person user ID
     */
    private String contactPersonId;

    /**
     * Primary contact email
     */
    @Email(message = "Invalid email format")
    @Size(max = 255, message = "Email cannot exceed 255 characters")
    private String email;

    /**
     * Primary contact phone
     */
    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    private String phone;

    /**
     * Physical address
     */
    @Size(max = 500, message = "Address cannot exceed 500 characters")
    private String address;

    /**
     * Website URL
     */
    @Size(max = 255, message = "Website URL cannot exceed 255 characters")
    @Pattern(regexp = "^(https?://).*", message = "Website must start with http:// or https://")
    private String website;

    /**
     * Service types enabled for this tenant
     */
    private Tenant.ServiceType serviceType;

    /**
     * Current pricing plan ID
     */
    private String pricingPlanId;

    /**
     * Maximum number of users allowed
     */
    @Min(value = 1, message = "Maximum users must be at least 1")
    @Max(value = 10000, message = "Maximum users cannot exceed 10000")
    private Integer maxUsers;

    /**
     * Maximum number of vehicles allowed
     */
    @Min(value = 1, message = "Maximum vehicles must be at least 1")
    @Max(value = 1000, message = "Maximum vehicles cannot exceed 1000")
    private Integer maxVehicles;

    /**
     * Custom branding configuration (JSON)
     */
    private String brandingConfig;

    /**
     * Feature flags configuration (JSON)
     */
    private String featureFlags;

    /**
     * Additional metadata (JSON)
     */
    private String metadata;

    /**
     * Subscription start date
     */
    private Instant subscriptionStartDate;

    /**
     * Subscription end date
     */
    private Instant subscriptionEndDate;
}
