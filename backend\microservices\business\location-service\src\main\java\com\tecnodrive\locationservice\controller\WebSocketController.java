package com.tecnodrive.locationservice.controller;

import com.tecnodrive.locationservice.websocket.LocationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.List;

/**
 * REST Controller for WebSocket management and broadcasting
 */
@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketController {

    @Autowired
    private LocationWebSocketHandler webSocketHandler;

    /**
     * Get WebSocket connection statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getConnectionStats() {
        return ResponseEntity.ok(webSocketHandler.getConnectionStats());
    }

    /**
     * Broadcast location update to all subscribed clients
     */
    @PostMapping("/broadcast/location")
    public ResponseEntity<Map<String, String>> broadcastLocationUpdate(@RequestBody Map<String, Object> locationData) {
        try {
            webSocketHandler.broadcastLocationUpdate(locationData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Location update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast location update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast ride update to all subscribed clients
     */
    @PostMapping("/broadcast/ride")
    public ResponseEntity<Map<String, String>> broadcastRideUpdate(@RequestBody Map<String, Object> rideData) {
        try {
            webSocketHandler.broadcastRideUpdate(rideData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Ride update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast ride update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast parcel update to all subscribed clients
     */
    @PostMapping("/broadcast/parcel")
    public ResponseEntity<Map<String, String>> broadcastParcelUpdate(@RequestBody Map<String, Object> parcelData) {
        try {
            webSocketHandler.broadcastParcelUpdate(parcelData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Parcel update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast parcel update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast fleet update to all subscribed clients
     */
    @PostMapping("/broadcast/fleet")
    public ResponseEntity<Map<String, String>> broadcastFleetUpdate(@RequestBody Map<String, Object> fleetData) {
        try {
            webSocketHandler.broadcastFleetUpdate(fleetData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Fleet update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast fleet update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast payment update to all subscribed clients
     */
    @PostMapping("/broadcast/payment")
    public ResponseEntity<Map<String, String>> broadcastPaymentUpdate(@RequestBody Map<String, Object> paymentData) {
        try {
            webSocketHandler.broadcastPaymentUpdate(paymentData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Payment update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast payment update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast notification to all or specific clients
     */
    @PostMapping("/broadcast/notification")
    public ResponseEntity<Map<String, String>> broadcastNotification(@RequestBody Map<String, Object> notificationData) {
        try {
            webSocketHandler.broadcastNotification(notificationData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Notification broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast notification: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast analytics update to subscribed clients
     */
    @PostMapping("/broadcast/analytics")
    public ResponseEntity<Map<String, String>> broadcastAnalyticsUpdate(@RequestBody Map<String, Object> analyticsData) {
        try {
            webSocketHandler.broadcastAnalyticsUpdate(analyticsData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Analytics update broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast analytics update: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast emergency alert to all clients
     */
    @PostMapping("/broadcast/emergency-alert")
    public ResponseEntity<Map<String, String>> broadcastEmergencyAlert(@RequestBody Map<String, Object> emergencyData) {
        try {
            webSocketHandler.broadcastEmergencyAlert(emergencyData);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Emergency alert broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast emergency alert: " + e.getMessage()
            ));
        }
    }

    /**
     * Send bulk location updates
     */
    @PostMapping("/broadcast/bulk-location")
    public ResponseEntity<Map<String, String>> broadcastBulkLocationUpdate(@RequestBody List<Map<String, Object>> locationUpdates) {
        try {
            webSocketHandler.broadcastBulkLocationUpdate(locationUpdates);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Bulk location updates broadcasted successfully",
                "count", String.valueOf(locationUpdates.size())
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast bulk location updates: " + e.getMessage()
            ));
        }
    }

    /**
     * Broadcast system status update
     */
    @PostMapping("/broadcast/system-status")
    public ResponseEntity<Map<String, String>> broadcastSystemStatus(@RequestBody Map<String, Object> systemStatus) {
        try {
            webSocketHandler.broadcastSystemStatus(systemStatus);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "System status broadcasted successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to broadcast system status: " + e.getMessage()
            ));
        }
    }

    /**
     * Send heartbeat to all connected clients
     */
    @PostMapping("/heartbeat")
    public ResponseEntity<Map<String, String>> sendHeartbeat() {
        try {
            webSocketHandler.sendHeartbeat();
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Heartbeat sent to all connected clients"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to send heartbeat: " + e.getMessage()
            ));
        }
    }

    /**
     * Cleanup inactive sessions
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, String>> cleanupInactiveSessions() {
        try {
            webSocketHandler.cleanupInactiveSessions();
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Inactive sessions cleaned up successfully"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to cleanup inactive sessions: " + e.getMessage()
            ));
        }
    }

    /**
     * Get session subscriptions for monitoring
     */
    @GetMapping("/subscriptions")
    public ResponseEntity<Map<String, Object>> getSessionSubscriptions() {
        try {
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "subscriptions", webSocketHandler.getSessionSubscriptions()
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Failed to get session subscriptions: " + e.getMessage()
            ));
        }
    }
}
