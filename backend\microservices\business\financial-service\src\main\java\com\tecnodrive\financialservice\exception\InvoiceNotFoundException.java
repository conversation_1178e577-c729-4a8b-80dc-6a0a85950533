package com.tecnodrive.financialservice.exception;

/**
 * Invoice Not Found Exception
 * 
 * Thrown when a requested invoice cannot be found
 */
public class InvoiceNotFoundException extends RuntimeException {

    public InvoiceNotFoundException() {
        super("Invoice not found");
    }

    public InvoiceNotFoundException(String message) {
        super(message);
    }

    public InvoiceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
