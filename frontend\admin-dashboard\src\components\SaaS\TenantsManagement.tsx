import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { saasService, TenantDto, CreateTenantRequest } from '../../services/saasService';

const TenantsManagement: React.FC = () => {
  const navigate = useNavigate();
  
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<TenantDto | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const [newTenant, setNewTenant] = useState<CreateTenantRequest>({
    name: '',
    domain: '',
    phone: '',
    email: '',
    contactPerson: '',
    address: '',
  });

  // Load tenants
  const loadTenants = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await saasService.getTenants(searchTerm, filterStatus);
      
      if (response.success && response.data) {
        setTenants(response.data);
      } else {
        setError(response.error || 'فشل في جلب بيانات العملاء');
      }
    } catch (err) {
      setError('حدث خطأ في جلب بيانات العملاء');
      console.error('Error loading tenants:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTenants();
  }, [searchTerm, filterStatus]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const },
      INACTIVE: { label: 'غير نشط', color: 'default' as const },
      SUSPENDED: { label: 'معلق', color: 'error' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleViewTenant = (tenantId: string) => {
    navigate(`/saas/tenants/${tenantId}`);
  };

  const handleEditTenant = (tenant: TenantDto) => {
    setSelectedTenant(tenant);
    setNewTenant({
      name: tenant.name,
      domain: tenant.domain,
      phone: tenant.phone,
      email: tenant.email,
      contactPerson: '',
      address: '',
    });
    setOpenEditDialog(true);
  };

  const handleDeleteTenant = async (tenantId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        await saasService.deleteTenant(tenantId);
        loadTenants();
      } catch (err) {
        setError('فشل في حذف العميل');
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم العميل',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BusinessIcon color="primary" />
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'domain',
      headerName: 'النطاق',
      width: 250,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'phone',
      headerName: 'الهاتف',
      width: 150,
    },
    {
      field: 'email',
      headerName: 'البريد الإلكتروني',
      width: 200,
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'subscriptionsCount',
      headerName: 'الاشتراكات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          {params.value || 0}
        </Typography>
      ),
    },
    {
      field: 'totalUsers',
      headerName: 'المستخدمين',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value || 0}
        </Typography>
      ),
    },
    {
      field: 'billingBalance',
      headerName: 'الرصيد',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'success.main' }}>
          {params.value || 0} ريال
        </Typography>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الإنشاء',
      width: 150,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<VisibilityIcon />}
          label="عرض"
          onClick={() => handleViewTenant(params.id as string)}
        />,
        <GridActionsCellItem
          icon={<EditIcon />}
          label="تعديل"
          onClick={() => handleEditTenant(params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="حذف"
          onClick={() => handleDeleteTenant(params.id as string)}
        />,
      ],
    },
  ];

  const handleAddTenant = async () => {
    try {
      const response = await saasService.createTenant(newTenant);
      
      if (response.success) {
        setOpenAddDialog(false);
        setNewTenant({
          name: '',
          domain: '',
          phone: '',
          email: '',
          contactPerson: '',
          address: '',
        });
        loadTenants();
      } else {
        setError(response.error || 'فشل في إضافة العميل');
      }
    } catch (err) {
      setError('حدث خطأ في إضافة العميل');
    }
  };

  const handleUpdateTenant = async () => {
    if (!selectedTenant) return;

    try {
      const response = await saasService.updateTenant(selectedTenant.id, newTenant);
      
      if (response.success) {
        setOpenEditDialog(false);
        setSelectedTenant(null);
        setNewTenant({
          name: '',
          domain: '',
          phone: '',
          email: '',
          contactPerson: '',
          address: '',
        });
        loadTenants();
      } else {
        setError(response.error || 'فشل في تحديث العميل');
      }
    } catch (err) {
      setError('حدث خطأ في تحديث العميل');
    }
  };

  const activeTenantsCount = tenants.filter(t => t.status === 'ACTIVE').length;
  const inactiveTenantsCount = tenants.filter(t => t.status === 'INACTIVE').length;
  const suspendedTenantsCount = tenants.filter(t => t.status === 'SUSPENDED').length;
  const totalRevenue = tenants.reduce((sum, t) => sum + (t.billingBalance || 0), 0);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة العملاء (SaaS)
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة العملاء والمؤسسات المشتركة في النظام
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {tenants.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي العملاء
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {activeTenantsCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                عملاء نشطين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {inactiveTenantsCount + suspendedTenantsCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                عملاء غير نشطين
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {totalRevenue.toLocaleString()} ريال
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإيرادات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في العملاء..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterStatus}
                label="الحالة"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ACTIVE">نشط</MenuItem>
                <MenuItem value="INACTIVE">غير نشط</MenuItem>
                <MenuItem value="SUSPENDED">معلق</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة عميل
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={tenants}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Tenant Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة عميل جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="اسم العميل"
                  value={newTenant.name}
                  onChange={(e) => setNewTenant({ ...newTenant, name: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="النطاق"
                  value={newTenant.domain}
                  onChange={(e) => setNewTenant({ ...newTenant, domain: e.target.value })}
                  fullWidth
                  required
                  placeholder="example.tecno-drive.com"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="رقم الهاتف"
                  value={newTenant.phone}
                  onChange={(e) => setNewTenant({ ...newTenant, phone: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="البريد الإلكتروني"
                  type="email"
                  value={newTenant.email}
                  onChange={(e) => setNewTenant({ ...newTenant, email: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="الشخص المسؤول"
                  value={newTenant.contactPerson}
                  onChange={(e) => setNewTenant({ ...newTenant, contactPerson: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="العنوان"
                  value={newTenant.address}
                  onChange={(e) => setNewTenant({ ...newTenant, address: e.target.value })}
                  fullWidth
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddTenant} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Tenant Dialog */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>تعديل العميل</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="اسم العميل"
                  value={newTenant.name}
                  onChange={(e) => setNewTenant({ ...newTenant, name: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="النطاق"
                  value={newTenant.domain}
                  onChange={(e) => setNewTenant({ ...newTenant, domain: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="رقم الهاتف"
                  value={newTenant.phone}
                  onChange={(e) => setNewTenant({ ...newTenant, phone: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="البريد الإلكتروني"
                  type="email"
                  value={newTenant.email}
                  onChange={(e) => setNewTenant({ ...newTenant, email: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>إلغاء</Button>
          <Button onClick={handleUpdateTenant} variant="contained">حفظ</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TenantsManagement;
