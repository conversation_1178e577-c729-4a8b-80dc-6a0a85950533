server:
  port: 8092

spring:
  application:
    name: fleet-service
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_fleet
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  cache:
    type: simple
eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics

# Logging
logging:
  level:
    com.tecnodrive.fleetservice: DEBUG

# Fleet Configuration
fleet:
  vehicle:
    default-status: AVAILABLE
    maintenance-interval-km: 10000
    inspection-interval-months: 6
  driver:
    max-vehicles-per-driver: 3
    license-expiry-warning-days: 30
  maintenance:
    reminder-days-before: 7
    overdue-threshold-days: 3
  insurance:
    expiry-warning-days: 30
    renewal-reminder-days: 60

# Feign Configuration
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000

# Async Configuration
async:
  core-pool-size: 5
  max-pool-size: 10
  queue-capacity: 100

# Scheduling Configuration
scheduling:
  maintenance-check-cron: "0 0 8 * * *"
  insurance-check-cron: "0 0 9 * * *"
  vehicle-status-update-cron: "0 */15 * * * *"
