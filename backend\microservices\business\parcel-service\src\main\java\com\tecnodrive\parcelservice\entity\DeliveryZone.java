package com.tecnodrive.parcelservice.entity;

import jakarta.persistence.*;
import org.locationtech.jts.geom.Polygon;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Delivery Zone Entity
 */
@Entity
@Table(name = "delivery_zones")
public class DeliveryZone {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(nullable = false)
    private String name;

    @Column(name = "name_ar", nullable = false)
    private String nameAr;

    @Column(nullable = false)
    private String city;

    @Column(name = "area_polygon", columnDefinition = "GEOMETRY")
    private Polygon areaPolygon;

    @Column(name = "base_delivery_fee", precision = 10, scale = 2, nullable = false)
    private BigDecimal baseDeliveryFee = BigDecimal.ZERO;

    @Column(name = "per_km_rate", precision = 10, scale = 2, nullable = false)
    private BigDecimal perKmRate = BigDecimal.ZERO;

    @Column(name = "estimated_delivery_hours")
    private Integer estimatedDeliveryHours = 24;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "created_at", updatable = false)
    private Instant createdAt = Instant.now();

    // Constructors
    public DeliveryZone() {}

    public DeliveryZone(String name, String nameAr, String city, 
                       BigDecimal baseDeliveryFee, BigDecimal perKmRate, Integer estimatedDeliveryHours) {
        this.name = name;
        this.nameAr = nameAr;
        this.city = city;
        this.baseDeliveryFee = baseDeliveryFee;
        this.perKmRate = perKmRate;
        this.estimatedDeliveryHours = estimatedDeliveryHours;
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getNameAr() { return nameAr; }
    public void setNameAr(String nameAr) { this.nameAr = nameAr; }

    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }

    public Polygon getAreaPolygon() { return areaPolygon; }
    public void setAreaPolygon(Polygon areaPolygon) { this.areaPolygon = areaPolygon; }

    public BigDecimal getBaseDeliveryFee() { return baseDeliveryFee; }
    public void setBaseDeliveryFee(BigDecimal baseDeliveryFee) { this.baseDeliveryFee = baseDeliveryFee; }

    public BigDecimal getPerKmRate() { return perKmRate; }
    public void setPerKmRate(BigDecimal perKmRate) { this.perKmRate = perKmRate; }

    public Integer getEstimatedDeliveryHours() { return estimatedDeliveryHours; }
    public void setEstimatedDeliveryHours(Integer estimatedDeliveryHours) { this.estimatedDeliveryHours = estimatedDeliveryHours; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    @Override
    public String toString() {
        return "DeliveryZone{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", nameAr='" + nameAr + '\'' +
                ", city='" + city + '\'' +
                ", baseDeliveryFee=" + baseDeliveryFee +
                '}';
    }
}
