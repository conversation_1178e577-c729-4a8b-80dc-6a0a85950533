package com.tecnodrive.paymentservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Payment Entity
 * 
 * Represents a payment transaction in the system.
 * Supports payments for various entities like rides, deliveries, etc.
 */
@Entity
@Table(name = "payments")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Payment {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * ID of the entity being paid for (e.g., ride ID, delivery ID)
     */
    @Column(nullable = false)
    private String entityId;

    /**
     * Type of entity being paid for (e.g., RIDE, DELIVERY, SUBSCRIPTION)
     */
    @Column(nullable = false)
    private String entityType;

    /**
     * ID of the user making the payment
     */
    @Column(nullable = false)
    private String payerUserId;

    /**
     * ID of the user receiving the payment (optional, for peer-to-peer payments)
     */
    private String payeeUserId;

    /**
     * Payment amount
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;

    /**
     * Currency code (e.g., USD, EUR, SAR)
     */
    @Column(nullable = false, length = 3)
    private String currency;

    /**
     * Payment status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private PaymentStatus status = PaymentStatus.PENDING;

    /**
     * Payment method used (e.g., CREDIT_CARD, DEBIT_CARD, WALLET, CASH)
     */
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;

    /**
     * External payment gateway transaction ID
     */
    private String gatewayTransactionId;

    /**
     * Payment description or notes
     */
    private String description;

    /**
     * Additional metadata as JSON string
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Payment Status Enum
     */
    public enum PaymentStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED,
        CANCELLED,
        REFUNDED,
        PARTIALLY_REFUNDED
    }

    /**
     * Payment Method Enum
     */
    public enum PaymentMethod {
        CREDIT_CARD,
        DEBIT_CARD,
        DIGITAL_WALLET,
        BANK_TRANSFER,
        CASH,
        CRYPTOCURRENCY
    }
}
