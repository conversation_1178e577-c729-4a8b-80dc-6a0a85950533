#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Comprehensive Monitoring Script
# This script monitors all services and provides real-time status updates

Write-Host "🔍 TECNO DRIVE Platform - Service Monitor" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Cyan

# Function to check service health
function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$TimeoutSeconds = 5
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $ServiceName - HEALTHY" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ $ServiceName - UNHEALTHY" -ForegroundColor Red
        return $false
    }
}

# Function to check Docker container status
function Test-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker inspect --format='{{.State.Status}}' $ContainerName 2>$null
        if ($status -eq "running") {
            Write-Host "✅ $ContainerName - RUNNING" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $ContainerName - $status" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $ContainerName - NOT FOUND" -ForegroundColor Red
        return $false
    }
}

# Monitor loop
while ($true) {
    Clear-Host
    Write-Host "🔍 TECNO DRIVE Platform - Service Monitor" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Cyan
    Write-Host "⏰ $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "📊 Backend Services:" -ForegroundColor Blue
    Test-ContainerStatus "infra-postgres-1"
    Test-ContainerStatus "infra-redis-1"
    Test-ContainerStatus "infra-eureka-server-1"
    Test-ServiceHealth "Auth Service" "http://localhost:8081/actuator/health"
    Test-ServiceHealth "Parcel Service" "http://localhost:8084/actuator/health"
    Test-ServiceHealth "Location Service" "http://localhost:8086/actuator/health"
    Test-ServiceHealth "API Gateway" "http://localhost:8080/actuator/health"
    
    Write-Host ""
    Write-Host "🌐 Frontend Applications:" -ForegroundColor Blue
    Test-ServiceHealth "Admin Dashboard" "http://localhost:3000"
    Test-ServiceHealth "HR Frontend" "http://localhost:3002"
    
    Write-Host ""
    Write-Host "📈 Monitoring Services:" -ForegroundColor Blue
    Test-ServiceHealth "Prometheus" "http://localhost:9090"
    Test-ServiceHealth "Grafana" "http://localhost:3001"
    
    Write-Host ""
    Write-Host "Press Ctrl+C to exit monitoring..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}
