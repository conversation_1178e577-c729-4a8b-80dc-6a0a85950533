# 🚨 TECNO DRIVE Advanced Security Runbook

## 📋 Overview

This runbook covers emergency procedures and troubleshooting for the advanced TECNO DRIVE security system including:
- Real-time webhook validation
- Auto-renewing TLS certificates  
- Unified logging with Loki
- Advanced SLO/SLI monitoring
- PagerDuty/Teams integration

---

## 🚨 Emergency Procedures

### 1. Critical Security Violation Response

#### Immediate Actions (0-5 minutes)
```bash
# Check current violations
kubectl get events --field-selector reason=ConstraintViolation -A

# Get violation details
kubectl get constraints -o yaml | grep -A 10 violations

# If system is unstable, set to warn mode
kubectl get constraints -o name | \
  xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
```

#### Investigation (5-15 minutes)
```bash
# Check Gatekeeper logs
kubectl logs -n gatekeeper-system -l control-plane=controller-manager --tail=100

# Check webhook performance
curl -s "http://prometheus:9090/api/v1/query?query=sli:webhook_latency:p99_5m"

# Check SLO burn rate
curl -s "http://prometheus:9090/api/v1/query?query=slo:security_policy_compliance:burn_rate_5m"
```

#### Resolution (15-30 minutes)
```bash
# Fix violating resources
kubectl get pods -A --field-selector status.phase=Failed
kubectl describe pod <failing-pod> -n <namespace>

# Restore deny mode after fixes
kubectl get constraints -o name | \
  xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"deny"}}'
```

### 2. Certificate Expiry Emergency

#### Critical Certificate Expiring (< 7 days)
```bash
# Emergency renewal
kubectl cert-manager renew gatekeeper-webhook-cert -n tecno-drive-system

# Check renewal status
kubectl describe certificate gatekeeper-webhook-cert -n tecno-drive-system

# If renewal fails, create emergency self-signed
./emergency-scripts/cert-emergency-renewal.sh gatekeeper-webhook-cert tecno-drive-system
```

#### Mass Certificate Failure
```bash
# Check cert-manager status
kubectl get pods -n cert-manager
kubectl logs -n cert-manager -l app=cert-manager --tail=50

# Check all certificate statuses
kubectl get certificates -A -o custom-columns="NAMESPACE:.metadata.namespace,NAME:.metadata.name,READY:.status.conditions[?(@.type=='Ready')].status,AGE:.metadata.creationTimestamp"

# Restart cert-manager if needed
kubectl rollout restart deployment/cert-manager -n cert-manager
kubectl rollout restart deployment/cert-manager-webhook -n cert-manager
kubectl rollout restart deployment/cert-manager-cainjector -n cert-manager
```

### 3. Webhook Performance Degradation

#### High Latency (P99 > 500ms)
```bash
# Check webhook metrics
kubectl port-forward -n gatekeeper-system svc/gatekeeper-webhook-service 8888:8888 &
curl -s "http://localhost:8888/metrics" | grep webhook_request_duration

# Check resource usage
kubectl top pods -n gatekeeper-system

# Scale up if needed
kubectl scale deployment gatekeeper-controller-manager -n gatekeeper-system --replicas=3
```

#### Webhook Failures
```bash
# Check webhook configuration
kubectl get validatingadmissionwebhook gatekeeper-validating-webhook-configuration -o yaml

# Test webhook connectivity
kubectl run test-pod --image=busybox --rm -it -- /bin/sh
# Inside pod: wget -qO- https://gatekeeper-webhook-service.gatekeeper-system:443/healthz

# Emergency disable webhook
kubectl delete validatingadmissionwebhook gatekeeper-validating-webhook-configuration
```

### 4. SLO Burn Rate Critical

#### Security Compliance Burn Rate > 14.4
```bash
# Immediate assessment
curl -s "http://prometheus:9090/api/v1/query?query=sli:security_policy_compliance:ratio_rate5m"

# Check error budget remaining
curl -s "http://prometheus:9090/api/v1/query?query=slo:security_policy_compliance:error_budget_remaining_30d"

# Identify violation sources
kubectl get events --field-selector reason=ConstraintViolation -A --sort-by='.lastTimestamp' | tail -20

# Temporary relaxation if needed
kubectl patch centralsecurity tecno-drive-security-policy \
  --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
```

---

## 🔍 Troubleshooting Guide

### Gatekeeper Issues

#### Webhook Not Responding
```bash
# Check webhook pod status
kubectl get pods -n gatekeeper-system -l control-plane=controller-manager

# Check webhook service
kubectl get svc -n gatekeeper-system gatekeeper-webhook-service

# Check webhook configuration
kubectl describe validatingadmissionwebhook gatekeeper-validating-webhook-configuration

# Test webhook endpoint
kubectl run debug-pod --image=curlimages/curl --rm -it -- \
  curl -k https://gatekeeper-webhook-service.gatekeeper-system:443/healthz
```

#### Policy Violations Not Detected
```bash
# Check constraint status
kubectl get constraints -o yaml | grep -A 5 status

# Check audit logs
kubectl logs -n gatekeeper-system -l control-plane=audit --tail=100

# Force audit run
kubectl annotate constraint centralsecurity audit.gatekeeper.sh/force-audit="$(date +%s)"
```

### Certificate Manager Issues

#### Certificate Not Renewing
```bash
# Check certificate status
kubectl describe certificate gatekeeper-webhook-cert -n tecno-drive-system

# Check issuer status
kubectl describe clusterissuer gatekeeper-ca-issuer

# Check cert-manager logs
kubectl logs -n cert-manager -l app=cert-manager --tail=100

# Manual renewal
kubectl cert-manager renew gatekeeper-webhook-cert -n tecno-drive-system
```

#### CA Bundle Not Injected
```bash
# Check webhook configuration
kubectl get validatingadmissionwebhook gatekeeper-validating-webhook-configuration \
  -o jsonpath='{.webhooks[0].clientConfig.caBundle}' | base64 -d | openssl x509 -text

# Check cert-manager cainjector
kubectl logs -n cert-manager -l app=cert-manager-cainjector --tail=50

# Force CA injection
kubectl annotate validatingadmissionwebhook gatekeeper-validating-webhook-configuration \
  cert-manager.io/inject-ca-from-secret="tecno-drive-system/gatekeeper-webhook-tls" --overwrite
```

### Loki Logging Issues

#### Logs Not Appearing in Loki
```bash
# Check Promtail status
kubectl get pods -n logging -l app.kubernetes.io/name=promtail

# Check Promtail logs
kubectl logs -n logging -l app.kubernetes.io/name=promtail --tail=50

# Check Loki status
kubectl get pods -n logging -l app.kubernetes.io/name=loki

# Test Loki API
kubectl port-forward -n logging svc/loki 3100:3100 &
curl -s "http://localhost:3100/ready"
curl -s "http://localhost:3100/loki/api/v1/label"
```

#### Loki Query Performance Issues
```bash
# Check Loki metrics
kubectl port-forward -n logging svc/loki 3100:3100 &
curl -s "http://localhost:3100/metrics" | grep loki_

# Check storage usage
kubectl exec -n logging deployment/loki -- df -h /tmp/loki

# Optimize queries
# Use specific time ranges: {namespace="tecno-drive-system"} [5m]
# Add filters early: {namespace="tecno-drive-system"} |= "error" |= "violation"
```

### Alertmanager Issues

#### Alerts Not Firing
```bash
# Check Prometheus rules
kubectl get prometheusrules -n tecno-drive-system

# Check alert status in Prometheus
kubectl port-forward -n monitoring svc/prometheus-kube-prometheus-prometheus 9090:9090 &
# Visit: http://localhost:9090/alerts

# Check Alertmanager configuration
kubectl get secret alertmanager-config -n tecno-drive-system -o yaml

# Test alert routing
kubectl port-forward -n monitoring svc/prometheus-kube-prometheus-alertmanager 9093:9093 &
# Visit: http://localhost:9093
```

#### PagerDuty/Teams Not Receiving Alerts
```bash
# Check Alertmanager logs
kubectl logs -n monitoring -l app.kubernetes.io/name=alertmanager --tail=100

# Test webhook endpoints
curl -X POST "https://events.pagerduty.com/v2/enqueue" \
  -H "Content-Type: application/json" \
  -d '{"routing_key":"test","event_action":"trigger","payload":{"summary":"test"}}'

# Check Teams webhook
curl -X POST "YOUR_TEAMS_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{"text":"Test message from TECNO DRIVE"}'
```

---

## 📊 Monitoring Queries

### Security Metrics
```promql
# Security policy compliance
sli:security_policy_compliance:ratio_rate5m

# Security violations by type
sum by (violation_kind) (rate(gatekeeper_violations_total[5m]))

# Security burn rate
slo:security_policy_compliance:burn_rate_5m
```

### Performance Metrics
```promql
# Webhook latency P99
sli:webhook_latency:p99_5m

# Webhook success rate
sli:webhook_success_rate:ratio_rate5m

# API Gateway performance
sli:api_gateway_performance:ratio_rate5m
```

### Certificate Metrics
```promql
# Certificate expiry days
cert:expiry_days_remaining

# Certificate renewal success rate
cert:renewal_success_rate_24h

# Certificates expiring soon
count(cert:expiry_days_remaining < 30)
```

### Business Metrics
```promql
# Business services availability
sli:business_services_availability:ratio_rate5m

# Individual service availability
sli:ride_service_availability:ratio_rate5m
sli:payment_service_availability:ratio_rate5m
sli:fleet_service_availability:ratio_rate5m
```

---

## 🔧 Maintenance Procedures

### Weekly Maintenance
```bash
# Check certificate health
./emergency-scripts/cert-health-check.sh

# Review SLO performance
kubectl port-forward -n monitoring svc/prometheus-kube-prometheus-prometheus 9090:9090 &
# Query: slo:security_policy_compliance:error_budget_remaining_30d

# Clean up old logs
kubectl exec -n logging deployment/loki -- find /tmp/loki -name "*.gz" -mtime +7 -delete

# Update Gatekeeper policies if needed
kubectl apply -f kubernetes/secure-cluster/templates/gatekeeper/
```

### Monthly Maintenance
```bash
# Review and update emergency procedures
kubectl get configmap gatekeeper-emergency-procedures -n tecno-drive-system -o yaml

# Test emergency procedures
./emergency-scripts/emergency-warn-mode.sh
./emergency-scripts/emergency-restore.sh

# Review SLO targets
# Adjust targets in values.yaml if needed based on performance trends

# Certificate backup verification
kubectl get cronjob certificate-backup -n tecno-drive-system
kubectl get jobs -n tecno-drive-system -l app.kubernetes.io/name=certificate-backup-job
```

---

## 📞 Escalation Matrix

### Severity Levels

#### Critical (Response: Immediate)
- Security policy compliance < 99%
- Certificate expiry < 7 days
- Webhook completely down
- Business service availability < 95%

**Contacts:**
- Security Team: <EMAIL>
- Platform Team: <EMAIL>
- On-Call Engineer: +966-11-XXX-XXXX

#### High (Response: 15 minutes)
- Security violations > 10/hour
- Certificate expiry < 30 days
- Webhook latency > 1s
- SLO burn rate > 6

**Contacts:**
- Security Team: <EMAIL>
- Platform Team: <EMAIL>

#### Medium (Response: 1 hour)
- Security violations > 5/hour
- Certificate renewal failures
- Webhook latency > 500ms
- SLO burn rate > 2

**Contacts:**
- Platform Team: <EMAIL>

#### Low (Response: Next business day)
- Security violations > 1/hour
- Certificate expiry < 60 days
- Minor performance degradation

**Contacts:**
- Platform Team: <EMAIL>

---

## 📚 Additional Resources

- [Gatekeeper Documentation](https://open-policy-agent.github.io/gatekeeper/)
- [Cert-Manager Documentation](https://cert-manager.io/docs/)
- [Loki Documentation](https://grafana.com/docs/loki/)
- [Prometheus Alerting](https://prometheus.io/docs/alerting/)
- [TECNO DRIVE Security Policies](./security-policies.md)
- [TECNO DRIVE SLO Definitions](./slo-definitions.md)

---

**Last Updated:** $(date)
**Version:** 1.0.0
**Maintained by:** TECNO DRIVE Platform Team
