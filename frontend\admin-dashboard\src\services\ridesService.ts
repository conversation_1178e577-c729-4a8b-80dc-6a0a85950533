import { apiMethods, handleApiError, SERVICE_URLS } from './api';
import { ApiResponse, RideDto, RideStatus } from '../types/api';
import { Ride } from '../store/slices/ridesSlice';
import { MockService } from './mockService';
import { smartApiService } from './smartApiService';

// Types for Ride Service - Updated to match backend DTOs
export interface RideRequestDto {
  passengerId: string;
  pickupLocation: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  destination: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  vehicleType?: string;
  scheduledTime?: string;
  notes?: string;
}

export interface RideDto {
  id: string;
  passengerId: string;
  driverId?: string;
  pickupLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  destination: {
    latitude: number;
    longitude: number;
    address: string;
  };
  status: 'REQUESTED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  fare: number;
  distance: number;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

export interface FareEstimateDto {
  estimatedFare: number;
  distance: number;
  duration: number;
  currency: string;
}

export interface RideMetrics {
  totalRides: number;
  activeRides: number;
  completedRides: number;
  cancelledRides: number;
  averageRating: number;
  totalRevenue: number;
}

export interface RideFilters {
  page?: number;
  limit?: number;
  status?: string;
  passengerId?: string;
  driverId?: string;
  startDate?: string;
  endDate?: string;
}

class RidesService {
  private baseUrl = SERVICE_URLS.RIDE_SERVICE;

  // Get all rides with filters - Smart API with automatic fallback
  async getRides(filters: RideFilters = {}): Promise<ApiResponse<Ride[]>> {
    return await smartApiService.getRides({
      page: filters.page,
      limit: filters.limit,
      status: filters.status,
      passengerId: filters.passengerId,
      driverId: filters.driverId,
      startDate: filters.startDate,
      endDate: filters.endDate
    });
  }

  // Get ride by ID
  async getRideById(rideId: string): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride>>(
        `${this.baseUrl}/${rideId}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Request a new ride - Updated to match backend endpoint
  async requestRide(rideData: RideRequestDto): Promise<ApiResponse<RideDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<RideDto>>(
        `${this.baseUrl}`, // POST /api/rides
        rideData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        return await mockApiResponses.createRide(rideData);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Estimate fare
  async estimateFare(pickup: { latitude: number; longitude: number }, destination: { latitude: number; longitude: number }): Promise<ApiResponse<FareEstimateDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<FareEstimateDto>>(
        `${this.baseUrl}/estimate-fare`,
        { pickup, destination }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Assign driver to ride
  async assignDriver(rideId: string, driverId: string): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.post<ApiResponse<Ride>>(
        `${this.baseUrl}/${rideId}/assign-driver`,
        { driverId }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update ride status - Updated to match backend endpoint
  async updateRideStatus(rideId: string, status: string): Promise<ApiResponse<RideDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<RideDto>>(
        `${this.baseUrl}/${rideId}/status?status=${status}`, // PUT /api/rides/{id}/status?status=...
        {}
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        return await mockApiResponses.updateRideStatus(rideId, status);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Complete ride
  async completeRide(rideId: string, finalFare: number, actualDistance: number, actualDuration: number): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.post<ApiResponse<Ride>>(
        `${this.baseUrl}/${rideId}/complete`,
        { finalFare, actualDistance, actualDuration }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Cancel ride
  async cancelRide(rideId: string, cancelStatus: string, reason: string): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.post<ApiResponse<Ride>>(
        `${this.baseUrl}/${rideId}/cancel`,
        { cancelStatus, reason }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Rate ride
  async rateRide(rideId: string, raterId: string, rating: number): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.post<ApiResponse<Ride>>(
        `${this.baseUrl}/${rideId}/rate`,
        { raterId, rating }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get rides by passenger
  async getRidesByPassenger(passengerId: string): Promise<ApiResponse<Ride[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride[]>>(
        `${this.baseUrl}/passenger/${passengerId}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get rides by driver
  async getRidesByDriver(driverId: string): Promise<ApiResponse<Ride[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride[]>>(
        `${this.baseUrl}/driver/${driverId}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get active ride by passenger
  async getActiveRideByPassenger(passengerId: string): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride>>(
        `${this.baseUrl}/passenger/${passengerId}/active`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get active ride by driver
  async getActiveRideByDriver(driverId: string): Promise<ApiResponse<Ride>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride>>(
        `${this.baseUrl}/driver/${driverId}/active`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get available rides for driver
  async getAvailableRides(driverId: string, latitude: number, longitude: number, radiusKm: number = 5.0): Promise<ApiResponse<Ride[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<Ride[]>>(
        `${this.baseUrl}/driver/${driverId}/available?latitude=${latitude}&longitude=${longitude}&radiusKm=${radiusKm}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get ride metrics
  async getRideMetrics(): Promise<ApiResponse<RideMetrics>> {
    try {
      const response = await apiMethods.get<ApiResponse<RideMetrics>>(
        `${this.baseUrl}/metrics`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const ridesService = new RidesService();
export default ridesService;
