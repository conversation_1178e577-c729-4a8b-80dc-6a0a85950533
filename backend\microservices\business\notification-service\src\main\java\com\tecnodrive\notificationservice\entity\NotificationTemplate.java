package com.tecnodrive.notificationservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

/**
 * Notification Template Entity
 * 
 * Represents a reusable notification template for different channels.
 * Supports variable substitution and multi-channel delivery.
 */
@Entity
@Table(name = "notification_templates")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class NotificationTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Unique template identifier
     */
    @Column(unique = true, nullable = false, length = 100)
    private String templateName;

    /**
     * Template display name
     */
    @Column(nullable = false, length = 200)
    private String displayName;

    /**
     * Template description
     */
    @Column(length = 500)
    private String description;

    /**
     * Notification channel
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationChannel channel;

    /**
     * Template category for organization
     */
    @Column(length = 50)
    private String category;

    /**
     * Subject template (for email/SMS)
     */
    @Column(length = 500)
    private String subjectTemplate;

    /**
     * Message body template
     */
    @Column(columnDefinition = "TEXT", nullable = false)
    private String bodyTemplate;

    /**
     * Template variables (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String templateVariables;

    /**
     * Template priority
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private NotificationPriority priority = NotificationPriority.NORMAL;

    /**
     * Template status
     */
    @Builder.Default
    private boolean isActive = true;

    /**
     * Template version for tracking changes
     */
    @Builder.Default
    private Integer version = 1;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Notification Channel Enum
     */
    public enum NotificationChannel {
        EMAIL,
        SMS,
        PUSH,
        IN_APP,
        WEBHOOK
    }

    /**
     * Notification Priority Enum
     */
    public enum NotificationPriority {
        LOW,
        NORMAL,
        HIGH,
        URGENT
    }
}
