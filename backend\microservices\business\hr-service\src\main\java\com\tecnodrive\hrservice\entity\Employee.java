package com.tecnodrive.hrservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Employee Entity
 * 
 * Represents an employee in the HR system with comprehensive profile information.
 * Supports payroll, performance tracking, and HR management features.
 */
@Entity
@Table(name = "employees")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Employee {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Employee personal information
     */
    @Column(nullable = false, length = 100)
    private String firstName;

    @Column(nullable = false, length = 100)
    private String lastName;

    @Column(nullable = false, unique = true, length = 150)
    private String email;

    @Column(unique = true, length = 20)
    private String employeeNumber;

    @Column(length = 20)
    private String phoneNumber;

    private LocalDate dateOfBirth;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Column(length = 500)
    private String address;

    /**
     * Employment information
     */
    @Column(nullable = false, length = 100)
    private String position;

    @Column(nullable = false, length = 100)
    private String department;

    @Enumerated(EnumType.STRING)
    @Builder.Default
    private EmployeeStatus status = EmployeeStatus.ACTIVE;

    @Enumerated(EnumType.STRING)
    private EmploymentType employmentType;

    @Column(nullable = false)
    private LocalDate hireDate;

    private LocalDate terminationDate;

    private LocalDate probationEndDate;

    /**
     * Compensation information
     */
    @Column(precision = 12, scale = 2)
    private BigDecimal salary;

    @Column(precision = 12, scale = 2)
    private BigDecimal hourlyRate;

    @Enumerated(EnumType.STRING)
    private PayFrequency payFrequency;

    @Column(length = 3)
    @Builder.Default
    private String currency = "RY";

    /**
     * Manager and reporting structure
     */
    private String managerId;

    @Column(length = 100)
    private String managerName;

    /**
     * Leave and benefits
     */
    @Builder.Default
    private Integer annualLeaveDays = 21;

    @Builder.Default
    private Integer sickLeaveDays = 10;

    @Builder.Default
    private Integer usedAnnualLeave = 0;

    @Builder.Default
    private Integer usedSickLeave = 0;

    /**
     * Performance and development
     */
    private LocalDate lastPerformanceReview;

    private LocalDate nextPerformanceReview;

    @Column(precision = 3, scale = 2)
    private BigDecimal performanceRating;

    @Column(columnDefinition = "TEXT")
    private String performanceNotes;

    /**
     * Emergency contact
     */
    @Column(length = 100)
    private String emergencyContactName;

    @Column(length = 20)
    private String emergencyContactPhone;

    @Column(length = 50)
    private String emergencyContactRelation;

    /**
     * Company/Tenant information
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * Additional information
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    @Column(length = 500)
    private String skills;

    @Column(length = 500)
    private String certifications;

    @Column(length = 200)
    private String education;

    /**
     * System fields
     */
    @Builder.Default
    private boolean isActive = true;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Gender Enum
     */
    public enum Gender {
        MALE,
        FEMALE,
        OTHER,
        PREFER_NOT_TO_SAY
    }

    /**
     * Employee Status Enum
     */
    public enum EmployeeStatus {
        ACTIVE,
        INACTIVE,
        PROBATION,
        TERMINATED,
        SUSPENDED,
        ON_LEAVE
    }

    /**
     * Employment Type Enum
     */
    public enum EmploymentType {
        FULL_TIME,
        PART_TIME,
        CONTRACT,
        TEMPORARY,
        INTERN,
        CONSULTANT
    }

    /**
     * Pay Frequency Enum
     */
    public enum PayFrequency {
        WEEKLY,
        BI_WEEKLY,
        MONTHLY,
        QUARTERLY,
        ANNUALLY
    }

    /**
     * Get full name
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }

    /**
     * Check if employee is on probation
     */
    public boolean isOnProbation() {
        return probationEndDate != null && LocalDate.now().isBefore(probationEndDate);
    }

    /**
     * Check if performance review is due
     */
    public boolean isPerformanceReviewDue() {
        return nextPerformanceReview != null && 
               (LocalDate.now().isAfter(nextPerformanceReview) || 
                LocalDate.now().isEqual(nextPerformanceReview));
    }

    /**
     * Calculate years of service
     */
    public int getYearsOfService() {
        if (hireDate == null) return 0;
        LocalDate endDate = terminationDate != null ? terminationDate : LocalDate.now();
        return endDate.getYear() - hireDate.getYear();
    }

    /**
     * Calculate remaining annual leave
     */
    public int getRemainingAnnualLeave() {
        return annualLeaveDays - usedAnnualLeave;
    }

    /**
     * Calculate remaining sick leave
     */
    public int getRemainingSickLeave() {
        return sickLeaveDays - usedSickLeave;
    }

    /**
     * Check if employee is eligible for benefits
     */
    public boolean isEligibleForBenefits() {
        return employmentType == EmploymentType.FULL_TIME && 
               status == EmployeeStatus.ACTIVE &&
               !isOnProbation();
    }

    /**
     * Calculate monthly salary
     */
    public BigDecimal getMonthlySalary() {
        if (salary == null) return BigDecimal.ZERO;
        
        return switch (payFrequency) {
            case WEEKLY -> salary.multiply(BigDecimal.valueOf(52)).divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP);
            case BI_WEEKLY -> salary.multiply(BigDecimal.valueOf(26)).divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP);
            case MONTHLY -> salary;
            case QUARTERLY -> salary.divide(BigDecimal.valueOf(3), 2, RoundingMode.HALF_UP);
            case ANNUALLY -> salary.divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP);
        };
    }

    /**
     * Calculate annual salary
     */
    public BigDecimal getAnnualSalary() {
        if (salary == null) return BigDecimal.ZERO;
        
        return switch (payFrequency) {
            case WEEKLY -> salary.multiply(BigDecimal.valueOf(52));
            case BI_WEEKLY -> salary.multiply(BigDecimal.valueOf(26));
            case MONTHLY -> salary.multiply(BigDecimal.valueOf(12));
            case QUARTERLY -> salary.multiply(BigDecimal.valueOf(4));
            case ANNUALLY -> salary;
        };
    }
}
