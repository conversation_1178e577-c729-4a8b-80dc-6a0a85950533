package com.tecnodrive.analyticsservice.controller;

import com.tecnodrive.analyticsservice.dto.ReportRequest;
import com.tecnodrive.analyticsservice.dto.ReportResponse;
import com.tecnodrive.analyticsservice.service.ReportService;
import com.tecnodrive.common.dto.common.ApiResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Report Controller
 *
 * REST API endpoints for analytics and report generation
 */
@Slf4j
@RestController
@RequestMapping("/api/analytics")
@RequiredArgsConstructor
public class ReportController {

    private final ReportService reportService;

    /**
     * Generate a report with multiple format support
     */
    @PostMapping(value = "/reports", produces = {
            MediaType.APPLICATION_JSON_VALUE,
            "text/csv",
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            MediaType.TEXT_HTML_VALUE
    })
    public ResponseEntity<?> generateReport(@Valid @RequestBody ReportRequest request) {
        log.info("Generating report: {} for company: {}", request.getReportType(), request.getCompanyId());
        
        try {
            ReportResponse report = reportService.generateReport(request);

            return switch (request.getFormat()) {
                case CSV -> ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.csv")
                        .contentType(MediaType.parseMediaType("text/csv"))
                        .body(reportService.exportToCsv(report));

                case PDF -> ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.pdf")
                        .contentType(MediaType.APPLICATION_PDF)
                        .body(reportService.exportToPdf(report));

                case XLSX -> ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.xlsx")
                        .contentType(MediaType.parseMediaType(
                                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                        .body(reportService.exportToExcel(report));

                case HTML -> ResponseEntity.ok()
                        .contentType(MediaType.TEXT_HTML)
                        .body(reportService.exportToHtml(report));

                default -> ResponseEntity.ok(ApiResponse.success(report));
            };
            
        } catch (Exception e) {
            log.error("Error generating report: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to generate report: " + e.getMessage()));
        }
    }

    /**
     * Get dashboard metrics for real-time display
     */
    @GetMapping("/dashboard/{companyId}")
    public ResponseEntity<ApiResponse<ReportResponse>> getDashboardMetrics(@PathVariable String companyId) {
        log.debug("Getting dashboard metrics for company: {}", companyId);
        
        try {
            ReportResponse metrics = reportService.getDashboardMetrics(companyId);
            return ResponseEntity.ok(ApiResponse.success(metrics));
            
        } catch (Exception e) {
            log.error("Error getting dashboard metrics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get dashboard metrics: " + e.getMessage()));
        }
    }

    /**
     * Get financial summary
     */
    @GetMapping("/financial-summary/{companyId}")
    public ResponseEntity<ApiResponse<ReportResponse.FinancialSummary>> getFinancialSummary(
            @PathVariable String companyId,
            @RequestParam(defaultValue = "LAST_30_DAYS") ReportRequest.Period period) {
        
        log.debug("Getting financial summary for company: {} and period: {}", companyId, period);
        
        try {
            ReportResponse.FinancialSummary summary = reportService.getFinancialSummary(companyId, period);
            return ResponseEntity.ok(ApiResponse.success(summary));
            
        } catch (Exception e) {
            log.error("Error getting financial summary: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get financial summary: " + e.getMessage()));
        }
    }

    /**
     * Get operational metrics
     */
    @GetMapping("/operational-metrics/{companyId}")
    public ResponseEntity<ApiResponse<ReportResponse.OperationalMetrics>> getOperationalMetrics(
            @PathVariable String companyId,
            @RequestParam(defaultValue = "LAST_30_DAYS") ReportRequest.Period period) {
        
        log.debug("Getting operational metrics for company: {} and period: {}", companyId, period);
        
        try {
            ReportResponse.OperationalMetrics metrics = reportService.getOperationalMetrics(companyId, period);
            return ResponseEntity.ok(ApiResponse.success(metrics));
            
        } catch (Exception e) {
            log.error("Error getting operational metrics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get operational metrics: " + e.getMessage()));
        }
    }

    /**
     * Get user analytics
     */
    @GetMapping("/user-analytics/{companyId}")
    public ResponseEntity<ApiResponse<ReportResponse.UserAnalytics>> getUserAnalytics(
            @PathVariable String companyId,
            @RequestParam(defaultValue = "LAST_30_DAYS") ReportRequest.Period period) {
        
        log.debug("Getting user analytics for company: {} and period: {}", companyId, period);
        
        try {
            ReportResponse.UserAnalytics analytics = reportService.getUserAnalytics(companyId, period);
            return ResponseEntity.ok(ApiResponse.success(analytics));
            
        } catch (Exception e) {
            log.error("Error getting user analytics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get user analytics: " + e.getMessage()));
        }
    }

    /**
     * Generate custom report with SQL query
     */
    @PostMapping("/custom-report/{companyId}")
    public ResponseEntity<ApiResponse<ReportResponse>> generateCustomReport(
            @PathVariable String companyId,
            @RequestParam String query,
            @RequestParam(defaultValue = "JSON") ReportRequest.Format format) {
        
        log.info("Generating custom report for company: {} with format: {}", companyId, format);
        
        try {
            ReportResponse report = reportService.generateCustomReport(companyId, query, format);
            return ResponseEntity.ok(ApiResponse.success(report));
            
        } catch (Exception e) {
            log.error("Error generating custom report: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to generate custom report: " + e.getMessage()));
        }
    }

    /**
     * Get available report types for a company
     */
    @GetMapping("/report-types/{companyId}")
    public ResponseEntity<ApiResponse<List<ReportRequest.ReportType>>> getAvailableReportTypes(
            @PathVariable String companyId) {
        
        log.debug("Getting available report types for company: {}", companyId);
        
        try {
            List<ReportRequest.ReportType> reportTypes = reportService.getAvailableReportTypes(companyId);
            return ResponseEntity.ok(ApiResponse.success(reportTypes));
            
        } catch (Exception e) {
            log.error("Error getting report types: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get report types: " + e.getMessage()));
        }
    }

    /**
     * Schedule a report for automatic generation
     */
    @PostMapping("/schedule-report")
    public ResponseEntity<ApiResponse<String>> scheduleReport(
            @Valid @RequestBody ReportRequest request,
            @RequestParam String cronExpression) {
        
        log.info("Scheduling report: {} with cron: {}", request.getReportType(), cronExpression);
        
        try {
            reportService.scheduleReport(request, cronExpression);
            return ResponseEntity.ok(ApiResponse.success("Report scheduled successfully"));
            
        } catch (Exception e) {
            log.error("Error scheduling report: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to schedule report: " + e.getMessage()));
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        return ResponseEntity.ok(ApiResponse.success("Analytics service is running"));
    }
}
