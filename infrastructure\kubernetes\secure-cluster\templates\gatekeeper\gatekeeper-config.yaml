{{- if .Values.gatekeeper.enabled }}
apiVersion: config.gatekeeper.sh/v1alpha1
kind: Config
metadata:
  name: config
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: gatekeeper-config
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  # Sync configuration
  sync:
    syncOnly:
      - group: ""
        version: "v1"
        kind: "Namespace"
      - group: ""
        version: "v1"
        kind: "Pod"
      - group: "apps"
        version: "v1"
        kind: "Deployment"
      - group: "apps"
        version: "v1"
        kind: "StatefulSet"
      - group: "apps"
        version: "v1"
        kind: "DaemonSet"
      - group: "batch"
        version: "v1"
        kind: "Job"
      - group: "batch"
        version: "v1"
        kind: "CronJob"
      - group: "networking.k8s.io"
        version: "v1"
        kind: "NetworkPolicy"
      - group: "policy"
        version: "v1"
        kind: "PodDisruptionBudget"
      - group: "extensions"
        version: "v1beta1"
        kind: "Ingress"
      - group: "networking.k8s.io"
        version: "v1"
        kind: "Ingress"
  
  # Validation configuration
  validation:
    # Traces for debugging
    traces:
      - user:
          kind:
            group: "*"
            version: "*"
            kind: "*"
        kind:
          group: "*"
          version: "*"
          kind: "*"
  
  # Match configuration
  match:
    - excludedNamespaces: 
        {{- if .Values.gatekeeper.exemptNamespaces }}
        {{- toYaml .Values.gatekeeper.exemptNamespaces | nindent 8 }}
        {{- else }}
        - "kube-system"
        - "kube-public"
        - "kube-node-lease"
        - "gatekeeper-system"
        {{- end }}
      processes: ["*"]
  
  # Readiness configuration
  readiness:
    statsEnabled: true

---
# Gatekeeper System Namespace Configuration
apiVersion: v1
kind: Namespace
metadata:
  name: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: gatekeeper-system
    app.kubernetes.io/instance: {{ .Release.Name }}
    control-plane: controller-manager
    gatekeeper.sh/system: "yes"
    admission.gatekeeper.sh/ignore: "no-self-managing"
    pod-security.kubernetes.io/enforce: privileged
    pod-security.kubernetes.io/audit: privileged
    pod-security.kubernetes.io/warn: privileged

---
# Gatekeeper Mutation Configuration
{{- if .Values.gatekeeper.mutation.enabled }}
apiVersion: mutations.gatekeeper.sh/v1alpha1
kind: Assign
metadata:
  name: add-security-context
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: gatekeeper-mutation
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  applyTo:
    - groups: ["apps"]
      kinds: ["Deployment", "StatefulSet", "DaemonSet"]
      versions: ["v1"]
  match:
    scope: Namespaced
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
    excludedNamespaces: 
      - "kube-system"
      - "kube-public"
      - "kube-node-lease"
      - "gatekeeper-system"
      - "argocd"
      - "monitoring"
      - "logging"
  location: "spec.template.spec.securityContext"
  parameters:
    assign:
      value:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 3000
        seccompProfile:
          type: RuntimeDefault

---
apiVersion: mutations.gatekeeper.sh/v1alpha1
kind: Assign
metadata:
  name: add-container-security-context
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: gatekeeper-mutation
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  applyTo:
    - groups: ["apps"]
      kinds: ["Deployment", "StatefulSet", "DaemonSet"]
      versions: ["v1"]
  match:
    scope: Namespaced
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment", "StatefulSet", "DaemonSet"]
    excludedNamespaces: 
      - "kube-system"
      - "kube-public"
      - "kube-node-lease"
      - "gatekeeper-system"
      - "argocd"
      - "monitoring"
      - "logging"
  location: "spec.template.spec.containers[name:*].securityContext"
  parameters:
    assign:
      value:
        allowPrivilegeEscalation: false
        capabilities:
          drop: ["ALL"]
        readOnlyRootFilesystem: true
{{- end }}

---
# Gatekeeper Metrics Configuration
apiVersion: v1
kind: Service
metadata:
  name: gatekeeper-controller-manager-metrics-service
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: gatekeeper-metrics
    app.kubernetes.io/instance: {{ .Release.Name }}
    control-plane: controller-manager
    gatekeeper.sh/system: "yes"
spec:
  ports:
    - name: https
      port: 8443
      protocol: TCP
      targetPort: https
  selector:
    control-plane: controller-manager
    gatekeeper.sh/operation: webhook
    gatekeeper.sh/system: "yes"

---
# ServiceMonitor for Prometheus
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gatekeeper-controller-manager-metrics
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: gatekeeper-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
    control-plane: controller-manager
    gatekeeper.sh/system: "yes"
spec:
  endpoints:
    - interval: 30s
      path: /metrics
      port: https
      scheme: https
      tlsConfig:
        insecureSkipVerify: true
  selector:
    matchLabels:
      control-plane: controller-manager
      gatekeeper.sh/system: "yes"
{{- end }}

---
# Gatekeeper Webhook Configuration
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: gatekeeper-validating-webhook-configuration
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook
    app.kubernetes.io/instance: {{ .Release.Name }}
    gatekeeper.sh/system: "yes"
webhooks:
  - name: validation.gatekeeper.sh
    clientConfig:
      service:
        name: gatekeeper-webhook-service
        namespace: gatekeeper-system
        path: "/v1/admit"
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: ["*"]
        apiVersions: ["*"]
        resources: ["*"]
    namespaceSelector:
      matchExpressions:
        - key: admission.gatekeeper.sh/ignore
          operator: DoesNotExist
        - key: name
          operator: NotIn
          values: ["gatekeeper-system"]
    failurePolicy: {{ .Values.gatekeeper.failurePolicy | default "Ignore" }}
    sideEffects: None
    admissionReviewVersions: ["v1", "v1beta1"]
    timeoutSeconds: {{ .Values.gatekeeper.timeoutSeconds | default 3 }}

---
# Emergency Break Glass ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: gatekeeper-emergency-procedures
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: gatekeeper-emergency
    app.kubernetes.io/instance: {{ .Release.Name }}
data:
  emergency-procedures.md: |
    # Gatekeeper Emergency Procedures
    
    ## Emergency Disable (Break Glass)
    
    ### 1. Disable All Constraints
    ```bash
    kubectl patch config config -n gatekeeper-system --type='merge' -p='{"spec":{"validation":{"traces":[{"user":{"kind":{"group":"*","version":"*","kind":"*"}},"kind":{"group":"*","version":"*","kind":"*"}}]}}}'
    ```
    
    ### 2. Set Constraints to Warn Mode
    ```bash
    kubectl get constraints -o name | xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
    ```
    
    ### 3. Disable Webhook (EXTREME CAUTION)
    ```bash
    kubectl delete validatingadmissionwebhook gatekeeper-validating-webhook-configuration
    ```
    
    ### 4. Re-enable After Emergency
    ```bash
    # Re-apply the webhook
    kubectl apply -f gatekeeper-webhook.yaml
    
    # Reset constraints to deny mode
    kubectl get constraints -o name | xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"deny"}}'
    ```
    
    ## Troubleshooting
    
    ### Check Gatekeeper Status
    ```bash
    kubectl get pods -n gatekeeper-system
    kubectl logs -n gatekeeper-system -l control-plane=controller-manager
    ```
    
    ### Check Constraint Violations
    ```bash
    kubectl get constraints -o yaml | grep -A 10 violations
    ```
    
    ### Test Policy
    ```bash
    kubectl apply --dry-run=server -f test-resource.yaml
    ```

  contact-info.yaml: |
    emergency_contacts:
      - name: "DevOps Team"
        email: "<EMAIL>"
        phone: "+966-XXX-XXXX"
        role: "Primary"
      
      - name: "Security Team"
        email: "<EMAIL>"
        phone: "+966-XXX-XXXX"
        role: "Security Incidents"
      
      - name: "Platform Team"
        email: "<EMAIL>"
        phone: "+966-XXX-XXXX"
        role: "Infrastructure"
{{- end }}
