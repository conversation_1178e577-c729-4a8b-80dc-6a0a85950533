import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Button,
  TextField,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Alert,
  AlertTitle,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Visibility as VisibilityIcon,
  Timeline as TimelineIcon,
  NewReleases as NewReleasesIcon,
  Announcement as AnnouncementIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';

// Types for Version Management
interface ApiVersionDto {
  id: string;
  version: string;
  status: 'ACTIVE' | 'DEPRECATED' | 'SUNSET' | 'RETIRED';
  releaseDate: string;
  deprecationDate?: string;
  sunsetDate?: string;
  retirementDate?: string;
  description: string;
  breakingChanges: string[];
  migrationGuide?: string;
  usageCount: number;
  consumers: string[];
  createdAt: string;
  updatedAt: string;
}

interface DeprecationNoticeDto {
  id: string;
  versionId: string;
  title: string;
  message: string;
  severity: 'INFO' | 'WARNING' | 'CRITICAL';
  notificationDate: string;
  targetAudience: string[];
  channels: ('EMAIL' | 'WEBHOOK' | 'DASHBOARD')[];
  sent: boolean;
  createdAt: string;
}

const VersionManagement: React.FC = () => {
  const [versions, setVersions] = useState<ApiVersionDto[]>([]);
  const [notices, setNotices] = useState<DeprecationNoticeDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [openVersionDialog, setOpenVersionDialog] = useState(false);
  const [openNoticeDialog, setOpenNoticeDialog] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<ApiVersionDto | null>(null);

  // Mock data
  const mockVersions: ApiVersionDto[] = [
    {
      id: 'v1',
      version: '1.0.0',
      status: 'DEPRECATED',
      releaseDate: '2023-01-15T00:00:00Z',
      deprecationDate: '2024-06-01T00:00:00Z',
      sunsetDate: '2025-01-01T00:00:00Z',
      retirementDate: '2025-06-01T00:00:00Z',
      description: 'الإصدار الأول من API مع المميزات الأساسية',
      breakingChanges: ['تغيير في هيكل response للمصادقة', 'إزالة endpoint /legacy/auth'],
      migrationGuide: 'https://docs.tecno-drive.com/migration/v1-to-v2',
      usageCount: 15000,
      consumers: ['mobile-app-legacy', 'partner-api-old'],
      createdAt: '2023-01-15T08:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z',
    },
    {
      id: 'v2',
      version: '2.0.0',
      status: 'ACTIVE',
      releaseDate: '2024-06-01T00:00:00Z',
      description: 'الإصدار الثاني مع تحسينات الأداء والأمان',
      breakingChanges: ['تحديث في JWT token structure', 'تغيير في pagination parameters'],
      migrationGuide: 'https://docs.tecno-drive.com/migration/v2',
      usageCount: 45000,
      consumers: ['mobile-app', 'web-dashboard', 'partner-api'],
      createdAt: '2024-06-01T08:00:00Z',
      updatedAt: '2025-07-09T14:30:00Z',
    },
    {
      id: 'v3',
      version: '3.0.0-beta',
      status: 'ACTIVE',
      releaseDate: '2025-07-01T00:00:00Z',
      description: 'الإصدار التجريبي الثالث مع GraphQL support',
      breakingChanges: [],
      usageCount: 2500,
      consumers: ['beta-testers'],
      createdAt: '2025-07-01T08:00:00Z',
      updatedAt: '2025-07-09T14:30:00Z',
    },
  ];

  const mockNotices: DeprecationNoticeDto[] = [
    {
      id: 'notice-1',
      versionId: 'v1',
      title: 'تحذير: الإصدار 1.0.0 سيتم إيقافه قريباً',
      message: 'سيتم إيقاف الإصدار 1.0.0 في 1 يناير 2025. يرجى الترقية إلى الإصدار 2.0.0',
      severity: 'WARNING',
      notificationDate: '2024-10-01T00:00:00Z',
      targetAudience: ['mobile-app-legacy', 'partner-api-old'],
      channels: ['EMAIL', 'WEBHOOK', 'DASHBOARD'],
      sent: true,
      createdAt: '2024-10-01T08:00:00Z',
    },
    {
      id: 'notice-2',
      versionId: 'v1',
      title: 'تحذير نهائي: إيقاف الإصدار 1.0.0',
      message: 'هذا تحذير نهائي. سيتم إيقاف الإصدار 1.0.0 نهائياً في 1 يونيو 2025',
      severity: 'CRITICAL',
      notificationDate: '2025-03-01T00:00:00Z',
      targetAudience: ['mobile-app-legacy', 'partner-api-old'],
      channels: ['EMAIL', 'WEBHOOK', 'DASHBOARD'],
      sent: false,
      createdAt: '2025-03-01T08:00:00Z',
    },
  ];

  useEffect(() => {
    setVersions(mockVersions);
    setNotices(mockNotices);
  }, []);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      DEPRECATED: { label: 'مهجور', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
      SUNSET: { label: 'في طور الإيقاف', color: 'error' as const, icon: <ScheduleIcon fontSize="small" /> },
      RETIRED: { label: 'متقاعد', color: 'default' as const, icon: <DeleteIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getSeverityChip = (severity: string) => {
    const severityConfig = {
      INFO: { label: 'معلومات', color: 'info' as const },
      WARNING: { label: 'تحذير', color: 'warning' as const },
      CRITICAL: { label: 'حرج', color: 'error' as const },
    };

    const config = severityConfig[severity as keyof typeof severityConfig] || { 
      label: severity, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const getVersionLifecycleProgress = (version: ApiVersionDto) => {
    const now = new Date();
    const releaseDate = new Date(version.releaseDate);
    const retirementDate = version.retirementDate ? new Date(version.retirementDate) : null;
    
    if (!retirementDate) return 25; // Active version
    
    const totalLifetime = retirementDate.getTime() - releaseDate.getTime();
    const elapsed = now.getTime() - releaseDate.getTime();
    
    return Math.min(100, Math.max(0, (elapsed / totalLifetime) * 100));
  };

  const getDaysUntilRetirement = (version: ApiVersionDto) => {
    if (!version.retirementDate) return null;
    
    const now = new Date();
    const retirementDate = new Date(version.retirementDate);
    const diffTime = retirementDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
  };

  const versionColumns: GridColDef[] = [
    {
      field: 'version',
      headerName: 'الإصدار',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <NewReleasesIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'bold', fontFamily: 'monospace' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'releaseDate',
      headerName: 'تاريخ الإصدار',
      width: 130,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'usageCount',
      headerName: 'عدد الاستخدامات',
      width: 130,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'consumers',
      headerName: 'المستهلكين',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={`${(params.value as string[]).length} مستهلك`}
          size="small"
          color="info"
          variant="outlined"
        />
      ),
    },
    {
      field: 'lifecycle',
      headerName: 'دورة الحياة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => {
        const progress = getVersionLifecycleProgress(params.row);
        const daysLeft = getDaysUntilRetirement(params.row);
        
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              color={progress > 80 ? 'error' : progress > 60 ? 'warning' : 'primary'}
              sx={{ mb: 0.5 }}
            />
            <Typography variant="caption" color="text.secondary">
              {daysLeft !== null ? `${daysLeft} يوم متبقي` : 'نشط'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض التفاصيل">
              <VisibilityIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => setSelectedVersion(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => console.log('Edit version:', params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="إنشاء إشعار">
              <AnnouncementIcon />
            </Tooltip>
          }
          label="إشعار"
          onClick={() => setOpenNoticeDialog(true)}
        />,
      ],
    },
  ];

  const noticeColumns: GridColDef[] = [
    {
      field: 'title',
      headerName: 'العنوان',
      width: 250,
    },
    {
      field: 'severity',
      headerName: 'الأهمية',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getSeverityChip(params.value),
    },
    {
      field: 'notificationDate',
      headerName: 'تاريخ الإشعار',
      width: 130,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'sent',
      headerName: 'تم الإرسال',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'مرسل' : 'معلق'}
          color={params.value ? 'success' : 'warning'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'targetAudience',
      headerName: 'الجمهور المستهدف',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={`${(params.value as string[]).length} مستهلك`}
          size="small"
          color="info"
          variant="outlined"
        />
      ),
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الإصدارات والتقادم
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة دورة حياة إصدارات API مع تحذيرات التقادم والإيقاف
        </Typography>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <CheckCircleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {versions.filter(v => v.status === 'ACTIVE').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إصدارات نشطة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <WarningIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {versions.filter(v => v.status === 'DEPRECATED').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إصدارات مهجورة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TimelineIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {versions.reduce((sum, v) => sum + v.usageCount, 0).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الاستخدامات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'error.main' }}>
                  <AnnouncementIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {notices.filter(n => !n.sent).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إشعارات معلقة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Deprecation Alerts */}
      {versions.filter(v => v.status === 'DEPRECATED' || v.status === 'SUNSET').map(version => {
        const daysLeft = getDaysUntilRetirement(version);
        return (
          <Alert 
            key={version.id}
            severity={daysLeft && daysLeft < 30 ? 'error' : 'warning'} 
            sx={{ mb: 2 }}
            action={
              <Button color="inherit" size="small">
                عرض دليل الترقية
              </Button>
            }
          >
            <AlertTitle>
              تحذير: الإصدار {version.version} {version.status === 'SUNSET' ? 'في طور الإيقاف' : 'مهجور'}
            </AlertTitle>
            {daysLeft !== null && daysLeft > 0 ? (
              `سيتم إيقاف هذا الإصدار نهائياً خلال ${daysLeft} يوم. يرجى الترقية إلى إصدار أحدث.`
            ) : (
              'هذا الإصدار مهجور. يرجى الترقية إلى إصدار أحدث في أقرب وقت ممكن.'
            )}
          </Alert>
        );
      })}

      {/* Versions Management */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">إدارة الإصدارات</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenVersionDialog(true)}
            >
              إضافة إصدار جديد
            </Button>
          </Box>
          <Box sx={{ height: 400, width: '100%' }}>
            <DataGrid
              rows={versions}
              columns={versionColumns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
              sx={{
                border: 0,
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Deprecation Notices */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">إشعارات التقادم</Typography>
            <Button
              variant="outlined"
              startIcon={<AnnouncementIcon />}
              onClick={() => setOpenNoticeDialog(true)}
            >
              إنشاء إشعار جديد
            </Button>
          </Box>
          <Box sx={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={notices}
              columns={noticeColumns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
              sx={{
                border: 0,
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Version Details Dialog */}
      <Dialog open={!!selectedVersion} onClose={() => setSelectedVersion(null)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل الإصدار {selectedVersion?.version}</DialogTitle>
        <DialogContent>
          {selectedVersion && (
            <Box sx={{ mt: 1 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">الحالة</Typography>
                  {getStatusChip(selectedVersion.status)}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">تاريخ الإصدار</Typography>
                  <Typography variant="body2">
                    {new Date(selectedVersion.releaseDate).toLocaleDateString('ar-SA')}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">الوصف</Typography>
                  <Typography variant="body2">{selectedVersion.description}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">التغييرات الجذرية</Typography>
                  <List dense>
                    {selectedVersion.breakingChanges.map((change, index) => (
                      <ListItem key={index}>
                        <ListItemText primary={change} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">المستهلكين</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {selectedVersion.consumers.map((consumer) => (
                      <Chip key={consumer} label={consumer} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Grid>
                {selectedVersion.migrationGuide && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">دليل الترقية</Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      href={selectedVersion.migrationGuide}
                      target="_blank"
                      sx={{ mt: 1 }}
                    >
                      عرض دليل الترقية
                    </Button>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedVersion(null)}>إغلاق</Button>
        </DialogActions>
      </Dialog>

      {/* Add Version Dialog */}
      <Dialog open={openVersionDialog} onClose={() => setOpenVersionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة إصدار جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="رقم الإصدار"
              placeholder="3.1.0"
              fullWidth
              required
            />
            <TextField
              label="الوصف"
              multiline
              rows={3}
              fullWidth
              required
            />
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select defaultValue="ACTIVE" label="الحالة">
                <MenuItem value="ACTIVE">نشط</MenuItem>
                <MenuItem value="DEPRECATED">مهجور</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="تاريخ الإصدار"
              type="date"
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenVersionDialog(false)}>إلغاء</Button>
          <Button variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* Add Notice Dialog */}
      <Dialog open={openNoticeDialog} onClose={() => setOpenNoticeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إنشاء إشعار تقادم جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>الإصدار</InputLabel>
              <Select label="الإصدار">
                {versions.map((version) => (
                  <MenuItem key={version.id} value={version.id}>
                    {version.version}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="عنوان الإشعار"
              fullWidth
              required
            />
            <TextField
              label="رسالة الإشعار"
              multiline
              rows={4}
              fullWidth
              required
            />
            <FormControl fullWidth>
              <InputLabel>مستوى الأهمية</InputLabel>
              <Select defaultValue="WARNING" label="مستوى الأهمية">
                <MenuItem value="INFO">معلومات</MenuItem>
                <MenuItem value="WARNING">تحذير</MenuItem>
                <MenuItem value="CRITICAL">حرج</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="تاريخ الإشعار"
              type="date"
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenNoticeDialog(false)}>إلغاء</Button>
          <Button variant="contained">إنشاء الإشعار</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VersionManagement;
