import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Subscriptions as SubscriptionsIcon,
  Receipt as ReceiptIcon,
  Analytics as AnalyticsIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import TenantsManagement from './TenantsManagement';
import SubscriptionsManagement from './SubscriptionsManagement';
import BillingManagement from './BillingManagement';
import UsageAnalytics from './UsageAnalytics';
import TenantDetail from './TenantDetail';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`saas-tabpanel-${index}`}
      aria-labelledby={`saas-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SaaSManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine active tab based on current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/tenants')) return 1;
    if (path.includes('/subscriptions')) return 2;
    if (path.includes('/billing')) return 3;
    if (path.includes('/analytics')) return 4;
    return 0; // Dashboard
  };

  const [tabValue, setTabValue] = React.useState(getActiveTab());

  React.useEffect(() => {
    setTabValue(getActiveTab());
  }, [location.pathname]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    const routes = ['', 'tenants', 'subscriptions', 'billing', 'analytics'];
    navigate(`/saas/${routes[newValue]}`);
  };

  // Mock data for dashboard
  const revenueData = [
    { month: 'يناير', revenue: 45000 },
    { month: 'فبراير', revenue: 52000 },
    { month: 'مارس', revenue: 48000 },
    { month: 'أبريل', revenue: 61000 },
    { month: 'مايو', revenue: 58000 },
    { month: 'يونيو', revenue: 67000 },
  ];

  const tenantDistribution = [
    { name: 'نشط', value: 12, color: '#4caf50' },
    { name: 'غير نشط', value: 3, color: '#ff9800' },
    { name: 'معلق', value: 1, color: '#f44336' },
  ];

  const recentActivities = [
    { id: 1, type: 'tenant', message: 'تم إنشاء عميل جديد: شركة التقنية المتقدمة', time: '5 دقائق' },
    { id: 2, type: 'subscription', message: 'تم تجديد اشتراك: مؤسسة التجارة الذكية', time: '15 دقيقة' },
    { id: 3, type: 'billing', message: 'تم دفع فاتورة: INV-2025-001', time: '30 دقيقة' },
    { id: 4, type: 'usage', message: 'تجاوز الحد المسموح: استدعاءات API', time: '1 ساعة' },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'tenant': return <BusinessIcon />;
      case 'subscription': return <SubscriptionsIcon />;
      case 'billing': return <ReceiptIcon />;
      case 'usage': return <AnalyticsIcon />;
      default: return <DashboardIcon />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'tenant': return 'primary';
      case 'subscription': return 'success';
      case 'billing': return 'warning';
      case 'usage': return 'info';
      default: return 'default';
    }
  };

  return (
    <Routes>
      <Route path="/tenants/*" element={<TenantsManagement />} />
      <Route path="/subscriptions" element={<SubscriptionsManagement />} />
      <Route path="/billing" element={<BillingManagement />} />
      <Route path="/analytics" element={<UsageAnalytics />} />
      <Route path="/*" element={
        <Box>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              إدارة SaaS
            </Typography>
            <Typography variant="body1" color="text.secondary">
              لوحة تحكم شاملة لإدارة العملاء والاشتراكات والفوترة
            </Typography>
          </Box>

          {/* Navigation Tabs */}
          <Card sx={{ mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab 
                  icon={<DashboardIcon />} 
                  label="لوحة التحكم" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<BusinessIcon />} 
                  label="العملاء" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<SubscriptionsIcon />} 
                  label="الاشتراكات" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<ReceiptIcon />} 
                  label="الفوترة" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<AnalyticsIcon />} 
                  label="التحليلات" 
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            {/* Dashboard Tab Content */}
            <TabPanel value={tabValue} index={0}>
              {/* Quick Stats */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <BusinessIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            16
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            إجمالي العملاء
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <SubscriptionsIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            24
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            الاشتراكات النشطة
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'warning.main' }}>
                          <MoneyIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            67,000
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            الإيرادات الشهرية (ريال)
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'info.main' }}>
                          <PeopleIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            342
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            إجمالي المستخدمين
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Charts and Analytics */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} md={8}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        اتجاه الإيرادات الشهرية
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={revenueData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Line 
                            type="monotone" 
                            dataKey="revenue" 
                            stroke="#1976d2" 
                            strokeWidth={3}
                            name="الإيرادات (ريال)"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        توزيع العملاء
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={tenantDistribution}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          >
                            {tenantDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Recent Activities and Quick Actions */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        الأنشطة الأخيرة
                      </Typography>
                      <List>
                        {recentActivities.map((activity) => (
                          <ListItem key={activity.id}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: `${getActivityColor(activity.type)}.main` }}>
                                {getActivityIcon(activity.type)}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={activity.message}
                              secondary={`منذ ${activity.time}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        إجراءات سريعة
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button
                          variant="contained"
                          startIcon={<BusinessIcon />}
                          onClick={() => navigate('/saas/tenants')}
                          fullWidth
                        >
                          إضافة عميل جديد
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<SubscriptionsIcon />}
                          onClick={() => navigate('/saas/subscriptions')}
                          fullWidth
                        >
                          إدارة الاشتراكات
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<ReceiptIcon />}
                          onClick={() => navigate('/saas/billing')}
                          fullWidth
                        >
                          إنشاء فاتورة
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<AnalyticsIcon />}
                          onClick={() => navigate('/saas/analytics')}
                          fullWidth
                        >
                          عرض التحليلات
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Other tabs will show their respective components */}
            <TabPanel value={tabValue} index={1}>
              <TenantsManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
              <SubscriptionsManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={3}>
              <BillingManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={4}>
              <UsageAnalytics />
            </TabPanel>
          </Card>
        </Box>
      } />
    </Routes>
  );
};

export default SaaSManagement;
