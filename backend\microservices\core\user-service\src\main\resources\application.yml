server:
  port: 8083
  servlet:
    context-path: /user-service

spring:
  application:
    name: user-service

  profiles:
    active: h2-dev
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_users
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:TecnoDrive2025!Redis#Cache}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # Cache disabled for development
  # cache:
  #   type: redis
  #   redis:
  #     time-to-live: 600000 # 10 minutes
  #     cache-null-values: false
  
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
    healthcheck:
      enabled: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: 1.0.0
      description: "User Management Service"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    redis:
      enabled: true
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.tecnodrive.userservice: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/user-service.log

# Custom application properties
app:
  security:
    jwt:
      secret: ${JWT_SECRET:user-service-secret-key-for-development-only}
      expiration: 86400000 # 24 hours
  
  cache:
    users:
      ttl: 600 # 10 minutes
    publicProfiles:
      ttl: 1800 # 30 minutes
    userStatistics:
      ttl: 3600 # 1 hour
  
  pagination:
    default-page-size: 20
    max-page-size: 100
  
  validation:
    email:
      pattern: "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    phone:
      pattern: "^\\+?[1-9]\\d{1,14}$"

---
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: *************************************************_dev
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

logging:
  level:
    com.tecnodrive.userservice: DEBUG
    org.springframework.web: DEBUG

---
spring:
  config:
    activate:
      on-profile: h2-dev

  datasource:
    url: jdbc:h2:mem:tecnodrive_users_dev
    driver-class-name: org.h2.Driver
    username: sa
    password:

  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: true

  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis disabled for development
  # redis:
  #   host: localhost
  #   port: 6379
  #   password:
  #   timeout: 2000ms

logging:
  level:
    com.tecnodrive.userservice: DEBUG
    org.springframework.web: DEBUG

---
spring:
  config:
    activate:
      on-profile: test

  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:

  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect

  h2:
    console:
      enabled: true

---
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: ${DATABASE_URL:*************************************************}
    username: ${DATABASE_USERNAME:tecnodrive_user}
    password: ${DATABASE_PASSWORD:tecnodrive_pass}
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URL:http://localhost:8761/eureka/}

logging:
  level:
    com.tecnodrive.userservice: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
