import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
} from '@mui/icons-material';
import Sidebar from './Sidebar';
import Header from './Header';
import Dashboard from '../Dashboard/Dashboard';
import RidesManagement from '../Rides/RidesManagement';
import FleetManagement from '../Fleet/FleetManagement';
import UsersManagement from '../Users/<USER>';
import PaymentsManagement from '../Payments/PaymentsManagement';
import Analytics from '../Analytics/Analytics';
import ServiceHealth from '../ServiceHealth/ServiceHealth';
import ParcelsManagement from '../Parcels/ParcelsManagement';
import FleetMap from '../Fleet/FleetMap';
import SaaSManagement from '../SaaS/SaaSManagement';
import ApiGatewayManagement from '../ApiGateway/ApiGatewayManagement';
import NotificationManagement from '../Notifications/NotificationManagement';
import RideRatings from '../Rides/RideRatings';
import DataImportExport from '../DataManagement/DataImportExport';
import OracleApexIntegration from '../Integration/OracleApexIntegration';
import FinanceManagement from '../Finance/FinanceManagement';
import HRManagement from '../HR/HRManagement';
import Settings from '../Settings/Settings';
import LoginPage from '../Auth/LoginPage';
import MultiAuthSystem from '../Auth/MultiAuthSystem';
import Login from '../Auth/Login';
import ControlPanel from '../ControlPanel/ControlPanel';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { initializeWebSocket, cleanupWebSocket } from '../../services/websocketService';

const drawerWidth = 280;

const Layout: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  const handleDrawerToggle = () => {
    if (isMobile) {
      setMobileOpen(!mobileOpen);
    } else {
      setDrawerOpen(!drawerOpen);
    }
  };

  const handleMobileDrawerClose = () => {
    setMobileOpen(false);
  };

  // Initialize WebSocket when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      initializeWebSocket();
    } else {
      cleanupWebSocket();
    }

    return () => {
      cleanupWebSocket();
    };
  }, [isAuthenticated]);

  if (!isAuthenticated) {
    return <Login />;
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' }}>
      {/* Enhanced App Bar */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { md: drawerOpen ? `calc(100% - ${drawerWidth}px)` : '100%' },
          ml: { md: drawerOpen ? `${drawerWidth}px` : 0 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar sx={{ minHeight: '70px !important' }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              background: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.2)',
              },
              borderRadius: 2,
            }}
          >
            {drawerOpen && !isMobile ? <ChevronLeftIcon /> : <MenuIcon />}
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
              }}
            >
              🚀
            </Box>
            <Typography
              variant="h5"
              noWrap
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 700,
                background: 'linear-gradient(45deg, #ffffff 30%, #f0f8ff 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              منصة تكنو درايف
            </Typography>
          </Box>
          <Header />
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: drawerOpen ? drawerWidth : 0 }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleMobileDrawerClose}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          <Sidebar onItemClick={handleMobileDrawerClose} />
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="persistent"
          open={drawerOpen}
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          <Sidebar />
        </Drawer>
      </Box>

      {/* Enhanced Main content */}
      <Box
        component="main"
        className="fade-in"
        sx={{
          flexGrow: 1,
          p: { xs: 2, md: 3 },
          width: { md: drawerOpen ? `calc(100% - ${drawerWidth}px)` : '100%' },
          mt: { xs: 9, md: 10 }, // Account for enhanced AppBar height
          minHeight: 'calc(100vh - 80px)',
          background: 'transparent',
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          '& > *': {
            animation: 'slideUp 0.6s ease-out',
          },
        }}
      >
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/control-panel" element={<ControlPanel />} />
          <Route path="/rides/*" element={<RidesManagement />} />
          <Route path="/fleet/*" element={<FleetManagement />} />
          <Route path="/fleet-map" element={<FleetMap />} />
          <Route path="/users/*" element={<UsersManagement />} />
          <Route path="/payments/*" element={<PaymentsManagement />} />
          <Route path="/finance/*" element={<FinanceManagement />} />
          <Route path="/hr/*" element={<HRManagement />} />
          <Route path="/analytics" element={<Analytics />} />
          <Route path="/parcels/*" element={<ParcelsManagement />} />
          <Route path="/saas/*" element={<SaaSManagement />} />
          <Route path="/api-gateway/*" element={<ApiGatewayManagement />} />
          <Route path="/notifications" element={<NotificationManagement />} />
          <Route path="/ride-ratings" element={<RideRatings />} />
          <Route path="/data-management" element={<DataImportExport />} />
          <Route path="/oracle-apex" element={<OracleApexIntegration />} />
          <Route path="/multi-auth" element={<MultiAuthSystem />} />
          <Route path="/services" element={<ServiceHealth />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Box>
    </Box>
  );
};

export default Layout;
