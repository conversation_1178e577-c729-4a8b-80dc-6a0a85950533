# 🎨 دليل استكشاف الواجهة - TecnoDrive Platform

## 🌐 الوصول للواجهة

**الرابط الرئيسي**: http://localhost:3000

## 📱 الصفحات الرئيسية

### 1. 🏠 الصفحة الرئيسية (Dashboard)
**الرابط**: http://localhost:3000/

**الميزات المتاحة:**
- ✅ نظرة عامة على الإحصائيات
- ✅ مؤشرات الأداء الرئيسية (KPIs)
- ✅ الرسوم البيانية السريعة
- ✅ التنبيهات الحديثة

**ما يمكنك فعله:**
- مراجعة الإحصائيات اليومية
- مراقبة حالة النظام العامة
- الوصول السريع للميزات الأخرى

### 2. 🗺️ لوحة التحكم المباشرة (Live Operations)
**الرابط**: http://localhost:3000/live-operations

**الميزات المتاحة:**
- ✅ **خريطة تفاعلية حية** مع Google Maps
- ✅ **تتبع المركبات** في الوقت الفعلي
- ✅ **مواقع السائقين** مع معلومات الحالة
- ✅ **تتبع الطرود** أثناء التوصيل
- ✅ **نظام التنبيهات** المباشر
- ✅ **إحصائيات فورية** (مركبات نشطة، رحلات جارية، طرود قيد التوصيل)

**كيفية الاستخدام:**
1. **عرض الخريطة**: ستظهر خريطة الرياض مع المركبات
2. **التفاعل مع العلامات**: انقر على أي مركبة لرؤية التفاصيل
3. **أدوات التحكم**: استخدم zoom in/out وإعادة التوسيط
4. **التنبيهات**: راقب اللوحة الجانبية للتنبيهات الحية
5. **الإحصائيات**: تحديث تلقائي كل 5 ثوانٍ

**اختبار الميزات:**
```bash
# إرسال تحديث موقع تجريبي
curl -X POST http://localhost:8085/api/locations/update \
  -H "Content-Type: application/json" \
  -d '{
    "vehicleId": "vehicle_001",
    "lat": 24.7136,
    "lng": 46.6753,
    "speed": 45.5,
    "heading": 180,
    "status": "busy",
    "driverName": "Ahmed Al-Rashid"
  }'
```

### 3. 🚗 إدارة الرحلات (Rides Management)
**الرابط**: http://localhost:3000/rides

**الميزات المتاحة:**
- ✅ **جدول شامل** لجميع الرحلات
- ✅ **بحث متقدم** (بالمعرف، العنوان، العميل)
- ✅ **فلترة متعددة** (الحالة، النوع، التاريخ)
- ✅ **تحديث الحالة** اليدوي
- ✅ **تعيين السائقين** للرحلات
- ✅ **عرض التفاصيل** الكاملة

**كيفية الاستخدام:**
1. **البحث**: استخدم شريط البحث للعثور على رحلة محددة
2. **التصفية**: اختر الحالة أو النوع من القوائم المنسدلة
3. **التحديث**: انقر على رحلة لتحديث حالتها
4. **التفاصيل**: انقر على "عرض التفاصيل" لرؤية المعلومات الكاملة

### 4. 📦 إدارة الطرود (Parcels Management)
**الرابط**: http://localhost:3000/parcels

**الميزات المتاحة:**
- ✅ **قائمة الطرود** مع الحالات
- ✅ **تتبع الطرود** على الخريطة
- ✅ **معلومات المرسل والمستلم**
- ✅ **تفاصيل الوزن والأبعاد**
- ✅ **سجل الأحداث** الزمني

**كيفية الاستخدام:**
1. **عرض الطرود**: جدول بجميع الطرود الحالية
2. **تتبع طرد**: انقر على طرد لرؤية موقعه على الخريطة
3. **تحديث الحالة**: تغيير حالة الطرد (تم الالتقاط، قيد التوصيل، تم التسليم)

### 5. 🚛 إدارة الأسطول (Fleet Management)
**الرابط**: http://localhost:3000/fleet

**الميزات المتاحة:**
- ✅ **قائمة المركبات** مع الحالات
- ✅ **معلومات السائقين**
- ✅ **حالة المركبات** (متاح، مشغول، صيانة)
- ✅ **إحصائيات الاستخدام**

### 6. 👥 إدارة المستخدمين (Users Management)
**الرابط**: http://localhost:3000/users

**الميزات المتاحة:**
- ✅ **قائمة المستخدمين** (ركاب، سائقين، إداريين)
- ✅ **معلومات الملف الشخصي**
- ✅ **إدارة الصلاحيات**
- ✅ **سجل النشاطات**

## 🎛️ الشريط الجانبي (Sidebar)

### الأقسام الرئيسية:
1. **📊 لوحة التحكم** - الصفحة الرئيسية
2. **🗺️ العمليات المباشرة** - التتبع الحي
3. **🚗 إدارة الرحلات** - الرحلات والحجوزات
4. **📦 إدارة الطرود** - تتبع الطرود
5. **🚛 إدارة الأسطول** - المركبات والسائقين
6. **👥 إدارة المستخدمين** - العملاء والموظفين
7. **💰 الإدارة المالية** - الفواتير والمدفوعات
8. **📈 التقارير والتحليلات** - الإحصائيات والرسوم البيانية
9. **⚙️ الإعدادات** - تكوين النظام

## 🔧 الميزات التفاعلية

### 1. التحديثات الفورية (Real-time Updates)
- **WebSocket Connection**: تحديثات تلقائية بدون إعادة تحميل
- **Live Notifications**: تنبيهات فورية للأحداث المهمة
- **Auto Refresh**: تحديث البيانات كل 5 ثوانٍ

### 2. الخرائط التفاعلية
- **Google Maps Integration**: خرائط عالية الجودة
- **Custom Markers**: أيقونات مخصصة للمركبات والطرود
- **Route Display**: عرض المسارات المخططة والفعلية
- **Zoom Controls**: أدوات تكبير وتصغير متقدمة

### 3. البحث والتصفية
- **Smart Search**: بحث ذكي عبر جميع الحقول
- **Advanced Filters**: فلاتر متعددة ومتقدمة
- **Date Range**: تصفية حسب النطاق الزمني
- **Status Filters**: تصفية حسب الحالة

## 🎨 التصميم والواجهة

### الألوان والثيمات:
- **اللون الأساسي**: أزرق TecnoDrive (#1976d2)
- **الألوان الثانوية**: رمادي وأبيض
- **التنبيهات**: أحمر للخطر، أصفر للتحذير، أخضر للنجاح

### التجاوب (Responsive Design):
- ✅ **Desktop**: تجربة كاملة على الشاشات الكبيرة
- ✅ **Tablet**: تخطيط محسن للأجهزة اللوحية
- ✅ **Mobile**: واجهة مبسطة للهواتف الذكية

### اللغة والاتجاه:
- ✅ **RTL Support**: دعم كامل للغة العربية
- ✅ **Arabic Fonts**: خطوط عربية واضحة
- ✅ **Bilingual**: دعم العربية والإنجليزية

## 🧪 اختبار الميزات

### 1. اختبار التحديثات الفورية:
```bash
# إرسال تحديث موقع
curl -X POST http://localhost:8085/api/locations/update \
  -H "Content-Type: application/json" \
  -d '{"vehicleId": "vehicle_001", "lat": 24.7136, "lng": 46.6753}'

# إرسال تنبيه
curl -X POST http://localhost:8085/api/locations/alert \
  -H "Content-Type: application/json" \
  -d '{"type": "delay", "severity": "warning", "message": "تأخير في التوصيل"}'
```

### 2. اختبار البحث:
- ابحث عن رحلة بالمعرف
- جرب البحث بالعنوان
- استخدم الفلاتر المختلفة

### 3. اختبار الخرائط:
- انقر على المركبات لرؤية التفاصيل
- استخدم أدوات التكبير والتصغير
- جرب إعادة توسيط الخريطة

## 🎯 نصائح للاستكشاف

1. **ابدأ بلوحة التحكم المباشرة** - أكثر الميزات إثارة
2. **جرب التحديثات الفورية** - استخدم curl commands
3. **استكشف الفلاتر** - جرب البحث والتصفية
4. **تفاعل مع الخرائط** - انقر على العلامات
5. **راقب التنبيهات** - لاحظ التحديثات الفورية

## 🚀 الخطوات التالية

بعد استكشاف الواجهة:
1. **تقييم الميزات الحالية**
2. **تحديد التحسينات المطلوبة**
3. **البدء في المرحلة الثانية** (المحافظ الإلكترونية)
4. **إضافة ميزات جديدة** حسب الحاجة

---

**استمتع باستكشاف منصة TecnoDrive!** 🎉
