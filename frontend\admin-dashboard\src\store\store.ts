import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import ridesSlice from './slices/ridesSlice';
import fleetSlice from './slices/fleetSlice';
import usersSlice from './slices/usersSlice';
import paymentsSlice from './slices/paymentsSlice';
import analyticsSlice from './slices/analyticsSlice';
import parcelsSlice from './slices/parcelsSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    rides: ridesSlice,
    fleet: fleetSlice,
    users: usersSlice,
    payments: paymentsSlice,
    analytics: analyticsSlice,
    parcels: parcelsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
