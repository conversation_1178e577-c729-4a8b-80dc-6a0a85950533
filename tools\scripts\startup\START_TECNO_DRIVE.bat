@echo off
chcp 65001 >nul
title TECNO DRIVE Platform - Unified Launcher

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TECNO DRIVE PLATFORM                     ║
echo ║                     Unified Launcher                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting TECNO DRIVE Platform...
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Start the platform using docker-compose
echo 🐳 Starting services with Docker Compose...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    pause
    exit /b 1
)

echo.
echo ✅ Services started successfully!
echo.

REM Wait a moment for services to initialize
echo ⏳ Waiting for services to initialize...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 Platform URLs:
echo ├── 🎛️  Admin Dashboard:     http://localhost:3000
echo ├── 🔧  API Gateway:         http://localhost:8080
echo ├── 📊  Database Explorer:   http://localhost:8081
echo ├── 📈  Monitoring:          http://localhost:3001
echo └── 📖  API Documentation:   http://localhost:8080/swagger-ui.html
echo.

echo 🎯 Platform is ready!
echo.
echo Press any key to open Admin Dashboard...
pause >nul

REM Open the admin dashboard
start http://localhost:3000

echo.
echo 📋 Useful Commands:
echo ├── View logs:        docker-compose logs -f
echo ├── Stop platform:    docker-compose down
echo ├── Restart:          docker-compose restart
echo └── Check status:     docker-compose ps
echo.

echo Platform is running in the background.
echo Close this window when you're done.
pause
