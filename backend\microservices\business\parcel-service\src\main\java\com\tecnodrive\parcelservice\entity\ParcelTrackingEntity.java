package com.tecnodrive.parcelservice.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity لتتبع الطرود
 */
@Entity
@Table(name = "parcel_tracking", indexes = {
    @Index(name = "idx_tracking_parcel_id", columnList = "parcel_id"),
    @Index(name = "idx_tracking_timestamp", columnList = "timestamp"),
    @Index(name = "idx_tracking_status", columnList = "status")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParcelTrackingEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "tracking_id")
    private String trackingId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parcel_id", nullable = false)
    private ParcelEntity parcel;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ParcelEntity.ParcelStatus status;
    
    @Column(name = "location_name", length = 255)
    private String locationName;
    
    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @CreationTimestamp
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    @Column(name = "estimated_arrival", columnDefinition = "TIMESTAMP")
    private LocalDateTime estimatedArrival;
    
    @Column(name = "actual_arrival", columnDefinition = "TIMESTAMP")
    private LocalDateTime actualArrival;
    
    @Column(name = "delay_reason", length = 255)
    private String delayReason;
    
    @Column(name = "temperature")
    private Double temperature;
    
    @Column(name = "humidity")
    private Double humidity;
    
    @Column(name = "is_milestone")
    private Boolean isMilestone;
    
    @Column(name = "milestone_type", length = 50)
    private String milestoneType; // PICKUP, TRANSIT, DELIVERY, etc.
    
    @Column(name = "vehicle_id", length = 50)
    private String vehicleId;
    
    @Column(name = "driver_id", length = 50)
    private String driverId;
    
    @Column(name = "facility_id", length = 50)
    private String facilityId;
    
    @Column(name = "scan_type", length = 20)
    private String scanType; // MANUAL, BARCODE, QR, RFID
    
    @Column(name = "device_id", length = 100)
    private String deviceId;
    
    @Column(name = "signature_url", length = 500)
    private String signatureUrl;
    
    @Column(name = "photo_url", length = 500)
    private String photoUrl;
    
    @Column(name = "recipient_name", length = 100)
    private String recipientName;
    
    @Column(name = "recipient_id_number", length = 50)
    private String recipientIdNumber;
    
    @Column(name = "delivery_instructions", length = 500)
    private String deliveryInstructions;
    
    @Column(name = "is_automated")
    private Boolean isAutomated;
    
    @Column(name = "source_system", length = 50)
    private String sourceSystem;
    
    @Column(name = "external_tracking_id", length = 100)
    private String externalTrackingId;
    
    /**
     * إنشاء تتبع جديد
     */
    public static ParcelTrackingEntity createTracking(
            ParcelEntity parcel,
            ParcelEntity.ParcelStatus status,
            String locationName,
            String updatedBy) {
        
        return ParcelTrackingEntity.builder()
                .parcel(parcel)
                .status(status)
                .locationName(locationName)
                .updatedBy(updatedBy)
                .timestamp(LocalDateTime.now())
                .isAutomated(false)
                .isMilestone(isMilestoneStatus(status))
                .milestoneType(getMilestoneType(status))
                .build();
    }
    
    /**
     * إنشاء تتبع مع موقع جغرافي
     */
    public static ParcelTrackingEntity createTrackingWithLocation(
            ParcelEntity parcel,
            ParcelEntity.ParcelStatus status,
            String locationName,
            BigDecimal latitude,
            BigDecimal longitude,
            String updatedBy) {
        
        return ParcelTrackingEntity.builder()
                .parcel(parcel)
                .status(status)
                .locationName(locationName)
                .latitude(latitude)
                .longitude(longitude)
                .updatedBy(updatedBy)
                .timestamp(LocalDateTime.now())
                .isAutomated(false)
                .isMilestone(isMilestoneStatus(status))
                .milestoneType(getMilestoneType(status))
                .build();
    }
    
    /**
     * تحديد ما إذا كانت الحالة معلم مهم
     */
    private static Boolean isMilestoneStatus(ParcelEntity.ParcelStatus status) {
        return status == ParcelEntity.ParcelStatus.PICKED_UP ||
               status == ParcelEntity.ParcelStatus.IN_TRANSIT ||
               status == ParcelEntity.ParcelStatus.OUT_FOR_DELIVERY ||
               status == ParcelEntity.ParcelStatus.DELIVERED ||
               status == ParcelEntity.ParcelStatus.RETURNED ||
               status == ParcelEntity.ParcelStatus.CANCELLED;
    }
    
    /**
     * تحديد نوع المعلم
     */
    private static String getMilestoneType(ParcelEntity.ParcelStatus status) {
        switch (status) {
            case PICKED_UP:
                return "PICKUP";
            case IN_TRANSIT:
                return "TRANSIT";
            case OUT_FOR_DELIVERY:
                return "OUT_FOR_DELIVERY";
            case DELIVERED:
                return "DELIVERY";
            case RETURNED:
                return "RETURN";
            case CANCELLED:
                return "CANCELLATION";
            default:
                return "STATUS_UPDATE";
        }
    }
    
    /**
     * إضافة توقيع التسليم
     */
    public void addDeliverySignature(String signatureUrl, String recipientName, String recipientId) {
        this.signatureUrl = signatureUrl;
        this.recipientName = recipientName;
        this.recipientIdNumber = recipientId;
        this.actualArrival = LocalDateTime.now();
    }
    
    /**
     * إضافة صورة التسليم
     */
    public void addDeliveryPhoto(String photoUrl) {
        this.photoUrl = photoUrl;
    }
    
    /**
     * تحديث الموقع الجغرافي
     */
    public void updateLocation(BigDecimal latitude, BigDecimal longitude, String locationName) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.locationName = locationName;
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * إضافة معلومات البيئة
     */
    public void addEnvironmentalData(Double temperature, Double humidity) {
        this.temperature = temperature;
        this.humidity = humidity;
    }
    
    /**
     * تحديد سبب التأخير
     */
    public void setDelayReason(String reason, LocalDateTime newEstimatedArrival) {
        this.delayReason = reason;
        this.estimatedArrival = newEstimatedArrival;
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return parcel != null && 
               status != null && 
               timestamp != null &&
               updatedBy != null && !updatedBy.trim().isEmpty();
    }
    
    /**
     * الحصول على وصف مفصل للحالة
     */
    public String getDetailedStatusDescription() {
        StringBuilder description = new StringBuilder();
        description.append(status.getArabicName());
        
        if (locationName != null && !locationName.trim().isEmpty()) {
            description.append(" في ").append(locationName);
        }
        
        if (notes != null && !notes.trim().isEmpty()) {
            description.append(" - ").append(notes);
        }
        
        return description.toString();
    }
    
    /**
     * التحقق من وجود تأخير
     */
    public boolean isDelayed() {
        return delayReason != null && !delayReason.trim().isEmpty();
    }
    
    /**
     * التحقق من اكتمال التسليم
     */
    public boolean isDeliveryComplete() {
        return status == ParcelEntity.ParcelStatus.DELIVERED &&
               (signatureUrl != null || photoUrl != null) &&
               recipientName != null;
    }
}
