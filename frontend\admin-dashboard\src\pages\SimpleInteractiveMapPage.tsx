import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Map as MapIcon,
  DirectionsCar as CarIcon,
  LocationOn as LocationIcon,
  Schedule as ClockIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import StreetMapComponent from '../components/Maps/StreetMapComponent';

interface MapStats {
  activeVehicles: number;
  totalRoutes: number;
  trafficAlerts: number;
  geofenceViolations: number;
  lastUpdate: string;
}

const SimpleInteractiveMapPage: React.FC = () => {
  const [mapStats, setMapStats] = useState<MapStats>({
    activeVehicles: 0,
    totalRoutes: 0,
    trafficAlerts: 0,
    geofenceViolations: 0,
    lastUpdate: new Date().toISOString()
  });
  
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiStatus, setApiStatus] = useState({
    mapService: false,
    vehicleData: false,
    locationService: false
  });

  // Fetch map statistics
  useEffect(() => {
    fetchMapStats();
    testAPIStatus();
    
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchMapStats();
        testAPIStatus();
      }, 30000); // Update every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchMapStats = async () => {
    try {
      setLoading(true);
      
      // Try to fetch from location service
      const response = await fetch('http://localhost:8085/api/locations/stats');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setMapStats({
            activeVehicles: data.data.activeVehicles || 0,
            totalRoutes: data.data.totalRoutes || 0,
            trafficAlerts: data.data.trafficAlerts || 0,
            geofenceViolations: data.data.geofenceViolations || 0,
            lastUpdate: data.data.lastUpdate || new Date().toISOString()
          });
          setError(null);
        }
      } else {
        throw new Error('Failed to fetch map stats');
      }
    } catch (err) {
      console.error('Failed to fetch map stats:', err);
      
      // Fallback to mock data
      setMapStats({
        activeVehicles: 3,
        totalRoutes: 5,
        trafficAlerts: 1,
        geofenceViolations: 0,
        lastUpdate: new Date().toISOString()
      });
      
      setError('Using mock statistics data');
    } finally {
      setLoading(false);
    }
  };

  const testAPIStatus = async () => {
    const endpoints = [
      { name: 'mapService', url: 'http://localhost:8085/health' },
      { name: 'vehicleData', url: 'http://localhost:8085/api/map/vehicles' },
      { name: 'locationService', url: 'http://localhost:8085/api/locations/stats' }
    ];

    const newStatus = { mapService: false, vehicleData: false, locationService: false };

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.url);
        newStatus[endpoint.name as keyof typeof newStatus] = response.ok;
      } catch (error) {
        console.error(`Failed to test ${endpoint.name}:`, error);
      }
    }

    setApiStatus(newStatus);
  };

  const handleRefresh = () => {
    fetchMapStats();
    testAPIStatus();
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <MapIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          خريطة الشوارع التفاعلية - OpenStreetMap
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          خريطة تفاعلية للشوارع مع تتبع المركبات والإحصائيات المباشرة
        </Typography>
      </Box>

      {/* API Status */}
      <Alert 
        severity={apiStatus.mapService ? 'success' : 'warning'} 
        sx={{ mb: 3 }}
        action={
          <IconButton onClick={handleRefresh} size="small">
            <RefreshIcon />
          </IconButton>
        }
      >
        <Typography variant="body2">
          <strong>حالة الخدمات:</strong> 
          {apiStatus.mapService ? ' جميع الخدمات متصلة وتعمل بشكل طبيعي' : ' بعض الخدمات غير متاحة، يتم استخدام البيانات التجريبية'}
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Statistics Panel */}
        <Grid item xs={12} md={4}>
          {/* Map Statistics */}
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6">
                  إحصائيات الخريطة
                </Typography>
                <IconButton onClick={handleRefresh} size="small">
                  <RefreshIcon />
                </IconButton>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center">
                    <CarIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {mapStats.activeVehicles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      مركبات نشطة
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box textAlign="center">
                    <LocationIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {mapStats.totalRoutes}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      طرق نشطة
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box textAlign="center">
                    <WarningIcon color="warning" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {mapStats.trafficAlerts}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      تنبيهات مرور
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box textAlign="center">
                    <ErrorIcon color="error" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h4" color="error.main">
                      {mapStats.geofenceViolations}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      مخالفات حدود
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  آخر تحديث: {new Date(mapStats.lastUpdate).toLocaleString('ar-SA')}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* API Status */}
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة الخدمات
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">خدمة الخرائط</Typography>
                  <Chip
                    icon={apiStatus.mapService ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.mapService ? 'متصل' : 'غير متصل'}
                    color={apiStatus.mapService ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">بيانات المركبات</Typography>
                  <Chip
                    icon={apiStatus.vehicleData ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.vehicleData ? 'متصل' : 'غير متصل'}
                    color={apiStatus.vehicleData ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">خدمة المواقع</Typography>
                  <Chip
                    icon={apiStatus.locationService ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.locationService ? 'متصل' : 'غير متصل'}
                    color={apiStatus.locationService ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Controls */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                إعدادات الخريطة
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                    />
                  }
                  label="التحديث التلقائي"
                />
                
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleRefresh}
                  fullWidth
                >
                  تحديث البيانات
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<FullscreenIcon />}
                  onClick={toggleFullscreen}
                  fullWidth
                >
                  {isFullscreen ? 'إنهاء ملء الشاشة' : 'ملء الشاشة'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Map Panel */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              <Typography variant="h6">
                خريطة الشوارع - الرياض، المملكة العربية السعودية
              </Typography>
              <Box display="flex" gap={1}>
                <Tooltip title="إعدادات">
                  <IconButton size="small">
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="ملء الشاشة">
                  <IconButton onClick={toggleFullscreen} size="small">
                    <FullscreenIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            
            {error && (
              <Alert severity="info" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <StreetMapComponent height="600px" />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default SimpleInteractiveMapPage;
