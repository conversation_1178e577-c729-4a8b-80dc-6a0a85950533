import json
import os
import uuid
from datetime import datetime

def load_json_data(filename):
    """تحميل البيانات من ملف JSON"""
    filepath = os.path.join("generated_data", filename)
    if os.path.exists(filepath):
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        print(f"⚠️ ملف {filename} غير موجود")
        return []

def escape_sql_string(value):
    """تنظيف النصوص لاستخدامها في SQL"""
    if value is None:
        return 'NULL'
    if isinstance(value, str):
        # استبدال الفواصل المنقوطة والعلامات الخاصة
        value = value.replace("'", "''")
        return f"'{value}'"
    elif isinstance(value, bool):
        return 'TRUE' if value else 'FALSE'
    elif isinstance(value, (int, float)):
        return str(value)
    else:
        return f"'{str(value)}'"

def generate_users_sql(users_data, drivers_data):
    """توليد SQL للمستخدمين والسائقين"""
    sql_statements = []

    # إضافة المستخدمين العاديين
    for user in users_data:
        # إضافة created_at إذا لم يكن موجوداً
        created_at = user.get('created_at', datetime.now().isoformat())

        sql = f"""INSERT INTO users (
            id, email, phone_number, password_hash, first_name, last_name,
            user_type, status, account_tier, nationality, preferred_language,
            country, is_email_verified, is_phone_verified, rating,
            total_trips, total_spent, wallet_balance, loyalty_points,
            created_at, updated_at
        ) VALUES (
            {escape_sql_string(user['id'])},
            {escape_sql_string(user['email'])},
            {escape_sql_string(user['phone_number'])},
            '$2a$10$defaultpasswordhash',
            {escape_sql_string(user['first_name'])},
            {escape_sql_string(user['last_name'])},
            'passenger',
            'active',
            'basic',
            'Yemen',
            'ar',
            'Yemen',
            TRUE,
            TRUE,
            4.0,
            0,
            0.00,
            1000.00,
            0,
            {escape_sql_string(created_at)},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)
    
    # إضافة السائقين
    for driver in drivers_data:
        # إضافة created_at إذا لم يكن موجوداً
        created_at = driver.get('created_at', datetime.now().isoformat())
        rating = driver.get('rating', 4.5)

        sql = f"""INSERT INTO users (
            id, email, phone_number, password_hash, first_name, last_name,
            user_type, status, account_tier, nationality, preferred_language,
            country, is_email_verified, is_phone_verified, rating,
            total_trips, total_spent, wallet_balance, loyalty_points,
            created_at, updated_at
        ) VALUES (
            {escape_sql_string(driver['id'])},
            {escape_sql_string(driver['email'])},
            {escape_sql_string(driver['phone_number'])},
            '$2a$10$defaultpasswordhash',
            {escape_sql_string(driver['first_name'])},
            {escape_sql_string(driver['last_name'])},
            'driver',
            'active',
            'silver',
            'Yemen',
            'ar',
            'Yemen',
            TRUE,
            TRUE,
            {escape_sql_string(rating)},
            50,
            0.00,
            2500.00,
            250,
            {escape_sql_string(created_at)},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)
    
    return sql_statements

def generate_tenants_sql(tenants_data):
    """توليد SQL للشركات (المستأجرين)"""
    sql_statements = []

    for tenant in tenants_data:
        # إضافة created_at إذا لم يكن موجوداً
        created_at = tenant.get('created_at', datetime.now().isoformat())

        sql = f"""INSERT INTO tenants (
            id, name, type, contact_email, phone_number, address,
            status, created_at, updated_at
        ) VALUES (
            {escape_sql_string(tenant['id'])},
            {escape_sql_string(tenant['name'])},
            {escape_sql_string(tenant['type'])},
            {escape_sql_string(tenant['contact_email'])},
            {escape_sql_string(tenant['phone_number'])},
            {escape_sql_string(tenant['address'])},
            {escape_sql_string(tenant['status'])},
            {escape_sql_string(created_at)},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)

    return sql_statements

def generate_vehicles_sql(vehicles_data):
    """توليد SQL للمركبات"""
    sql_statements = []
    
    for vehicle in vehicles_data:
        sql = f"""INSERT INTO vehicles (
            id, driver_id, make, model, year, license_plate, color, 
            vehicle_type, capacity, status, created_at, updated_at
        ) VALUES (
            {escape_sql_string(vehicle['id'])},
            {escape_sql_string(vehicle['driver_id'])},
            {escape_sql_string(vehicle['make'])},
            {escape_sql_string(vehicle['model'])},
            {escape_sql_string(vehicle['year'])},
            {escape_sql_string(vehicle['license_plate'])},
            {escape_sql_string(vehicle['color'])},
            {escape_sql_string(vehicle['vehicle_type'])},
            {escape_sql_string(vehicle['capacity'])},
            {escape_sql_string(vehicle['status'])},
            {escape_sql_string(vehicle['created_at'])},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)
    
    return sql_statements

def generate_trips_sql(trips_data):
    """توليد SQL للرحلات"""
    sql_statements = []
    
    for trip in trips_data:
        start_time = escape_sql_string(trip['start_time']) if trip['start_time'] else 'NULL'
        end_time = escape_sql_string(trip['end_time']) if trip['end_time'] else 'NULL'
        driver_rating = escape_sql_string(trip['driver_rating']) if trip['driver_rating'] else 'NULL'
        user_rating = escape_sql_string(trip['user_rating']) if trip['user_rating'] else 'NULL'
        
        sql = f"""INSERT INTO trips (
            id, user_id, driver_id, vehicle_id, trip_type, status,
            pickup_location, dropoff_location, pickup_lat, pickup_lon,
            dropoff_lat, dropoff_lon, distance_km, fare_amount,
            payment_status, start_time, end_time, requested_at,
            driver_rating, user_rating, created_at, updated_at
        ) VALUES (
            {escape_sql_string(trip['trip_id'])},
            {escape_sql_string(trip['user_id'])},
            {escape_sql_string(trip['driver_id'])},
            {escape_sql_string(trip['vehicle_id'])},
            {escape_sql_string(trip['trip_type'])},
            {escape_sql_string(trip['status'])},
            {escape_sql_string(trip['pickup_location'])},
            {escape_sql_string(trip['dropoff_location'])},
            {escape_sql_string(trip['pickup_lat'])},
            {escape_sql_string(trip['pickup_lon'])},
            {escape_sql_string(trip['dropoff_lat'])},
            {escape_sql_string(trip['dropoff_lon'])},
            {escape_sql_string(trip['distance_km'])},
            {escape_sql_string(trip['fare_amount'])},
            {escape_sql_string(trip['payment_status'])},
            {start_time},
            {end_time},
            {escape_sql_string(trip['requested_at'])},
            {driver_rating},
            {user_rating},
            {escape_sql_string(trip['created_at'])},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)
    
    return sql_statements

def generate_parcels_sql(parcels_data):
    """توليد SQL للطرود"""
    sql_statements = []
    
    for parcel in parcels_data:
        pickup_time = escape_sql_string(parcel['pickup_time']) if parcel['pickup_time'] else 'NULL'
        delivery_time = escape_sql_string(parcel['delivery_time']) if parcel['delivery_time'] else 'NULL'
        
        sql = f"""INSERT INTO parcels (
            id, sender_id, driver_id, vehicle_id, status,
            pickup_address, pickup_lat, pickup_lon,
            delivery_address, delivery_lat, delivery_lon,
            recipient_name, recipient_phone, item_description,
            weight_kg, dimensions_cm, delivery_fee, payment_status,
            pickup_time, delivery_time, requested_at, created_at, updated_at
        ) VALUES (
            {escape_sql_string(parcel['parcel_id'])},
            {escape_sql_string(parcel['sender_id'])},
            {escape_sql_string(parcel['driver_id'])},
            {escape_sql_string(parcel['vehicle_id'])},
            {escape_sql_string(parcel['status'])},
            {escape_sql_string(parcel['pickup_address'])},
            {escape_sql_string(parcel['pickup_lat'])},
            {escape_sql_string(parcel['pickup_lon'])},
            {escape_sql_string(parcel['delivery_address'])},
            {escape_sql_string(parcel['delivery_lat'])},
            {escape_sql_string(parcel['delivery_lon'])},
            {escape_sql_string(parcel['recipient_name'])},
            {escape_sql_string(parcel['recipient_phone'])},
            {escape_sql_string(parcel['item_description'])},
            {escape_sql_string(parcel['weight_kg'])},
            {escape_sql_string(parcel['dimensions_cm'])},
            {escape_sql_string(parcel['delivery_fee'])},
            {escape_sql_string(parcel['payment_status'])},
            {pickup_time},
            {delivery_time},
            {escape_sql_string(parcel['requested_at'])},
            {escape_sql_string(parcel['created_at'])},
            CURRENT_TIMESTAMP
        );"""
        sql_statements.append(sql)
    
    return sql_statements

def main():
    """الدالة الرئيسية لتحويل البيانات إلى SQL"""
    print("🔄 بدء تحويل البيانات اليمنية إلى SQL...")
    print("=" * 60)

    # تحميل البيانات من ملفات JSON
    print("📂 جاري تحميل البيانات من ملفات JSON...")

    # محاولة تحميل البيانات من الملفات المولدة أو المبسطة
    tenants_data = load_json_data("tenants_data.json") or load_json_data("tenants_simple.json") or []
    users_data = load_json_data("users_data.json") or load_json_data("users_simple.json") or []
    drivers_data = load_json_data("drivers_data.json") or load_json_data("drivers_simple.json") or []
    vehicles_data = load_json_data("vehicles_data.json") or []
    trips_data = load_json_data("trips_data.json") or []
    parcels_data = load_json_data("parcels_data.json") or []

    # إذا لم توجد ملفات مولدة، استخدم البيانات النموذجية
    if not any([tenants_data, users_data, drivers_data]):
        print("📋 لم توجد بيانات مولدة، سيتم استخدام البيانات النموذجية...")
        sample_data_path = "sample_yemen_data.json"
        if os.path.exists(sample_data_path):
            with open(sample_data_path, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)
                tenants_data = sample_data.get('companies', [])
                users_data = sample_data.get('users', [])
                drivers_data = sample_data.get('drivers', [])
                vehicles_data = sample_data.get('vehicles', [])
                trips_data = sample_data.get('trips', [])
                parcels_data = sample_data.get('parcels', [])

    print(f"✅ تم تحميل:")
    print(f"   • {len(tenants_data)} شركة")
    print(f"   • {len(users_data)} مستخدم")
    print(f"   • {len(drivers_data)} سائق")
    print(f"   • {len(vehicles_data)} مركبة")
    print(f"   • {len(trips_data)} رحلة")
    print(f"   • {len(parcels_data)} طرد")

    # توليد SQL statements
    print("\n🔧 جاري توليد SQL statements...")

    all_sql_statements = []

    # إضافة header للملف
    all_sql_statements.append("-- ===================================================================")
    all_sql_statements.append("-- TECNODRIVE Platform - البيانات اليمنية المولدة")
    all_sql_statements.append("-- تم إنشاؤها تلقائياً من مولد البيانات")
    all_sql_statements.append(f"-- تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    all_sql_statements.append("-- ===================================================================")
    all_sql_statements.append("")

    # تنظيف البيانات الموجودة (اختياري)
    all_sql_statements.append("-- تنظيف البيانات الموجودة (قم بإلغاء التعليق إذا كنت تريد حذف البيانات الموجودة)")
    all_sql_statements.append("-- TRUNCATE TABLE parcels, trips, vehicles, users, tenants CASCADE;")
    all_sql_statements.append("")

    # إضافة الشركات أولاً
    if tenants_data:
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.append("-- إدراج الشركات والمؤسسات")
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.extend(generate_tenants_sql(tenants_data))
        all_sql_statements.append("")

    # إضافة المستخدمين والسائقين
    if users_data or drivers_data:
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.append("-- إدراج المستخدمين والسائقين")
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.extend(generate_users_sql(users_data, drivers_data))
        all_sql_statements.append("")

    # إضافة المركبات
    if vehicles_data:
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.append("-- إدراج المركبات")
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.extend(generate_vehicles_sql(vehicles_data))
        all_sql_statements.append("")

    # إضافة الرحلات
    if trips_data:
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.append("-- إدراج الرحلات")
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.extend(generate_trips_sql(trips_data))
        all_sql_statements.append("")

    # إضافة الطرود
    if parcels_data:
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.append("-- إدراج الطرود")
        all_sql_statements.append("-- ===================================================================")
        all_sql_statements.extend(generate_parcels_sql(parcels_data))
        all_sql_statements.append("")

    # حفظ ملف SQL
    output_file = "yemen_data_import.sql"
    print(f"💾 جاري حفظ ملف SQL: {output_file}")

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(all_sql_statements))

    print(f"✅ تم إنشاء ملف {output_file} بنجاح!")
    print(f"📊 إجمالي SQL statements: {len([s for s in all_sql_statements if s.startswith('INSERT')])}")

    print("\n🚀 خطوات التشغيل:")
    print("1. تأكد من تشغيل PostgreSQL")
    print("2. قم بتشغيل الأمر التالي:")
    print(f"   psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f {output_file}")
    print("\n📝 ملاحظة: تأكد من وجود قاعدة البيانات وتطبيق schema أولاً")

if __name__ == "__main__":
    main()
