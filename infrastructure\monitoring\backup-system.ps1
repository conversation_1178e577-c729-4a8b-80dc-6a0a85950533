#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Automated Backup System
# This script creates comprehensive backups of all platform data

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("full", "database", "config", "logs")]
    [string]$BackupType = "full",
    
    [Parameter(Mandatory=$false)]
    [string]$BackupPath = "backups"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "$BackupPath/$timestamp"

Write-Host "💾 TECNO DRIVE Backup System" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Cyan
Write-Host "Backup Type: $BackupType" -ForegroundColor Yellow
Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow

# Create backup directory
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

function Backup-Databases {
    Write-Host "📊 Backing up databases..." -ForegroundColor Blue
    
    $databases = @(
        "auth_db", "ride_db", "fleet_db", "parcel_db", 
        "payment_db", "notification_db", "financial_db", 
        "hr_db", "analytics_db", "saas_db", "location_db"
    )
    
    $dbBackupDir = "$backupDir/databases"
    New-Item -ItemType Directory -Path $dbBackupDir -Force | Out-Null
    
    foreach ($db in $databases) {
        Write-Host "  Backing up $db..." -ForegroundColor Yellow
        docker exec infra-postgres-1 pg_dump -U postgres $db > "$dbBackupDir/$db.sql"
    }
    
    Write-Host "✅ Database backup completed!" -ForegroundColor Green
}

function Backup-Configuration {
    Write-Host "⚙️ Backing up configuration files..." -ForegroundColor Blue
    
    $configBackupDir = "$backupDir/config"
    New-Item -ItemType Directory -Path $configBackupDir -Force | Out-Null
    
    # Copy configuration files
    Copy-Item "docker-compose.yml" "$configBackupDir/" -Force
    Copy-Item "prometheus.yml" "$configBackupDir/" -Force
    Copy-Item "security-config.yml" "$configBackupDir/" -Force
    Copy-Item "*.ps1" "$configBackupDir/" -Force
    
    Write-Host "✅ Configuration backup completed!" -ForegroundColor Green
}

function Backup-Logs {
    Write-Host "📋 Backing up logs..." -ForegroundColor Blue
    
    $logBackupDir = "$backupDir/logs"
    New-Item -ItemType Directory -Path $logBackupDir -Force | Out-Null
    
    # Export Docker logs
    $containers = @(
        "infra-postgres-1", "infra-redis-1", "infra-eureka-server-1",
        "auth-service-fixed", "infra-parcel-service-1", 
        "infra-location-service-1", "api-gateway-fixed"
    )
    
    foreach ($container in $containers) {
        Write-Host "  Exporting logs for $container..." -ForegroundColor Yellow
        docker logs $container > "$logBackupDir/$container.log" 2>&1
    }
    
    Write-Host "✅ Logs backup completed!" -ForegroundColor Green
}

function Backup-Redis {
    Write-Host "🔴 Backing up Redis data..." -ForegroundColor Blue
    
    $redisBackupDir = "$backupDir/redis"
    New-Item -ItemType Directory -Path $redisBackupDir -Force | Out-Null
    
    # Create Redis backup
    docker exec infra-redis-1 redis-cli BGSAVE
    Start-Sleep -Seconds 5
    docker cp infra-redis-1:/data/dump.rdb "$redisBackupDir/dump.rdb"
    
    Write-Host "✅ Redis backup completed!" -ForegroundColor Green
}

function Create-BackupManifest {
    $manifest = @{
        timestamp = $timestamp
        backup_type = $BackupType
        platform_version = "1.0.0"
        services = @{
            postgres = "15-3.4"
            redis = "7-alpine"
            eureka = "latest"
        }
        databases = @(
            "auth_db", "ride_db", "fleet_db", "parcel_db", 
            "payment_db", "notification_db", "financial_db", 
            "hr_db", "analytics_db", "saas_db", "location_db"
        )
    }
    
    $manifest | ConvertTo-Json -Depth 3 | Out-File "$backupDir/manifest.json"
    Write-Host "📄 Backup manifest created!" -ForegroundColor Green
}

# Execute backup based on type
switch ($BackupType) {
    "full" {
        Backup-Databases
        Backup-Configuration
        Backup-Logs
        Backup-Redis
    }
    "database" {
        Backup-Databases
        Backup-Redis
    }
    "config" {
        Backup-Configuration
    }
    "logs" {
        Backup-Logs
    }
}

Create-BackupManifest

# Compress backup
Write-Host "🗜️ Compressing backup..." -ForegroundColor Blue
Compress-Archive -Path $backupDir -DestinationPath "$BackupPath/tecnodrive_backup_$timestamp.zip"
Remove-Item -Path $backupDir -Recurse -Force

Write-Host ""
Write-Host "✅ Backup completed successfully!" -ForegroundColor Green
Write-Host "📁 Backup file: tecnodrive_backup_$timestamp.zip" -ForegroundColor Cyan
Write-Host "📊 Backup size: $((Get-Item "$BackupPath/tecnodrive_backup_$timestamp.zip").Length / 1MB) MB" -ForegroundColor Yellow
