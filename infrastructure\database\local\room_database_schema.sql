-- =====================================================
-- TECNO DRIVE - Room Database Schema (SQLite)
-- قاعدة البيانات المحلية للتطبيق المحمول
-- =====================================================

-- 1. معلومات المستخدم المحلية
CREATE TABLE user_info (
    user_id         TEXT    PRIMARY KEY,
    full_name       TEXT    NOT NULL,
    email           TEXT    UNIQUE NOT NULL,
    phone           TEXT,
    access_token    TEXT,
    refresh_token   TEXT,
    is_verified     BOOLEAN DEFAULT FALSE,
    profile_image   TEXT,
    language        TEXT    DEFAULT 'ar',
    theme           TEXT    DEFAULT 'light',
    notifications_enabled BOOLEAN DEFAULT TRUE,
    created_at      INTEGER NOT NULL,
    updated_at      INTEGER NOT NULL,
    last_sync       INTEGER
);

-- 2. معلومات الحجوزات المحلية
CREATE TABLE booking_info (
    booking_id      TEXT    PRIMARY KEY,
    user_id         TEXT    NOT NULL REFERENCES user_info(user_id) ON DELETE CASCADE,
    trip_id         TEXT,
    from_location   TEXT    NOT NULL,
    to_location     TEXT    NOT NULL,
    departure_time  INTEGER,
    arrival_time    INTEGER,
    seat_count      INTEGER NOT NULL DEFAULT 1,
    passenger_names TEXT,   -- JSON array of names
    expected_price  REAL,
    actual_price    REAL,
    status          TEXT    NOT NULL DEFAULT 'pending',
    payment_status  TEXT    DEFAULT 'pending',
    booking_type    TEXT    DEFAULT 'regular', -- regular, shared, private
    special_requests TEXT,
    created_at      INTEGER NOT NULL,
    updated_at      INTEGER NOT NULL,
    synced          BOOLEAN DEFAULT FALSE
);

-- 3. الأماكن المحفوظة
CREATE TABLE saved_places (
    place_id        TEXT    PRIMARY KEY,
    user_id         TEXT    NOT NULL REFERENCES user_info(user_id) ON DELETE CASCADE,
    place_name      TEXT    NOT NULL,
    place_type      TEXT    DEFAULT 'custom', -- home, work, custom
    address         TEXT    NOT NULL,
    latitude        REAL    NOT NULL,
    longitude       REAL    NOT NULL,
    city            TEXT,
    country         TEXT    DEFAULT 'Yemen',
    is_favorite     BOOLEAN DEFAULT FALSE,
    usage_count     INTEGER DEFAULT 0,
    created_at      INTEGER NOT NULL,
    updated_at      INTEGER NOT NULL
);

-- 4. معلومات الطرود المحلية
CREATE TABLE parcel_info (
    parcel_id        TEXT    PRIMARY KEY,
    user_id          TEXT    NOT NULL REFERENCES user_info(user_id) ON DELETE CASCADE,
    barcode          TEXT    UNIQUE NOT NULL,
    sender_name      TEXT    NOT NULL,
    receiver_name    TEXT    NOT NULL,
    sender_address   TEXT    NOT NULL,
    receiver_address TEXT    NOT NULL,
    sender_phone     TEXT,
    receiver_phone   TEXT,
    weight_kg        REAL    NOT NULL,
    length_cm        REAL    NOT NULL,
    width_cm         REAL    NOT NULL,
    height_cm        REAL    NOT NULL,
    status           TEXT    NOT NULL DEFAULT 'Created',
    priority         TEXT    DEFAULT 'MEDIUM',
    fragile          BOOLEAN DEFAULT FALSE,
    insurance_value  REAL    DEFAULT 0,
    estimated_cost   REAL,
    actual_cost      REAL,
    notes            TEXT,
    estimated_delivery_date INTEGER,
    actual_delivery_date    INTEGER,
    created_at       INTEGER NOT NULL,
    updated_at       INTEGER NOT NULL,
    synced           BOOLEAN DEFAULT FALSE
);

-- 5. تتبع الطرود المحلي
CREATE TABLE parcel_tracking (
    tracking_id     TEXT    PRIMARY KEY,
    parcel_id       TEXT    NOT NULL REFERENCES parcel_info(parcel_id) ON DELETE CASCADE,
    status          TEXT    NOT NULL,
    location_name   TEXT,
    latitude        REAL,
    longitude       REAL,
    timestamp       INTEGER NOT NULL,
    notes           TEXT,
    updated_by      TEXT,
    synced          BOOLEAN DEFAULT FALSE
);

-- 6. مدفوعات الطرود المحلية
CREATE TABLE parcel_payments (
    payment_id      TEXT    PRIMARY KEY,
    parcel_id       TEXT    NOT NULL REFERENCES parcel_info(parcel_id) ON DELETE CASCADE,
    amount          REAL    NOT NULL,
    currency        TEXT    DEFAULT 'YER',
    method          TEXT    NOT NULL, -- cash, card, wallet, bank_transfer
    status          TEXT    NOT NULL DEFAULT 'pending',
    transaction_id  TEXT,
    payment_date    INTEGER,
    notes           TEXT,
    created_at      INTEGER NOT NULL,
    synced          BOOLEAN DEFAULT FALSE
);

-- 7. عناصر الطرود (للطرود متعددة العناصر)
CREATE TABLE parcel_items (
    item_id         TEXT    PRIMARY KEY,
    parcel_id       TEXT    NOT NULL REFERENCES parcel_info(parcel_id) ON DELETE CASCADE,
    item_name       TEXT    NOT NULL,
    item_description TEXT,
    quantity        INTEGER DEFAULT 1,
    weight_kg       REAL,
    value           REAL,
    category        TEXT,
    fragile         BOOLEAN DEFAULT FALSE,
    created_at      INTEGER NOT NULL
);

-- 8. نقاط الطريق للرحلات
CREATE TABLE way_points (
    waypoint_id     TEXT    PRIMARY KEY,
    booking_id      TEXT    NOT NULL REFERENCES booking_info(booking_id) ON DELETE CASCADE,
    sequence_order  INTEGER NOT NULL,
    location_name   TEXT    NOT NULL,
    latitude        REAL    NOT NULL,
    longitude       REAL    NOT NULL,
    estimated_time  INTEGER,
    actual_time     INTEGER,
    status          TEXT    DEFAULT 'pending' -- pending, reached, skipped
);

-- 9. تقييمات ما بعد الرحلة
CREATE TABLE post_trip_ratings (
    rating_id       TEXT    PRIMARY KEY,
    booking_id      TEXT    NOT NULL REFERENCES booking_info(booking_id) ON DELETE CASCADE,
    driver_rating   INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
    vehicle_rating  INTEGER CHECK (vehicle_rating >= 1 AND vehicle_rating <= 5),
    service_rating  INTEGER CHECK (service_rating >= 1 AND service_rating <= 5),
    overall_rating  INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
    comment         TEXT,
    would_recommend BOOLEAN,
    created_at      INTEGER NOT NULL,
    synced          BOOLEAN DEFAULT FALSE
);

-- 10. سجل الإشعارات المحلية
CREATE TABLE notifications_log (
    notification_id TEXT    PRIMARY KEY,
    user_id         TEXT    NOT NULL REFERENCES user_info(user_id) ON DELETE CASCADE,
    title           TEXT    NOT NULL,
    message         TEXT    NOT NULL,
    type            TEXT    NOT NULL, -- booking, parcel, system, promotion
    data            TEXT,   -- JSON data for the notification
    is_read         BOOLEAN DEFAULT FALSE,
    is_important    BOOLEAN DEFAULT FALSE,
    action_url      TEXT,
    expires_at      INTEGER,
    created_at      INTEGER NOT NULL
);

-- 11. ذاكرة التخزين المؤقت للبيانات
CREATE TABLE cache_data (
    cache_key       TEXT    PRIMARY KEY,
    cache_value     TEXT    NOT NULL, -- JSON data
    cache_type      TEXT    NOT NULL, -- routes, prices, drivers, etc.
    expires_at      INTEGER NOT NULL,
    created_at      INTEGER NOT NULL
);

-- 12. سجل المزامنة
CREATE TABLE sync_log (
    sync_id         TEXT    PRIMARY KEY,
    table_name      TEXT    NOT NULL,
    record_id       TEXT    NOT NULL,
    operation       TEXT    NOT NULL, -- insert, update, delete
    sync_status     TEXT    NOT NULL DEFAULT 'pending', -- pending, success, failed
    retry_count     INTEGER DEFAULT 0,
    error_message   TEXT,
    created_at      INTEGER NOT NULL,
    synced_at       INTEGER
);

-- =====================================================
-- INDEXES للأداء المحسن
-- =====================================================

-- فهارس المستخدم
CREATE INDEX idx_user_email ON user_info(email);
CREATE INDEX idx_user_phone ON user_info(phone);

-- فهارس الحجوزات
CREATE INDEX idx_booking_user_id ON booking_info(user_id);
CREATE INDEX idx_booking_status ON booking_info(status);
CREATE INDEX idx_booking_departure_time ON booking_info(departure_time);
CREATE INDEX idx_booking_synced ON booking_info(synced);

-- فهارس الأماكن المحفوظة
CREATE INDEX idx_saved_places_user_id ON saved_places(user_id);
CREATE INDEX idx_saved_places_type ON saved_places(place_type);
CREATE INDEX idx_saved_places_favorite ON saved_places(is_favorite);

-- فهارس الطرود
CREATE INDEX idx_parcel_user_id ON parcel_info(user_id);
CREATE INDEX idx_parcel_barcode ON parcel_info(barcode);
CREATE INDEX idx_parcel_status ON parcel_info(status);
CREATE INDEX idx_parcel_synced ON parcel_info(synced);

-- فهارس التتبع
CREATE INDEX idx_tracking_parcel_id ON parcel_tracking(parcel_id);
CREATE INDEX idx_tracking_timestamp ON parcel_tracking(timestamp);

-- فهارس المدفوعات
CREATE INDEX idx_payment_parcel_id ON parcel_payments(parcel_id);
CREATE INDEX idx_payment_status ON parcel_payments(status);
CREATE INDEX idx_payment_synced ON parcel_payments(synced);

-- فهارس الإشعارات
CREATE INDEX idx_notifications_user_id ON notifications_log(user_id);
CREATE INDEX idx_notifications_type ON notifications_log(type);
CREATE INDEX idx_notifications_read ON notifications_log(is_read);
CREATE INDEX idx_notifications_created_at ON notifications_log(created_at);

-- فهارس المزامنة
CREATE INDEX idx_sync_table_record ON sync_log(table_name, record_id);
CREATE INDEX idx_sync_status ON sync_log(sync_status);
CREATE INDEX idx_sync_created_at ON sync_log(created_at);

-- =====================================================
-- TRIGGERS للتحديث التلقائي
-- =====================================================

-- تحديث updated_at تلقائياً
CREATE TRIGGER update_user_info_timestamp 
    AFTER UPDATE ON user_info
    BEGIN
        UPDATE user_info SET updated_at = strftime('%s', 'now') * 1000 
        WHERE user_id = NEW.user_id;
    END;

CREATE TRIGGER update_booking_info_timestamp 
    AFTER UPDATE ON booking_info
    BEGIN
        UPDATE booking_info SET updated_at = strftime('%s', 'now') * 1000 
        WHERE booking_id = NEW.booking_id;
    END;

CREATE TRIGGER update_parcel_info_timestamp 
    AFTER UPDATE ON parcel_info
    BEGIN
        UPDATE parcel_info SET updated_at = strftime('%s', 'now') * 1000 
        WHERE parcel_id = NEW.parcel_id;
    END;
