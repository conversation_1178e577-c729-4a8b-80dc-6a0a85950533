package com.tecnodrive.paymentservice.repository;

import com.tecnodrive.paymentservice.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Payment Repository
 * 
 * Data access layer for Payment entities
 */
@Repository
public interface PaymentRepository extends JpaRepository<Payment, UUID> {

    /**
     * Find payments by entity ID and type
     */
    List<Payment> findByEntityIdAndEntityType(String entityId, String entityType);

    /**
     * Find payments by payer user ID
     */
    Page<Payment> findByPayerUserId(String payerUserId, Pageable pageable);

    /**
     * Find payments by payee user ID
     */
    Page<Payment> findByPayeeUserId(String payeeUserId, Pageable pageable);

    /**
     * Find payments by status
     */
    List<Payment> findByStatus(Payment.PaymentStatus status);

    /**
     * Find payments by gateway transaction ID
     */
    Optional<Payment> findByGatewayTransactionId(String gatewayTransactionId);

    /**
     * Find payments created between dates
     */
    @Query("SELECT p FROM Payment p WHERE p.createdAt BETWEEN :startDate AND :endDate")
    List<Payment> findPaymentsBetweenDates(
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find payments by user ID (either payer or payee)
     */
    @Query("SELECT p FROM Payment p WHERE p.payerUserId = :userId OR p.payeeUserId = :userId")
    Page<Payment> findByUserId(@Param("userId") String userId, Pageable pageable);

    /**
     * Count payments by status
     */
    long countByStatus(Payment.PaymentStatus status);

    /**
     * Find pending payments older than specified time
     */
    @Query("SELECT p FROM Payment p WHERE p.status = 'PENDING' AND p.createdAt < :cutoffTime")
    List<Payment> findPendingPaymentsOlderThan(@Param("cutoffTime") Instant cutoffTime);
}
