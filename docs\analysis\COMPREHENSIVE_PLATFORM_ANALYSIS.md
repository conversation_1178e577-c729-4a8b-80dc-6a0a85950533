# 📊 التحليل الشامل والتفصيلي لمنصة TecnoDrive

## 🎯 نظرة عامة على المنصة

**منصة TecnoDrive** هي نظام شامل ومتطور لإدارة النقل والتوصيل يعتمد على معمارية الخدمات المصغرة (Microservices Architecture) مع دعم متعدد المستأجرين (Multi-tenant SaaS Platform).

### 📍 الموقع والمسار
- **المسار الرئيسي**: `D:\tecno-drive-platform`
- **نوع المشروع**: منصة SaaS متكاملة
- **المعمارية**: Microservices + Event-Driven Architecture
- **قواعد البيانات**: PostgreSQL + Redis + MongoDB
- **التقنيات**: Java Spring Boot + Python FastAPI + React + Angular

### 🔢 إحصائيات المشروع
- **عدد الخدمات المصغرة**: 13 خدمة
- **عدد قواعد البيانات**: 13 قاعدة بيانات
- **عدد التطبيقات الأمامية**: 4 تطبيقات
- **عدد ملفات الكود**: 500+ ملف
- **حجم المشروع**: ~2.5 GB

---

## 🏗️ الهيكل التنظيمي للمشروع

### 📁 المجلدات الرئيسية (التسلسل الهرمي)

```
tecno-drive-platform/
├── 📁 backend/                     # الخدمات الخلفية (Backend Services)
│   ├── 📁 microservices/          # الخدمات المصغرة Java Spring Boot
│   │   ├── 📁 core/               # الخدمات الأساسية (3 خدمات)
│   │   │   ├── user-service/      # خدمة إدارة المستخدمين
│   │   │   ├── auth-service/      # خدمة المصادقة والتفويض
│   │   │   └── payment-service/   # خدمة المدفوعات والمحافظ
│   │   ├── 📁 business/           # خدمات الأعمال (6 خدمات)
│   │   │   ├── ride-service/      # خدمة إدارة الرحلات
│   │   │   ├── fleet-service/     # خدمة إدارة الأسطول
│   │   │   ├── parcel-service/    # خدمة الطرود والتوصيل
│   │   │   ├── location-service/  # خدمة المواقع والخرائط
│   │   │   ├── analytics-service/ # خدمة التحليلات والذكاء الاصطناعي
│   │   │   └── notification-service/ # خدمة الإشعارات
│   │   └── 📁 infrastructure/     # خدمات البنية التحتية (4 خدمات)
│   │       ├── api-gateway/       # بوابة API الموحدة
│   │       ├── eureka-server/     # خادم اكتشاف الخدمات
│   │       ├── config-server/     # خادم التكوين المركزي
│   │       └── monitoring-service/ # خدمة المراقبة والصحة
│   ├── 📁 shared/                 # المكتبات والمكونات المشتركة
│   ├── 📁 comprehensive-system/   # النظام الشامل Python FastAPI
│   └── 📁 api-docs/              # وثائق API التفاعلية
│
├── 📁 frontend/                    # الواجهات الأمامية
│   ├── 📁 admin-dashboard/        # لوحة تحكم الإدارة (React)
│   ├── 📁 user-apps/             # تطبيقات المستخدمين
│   │   ├── 📁 driver-app/        # تطبيق السائقين (React Native)
│   │   └── 📁 passenger-app/     # تطبيق الركاب (React Native)
│   ├── 📁 operator-dashboard/     # لوحة تحكم المشغلين (Angular)
│   └── 📁 shared-components/      # المكونات المشتركة
│
├── 📁 database/                   # قواعد البيانات والمخططات
│   ├── 📁 schemas/               # مخططات قواعد البيانات
│   ├── 📁 migrations/            # ملفات الترحيل
│   ├── 📁 seeds/                 # البيانات الأولية
│   └── 📁 backups/               # النسخ الاحتياطية
│
├── 📁 infrastructure/             # البنية التحتية والنشر
│   ├── 📁 docker/                # ملفات Docker
│   ├── 📁 kubernetes/            # ملفات Kubernetes
│   ├── 📁 terraform/             # Infrastructure as Code
│   └── 📁 monitoring/            # أدوات المراقبة
│
├── 📁 tools/                      # الأدوات المساعدة
│   ├── 📁 scripts/               # سكريبتات التشغيل والإدارة
│   ├── 📁 generators/            # مولدات الكود
│   └── 📁 testing/               # أدوات الاختبار
│
└── 📁 docs/                       # التوثيق الشامل
    ├── 📁 api/                   # توثيق APIs
    ├── 📁 architecture/          # الهندسة المعمارية
    ├── 📁 deployment/            # أدلة النشر
    └── 📁 development/           # أدلة التطوير
```

---

## 🔧 المعمارية التقنية المفصلة

### 1. 🏛️ معمارية الخدمات المصغرة (Microservices Architecture)

#### أ) الخدمات الأساسية (Core Services)
```
🔒 auth-service (Port: 8081)
├── المسؤوليات:
│   ├── المصادقة والتفويض (JWT + OAuth2)
│   ├── إدارة الجلسات والرموز المميزة
│   ├── التحكم في الوصول القائم على الأدوار (RBAC)
│   └── تكامل مع مقدمي الهوية الخارجيين
├── قاعدة البيانات: tecnodrive_auth
├── التقنيات: Spring Security + JWT + Redis
└── APIs: /auth/login, /auth/register, /auth/validate

👤 user-service (Port: 8083)
├── المسؤوليات:
│   ├── إدارة ملفات المستخدمين الشخصية
│   ├── التحقق من الهوية والوثائق
│   ├── إدارة التفضيلات والإعدادات
│   └── تتبع نشاط المستخدمين
├── قاعدة البيانات: tecnodrive_users
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /users/profile, /users/documents, /users/preferences

💰 payment-service (Port: 8085)
├── المسؤوليات:
│   ├── معالجة المدفوعات والمحافظ الرقمية
│   ├── إدارة طرق الدفع المتعددة
│   ├── تتبع المعاملات المالية
│   └── تكامل مع بوابات الدفع المحلية
├── قاعدة البيانات: tecnodrive_payments
├── التقنيات: Spring Boot + Stripe API + PayPal
└── APIs: /payments/process, /wallet/balance, /transactions/history
```

#### ب) خدمات الأعمال (Business Services)
```
🚗 ride-service (Port: 8082)
├── المسؤوليات:
│   ├── إدارة طلبات الرحلات والحجوزات
│   ├── مطابقة السائقين مع الركاب
│   ├── تتبع الرحلات في الوقت الفعلي
│   └── حساب التكاليف والمسافات
├── قاعدة البيانات: tecnodrive_rides
├── التقنيات: Spring Boot + WebSocket + Redis
└── APIs: /rides/request, /rides/track, /rides/complete

🚛 fleet-service (Port: 8084)
├── المسؤوليات:
│   ├── إدارة أسطول المركبات
│   ├── جدولة الصيانة والفحوصات
│   ├── تتبع استهلاك الوقود والأداء
│   └── إدارة تراخيص السائقين
├── قاعدة البيانات: tecnodrive_fleet
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /fleet/vehicles, /fleet/maintenance, /fleet/drivers

📦 parcel-service (Port: 8086)
├── المسؤوليات:
│   ├── إدارة طلبات توصيل الطرود
│   ├── تتبع الطرود عبر المراحل المختلفة
│   ├── إدارة المستودعات ونقاط التوزيع
│   └── حساب تكاليف الشحن
├── قاعدة البيانات: tecnodrive_parcels
├── التقنيات: Spring Boot + JPA + MongoDB
└── APIs: /parcels/create, /parcels/track, /parcels/deliver

📍 location-service (Port: 8087)
├── المسؤوليات:
│   ├── إدارة المواقع الجغرافية والخرائط
│   ├── حساب المسارات المثلى
│   ├── تتبع المواقع في الوقت الفعلي
│   └── تكامل مع خدمات الخرائط الخارجية
├── قاعدة البيانات: tecnodrive_locations
├── التقنيات: Spring Boot + PostGIS + Google Maps API
└── APIs: /locations/geocode, /routes/optimize, /tracking/live

📊 analytics-service (Port: 8088)
├── المسؤوليات:
│   ├── تحليل البيانات والذكاء الاصطناعي
│   ├── إنشاء التقارير والإحصائيات
│   ├── التنبؤ بالطلب والأنماط
│   └── مراقبة الأداء والمؤشرات
├── قاعدة البيانات: tecnodrive_analytics
├── التقنيات: Spring Boot + Apache Spark + TensorFlow
└── APIs: /analytics/reports, /analytics/predictions, /analytics/kpis

🔔 notification-service (Port: 8089)
├── المسؤوليات:
│   ├── إرسال الإشعارات المتعددة القنوات
│   ├── إدارة قوالب الرسائل
│   ├── جدولة الإشعارات المؤجلة
│   └── تتبع معدلات التسليم والقراءة
├── قاعدة البيانات: tecnodrive_notifications
├── التقنيات: Spring Boot + Firebase + Twilio + SMTP
└── APIs: /notifications/send, /notifications/templates, /notifications/status
```

#### ج) خدمات البنية التحتية (Infrastructure Services)
```
🌐 api-gateway (Port: 8080)
├── المسؤوليات:
│   ├── توجيه الطلبات للخدمات المناسبة
│   ├── المصادقة والتفويض المركزي
│   ├── تحديد معدل الطلبات (Rate Limiting)
│   └── مراقبة وتسجيل الطلبات
├── التقنيات: Spring Cloud Gateway + Eureka Client
└── التكوين: Load Balancing + Circuit Breaker

🔍 eureka-server (Port: 8761)
├── المسؤوليات:
│   ├── اكتشاف وتسجيل الخدمات
│   ├── مراقبة صحة الخدمات
│   ├── توزيع الأحمال التلقائي
│   └── إدارة دورة حياة الخدمات
├── التقنيات: Spring Cloud Netflix Eureka
└── واجهة الإدارة: http://localhost:8761

⚙️ config-server (Port: 8888)
├── المسؤوليات:
│   ├── إدارة التكوين المركزي
│   ├── تحديث التكوين بدون إعادة تشغيل
│   ├── إدارة البيئات المختلفة
│   └── تشفير البيانات الحساسة
├── التقنيات: Spring Cloud Config
└── مصدر التكوين: Git Repository

📈 monitoring-service (Port: 9090)
├── المسؤوليات:
│   ├── مراقبة أداء النظام والخدمات
│   ├── جمع المقاييس والسجلات
│   ├── إنشاء التنبيهات التلقائية
│   └── لوحات مراقبة تفاعلية
├── التقنيات: Prometheus + Grafana + ELK Stack
└── واجهة المراقبة: http://localhost:9090
```

### 2. 🗺️ نظام الخرائط التفاعلية المتقدم

#### أ) مقدمو الخرائط المدعومون
```
خدمات الخرائط المتاحة:
├── 🌍 OpenStreetMap (افتراضي)
│   ├── خادم البلاط: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
│   ├── خدمة Nominatim للبحث الجغرافي
│   ├── خدمة OSRM لحساب المسارات
│   └── مجاني ومفتوح المصدر
├── 🗺️ Google Maps
│   ├── يتطلب مفتاح API: GOOGLE_MAPS_API_KEY
│   ├── دقة عالية في البيانات
│   ├── دعم متقدم للمسارات
│   └── تكلفة حسب الاستخدام
├── 🎨 Mapbox
│   ├── يتطلب رمز الوصول: MAPBOX_ACCESS_TOKEN
│   ├── تخصيص متقدم للخرائط
│   ├── أداء عالي
│   └── خطط مرنة للتسعير
└── 🛰️ خرائط الأقمار الصناعية
    ├── صور عالية الدقة
    ├── تحديثات دورية
    └── مناسبة للمناطق النائية
```

#### ب) ميزات الخرائط التفاعلية
```
الطبقات المتاحة:
├── 🚗 طبقة المركبات
│   ├── مواقع المركبات الحالية
│   ├── حالة المركبات (نشط، مشغول، متوقف)
│   ├── معلومات السائق والرحلة
│   └── تحديث فوري كل 5 ثوانٍ
├── 🚦 طبقة حركة المرور
│   ├── مستويات الازدحام
│   ├── السرعة المتوسطة
│   ├── الحوادث والعوائق
│   └── توقعات حركة المرور
├── 📦 طبقة الطرود
│   ├── مواقع الطرود الحالية
│   ├── حالة التسليم
│   ├── مسارات التوصيل
│   └── نقاط التجميع والتوزيع
├── 🔥 خرائط الطلب الحرارية
│   ├── مناطق الطلب العالي
│   ├── تحليل الطلب حسب الوقت
│   ├── توقعات الطلب المستقبلي
│   └── تحسين توزيع الأسطول
└── 🏢 طبقة نقاط الاهتمام
    ├── المطارات والمحطات
    ├── المستشفيات والمراكز الطبية
    ├── المراكز التجارية
    └── المعالم السياحية
```

---

## 🗄️ قواعد البيانات والمخططات التفصيلية

### 1. 📊 نظرة عامة على قواعد البيانات

```
قواعد البيانات الرئيسية (13 قاعدة بيانات):
├── PostgreSQL Databases (11 قاعدة)
│   ├── tecnodrive_auth          # المصادقة والتفويض
│   ├── tecnodrive_users         # بيانات المستخدمين
│   ├── tecnodrive_rides         # إدارة الرحلات
│   ├── tecnodrive_fleet         # إدارة الأسطول
│   ├── tecnodrive_payments      # المدفوعات والمحافظ
│   ├── tecnodrive_parcels       # إدارة الطرود
│   ├── tecnodrive_locations     # المواقع والخرائط
│   ├── tecnodrive_analytics     # التحليلات والتقارير
│   ├── tecnodrive_notifications # الإشعارات
│   ├── tecnodrive_config        # التكوين المركزي
│   └── tecnodrive_monitoring    # المراقبة والسجلات
├── Redis Cache (1 قاعدة)
│   └── tecnodrive_cache         # التخزين المؤقت
└── MongoDB (1 قاعدة)
    └── tecnodrive_documents     # الوثائق والملفات
```

### 2. 🔗 مخططات قواعد البيانات التفصيلية

#### أ) قاعدة بيانات المصادقة (tecnodrive_auth)
```sql
-- جدول المستخدمين للمصادقة
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    role_id BIGINT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- جدول الجلسات النشطة
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255),
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجل المصادقة
CREATE TABLE auth_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- LOGIN, LOGOUT, FAILED_LOGIN, etc.
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ب) قاعدة بيانات المواقع والخرائط (tecnodrive_locations)
```sql
-- جدول المواقع الجغرافية
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255),
    address TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_type VARCHAR(50), -- PICKUP, DROPOFF, WAREHOUSE, etc.
    city VARCHAR(100),
    district VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Yemen',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهرس مكاني للبحث السريع
CREATE INDEX idx_locations_coordinates ON locations USING GIST (
    ST_Point(longitude, latitude)
);

-- جدول تتبع المواقع في الوقت الفعلي
CREATE TABLE location_tracking (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL, -- VEHICLE, DRIVER, PARCEL
    entity_id BIGINT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2), -- km/h
    heading INTEGER, -- degrees (0-360)
    accuracy DECIMAL(5, 2), -- meters
    battery_level INTEGER, -- percentage
    signal_strength INTEGER, -- dBm
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للبحث السريع حسب النوع والمعرف
CREATE INDEX idx_tracking_entity ON location_tracking (entity_type, entity_id);
CREATE INDEX idx_tracking_timestamp ON location_tracking (timestamp);

-- جدول المسارات المحسوبة
CREATE TABLE calculated_routes (
    id BIGSERIAL PRIMARY KEY,
    route_name VARCHAR(255),
    start_location JSONB NOT NULL,
    end_location JSONB NOT NULL,
    waypoints JSONB, -- array of coordinates
    route_geometry JSONB, -- GeoJSON LineString
    total_distance DECIMAL(10, 2), -- kilometers
    estimated_duration INTEGER, -- minutes
    optimization_type VARCHAR(50), -- SHORTEST, FASTEST, FUEL_EFFICIENT
    traffic_considered BOOLEAN DEFAULT false,
    created_by BIGINT,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المناطق الجغرافية (Geofences)
CREATE TABLE geofences (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fence_type VARCHAR(20) NOT NULL, -- CIRCLE, POLYGON
    geometry JSONB NOT NULL, -- GeoJSON geometry
    radius DECIMAL(8, 2), -- meters (for circle type)
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ج) قاعدة بيانات الطرود المحسنة (tecnodrive_parcels)
```sql
-- جدول الطرود الرئيسي
CREATE TABLE parcels (
    id BIGSERIAL PRIMARY KEY,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    parcel_type VARCHAR(50) NOT NULL, -- DOCUMENT, PACKAGE, FRAGILE, etc.
    weight DECIMAL(8, 3), -- kg
    dimensions JSONB, -- {length, width, height} in cm
    declared_value DECIMAL(12, 2),
    pickup_location JSONB NOT NULL,
    delivery_location JSONB NOT NULL,
    pickup_instructions TEXT,
    delivery_instructions TEXT,
    status VARCHAR(30) DEFAULT 'CREATED', -- CREATED, PICKED_UP, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED, CANCELLED
    priority_level VARCHAR(20) DEFAULT 'STANDARD', -- EXPRESS, STANDARD, ECONOMY
    payment_method VARCHAR(30),
    shipping_cost DECIMAL(10, 2),
    insurance_cost DECIMAL(10, 2) DEFAULT 0,
    cod_amount DECIMAL(12, 2) DEFAULT 0, -- Cash on Delivery
    scheduled_pickup_time TIMESTAMP,
    actual_pickup_time TIMESTAMP,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    assigned_driver_id BIGINT,
    assigned_vehicle_id BIGINT,
    current_warehouse_id BIGINT,
    delivery_proof JSONB, -- photos, signatures, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تتبع حالة الطرود
CREATE TABLE parcel_status_history (
    id BIGSERIAL PRIMARY KEY,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    status VARCHAR(30) NOT NULL,
    location JSONB,
    notes TEXT,
    updated_by BIGINT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستودعات
CREATE TABLE warehouses (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    location JSONB NOT NULL, -- {lat, lng}
    capacity INTEGER, -- number of parcels
    current_load INTEGER DEFAULT 0,
    operating_hours JSONB, -- {open_time, close_time} for each day
    contact_info JSONB, -- {phone, email, manager}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون المستودعات
CREATE TABLE warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    warehouse_id BIGINT REFERENCES warehouses(id) ON DELETE CASCADE,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    shelf_location VARCHAR(50),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatched_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'STORED' -- STORED, DISPATCHED
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
├── location-service ↔ All Services
│   └── تتبع المواقع لجميع الكيانات
├── parcel-service ↔ fleet-service
│   └── parcels.assigned_driver_id → drivers.id
│   └── parcels.assigned_vehicle_id → vehicles.id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود المحسنة

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. معالجة الطلب:
   parcel-service → warehouse-management
   │                 │
   ▼                 ▼
   inventory-system → route-optimization
   │                 │
   ▼                 ▼
   fleet-service

3. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

4. الالتقاط والنقل:
   driver-app → parcel-service
   │                 │
   ▼                 ▼
   location-service → warehouse-system
   │                 │
   ▼                 ▼
   tracking-updates

5. التسليم النهائي:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
   │                 │
   ▼                 ▼
   customer-feedback
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
│   ├── تسجيل دخول بالهاتف/البريد الإلكتروني
│   ├── التحقق من الهوية بالرسائل النصية
│   ├── تسجيل الدخول بالبصمة/الوجه
│   └── تسجيل الدخول بوسائل التواصل الاجتماعي
├── 📍 تحديد المواقع والوجهات
│   ├── تحديد الموقع الحالي تلقائ
│   ├── البحث الذكي للعناوين
│   ├── حفظ العناوين المفضلة
│   └── اقتراح الوجهات الشائعة
├── 🚗 طلب الرحلات المختلفة
│   ├── رحلات فردية (اقتصادية، مريحة، فاخرة)
│   ├── رحلات مشتركة لتوفير التكلفة
│   ├── رحلات مجدولة مسبق
│   └── رحلات طويلة المدى
├── 💰 إدارة المحفظة والمدفوعات
│   ├── عرض رصيد المحفظة الرقمية
│   ├── إضافة أموال للمحفظة
│   ├── ربط بطاقات الائتمان/الخصم
│   └── تاريخ المعاملات المالية
├── 📊 تتبع الرحلات في الوقت الفعلي
│   ├── موقع السائق على الخريطة
│   ├── الوقت المتوقع للوصول
│   ├── معلومات السائق والمركبة
│   └── إمكانية التواصل مع السائق
├── ⭐ تقييم السائقين
│   ├── تقييم من 1-5 نجوم
│   ├── كتابة تعليقات
│   ├── الإبلاغ عن مشاكل
│   └── إضافة السائقين للمفضلة
├── 📱 الإشعارات الفورية
│   ├── تأكيد قبول الرحلة
│   ├── تحديثات حالة الرحلة
│   ├── العروض والخصومات
│   └── تذكيرات الرحلات المجدولة
└── 📈 تاريخ الرحلات والفواتير
    ├── سجل جميع الرحلات السابقة
    ├── تفاصيل التكلفة لكل رحلة
    ├── تحميل الفواتير
    └── إحصائيات الاستخدام الشهرية

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
│   ├── مصادقة قوية للسائقين
│   ├── التحقق من صحة الرخصة
│   ├── فحص الخلفية الجنائية
│   └── تحديث الوثائق دور
├── 🟢 تبديل حالة الاتصال (Online/Offline)
│   ├── تشغيل/إيقاف استقبال الطلبات
│   ├── تحديد ساعات العمل
│   ├── وضع الاستراحة
│   └── حالة الطوارئ
├── 📍 تتبع الموقع التلقائي
│   ├── GPS عالي الدقة
│   ├── تحديث الموقع كل 5 ثوانٍ
│   ├── توفير البطارية الذكي
│   └── العمل في المناطق ضعيفة الإشارة
├── 🔔 استقبال طلبات الرحلات
│   ├── تنبيهات صوتية ومرئية
│   ├── معلومات الراكب والوجهة
│   ├── تقدير الأرباح
│   └── خيار القبول/الرفض
├── 🗺️ التنقل والمسارات
│   ├── تكامل مع خرائط Google/OpenStreetMap
│   ├── مسارات محسنة لتجنب الازدحام
│   ├── إرشادات صوتية
│   └── تحديثات حركة المرور الفورية
├── 💰 تتبع الأرباح
│   ├── الأرباح اليومية والأسبوعية
│   ├── تفاصيل كل رحلة
│   ├── العمولات والخصومات
│   └── تقارير الضرائب
├── ⭐ تقييم الركاب
│   ├── تقييم تجربة الرحلة
│   ├── الإبلاغ عن مشاكل
│   ├── حظر ركاب مشكلين
│   └── تفضيل ركاب معينين
├── 📊 إحصائيات الأداء
│   ├── معدل القبول
│   ├── التقييم العام
│   ├── عدد الرحلات المكتملة
│   └── مقارنة مع السائقين الآخرين
└── 🚗 إدارة المركبة
    ├── معلومات المركبة
    ├── جدولة الصيانة
    ├── تتبع استهلاك الوقود
    └── تقارير الأعطال

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js + D3.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   │   ├── عدد الرحلات النشطة
│   │   ├── عدد السائقين المتصلين
│   │   ├── عدد الطرود قيد التوصيل
│   │   └── الإيرادات اليومية
│   ├── مؤشرات الأداء الرئيسية (KPIs)
│   │   ├── معدل إكمال الرحلات
│   │   ├── متوسط وقت الاستجابة
│   │   ├── رضا العملاء
│   │   └── كفاءة الأسطول
│   ├── الرسوم البيانية التفاعلية
│   │   ├── اتجاهات الطلب
│   │   ├── توزيع الرحلات جغراف
│   │   ├── أداء السائقين
│   │   └── تحليل الإيرادات
│   └── التنبيهات والإشعارات
│       ├── تنبيهات النظام
│       ├── مشاكل تقنية
│       ├── شكاوى العملاء
│       └── تحديثات مهمة
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   │   ├── قائمة شاملة بالمستخدمين
│   │   ├── تفاصيل الملفات الشخصية
│   │   ├── إحصائيات الاستخدام
│   │   └── إدارة الحسابات
│   ├── التحقق من الهوية
│   │   ├── مراجعة الوثائق المرفوعة
│   │   ├── التحقق من صحة البيانات
│   │   ├── الموافقة/الرفض
│   │   └── طلب وثائق إضافية
│   ├── إدارة الأدوار والصلاحيات
│   │   ├── تعريف الأدوار
│   │   ├── تخصيص الصلاحيات
│   │   ├── إدارة الوصول
│   │   └── مراجعة الأنشطة
│   └── سجل النشاطات
│       ├── تسجيل الدخول/الخروج
│       ├── العمليات المنجزة
│       ├── التغييرات على البيانات
│       └── الأنشطة المشبوهة
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   │   ├── قائمة المركبات
│   │   ├── حالة كل مركبة
│   │   ├── تخصيص السائقين
│   │   └── تتبع الأداء
│   ├── جدولة الصيانة
│   │   ├── جدولة الصيانة الدورية
│   │   ├── تتبع تكاليف الصيانة
│   │   ├── تذكيرات الصيانة
│   │   └── تقارير الأعطال
│   ├── تتبع الأداء
│   │   ├── استهلاك الوقود
│   │   ├── المسافات المقطوعة
│   │   ├── ساعات التشغيل
│   │   └── معدلات الأعطال
│   └── إدارة التراخيص
│       ├── تراخيص السائقين
│       ├── تراخيص المركبات
│       ├── التأمين
│       └── تجديد الوثائق
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   │   ├── معالجة المدفوعات
│   │   ├── إنشاء الفواتير
│   │   ├── تتبع المستحقات
│   │   └── إدارة المبالغ المستردة
│   ├── أرباح السائقين
│   │   ├── حساب العمولات
│   │   ├── جدولة المدفوعات
│   │   ├── تقارير الأرباح
│   │   └── إدارة الحوافز
│   ├── التقارير المالية
│   │   ├── تقارير الإيرادات
│   │   ├── تحليل التكاليف
│   │   ├── الربحية
│   │   └── التنبؤات المالية
│   └── إعدادات الأسعار
│       ├── تحديد أسعار الرحلات
│       ├── أسعار الطرود
│       ├── العروض والخصومات
│       └── الأسعار الديناميكية
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   │   ├── قائمة الطلبات
│   │   ├── تفاصيل كل طلب
│   │   ├── تخصيص السائقين
│   │   └── جدولة التوصيل
│   ├── تتبع الشحنات
│   │   ├── تتبع فوري للطرود
│   │   ├── تحديثات الحالة
│   │   ├── إشعارات العملاء
│   │   └── حل المشاكل
│   ├── إدارة المستودعات
│   │   ├── مخزون المستودعات
│   │   ├── عمليات الاستلام والإرسال
│   │   ├── تحسين التخزين
│   │   └── إدارة الموظفين
│   └── تقارير التوصيل
│       ├── معدلات التسليم
│       ├── أوقات التوصيل
│       ├── رضا العملاء
│       └── تحليل الأداء
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   │   ├── تحليل سلوك العملاء
│   │   ├── أنماط الاستخدام
│   │   ├── تحليل الطلب
│   │   └── تحليل الأداء
│   ├── التنبؤات الذكية
│   │   ├── توقع الطلب
│   │   ├── تحسين الأسطول
│   │   ├── التنبؤ بالإيرادات
│   │   └── تحليل المخاطر
│   ├── تقارير مخصصة
│   │   ├── إنشاء تقارير حسب الطلب
│   │   ├── جدولة التقارير
│   │   ├── تخصيص المحتوى
│   │   └── توزيع التقارير
│   └── تصدير البيانات
│       ├── تصدير إلى Excel/CSV
│       ├── تكامل مع أنظمة خارجية
│       ├── APIs للبيانات
│       └── النسخ الاحتياطية
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    │   ├── إعدادات المنصة
    │   ├── معاملات النظام
    │   ├── إعدادات الأمان
    │   └── تخصيص الواجهة
    ├── إدارة الإشعارات
    │   ├── قوالب الرسائل
    │   ├── قنوات الإرسال
    │   ├── جدولة الإشعارات
    │   └── تتبع التسليم
    ├── النسخ الاحتياطية
    │   ├── جدولة النسخ الاحتياطية
    │   ├── استعادة البيانات
    │   ├── تشفير النسخ
    │   └── التخزين السحابي
    └── سجلات النظام
        ├── سجلات الأنشطة
        ├── سجلات الأخطاء
        ├── سجلات الأداء
        └── سجلات الأمان
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js + Socket.io
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   │   ├── خريطة تفاعلية للرحلات الجارية
│   │   ├── تفاصيل كل رحلة
│   │   ├── التدخل في حالات الطوارئ
│   │   └── إعادة توجيه الرحلات
│   ├── حالة السائقين
│   │   ├── السائقين المتصلين/غير المتصلين
│   │   ├── موقع كل سائق
│   │   ├── حالة المركبات
│   │   └── أداء السائقين
│   ├── طوارئ ومشاكل
│   │   ├── تنبيهات الطوارئ الفورية
│   │   ├── حوادث المرور
│   │   ├── أعطال المركبات
│   │   └── شكاوى العملاء العاجلة
│   └── تدخل سريع
│       ├── إرسال مساعدة فورية
│       ├── تحويل الرحلات
│       ├── التواصل مع الطوارئ
│       └── تنسيق الإنقاذ
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   │   ├── عرض جميع المركبات
│   │   ├── طبقات متعددة للبيانات
│   │   ├── تحديث فوري للمواقع
│   │   └── أدوات تحليل مكانية
│   ├── تتبع المركبات
│   │   ├── مسار كل مركبة
│   │   ├── سرعة وحالة المركبة
│   │   ├── تاريخ المواقع
│   │   └── تنبيهات الانحراف
│   ├── تحليل المناطق
│   │   ├── مناطق الطلب العالي
│   │   ├── نقاط الازدحام
│   │   ├── أوقات الذروة
│   │   └── تحسين التغطية
│   └── إدارة المسارات
│       ├── تحسين المسارات
│       ├── تجنب الازدحام
│       ├── مسارات الطوارئ
│       └── تحديث المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   │   ├── تنبيهات النظام
│   │   ├── تنبيهات الأمان
│   │   ├── تنبيهات الأداء
│   │   └── تنبيهات العملاء
│   ├── إدارة الطوارئ
│   │   ├── بروتوكولات الطوارئ
│   │   ├── فرق الاستجابة
│   │   ├── تنسيق الإنقاذ
│   │   └── تقارير الحوادث
│   ├── تواصل مع السائقين
│   │   ├── رسائل فورية
│   │   ├── مكالمات صوتية
│   │   ├── تعليمات التنقل
│   │   └── تحديثات الحالة
│   └── دعم العملاء
│       ├── استقبال الشكاوى
│       ├── حل المشاكل
│       ├── متابعة الطلبات
│       └── تحسين الخدمة
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   │   ├── معدلات إكمال الرحلات
│   │   ├── أوقات الاستجابة
│   │   ├── جودة الخدمة
│   │   └── مقارنات الأداء
│   ├── معدلات الاستجابة
│   │   ├── وقت الوصول للعملاء
│   │   ├── سرعة حل المشاكل
│   │   ├── كفاءة التوزيع
│   │   └── تحسين العمليات
│   ├── رضا العملاء
│   │   ├── تقييمات العملاء
│   │   ├── شكاوى ومقترحات
│   │   ├── معدلات الاحتفاظ
│   │   └── تحليل التغذية الراجعة
│   └── كفاءة الأسطول
│       ├── استخدام المركبات
│       ├── استهلاك الوقود
│       ├── صيانة المركبات
│       └── تحسين التشغيل
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    │   ├── إجراءات الطوارئ
    │   ├── خطط الإخلاء
    │   ├── تدريب الموظفين
    │   └── تحديث البروتوكولات
    ├── تتبع الحوادث
    │   ├── تسجيل الحوادث
    │   ├── تحليل الأسباب
    │   ├── إجراءات التحقيق
    │   └── منع تكرار الحوادث
    ├── تنسيق الإنقاذ
    │   ├── فرق الإنقاذ
    │   ├── المعدات الطبية
    │   ├── التنسيق مع السلطات
    │   └── متابعة الحالات
    └── تقارير الحوادث
        ├── تقارير مفصلة
        ├── إحصائيات الحوادث
        ├── تحليل الاتجاهات
        └── توصيات التحسين
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل المحسنة
```powershell
# سكريبت التشغيل الرئيسي المحسن
start-platform.ps1
├── المعاملات المتقدمة:
│   ├── -Mode (development/staging/production/minimal)
│   ├── -Services (comma-separated list of services)
│   ├── -SkipDatabase (تخطي تشغيل قواعد البيانات)
│   ├── -SkipMicroservices (تخطي الخدمات المصغرة)
│   ├── -SkipFrontend (تخطي الواجهات الأمامية)
│   ├── -SkipComprehensive (تخطي النظام الشامل)
│   ├── -EnableMonitoring (تفعيل المراقبة)
│   ├── -EnableLogging (تفعيل السجلات المفصلة)
│   ├── -HealthCheck (فحص صحة النظام)
│   └── -Parallel (تشغيل متوازي للخدمات)
├── الوظائف المتقدمة:
│   ├── فحص المتطلبات والتبعيات
│   ├── تشغيل قواعد البيانات بالترتيب الصحيح
│   ├── تشغيل الخدمات المصغرة مع مراقبة الحالة
│   ├── تشغيل الواجهات الأمامية مع Hot Reload
│   ├── مراقبة الحالة المستمرة
│   ├── إنشاء تقارير التشغيل
│   └── إعداد البيئة التطويرية
├── ميزات الأمان:
│   ├── التحقق من صحة التكوين
│   ├── تشفير البيانات الحساسة
│   ├── مراقبة الوصول غير المصرح
│   └── تسجيل جميع العمليات
└── التحسينات:
    ├── تحسين استخدام الذاكرة
    ├── تحسين أداء الشبكة
    ├── تحسين استخدام المعالج
    └── تحسين استخدام القرص

# سكريبت الإيقاف المحسن
stop-platform.ps1
├── المعاملات:
│   ├── -Force (إيقاف قسري)
│   ├── -Graceful (إيقاف تدريجي)
│   ├── -KeepDatabase (الاحتفاظ بقواعد البيانات)
│   ├── -KeepCache (الاحتفاظ بالتخزين المؤقت)
│   ├── -SaveState (حفظ حالة النظام)
│   └── -Backup (إنشاء نسخة احتياطية)
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب الصحيح
│   ├── تنظيف الموارد والذاكرة
│   ├── حفظ السجلات والتقارير
│   ├── إنشاء نسخ احتياطية
│   ├── تقرير الحالة النهائية
│   └── تنظيف الملفات المؤقتة

# سكريبت الاختبار الشامل
test-platform.ps1
├── المعاملات:
│   ├── -TestType (unit/integration/performance/security)
│   ├── -DatabaseOnly (اختبار قواعد البيانات فقط)
│   ├── -ServicesOnly (اختبار الخدمات فقط)
│   ├── -FullTest (اختبار شامل)
│   ├── -LoadTest (اختبار الأحمال)
│   ├── -SecurityTest (اختبار الأمان)
│   └── -GenerateReport (إنشاء تقرير مفصل)
├── الاختبارات:
│   ├── اختبار اتصال قواعد البيانات
│   ├── اختبار صحة الخدمات
│   ├── اختبار APIs والواجهات
│   ├── اختبار التكامل بين الخدمات
│   ├── اختبار
    total_earnings DECIMAL(12, 2) DEFAULT 0,
    average_rating DECIMAL(3, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط السائقين بالمركبات
CREATE TABLE driver_vehicle_assignments (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT REFERENCES drivers(id) ON DELETE CASCADE,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- جدول صيانة المركبات
CREATE TABLE vehicle_maintenance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL, -- ROUTINE, REPAIR, INSPECTION
    description TEXT NOT NULL,
    cost DECIMAL(10, 2),
    service_provider VARCHAR(100),
    scheduled_date DATE,
    completed_date DATE,
    next_service_date DATE,
    odometer_at_service DECIMAL(10, 2),
    status VARCHAR(20) DEFAULT 'SCHEDULED', -- SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول استهلاك الوقود
CREATE TABLE fuel_consumption (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT REFERENCES drivers(id),
    fuel_amount DECIMAL(8, 2) NOT NULL, -- liters
    cost DECIMAL(10, 2) NOT NULL,
    odometer_reading DECIMAL(10, 2),
    fuel_station VARCHAR(100),
    refuel_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### هـ) قاعدة بيانات المدفوعات (tecnodrive_payments)
```sql
-- جدول المحافظ الرقمية
CREATE TABLE wallets (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(12, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'YER', -- Yemeni Rial
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, FROZEN
    daily_limit DECIMAL(12, 2),
    monthly_limit DECIMAL(12, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    wallet_id BIGINT REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- CREDIT, DEBIT, TRANSFER
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    description TEXT,
    reference_id VARCHAR(100), -- Reference to ride, parcel, etc.
    reference_type VARCHAR(50), -- RIDE_PAYMENT, PARCEL_PAYMENT, TOP_UP, etc.
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED, CANCELLED
    payment_method VARCHAR(50), -- WALLET, CARD, BANK_TRANSFER, CASH
    external_transaction_id VARCHAR(100),
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طرق الدفع
CREATE TABLE payment_methods (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL, -- CREDIT_CARD, DEBIT_CARD, BANK_ACCOUNT, MOBILE_WALLET
    provider VARCHAR(50), -- VISA, MASTERCARD, PAYPAL, etc.
    last_four_digits VARCHAR(4),
    expiry_month INTEGER,
    expiry_year INTEGER,
    cardholder_name VARCHAR(100),
    is_default BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    token VARCHAR(255), -- Tokenized card details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- RIDE, PARCEL, SUBSCRIPTION
    service_id BIGINT NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_amount DECIMAL(12, 2) DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, CANCELLED
    due_date DATE,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عمولات السائقين
CREATE TABLE driver_earnings (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT NOT NULL,
    ride_id BIGINT,
    gross_amount DECIMAL(12, 2) NOT NULL,
    commission_rate DECIMAL(5, 4) NOT NULL, -- e.g., 0.15 for 15%
    commission_amount DECIMAL(12, 2) NOT NULL,
    net_amount DECIMAL(12, 2) NOT NULL,
    bonus_amount DECIMAL(12, 2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, WITHHELD
    payout_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

3. تتبع الطرد:
   parcel-service ↔ location-service
   │                 │
   ▼                 ▼
   customer-app ← driver-app

4. تسليم الطرد:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
├── 📍 تحديد المواقع والوجهات
├── 🚗 طلب الرحلات المختلفة
├── 💰 إدارة المحفظة والمدفوعات
├── 📊 تتبع الرحلات في الوقت الفعلي
├── ⭐ تقييم السائقين
├── 📱 الإشعارات الفورية
└── 📈 تاريخ الرحلات والفواتير

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
├── 🟢 تبديل حالة الاتصال (Online/Offline)
├── 📍 تتبع الموقع التلقائي
├── 🔔 استقبال طلبات الرحلات
├── 🗺️ التنقل والمسارات
├── 💰 تتبع الأرباح
├── ⭐ تقييم الركاب
├── 📊 إحصائيات الأداء
└── 🚗 إدارة المركبة

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   ├── مؤشرات الأداء الرئيسية
│   ├── الرسوم البيانية التفاعلية
│   └── التنبيهات والإشعارات
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   ├── التحقق من الهوية
│   ├── إدارة الأدوار والصلاحيات
│   └── سجل النشاطات
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   ├── جدولة الصيانة
│   ├── تتبع الأداء
│   └── إدارة التراخيص
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   ├── أرباح السائقين
│   ├── التقارير المالية
│   └── إعدادات الأسعار
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   ├── تتبع الشحنات
│   ├── إدارة المستودعات
│   └── تقارير التوصيل
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   ├── التنبؤات الذكية
│   ├── تقارير مخصصة
│   └── تصدير البيانات
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    ├── إدارة الإشعارات
    ├── النسخ الاحتياطية
    └── سجلات النظام
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   ├── حالة السائقين
│   ├── طوارئ ومشاكل
│   └── تدخل سريع
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   ├── تتبع المركبات
│   ├── تحليل المناطق
│   └── إدارة المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   ├── إدارة الطوارئ
│   ├── تواصل مع السائقين
│   └── دعم العملاء
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   ├── معدلات الاستجابة
│   ├── رضا العملاء
│   └── كفاءة الأسطول
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    ├── تتبع الحوادث
    ├── تنسيق الإنقاذ
    └── تقارير الحوادث
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل
```powershell
# سكريبت التشغيل الرئيسي
start-platform.ps1
├── المعاملات:
│   ├── -Mode (development/production/minimal)
│   ├── -SkipDatabase
│   ├── -SkipMicroservices
│   ├── -SkipFrontend
│   └── -SkipComprehensive
├── الوظائف:
│   ├── فحص المتطلبات
│   ├── تشغيل قواعد البيانات
│   ├── تشغيل الخدمات المصغرة
│   ├── تشغيل الواجهات الأمامية
│   └── مراقبة الحالة

# سكريبت الإيقاف
stop-platform.ps1
├── المعاملات:
│   ├── -Force
│   ├── -KeepDatabase
│   └── -KeepCache
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب
│   ├── تنظيف الموارد
│   ├── حفظ السجلات
│   └── تقرير الحالة النهائية

# سكريبت الاختبار
test-platform.ps1
├── المعاملات:
│   ├── -DatabaseOnly
│   ├── -ServicesOnly
│   └── -FullTest
├── الاختبارات:
│   ├── اتصال قواعد البيانات
│   ├── صحة الخدمات
│   ├── اختبار APIs
│   └── اختبار التكامل
```

### 2. 🛠️ أدوات التطوير
```
tools/
├── generators/
│   ├── service-generator.js      # مولد الخدمات المصغرة
│   ├── api-generator.js          # مولد APIs
│   ├── database-generator.js     # مولد مخططات قواعد البيانات
│   └── frontend-generator.js     # مولد مكونات الواجهة
├── testing/
│   ├── load-testing/            # اختبارات الحمولة
│   ├── integration-testing/     # اختبارات التكامل
│   ├── performance-testing/     # اختبارات الأداء
│   └── security-testing/        # اختبارات الأمان
├── monitoring/
│   ├── health-check.js          # فحص صحة النظام
│   ├── performance-monitor.js   # مراقبة الأداء
│   ├── log-analyzer.js          # تحليل السجلات
│   └── alert-manager.js         # إدارة التنبيهات
└── deployment/
    ├── docker-builder.js        # بناء صور Docker
    ├── k8s-deployer.js          # نشر على Kubernetes
    ├── backup-manager.js        # إدارة النسخ الاحتياطية
    └── migration-runner.js      # تشغيل ترحيل البيانات
```

---

## 🔒 الأمان والحماية

### 1. 🛡️ طبقات الأمان
```
طبقات الحماية المتعددة:
├── 🌐 طبقة الشبكة
│   ├── HTTPS/TLS 1.3
│   ├── WAF (Web Application Firewall)
│   ├── DDoS Protection
│   └── IP Whitelisting
├── 🔐 طبقة المصادقة
│   ├── JWT + Refresh Tokens
│   ├── OAuth2 / OpenID Connect
│   ├── Multi-Factor Authentication
│   └── Biometric Authentication
├── 🔑 طبقة التفويض
│   ├── Role-Based Access Control (RBAC)
│   ├── Attribute-Based Access Control (ABAC)
│   ├── API Rate Limiting
│   └── Resource-Level Permissions
├── 💾 طبقة البيانات
│   ├── Database Encryption at Rest
│   ├── Field-Level Encryption
│   ├── Data Masking
│   └── Audit Logging
└── 🔍 طبقة المراقبة
    ├── Security Information and Event Management (SIEM)
    ├── Intrusion Detection System (IDS)
    ├── Vulnerability Scanning
    └── Penetration Testing
```

### 2. 🔐 إدارة المفاتيح والأسرار
```
إدارة الأسرار:
├── HashiCorp Vault
│   ├── تشفير المفاتيح
│   ├── دوران المفاتيح التلقائي
│   ├── إدارة الشهادات
│   └── سياسات الوصول
├── Kubernetes Secrets
│   ├── أسرار قواعد البيانات
│   ├── مفاتيح APIs الخارجية
│   ├── شهادات TLS
│   └── متغيرات البيئة الحساسة
└── Environment-Specific Configs
    ├── Development Environment
    ├── Staging Environment
    ├── Production Environment
    └── Disaster Recovery Environment
```

---

## 📈 المراقبة والتحليلات

### 1. 📊 نظام المراقبة الشامل
```
مكونات المراقبة:
├── 📈 Prometheus + Grafana
│   ├── مقاييس الأداء
│   ├── استخدام الموارد
│   ├── معدلات الاستجابة
│   └── لوحات مراقبة تفاعلية
├── 📋 ELK Stack (Elasticsearch + Logstash + Kibana)
│   ├── جمع السجلات المركزي
│   ├── تحليل السجلات
│   ├── البحث في السجلات
│   └── تصور البيانات
├── 🔍 Jaeger (Distributed Tracing)
│   ├── تتبع الطلبات عبر الخدمات
│   ├── تحليل زمن الاستجابة
│   ├── اكتشاف الاختناقات
│   └── تحليل الأخطاء
└── 🚨 AlertManager
    ├── تنبيهات الأداء
    ├── تنبيهات الأمان
    ├── تنبيهات الأخطاء
    └── تصعيد التنبيهات
```

### 2. 🤖 الذكاء الاصطناعي والتحليلات
```
وحدات الذكاء الاصطناعي:
├── 🧠 Machine Learning Models
│   ├── تنبؤ الطلب
│   ├── تحسين المسارات
│   ├── تحليل السلوك
│   └── كشف الاحتيال
├── 📊 Real-time Analytics
│   ├── تحليل البيانات الفورية
│   ├── مؤشرات الأداء المباشرة
│   ├── تحليل الاتجاهات
│   └── التنبؤات قصيرة المدى
├── 🔍 Business Intelligence
│   ├── تقارير تنفيذية
│   ├── تحليل الربحية
│   ├── تحليل العملاء
│   └── تحليل السوق
└── 🎯 Recommendation Engine
    ├── توصيات للركاب
    ├── تحسين توزيع السائقين
    ├── تحسين الأسعار
    └── تحسين الخدمات
```

---

## 🚀 خطة التطوير والتوسع

### 1. 📅 خارطة الطريق (Roadmap)
```
المراحل القادمة:
├── 🎯 المرحلة 1 (Q1 2024): الإطلاق المحلي
│   ├── إطلاق في صنعاء وعدن
│   ├── 100 سائق و 1000 مستخدم
│   ├── خدمات الرحلات الأساسية
│   └── دعم اللغة العربية الكامل
├── 🎯 المرحلة 2 (Q2 2024): التوسع الوطني
│   ├── تغطية جميع المحافظات اليمنية
│   ├── 500 سائق و 10,000 مستخدم
│   ├── خدمات توصيل الطرود
│   └── تكامل مع البنوك المحلية
├── 🎯 المرحلة 3 (Q3 2024): الميزات المتقدمة
│   ├── الذكاء الاصطناعي والتحليلات
│   ├── خدمات الأسطول للشركات
│   ├── تطبيق الويب الكامل
│   └── APIs للمطورين الخارجيين
└── 🎯 المرحلة 4 (Q4 2024): التوسع الإقليمي
    ├── دول الخليج العربي
    ├── 5,000 سائق و 100,000 مستخدم
    ├── خدمات متعددة العملات
    └── شراكات استراتيجية
```

### 2. 🔧 التحسينات التقنية المستقبلية
```
التطويرات المخططة:
├── 🌐 تقنيات الجيل القادم
│   ├── 5G Integration
│   ├── Edge Computing
│   ├── Blockchain للشفافية
│   └── IoT للمركبات الذكية
├── 🤖 الذكاء الاصطناعي المتقدم
│   ├── Computer Vision للأمان
│   ├── Natural Language Processing
│   ├── Predictive Maintenance
│   └── Autonomous Vehicle Support
├── 🔒 أمان محسن
│   ├── Zero Trust Architecture
│   ├── Quantum-Safe Cryptography
│   ├── Advanced Threat Detection
│   └── Privacy-Preserving Analytics
└── 🚀 أداء محسن
    ├── Serverless Architecture
    ├── GraphQL APIs
    ├── Advanced Caching
    └── Global CDN
```

---

## 📋 الخلاصة والتوصيات

### ✅ نقاط القوة الحالية
1. **معمارية قابلة للتوسع**: تصميم microservices متقدم
2. **تغطية شاملة**: جميع جوانب النقل والتوصيل
3. **تقنيات حديثة**: استخدام أحدث التقنيات والأدوات
4. **أمان متقدم**: طبقات حماية متعددة
5. **مراقبة شاملة**: نظام مراقبة متكامل

### 🔧 التحسينات المقترحة
1. **تحسين الأداء**: تحسين استعلامات قواعد البيانات
2. **تطوير الاختبارات**: زيادة تغطية الاختبارات التلقائية
3. **تحسين التوثيق**: توثيق أكثر تفصيلاً للمطورين
4. **تحسين UX**: تحسين تجربة المستخدم في التطبيقات
5. **تحسين DevOps**: أتمتة أكثر لعمليات النشر

### 🎯 الأولويات القادمة
1. **إكمال الاختبارات**: اختبارات شاملة لجميع المكونات
2. **تحسين الأمان**: مراجعة أمنية شاملة
3. **تحسين الأداء**: تحسين أداء النظام تحت الأحمال العالية
4. **التوثيق**: إكمال التوثيق الفني والمستخدم
5. **التدريب**: تدريب الفريق على النظام الجديد

---

*تم إعداد هذا التحليل بناءً على فحص شامل لملفات المشروع في `D:\tecno-drive-platform`*
*آخر تحديث: $(date)*


# 📊 التحليل الشامل والتفصيلي لمنصة TecnoDrive

## 🎯 نظرة عامة على المنصة

**منصة TecnoDrive** هي نظام شامل ومتطور لإدارة النقل والتوصيل يعتمد على معمارية الخدمات المصغرة (Microservices Architecture) مع دعم متعدد المستأجرين (Multi-tenant SaaS Platform).

### 📍 الموقع والمسار
- **المسار الرئيسي**: `D:\tecno-drive-platform`
- **نوع المشروع**: منصة SaaS متكاملة
- **المعمارية**: Microservices + Event-Driven Architecture
- **قواعد البيانات**: PostgreSQL + Redis + MongoDB
- **التقنيات**: Java Spring Boot + Python FastAPI + React + Angular

### 🔢 إحصائيات المشروع
- **عدد الخدمات المصغرة**: 13 خدمة
- **عدد قواعد البيانات**: 13 قاعدة بيانات
- **عدد التطبيقات الأمامية**: 4 تطبيقات
- **عدد ملفات الكود**: 500+ ملف
- **حجم المشروع**: ~2.5 GB

---

## 🏗️ الهيكل التنظيمي للمشروع

### 📁 المجلدات الرئيسية (التسلسل الهرمي)

```
tecno-drive-platform/
├── 📁 backend/                     # الخدمات الخلفية (Backend Services)
│   ├── 📁 microservices/          # الخدمات المصغرة Java Spring Boot
│   │   ├── 📁 core/               # الخدمات الأساسية (3 خدمات)
│   │   │   ├── user-service/      # خدمة إدارة المستخدمين
│   │   │   ├── auth-service/      # خدمة المصادقة والتفويض
│   │   │   └── payment-service/   # خدمة المدفوعات والمحافظ
│   │   ├── 📁 business/           # خدمات الأعمال (6 خدمات)
│   │   │   ├── ride-service/      # خدمة إدارة الرحلات
│   │   │   ├── fleet-service/     # خدمة إدارة الأسطول
│   │   │   ├── parcel-service/    # خدمة الطرود والتوصيل
│   │   │   ├── location-service/  # خدمة المواقع والخرائط
│   │   │   ├── analytics-service/ # خدمة التحليلات والذكاء الاصطناعي
│   │   │   └── notification-service/ # خدمة الإشعارات
│   │   └── 📁 infrastructure/     # خدمات البنية التحتية (4 خدمات)
│   │       ├── api-gateway/       # بوابة API الموحدة
│   │       ├── eureka-server/     # خادم اكتشاف الخدمات
│   │       ├── config-server/     # خادم التكوين المركزي
│   │       └── monitoring-service/ # خدمة المراقبة والصحة
│   ├── 📁 shared/                 # المكتبات والمكونات المشتركة
│   ├── 📁 comprehensive-system/   # النظام الشامل Python FastAPI
│   └── 📁 api-docs/              # وثائق API التفاعلية
│
├── 📁 frontend/                    # الواجهات الأمامية
│   ├── 📁 admin-dashboard/        # لوحة تحكم الإدارة (React)
│   ├── 📁 user-apps/             # تطبيقات المستخدمين
│   │   ├── 📁 driver-app/        # تطبيق السائقين (React Native)
│   │   └── 📁 passenger-app/     # تطبيق الركاب (React Native)
│   ├── 📁 operator-dashboard/     # لوحة تحكم المشغلين (Angular)
│   └── 📁 shared-components/      # المكونات المشتركة
│
├── 📁 database/                   # قواعد البيانات والمخططات
│   ├── 📁 schemas/               # مخططات قواعد البيانات
│   ├── 📁 migrations/            # ملفات الترحيل
│   ├── 📁 seeds/                 # البيانات الأولية
│   └── 📁 backups/               # النسخ الاحتياطية
│
├── 📁 infrastructure/             # البنية التحتية والنشر
│   ├── 📁 docker/                # ملفات Docker
│   ├── 📁 kubernetes/            # ملفات Kubernetes
│   ├── 📁 terraform/             # Infrastructure as Code
│   └── 📁 monitoring/            # أدوات المراقبة
│
├── 📁 tools/                      # الأدوات المساعدة
│   ├── 📁 scripts/               # سكريبتات التشغيل والإدارة
│   ├── 📁 generators/            # مولدات الكود
│   └── 📁 testing/               # أدوات الاختبار
│
└── 📁 docs/                       # التوثيق الشامل
    ├── 📁 api/                   # توثيق APIs
    ├── 📁 architecture/          # الهندسة المعمارية
    ├── 📁 deployment/            # أدلة النشر
    └── 📁 development/           # أدلة التطوير
```

---

## 🔧 المعمارية التقنية المفصلة

### 1. 🏛️ معمارية الخدمات المصغرة (Microservices Architecture)

#### أ) الخدمات الأساسية (Core Services)
```
🔒 auth-service (Port: 8081)
├── المسؤوليات:
│   ├── المصادقة والتفويض (JWT + OAuth2)
│   ├── إدارة الجلسات والرموز المميزة
│   ├── التحكم في الوصول القائم على الأدوار (RBAC)
│   └── تكامل مع مقدمي الهوية الخارجيين
├── قاعدة البيانات: tecnodrive_auth
├── التقنيات: Spring Security + JWT + Redis
└── APIs: /auth/login, /auth/register, /auth/validate

👤 user-service (Port: 8083)
├── المسؤوليات:
│   ├── إدارة ملفات المستخدمين الشخصية
│   ├── التحقق من الهوية والوثائق
│   ├── إدارة التفضيلات والإعدادات
│   └── تتبع نشاط المستخدمين
├── قاعدة البيانات: tecnodrive_users
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /users/profile, /users/documents, /users/preferences

💰 payment-service (Port: 8085)
├── المسؤوليات:
│   ├── معالجة المدفوعات والمحافظ الرقمية
│   ├── إدارة طرق الدفع المتعددة
│   ├── تتبع المعاملات المالية
│   └── تكامل مع بوابات الدفع المحلية
├── قاعدة البيانات: tecnodrive_payments
├── التقنيات: Spring Boot + Stripe API + PayPal
└── APIs: /payments/process, /wallet/balance, /transactions/history
```

#### ب) خدمات الأعمال (Business Services)
```
🚗 ride-service (Port: 8082)
├── المسؤوليات:
│   ├── إدارة طلبات الرحلات والحجوزات
│   ├── مطابقة السائقين مع الركاب
│   ├── تتبع الرحلات في الوقت الفعلي
│   └── حساب التكاليف والمسافات
├── قاعدة البيانات: tecnodrive_rides
├── التقنيات: Spring Boot + WebSocket + Redis
└── APIs: /rides/request, /rides/track, /rides/complete

🚛 fleet-service (Port: 8084)
├── المسؤوليات:
│   ├── إدارة أسطول المركبات
│   ├── جدولة الصيانة والفحوصات
│   ├── تتبع استهلاك الوقود والأداء
│   └── إدارة تراخيص السائقين
├── قاعدة البيانات: tecnodrive_fleet
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /fleet/vehicles, /fleet/maintenance, /fleet/drivers

📦 parcel-service (Port: 8086)
├── المسؤوليات:
│   ├── إدارة طلبات توصيل الطرود
│   ├── تتبع الطرود عبر المراحل المختلفة
│   ├── إدارة المستودعات ونقاط التوزيع
│   └── حساب تكاليف الشحن
├── قاعدة البيانات: tecnodrive_parcels
├── التقنيات: Spring Boot + JPA + MongoDB
└── APIs: /parcels/create, /parcels/track, /parcels/deliver

📍 location-service (Port: 8087)
├── المسؤوليات:
│   ├── إدارة المواقع الجغرافية والخرائط
│   ├── حساب المسارات المثلى
│   ├── تتبع المواقع في الوقت الفعلي
│   └── تكامل مع خدمات الخرائط الخارجية
├── قاعدة البيانات: tecnodrive_locations
├── التقنيات: Spring Boot + PostGIS + Google Maps API
└── APIs: /locations/geocode, /routes/optimize, /tracking/live

📊 analytics-service (Port: 8088)
├── المسؤوليات:
│   ├── تحليل البيانات والذكاء الاصطناعي
│   ├── إنشاء التقارير والإحصائيات
│   ├── التنبؤ بالطلب والأنماط
│   └── مراقبة الأداء والمؤشرات
├── قاعدة البيانات: tecnodrive_analytics
├── التقنيات: Spring Boot + Apache Spark + TensorFlow
└── APIs: /analytics/reports, /analytics/predictions, /analytics/kpis

🔔 notification-service (Port: 8089)
├── المسؤوليات:
│   ├── إرسال الإشعارات المتعددة القنوات
│   ├── إدارة قوالب الرسائل
│   ├── جدولة الإشعارات المؤجلة
│   └── تتبع معدلات التسليم والقراءة
├── قاعدة البيانات: tecnodrive_notifications
├── التقنيات: Spring Boot + Firebase + Twilio + SMTP
└── APIs: /notifications/send, /notifications/templates, /notifications/status
```

#### ج) خدمات البنية التحتية (Infrastructure Services)
```
🌐 api-gateway (Port: 8080)
├── المسؤوليات:
│   ├── توجيه الطلبات للخدمات المناسبة
│   ├── المصادقة والتفويض المركزي
│   ├── تحديد معدل الطلبات (Rate Limiting)
│   └── مراقبة وتسجيل الطلبات
├── التقنيات: Spring Cloud Gateway + Eureka Client
└── التكوين: Load Balancing + Circuit Breaker

🔍 eureka-server (Port: 8761)
├── المسؤوليات:
│   ├── اكتشاف وتسجيل الخدمات
│   ├── مراقبة صحة الخدمات
│   ├── توزيع الأحمال التلقائي
│   └── إدارة دورة حياة الخدمات
├── التقنيات: Spring Cloud Netflix Eureka
└── واجهة الإدارة: http://localhost:8761

⚙️ config-server (Port: 8888)
├── المسؤوليات:
│   ├── إدارة التكوين المركزي
│   ├── تحديث التكوين بدون إعادة تشغيل
│   ├── إدارة البيئات المختلفة
│   └── تشفير البيانات الحساسة
├── التقنيات: Spring Cloud Config
└── مصدر التكوين: Git Repository

📈 monitoring-service (Port: 9090)
├── المسؤوليات:
│   ├── مراقبة أداء النظام والخدمات
│   ├── جمع المقاييس والسجلات
│   ├── إنشاء التنبيهات التلقائية
│   └── لوحات مراقبة تفاعلية
├── التقنيات: Prometheus + Grafana + ELK Stack
└── واجهة المراقبة: http://localhost:9090
```

### 2. 🗺️ نظام الخرائط التفاعلية المتقدم

#### أ) مقدمو الخرائط المدعومون
```
خدمات الخرائط المتاحة:
├── 🌍 OpenStreetMap (افتراضي)
│   ├── خادم البلاط: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
│   ├── خدمة Nominatim للبحث الجغرافي
│   ├── خدمة OSRM لحساب المسارات
│   └── مجاني ومفتوح المصدر
├── 🗺️ Google Maps
│   ├── يتطلب مفتاح API: GOOGLE_MAPS_API_KEY
│   ├── دقة عالية في البيانات
│   ├── دعم متقدم للمسارات
│   └── تكلفة حسب الاستخدام
├── 🎨 Mapbox
│   ├── يتطلب رمز الوصول: MAPBOX_ACCESS_TOKEN
│   ├── تخصيص متقدم للخرائط
│   ├── أداء عالي
│   └── خطط مرنة للتسعير
└── 🛰️ خرائط الأقمار الصناعية
    ├── صور عالية الدقة
    ├── تحديثات دورية
    └── مناسبة للمناطق النائية
```

#### ب) ميزات الخرائط التفاعلية
```
الطبقات المتاحة:
├── 🚗 طبقة المركبات
│   ├── مواقع المركبات الحالية
│   ├── حالة المركبات (نشط، مشغول، متوقف)
│   ├── معلومات السائق والرحلة
│   └── تحديث فوري كل 5 ثوانٍ
├── 🚦 طبقة حركة المرور
│   ├── مستويات الازدحام
│   ├── السرعة المتوسطة
│   ├── الحوادث والعوائق
│   └── توقعات حركة المرور
├── 📦 طبقة الطرود
│   ├── مواقع الطرود الحالية
│   ├── حالة التسليم
│   ├── مسارات التوصيل
│   └── نقاط التجميع والتوزيع
├── 🔥 خرائط الطلب الحرارية
│   ├── مناطق الطلب العالي
│   ├── تحليل الطلب حسب الوقت
│   ├── توقعات الطلب المستقبلي
│   └── تحسين توزيع الأسطول
└── 🏢 طبقة نقاط الاهتمام
    ├── المطارات والمحطات
    ├── المستشفيات والمراكز الطبية
    ├── المراكز التجارية
    └── المعالم السياحية
```

---

## 🗄️ قواعد البيانات والمخططات التفصيلية

### 1. 📊 نظرة عامة على قواعد البيانات

```
قواعد البيانات الرئيسية (13 قاعدة بيانات):
├── PostgreSQL Databases (11 قاعدة)
│   ├── tecnodrive_auth          # المصادقة والتفويض
│   ├── tecnodrive_users         # بيانات المستخدمين
│   ├── tecnodrive_rides         # إدارة الرحلات
│   ├── tecnodrive_fleet         # إدارة الأسطول
│   ├── tecnodrive_payments      # المدفوعات والمحافظ
│   ├── tecnodrive_parcels       # إدارة الطرود
│   ├── tecnodrive_locations     # المواقع والخرائط
│   ├── tecnodrive_analytics     # التحليلات والتقارير
│   ├── tecnodrive_notifications # الإشعارات
│   ├── tecnodrive_config        # التكوين المركزي
│   └── tecnodrive_monitoring    # المراقبة والسجلات
├── Redis Cache (1 قاعدة)
│   └── tecnodrive_cache         # التخزين المؤقت
└── MongoDB (1 قاعدة)
    └── tecnodrive_documents     # الوثائق والملفات
```

### 2. 🔗 مخططات قواعد البيانات التفصيلية

#### أ) قاعدة بيانات المصادقة (tecnodrive_auth)
```sql
-- جدول المستخدمين للمصادقة
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    role_id BIGINT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- جدول الجلسات النشطة
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255),
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجل المصادقة
CREATE TABLE auth_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- LOGIN, LOGOUT, FAILED_LOGIN, etc.
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ب) قاعدة بيانات المواقع والخرائط (tecnodrive_locations)
```sql
-- جدول المواقع الجغرافية
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255),
    address TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_type VARCHAR(50), -- PICKUP, DROPOFF, WAREHOUSE, etc.
    city VARCHAR(100),
    district VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Yemen',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهرس مكاني للبحث السريع
CREATE INDEX idx_locations_coordinates ON locations USING GIST (
    ST_Point(longitude, latitude)
);

-- جدول تتبع المواقع في الوقت الفعلي
CREATE TABLE location_tracking (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL, -- VEHICLE, DRIVER, PARCEL
    entity_id BIGINT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2), -- km/h
    heading INTEGER, -- degrees (0-360)
    accuracy DECIMAL(5, 2), -- meters
    battery_level INTEGER, -- percentage
    signal_strength INTEGER, -- dBm
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للبحث السريع حسب النوع والمعرف
CREATE INDEX idx_tracking_entity ON location_tracking (entity_type, entity_id);
CREATE INDEX idx_tracking_timestamp ON location_tracking (timestamp);

-- جدول المسارات المحسوبة
CREATE TABLE calculated_routes (
    id BIGSERIAL PRIMARY KEY,
    route_name VARCHAR(255),
    start_location JSONB NOT NULL,
    end_location JSONB NOT NULL,
    waypoints JSONB, -- array of coordinates
    route_geometry JSONB, -- GeoJSON LineString
    total_distance DECIMAL(10, 2), -- kilometers
    estimated_duration INTEGER, -- minutes
    optimization_type VARCHAR(50), -- SHORTEST, FASTEST, FUEL_EFFICIENT
    traffic_considered BOOLEAN DEFAULT false,
    created_by BIGINT,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المناطق الجغرافية (Geofences)
CREATE TABLE geofences (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fence_type VARCHAR(20) NOT NULL, -- CIRCLE, POLYGON
    geometry JSONB NOT NULL, -- GeoJSON geometry
    radius DECIMAL(8, 2), -- meters (for circle type)
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ج) قاعدة بيانات الطرود المحسنة (tecnodrive_parcels)
```sql
-- جدول الطرود الرئيسي
CREATE TABLE parcels (
    id BIGSERIAL PRIMARY KEY,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    parcel_type VARCHAR(50) NOT NULL, -- DOCUMENT, PACKAGE, FRAGILE, etc.
    weight DECIMAL(8, 3), -- kg
    dimensions JSONB, -- {length, width, height} in cm
    declared_value DECIMAL(12, 2),
    pickup_location JSONB NOT NULL,
    delivery_location JSONB NOT NULL,
    pickup_instructions TEXT,
    delivery_instructions TEXT,
    status VARCHAR(30) DEFAULT 'CREATED', -- CREATED, PICKED_UP, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED, CANCELLED
    priority_level VARCHAR(20) DEFAULT 'STANDARD', -- EXPRESS, STANDARD, ECONOMY
    payment_method VARCHAR(30),
    shipping_cost DECIMAL(10, 2),
    insurance_cost DECIMAL(10, 2) DEFAULT 0,
    cod_amount DECIMAL(12, 2) DEFAULT 0, -- Cash on Delivery
    scheduled_pickup_time TIMESTAMP,
    actual_pickup_time TIMESTAMP,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    assigned_driver_id BIGINT,
    assigned_vehicle_id BIGINT,
    current_warehouse_id BIGINT,
    delivery_proof JSONB, -- photos, signatures, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تتبع حالة الطرود
CREATE TABLE parcel_status_history (
    id BIGSERIAL PRIMARY KEY,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    status VARCHAR(30) NOT NULL,
    location JSONB,
    notes TEXT,
    updated_by BIGINT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستودعات
CREATE TABLE warehouses (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    location JSONB NOT NULL, -- {lat, lng}
    capacity INTEGER, -- number of parcels
    current_load INTEGER DEFAULT 0,
    operating_hours JSONB, -- {open_time, close_time} for each day
    contact_info JSONB, -- {phone, email, manager}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون المستودعات
CREATE TABLE warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    warehouse_id BIGINT REFERENCES warehouses(id) ON DELETE CASCADE,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    shelf_location VARCHAR(50),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatched_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'STORED' -- STORED, DISPATCHED
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
├── location-service ↔ All Services
│   └── تتبع المواقع لجميع الكيانات
├── parcel-service ↔ fleet-service
│   └── parcels.assigned_driver_id → drivers.id
│   └── parcels.assigned_vehicle_id → vehicles.id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود المحسنة

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. معالجة الطلب:
   parcel-service → warehouse-management
   │                 │
   ▼                 ▼
   inventory-system → route-optimization
   │                 │
   ▼                 ▼
   fleet-service

3. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

4. الالتقاط والنقل:
   driver-app → parcel-service
   │                 │
   ▼                 ▼
   location-service → warehouse-system
   │                 │
   ▼                 ▼
   tracking-updates

5. التسليم النهائي:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
   │                 │
   ▼                 ▼
   customer-feedback
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
│   ├── تسجيل دخول بالهاتف/البريد الإلكتروني
│   ├── التحقق من الهوية بالرسائل النصية
│   ├── تسجيل الدخول بالبصمة/الوجه
│   └── تسجيل الدخول بوسائل التواصل الاجتماعي
├── 📍 تحديد المواقع والوجهات
│   ├── تحديد الموقع الحالي تلقائ
│   ├── البحث الذكي للعناوين
│   ├── حفظ العناوين المفضلة
│   └── اقتراح الوجهات الشائعة
├── 🚗 طلب الرحلات المختلفة
│   ├── رحلات فردية (اقتصادية، مريحة، فاخرة)
│   ├── رحلات مشتركة لتوفير التكلفة
│   ├── رحلات مجدولة مسبق
│   └── رحلات طويلة المدى
├── 💰 إدارة المحفظة والمدفوعات
│   ├── عرض رصيد المحفظة الرقمية
│   ├── إضافة أموال للمحفظة
│   ├── ربط بطاقات الائتمان/الخصم
│   └── تاريخ المعاملات المالية
├── 📊 تتبع الرحلات في الوقت الفعلي
│   ├── موقع السائق على الخريطة
│   ├── الوقت المتوقع للوصول
│   ├── معلومات السائق والمركبة
│   └── إمكانية التواصل مع السائق
├── ⭐ تقييم السائقين
│   ├── تقييم من 1-5 نجوم
│   ├── كتابة تعليقات
│   ├── الإبلاغ عن مشاكل
│   └── إضافة السائقين للمفضلة
├── 📱 الإشعارات الفورية
│   ├── تأكيد قبول الرحلة
│   ├── تحديثات حالة الرحلة
│   ├── العروض والخصومات
│   └── تذكيرات الرحلات المجدولة
└── 📈 تاريخ الرحلات والفواتير
    ├── سجل جميع الرحلات السابقة
    ├── تفاصيل التكلفة لكل رحلة
    ├── تحميل الفواتير
    └── إحصائيات الاستخدام الشهرية

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
│   ├── مصادقة قوية للسائقين
│   ├── التحقق من صحة الرخصة
│   ├── فحص الخلفية الجنائية
│   └── تحديث الوثائق دور
├── 🟢 تبديل حالة الاتصال (Online/Offline)
│   ├── تشغيل/إيقاف استقبال الطلبات
│   ├── تحديد ساعات العمل
│   ├── وضع الاستراحة
│   └── حالة الطوارئ
├── 📍 تتبع الموقع التلقائي
│   ├── GPS عالي الدقة
│   ├── تحديث الموقع كل 5 ثوانٍ
│   ├── توفير البطارية الذكي
│   └── العمل في المناطق ضعيفة الإشارة
├── 🔔 استقبال طلبات الرحلات
│   ├── تنبيهات صوتية ومرئية
│   ├── معلومات الراكب والوجهة
│   ├── تقدير الأرباح
│   └── خيار القبول/الرفض
├── 🗺️ التنقل والمسارات
│   ├── تكامل مع خرائط Google/OpenStreetMap
│   ├── مسارات محسنة لتجنب الازدحام
│   ├── إرشادات صوتية
│   └── تحديثات حركة المرور الفورية
├── 💰 تتبع الأرباح
│   ├── الأرباح اليومية والأسبوعية
│   ├── تفاصيل كل رحلة
│   ├── العمولات والخصومات
│   └── تقارير الضرائب
├── ⭐ تقييم الركاب
│   ├── تقييم تجربة الرحلة
│   ├── الإبلاغ عن مشاكل
│   ├── حظر ركاب مشكلين
│   └── تفضيل ركاب معينين
├── 📊 إحصائيات الأداء
│   ├── معدل القبول
│   ├── التقييم العام
│   ├── عدد الرحلات المكتملة
│   └── مقارنة مع السائقين الآخرين
└── 🚗 إدارة المركبة
    ├── معلومات المركبة
    ├── جدولة الصيانة
    ├── تتبع استهلاك الوقود
    └── تقارير الأعطال

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js + D3.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   │   ├── عدد الرحلات النشطة
│   │   ├── عدد السائقين المتصلين
│   │   ├── عدد الطرود قيد التوصيل
│   │   └── الإيرادات اليومية
│   ├── مؤشرات الأداء الرئيسية (KPIs)
│   │   ├── معدل إكمال الرحلات
│   │   ├── متوسط وقت الاستجابة
│   │   ├── رضا العملاء
│   │   └── كفاءة الأسطول
│   ├── الرسوم البيانية التفاعلية
│   │   ├── اتجاهات الطلب
│   │   ├── توزيع الرحلات جغراف
│   │   ├── أداء السائقين
│   │   └── تحليل الإيرادات
│   └── التنبيهات والإشعارات
│       ├── تنبيهات النظام
│       ├── مشاكل تقنية
│       ├── شكاوى العملاء
│       └── تحديثات مهمة
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   │   ├── قائمة شاملة بالمستخدمين
│   │   ├── تفاصيل الملفات الشخصية
│   │   ├── إحصائيات الاستخدام
│   │   └── إدارة الحسابات
│   ├── التحقق من الهوية
│   │   ├── مراجعة الوثائق المرفوعة
│   │   ├── التحقق من صحة البيانات
│   │   ├── الموافقة/الرفض
│   │   └── طلب وثائق إضافية
│   ├── إدارة الأدوار والصلاحيات
│   │   ├── تعريف الأدوار
│   │   ├── تخصيص الصلاحيات
│   │   ├── إدارة الوصول
│   │   └── مراجعة الأنشطة
│   └── سجل النشاطات
│       ├── تسجيل الدخول/الخروج
│       ├── العمليات المنجزة
│       ├── التغييرات على البيانات
│       └── الأنشطة المشبوهة
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   │   ├── قائمة المركبات
│   │   ├── حالة كل مركبة
│   │   ├── تخصيص السائقين
│   │   └── تتبع الأداء
│   ├── جدولة الصيانة
│   │   ├── جدولة الصيانة الدورية
│   │   ├── تتبع تكاليف الصيانة
│   │   ├── تذكيرات الصيانة
│   │   └── تقارير الأعطال
│   ├── تتبع الأداء
│   │   ├── استهلاك الوقود
│   │   ├── المسافات المقطوعة
│   │   ├── ساعات التشغيل
│   │   └── معدلات الأعطال
│   └── إدارة التراخيص
│       ├── تراخيص السائقين
│       ├── تراخيص المركبات
│       ├── التأمين
│       └── تجديد الوثائق
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   │   ├── معالجة المدفوعات
│   │   ├── إنشاء الفواتير
│   │   ├── تتبع المستحقات
│   │   └── إدارة المبالغ المستردة
│   ├── أرباح السائقين
│   │   ├── حساب العمولات
│   │   ├── جدولة المدفوعات
│   │   ├── تقارير الأرباح
│   │   └── إدارة الحوافز
│   ├── التقارير المالية
│   │   ├── تقارير الإيرادات
│   │   ├── تحليل التكاليف
│   │   ├── الربحية
│   │   └── التنبؤات المالية
│   └── إعدادات الأسعار
│       ├── تحديد أسعار الرحلات
│       ├── أسعار الطرود
│       ├── العروض والخصومات
│       └── الأسعار الديناميكية
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   │   ├── قائمة الطلبات
│   │   ├── تفاصيل كل طلب
│   │   ├── تخصيص السائقين
│   │   └── جدولة التوصيل
│   ├── تتبع الشحنات
│   │   ├── تتبع فوري للطرود
│   │   ├── تحديثات الحالة
│   │   ├── إشعارات العملاء
│   │   └── حل المشاكل
│   ├── إدارة المستودعات
│   │   ├── مخزون المستودعات
│   │   ├── عمليات الاستلام والإرسال
│   │   ├── تحسين التخزين
│   │   └── إدارة الموظفين
│   └── تقارير التوصيل
│       ├── معدلات التسليم
│       ├── أوقات التوصيل
│       ├── رضا العملاء
│       └── تحليل الأداء
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   │   ├── تحليل سلوك العملاء
│   │   ├── أنماط الاستخدام
│   │   ├── تحليل الطلب
│   │   └── تحليل الأداء
│   ├── التنبؤات الذكية
│   │   ├── توقع الطلب
│   │   ├── تحسين الأسطول
│   │   ├── التنبؤ بالإيرادات
│   │   └── تحليل المخاطر
│   ├── تقارير مخصصة
│   │   ├── إنشاء تقارير حسب الطلب
│   │   ├── جدولة التقارير
│   │   ├── تخصيص المحتوى
│   │   └── توزيع التقارير
│   └── تصدير البيانات
│       ├── تصدير إلى Excel/CSV
│       ├── تكامل مع أنظمة خارجية
│       ├── APIs للبيانات
│       └── النسخ الاحتياطية
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    │   ├── إعدادات المنصة
    │   ├── معاملات النظام
    │   ├── إعدادات الأمان
    │   └── تخصيص الواجهة
    ├── إدارة الإشعارات
    │   ├── قوالب الرسائل
    │   ├── قنوات الإرسال
    │   ├── جدولة الإشعارات
    │   └── تتبع التسليم
    ├── النسخ الاحتياطية
    │   ├── جدولة النسخ الاحتياطية
    │   ├── استعادة البيانات
    │   ├── تشفير النسخ
    │   └── التخزين السحابي
    └── سجلات النظام
        ├── سجلات الأنشطة
        ├── سجلات الأخطاء
        ├── سجلات الأداء
        └── سجلات الأمان
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js + Socket.io
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   │   ├── خريطة تفاعلية للرحلات الجارية
│   │   ├── تفاصيل كل رحلة
│   │   ├── التدخل في حالات الطوارئ
│   │   └── إعادة توجيه الرحلات
│   ├── حالة السائقين
│   │   ├── السائقين المتصلين/غير المتصلين
│   │   ├── موقع كل سائق
│   │   ├── حالة المركبات
│   │   └── أداء السائقين
│   ├── طوارئ ومشاكل
│   │   ├── تنبيهات الطوارئ الفورية
│   │   ├── حوادث المرور
│   │   ├── أعطال المركبات
│   │   └── شكاوى العملاء العاجلة
│   └── تدخل سريع
│       ├── إرسال مساعدة فورية
│       ├── تحويل الرحلات
│       ├── التواصل مع الطوارئ
│       └── تنسيق الإنقاذ
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   │   ├── عرض جميع المركبات
│   │   ├── طبقات متعددة للبيانات
│   │   ├── تحديث فوري للمواقع
│   │   └── أدوات تحليل مكانية
│   ├── تتبع المركبات
│   │   ├── مسار كل مركبة
│   │   ├── سرعة وحالة المركبة
│   │   ├── تاريخ المواقع
│   │   └── تنبيهات الانحراف
│   ├── تحليل المناطق
│   │   ├── مناطق الطلب العالي
│   │   ├── نقاط الازدحام
│   │   ├── أوقات الذروة
│   │   └── تحسين التغطية
│   └── إدارة المسارات
│       ├── تحسين المسارات
│       ├── تجنب الازدحام
│       ├── مسارات الطوارئ
│       └── تحديث المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   │   ├── تنبيهات النظام
│   │   ├── تنبيهات الأمان
│   │   ├── تنبيهات الأداء
│   │   └── تنبيهات العملاء
│   ├── إدارة الطوارئ
│   │   ├── بروتوكولات الطوارئ
│   │   ├── فرق الاستجابة
│   │   ├── تنسيق الإنقاذ
│   │   └── تقارير الحوادث
│   ├── تواصل مع السائقين
│   │   ├── رسائل فورية
│   │   ├── مكالمات صوتية
│   │   ├── تعليمات التنقل
│   │   └── تحديثات الحالة
│   └── دعم العملاء
│       ├── استقبال الشكاوى
│       ├── حل المشاكل
│       ├── متابعة الطلبات
│       └── تحسين الخدمة
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   │   ├── معدلات إكمال الرحلات
│   │   ├── أوقات الاستجابة
│   │   ├── جودة الخدمة
│   │   └── مقارنات الأداء
│   ├── معدلات الاستجابة
│   │   ├── وقت الوصول للعملاء
│   │   ├── سرعة حل المشاكل
│   │   ├── كفاءة التوزيع
│   │   └── تحسين العمليات
│   ├── رضا العملاء
│   │   ├── تقييمات العملاء
│   │   ├── شكاوى ومقترحات
│   │   ├── معدلات الاحتفاظ
│   │   └── تحليل التغذية الراجعة
│   └── كفاءة الأسطول
│       ├── استخدام المركبات
│       ├── استهلاك الوقود
│       ├── صيانة المركبات
│       └── تحسين التشغيل
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    │   ├── إجراءات الطوارئ
    │   ├── خطط الإخلاء
    │   ├── تدريب الموظفين
    │   └── تحديث البروتوكولات
    ├── تتبع الحوادث
    │   ├── تسجيل الحوادث
    │   ├── تحليل الأسباب
    │   ├── إجراءات التحقيق
    │   └── منع تكرار الحوادث
    ├── تنسيق الإنقاذ
    │   ├── فرق الإنقاذ
    │   ├── المعدات الطبية
    │   ├── التنسيق مع السلطات
    │   └── متابعة الحالات
    └── تقارير الحوادث
        ├── تقارير مفصلة
        ├── إحصائيات الحوادث
        ├── تحليل الاتجاهات
        └── توصيات التحسين
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل المحسنة
```powershell
# سكريبت التشغيل الرئيسي المحسن
start-platform.ps1
├── المعاملات المتقدمة:
│   ├── -Mode (development/staging/production/minimal)
│   ├── -Services (comma-separated list of services)
│   ├── -SkipDatabase (تخطي تشغيل قواعد البيانات)
│   ├── -SkipMicroservices (تخطي الخدمات المصغرة)
│   ├── -SkipFrontend (تخطي الواجهات الأمامية)
│   ├── -SkipComprehensive (تخطي النظام الشامل)
│   ├── -EnableMonitoring (تفعيل المراقبة)
│   ├── -EnableLogging (تفعيل السجلات المفصلة)
│   ├── -HealthCheck (فحص صحة النظام)
│   └── -Parallel (تشغيل متوازي للخدمات)
├── الوظائف المتقدمة:
│   ├── فحص المتطلبات والتبعيات
│   ├── تشغيل قواعد البيانات بالترتيب الصحيح
│   ├── تشغيل الخدمات المصغرة مع مراقبة الحالة
│   ├── تشغيل الواجهات الأمامية مع Hot Reload
│   ├── مراقبة الحالة المستمرة
│   ├── إنشاء تقارير التشغيل
│   └── إعداد البيئة التطويرية
├── ميزات الأمان:
│   ├── التحقق من صحة التكوين
│   ├── تشفير البيانات الحساسة
│   ├── مراقبة الوصول غير المصرح
│   └── تسجيل جميع العمليات
└── التحسينات:
    ├── تحسين استخدام الذاكرة
    ├── تحسين أداء الشبكة
    ├── تحسين استخدام المعالج
    └── تحسين استخدام القرص

# سكريبت الإيقاف المحسن
stop-platform.ps1
├── المعاملات:
│   ├── -Force (إيقاف قسري)
│   ├── -Graceful (إيقاف تدريجي)
│   ├── -KeepDatabase (الاحتفاظ بقواعد البيانات)
│   ├── -KeepCache (الاحتفاظ بالتخزين المؤقت)
│   ├── -SaveState (حفظ حالة النظام)
│   └── -Backup (إنشاء نسخة احتياطية)
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب الصحيح
│   ├── تنظيف الموارد والذاكرة
│   ├── حفظ السجلات والتقارير
│   ├── إنشاء نسخ احتياطية
│   ├── تقرير الحالة النهائية
│   └── تنظيف الملفات المؤقتة

# سكريبت الاختبار الشامل
test-platform.ps1
├── المعاملات:
│   ├── -TestType (unit/integration/performance/security)
│   ├── -DatabaseOnly (اختبار قواعد البيانات فقط)
│   ├── -ServicesOnly (اختبار الخدمات فقط)
│   ├── -FullTest (اختبار شامل)
│   ├── -LoadTest (اختبار الأحمال)
│   ├── -SecurityTest (اختبار الأمان)
│   └── -GenerateReport (إنشاء تقرير مفصل)
├── الاختبارات:
│   ├── اختبار اتصال قواعد البيانات
│   ├── اختبار صحة الخدمات
│   ├── اختبار APIs والواجهات
│   ├── اختبار التكامل بين الخدمات
│   ├── اختبار
);

-- جدول ربط السائقين بالمركبات
CREATE TABLE driver_vehicle_assignments (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT REFERENCES drivers(id) ON DELETE CASCADE,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- جدول صيانة المركبات
CREATE TABLE vehicle_maintenance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL, -- ROUTINE, REPAIR, INSPECTION
    description TEXT NOT NULL,
    cost DECIMAL(10, 2),
    service_provider VARCHAR(100),
    scheduled_date DATE,
    completed_date DATE,
    next_service_date DATE,
    odometer_at_service DECIMAL(10, 2),
    status VARCHAR(20) DEFAULT 'SCHEDULED', -- SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول استهلاك الوقود
CREATE TABLE fuel_consumption (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT REFERENCES drivers(id),
    fuel_amount DECIMAL(8, 2) NOT NULL, -- liters
    cost DECIMAL(10, 2) NOT NULL,
    odometer_reading DECIMAL(10, 2),
    fuel_station VARCHAR(100),
    refuel_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### هـ) قاعدة بيانات المدفوعات (tecnodrive_payments)
```sql
-- جدول المحافظ الرقمية
CREATE TABLE wallets (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(12, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'YER', -- Yemeni Rial
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, FROZEN
    daily_limit DECIMAL(12, 2),
    monthly_limit DECIMAL(12, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    wallet_id BIGINT REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- CREDIT, DEBIT, TRANSFER
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    description TEXT,
    reference_id VARCHAR(100), -- Reference to ride, parcel, etc.
    reference_type VARCHAR(50), -- RIDE_PAYMENT, PARCEL_PAYMENT, TOP_UP, etc.
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED, CANCELLED
    payment_method VARCHAR(50), -- WALLET, CARD, BANK_TRANSFER, CASH
    external_transaction_id VARCHAR(100),
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طرق الدفع
CREATE TABLE payment_methods (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL, -- CREDIT_CARD, DEBIT_CARD, BANK_ACCOUNT, MOBILE_WALLET
    provider VARCHAR(50), -- VISA, MASTERCARD, PAYPAL, etc.
    last_four_digits VARCHAR(4),
    expiry_month INTEGER,
    expiry_year INTEGER,
    cardholder_name VARCHAR(100),
    is_default BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    token VARCHAR(255), -- Tokenized card details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- RIDE, PARCEL, SUBSCRIPTION
    service_id BIGINT NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_amount DECIMAL(12, 2) DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, CANCELLED
    due_date DATE,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عمولات السائقين
CREATE TABLE driver_earnings (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT NOT NULL,
    ride_id BIGINT,
    gross_amount DECIMAL(12, 2) NOT NULL,
    commission_rate DECIMAL(5, 4) NOT NULL, -- e.g., 0.15 for 15%
    commission_amount DECIMAL(12, 2) NOT NULL,
    net_amount DECIMAL(12, 2) NOT NULL,
    bonus_amount DECIMAL(12, 2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, WITHHELD
    payout_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

3. تتبع الطرد:
   parcel-service ↔ location-service
   │                 │
   ▼                 ▼
   customer-app ← driver-app

4. تسليم الطرد:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
├── 📍 تحديد المواقع والوجهات
├── 🚗 طلب الرحلات المختلفة
├── 💰 إدارة المحفظة والمدفوعات
├── 📊 تتبع الرحلات في الوقت الفعلي
├── ⭐ تقييم السائقين
├── 📱 الإشعارات الفورية
└── 📈 تاريخ الرحلات والفواتير

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
├── 🟢 تبديل حالة الاتصال (Online/Offline)
├── 📍 تتبع الموقع التلقائي
├── 🔔 استقبال طلبات الرحلات
├── 🗺️ التنقل والمسارات
├── 💰 تتبع الأرباح
├── ⭐ تقييم الركاب
├── 📊 إحصائيات الأداء
└── 🚗 إدارة المركبة

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   ├── مؤشرات الأداء الرئيسية
│   ├── الرسوم البيانية التفاعلية
│   └── التنبيهات والإشعارات
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   ├── التحقق من الهوية
│   ├── إدارة الأدوار والصلاحيات
│   └── سجل النشاطات
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   ├── جدولة الصيانة
│   ├── تتبع الأداء
│   └── إدارة التراخيص
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   ├── أرباح السائقين
│   ├── التقارير المالية
│   └── إعدادات الأسعار
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   ├── تتبع الشحنات
│   ├── إدارة المستودعات
│   └── تقارير التوصيل
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   ├── التنبؤات الذكية
│   ├── تقارير مخصصة
│   └── تصدير البيانات
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    ├── إدارة الإشعارات
    ├── النسخ الاحتياطية
    └── سجلات النظام
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   ├── حالة السائقين
│   ├── طوارئ ومشاكل
│   └── تدخل سريع
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   ├── تتبع المركبات
│   ├── تحليل المناطق
│   └── إدارة المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   ├── إدارة الطوارئ
│   ├── تواصل مع السائقين
│   └── دعم العملاء
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   ├── معدلات الاستجابة
│   ├── رضا العملاء
│   └── كفاءة الأسطول
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    ├── تتبع الحوادث
    ├── تنسيق الإنقاذ
    └── تقارير الحوادث
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل
```powershell
# سكريبت التشغيل الرئيسي
start-platform.ps1
├── المعاملات:
│   ├── -Mode (development/production/minimal)
│   ├── -SkipDatabase
│   ├── -SkipMicroservices
│   ├── -SkipFrontend
│   └── -SkipComprehensive
├── الوظائف:
│   ├── فحص المتطلبات
│   ├── تشغيل قواعد البيانات
│   ├── تشغيل الخدمات المصغرة
│   ├── تشغيل الواجهات الأمامية
│   └── مراقبة الحالة

# سكريبت الإيقاف
stop-platform.ps1
├── المعاملات:
│   ├── -Force
│   ├── -KeepDatabase
│   └── -KeepCache
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب
│   ├── تنظيف الموارد
│   ├── حفظ السجلات
│   └── تقرير الحالة النهائية

# سكريبت الاختبار
test-platform.ps1
├── المعاملات:
│   ├── -DatabaseOnly
│   ├── -ServicesOnly
│   └── -FullTest
├── الاختبارات:
│   ├── اتصال قواعد البيانات
│   ├── صحة الخدمات
│   ├── اختبار APIs
│   └── اختبار التكامل
```

### 2. 🛠️ أدوات التطوير
```
tools/
├── generators/
│   ├── service-generator.js      # مولد الخدمات المصغرة
│   ├── api-generator.js          # مولد APIs
│   ├── database-generator.js     # مولد مخططات قواعد البيانات
│   └── frontend-generator.js     # مولد مكونات الواجهة
├── testing/
│   ├── load-testing/            # اختبارات الحمولة
│   ├── integration-testing/     # اختبارات التكامل
│   ├── performance-testing/     # اختبارات الأداء
│   └── security-testing/        # اختبارات الأمان
├── monitoring/
│   ├── health-check.js          # فحص صحة النظام
│   ├── performance-monitor.js   # مراقبة الأداء
│   ├── log-analyzer.js          # تحليل السجلات
│   └── alert-manager.js         # إدارة التنبيهات
└── deployment/
    ├── docker-builder.js        # بناء صور Docker
    ├── k8s-deployer.js          # نشر على Kubernetes
    ├── backup-manager.js        # إدارة النسخ الاحتياطية
    └── migration-runner.js      # تشغيل ترحيل البيانات
```

---

## 🔒 الأمان والحماية

### 1. 🛡️ طبقات الأمان
```
طبقات الحماية المتعددة:
├── 🌐 طبقة الشبكة
│   ├── HTTPS/TLS 1.3
│   ├── WAF (Web Application Firewall)
│   ├── DDoS Protection
│   └── IP Whitelisting
├── 🔐 طبقة المصادقة
│   ├── JWT + Refresh Tokens
│   ├── OAuth2 / OpenID Connect
│   ├── Multi-Factor Authentication
│   └── Biometric Authentication
├── 🔑 طبقة التفويض
│   ├── Role-Based Access Control (RBAC)
│   ├── Attribute-Based Access Control (ABAC)
│   ├── API Rate Limiting
│   └── Resource-Level Permissions
├── 💾 طبقة البيانات
│   ├── Database Encryption at Rest
│   ├── Field-Level Encryption
│   ├── Data Masking
│   └── Audit Logging
└── 🔍 طبقة المراقبة
    ├── Security Information and Event Management (SIEM)
    ├── Intrusion Detection System (IDS)
    ├── Vulnerability Scanning
    └── Penetration Testing
```

### 2. 🔐 إدارة المفاتيح والأسرار
```
إدارة الأسرار:
├── HashiCorp Vault
│   ├── تشفير المفاتيح
│   ├── دوران المفاتيح التلقائي
│   ├── إدارة الشهادات
│   └── سياسات الوصول
├── Kubernetes Secrets
│   ├── أسرار قواعد البيانات
│   ├── مفاتيح APIs الخارجية
│   ├── شهادات TLS
│   └── متغيرات البيئة الحساسة
└── Environment-Specific Configs
    ├── Development Environment
    ├── Staging Environment
    ├── Production Environment
    └── Disaster Recovery Environment
```

---

## 📈 المراقبة والتحليلات

### 1. 📊 نظام المراقبة الشامل
```
مكونات المراقبة:
├── 📈 Prometheus + Grafana
│   ├── مقاييس الأداء
│   ├── استخدام الموارد
│   ├── معدلات الاستجابة
│   └── لوحات مراقبة تفاعلية
├── 📋 ELK Stack (Elasticsearch + Logstash + Kibana)
│   ├── جمع السجلات المركزي
│   ├── تحليل السجلات
│   ├── البحث في السجلات
│   └── تصور البيانات
├── 🔍 Jaeger (Distributed Tracing)
│   ├── تتبع الطلبات عبر الخدمات
│   ├── تحليل زمن الاستجابة
│   ├── اكتشاف الاختناقات
│   └── تحليل الأخطاء
└── 🚨 AlertManager
    ├── تنبيهات الأداء
    ├── تنبيهات الأمان
    ├── تنبيهات الأخطاء
    └── تصعيد التنبيهات
```

### 2. 🤖 الذكاء الاصطناعي والتحليلات
```
وحدات الذكاء الاصطناعي:
├── 🧠 Machine Learning Models
│   ├── تنبؤ الطلب
│   ├── تحسين المسارات
│   ├── تحليل السلوك
│   └── كشف الاحتيال
├── 📊 Real-time Analytics
│   ├── تحليل البيانات الفورية
│   ├── مؤشرات الأداء المباشرة
│   ├── تحليل الاتجاهات
│   └── التنبؤات قصيرة المدى
├── 🔍 Business Intelligence
│   ├── تقارير تنفيذية
│   ├── تحليل الربحية
│   ├── تحليل العملاء
│   └── تحليل السوق
└── 🎯 Recommendation Engine
    ├── توصيات للركاب
    ├── تحسين توزيع السائقين
    ├── تحسين الأسعار
    └── تحسين الخدمات
```

---

## 🚀 خطة التطوير والتوسع

### 1. 📅 خارطة الطريق (Roadmap)
```
المراحل القادمة:
├── 🎯 المرحلة 1 (Q1 2024): الإطلاق المحلي
│   ├── إطلاق في صنعاء وعدن
│   ├── 100 سائق و 1000 مستخدم
│   ├── خدمات الرحلات الأساسية
│   └── دعم اللغة العربية الكامل
├── 🎯 المرحلة 2 (Q2 2024): التوسع الوطني
│   ├── تغطية جميع المحافظات اليمنية
│   ├── 500 سائق و 10,000 مستخدم
│   ├── خدمات توصيل الطرود
│   └── تكامل مع البنوك المحلية
├── 🎯 المرحلة 3 (Q3 2024): الميزات المتقدمة
│   ├── الذكاء الاصطناعي والتحليلات
│   ├── خدمات الأسطول للشركات
│   ├── تطبيق الويب الكامل
│   └── APIs للمطورين الخارجيين
└── 🎯 المرحلة 4 (Q4 2024): التوسع الإقليمي
    ├── دول الخليج العربي
    ├── 5,000 سائق و 100,000 مستخدم
    ├── خدمات متعددة العملات
    └── شراكات استراتيجية
```

### 2. 🔧 التحسينات التقنية المستقبلية
```
التطويرات المخططة:
├── 🌐 تقنيات الجيل القادم
│   ├── 5G Integration
│   ├── Edge Computing
│   ├── Blockchain للشفافية
│   └── IoT للمركبات الذكية
├── 🤖 الذكاء الاصطناعي المتقدم
│   ├── Computer Vision للأمان
│   ├── Natural Language Processing
│   ├── Predictive Maintenance
│   └── Autonomous Vehicle Support
├── 🔒 أمان محسن
│   ├── Zero Trust Architecture
│   ├── Quantum-Safe Cryptography
│   ├── Advanced Threat Detection
│   └── Privacy-Preserving Analytics
└── 🚀 أداء محسن
    ├── Serverless Architecture
    ├── GraphQL APIs
    ├── Advanced Caching
    └── Global CDN
```

---

## 📋 الخلاصة والتوصيات

### ✅ نقاط القوة الحالية
1. **معمارية قابلة للتوسع**: تصميم microservices متقدم
2. **تغطية شاملة**: جميع جوانب النقل والتوصيل
3. **تقنيات حديثة**: استخدام أحدث التقنيات والأدوات
4. **أمان متقدم**: طبقات حماية متعددة
5. **مراقبة شاملة**: نظام مراقبة متكامل

### 🔧 التحسينات المقترحة
1. **تحسين الأداء**: تحسين استعلامات قواعد البيانات
2. **تطوير الاختبارات**: زيادة تغطية الاختبارات التلقائية
3. **تحسين التوثيق**: توثيق أكثر تفصيلاً للمطورين
4. **تحسين UX**: تحسين تجربة المستخدم في التطبيقات
5. **تحسين DevOps**: أتمتة أكثر لعمليات النشر

### 🎯 الأولويات القادمة
1. **إكمال الاختبارات**: اختبارات شاملة لجميع المكونات
2. **تحسين الأمان**: مراجعة أمنية شاملة
3. **تحسين الأداء**: تحسين أداء النظام تحت الأحمال العالية
4. **التوثيق**: إكمال التوثيق الفني والمستخدم
5. **التدريب**: تدريب الفريق على النظام الجديد

---

*تم إعداد هذا التحليل بناءً على فحص شامل لملفات المشروع في `D:\tecno-drive-platform`*
*آخر تحديث: $(date)*

# 📊 التحليل الشامل والتفصيلي لمنصة TecnoDrive

## 🎯 نظرة عامة على المنصة

**منصة TecnoDrive** هي نظام شامل ومتطور لإدارة النقل والتوصيل يعتمد على معمارية الخدمات المصغرة (Microservices Architecture) مع دعم متعدد المستأجرين (Multi-tenant SaaS Platform).

### 📍 الموقع والمسار
- **المسار الرئيسي**: `D:\tecno-drive-platform`
- **نوع المشروع**: منصة SaaS متكاملة
- **المعمارية**: Microservices + Event-Driven Architecture
- **قواعد البيانات**: PostgreSQL + Redis + MongoDB
- **التقنيات**: Java Spring Boot + Python FastAPI + React + Angular

### 🔢 إحصائيات المشروع
- **عدد الخدمات المصغرة**: 13 خدمة
- **عدد قواعد البيانات**: 13 قاعدة بيانات
- **عدد التطبيقات الأمامية**: 4 تطبيقات
- **عدد ملفات الكود**: 500+ ملف
- **حجم المشروع**: ~2.5 GB

---

## 🏗️ الهيكل التنظيمي للمشروع

### 📁 المجلدات الرئيسية (التسلسل الهرمي)

```
tecno-drive-platform/
├── 📁 backend/                     # الخدمات الخلفية (Backend Services)
│   ├── 📁 microservices/          # الخدمات المصغرة Java Spring Boot
│   │   ├── 📁 core/               # الخدمات الأساسية (3 خدمات)
│   │   │   ├── user-service/      # خدمة إدارة المستخدمين
│   │   │   ├── auth-service/      # خدمة المصادقة والتفويض
│   │   │   └── payment-service/   # خدمة المدفوعات والمحافظ
│   │   ├── 📁 business/           # خدمات الأعمال (6 خدمات)
│   │   │   ├── ride-service/      # خدمة إدارة الرحلات
│   │   │   ├── fleet-service/     # خدمة إدارة الأسطول
│   │   │   ├── parcel-service/    # خدمة الطرود والتوصيل
│   │   │   ├── location-service/  # خدمة المواقع والخرائط
│   │   │   ├── analytics-service/ # خدمة التحليلات والذكاء الاصطناعي
│   │   │   └── notification-service/ # خدمة الإشعارات
│   │   └── 📁 infrastructure/     # خدمات البنية التحتية (4 خدمات)
│   │       ├── api-gateway/       # بوابة API الموحدة
│   │       ├── eureka-server/     # خادم اكتشاف الخدمات
│   │       ├── config-server/     # خادم التكوين المركزي
│   │       └── monitoring-service/ # خدمة المراقبة والصحة
│   ├── 📁 shared/                 # المكتبات والمكونات المشتركة
│   ├── 📁 comprehensive-system/   # النظام الشامل Python FastAPI
│   └── 📁 api-docs/              # وثائق API التفاعلية
│
├── 📁 frontend/                    # الواجهات الأمامية
│   ├── 📁 admin-dashboard/        # لوحة تحكم الإدارة (React)
│   ├── 📁 user-apps/             # تطبيقات المستخدمين
│   │   ├── 📁 driver-app/        # تطبيق السائقين (React Native)
│   │   └── 📁 passenger-app/     # تطبيق الركاب (React Native)
│   ├── 📁 operator-dashboard/     # لوحة تحكم المشغلين (Angular)
│   └── 📁 shared-components/      # المكونات المشتركة
│
├── 📁 database/                   # قواعد البيانات والمخططات
│   ├── 📁 schemas/               # مخططات قواعد البيانات
│   ├── 📁 migrations/            # ملفات الترحيل
│   ├── 📁 seeds/                 # البيانات الأولية
│   └── 📁 backups/               # النسخ الاحتياطية
│
├── 📁 infrastructure/             # البنية التحتية والنشر
│   ├── 📁 docker/                # ملفات Docker
│   ├── 📁 kubernetes/            # ملفات Kubernetes
│   ├── 📁 terraform/             # Infrastructure as Code
│   └── 📁 monitoring/            # أدوات المراقبة
│
├── 📁 tools/                      # الأدوات المساعدة
│   ├── 📁 scripts/               # سكريبتات التشغيل والإدارة
│   ├── 📁 generators/            # مولدات الكود
│   └── 📁 testing/               # أدوات الاختبار
│
└── 📁 docs/                       # التوثيق الشامل
    ├── 📁 api/                   # توثيق APIs
    ├── 📁 architecture/          # الهندسة المعمارية
    ├── 📁 deployment/            # أدلة النشر
    └── 📁 development/           # أدلة التطوير
```

---

## 🔧 المعمارية التقنية المفصلة

### 1. 🏛️ معمارية الخدمات المصغرة (Microservices Architecture)

#### أ) الخدمات الأساسية (Core Services)
```
🔒 auth-service (Port: 8081)
├── المسؤوليات:
│   ├── المصادقة والتفويض (JWT + OAuth2)
│   ├── إدارة الجلسات والرموز المميزة
│   ├── التحكم في الوصول القائم على الأدوار (RBAC)
│   └── تكامل مع مقدمي الهوية الخارجيين
├── قاعدة البيانات: tecnodrive_auth
├── التقنيات: Spring Security + JWT + Redis
└── APIs: /auth/login, /auth/register, /auth/validate

👤 user-service (Port: 8083)
├── المسؤوليات:
│   ├── إدارة ملفات المستخدمين الشخصية
│   ├── التحقق من الهوية والوثائق
│   ├── إدارة التفضيلات والإعدادات
│   └── تتبع نشاط المستخدمين
├── قاعدة البيانات: tecnodrive_users
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /users/profile, /users/documents, /users/preferences

💰 payment-service (Port: 8085)
├── المسؤوليات:
│   ├── معالجة المدفوعات والمحافظ الرقمية
│   ├── إدارة طرق الدفع المتعددة
│   ├── تتبع المعاملات المالية
│   └── تكامل مع بوابات الدفع المحلية
├── قاعدة البيانات: tecnodrive_payments
├── التقنيات: Spring Boot + Stripe API + PayPal
└── APIs: /payments/process, /wallet/balance, /transactions/history
```

#### ب) خدمات الأعمال (Business Services)
```
🚗 ride-service (Port: 8082)
├── المسؤوليات:
│   ├── إدارة طلبات الرحلات والحجوزات
│   ├── مطابقة السائقين مع الركاب
│   ├── تتبع الرحلات في الوقت الفعلي
│   └── حساب التكاليف والمسافات
├── قاعدة البيانات: tecnodrive_rides
├── التقنيات: Spring Boot + WebSocket + Redis
└── APIs: /rides/request, /rides/track, /rides/complete

🚛 fleet-service (Port: 8084)
├── المسؤوليات:
│   ├── إدارة أسطول المركبات
│   ├── جدولة الصيانة والفحوصات
│   ├── تتبع استهلاك الوقود والأداء
│   └── إدارة تراخيص السائقين
├── قاعدة البيانات: tecnodrive_fleet
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /fleet/vehicles, /fleet/maintenance, /fleet/drivers

📦 parcel-service (Port: 8086)
├── المسؤوليات:
│   ├── إدارة طلبات توصيل الطرود
│   ├── تتبع الطرود عبر المراحل المختلفة
│   ├── إدارة المستودعات ونقاط التوزيع
│   └── حساب تكاليف الشحن
├── قاعدة البيانات: tecnodrive_parcels
├── التقنيات: Spring Boot + JPA + MongoDB
└── APIs: /parcels/create, /parcels/track, /parcels/deliver

📍 location-service (Port: 8087)
├── المسؤوليات:
│   ├── إدارة المواقع الجغرافية والخرائط
│   ├── حساب المسارات المثلى
│   ├── تتبع المواقع في الوقت الفعلي
│   └── تكامل مع خدمات الخرائط الخارجية
├── قاعدة البيانات: tecnodrive_locations
├── التقنيات: Spring Boot + PostGIS + Google Maps API
└── APIs: /locations/geocode, /routes/optimize, /tracking/live

📊 analytics-service (Port: 8088)
├── المسؤوليات:
│   ├── تحليل البيانات والذكاء الاصطناعي
│   ├── إنشاء التقارير والإحصائيات
│   ├── التنبؤ بالطلب والأنماط
│   └── مراقبة الأداء والمؤشرات
├── قاعدة البيانات: tecnodrive_analytics
├── التقنيات: Spring Boot + Apache Spark + TensorFlow
└── APIs: /analytics/reports, /analytics/predictions, /analytics/kpis

🔔 notification-service (Port: 8089)
├── المسؤوليات:
│   ├── إرسال الإشعارات المتعددة القنوات
│   ├── إدارة قوالب الرسائل
│   ├── جدولة الإشعارات المؤجلة
│   └── تتبع معدلات التسليم والقراءة
├── قاعدة البيانات: tecnodrive_notifications
├── التقنيات: Spring Boot + Firebase + Twilio + SMTP
└── APIs: /notifications/send, /notifications/templates, /notifications/status
```

#### ج) خدمات البنية التحتية (Infrastructure Services)
```
🌐 api-gateway (Port: 8080)
├── المسؤوليات:
│   ├── توجيه الطلبات للخدمات المناسبة
│   ├── المصادقة والتفويض المركزي
│   ├── تحديد معدل الطلبات (Rate Limiting)
│   └── مراقبة وتسجيل الطلبات
├── التقنيات: Spring Cloud Gateway + Eureka Client
└── التكوين: Load Balancing + Circuit Breaker

🔍 eureka-server (Port: 8761)
├── المسؤوليات:
│   ├── اكتشاف وتسجيل الخدمات
│   ├── مراقبة صحة الخدمات
│   ├── توزيع الأحمال التلقائي
│   └── إدارة دورة حياة الخدمات
├── التقنيات: Spring Cloud Netflix Eureka
└── واجهة الإدارة: http://localhost:8761

⚙️ config-server (Port: 8888)
├── المسؤوليات:
│   ├── إدارة التكوين المركزي
│   ├── تحديث التكوين بدون إعادة تشغيل
│   ├── إدارة البيئات المختلفة
│   └── تشفير البيانات الحساسة
├── التقنيات: Spring Cloud Config
└── مصدر التكوين: Git Repository

📈 monitoring-service (Port: 9090)
├── المسؤوليات:
│   ├── مراقبة أداء النظام والخدمات
│   ├── جمع المقاييس والسجلات
│   ├── إنشاء التنبيهات التلقائية
│   └── لوحات مراقبة تفاعلية
├── التقنيات: Prometheus + Grafana + ELK Stack
└── واجهة المراقبة: http://localhost:9090
```

### 2. 🗺️ نظام الخرائط التفاعلية المتقدم

#### أ) مقدمو الخرائط المدعومون
```
خدمات الخرائط المتاحة:
├── 🌍 OpenStreetMap (افتراضي)
│   ├── خادم البلاط: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
│   ├── خدمة Nominatim للبحث الجغرافي
│   ├── خدمة OSRM لحساب المسارات
│   └── مجاني ومفتوح المصدر
├── 🗺️ Google Maps
│   ├── يتطلب مفتاح API: GOOGLE_MAPS_API_KEY
│   ├── دقة عالية في البيانات
│   ├── دعم متقدم للمسارات
│   └── تكلفة حسب الاستخدام
├── 🎨 Mapbox
│   ├── يتطلب رمز الوصول: MAPBOX_ACCESS_TOKEN
│   ├── تخصيص متقدم للخرائط
│   ├── أداء عالي
│   └── خطط مرنة للتسعير
└── 🛰️ خرائط الأقمار الصناعية
    ├── صور عالية الدقة
    ├── تحديثات دورية
    └── مناسبة للمناطق النائية
```

#### ب) ميزات الخرائط التفاعلية
```
الطبقات المتاحة:
├── 🚗 طبقة المركبات
│   ├── مواقع المركبات الحالية
│   ├── حالة المركبات (نشط، مشغول، متوقف)
│   ├── معلومات السائق والرحلة
│   └── تحديث فوري كل 5 ثوانٍ
├── 🚦 طبقة حركة المرور
│   ├── مستويات الازدحام
│   ├── السرعة المتوسطة
│   ├── الحوادث والعوائق
│   └── توقعات حركة المرور
├── 📦 طبقة الطرود
│   ├── مواقع الطرود الحالية
│   ├── حالة التسليم
│   ├── مسارات التوصيل
│   └── نقاط التجميع والتوزيع
├── 🔥 خرائط الطلب الحرارية
│   ├── مناطق الطلب العالي
│   ├── تحليل الطلب حسب الوقت
│   ├── توقعات الطلب المستقبلي
│   └── تحسين توزيع الأسطول
└── 🏢 طبقة نقاط الاهتمام
    ├── المطارات والمحطات
    ├── المستشفيات والمراكز الطبية
    ├── المراكز التجارية
    └── المعالم السياحية
```

---

## 🗄️ قواعد البيانات والمخططات التفصيلية

### 1. 📊 نظرة عامة على قواعد البيانات

```
قواعد البيانات الرئيسية (13 قاعدة بيانات):
├── PostgreSQL Databases (11 قاعدة)
│   ├── tecnodrive_auth          # المصادقة والتفويض
│   ├── tecnodrive_users         # بيانات المستخدمين
│   ├── tecnodrive_rides         # إدارة الرحلات
│   ├── tecnodrive_fleet         # إدارة الأسطول
│   ├── tecnodrive_payments      # المدفوعات والمحافظ
│   ├── tecnodrive_parcels       # إدارة الطرود
│   ├── tecnodrive_locations     # المواقع والخرائط
│   ├── tecnodrive_analytics     # التحليلات والتقارير
│   ├── tecnodrive_notifications # الإشعارات
│   ├── tecnodrive_config        # التكوين المركزي
│   └── tecnodrive_monitoring    # المراقبة والسجلات
├── Redis Cache (1 قاعدة)
│   └── tecnodrive_cache         # التخزين المؤقت
└── MongoDB (1 قاعدة)
    └── tecnodrive_documents     # الوثائق والملفات
```

### 2. 🔗 مخططات قواعد البيانات التفصيلية

#### أ) قاعدة بيانات المصادقة (tecnodrive_auth)
```sql
-- جدول المستخدمين للمصادقة
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    role_id BIGINT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- جدول الجلسات النشطة
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255),
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجل المصادقة
CREATE TABLE auth_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- LOGIN, LOGOUT, FAILED_LOGIN, etc.
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ب) قاعدة بيانات المواقع والخرائط (tecnodrive_locations)
```sql
-- جدول المواقع الجغرافية
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255),
    address TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_type VARCHAR(50), -- PICKUP, DROPOFF, WAREHOUSE, etc.
    city VARCHAR(100),
    district VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Yemen',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهرس مكاني للبحث السريع
CREATE INDEX idx_locations_coordinates ON locations USING GIST (
    ST_Point(longitude, latitude)
);

-- جدول تتبع المواقع في الوقت الفعلي
CREATE TABLE location_tracking (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL, -- VEHICLE, DRIVER, PARCEL
    entity_id BIGINT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2), -- km/h
    heading INTEGER, -- degrees (0-360)
    accuracy DECIMAL(5, 2), -- meters
    battery_level INTEGER, -- percentage
    signal_strength INTEGER, -- dBm
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للبحث السريع حسب النوع والمعرف
CREATE INDEX idx_tracking_entity ON location_tracking (entity_type, entity_id);
CREATE INDEX idx_tracking_timestamp ON location_tracking (timestamp);

-- جدول المسارات المحسوبة
CREATE TABLE calculated_routes (
    id BIGSERIAL PRIMARY KEY,
    route_name VARCHAR(255),
    start_location JSONB NOT NULL,
    end_location JSONB NOT NULL,
    waypoints JSONB, -- array of coordinates
    route_geometry JSONB, -- GeoJSON LineString
    total_distance DECIMAL(10, 2), -- kilometers
    estimated_duration INTEGER, -- minutes
    optimization_type VARCHAR(50), -- SHORTEST, FASTEST, FUEL_EFFICIENT
    traffic_considered BOOLEAN DEFAULT false,
    created_by BIGINT,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المناطق الجغرافية (Geofences)
CREATE TABLE geofences (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fence_type VARCHAR(20) NOT NULL, -- CIRCLE, POLYGON
    geometry JSONB NOT NULL, -- GeoJSON geometry
    radius DECIMAL(8, 2), -- meters (for circle type)
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ج) قاعدة بيانات الطرود المحسنة (tecnodrive_parcels)
```sql
-- جدول الطرود الرئيسي
CREATE TABLE parcels (
    id BIGSERIAL PRIMARY KEY,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    parcel_type VARCHAR(50) NOT NULL, -- DOCUMENT, PACKAGE, FRAGILE, etc.
    weight DECIMAL(8, 3), -- kg
    dimensions JSONB, -- {length, width, height} in cm
    declared_value DECIMAL(12, 2),
    pickup_location JSONB NOT NULL,
    delivery_location JSONB NOT NULL,
    pickup_instructions TEXT,
    delivery_instructions TEXT,
    status VARCHAR(30) DEFAULT 'CREATED', -- CREATED, PICKED_UP, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED, CANCELLED
    priority_level VARCHAR(20) DEFAULT 'STANDARD', -- EXPRESS, STANDARD, ECONOMY
    payment_method VARCHAR(30),
    shipping_cost DECIMAL(10, 2),
    insurance_cost DECIMAL(10, 2) DEFAULT 0,
    cod_amount DECIMAL(12, 2) DEFAULT 0, -- Cash on Delivery
    scheduled_pickup_time TIMESTAMP,
    actual_pickup_time TIMESTAMP,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    assigned_driver_id BIGINT,
    assigned_vehicle_id BIGINT,
    current_warehouse_id BIGINT,
    delivery_proof JSONB, -- photos, signatures, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تتبع حالة الطرود
CREATE TABLE parcel_status_history (
    id BIGSERIAL PRIMARY KEY,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    status VARCHAR(30) NOT NULL,
    location JSONB,
    notes TEXT,
    updated_by BIGINT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستودعات
CREATE TABLE warehouses (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    location JSONB NOT NULL, -- {lat, lng}
    capacity INTEGER, -- number of parcels
    current_load INTEGER DEFAULT 0,
    operating_hours JSONB, -- {open_time, close_time} for each day
    contact_info JSONB, -- {phone, email, manager}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون المستودعات
CREATE TABLE warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    warehouse_id BIGINT REFERENCES warehouses(id) ON DELETE CASCADE,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    shelf_location VARCHAR(50),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatched_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'STORED' -- STORED, DISPATCHED
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
├── location-service ↔ All Services
│   └── تتبع المواقع لجميع الكيانات
├── parcel-service ↔ fleet-service
│   └── parcels.assigned_driver_id → drivers.id
│   └── parcels.assigned_vehicle_id → vehicles.id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود المحسنة

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. معالجة الطلب:
   parcel-service → warehouse-management
   │                 │
   ▼                 ▼
   inventory-system → route-optimization
   │                 │
   ▼                 ▼
   fleet-service

3. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

4. الالتقاط والنقل:
   driver-app → parcel-service
   │                 │
   ▼                 ▼
   location-service → warehouse-system
   │                 │
   ▼                 ▼
   tracking-updates

5. التسليم النهائي:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
   │                 │
   ▼                 ▼
   customer-feedback
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
│   ├── تسجيل دخول بالهاتف/البريد الإلكتروني
│   ├── التحقق من الهوية بالرسائل النصية
│   ├── تسجيل الدخول بالبصمة/الوجه
│   └── تسجيل الدخول بوسائل التواصل الاجتماعي
├── 📍 تحديد المواقع والوجهات
│   ├── تحديد الموقع الحالي تلقائ
│   ├── البحث الذكي للعناوين
│   ├── حفظ العناوين المفضلة
│   └── اقتراح الوجهات الشائعة
├── 🚗 طلب الرحلات المختلفة
│   ├── رحلات فردية (اقتصادية، مريحة، فاخرة)
│   ├── رحلات مشتركة لتوفير التكلفة
│   ├── رحلات مجدولة مسبق
│   └── رحلات طويلة المدى
├── 💰 إدارة المحفظة والمدفوعات
│   ├── عرض رصيد المحفظة الرقمية
│   ├── إضافة أموال للمحفظة
│   ├── ربط بطاقات الائتمان/الخصم
│   └── تاريخ المعاملات المالية
├── 📊 تتبع الرحلات في الوقت الفعلي
│   ├── موقع السائق على الخريطة
│   ├── الوقت المتوقع للوصول
│   ├── معلومات السائق والمركبة
│   └── إمكانية التواصل مع السائق
├── ⭐ تقييم السائقين
│   ├── تقييم من 1-5 نجوم
│   ├── كتابة تعليقات
│   ├── الإبلاغ عن مشاكل
│   └── إضافة السائقين للمفضلة
├── 📱 الإشعارات الفورية
│   ├── تأكيد قبول الرحلة
│   ├── تحديثات حالة الرحلة
│   ├── العروض والخصومات
│   └── تذكيرات الرحلات المجدولة
└── 📈 تاريخ الرحلات والفواتير
    ├── سجل جميع الرحلات السابقة
    ├── تفاصيل التكلفة لكل رحلة
    ├── تحميل الفواتير
    └── إحصائيات الاستخدام الشهرية

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
│   ├── مصادقة قوية للسائقين
│   ├── التحقق من صحة الرخصة
│   ├── فحص الخلفية الجنائية
│   └── تحديث الوثائق دور
├── 🟢 تبديل حالة الاتصال (Online/Offline)
│   ├── تشغيل/إيقاف استقبال الطلبات
│   ├── تحديد ساعات العمل
│   ├── وضع الاستراحة
│   └── حالة الطوارئ
├── 📍 تتبع الموقع التلقائي
│   ├── GPS عالي الدقة
│   ├── تحديث الموقع كل 5 ثوانٍ
│   ├── توفير البطارية الذكي
│   └── العمل في المناطق ضعيفة الإشارة
├── 🔔 استقبال طلبات الرحلات
│   ├── تنبيهات صوتية ومرئية
│   ├── معلومات الراكب والوجهة
│   ├── تقدير الأرباح
│   └── خيار القبول/الرفض
├── 🗺️ التنقل والمسارات
│   ├── تكامل مع خرائط Google/OpenStreetMap
│   ├── مسارات محسنة لتجنب الازدحام
│   ├── إرشادات صوتية
│   └── تحديثات حركة المرور الفورية
├── 💰 تتبع الأرباح
│   ├── الأرباح اليومية والأسبوعية
│   ├── تفاصيل كل رحلة
│   ├── العمولات والخصومات
│   └── تقارير الضرائب
├── ⭐ تقييم الركاب
│   ├── تقييم تجربة الرحلة
│   ├── الإبلاغ عن مشاكل
│   ├── حظر ركاب مشكلين
│   └── تفضيل ركاب معينين
├── 📊 إحصائيات الأداء
│   ├── معدل القبول
│   ├── التقييم العام
│   ├── عدد الرحلات المكتملة
│   └── مقارنة مع السائقين الآخرين
└── 🚗 إدارة المركبة
    ├── معلومات المركبة
    ├── جدولة الصيانة
    ├── تتبع استهلاك الوقود
    └── تقارير الأعطال

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js + D3.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   │   ├── عدد الرحلات النشطة
│   │   ├── عدد السائقين المتصلين
│   │   ├── عدد الطرود قيد التوصيل
│   │   └── الإيرادات اليومية
│   ├── مؤشرات الأداء الرئيسية (KPIs)
│   │   ├── معدل إكمال الرحلات
│   │   ├── متوسط وقت الاستجابة
│   │   ├── رضا العملاء
│   │   └── كفاءة الأسطول
│   ├── الرسوم البيانية التفاعلية
│   │   ├── اتجاهات الطلب
│   │   ├── توزيع الرحلات جغراف
│   │   ├── أداء السائقين
│   │   └── تحليل الإيرادات
│   └── التنبيهات والإشعارات
│       ├── تنبيهات النظام
│       ├── مشاكل تقنية
│       ├── شكاوى العملاء
│       └── تحديثات مهمة
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   │   ├── قائمة شاملة بالمستخدمين
│   │   ├── تفاصيل الملفات الشخصية
│   │   ├── إحصائيات الاستخدام
│   │   └── إدارة الحسابات
│   ├── التحقق من الهوية
│   │   ├── مراجعة الوثائق المرفوعة
│   │   ├── التحقق من صحة البيانات
│   │   ├── الموافقة/الرفض
│   │   └── طلب وثائق إضافية
│   ├── إدارة الأدوار والصلاحيات
│   │   ├── تعريف الأدوار
│   │   ├── تخصيص الصلاحيات
│   │   ├── إدارة الوصول
│   │   └── مراجعة الأنشطة
│   └── سجل النشاطات
│       ├── تسجيل الدخول/الخروج
│       ├── العمليات المنجزة
│       ├── التغييرات على البيانات
│       └── الأنشطة المشبوهة
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   │   ├── قائمة المركبات
│   │   ├── حالة كل مركبة
│   │   ├── تخصيص السائقين
│   │   └── تتبع الأداء
│   ├── جدولة الصيانة
│   │   ├── جدولة الصيانة الدورية
│   │   ├── تتبع تكاليف الصيانة
│   │   ├── تذكيرات الصيانة
│   │   └── تقارير الأعطال
│   ├── تتبع الأداء
│   │   ├── استهلاك الوقود
│   │   ├── المسافات المقطوعة
│   │   ├── ساعات التشغيل
│   │   └── معدلات الأعطال
│   └── إدارة التراخيص
│       ├── تراخيص السائقين
│       ├── تراخيص المركبات
│       ├── التأمين
│       └── تجديد الوثائق
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   │   ├── معالجة المدفوعات
│   │   ├── إنشاء الفواتير
│   │   ├── تتبع المستحقات
│   │   └── إدارة المبالغ المستردة
│   ├── أرباح السائقين
│   │   ├── حساب العمولات
│   │   ├── جدولة المدفوعات
│   │   ├── تقارير الأرباح
│   │   └── إدارة الحوافز
│   ├── التقارير المالية
│   │   ├── تقارير الإيرادات
│   │   ├── تحليل التكاليف
│   │   ├── الربحية
│   │   └── التنبؤات المالية
│   └── إعدادات الأسعار
│       ├── تحديد أسعار الرحلات
│       ├── أسعار الطرود
│       ├── العروض والخصومات
│       └── الأسعار الديناميكية
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   │   ├── قائمة الطلبات
│   │   ├── تفاصيل كل طلب
│   │   ├── تخصيص السائقين
│   │   └── جدولة التوصيل
│   ├── تتبع الشحنات
│   │   ├── تتبع فوري للطرود
│   │   ├── تحديثات الحالة
│   │   ├── إشعارات العملاء
│   │   └── حل المشاكل
│   ├── إدارة المستودعات
│   │   ├── مخزون المستودعات
│   │   ├── عمليات الاستلام والإرسال
│   │   ├── تحسين التخزين
│   │   └── إدارة الموظفين
│   └── تقارير التوصيل
│       ├── معدلات التسليم
│       ├── أوقات التوصيل
│       ├── رضا العملاء
│       └── تحليل الأداء
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   │   ├── تحليل سلوك العملاء
│   │   ├── أنماط الاستخدام
│   │   ├── تحليل الطلب
│   │   └── تحليل الأداء
│   ├── التنبؤات الذكية
│   │   ├── توقع الطلب
│   │   ├── تحسين الأسطول
│   │   ├── التنبؤ بالإيرادات
│   │   └── تحليل المخاطر
│   ├── تقارير مخصصة
│   │   ├── إنشاء تقارير حسب الطلب
│   │   ├── جدولة التقارير
│   │   ├── تخصيص المحتوى
│   │   └── توزيع التقارير
│   └── تصدير البيانات
│       ├── تصدير إلى Excel/CSV
│       ├── تكامل مع أنظمة خارجية
│       ├── APIs للبيانات
│       └── النسخ الاحتياطية
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    │   ├── إعدادات المنصة
    │   ├── معاملات النظام
    │   ├── إعدادات الأمان
    │   └── تخصيص الواجهة
    ├── إدارة الإشعارات
    │   ├── قوالب الرسائل
    │   ├── قنوات الإرسال
    │   ├── جدولة الإشعارات
    │   └── تتبع التسليم
    ├── النسخ الاحتياطية
    │   ├── جدولة النسخ الاحتياطية
    │   ├── استعادة البيانات
    │   ├── تشفير النسخ
    │   └── التخزين السحابي
    └── سجلات النظام
        ├── سجلات الأنشطة
        ├── سجلات الأخطاء
        ├── سجلات الأداء
        └── سجلات الأمان
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js + Socket.io
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   │   ├── خريطة تفاعلية للرحلات الجارية
│   │   ├── تفاصيل كل رحلة
│   │   ├── التدخل في حالات الطوارئ
│   │   └── إعادة توجيه الرحلات
│   ├── حالة السائقين
│   │   ├── السائقين المتصلين/غير المتصلين
│   │   ├── موقع كل سائق
│   │   ├── حالة المركبات
│   │   └── أداء السائقين
│   ├── طوارئ ومشاكل
│   │   ├── تنبيهات الطوارئ الفورية
│   │   ├── حوادث المرور
│   │   ├── أعطال المركبات
│   │   └── شكاوى العملاء العاجلة
│   └── تدخل سريع
│       ├── إرسال مساعدة فورية
│       ├── تحويل الرحلات
│       ├── التواصل مع الطوارئ
│       └── تنسيق الإنقاذ
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   │   ├── عرض جميع المركبات
│   │   ├── طبقات متعددة للبيانات
│   │   ├── تحديث فوري للمواقع
│   │   └── أدوات تحليل مكانية
│   ├── تتبع المركبات
│   │   ├── مسار كل مركبة
│   │   ├── سرعة وحالة المركبة
│   │   ├── تاريخ المواقع
│   │   └── تنبيهات الانحراف
│   ├── تحليل المناطق
│   │   ├── مناطق الطلب العالي
│   │   ├── نقاط الازدحام
│   │   ├── أوقات الذروة
│   │   └── تحسين التغطية
│   └── إدارة المسارات
│       ├── تحسين المسارات
│       ├── تجنب الازدحام
│       ├── مسارات الطوارئ
│       └── تحديث المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   │   ├── تنبيهات النظام
│   │   ├── تنبيهات الأمان
│   │   ├── تنبيهات الأداء
│   │   └── تنبيهات العملاء
│   ├── إدارة الطوارئ
│   │   ├── بروتوكولات الطوارئ
│   │   ├── فرق الاستجابة
│   │   ├── تنسيق الإنقاذ
│   │   └── تقارير الحوادث
│   ├── تواصل مع السائقين
│   │   ├── رسائل فورية
│   │   ├── مكالمات صوتية
│   │   ├── تعليمات التنقل
│   │   └── تحديثات الحالة
│   └── دعم العملاء
│       ├── استقبال الشكاوى
│       ├── حل المشاكل
│       ├── متابعة الطلبات
│       └── تحسين الخدمة
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   │   ├── معدلات إكمال الرحلات
│   │   ├── أوقات الاستجابة
│   │   ├── جودة الخدمة
│   │   └── مقارنات الأداء
│   ├── معدلات الاستجابة
│   │   ├── وقت الوصول للعملاء
│   │   ├── سرعة حل المشاكل
│   │   ├── كفاءة التوزيع
│   │   └── تحسين العمليات
│   ├── رضا العملاء
│   │   ├── تقييمات العملاء
│   │   ├── شكاوى ومقترحات
│   │   ├── معدلات الاحتفاظ
│   │   └── تحليل التغذية الراجعة
│   └── كفاءة الأسطول
│       ├── استخدام المركبات
│       ├── استهلاك الوقود
│       ├── صيانة المركبات
│       └── تحسين التشغيل
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    │   ├── إجراءات الطوارئ
    │   ├── خطط الإخلاء
    │   ├── تدريب الموظفين
    │   └── تحديث البروتوكولات
    ├── تتبع الحوادث
    │   ├── تسجيل الحوادث
    │   ├── تحليل الأسباب
    │   ├── إجراءات التحقيق
    │   └── منع تكرار الحوادث
    ├── تنسيق الإنقاذ
    │   ├── فرق الإنقاذ
    │   ├── المعدات الطبية
    │   ├── التنسيق مع السلطات
    │   └── متابعة الحالات
    └── تقارير الحوادث
        ├── تقارير مفصلة
        ├── إحصائيات الحوادث
        ├── تحليل الاتجاهات
        └── توصيات التحسين
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل المحسنة
```powershell
# سكريبت التشغيل الرئيسي المحسن
start-platform.ps1
├── المعاملات المتقدمة:
│   ├── -Mode (development/staging/production/minimal)
│   ├── -Services (comma-separated list of services)
│   ├── -SkipDatabase (تخطي تشغيل قواعد البيانات)
│   ├── -SkipMicroservices (تخطي الخدمات المصغرة)
│   ├── -SkipFrontend (تخطي الواجهات الأمامية)
│   ├── -SkipComprehensive (تخطي النظام الشامل)
│   ├── -EnableMonitoring (تفعيل المراقبة)
│   ├── -EnableLogging (تفعيل السجلات المفصلة)
│   ├── -HealthCheck (فحص صحة النظام)
│   └── -Parallel (تشغيل متوازي للخدمات)
├── الوظائف المتقدمة:
│   ├── فحص المتطلبات والتبعيات
│   ├── تشغيل قواعد البيانات بالترتيب الصحيح
│   ├── تشغيل الخدمات المصغرة مع مراقبة الحالة
│   ├── تشغيل الواجهات الأمامية مع Hot Reload
│   ├── مراقبة الحالة المستمرة
│   ├── إنشاء تقارير التشغيل
│   └── إعداد البيئة التطويرية
├── ميزات الأمان:
│   ├── التحقق من صحة التكوين
│   ├── تشفير البيانات الحساسة
│   ├── مراقبة الوصول غير المصرح
│   └── تسجيل جميع العمليات
└── التحسينات:
    ├── تحسين استخدام الذاكرة
    ├── تحسين أداء الشبكة
    ├── تحسين استخدام المعالج
    └── تحسين استخدام القرص

# سكريبت الإيقاف المحسن
stop-platform.ps1
├── المعاملات:
│   ├── -Force (إيقاف قسري)
│   ├── -Graceful (إيقاف تدريجي)
│   ├── -KeepDatabase (الاحتفاظ بقواعد البيانات)
│   ├── -KeepCache (الاحتفاظ بالتخزين المؤقت)
│   ├── -SaveState (حفظ حالة النظام)
│   └── -Backup (إنشاء نسخة احتياطية)
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب الصحيح
│   ├── تنظيف الموارد والذاكرة
│   ├── حفظ السجلات والتقارير
│   ├── إنشاء نسخ احتياطية
│   ├── تقرير الحالة النهائية
│   └── تنظيف الملفات المؤقتة

# سكريبت الاختبار الشامل
test-platform.ps1
├── المعاملات:
│   ├── -TestType (unit/integration/performance/security)
│   ├── -DatabaseOnly (اختبار قواعد البيانات فقط)
│   ├── -ServicesOnly (اختبار الخدمات فقط)
│   ├── -FullTest (اختبار شامل)
│   ├── -LoadTest (اختبار الأحمال)
│   ├── -SecurityTest (اختبار الأمان)
│   └── -GenerateReport (إنشاء تقرير مفصل)
├── الاختبارات:
│   ├── اختبار اتصال قواعد البيانات
│   ├── اختبار صحة الخدمات
│   ├── اختبار APIs والواجهات
│   ├── اختبار التكامل بين الخدمات
│   ├── اختبار
    total_earnings DECIMAL(12, 2) DEFAULT 0,
    average_rating DECIMAL(3, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط السائقين بالمركبات
CREATE TABLE driver_vehicle_assignments (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT REFERENCES drivers(id) ON DELETE CASCADE,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- جدول صيانة المركبات
CREATE TABLE vehicle_maintenance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL, -- ROUTINE, REPAIR, INSPECTION
    description TEXT NOT NULL,
    cost DECIMAL(10, 2),
    service_provider VARCHAR(100),
    scheduled_date DATE,
    completed_date DATE,
    next_service_date DATE,
    odometer_at_service DECIMAL(10, 2),
    status VARCHAR(20) DEFAULT 'SCHEDULED', -- SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول استهلاك الوقود
CREATE TABLE fuel_consumption (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT REFERENCES drivers(id),
    fuel_amount DECIMAL(8, 2) NOT NULL, -- liters
    cost DECIMAL(10, 2) NOT NULL,
    odometer_reading DECIMAL(10, 2),
    fuel_station VARCHAR(100),
    refuel_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### هـ) قاعدة بيانات المدفوعات (tecnodrive_payments)
```sql
-- جدول المحافظ الرقمية
CREATE TABLE wallets (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(12, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'YER', -- Yemeni Rial
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, FROZEN
    daily_limit DECIMAL(12, 2),
    monthly_limit DECIMAL(12, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    wallet_id BIGINT REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- CREDIT, DEBIT, TRANSFER
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    description TEXT,
    reference_id VARCHAR(100), -- Reference to ride, parcel, etc.
    reference_type VARCHAR(50), -- RIDE_PAYMENT, PARCEL_PAYMENT, TOP_UP, etc.
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED, CANCELLED
    payment_method VARCHAR(50), -- WALLET, CARD, BANK_TRANSFER, CASH
    external_transaction_id VARCHAR(100),
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طرق الدفع
CREATE TABLE payment_methods (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL, -- CREDIT_CARD, DEBIT_CARD, BANK_ACCOUNT, MOBILE_WALLET
    provider VARCHAR(50), -- VISA, MASTERCARD, PAYPAL, etc.
    last_four_digits VARCHAR(4),
    expiry_month INTEGER,
    expiry_year INTEGER,
    cardholder_name VARCHAR(100),
    is_default BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    token VARCHAR(255), -- Tokenized card details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- RIDE, PARCEL, SUBSCRIPTION
    service_id BIGINT NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_amount DECIMAL(12, 2) DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, CANCELLED
    due_date DATE,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عمولات السائقين
CREATE TABLE driver_earnings (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT NOT NULL,
    ride_id BIGINT,
    gross_amount DECIMAL(12, 2) NOT NULL,
    commission_rate DECIMAL(5, 4) NOT NULL, -- e.g., 0.15 for 15%
    commission_amount DECIMAL(12, 2) NOT NULL,
    net_amount DECIMAL(12, 2) NOT NULL,
    bonus_amount DECIMAL(12, 2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, WITHHELD
    payout_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

3. تتبع الطرد:
   parcel-service ↔ location-service
   │                 │
   ▼                 ▼
   customer-app ← driver-app

4. تسليم الطرد:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
├── 📍 تحديد المواقع والوجهات
├── 🚗 طلب الرحلات المختلفة
├── 💰 إدارة المحفظة والمدفوعات
├── 📊 تتبع الرحلات في الوقت الفعلي
├── ⭐ تقييم السائقين
├── 📱 الإشعارات الفورية
└── 📈 تاريخ الرحلات والفواتير

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
├── 🟢 تبديل حالة الاتصال (Online/Offline)
├── 📍 تتبع الموقع التلقائي
├── 🔔 استقبال طلبات الرحلات
├── 🗺️ التنقل والمسارات
├── 💰 تتبع الأرباح
├── ⭐ تقييم الركاب
├── 📊 إحصائيات الأداء
└── 🚗 إدارة المركبة

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   ├── مؤشرات الأداء الرئيسية
│   ├── الرسوم البيانية التفاعلية
│   └── التنبيهات والإشعارات
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   ├── التحقق من الهوية
│   ├── إدارة الأدوار والصلاحيات
│   └── سجل النشاطات
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   ├── جدولة الصيانة
│   ├── تتبع الأداء
│   └── إدارة التراخيص
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   ├── أرباح السائقين
│   ├── التقارير المالية
│   └── إعدادات الأسعار
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   ├── تتبع الشحنات
│   ├── إدارة المستودعات
│   └── تقارير التوصيل
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   ├── التنبؤات الذكية
│   ├── تقارير مخصصة
│   └── تصدير البيانات
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    ├── إدارة الإشعارات
    ├── النسخ الاحتياطية
    └── سجلات النظام
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   ├── حالة السائقين
│   ├── طوارئ ومشاكل
│   └── تدخل سريع
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   ├── تتبع المركبات
│   ├── تحليل المناطق
│   └── إدارة المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   ├── إدارة الطوارئ
│   ├── تواصل مع السائقين
│   └── دعم العملاء
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   ├── معدلات الاستجابة
│   ├── رضا العملاء
│   └── كفاءة الأسطول
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    ├── تتبع الحوادث
    ├── تنسيق الإنقاذ
    └── تقارير الحوادث
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل
```powershell
# سكريبت التشغيل الرئيسي
start-platform.ps1
├── المعاملات:
│   ├── -Mode (development/production/minimal)
│   ├── -SkipDatabase
│   ├── -SkipMicroservices
│   ├── -SkipFrontend
│   └── -SkipComprehensive
├── الوظائف:
│   ├── فحص المتطلبات
│   ├── تشغيل قواعد البيانات
│   ├── تشغيل الخدمات المصغرة
│   ├── تشغيل الواجهات الأمامية
│   └── مراقبة الحالة

# سكريبت الإيقاف
stop-platform.ps1
├── المعاملات:
│   ├── -Force
│   ├── -KeepDatabase
│   └── -KeepCache
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب
│   ├── تنظيف الموارد
│   ├── حفظ السجلات
│   └── تقرير الحالة النهائية

# سكريبت الاختبار
test-platform.ps1
├── المعاملات:
│   ├── -DatabaseOnly
│   ├── -ServicesOnly
│   └── -FullTest
├── الاختبارات:
│   ├── اتصال قواعد البيانات
│   ├── صحة الخدمات
│   ├── اختبار APIs
│   └── اختبار التكامل
```

### 2. 🛠️ أدوات التطوير
```
tools/
├── generators/
│   ├── service-generator.js      # مولد الخدمات المصغرة
│   ├── api-generator.js          # مولد APIs
│   ├── database-generator.js     # مولد مخططات قواعد البيانات
│   └── frontend-generator.js     # مولد مكونات الواجهة
├── testing/
│   ├── load-testing/            # اختبارات الحمولة
│   ├── integration-testing/     # اختبارات التكامل
│   ├── performance-testing/     # اختبارات الأداء
│   └── security-testing/        # اختبارات الأمان
├── monitoring/
│   ├── health-check.js          # فحص صحة النظام
│   ├── performance-monitor.js   # مراقبة الأداء
│   ├── log-analyzer.js          # تحليل السجلات
│   └── alert-manager.js         # إدارة التنبيهات
└── deployment/
    ├── docker-builder.js        # بناء صور Docker
    ├── k8s-deployer.js          # نشر على Kubernetes
    ├── backup-manager.js        # إدارة النسخ الاحتياطية
    └── migration-runner.js      # تشغيل ترحيل البيانات
```

---

## 🔒 الأمان والحماية

### 1. 🛡️ طبقات الأمان
```
طبقات الحماية المتعددة:
├── 🌐 طبقة الشبكة
│   ├── HTTPS/TLS 1.3
│   ├── WAF (Web Application Firewall)
│   ├── DDoS Protection
│   └── IP Whitelisting
├── 🔐 طبقة المصادقة
│   ├── JWT + Refresh Tokens
│   ├── OAuth2 / OpenID Connect
│   ├── Multi-Factor Authentication
│   └── Biometric Authentication
├── 🔑 طبقة التفويض
│   ├── Role-Based Access Control (RBAC)
│   ├── Attribute-Based Access Control (ABAC)
│   ├── API Rate Limiting
│   └── Resource-Level Permissions
├── 💾 طبقة البيانات
│   ├── Database Encryption at Rest
│   ├── Field-Level Encryption
│   ├── Data Masking
│   └── Audit Logging
└── 🔍 طبقة المراقبة
    ├── Security Information and Event Management (SIEM)
    ├── Intrusion Detection System (IDS)
    ├── Vulnerability Scanning
    └── Penetration Testing
```

### 2. 🔐 إدارة المفاتيح والأسرار
```
إدارة الأسرار:
├── HashiCorp Vault
│   ├── تشفير المفاتيح
│   ├── دوران المفاتيح التلقائي
│   ├── إدارة الشهادات
│   └── سياسات الوصول
├── Kubernetes Secrets
│   ├── أسرار قواعد البيانات
│   ├── مفاتيح APIs الخارجية
│   ├── شهادات TLS
│   └── متغيرات البيئة الحساسة
└── Environment-Specific Configs
    ├── Development Environment
    ├── Staging Environment
    ├── Production Environment
    └── Disaster Recovery Environment
```

---

## 📈 المراقبة والتحليلات

### 1. 📊 نظام المراقبة الشامل
```
مكونات المراقبة:
├── 📈 Prometheus + Grafana
│   ├── مقاييس الأداء
│   ├── استخدام الموارد
│   ├── معدلات الاستجابة
│   └── لوحات مراقبة تفاعلية
├── 📋 ELK Stack (Elasticsearch + Logstash + Kibana)
│   ├── جمع السجلات المركزي
│   ├── تحليل السجلات
│   ├── البحث في السجلات
│   └── تصور البيانات
├── 🔍 Jaeger (Distributed Tracing)
│   ├── تتبع الطلبات عبر الخدمات
│   ├── تحليل زمن الاستجابة
│   ├── اكتشاف الاختناقات
│   └── تحليل الأخطاء
└── 🚨 AlertManager
    ├── تنبيهات الأداء
    ├── تنبيهات الأمان
    ├── تنبيهات الأخطاء
    └── تصعيد التنبيهات
```

### 2. 🤖 الذكاء الاصطناعي والتحليلات
```
وحدات الذكاء الاصطناعي:
├── 🧠 Machine Learning Models
│   ├── تنبؤ الطلب
│   ├── تحسين المسارات
│   ├── تحليل السلوك
│   └── كشف الاحتيال
├── 📊 Real-time Analytics
│   ├── تحليل البيانات الفورية
│   ├── مؤشرات الأداء المباشرة
│   ├── تحليل الاتجاهات
│   └── التنبؤات قصيرة المدى
├── 🔍 Business Intelligence
│   ├── تقارير تنفيذية
│   ├── تحليل الربحية
│   ├── تحليل العملاء
│   └── تحليل السوق
└── 🎯 Recommendation Engine
    ├── توصيات للركاب
    ├── تحسين توزيع السائقين
    ├── تحسين الأسعار
    └── تحسين الخدمات
```

---

## 🚀 خطة التطوير والتوسع

### 1. 📅 خارطة الطريق (Roadmap)
```
المراحل القادمة:
├── 🎯 المرحلة 1 (Q1 2024): الإطلاق المحلي
│   ├── إطلاق في صنعاء وعدن
│   ├── 100 سائق و 1000 مستخدم
│   ├── خدمات الرحلات الأساسية
│   └── دعم اللغة العربية الكامل
├── 🎯 المرحلة 2 (Q2 2024): التوسع الوطني
│   ├── تغطية جميع المحافظات اليمنية
│   ├── 500 سائق و 10,000 مستخدم
│   ├── خدمات توصيل الطرود
│   └── تكامل مع البنوك المحلية
├── 🎯 المرحلة 3 (Q3 2024): الميزات المتقدمة
│   ├── الذكاء الاصطناعي والتحليلات
│   ├── خدمات الأسطول للشركات
│   ├── تطبيق الويب الكامل
│   └── APIs للمطورين الخارجيين
└── 🎯 المرحلة 4 (Q4 2024): التوسع الإقليمي
    ├── دول الخليج العربي
    ├── 5,000 سائق و 100,000 مستخدم
    ├── خدمات متعددة العملات
    └── شراكات استراتيجية
```

### 2. 🔧 التحسينات التقنية المستقبلية
```
التطويرات المخططة:
├── 🌐 تقنيات الجيل القادم
│   ├── 5G Integration
│   ├── Edge Computing
│   ├── Blockchain للشفافية
│   └── IoT للمركبات الذكية
├── 🤖 الذكاء الاصطناعي المتقدم
│   ├── Computer Vision للأمان
│   ├── Natural Language Processing
│   ├── Predictive Maintenance
│   └── Autonomous Vehicle Support
├── 🔒 أمان محسن
│   ├── Zero Trust Architecture
│   ├── Quantum-Safe Cryptography
│   ├── Advanced Threat Detection
│   └── Privacy-Preserving Analytics
└── 🚀 أداء محسن
    ├── Serverless Architecture
    ├── GraphQL APIs
    ├── Advanced Caching
    └── Global CDN
```

---

## 📋 الخلاصة والتوصيات

### ✅ نقاط القوة الحالية
1. **معمارية قابلة للتوسع**: تصميم microservices متقدم
2. **تغطية شاملة**: جميع جوانب النقل والتوصيل
3. **تقنيات حديثة**: استخدام أحدث التقنيات والأدوات
4. **أمان متقدم**: طبقات حماية متعددة
5. **مراقبة شاملة**: نظام مراقبة متكامل

### 🔧 التحسينات المقترحة
1. **تحسين الأداء**: تحسين استعلامات قواعد البيانات
2. **تطوير الاختبارات**: زيادة تغطية الاختبارات التلقائية
3. **تحسين التوثيق**: توثيق أكثر تفصيلاً للمطورين
4. **تحسين UX**: تحسين تجربة المستخدم في التطبيقات
5. **تحسين DevOps**: أتمتة أكثر لعمليات النشر

### 🎯 الأولويات القادمة
1. **إكمال الاختبارات**: اختبارات شاملة لجميع المكونات
2. **تحسين الأمان**: مراجعة أمنية شاملة
3. **تحسين الأداء**: تحسين أداء النظام تحت الأحمال العالية
4. **التوثيق**: إكمال التوثيق الفني والمستخدم
5. **التدريب**: تدريب الفريق على النظام الجديد

---

*تم إعداد هذا التحليل بناءً على فحص شامل لملفات المشروع في `D:\tecno-drive-platform`*
*آخر تحديث: $(date)*


# 📊 التحليل الشامل والتفصيلي لمنصة TecnoDrive

## 🎯 نظرة عامة على المنصة

**منصة TecnoDrive** هي نظام شامل ومتطور لإدارة النقل والتوصيل يعتمد على معمارية الخدمات المصغرة (Microservices Architecture) مع دعم متعدد المستأجرين (Multi-tenant SaaS Platform).

### 📍 الموقع والمسار
- **المسار الرئيسي**: `D:\tecno-drive-platform`
- **نوع المشروع**: منصة SaaS متكاملة
- **المعمارية**: Microservices + Event-Driven Architecture
- **قواعد البيانات**: PostgreSQL + Redis + MongoDB
- **التقنيات**: Java Spring Boot + Python FastAPI + React + Angular

### 🔢 إحصائيات المشروع
- **عدد الخدمات المصغرة**: 13 خدمة
- **عدد قواعد البيانات**: 13 قاعدة بيانات
- **عدد التطبيقات الأمامية**: 4 تطبيقات
- **عدد ملفات الكود**: 500+ ملف
- **حجم المشروع**: ~2.5 GB

---

## 🏗️ الهيكل التنظيمي للمشروع

### 📁 المجلدات الرئيسية (التسلسل الهرمي)

```
tecno-drive-platform/
├── 📁 backend/                     # الخدمات الخلفية (Backend Services)
│   ├── 📁 microservices/          # الخدمات المصغرة Java Spring Boot
│   │   ├── 📁 core/               # الخدمات الأساسية (3 خدمات)
│   │   │   ├── user-service/      # خدمة إدارة المستخدمين
│   │   │   ├── auth-service/      # خدمة المصادقة والتفويض
│   │   │   └── payment-service/   # خدمة المدفوعات والمحافظ
│   │   ├── 📁 business/           # خدمات الأعمال (6 خدمات)
│   │   │   ├── ride-service/      # خدمة إدارة الرحلات
│   │   │   ├── fleet-service/     # خدمة إدارة الأسطول
│   │   │   ├── parcel-service/    # خدمة الطرود والتوصيل
│   │   │   ├── location-service/  # خدمة المواقع والخرائط
│   │   │   ├── analytics-service/ # خدمة التحليلات والذكاء الاصطناعي
│   │   │   └── notification-service/ # خدمة الإشعارات
│   │   └── 📁 infrastructure/     # خدمات البنية التحتية (4 خدمات)
│   │       ├── api-gateway/       # بوابة API الموحدة
│   │       ├── eureka-server/     # خادم اكتشاف الخدمات
│   │       ├── config-server/     # خادم التكوين المركزي
│   │       └── monitoring-service/ # خدمة المراقبة والصحة
│   ├── 📁 shared/                 # المكتبات والمكونات المشتركة
│   ├── 📁 comprehensive-system/   # النظام الشامل Python FastAPI
│   └── 📁 api-docs/              # وثائق API التفاعلية
│
├── 📁 frontend/                    # الواجهات الأمامية
│   ├── 📁 admin-dashboard/        # لوحة تحكم الإدارة (React)
│   ├── 📁 user-apps/             # تطبيقات المستخدمين
│   │   ├── 📁 driver-app/        # تطبيق السائقين (React Native)
│   │   └── 📁 passenger-app/     # تطبيق الركاب (React Native)
│   ├── 📁 operator-dashboard/     # لوحة تحكم المشغلين (Angular)
│   └── 📁 shared-components/      # المكونات المشتركة
│
├── 📁 database/                   # قواعد البيانات والمخططات
│   ├── 📁 schemas/               # مخططات قواعد البيانات
│   ├── 📁 migrations/            # ملفات الترحيل
│   ├── 📁 seeds/                 # البيانات الأولية
│   └── 📁 backups/               # النسخ الاحتياطية
│
├── 📁 infrastructure/             # البنية التحتية والنشر
│   ├── 📁 docker/                # ملفات Docker
│   ├── 📁 kubernetes/            # ملفات Kubernetes
│   ├── 📁 terraform/             # Infrastructure as Code
│   └── 📁 monitoring/            # أدوات المراقبة
│
├── 📁 tools/                      # الأدوات المساعدة
│   ├── 📁 scripts/               # سكريبتات التشغيل والإدارة
│   ├── 📁 generators/            # مولدات الكود
│   └── 📁 testing/               # أدوات الاختبار
│
└── 📁 docs/                       # التوثيق الشامل
    ├── 📁 api/                   # توثيق APIs
    ├── 📁 architecture/          # الهندسة المعمارية
    ├── 📁 deployment/            # أدلة النشر
    └── 📁 development/           # أدلة التطوير
```

---

## 🔧 المعمارية التقنية المفصلة

### 1. 🏛️ معمارية الخدمات المصغرة (Microservices Architecture)

#### أ) الخدمات الأساسية (Core Services)
```
🔒 auth-service (Port: 8081)
├── المسؤوليات:
│   ├── المصادقة والتفويض (JWT + OAuth2)
│   ├── إدارة الجلسات والرموز المميزة
│   ├── التحكم في الوصول القائم على الأدوار (RBAC)
│   └── تكامل مع مقدمي الهوية الخارجيين
├── قاعدة البيانات: tecnodrive_auth
├── التقنيات: Spring Security + JWT + Redis
└── APIs: /auth/login, /auth/register, /auth/validate

👤 user-service (Port: 8083)
├── المسؤوليات:
│   ├── إدارة ملفات المستخدمين الشخصية
│   ├── التحقق من الهوية والوثائق
│   ├── إدارة التفضيلات والإعدادات
│   └── تتبع نشاط المستخدمين
├── قاعدة البيانات: tecnodrive_users
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /users/profile, /users/documents, /users/preferences

💰 payment-service (Port: 8085)
├── المسؤوليات:
│   ├── معالجة المدفوعات والمحافظ الرقمية
│   ├── إدارة طرق الدفع المتعددة
│   ├── تتبع المعاملات المالية
│   └── تكامل مع بوابات الدفع المحلية
├── قاعدة البيانات: tecnodrive_payments
├── التقنيات: Spring Boot + Stripe API + PayPal
└── APIs: /payments/process, /wallet/balance, /transactions/history
```

#### ب) خدمات الأعمال (Business Services)
```
🚗 ride-service (Port: 8082)
├── المسؤوليات:
│   ├── إدارة طلبات الرحلات والحجوزات
│   ├── مطابقة السائقين مع الركاب
│   ├── تتبع الرحلات في الوقت الفعلي
│   └── حساب التكاليف والمسافات
├── قاعدة البيانات: tecnodrive_rides
├── التقنيات: Spring Boot + WebSocket + Redis
└── APIs: /rides/request, /rides/track, /rides/complete

🚛 fleet-service (Port: 8084)
├── المسؤوليات:
│   ├── إدارة أسطول المركبات
│   ├── جدولة الصيانة والفحوصات
│   ├── تتبع استهلاك الوقود والأداء
│   └── إدارة تراخيص السائقين
├── قاعدة البيانات: tecnodrive_fleet
├── التقنيات: Spring Boot + JPA + PostgreSQL
└── APIs: /fleet/vehicles, /fleet/maintenance, /fleet/drivers

📦 parcel-service (Port: 8086)
├── المسؤوليات:
│   ├── إدارة طلبات توصيل الطرود
│   ├── تتبع الطرود عبر المراحل المختلفة
│   ├── إدارة المستودعات ونقاط التوزيع
│   └── حساب تكاليف الشحن
├── قاعدة البيانات: tecnodrive_parcels
├── التقنيات: Spring Boot + JPA + MongoDB
└── APIs: /parcels/create, /parcels/track, /parcels/deliver

📍 location-service (Port: 8087)
├── المسؤوليات:
│   ├── إدارة المواقع الجغرافية والخرائط
│   ├── حساب المسارات المثلى
│   ├── تتبع المواقع في الوقت الفعلي
│   └── تكامل مع خدمات الخرائط الخارجية
├── قاعدة البيانات: tecnodrive_locations
├── التقنيات: Spring Boot + PostGIS + Google Maps API
└── APIs: /locations/geocode, /routes/optimize, /tracking/live

📊 analytics-service (Port: 8088)
├── المسؤوليات:
│   ├── تحليل البيانات والذكاء الاصطناعي
│   ├── إنشاء التقارير والإحصائيات
│   ├── التنبؤ بالطلب والأنماط
│   └── مراقبة الأداء والمؤشرات
├── قاعدة البيانات: tecnodrive_analytics
├── التقنيات: Spring Boot + Apache Spark + TensorFlow
└── APIs: /analytics/reports, /analytics/predictions, /analytics/kpis

🔔 notification-service (Port: 8089)
├── المسؤوليات:
│   ├── إرسال الإشعارات المتعددة القنوات
│   ├── إدارة قوالب الرسائل
│   ├── جدولة الإشعارات المؤجلة
│   └── تتبع معدلات التسليم والقراءة
├── قاعدة البيانات: tecnodrive_notifications
├── التقنيات: Spring Boot + Firebase + Twilio + SMTP
└── APIs: /notifications/send, /notifications/templates, /notifications/status
```

#### ج) خدمات البنية التحتية (Infrastructure Services)
```
🌐 api-gateway (Port: 8080)
├── المسؤوليات:
│   ├── توجيه الطلبات للخدمات المناسبة
│   ├── المصادقة والتفويض المركزي
│   ├── تحديد معدل الطلبات (Rate Limiting)
│   └── مراقبة وتسجيل الطلبات
├── التقنيات: Spring Cloud Gateway + Eureka Client
└── التكوين: Load Balancing + Circuit Breaker

🔍 eureka-server (Port: 8761)
├── المسؤوليات:
│   ├── اكتشاف وتسجيل الخدمات
│   ├── مراقبة صحة الخدمات
│   ├── توزيع الأحمال التلقائي
│   └── إدارة دورة حياة الخدمات
├── التقنيات: Spring Cloud Netflix Eureka
└── واجهة الإدارة: http://localhost:8761

⚙️ config-server (Port: 8888)
├── المسؤوليات:
│   ├── إدارة التكوين المركزي
│   ├── تحديث التكوين بدون إعادة تشغيل
│   ├── إدارة البيئات المختلفة
│   └── تشفير البيانات الحساسة
├── التقنيات: Spring Cloud Config
└── مصدر التكوين: Git Repository

📈 monitoring-service (Port: 9090)
├── المسؤوليات:
│   ├── مراقبة أداء النظام والخدمات
│   ├── جمع المقاييس والسجلات
│   ├── إنشاء التنبيهات التلقائية
│   └── لوحات مراقبة تفاعلية
├── التقنيات: Prometheus + Grafana + ELK Stack
└── واجهة المراقبة: http://localhost:9090
```

### 2. 🗺️ نظام الخرائط التفاعلية المتقدم

#### أ) مقدمو الخرائط المدعومون
```
خدمات الخرائط المتاحة:
├── 🌍 OpenStreetMap (افتراضي)
│   ├── خادم البلاط: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
│   ├── خدمة Nominatim للبحث الجغرافي
│   ├── خدمة OSRM لحساب المسارات
│   └── مجاني ومفتوح المصدر
├── 🗺️ Google Maps
│   ├── يتطلب مفتاح API: GOOGLE_MAPS_API_KEY
│   ├── دقة عالية في البيانات
│   ├── دعم متقدم للمسارات
│   └── تكلفة حسب الاستخدام
├── 🎨 Mapbox
│   ├── يتطلب رمز الوصول: MAPBOX_ACCESS_TOKEN
│   ├── تخصيص متقدم للخرائط
│   ├── أداء عالي
│   └── خطط مرنة للتسعير
└── 🛰️ خرائط الأقمار الصناعية
    ├── صور عالية الدقة
    ├── تحديثات دورية
    └── مناسبة للمناطق النائية
```

#### ب) ميزات الخرائط التفاعلية
```
الطبقات المتاحة:
├── 🚗 طبقة المركبات
│   ├── مواقع المركبات الحالية
│   ├── حالة المركبات (نشط، مشغول، متوقف)
│   ├── معلومات السائق والرحلة
│   └── تحديث فوري كل 5 ثوانٍ
├── 🚦 طبقة حركة المرور
│   ├── مستويات الازدحام
│   ├── السرعة المتوسطة
│   ├── الحوادث والعوائق
│   └── توقعات حركة المرور
├── 📦 طبقة الطرود
│   ├── مواقع الطرود الحالية
│   ├── حالة التسليم
│   ├── مسارات التوصيل
│   └── نقاط التجميع والتوزيع
├── 🔥 خرائط الطلب الحرارية
│   ├── مناطق الطلب العالي
│   ├── تحليل الطلب حسب الوقت
│   ├── توقعات الطلب المستقبلي
│   └── تحسين توزيع الأسطول
└── 🏢 طبقة نقاط الاهتمام
    ├── المطارات والمحطات
    ├── المستشفيات والمراكز الطبية
    ├── المراكز التجارية
    └── المعالم السياحية
```

---

## 🗄️ قواعد البيانات والمخططات التفصيلية

### 1. 📊 نظرة عامة على قواعد البيانات

```
قواعد البيانات الرئيسية (13 قاعدة بيانات):
├── PostgreSQL Databases (11 قاعدة)
│   ├── tecnodrive_auth          # المصادقة والتفويض
│   ├── tecnodrive_users         # بيانات المستخدمين
│   ├── tecnodrive_rides         # إدارة الرحلات
│   ├── tecnodrive_fleet         # إدارة الأسطول
│   ├── tecnodrive_payments      # المدفوعات والمحافظ
│   ├── tecnodrive_parcels       # إدارة الطرود
│   ├── tecnodrive_locations     # المواقع والخرائط
│   ├── tecnodrive_analytics     # التحليلات والتقارير
│   ├── tecnodrive_notifications # الإشعارات
│   ├── tecnodrive_config        # التكوين المركزي
│   └── tecnodrive_monitoring    # المراقبة والسجلات
├── Redis Cache (1 قاعدة)
│   └── tecnodrive_cache         # التخزين المؤقت
└── MongoDB (1 قاعدة)
    └── tecnodrive_documents     # الوثائق والملفات
```

### 2. 🔗 مخططات قواعد البيانات التفصيلية

#### أ) قاعدة بيانات المصادقة (tecnodrive_auth)
```sql
-- جدول المستخدمين للمصادقة
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    role_id BIGINT REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT REFERENCES users(id),
    PRIMARY KEY (user_id, role_id)
);

-- جدول الجلسات النشطة
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255),
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجل المصادقة
CREATE TABLE auth_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- LOGIN, LOGOUT, FAILED_LOGIN, etc.
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ب) قاعدة بيانات المواقع والخرائط (tecnodrive_locations)
```sql
-- جدول المواقع الجغرافية
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255),
    address TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_type VARCHAR(50), -- PICKUP, DROPOFF, WAREHOUSE, etc.
    city VARCHAR(100),
    district VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Yemen',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهرس مكاني للبحث السريع
CREATE INDEX idx_locations_coordinates ON locations USING GIST (
    ST_Point(longitude, latitude)
);

-- جدول تتبع المواقع في الوقت الفعلي
CREATE TABLE location_tracking (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL, -- VEHICLE, DRIVER, PARCEL
    entity_id BIGINT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2), -- km/h
    heading INTEGER, -- degrees (0-360)
    accuracy DECIMAL(5, 2), -- meters
    battery_level INTEGER, -- percentage
    signal_strength INTEGER, -- dBm
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للبحث السريع حسب النوع والمعرف
CREATE INDEX idx_tracking_entity ON location_tracking (entity_type, entity_id);
CREATE INDEX idx_tracking_timestamp ON location_tracking (timestamp);

-- جدول المسارات المحسوبة
CREATE TABLE calculated_routes (
    id BIGSERIAL PRIMARY KEY,
    route_name VARCHAR(255),
    start_location JSONB NOT NULL,
    end_location JSONB NOT NULL,
    waypoints JSONB, -- array of coordinates
    route_geometry JSONB, -- GeoJSON LineString
    total_distance DECIMAL(10, 2), -- kilometers
    estimated_duration INTEGER, -- minutes
    optimization_type VARCHAR(50), -- SHORTEST, FASTEST, FUEL_EFFICIENT
    traffic_considered BOOLEAN DEFAULT false,
    created_by BIGINT,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المناطق الجغرافية (Geofences)
CREATE TABLE geofences (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fence_type VARCHAR(20) NOT NULL, -- CIRCLE, POLYGON
    geometry JSONB NOT NULL, -- GeoJSON geometry
    radius DECIMAL(8, 2), -- meters (for circle type)
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ج) قاعدة بيانات الطرود المحسنة (tecnodrive_parcels)
```sql
-- جدول الطرود الرئيسي
CREATE TABLE parcels (
    id BIGSERIAL PRIMARY KEY,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    parcel_type VARCHAR(50) NOT NULL, -- DOCUMENT, PACKAGE, FRAGILE, etc.
    weight DECIMAL(8, 3), -- kg
    dimensions JSONB, -- {length, width, height} in cm
    declared_value DECIMAL(12, 2),
    pickup_location JSONB NOT NULL,
    delivery_location JSONB NOT NULL,
    pickup_instructions TEXT,
    delivery_instructions TEXT,
    status VARCHAR(30) DEFAULT 'CREATED', -- CREATED, PICKED_UP, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED, CANCELLED
    priority_level VARCHAR(20) DEFAULT 'STANDARD', -- EXPRESS, STANDARD, ECONOMY
    payment_method VARCHAR(30),
    shipping_cost DECIMAL(10, 2),
    insurance_cost DECIMAL(10, 2) DEFAULT 0,
    cod_amount DECIMAL(12, 2) DEFAULT 0, -- Cash on Delivery
    scheduled_pickup_time TIMESTAMP,
    actual_pickup_time TIMESTAMP,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    assigned_driver_id BIGINT,
    assigned_vehicle_id BIGINT,
    current_warehouse_id BIGINT,
    delivery_proof JSONB, -- photos, signatures, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تتبع حالة الطرود
CREATE TABLE parcel_status_history (
    id BIGSERIAL PRIMARY KEY,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    status VARCHAR(30) NOT NULL,
    location JSONB,
    notes TEXT,
    updated_by BIGINT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستودعات
CREATE TABLE warehouses (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    location JSONB NOT NULL, -- {lat, lng}
    capacity INTEGER, -- number of parcels
    current_load INTEGER DEFAULT 0,
    operating_hours JSONB, -- {open_time, close_time} for each day
    contact_info JSONB, -- {phone, email, manager}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون المستودعات
CREATE TABLE warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    warehouse_id BIGINT REFERENCES warehouses(id) ON DELETE CASCADE,
    parcel_id BIGINT REFERENCES parcels(id) ON DELETE CASCADE,
    shelf_location VARCHAR(50),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatched_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'STORED' -- STORED, DISPATCHED
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
├── location-service ↔ All Services
│   └── تتبع المواقع لجميع الكيانات
├── parcel-service ↔ fleet-service
│   └── parcels.assigned_driver_id → drivers.id
│   └── parcels.assigned_vehicle_id → vehicles.id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود المحسنة

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. معالجة الطلب:
   parcel-service → warehouse-management
   │                 │
   ▼                 ▼
   inventory-system → route-optimization
   │                 │
   ▼                 ▼
   fleet-service

3. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

4. الالتقاط والنقل:
   driver-app → parcel-service
   │                 │
   ▼                 ▼
   location-service → warehouse-system
   │                 │
   ▼                 ▼
   tracking-updates

5. التسليم النهائي:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
   │                 │
   ▼                 ▼
   customer-feedback
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
│   ├── تسجيل دخول بالهاتف/البريد الإلكتروني
│   ├── التحقق من الهوية بالرسائل النصية
│   ├── تسجيل الدخول بالبصمة/الوجه
│   └── تسجيل الدخول بوسائل التواصل الاجتماعي
├── 📍 تحديد المواقع والوجهات
│   ├── تحديد الموقع الحالي تلقائ
│   ├── البحث الذكي للعناوين
│   ├── حفظ العناوين المفضلة
│   └── اقتراح الوجهات الشائعة
├── 🚗 طلب الرحلات المختلفة
│   ├── رحلات فردية (اقتصادية، مريحة، فاخرة)
│   ├── رحلات مشتركة لتوفير التكلفة
│   ├── رحلات مجدولة مسبق
│   └── رحلات طويلة المدى
├── 💰 إدارة المحفظة والمدفوعات
│   ├── عرض رصيد المحفظة الرقمية
│   ├── إضافة أموال للمحفظة
│   ├── ربط بطاقات الائتمان/الخصم
│   └── تاريخ المعاملات المالية
├── 📊 تتبع الرحلات في الوقت الفعلي
│   ├── موقع السائق على الخريطة
│   ├── الوقت المتوقع للوصول
│   ├── معلومات السائق والمركبة
│   └── إمكانية التواصل مع السائق
├── ⭐ تقييم السائقين
│   ├── تقييم من 1-5 نجوم
│   ├── كتابة تعليقات
│   ├── الإبلاغ عن مشاكل
│   └── إضافة السائقين للمفضلة
├── 📱 الإشعارات الفورية
│   ├── تأكيد قبول الرحلة
│   ├── تحديثات حالة الرحلة
│   ├── العروض والخصومات
│   └── تذكيرات الرحلات المجدولة
└── 📈 تاريخ الرحلات والفواتير
    ├── سجل جميع الرحلات السابقة
    ├── تفاصيل التكلفة لكل رحلة
    ├── تحميل الفواتير
    └── إحصائيات الاستخدام الشهرية

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
│   ├── مصادقة قوية للسائقين
│   ├── التحقق من صحة الرخصة
│   ├── فحص الخلفية الجنائية
│   └── تحديث الوثائق دور
├── 🟢 تبديل حالة الاتصال (Online/Offline)
│   ├── تشغيل/إيقاف استقبال الطلبات
│   ├── تحديد ساعات العمل
│   ├── وضع الاستراحة
│   └── حالة الطوارئ
├── 📍 تتبع الموقع التلقائي
│   ├── GPS عالي الدقة
│   ├── تحديث الموقع كل 5 ثوانٍ
│   ├── توفير البطارية الذكي
│   └── العمل في المناطق ضعيفة الإشارة
├── 🔔 استقبال طلبات الرحلات
│   ├── تنبيهات صوتية ومرئية
│   ├── معلومات الراكب والوجهة
│   ├── تقدير الأرباح
│   └── خيار القبول/الرفض
├── 🗺️ التنقل والمسارات
│   ├── تكامل مع خرائط Google/OpenStreetMap
│   ├── مسارات محسنة لتجنب الازدحام
│   ├── إرشادات صوتية
│   └── تحديثات حركة المرور الفورية
├── 💰 تتبع الأرباح
│   ├── الأرباح اليومية والأسبوعية
│   ├── تفاصيل كل رحلة
│   ├── العمولات والخصومات
│   └── تقارير الضرائب
├── ⭐ تقييم الركاب
│   ├── تقييم تجربة الرحلة
│   ├── الإبلاغ عن مشاكل
│   ├── حظر ركاب مشكلين
│   └── تفضيل ركاب معينين
├── 📊 إحصائيات الأداء
│   ├── معدل القبول
│   ├── التقييم العام
│   ├── عدد الرحلات المكتملة
│   └── مقارنة مع السائقين الآخرين
└── 🚗 إدارة المركبة
    ├── معلومات المركبة
    ├── جدولة الصيانة
    ├── تتبع استهلاك الوقود
    └── تقارير الأعطال

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js + D3.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   │   ├── عدد الرحلات النشطة
│   │   ├── عدد السائقين المتصلين
│   │   ├── عدد الطرود قيد التوصيل
│   │   └── الإيرادات اليومية
│   ├── مؤشرات الأداء الرئيسية (KPIs)
│   │   ├── معدل إكمال الرحلات
│   │   ├── متوسط وقت الاستجابة
│   │   ├── رضا العملاء
│   │   └── كفاءة الأسطول
│   ├── الرسوم البيانية التفاعلية
│   │   ├── اتجاهات الطلب
│   │   ├── توزيع الرحلات جغراف
│   │   ├── أداء السائقين
│   │   └── تحليل الإيرادات
│   └── التنبيهات والإشعارات
│       ├── تنبيهات النظام
│       ├── مشاكل تقنية
│       ├── شكاوى العملاء
│       └── تحديثات مهمة
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   │   ├── قائمة شاملة بالمستخدمين
│   │   ├── تفاصيل الملفات الشخصية
│   │   ├── إحصائيات الاستخدام
│   │   └── إدارة الحسابات
│   ├── التحقق من الهوية
│   │   ├── مراجعة الوثائق المرفوعة
│   │   ├── التحقق من صحة البيانات
│   │   ├── الموافقة/الرفض
│   │   └── طلب وثائق إضافية
│   ├── إدارة الأدوار والصلاحيات
│   │   ├── تعريف الأدوار
│   │   ├── تخصيص الصلاحيات
│   │   ├── إدارة الوصول
│   │   └── مراجعة الأنشطة
│   └── سجل النشاطات
│       ├── تسجيل الدخول/الخروج
│       ├── العمليات المنجزة
│       ├── التغييرات على البيانات
│       └── الأنشطة المشبوهة
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   │   ├── قائمة المركبات
│   │   ├── حالة كل مركبة
│   │   ├── تخصيص السائقين
│   │   └── تتبع الأداء
│   ├── جدولة الصيانة
│   │   ├── جدولة الصيانة الدورية
│   │   ├── تتبع تكاليف الصيانة
│   │   ├── تذكيرات الصيانة
│   │   └── تقارير الأعطال
│   ├── تتبع الأداء
│   │   ├── استهلاك الوقود
│   │   ├── المسافات المقطوعة
│   │   ├── ساعات التشغيل
│   │   └── معدلات الأعطال
│   └── إدارة التراخيص
│       ├── تراخيص السائقين
│       ├── تراخيص المركبات
│       ├── التأمين
│       └── تجديد الوثائق
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   │   ├── معالجة المدفوعات
│   │   ├── إنشاء الفواتير
│   │   ├── تتبع المستحقات
│   │   └── إدارة المبالغ المستردة
│   ├── أرباح السائقين
│   │   ├── حساب العمولات
│   │   ├── جدولة المدفوعات
│   │   ├── تقارير الأرباح
│   │   └── إدارة الحوافز
│   ├── التقارير المالية
│   │   ├── تقارير الإيرادات
│   │   ├── تحليل التكاليف
│   │   ├── الربحية
│   │   └── التنبؤات المالية
│   └── إعدادات الأسعار
│       ├── تحديد أسعار الرحلات
│       ├── أسعار الطرود
│       ├── العروض والخصومات
│       └── الأسعار الديناميكية
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   │   ├── قائمة الطلبات
│   │   ├── تفاصيل كل طلب
│   │   ├── تخصيص السائقين
│   │   └── جدولة التوصيل
│   ├── تتبع الشحنات
│   │   ├── تتبع فوري للطرود
│   │   ├── تحديثات الحالة
│   │   ├── إشعارات العملاء
│   │   └── حل المشاكل
│   ├── إدارة المستودعات
│   │   ├── مخزون المستودعات
│   │   ├── عمليات الاستلام والإرسال
│   │   ├── تحسين التخزين
│   │   └── إدارة الموظفين
│   └── تقارير التوصيل
│       ├── معدلات التسليم
│       ├── أوقات التوصيل
│       ├── رضا العملاء
│       └── تحليل الأداء
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   │   ├── تحليل سلوك العملاء
│   │   ├── أنماط الاستخدام
│   │   ├── تحليل الطلب
│   │   └── تحليل الأداء
│   ├── التنبؤات الذكية
│   │   ├── توقع الطلب
│   │   ├── تحسين الأسطول
│   │   ├── التنبؤ بالإيرادات
│   │   └── تحليل المخاطر
│   ├── تقارير مخصصة
│   │   ├── إنشاء تقارير حسب الطلب
│   │   ├── جدولة التقارير
│   │   ├── تخصيص المحتوى
│   │   └── توزيع التقارير
│   └── تصدير البيانات
│       ├── تصدير إلى Excel/CSV
│       ├── تكامل مع أنظمة خارجية
│       ├── APIs للبيانات
│       └── النسخ الاحتياطية
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    │   ├── إعدادات المنصة
    │   ├── معاملات النظام
    │   ├── إعدادات الأمان
    │   └── تخصيص الواجهة
    ├── إدارة الإشعارات
    │   ├── قوالب الرسائل
    │   ├── قنوات الإرسال
    │   ├── جدولة الإشعارات
    │   └── تتبع التسليم
    ├── النسخ الاحتياطية
    │   ├── جدولة النسخ الاحتياطية
    │   ├── استعادة البيانات
    │   ├── تشفير النسخ
    │   └── التخزين السحابي
    └── سجلات النظام
        ├── سجلات الأنشطة
        ├── سجلات الأخطاء
        ├── سجلات الأداء
        └── سجلات الأمان
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js + Socket.io
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   │   ├── خريطة تفاعلية للرحلات الجارية
│   │   ├── تفاصيل كل رحلة
│   │   ├── التدخل في حالات الطوارئ
│   │   └── إعادة توجيه الرحلات
│   ├── حالة السائقين
│   │   ├── السائقين المتصلين/غير المتصلين
│   │   ├── موقع كل سائق
│   │   ├── حالة المركبات
│   │   └── أداء السائقين
│   ├── طوارئ ومشاكل
│   │   ├── تنبيهات الطوارئ الفورية
│   │   ├── حوادث المرور
│   │   ├── أعطال المركبات
│   │   └── شكاوى العملاء العاجلة
│   └── تدخل سريع
│       ├── إرسال مساعدة فورية
│       ├── تحويل الرحلات
│       ├── التواصل مع الطوارئ
│       └── تنسيق الإنقاذ
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   │   ├── عرض جميع المركبات
│   │   ├── طبقات متعددة للبيانات
│   │   ├── تحديث فوري للمواقع
│   │   └── أدوات تحليل مكانية
│   ├── تتبع المركبات
│   │   ├── مسار كل مركبة
│   │   ├── سرعة وحالة المركبة
│   │   ├── تاريخ المواقع
│   │   └── تنبيهات الانحراف
│   ├── تحليل المناطق
│   │   ├── مناطق الطلب العالي
│   │   ├── نقاط الازدحام
│   │   ├── أوقات الذروة
│   │   └── تحسين التغطية
│   └── إدارة المسارات
│       ├── تحسين المسارات
│       ├── تجنب الازدحام
│       ├── مسارات الطوارئ
│       └── تحديث المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   │   ├── تنبيهات النظام
│   │   ├── تنبيهات الأمان
│   │   ├── تنبيهات الأداء
│   │   └── تنبيهات العملاء
│   ├── إدارة الطوارئ
│   │   ├── بروتوكولات الطوارئ
│   │   ├── فرق الاستجابة
│   │   ├── تنسيق الإنقاذ
│   │   └── تقارير الحوادث
│   ├── تواصل مع السائقين
│   │   ├── رسائل فورية
│   │   ├── مكالمات صوتية
│   │   ├── تعليمات التنقل
│   │   └── تحديثات الحالة
│   └── دعم العملاء
│       ├── استقبال الشكاوى
│       ├── حل المشاكل
│       ├── متابعة الطلبات
│       └── تحسين الخدمة
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   │   ├── معدلات إكمال الرحلات
│   │   ├── أوقات الاستجابة
│   │   ├── جودة الخدمة
│   │   └── مقارنات الأداء
│   ├── معدلات الاستجابة
│   │   ├── وقت الوصول للعملاء
│   │   ├── سرعة حل المشاكل
│   │   ├── كفاءة التوزيع
│   │   └── تحسين العمليات
│   ├── رضا العملاء
│   │   ├── تقييمات العملاء
│   │   ├── شكاوى ومقترحات
│   │   ├── معدلات الاحتفاظ
│   │   └── تحليل التغذية الراجعة
│   └── كفاءة الأسطول
│       ├── استخدام المركبات
│       ├── استهلاك الوقود
│       ├── صيانة المركبات
│       └── تحسين التشغيل
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    │   ├── إجراءات الطوارئ
    │   ├── خطط الإخلاء
    │   ├── تدريب الموظفين
    │   └── تحديث البروتوكولات
    ├── تتبع الحوادث
    │   ├── تسجيل الحوادث
    │   ├── تحليل الأسباب
    │   ├── إجراءات التحقيق
    │   └── منع تكرار الحوادث
    ├── تنسيق الإنقاذ
    │   ├── فرق الإنقاذ
    │   ├── المعدات الطبية
    │   ├── التنسيق مع السلطات
    │   └── متابعة الحالات
    └── تقارير الحوادث
        ├── تقارير مفصلة
        ├── إحصائيات الحوادث
        ├── تحليل الاتجاهات
        └── توصيات التحسين
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل المحسنة
```powershell
# سكريبت التشغيل الرئيسي المحسن
start-platform.ps1
├── المعاملات المتقدمة:
│   ├── -Mode (development/staging/production/minimal)
│   ├── -Services (comma-separated list of services)
│   ├── -SkipDatabase (تخطي تشغيل قواعد البيانات)
│   ├── -SkipMicroservices (تخطي الخدمات المصغرة)
│   ├── -SkipFrontend (تخطي الواجهات الأمامية)
│   ├── -SkipComprehensive (تخطي النظام الشامل)
│   ├── -EnableMonitoring (تفعيل المراقبة)
│   ├── -EnableLogging (تفعيل السجلات المفصلة)
│   ├── -HealthCheck (فحص صحة النظام)
│   └── -Parallel (تشغيل متوازي للخدمات)
├── الوظائف المتقدمة:
│   ├── فحص المتطلبات والتبعيات
│   ├── تشغيل قواعد البيانات بالترتيب الصحيح
│   ├── تشغيل الخدمات المصغرة مع مراقبة الحالة
│   ├── تشغيل الواجهات الأمامية مع Hot Reload
│   ├── مراقبة الحالة المستمرة
│   ├── إنشاء تقارير التشغيل
│   └── إعداد البيئة التطويرية
├── ميزات الأمان:
│   ├── التحقق من صحة التكوين
│   ├── تشفير البيانات الحساسة
│   ├── مراقبة الوصول غير المصرح
│   └── تسجيل جميع العمليات
└── التحسينات:
    ├── تحسين استخدام الذاكرة
    ├── تحسين أداء الشبكة
    ├── تحسين استخدام المعالج
    └── تحسين استخدام القرص

# سكريبت الإيقاف المحسن
stop-platform.ps1
├── المعاملات:
│   ├── -Force (إيقاف قسري)
│   ├── -Graceful (إيقاف تدريجي)
│   ├── -KeepDatabase (الاحتفاظ بقواعد البيانات)
│   ├── -KeepCache (الاحتفاظ بالتخزين المؤقت)
│   ├── -SaveState (حفظ حالة النظام)
│   └── -Backup (إنشاء نسخة احتياطية)
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب الصحيح
│   ├── تنظيف الموارد والذاكرة
│   ├── حفظ السجلات والتقارير
│   ├── إنشاء نسخ احتياطية
│   ├── تقرير الحالة النهائية
│   └── تنظيف الملفات المؤقتة

# سكريبت الاختبار الشامل
test-platform.ps1
├── المعاملات:
│   ├── -TestType (unit/integration/performance/security)
│   ├── -DatabaseOnly (اختبار قواعد البيانات فقط)
│   ├── -ServicesOnly (اختبار الخدمات فقط)
│   ├── -FullTest (اختبار شامل)
│   ├── -LoadTest (اختبار الأحمال)
│   ├── -SecurityTest (اختبار الأمان)
│   └── -GenerateReport (إنشاء تقرير مفصل)
├── الاختبارات:
│   ├── اختبار اتصال قواعد البيانات
│   ├── اختبار صحة الخدمات
│   ├── اختبار APIs والواجهات
│   ├── اختبار التكامل بين الخدمات
│   ├── اختبار
);

-- جدول ربط السائقين بالمركبات
CREATE TABLE driver_vehicle_assignments (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT REFERENCES drivers(id) ON DELETE CASCADE,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- جدول صيانة المركبات
CREATE TABLE vehicle_maintenance (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL, -- ROUTINE, REPAIR, INSPECTION
    description TEXT NOT NULL,
    cost DECIMAL(10, 2),
    service_provider VARCHAR(100),
    scheduled_date DATE,
    completed_date DATE,
    next_service_date DATE,
    odometer_at_service DECIMAL(10, 2),
    status VARCHAR(20) DEFAULT 'SCHEDULED', -- SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول استهلاك الوقود
CREATE TABLE fuel_consumption (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id BIGINT REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id BIGINT REFERENCES drivers(id),
    fuel_amount DECIMAL(8, 2) NOT NULL, -- liters
    cost DECIMAL(10, 2) NOT NULL,
    odometer_reading DECIMAL(10, 2),
    fuel_station VARCHAR(100),
    refuel_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### هـ) قاعدة بيانات المدفوعات (tecnodrive_payments)
```sql
-- جدول المحافظ الرقمية
CREATE TABLE wallets (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(12, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'YER', -- Yemeni Rial
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, SUSPENDED, FROZEN
    daily_limit DECIMAL(12, 2),
    monthly_limit DECIMAL(12, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    wallet_id BIGINT REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- CREDIT, DEBIT, TRANSFER
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    description TEXT,
    reference_id VARCHAR(100), -- Reference to ride, parcel, etc.
    reference_type VARCHAR(50), -- RIDE_PAYMENT, PARCEL_PAYMENT, TOP_UP, etc.
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED, CANCELLED
    payment_method VARCHAR(50), -- WALLET, CARD, BANK_TRANSFER, CASH
    external_transaction_id VARCHAR(100),
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طرق الدفع
CREATE TABLE payment_methods (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL, -- CREDIT_CARD, DEBIT_CARD, BANK_ACCOUNT, MOBILE_WALLET
    provider VARCHAR(50), -- VISA, MASTERCARD, PAYPAL, etc.
    last_four_digits VARCHAR(4),
    expiry_month INTEGER,
    expiry_year INTEGER,
    cardholder_name VARCHAR(100),
    is_default BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    token VARCHAR(255), -- Tokenized card details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- RIDE, PARCEL, SUBSCRIPTION
    service_id BIGINT NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_amount DECIMAL(12, 2) DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, CANCELLED
    due_date DATE,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عمولات السائقين
CREATE TABLE driver_earnings (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT NOT NULL,
    ride_id BIGINT,
    gross_amount DECIMAL(12, 2) NOT NULL,
    commission_rate DECIMAL(5, 4) NOT NULL, -- e.g., 0.15 for 15%
    commission_amount DECIMAL(12, 2) NOT NULL,
    net_amount DECIMAL(12, 2) NOT NULL,
    bonus_amount DECIMAL(12, 2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, WITHHELD
    payout_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 🔗 العلاقات بين قواعد البيانات

```
علاقات البيانات عبر الخدمات:
├── auth-service ↔ user-service
│   └── users.id → user_profiles.auth_user_id
├── user-service ↔ ride-service
│   └── user_profiles.id → rides.passenger_id
├── user-service ↔ fleet-service
│   └── user_profiles.id → drivers.user_id
├── ride-service ↔ payment-service
│   └── rides.id → transactions.reference_id
├── fleet-service ↔ ride-service
│   └── drivers.id → rides.driver_id
│   └── vehicles.id → rides.vehicle_id
└── notification-service ↔ All Services
    └── Event-driven notifications
```

---

## 🚀 تدفق البيانات والعمليات

### 1. 🔄 دورة حياة الرحلة الكاملة

```
1. طلب الرحلة (Ride Request):
   ┌─ passenger-app ─┐
   │                 │
   ▼                 ▼
   api-gateway → ride-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service → fleet-service
   │                 │
   ▼                 ▼
   notification-service

2. مطابقة السائق (Driver Matching):
   ride-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → analytics-service
   │                 │
   ▼                 ▼
   notification-service

3. تتبع الرحلة (Live Tracking):
   driver-app → location-service
   │                 │
   ▼                 ▼
   ride-service → passenger-app
   │                 │
   ▼                 ▼
   analytics-service

4. إكمال الرحلة (Ride Completion):
   ride-service → payment-service
   │                 │
   ▼                 ▼
   user-service ← notification-service
   │                 │
   ▼                 ▼
   analytics-service
```

### 2. 📦 دورة حياة توصيل الطرود

```
1. إنشاء طلب التوصيل:
   customer-app → parcel-service
   │                 │
   ▼                 ▼
   user-service ← location-service
   │                 │
   ▼                 ▼
   payment-service

2. تخصيص السائق:
   parcel-service → fleet-service
   │                 │
   ▼                 ▼
   location-service → notification-service

3. تتبع الطرد:
   parcel-service ↔ location-service
   │                 │
   ▼                 ▼
   customer-app ← driver-app

4. تسليم الطرد:
   parcel-service → payment-service
   │                 │
   ▼                 ▼
   notification-service → analytics-service
```

---

## 🎛️ الواجهات الأمامية التفصيلية

### 1. 📱 تطبيق الركاب (Passenger App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والمصادقة
├── 📍 تحديد المواقع والوجهات
├── 🚗 طلب الرحلات المختلفة
├── 💰 إدارة المحفظة والمدفوعات
├── 📊 تتبع الرحلات في الوقت الفعلي
├── ⭐ تقييم السائقين
├── 📱 الإشعارات الفورية
└── 📈 تاريخ الرحلات والفواتير

الشاشات الرئيسية:
├── شاشة الترحيب والتسجيل
├── الخريطة الرئيسية
├── اختيار نوع الرحلة
├── تأكيد الحجز
├── تتبع الرحلة
├── الدفع والتقييم
├── الملف الشخصي
└── الإعدادات
```

### 2. 🚗 تطبيق السائقين (Driver App)
```
التقنيات: React Native + Redux + Socket.io
الميزات الرئيسية:
├── 🔐 تسجيل الدخول والتحقق
├── 🟢 تبديل حالة الاتصال (Online/Offline)
├── 📍 تتبع الموقع التلقائي
├── 🔔 استقبال طلبات الرحلات
├── 🗺️ التنقل والمسارات
├── 💰 تتبع الأرباح
├── ⭐ تقييم الركاب
├── 📊 إحصائيات الأداء
└── 🚗 إدارة المركبة

الشاشات الرئيسية:
├── لوحة التحكم الرئيسية
├── طلبات الرحلات الواردة
├── تفاصيل الرحلة
├── التنقل والخريطة
├── الأرباح والمدفوعات
├── الملف الشخصي
├── إعدادات المركبة
└── الدعم الفني
```

### 3. 💼 لوحة تحكم الإدارة (Admin Dashboard)
```
التقنيات: React + Material-UI + Chart.js
الوحدات الرئيسية:
├── 📊 لوحة المعلومات الرئيسية
│   ├── إحصائيات الوقت الفعلي
│   ├── مؤشرات الأداء الرئيسية
│   ├── الرسوم البيانية التفاعلية
│   └── التنبيهات والإشعارات
├── 👥 إدارة المستخدمين
│   ├── الركاب والسائقين
│   ├── التحقق من الهوية
│   ├── إدارة الأدوار والصلاحيات
│   └── سجل النشاطات
├── 🚗 إدارة الأسطول
│   ├── المركبات والسائقين
│   ├── جدولة الصيانة
│   ├── تتبع الأداء
│   └── إدارة التراخيص
├── 💰 إدارة المالية
│   ├── المدفوعات والفواتير
│   ├── أرباح السائقين
│   ├── التقارير المالية
│   └── إعدادات الأسعار
├── 📦 إدارة الطرود
│   ├── طلبات التوصيل
│   ├── تتبع الشحنات
│   ├── إدارة المستودعات
│   └── تقارير التوصيل
├── 📊 التحليلات والتقارير
│   ├── تحليل البيانات
│   ├── التنبؤات الذكية
│   ├── تقارير مخصصة
│   └── تصدير البيانات
└── ⚙️ إعدادات النظام
    ├── التكوين العام
    ├── إدارة الإشعارات
    ├── النسخ الاحتياطية
    └── سجلات النظام
```

### 4. 🏢 لوحة تحكم المشغلين (Operator Dashboard)
```
التقنيات: Angular + PrimeNG + D3.js
الوحدات المتخصصة:
├── 🎯 مراقبة العمليات
│   ├── الرحلات النشطة
│   ├── حالة السائقين
│   ├── طوارئ ومشاكل
│   └── تدخل سريع
├── 📍 مراقبة المواقع
│   ├── خريطة تفاعلية
│   ├── تتبع المركبات
│   ├── تحليل المناطق
│   └── إدارة المسارات
├── 🔔 مركز الإشعارات
│   ├── التنبيهات الفورية
│   ├── إدارة الطوارئ
│   ├── تواصل مع السائقين
│   └── دعم العملاء
├── 📊 تقارير تشغيلية
│   ├── أداء الخدمة
│   ├── معدلات الاستجابة
│   ├── رضا العملاء
│   └── كفاءة الأسطول
└── ⚡ إدارة الطوارئ
    ├── بروتوكولات الأمان
    ├── تتبع الحوادث
    ├── تنسيق الإنقاذ
    └── تقارير الحوادث
```

---

## 🔧 الأدوات والسكريبتات

### 1. 🚀 سكريبتات التشغيل
```powershell
# سكريبت التشغيل الرئيسي
start-platform.ps1
├── المعاملات:
│   ├── -Mode (development/production/minimal)
│   ├── -SkipDatabase
│   ├── -SkipMicroservices
│   ├── -SkipFrontend
│   └── -SkipComprehensive
├── الوظائف:
│   ├── فحص المتطلبات
│   ├── تشغيل قواعد البيانات
│   ├── تشغيل الخدمات المصغرة
│   ├── تشغيل الواجهات الأمامية
│   └── مراقبة الحالة

# سكريبت الإيقاف
stop-platform.ps1
├── المعاملات:
│   ├── -Force
│   ├── -KeepDatabase
│   └── -KeepCache
├── الوظائف:
│   ├── إيقاف الخدمات بالترتيب
│   ├── تنظيف الموارد
│   ├── حفظ السجلات
│   └── تقرير الحالة النهائية

# سكريبت الاختبار
test-platform.ps1
├── المعاملات:
│   ├── -DatabaseOnly
│   ├── -ServicesOnly
│   └── -FullTest
├── الاختبارات:
│   ├── اتصال قواعد البيانات
│   ├── صحة الخدمات
│   ├── اختبار APIs
│   └── اختبار التكامل
```

### 2. 🛠️ أدوات التطوير
```
tools/
├── generators/
│   ├── service-generator.js      # مولد الخدمات المصغرة
│   ├── api-generator.js          # مولد APIs
│   ├── database-generator.js     # مولد مخططات قواعد البيانات
│   └── frontend-generator.js     # مولد مكونات الواجهة
├── testing/
│   ├── load-testing/            # اختبارات الحمولة
│   ├── integration-testing/     # اختبارات التكامل
│   ├── performance-testing/     # اختبارات الأداء
│   └── security-testing/        # اختبارات الأمان
├── monitoring/
│   ├── health-check.js          # فحص صحة النظام
│   ├── performance-monitor.js   # مراقبة الأداء
│   ├── log-analyzer.js          # تحليل السجلات
│   └── alert-manager.js         # إدارة التنبيهات
└── deployment/
    ├── docker-builder.js        # بناء صور Docker
    ├── k8s-deployer.js          # نشر على Kubernetes
    ├── backup-manager.js        # إدارة النسخ الاحتياطية
    └── migration-runner.js      # تشغيل ترحيل البيانات
```

---

## 🔒 الأمان والحماية

### 1. 🛡️ طبقات الأمان
```
طبقات الحماية المتعددة:
├── 🌐 طبقة الشبكة
│   ├── HTTPS/TLS 1.3
│   ├── WAF (Web Application Firewall)
│   ├── DDoS Protection
│   └── IP Whitelisting
├── 🔐 طبقة المصادقة
│   ├── JWT + Refresh Tokens
│   ├── OAuth2 / OpenID Connect
│   ├── Multi-Factor Authentication
│   └── Biometric Authentication
├── 🔑 طبقة التفويض
│   ├── Role-Based Access Control (RBAC)
│   ├── Attribute-Based Access Control (ABAC)
│   ├── API Rate Limiting
│   └── Resource-Level Permissions
├── 💾 طبقة البيانات
│   ├── Database Encryption at Rest
│   ├── Field-Level Encryption
│   ├── Data Masking
│   └── Audit Logging
└── 🔍 طبقة المراقبة
    ├── Security Information and Event Management (SIEM)
    ├── Intrusion Detection System (IDS)
    ├── Vulnerability Scanning
    └── Penetration Testing
```

### 2. 🔐 إدارة المفاتيح والأسرار
```
إدارة الأسرار:
├── HashiCorp Vault
│   ├── تشفير المفاتيح
│   ├── دوران المفاتيح التلقائي
│   ├── إدارة الشهادات
│   └── سياسات الوصول
├── Kubernetes Secrets
│   ├── أسرار قواعد البيانات
│   ├── مفاتيح APIs الخارجية
│   ├── شهادات TLS
│   └── متغيرات البيئة الحساسة
└── Environment-Specific Configs
    ├── Development Environment
    ├── Staging Environment
    ├── Production Environment
    └── Disaster Recovery Environment
```

---

## 📈 المراقبة والتحليلات

### 1. 📊 نظام المراقبة الشامل
```
مكونات المراقبة:
├── 📈 Prometheus + Grafana
│   ├── مقاييس الأداء
│   ├── استخدام الموارد
│   ├── معدلات الاستجابة
│   └── لوحات مراقبة تفاعلية
├── 📋 ELK Stack (Elasticsearch + Logstash + Kibana)
│   ├── جمع السجلات المركزي
│   ├── تحليل السجلات
│   ├── البحث في السجلات
│   └── تصور البيانات
├── 🔍 Jaeger (Distributed Tracing)
│   ├── تتبع الطلبات عبر الخدمات
│   ├── تحليل زمن الاستجابة
│   ├── اكتشاف الاختناقات
│   └── تحليل الأخطاء
└── 🚨 AlertManager
    ├── تنبيهات الأداء
    ├── تنبيهات الأمان
    ├── تنبيهات الأخطاء
    └── تصعيد التنبيهات
```

### 2. 🤖 الذكاء الاصطناعي والتحليلات
```
وحدات الذكاء الاصطناعي:
├── 🧠 Machine Learning Models
│   ├── تنبؤ الطلب
│   ├── تحسين المسارات
│   ├── تحليل السلوك
│   └── كشف الاحتيال
├── 📊 Real-time Analytics
│   ├── تحليل البيانات الفورية
│   ├── مؤشرات الأداء المباشرة
│   ├── تحليل الاتجاهات
│   └── التنبؤات قصيرة المدى
├── 🔍 Business Intelligence
│   ├── تقارير تنفيذية
│   ├── تحليل الربحية
│   ├── تحليل العملاء
│   └── تحليل السوق
└── 🎯 Recommendation Engine
    ├── توصيات للركاب
    ├── تحسين توزيع السائقين
    ├── تحسين الأسعار
    └── تحسين الخدمات
```

---

## 🚀 خطة التطوير والتوسع

### 1. 📅 خارطة الطريق (Roadmap)
```
المراحل القادمة:
├── 🎯 المرحلة 1 (Q1 2024): الإطلاق المحلي
│   ├── إطلاق في صنعاء وعدن
│   ├── 100 سائق و 1000 مستخدم
│   ├── خدمات الرحلات الأساسية
│   └── دعم اللغة العربية الكامل
├── 🎯 المرحلة 2 (Q2 2024): التوسع الوطني
│   ├── تغطية جميع المحافظات اليمنية
│   ├── 500 سائق و 10,000 مستخدم
│   ├── خدمات توصيل الطرود
│   └── تكامل مع البنوك المحلية
├── 🎯 المرحلة 3 (Q3 2024): الميزات المتقدمة
│   ├── الذكاء الاصطناعي والتحليلات
│   ├── خدمات الأسطول للشركات
│   ├── تطبيق الويب الكامل
│   └── APIs للمطورين الخارجيين
└── 🎯 المرحلة 4 (Q4 2024): التوسع الإقليمي
    ├── دول الخليج العربي
    ├── 5,000 سائق و 100,000 مستخدم
    ├── خدمات متعددة العملات
    └── شراكات استراتيجية
```

### 2. 🔧 التحسينات التقنية المستقبلية
```
التطويرات المخططة:
├── 🌐 تقنيات الجيل القادم
│   ├── 5G Integration
│   ├── Edge Computing
│   ├── Blockchain للشفافية
│   └── IoT للمركبات الذكية
├── 🤖 الذكاء الاصطناعي المتقدم
│   ├── Computer Vision للأمان
│   ├── Natural Language Processing
│   ├── Predictive Maintenance
│   └── Autonomous Vehicle Support
├── 🔒 أمان محسن
│   ├── Zero Trust Architecture
│   ├── Quantum-Safe Cryptography
│   ├── Advanced Threat Detection
│   └── Privacy-Preserving Analytics
└── 🚀 أداء محسن
    ├── Serverless Architecture
    ├── GraphQL APIs
    ├── Advanced Caching
    └── Global CDN
```

---

## 📋 الخلاصة والتوصيات

### ✅ نقاط القوة الحالية
1. **معمارية قابلة للتوسع**: تصميم microservices متقدم
2. **تغطية شاملة**: جميع جوانب النقل والتوصيل
3. **تقنيات حديثة**: استخدام أحدث التقنيات والأدوات
4. **أمان متقدم**: طبقات حماية متعددة
5. **مراقبة شاملة**: نظام مراقبة متكامل

### 🔧 التحسينات المقترحة
1. **تحسين الأداء**: تحسين استعلامات قواعد البيانات
2. **تطوير الاختبارات**: زيادة تغطية الاختبارات التلقائية
3. **تحسين التوثيق**: توثيق أكثر تفصيلاً للمطورين
4. **تحسين UX**: تحسين تجربة المستخدم في التطبيقات
5. **تحسين DevOps**: أتمتة أكثر لعمليات النشر

### 🎯 الأولويات القادمة
1. **إكمال الاختبارات**: اختبارات شاملة لجميع المكونات
2. **تحسين الأمان**: مراجعة أمنية شاملة
3. **تحسين الأداء**: تحسين أداء النظام تحت الأحمال العالية
4. **التوثيق**: إكمال التوثيق الفني والمستخدم
5. **التدريب**: تدريب الفريق على النظام الجديد

---

*تم إعداد هذا التحليل بناءً على فحص شامل لملفات المشروع في `D:\tecno-drive-platform`*
*آخر تحديث: $(date)*

