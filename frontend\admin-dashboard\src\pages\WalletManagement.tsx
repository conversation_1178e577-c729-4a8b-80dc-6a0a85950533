import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  // Grid, // سيتم استيراده بشكل منفصل
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Avatar,
  Badge,
  CircularProgress
} from '@mui/material';
import Grid from '@mui/material/Unstable_Grid2'; // استخدام Grid v2 لحل مشاكل التوافق
import {
  AccountBalanceWallet as WalletIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  SwapHoriz as TransferIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  CreditCard as CardIcon,
  AccountBalance as BankIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

interface Wallet {
  id: string;
  userId: string;
  userType: string;
  userName: string;
  balance: number;
  pendingBalance: number;
  totalBalance: number;
  currency: string;
  status: string;
  createdAt: string;
  lastTransactionAt: string;
  dailyLimit: number;
  monthlyLimit: number;
  dailySpent: number;
  monthlySpent: number;
  isVerified: boolean;
  creditScore: number;
  loyaltyPoints: number;
}

interface WalletStatistics {
  totalWallets: number;
  activeWallets: number;
  totalBalance: number;
  averageBalance: number;
  totalTransactions: number;
  totalTransactionVolume: number;
  pendingTopUps: number;
  pendingWithdrawals: number;
  userTypeDistribution: Record<string, number>;
  monthlyGrowth: number;
}

interface Transaction {
  id: string;
  type: string;
  category: string;
  amount: number;
  fee: number;
  netAmount: number;
  status: string;
  description: string;
  createdAt: string;
  completedAt?: string;
}

const WalletManagement: React.FC = () => {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [statistics, setStatistics] = useState<WalletStatistics | null>(null);
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);
  const [walletTransactions, setWalletTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filters
  const [filters, setFilters] = useState({
    userType: '',
    status: '',
    search: '',
    minBalance: '',
    maxBalance: ''
  });

  useEffect(() => {
    loadWallets();
    loadStatistics();
  }, [filters]);

  const loadWallets = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(`http://localhost:8099/api/wallets?${queryParams}`);
      const data = await response.json();
      
      if (data.success) {
        setWallets(data.data);
      }
    } catch (error) {
      console.error('Failed to load wallets:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('http://localhost:8099/api/wallets/statistics');
      const data = await response.json();
      
      if (data.success) {
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  const loadWalletTransactions = async (walletId: string) => {
    try {
      const response = await fetch(`http://localhost:8099/api/wallets/${walletId}/transactions?limit=20`);
      const data = await response.json();
      
      if (data.success) {
        setWalletTransactions(data.data);
      }
    } catch (error) {
      console.error('Failed to load wallet transactions:', error);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleViewDetails = async (wallet: Wallet) => {
    setSelectedWallet(wallet);
    await loadWalletTransactions(wallet.id);
    setDetailsDialogOpen(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'active': 'success',
      'suspended': 'error',
      'pending_verification': 'warning'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'active': 'نشط',
      'suspended': 'موقوف',
      'pending_verification': 'في انتظار التحقق'
    };
    return labels[status] || status;
  };

  const getUserTypeLabel = (userType: string) => {
    const labels: Record<string, string> = {
      'driver': 'سائق',
      'customer': 'عميل',
      'corporate': 'شركة'
    };
    return labels[userType] || userType;
  };

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'driver': return <PersonIcon />;
      case 'customer': return <PersonIcon />;
      case 'corporate': return <BusinessIcon />;
      default: return <PersonIcon />;
    }
  };

  const getTransactionTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'credit': 'إيداع',
      'debit': 'سحب',
      'transfer': 'تحويل',
      'refund': 'استرداد',
      'fee': 'رسوم',
      'bonus': 'مكافأة',
      'cashback': 'استرداد نقدي'
    };
    return labels[type] || type;
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'credit': return 'success.main';
      case 'debit': return 'error.main';
      case 'transfer': return 'info.main';
      case 'refund': return 'warning.main';
      default: return 'text.primary';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل المحافظ...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <WalletIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          إدارة المحافظ الإلكترونية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة للمحافظ والمعاملات المالية
        </Typography>
      </Box>

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WalletIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{statistics.totalWallets}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المحافظ
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      نشط: {statistics.activeWallets}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <BankIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(statistics.totalBalance)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الأرصدة
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      متوسط: {formatCurrency(statistics.averageBalance)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TransferIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatNumber(statistics.totalTransactions)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المعاملات
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      حجم: {formatCurrency(statistics.totalTransactionVolume)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{statistics.pendingTopUps + statistics.pendingWithdrawals}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      طلبات معلقة
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      شحن: {statistics.pendingTopUps} | سحب: {statistics.pendingWithdrawals}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              placeholder="البحث..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>نوع المستخدم</InputLabel>
              <Select
                value={filters.userType}
                onChange={(e) => handleFilterChange('userType', e.target.value)}
                label="نوع المستخدم"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="driver">سائق</MenuItem>
                <MenuItem value="customer">عميل</MenuItem>
                <MenuItem value="corporate">شركة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="suspended">موقوف</MenuItem>
                <MenuItem value="pending_verification">في انتظار التحقق</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="number"
              placeholder="الحد الأدنى للرصيد"
              value={filters.minBalance}
              onChange={(e) => handleFilterChange('minBalance', e.target.value)}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="number"
              placeholder="الحد الأقصى للرصيد"
              value={filters.maxBalance}
              onChange={(e) => handleFilterChange('maxBalance', e.target.value)}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadWallets}
            >
              تحديث
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Wallets Table */}
      <Paper>
        {loading && <LinearProgress />}
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>المحفظة</TableCell>
                <TableCell>نوع المستخدم</TableCell>
                <TableCell>الرصيد المتاح</TableCell>
                <TableCell>الرصيد المعلق</TableCell>
                <TableCell>إجمالي الرصيد</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>التحقق</TableCell>
                <TableCell>آخر معاملة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {wallets
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((wallet) => (
                <TableRow key={wallet.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                        {getUserTypeIcon(wallet.userType)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {wallet.userName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {wallet.id}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getUserTypeLabel(wallet.userType)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold" color="success.main">
                      {formatCurrency(wallet.balance)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="warning.main">
                      {formatCurrency(wallet.pendingBalance)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {formatCurrency(wallet.totalBalance)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(wallet.status)}
                      color={getStatusColor(wallet.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {wallet.isVerified ? (
                      <Chip label="محقق" color="success" size="small" />
                    ) : (
                      <Chip label="غير محقق" color="warning" size="small" />
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(wallet.lastTransactionAt).toLocaleDateString('ar-SA')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <IconButton size="small" onClick={() => handleViewDetails(wallet)}>
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small">
                        <AddIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          component="div"
          count={wallets.length}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
        />
      </Paper>

      {/* Wallet Details Dialog */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>تفاصيل المحفظة</DialogTitle>
        <DialogContent>
          {selectedWallet && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {/* Wallet Info */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>معلومات المحفظة</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">اسم المستخدم</Typography>
                        <Typography variant="body1">{selectedWallet.userName}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">نوع المستخدم</Typography>
                        <Typography variant="body1">{getUserTypeLabel(selectedWallet.userType)}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">الرصيد المتاح</Typography>
                        <Typography variant="body1" color="success.main">
                          {formatCurrency(selectedWallet.balance)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">الرصيد المعلق</Typography>
                        <Typography variant="body1" color="warning.main">
                          {formatCurrency(selectedWallet.pendingBalance)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">الحد اليومي</Typography>
                        <Typography variant="body1">{formatCurrency(selectedWallet.dailyLimit)}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">المصروف اليومي</Typography>
                        <Typography variant="body1">{formatCurrency(selectedWallet.dailySpent)}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">نقاط الولاء</Typography>
                        <Typography variant="body1">{selectedWallet.loyaltyPoints}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2">درجة الائتمان</Typography>
                        <Typography variant="body1">{selectedWallet.creditScore}</Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Recent Transactions */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>المعاملات الأخيرة</Typography>
                    <TableContainer sx={{ maxHeight: 300 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>النوع</TableCell>
                            <TableCell>المبلغ</TableCell>
                            <TableCell>الحالة</TableCell>
                            <TableCell>التاريخ</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {walletTransactions.slice(0, 10).map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>
                                <Typography variant="body2">
                                  {getTransactionTypeLabel(transaction.type)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography 
                                  variant="body2" 
                                  color={getTransactionColor(transaction.type)}
                                  fontWeight="bold"
                                >
                                  {transaction.type === 'credit' ? '+' : '-'}
                                  {formatCurrency(transaction.amount)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={transaction.status}
                                  size="small"
                                  color={transaction.status === 'completed' ? 'success' : 'warning'}
                                />
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {new Date(transaction.createdAt).toLocaleDateString('ar-SA')}
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>إغلاق</Button>
          <Button variant="contained" startIcon={<AddIcon />}>
            شحن المحفظة
          </Button>
          <Button variant="outlined" startIcon={<RemoveIcon />}>
            سحب من المحفظة
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default WalletManagement;
