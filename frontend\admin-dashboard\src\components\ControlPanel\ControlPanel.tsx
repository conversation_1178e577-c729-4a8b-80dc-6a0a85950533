import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  LinearProgress,
} from '@mui/material';
import {
  Settings,
  Power,
  Refresh,
  Security,
  Storage,
  NetworkCheck,
  Speed,
  Memory,
  CloudDone,
  Warning,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';

interface SystemService {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'error';
  port?: number;
  url?: string;
  description: string;
  critical: boolean;
}

interface SystemMetric {
  id: string;
  name: string;
  value: number;
  max: number;
  unit: string;
  status: 'good' | 'warning' | 'critical';
  icon: React.ReactNode;
}

const ControlPanel: React.FC = () => {
  const [services, setServices] = useState<SystemService[]>([
    {
      id: 'postgres',
      name: 'PostgreSQL Database',
      status: 'running',
      port: 5432,
      description: 'قاعدة البيانات الرئيسية',
      critical: true,
    },
    {
      id: 'redis',
      name: '<PERSON><PERSON>',
      status: 'running',
      port: 6379,
      description: 'نظام التخزين المؤقت',
      critical: true,
    },
    {
      id: 'eureka',
      name: 'Eureka Server',
      status: 'running',
      port: 8761,
      url: 'http://localhost:8761',
      description: 'خدمة اكتشاف الخدمات',
      critical: true,
    },
    {
      id: 'auth',
      name: 'Auth Service',
      status: 'running',
      port: 8081,
      url: 'http://localhost:8081',
      description: 'خدمة المصادقة والتفويض',
      critical: true,
    },
    {
      id: 'api-gateway',
      name: 'API Gateway',
      status: 'running',
      port: 8080,
      url: 'http://localhost:8080',
      description: 'بوابة API الرئيسية',
      critical: true,
    },
    {
      id: 'prometheus',
      name: 'Prometheus',
      status: 'stopped',
      port: 9090,
      url: 'http://localhost:9090',
      description: 'نظام المراقبة',
      critical: false,
    },
    {
      id: 'grafana',
      name: 'Grafana',
      status: 'stopped',
      port: 3001,
      url: 'http://localhost:3001',
      description: 'لوحة التحليلات',
      critical: false,
    },
  ]);

  const [metrics, setMetrics] = useState<SystemMetric[]>([
    {
      id: 'cpu',
      name: 'استخدام المعالج',
      value: 45,
      max: 100,
      unit: '%',
      status: 'good',
      icon: <Speed />,
    },
    {
      id: 'memory',
      name: 'استخدام الذاكرة',
      value: 68,
      max: 100,
      unit: '%',
      status: 'warning',
      icon: <Memory />,
    },
    {
      id: 'storage',
      name: 'مساحة التخزين',
      value: 35,
      max: 100,
      unit: '%',
      status: 'good',
      icon: <Storage />,
    },
    {
      id: 'network',
      name: 'حالة الشبكة',
      value: 98,
      max: 100,
      unit: '%',
      status: 'good',
      icon: <NetworkCheck />,
    },
  ]);

  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return '#48bb78';
      case 'stopped': return '#ed8936';
      case 'error': return '#f56565';
      default: return '#718096';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle />;
      case 'stopped': return <Warning />;
      case 'error': return <Error />;
      default: return <Info />;
    }
  };

  const getMetricStatus = (value: number, max: number) => {
    const percentage = (value / max) * 100;
    if (percentage > 80) return 'critical';
    if (percentage > 60) return 'warning';
    return 'good';
  };

  const refreshServices = async () => {
    // Simulate service status check
    setServices(prev => prev.map(service => ({
      ...service,
      status: Math.random() > 0.1 ? 'running' : 'stopped',
    })));
    setLastUpdate(new Date());
  };

  const refreshMetrics = () => {
    setMetrics(prev => prev.map(metric => ({
      ...metric,
      value: Math.max(0, Math.min(metric.max, metric.value + (Math.random() - 0.5) * 10)),
      status: getMetricStatus(metric.value, metric.max),
    })));
  };

  const toggleService = (serviceId: string) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId 
        ? { ...service, status: service.status === 'running' ? 'stopped' : 'running' }
        : service
    ));
  };

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        refreshServices();
        refreshMetrics();
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const runningServices = services.filter(s => s.status === 'running').length;
  const criticalServices = services.filter(s => s.critical && s.status !== 'running').length;

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Header */}
      <Card className="enhanced-card gradient-primary" sx={{ mb: 3, color: 'white' }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  background: 'rgba(255, 255, 255, 0.2)',
                  mr: 3,
                  fontSize: '2rem',
                }}
              >
                🎛️
              </Avatar>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 800, mb: 1 }}>
                  لوحة التحكم الرئيسية
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9 }}>
                  إدارة ومراقبة جميع خدمات النظام
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    sx={{
                      '& .MuiSwitch-thumb': {
                        background: 'white',
                      },
                    }}
                  />
                }
                label="تحديث تلقائي"
                sx={{ color: 'white' }}
              />
              <Tooltip title="تحديث البيانات">
                <IconButton
                  onClick={() => {
                    refreshServices();
                    refreshMetrics();
                  }}
                  sx={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    '&:hover': {
                      background: 'rgba(255, 255, 255, 0.3)',
                    },
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          <Box sx={{ mt: 3, display: 'flex', gap: 4 }}>
            <Box>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                الخدمات النشطة
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                {runningServices}/{services.length}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                آخر تحديث
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {lastUpdate.toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Alerts */}
      {criticalServices > 0 && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={refreshServices}>
              إعادة فحص
            </Button>
          }
        >
          تحذير: {criticalServices} من الخدمات الحرجة متوقفة
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* System Metrics */}
        <Grid item xs={12} md={6}>
          <Card className="enhanced-card">
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ background: '#4299e1', mr: 2 }}>
                  📊
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  مؤشرات النظام
                </Typography>
              </Box>
              
              {metrics.map((metric) => (
                <Box key={metric.id} sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          background: `${getStatusColor(metric.status)}20`,
                          color: getStatusColor(metric.status),
                          mr: 1.5,
                        }}
                      >
                        {metric.icon}
                      </Avatar>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {metric.name}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {metric.value.toFixed(0)}{metric.unit}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(metric.value / metric.max) * 100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: `${getStatusColor(metric.status)}20`,
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        background: getStatusColor(metric.status),
                      },
                    }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Services Control */}
        <Grid item xs={12} md={6}>
          <Card className="enhanced-card">
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ background: '#48bb78', mr: 2 }}>
                  ⚙️
                </Avatar>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  إدارة الخدمات
                </Typography>
              </Box>
              
              {services.map((service, index) => (
                <Box key={service.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                      <Avatar
                        sx={{
                          width: 40,
                          height: 40,
                          background: `${getStatusColor(service.status)}20`,
                          color: getStatusColor(service.status),
                          mr: 2,
                        }}
                      >
                        {getStatusIcon(service.status)}
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {service.name}
                          </Typography>
                          {service.critical && (
                            <Chip
                              label="حرج"
                              size="small"
                              sx={{
                                background: '#f56565',
                                color: 'white',
                                fontSize: '0.7rem',
                              }}
                            />
                          )}
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {service.description}
                          {service.port && ` - Port ${service.port}`}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {service.url && (
                        <Tooltip title="فتح الخدمة">
                          <IconButton
                            size="small"
                            onClick={() => window.open(service.url, '_blank')}
                            sx={{ color: '#4299e1' }}
                          >
                            🔗
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title={service.status === 'running' ? 'إيقاف' : 'تشغيل'}>
                        <IconButton
                          size="small"
                          onClick={() => toggleService(service.id)}
                          sx={{ color: getStatusColor(service.status) }}
                        >
                          <Power />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  {index < services.length - 1 && <Divider />}
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ControlPanel;
