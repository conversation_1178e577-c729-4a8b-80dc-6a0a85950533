package com.tecnodrive.walletservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Wallet Transaction Entity
 */
@Entity
@Table(name = "wallet_transactions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class WalletTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "wallet_id", nullable = false)
    private UUID walletId;

    @Column(name = "transaction_reference", nullable = false, unique = true, length = 100)
    private String transactionReference;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private TransactionType type;

    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;

    @Column(name = "currency", nullable = false, length = 3)
    @Builder.Default
    private String currency = "SAR";

    @Column(name = "balance_before", nullable = false, precision = 10, scale = 2)
    private BigDecimal balanceBefore;

    @Column(name = "balance_after", nullable = false, precision = 10, scale = 2)
    private BigDecimal balanceAfter;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private TransactionStatus status = TransactionStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    private TransactionSource source;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "reference_id", length = 100)
    private String referenceId; // Ride ID, Parcel ID, etc.

    @Column(name = "reference_type", length = 50)
    private String referenceType; // RIDE, PARCEL, TOPUP, etc.

    @Column(name = "agent_id")
    private UUID agentId; // For cash top-ups by agents

    @Column(name = "agent_name", length = 100)
    private String agentName;

    @Column(name = "payment_method", length = 50)
    private String paymentMethod; // CASH, CARD, BANK_TRANSFER, etc.

    @Column(name = "external_transaction_id", length = 100)
    private String externalTransactionId;

    @Column(name = "fees", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal fees = BigDecimal.ZERO;

    @Column(name = "tax", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal tax = BigDecimal.ZERO;

    @Column(name = "net_amount", precision = 10, scale = 2)
    private BigDecimal netAmount;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    @Column(name = "failed_reason", length = 500)
    private String failedReason;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata; // JSON string for additional data

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * Transaction Type Enum
     */
    public enum TransactionType {
        CREDIT,     // Money added to wallet
        DEBIT       // Money deducted from wallet
    }

    /**
     * Transaction Status Enum
     */
    public enum TransactionStatus {
        PENDING,
        COMPLETED,
        FAILED,
        CANCELLED,
        REFUNDED
    }

    /**
     * Transaction Source Enum
     */
    public enum TransactionSource {
        CASH_TOPUP,         // Cash top-up by agent
        CARD_TOPUP,         // Credit/Debit card top-up
        BANK_TRANSFER,      // Bank transfer
        RIDE_PAYMENT,       // Payment for ride
        PARCEL_PAYMENT,     // Payment for parcel delivery
        REFUND,             // Refund transaction
        BONUS,              // Bonus/Promotional credit
        PENALTY,            // Penalty deduction
        ADMIN_ADJUSTMENT,   // Manual adjustment by admin
        TRANSFER_IN,        // Transfer from another wallet
        TRANSFER_OUT        // Transfer to another wallet
    }

    /**
     * Check if transaction is completed
     */
    public boolean isCompleted() {
        return status == TransactionStatus.COMPLETED;
    }

    /**
     * Check if transaction is pending
     */
    public boolean isPending() {
        return status == TransactionStatus.PENDING;
    }

    /**
     * Check if transaction failed
     */
    public boolean isFailed() {
        return status == TransactionStatus.FAILED;
    }

    /**
     * Check if transaction is a credit (money in)
     */
    public boolean isCredit() {
        return type == TransactionType.CREDIT;
    }

    /**
     * Check if transaction is a debit (money out)
     */
    public boolean isDebit() {
        return type == TransactionType.DEBIT;
    }

    /**
     * Mark transaction as completed
     */
    public void markCompleted() {
        this.status = TransactionStatus.COMPLETED;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * Mark transaction as failed
     */
    public void markFailed(String reason) {
        this.status = TransactionStatus.FAILED;
        this.failedReason = reason;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * Calculate net amount (amount - fees - tax)
     */
    public void calculateNetAmount() {
        this.netAmount = amount.subtract(fees).subtract(tax);
    }

    /**
     * Generate transaction reference
     */
    public static String generateReference(TransactionSource source) {
        String prefix = switch (source) {
            case CASH_TOPUP -> "CT";
            case CARD_TOPUP -> "CD";
            case BANK_TRANSFER -> "BT";
            case RIDE_PAYMENT -> "RP";
            case PARCEL_PAYMENT -> "PP";
            case REFUND -> "RF";
            case BONUS -> "BN";
            case PENALTY -> "PN";
            case ADMIN_ADJUSTMENT -> "AA";
            case TRANSFER_IN -> "TI";
            case TRANSFER_OUT -> "TO";
        };
        
        return prefix + System.currentTimeMillis() + 
               String.format("%04d", (int)(Math.random() * 10000));
    }
}
