package com.tecnodrive.rideservice.repository;

import com.tecnodrive.rideservice.entity.VehicleType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Vehicle Type Repository
 */
@Repository
public interface VehicleTypeRepository extends JpaRepository<VehicleType, UUID> {

    /**
     * Find vehicle type by name
     */
    Optional<VehicleType> findByName(String name);

    /**
     * Find active vehicle type by name
     */
    Optional<VehicleType> findByNameAndIsActiveTrue(String name);

    /**
     * Find all active vehicle types
     */
    List<VehicleType> findByIsActiveTrue();

    /**
     * Find vehicle types ordered by base fare
     */
    List<VehicleType> findByIsActiveTrueOrderByBaseFareAsc();
}
