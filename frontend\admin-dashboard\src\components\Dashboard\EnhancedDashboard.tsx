import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  useTheme,
  alpha,
  Skeleton,
} from '@mui/material';
import {
  DirectionsCar as RidesIcon,
  People as UsersIcon,
  AttachMoney as RevenueIcon,
  LocalShipping as FleetIcon,
  TrendingUp as TrendingUpIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
} from '@mui/icons-material';

// Enhanced Components
import TecnoCard from '../UI/Card/TecnoCard';
import StatsCard from '../UI/Stats/StatsCard';
import TecnoButton from '../UI/Button/TecnoButton';

const EnhancedDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setDashboardData({
        stats: {
          totalRides: 1247,
          activeUsers: 892,
          totalRevenue: 45680,
          activeFleet: 156,
        },
        trends: {
          rides: 12.5,
          users: 8.3,
          revenue: 15.7,
          fleet: 5.2,
        },
        recentActivities: [
          { id: 1, type: 'ride', message: 'رحلة جديدة من الرياض إلى جدة', time: 'منذ 5 دقائق' },
          { id: 2, type: 'user', message: 'مستخدم جديد انضم للمنصة', time: 'منذ 10 دقائق' },
          { id: 3, type: 'payment', message: 'دفعة بقيمة 250 ريال تمت بنجاح', time: 'منذ 15 دقيقة' },
        ],
        systemHealth: {
          status: 'healthy',
          uptime: '99.9%',
          responseTime: '120ms',
        },
      });
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getWelcomeCardStyles = () => ({
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    color: 'white',
    position: 'relative' as const,
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      right: 0,
      width: '200px',
      height: '200px',
      background: `radial-gradient(circle, ${alpha('#ffffff', 0.1)} 0%, transparent 70%)`,
      transform: 'translate(50%, -50%)',
    },
  });

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
          لوحة المعلومات
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 2 }} />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          لوحة المعلومات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          نظرة شاملة على أداء منصة TECNO DRIVE
        </Typography>
      </Box>

      {/* Welcome Card */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <TecnoCard variant="gradient">
            <Box sx={getWelcomeCardStyles()}>
              <Box sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                  مرحباً بك في TECNO DRIVE
                </Typography>
                <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                  إدارة شاملة ومتطورة لمنصة النقل الذكي
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <TecnoButton
                    variant="outline"
                    size="medium"
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': {
                        backgroundColor: alpha('#ffffff', 0.1),
                        borderColor: 'white',
                      },
                    }}
                  >
                    عرض التقارير
                  </TecnoButton>
                  <TecnoButton
                    variant="ghost"
                    size="medium"
                    sx={{
                      color: 'white',
                      '&:hover': {
                        backgroundColor: alpha('#ffffff', 0.1),
                      },
                    }}
                  >
                    إعدادات النظام
                  </TecnoButton>
                </Box>
              </Box>
            </Box>
          </TecnoCard>
        </Grid>
      </Grid>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="إجمالي الرحلات"
            value={dashboardData.stats.totalRides.toLocaleString()}
            subtitle="رحلة مكتملة"
            icon={<RidesIcon />}
            trend={{
              value: dashboardData.trends.rides,
              label: 'من الشهر الماضي',
              period: 'شهرياً',
            }}
            color="primary"
            variant="default"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="المستخدمين النشطين"
            value={dashboardData.stats.activeUsers.toLocaleString()}
            subtitle="مستخدم نشط"
            icon={<UsersIcon />}
            trend={{
              value: dashboardData.trends.users,
              label: 'من الشهر الماضي',
              period: 'شهرياً',
            }}
            color="success"
            variant="default"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="إجمالي الإيرادات"
            value={`${dashboardData.stats.totalRevenue.toLocaleString()} ريال`}
            subtitle="إيرادات هذا الشهر"
            icon={<RevenueIcon />}
            trend={{
              value: dashboardData.trends.revenue,
              label: 'من الشهر الماضي',
              period: 'شهرياً',
            }}
            color="warning"
            variant="default"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="الأسطول النشط"
            value={dashboardData.stats.activeFleet.toLocaleString()}
            subtitle="مركبة نشطة"
            icon={<FleetIcon />}
            trend={{
              value: dashboardData.trends.fleet,
              label: 'من الشهر الماضي',
              period: 'شهرياً',
            }}
            color="info"
            variant="default"
            progress={{
              value: 78,
              label: 'معدل الاستخدام',
            }}
          />
        </Grid>
      </Grid>

      {/* Content Cards */}
      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={8}>
          <TecnoCard
            title="الأنشطة الأخيرة"
            subtitle="آخر العمليات في النظام"
            variant="elevated"
          >
            <Box>
              {dashboardData.recentActivities.map((activity: any, index: number) => (
                <Box
                  key={activity.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    borderRadius: 1,
                    backgroundColor: index % 2 === 0 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
                    mb: index < dashboardData.recentActivities.length - 1 ? 1 : 0,
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    {activity.type === 'ride' && <RidesIcon color="primary" />}
                    {activity.type === 'user' && <UsersIcon color="primary" />}
                    {activity.type === 'payment' && <RevenueIcon color="primary" />}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {activity.message}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {activity.time}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <TecnoButton variant="ghost" size="small">
                عرض جميع الأنشطة
              </TecnoButton>
            </Box>
          </TecnoCard>
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={4}>
          <TecnoCard
            title="صحة النظام"
            subtitle="حالة الخوادم والخدمات"
            variant="elevated"
            status="success"
          >
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <SuccessIcon
                sx={{
                  fontSize: 48,
                  color: theme.palette.success.main,
                  mb: 2,
                }}
              />
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                النظام يعمل بشكل طبيعي
              </Typography>
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">وقت التشغيل</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {dashboardData.systemHealth.uptime}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">زمن الاستجابة</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {dashboardData.systemHealth.responseTime}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <TecnoButton variant="outline" size="small" fullWidth>
              عرض التفاصيل
            </TecnoButton>
          </TecnoCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EnhancedDashboard;
