package com.tecnodrive.rideservice.repository;

import com.tecnodrive.rideservice.entity.Ride;
import com.tecnodrive.rideservice.entity.RideStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Ride Repository
 */
@Repository
public interface RideRepository extends JpaRepository<Ride, UUID> {

    /**
     * Find rides by passenger ID
     */
    List<Ride> findByPassengerIdOrderByRequestedAtDesc(UUID passengerId);

    /**
     * Find rides by driver ID
     */
    List<Ride> findByDriverIdOrderByRequestedAtDesc(UUID driverId);

    /**
     * Find rides by status
     */
    List<Ride> findByStatus(RideStatus status);

    /**
     * Find rides by passenger and status
     */
    List<Ride> findByPassengerIdAndStatus(UUID passengerId, RideStatus status);

    /**
     * Find rides by driver and status
     */
    List<Ride> findByDriverIdAndStatus(UUID driverId, RideStatus status);

    /**
     * Find active rides for passenger
     */
    @Query("SELECT r FROM Ride r WHERE r.passengerId = :passengerId AND r.status IN ('REQUESTED', 'DRIVER_ASSIGNED', 'DRIVER_ARRIVED', 'IN_PROGRESS')")
    Optional<Ride> findActiveRideByPassenger(@Param("passengerId") UUID passengerId);

    /**
     * Find active rides for driver
     */
    @Query("SELECT r FROM Ride r WHERE r.driverId = :driverId AND r.status IN ('DRIVER_ASSIGNED', 'DRIVER_ARRIVED', 'IN_PROGRESS')")
    Optional<Ride> findActiveRideByDriver(@Param("driverId") UUID driverId);

    /**
     * Find requested rides (waiting for driver assignment)
     */
    @Query("SELECT r FROM Ride r WHERE r.status = 'REQUESTED' ORDER BY r.requestedAt ASC")
    List<Ride> findRequestedRides();

    /**
     * Find rides within time range
     */
    @Query("SELECT r FROM Ride r WHERE r.requestedAt BETWEEN :startTime AND :endTime")
    List<Ride> findRidesBetween(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * Find rides by company ID
     */
    List<Ride> findByCompanyIdOrderByRequestedAtDesc(UUID companyId);

    /**
     * Find rides by school ID
     */
    List<Ride> findBySchoolIdOrderByRequestedAtDesc(UUID schoolId);

    /**
     * Find rides with pagination
     */
    Page<Ride> findByPassengerIdOrderByRequestedAtDesc(UUID passengerId, Pageable pageable);

    /**
     * Count rides by status
     */
    long countByStatus(RideStatus status);

    /**
     * Count rides by passenger
     */
    long countByPassengerId(UUID passengerId);

    /**
     * Count rides by driver
     */
    long countByDriverId(UUID driverId);

    /**
     * Find rides near pickup location using PostGIS
     */
    @Query(value = "SELECT * FROM rides r WHERE ST_DWithin(r.pickup_location, ST_SetSRID(ST_MakePoint(:longitude, :latitude), 4326), :radiusMeters) AND r.status = 'REQUESTED'", 
           nativeQuery = true)
    List<Ride> findRidesNearLocation(@Param("latitude") double latitude, 
                                    @Param("longitude") double longitude, 
                                    @Param("radiusMeters") double radiusMeters);

    /**
     * Calculate distance between two points
     */
    @Query(value = "SELECT ST_Distance(ST_SetSRID(ST_MakePoint(:lon1, :lat1), 4326), ST_SetSRID(ST_MakePoint(:lon2, :lat2), 4326))", 
           nativeQuery = true)
    Double calculateDistance(@Param("lat1") double lat1, @Param("lon1") double lon1,
                           @Param("lat2") double lat2, @Param("lon2") double lon2);

    /**
     * Find completed rides for analytics
     */
    @Query("SELECT r FROM Ride r WHERE r.status = 'COMPLETED' AND r.completedAt BETWEEN :startDate AND :endDate")
    List<Ride> findCompletedRidesBetween(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Find cancelled rides for analytics
     */
    @Query("SELECT r FROM Ride r WHERE r.status IN ('CANCELLED_BY_PASSENGER', 'CANCELLED_BY_DRIVER', 'CANCELLED_BY_SYSTEM') AND r.cancelledAt BETWEEN :startDate AND :endDate")
    List<Ride> findCancelledRidesBetween(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Calculate average rating for driver
     */
    @Query("SELECT AVG(r.ratingByPassenger) FROM Ride r WHERE r.driverId = :driverId AND r.ratingByPassenger IS NOT NULL")
    Double calculateAverageDriverRating(@Param("driverId") UUID driverId);

    /**
     * Calculate average rating for passenger
     */
    @Query("SELECT AVG(r.ratingByDriver) FROM Ride r WHERE r.passengerId = :passengerId AND r.ratingByDriver IS NOT NULL")
    Double calculateAveragePassengerRating(@Param("passengerId") UUID passengerId);

    /**
     * Find rides requiring driver assignment (older than X minutes)
     */
    @Query("SELECT r FROM Ride r WHERE r.status = 'REQUESTED' AND r.requestedAt < :cutoffTime")
    List<Ride> findRidesRequiringDriverAssignment(@Param("cutoffTime") Instant cutoffTime);

    /**
     * Find scheduled rides for today
     */
    @Query("SELECT r FROM Ride r WHERE r.scheduledAt BETWEEN :startOfDay AND :endOfDay AND r.status = 'REQUESTED'")
    List<Ride> findScheduledRidesForToday(@Param("startOfDay") Instant startOfDay, @Param("endOfDay") Instant endOfDay);
}
