package com.tecnodrive.tenantservice.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Tenant Entity for Multi-Tenant SaaS Platform
 * Each tenant represents a separate customer organization
 */
@Entity
@Table(name = "tenants")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Tenant {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    @NotBlank(message = "Tenant identifier is required")
    private String tenantId;

    @Column(nullable = false)
    @NotBlank(message = "Company name is required")
    private String companyName;

    @Column(nullable = false)
    @NotBlank(message = "Contact name is required")
    private String contactName;

    @Email(message = "Valid email is required")
    @Column(nullable = false)
    private String contactEmail;

    @Column(nullable = false)
    private String contactPhone;

    @Column(nullable = false)
    private String address;

    @Column(nullable = false)
    private String city;

    @Column(nullable = false)
    private String country;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @NotNull(message = "Subscription plan is required")
    private SubscriptionPlan subscriptionPlan;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private TenantStatus status = TenantStatus.PENDING;

    @Column(nullable = false)
    private String databaseName;

    @Column(nullable = false)
    private String databaseUrl;

    @ElementCollection(fetch = FetchType.EAGER)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "tenant_features", joinColumns = @JoinColumn(name = "tenant_id"))
    @Column(name = "feature")
    private Set<TenantFeature> enabledFeatures;

    @Column(columnDefinition = "TEXT")
    private String customConfiguration;

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    private LocalDateTime lastAccessedAt;

    private LocalDateTime subscriptionExpiresAt;

    @Column(nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(nullable = false)
    @Builder.Default
    private Integer maxUsers = 10;

    @Column(nullable = false)
    @Builder.Default
    private Integer maxVehicles = 5;

    @Column(nullable = false)
    @Builder.Default
    private Integer maxDrivers = 10;

    // Subscription Plan Enum
    public enum SubscriptionPlan {
        BASIC("Basic Plan - Up to 5 vehicles"),
        STANDARD("Standard Plan - Up to 25 vehicles"),
        PREMIUM("Premium Plan - Up to 100 vehicles"),
        ENTERPRISE("Enterprise Plan - Unlimited vehicles");

        private final String description;

        SubscriptionPlan(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Tenant Status Enum
    public enum TenantStatus {
        PENDING("Pending activation"),
        ACTIVE("Active and operational"),
        SUSPENDED("Temporarily suspended"),
        EXPIRED("Subscription expired"),
        CANCELLED("Cancelled by customer");

        private final String description;

        TenantStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Tenant Features Enum
    public enum TenantFeature {
        RIDE_BOOKING("Ride booking and management"),
        PARCEL_DELIVERY("Parcel delivery service"),
        FLEET_MANAGEMENT("Fleet management tools"),
        REAL_TIME_TRACKING("Real-time GPS tracking"),
        ANALYTICS_REPORTING("Analytics and reporting"),
        MOBILE_APPS("Mobile applications"),
        API_ACCESS("API access"),
        CUSTOM_BRANDING("Custom branding"),
        MULTI_LANGUAGE("Multi-language support"),
        ADVANCED_ROUTING("Advanced routing algorithms"),
        DRIVER_MANAGEMENT("Driver management"),
        CUSTOMER_SUPPORT("Customer support tools"),
        BILLING_INVOICING("Billing and invoicing"),
        WALLET_PAYMENTS("Digital wallet and payments"),
        MAINTENANCE_TRACKING("Vehicle maintenance tracking"),
        HR_MANAGEMENT("HR management tools"),
        WAREHOUSE_MANAGEMENT("Warehouse management"),
        RISK_COMPLIANCE("Risk and compliance tools");

        private final String description;

        TenantFeature(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Helper methods
    public boolean hasFeature(TenantFeature feature) {
        return enabledFeatures != null && enabledFeatures.contains(feature);
    }

    public boolean isSubscriptionExpired() {
        return subscriptionExpiresAt != null && subscriptionExpiresAt.isBefore(LocalDateTime.now());
    }

    public boolean isOperational() {
        return status == TenantStatus.ACTIVE && isActive && !isSubscriptionExpired();
    }
}
