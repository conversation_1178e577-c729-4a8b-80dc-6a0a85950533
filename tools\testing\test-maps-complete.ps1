# Complete Maps Testing Script
Write-Host "🗺️ Complete Maps Testing - TecnoDrive Platform" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Test Map Service APIs
Write-Host "`n🔍 Testing Map Service APIs..." -ForegroundColor Cyan

$endpoints = @(
    @{ Name = "Map Configuration"; Url = "http://localhost:8085/api/map/config/enhanced" },
    @{ Name = "Tile Servers"; Url = "http://localhost:8085/api/map/tiles" },
    @{ Name = "Vehicle Data"; Url = "http://localhost:8085/api/map/vehicles" },
    @{ Name = "Location Stats"; Url = "http://localhost:8085/api/locations/stats" },
    @{ Name = "Health Check"; Url = "http://localhost:8085/health" }
)

$apiResults = @{}

foreach ($endpoint in $endpoints) {
    Write-Host "`n🔗 Testing: $($endpoint.Name)" -ForegroundColor Yellow
    Write-Host "URL: $($endpoint.Url)" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $endpoint.Url -Method GET -TimeoutSec 10
        
        Write-Host "✅ Success!" -ForegroundColor Green
        
        if ($response.success -ne $null) {
            Write-Host "📊 Success: $($response.success)" -ForegroundColor Green
            
            if ($response.data) {
                if ($endpoint.Name -eq "Map Configuration") {
                    Write-Host "🗺️ Default Provider: $($response.data.defaultProvider)" -ForegroundColor Cyan
                    Write-Host "🎯 Default Center: $($response.data.defaultCenter.lat), $($response.data.defaultCenter.lng)" -ForegroundColor Cyan
                    Write-Host "📐 Zoom Range: $($response.data.minZoom) - $($response.data.maxZoom)" -ForegroundColor Cyan
                    Write-Host "🔧 Providers: $($response.data.providers.Count)" -ForegroundColor Cyan
                }
                elseif ($endpoint.Name -eq "Vehicle Data") {
                    Write-Host "🚗 Total Vehicles: $($response.data.Count)" -ForegroundColor Cyan
                    if ($response.data.Count -gt 0) {
                        $activeVehicles = ($response.data | Where-Object { $_.status -eq "active" }).Count
                        Write-Host "🟢 Active Vehicles: $activeVehicles" -ForegroundColor Green
                    }
                }
                elseif ($endpoint.Name -eq "Location Stats") {
                    Write-Host "📊 Total Vehicles: $($response.data.totalVehicles)" -ForegroundColor Cyan
                    Write-Host "🟢 Active: $($response.data.activeVehicles)" -ForegroundColor Green
                    Write-Host "🟡 Idle: $($response.data.idleVehicles)" -ForegroundColor Yellow
                    Write-Host "⚡ Avg Speed: $($response.data.averageSpeed) km/h" -ForegroundColor Cyan
                }
            }
        } else {
            Write-Host "📄 Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Yellow
        }
        
        $apiResults[$endpoint.Name] = $true
        
    } catch {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        $apiResults[$endpoint.Name] = $false
    }
}

# Test Frontend
Write-Host "`n🌐 Testing Frontend..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "✅ Frontend: Ready (Status: $($frontendResponse.StatusCode))" -ForegroundColor Green
    $frontendWorking = $true
} catch {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
    $frontendWorking = $false
}

# Check Leaflet dependencies
Write-Host "`n📦 Checking Map Dependencies..." -ForegroundColor Cyan
$packageJsonPath = "frontend/admin-dashboard/package.json"
if (Test-Path $packageJsonPath) {
    $packageJson = Get-Content $packageJsonPath | ConvertFrom-Json
    
    $mapDependencies = @("leaflet", "react-leaflet")
    foreach ($dep in $mapDependencies) {
        if ($packageJson.dependencies.$dep) {
            Write-Host "✅ $dep: $($packageJson.dependencies.$dep)" -ForegroundColor Green
        } else {
            Write-Host "❌ $dep: Not installed" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ package.json not found" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Test Summary" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green

Write-Host "`n🔗 API Endpoints:" -ForegroundColor Cyan
foreach ($result in $apiResults.GetEnumerator()) {
    $status = if ($result.Value) { "✅ Working" } else { "❌ Failed" }
    $color = if ($result.Value) { "Green" } else { "Red" }
    Write-Host "   - $($result.Key): $status" -ForegroundColor $color
}

Write-Host "`n🌐 Frontend Status:" -ForegroundColor Cyan
$frontendStatus = if ($frontendWorking) { "✅ Working" } else { "❌ Failed" }
$frontendColor = if ($frontendWorking) { "Green" } else { "Red" }
Write-Host "   - React App: $frontendStatus" -ForegroundColor $frontendColor

# Test URLs
Write-Host "`n🔗 Test URLs:" -ForegroundColor Yellow
Write-Host "==============" -ForegroundColor Yellow
Write-Host "🗺️ Map Test Page: http://localhost:3000/map/test" -ForegroundColor White
Write-Host "🗺️ Simple Map: http://localhost:3000/map" -ForegroundColor White
Write-Host "🗺️ Street Map: http://localhost:3000/map/street" -ForegroundColor White
Write-Host "🚗 Fleet Map: http://localhost:3000/fleet/map" -ForegroundColor White

# API Test URLs
Write-Host "`n🔗 API Test URLs:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow
Write-Host "📊 Map Config: http://localhost:8085/api/map/config/enhanced" -ForegroundColor White
Write-Host "🗺️ Tile Servers: http://localhost:8085/api/map/tiles" -ForegroundColor White
Write-Host "🚗 Vehicles: http://localhost:8085/api/map/vehicles" -ForegroundColor White
Write-Host "📈 Stats: http://localhost:8085/api/locations/stats" -ForegroundColor White
Write-Host "🏥 Health: http://localhost:8085/health" -ForegroundColor White

# Recommendations
Write-Host "`n💡 Recommendations:" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow

$workingApis = ($apiResults.Values | Where-Object { $_ -eq $true }).Count
$totalApis = $apiResults.Count

if ($workingApis -eq $totalApis) {
    Write-Host "🎉 All APIs are working! Maps should function perfectly." -ForegroundColor Green
    Write-Host "🔗 Go to: http://localhost:3000/map/test" -ForegroundColor Cyan
} elseif ($workingApis -gt 0) {
    Write-Host "⚠️ Some APIs are working. Maps may have limited functionality." -ForegroundColor Yellow
    Write-Host "🔧 Check failed endpoints and restart Map Service if needed." -ForegroundColor Yellow
} else {
    Write-Host "❌ No APIs are working. Map Service needs to be started." -ForegroundColor Red
    Write-Host "🔧 Run: powershell -ExecutionPolicy Bypass -File map-service.ps1" -ForegroundColor Yellow
}

if ($frontendWorking) {
    Write-Host "✅ Frontend is ready for map testing." -ForegroundColor Green
} else {
    Write-Host "❌ Frontend needs to be started first." -ForegroundColor Red
    Write-Host "🔧 Run: cd frontend/admin-dashboard && npm start" -ForegroundColor Yellow
}

# Next Steps
Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "1. 🌐 Open: http://localhost:3000/map/test" -ForegroundColor White
Write-Host "2. 🔍 Check API status indicators" -ForegroundColor White
Write-Host "3. 🗺️ Test different map providers" -ForegroundColor White
Write-Host "4. 🚗 Click on vehicle markers" -ForegroundColor White
Write-Host "5. 🎛️ Try map controls (zoom, layers, etc.)" -ForegroundColor White
Write-Host "6. 📱 Test satellite view toggle" -ForegroundColor White

Write-Host "`n🎯 Expected Results:" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "✅ Interactive map loads successfully" -ForegroundColor White
Write-Host "✅ Vehicle markers appear on map" -ForegroundColor White
Write-Host "✅ Map controls work (zoom, pan, layers)" -ForegroundColor White
Write-Host "✅ Different tile providers can be selected" -ForegroundColor White
Write-Host "✅ Vehicle popups show detailed information" -ForegroundColor White
Write-Host "✅ Real-time updates work" -ForegroundColor White

Write-Host "`n🎉 Maps Testing Complete!" -ForegroundColor Green
