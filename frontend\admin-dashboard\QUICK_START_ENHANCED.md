# 🚀 TECNO DRIVE - Quick Start Guide للتحسينات الجديدة

## ⚡ **البدء السريع**

### 1. **تثبيت المتطلبات**
```bash
cd tecno-drive/frontend/admin-dashboard
npm install
```

### 2. **تشغيل التطبيق**
```bash
npm start
```

### 3. **الوصول للميزات الجديدة**
افتح المتصفح وانتقل إلى:
- **http://localhost:3000/risk** - إدارة المخاطر
- **http://localhost:3000/crm** - إدارة علاقات العملاء  
- **http://localhost:3000/maintenance** - إدارة الصيانة
- **http://localhost:3000/monitoring/enhanced** - المراقبة المحسنة

## 🎯 **الميزات الجديدة المتاحة**

### 🛡️ **إدارة المخاطر**
```typescript
// مثال على استخدام خدمة إدارة المخاطر
import riskManagementService from './services/riskManagementService';

// الاشتراك في أحداث المخاطر المباشرة
riskManagementService.subscribeToRiskEvents('tenant-id', (event) => {
  console.log('New risk event:', event);
});

// إنشاء حدث مخاطر جديد
const riskEvent = await riskManagementService.createRiskEvent({
  tenantId: 'tenant-id',
  riskType: 'security',
  severity: 'high',
  title: 'Suspicious Activity Detected',
  description: 'Multiple failed login attempts detected'
});
```

### 👥 **إدارة علاقات العملاء**
```typescript
// مثال على استخدام خدمة CRM
import crmService from './services/crmService';

// إنشاء تذكرة دعم جديدة
const ticket = await crmService.createSupportTicket({
  tenantId: 'tenant-id',
  customerId: 'customer-id',
  title: 'Payment Issue',
  description: 'Customer unable to process payment',
  category: 'billing',
  priority: 'high'
});

// تحليل المشاعر للنص
const sentiment = await crmService.analyzeSentiment(
  'I am very disappointed with the service'
);
console.log('Sentiment:', sentiment); // { score: -0.8, label: 'negative' }
```

### 🔧 **إدارة الصيانة**
```typescript
// مثال على استخدام خدمة الصيانة
import maintenanceService from './services/maintenanceService';

// جدولة صيانة جديدة
const schedule = await maintenanceService.createMaintenanceSchedule({
  tenantId: 'tenant-id',
  vehicleId: 'vehicle-id',
  type: 'preventive',
  title: 'Regular Oil Change',
  dueDate: '2024-02-15',
  priority: 'medium'
});

// الحصول على تنبيهات الصيانة التنبؤية
const alerts = await maintenanceService.getPredictiveAlerts('tenant-id');
```

### 📊 **المراقبة المحسنة**
```typescript
// مثال على استخدام خدمة المراقبة المحسنة
import realTimeDashboardService from './services/realTimeDashboardService';

// الاشتراك في مقاييس الأداء
const unsubscribe = realTimeDashboardService.subscribe('performance', (metrics) => {
  console.log('Performance metrics:', metrics);
});

// إرسال أمر (CQRS Pattern)
await realTimeDashboardService.sendCommand({
  type: 'UpdateVehicleLocation',
  aggregateId: 'vehicle-123',
  payload: { lat: 40.7128, lng: -74.0060 },
  userId: 'user-id',
  tenantId: 'tenant-id'
});
```

## 🛠️ **استخدام المكونات المشتركة**

### **Circuit Breaker**
```tsx
import CircuitBreaker from './components/Common/CircuitBreaker';

function MyComponent() {
  return (
    <CircuitBreaker
      maxFailures={5}
      resetTimeout={60000}
      onError={(error) => console.error('Circuit breaker:', error)}
      fallback={<div>Service temporarily unavailable</div>}
    >
      <YourMainComponent />
    </CircuitBreaker>
  );
}
```

### **Rate Limiter**
```tsx
import RateLimiter from './components/Common/RateLimiter';

function MyApp() {
  return (
    <RateLimiter
      maxRequests={100}
      windowMs={60000}
      showStatus={true}
      onLimitExceeded={() => alert('Rate limit exceeded')}
    >
      <YourAppContent />
    </RateLimiter>
  );
}
```

### **Enhanced WebSocket Hook**
```tsx
import useEnhancedWebSocket from './hooks/useEnhancedWebSocket';

function RealTimeComponent() {
  const {
    socket,
    state,
    subscribe,
    emit,
    emitWithAck
  } = useEnhancedWebSocket({
    url: 'ws://localhost:8080',
    namespace: 'dashboard',
    autoConnect: true
  });

  useEffect(() => {
    const unsubscribe = subscribe('live-event', (data) => {
      console.log('Live event received:', data);
    });

    return unsubscribe;
  }, [subscribe]);

  const sendMessage = async () => {
    try {
      const response = await emitWithAck('get-data', { type: 'metrics' });
      console.log('Response:', response);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <div>
      <p>Connection Status: {state.connected ? 'Connected' : 'Disconnected'}</p>
      <p>Latency: {state.latency}ms</p>
      <button onClick={sendMessage}>Send Message</button>
    </div>
  );
}
```

## 🎨 **تخصيص الواجهة**

### **تخصيص ألوان المخاطر**
```tsx
// في ملف theme.ts
export const riskTheme = {
  colors: {
    critical: '#d32f2f',
    high: '#f57c00',
    medium: '#1976d2',
    low: '#388e3c'
  }
};
```

### **تخصيص ألوان CRM**
```tsx
export const crmTheme = {
  segments: {
    vip: '#e91e63',
    premium: '#ff9800',
    enterprise: '#3f51b5',
    regular: '#9e9e9e'
  }
};
```

## 📱 **الاستجابة للأجهزة المختلفة**

جميع المكونات الجديدة مصممة لتكون متجاوبة:

```tsx
// مثال على الاستجابة في Risk Dashboard
<Grid container spacing={3}>
  <Grid item xs={12} sm={6} md={3}>
    <RiskMetricCard />
  </Grid>
  <Grid item xs={12} md={8}>
    <RiskTrendChart />
  </Grid>
  <Grid item xs={12} md={4}>
    <SecurityAlerts />
  </Grid>
</Grid>
```

## 🔄 **Real-time Updates**

### **تفعيل التحديثات المباشرة**
```tsx
// في أي مكون
useEffect(() => {
  // تفعيل التحديثات المباشرة للمخاطر
  riskManagementService.subscribeToRiskEvents(tenantId, (event) => {
    setRiskEvents(prev => [event, ...prev]);
  });

  // تفعيل التحديثات المباشرة للعملاء
  crmService.subscribeToNewCustomers(tenantId, (customer) => {
    setCustomers(prev => [customer, ...prev]);
  });

  // تفعيل التحديثات المباشرة للصيانة
  maintenanceService.subscribeToPredictiveAlerts(tenantId, (alert) => {
    setPredictiveAlerts(prev => [alert, ...prev]);
  });

  return () => {
    // تنظيف الاشتراكات عند إلغاء تحميل المكون
    riskManagementService.disconnect();
    crmService.disconnect();
    maintenanceService.disconnect();
  };
}, [tenantId]);
```

## 🧪 **اختبار الميزات**

### **اختبار إدارة المخاطر**
1. انتقل إلى `/risk`
2. انقر على "إنشاء حدث مخاطر جديد"
3. املأ البيانات واحفظ
4. راقب التحديثات المباشرة

### **اختبار CRM**
1. انتقل إلى `/crm`
2. انقر على "إنشاء تذكرة جديدة"
3. اكتب وصف المشكلة
4. راقب تحليل المشاعر التلقائي

### **اختبار الصيانة**
1. انتقل إلى `/maintenance`
2. انقر على "جدولة صيانة جديدة"
3. اختر المركبة والنوع
4. راقب التنبيهات التنبؤية

## 🚨 **استكشاف الأخطاء**

### **مشاكل الاتصال**
```bash
# تحقق من حالة الخدمات
curl http://localhost:8080/health

# تحقق من WebSocket
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: SGVsbG8sIHdvcmxkIQ==" \
     -H "Sec-WebSocket-Version: 13" \
     http://localhost:8080/socket.io/
```

### **مشاكل الأداء**
- افتح Developer Tools
- انتقل إلى Network tab
- راقب WebSocket connections
- تحقق من Console للأخطاء

### **مشاكل البيانات**
- تحقق من localStorage للمصادقة
- تأكد من صحة tenantId
- راجع Network requests للأخطاء

## 📞 **الحصول على المساعدة**

- **الوثائق الكاملة**: راجع `ENHANCED_FEATURES_README.md`
- **أمثلة الكود**: راجع مجلد `examples/`
- **API Documentation**: راجع `docs/api/`
- **GitHub Issues**: أنشئ issue للمشاكل التقنية

---

**🎉 مبروك! أصبحت جاهزاً لاستخدام جميع التحسينات الجديدة في منصة TECNO DRIVE.**
