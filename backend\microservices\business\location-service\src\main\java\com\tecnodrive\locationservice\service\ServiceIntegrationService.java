package com.tecnodrive.locationservice.service;

import com.tecnodrive.locationservice.websocket.LocationWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Service for integrating with other TecnoDrive services
 */
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class ServiceIntegrationService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceIntegrationService.class);

    @Autowired
    private LocationWebSocketHandler webSocketHandler;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${tecnodrive.services.ride-service.url:http://localhost:8082}")
    private String rideServiceUrl;

    @Value("${tecnodrive.services.user-service.url:http://localhost:8083}")
    private String userServiceUrl;

    @Value("${tecnodrive.services.fleet-service.url:http://localhost:8084}")
    private String fleetServiceUrl;

    @Value("${tecnodrive.services.payment-service.url:http://localhost:8086}")
    private String paymentServiceUrl;

    @Value("${tecnodrive.services.parcel-service.url:http://localhost:8087}")
    private String parcelServiceUrl;

    @Value("${tecnodrive.services.notification-service.url:http://localhost:8088}")
    private String notificationServiceUrl;

    @Value("${tecnodrive.services.analytics-service.url:http://localhost:8089}")
    private String analyticsServiceUrl;

    /**
     * Notify ride service about location update
     */
    public CompletableFuture<Void> notifyRideService(Map<String, Object> locationData) {
        return CompletableFuture.runAsync(() -> {
            try {
                String url = rideServiceUrl + "/api/rides/location-update";
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(locationData, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    logger.debug("Successfully notified ride service about location update");
                } else {
                    logger.warn("Failed to notify ride service: {}", response.getStatusCode());
                }
            } catch (Exception e) {
                logger.error("Error notifying ride service: {}", e.getMessage());
            }
        });
    }

    /**
     * Notify fleet service about vehicle location update
     */
    public CompletableFuture<Void> notifyFleetService(Map<String, Object> locationData) {
        return CompletableFuture.runAsync(() -> {
            try {
                String url = fleetServiceUrl + "/api/fleet/location-update";
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(locationData, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    logger.debug("Successfully notified fleet service about location update");
                } else {
                    logger.warn("Failed to notify fleet service: {}", response.getStatusCode());
                }
            } catch (Exception e) {
                logger.error("Error notifying fleet service: {}", e.getMessage());
            }
        });
    }

    /**
     * Notify analytics service about location data
     */
    public CompletableFuture<Void> notifyAnalyticsService(Map<String, Object> locationData) {
        return CompletableFuture.runAsync(() -> {
            try {
                String url = analyticsServiceUrl + "/api/analytics/location-data";
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(locationData, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    logger.debug("Successfully sent location data to analytics service");
                } else {
                    logger.warn("Failed to send data to analytics service: {}", response.getStatusCode());
                }
            } catch (Exception e) {
                logger.error("Error sending data to analytics service: {}", e.getMessage());
            }
        });
    }

    /**
     * Send notification through notification service
     */
    public CompletableFuture<Void> sendNotification(Map<String, Object> notificationData) {
        return CompletableFuture.runAsync(() -> {
            try {
                String url = notificationServiceUrl + "/api/notifications/send";
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(notificationData, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    logger.debug("Successfully sent notification");
                    // Also broadcast via WebSocket
                    webSocketHandler.broadcastNotification(notificationData);
                } else {
                    logger.warn("Failed to send notification: {}", response.getStatusCode());
                }
            } catch (Exception e) {
                logger.error("Error sending notification: {}", e.getMessage());
            }
        });
    }

    /**
     * Process location update and notify all relevant services
     */
    public void processLocationUpdate(Map<String, Object> locationData) {
        // Broadcast to WebSocket clients immediately
        webSocketHandler.broadcastLocationUpdate(locationData);

        // Notify other services asynchronously
        notifyRideService(locationData);
        notifyFleetService(locationData);
        notifyAnalyticsService(locationData);

        // Check for alerts based on location data
        checkLocationAlerts(locationData);
    }

    /**
     * Check for location-based alerts
     */
    private void checkLocationAlerts(Map<String, Object> locationData) {
        try {
            String vehicleId = (String) locationData.get("vehicleId");
            Double speed = (Double) locationData.get("speed");
            String status = (String) locationData.get("status");

            // Speed alert
            if (speed != null && speed > 120) {
                Map<String, Object> alert = Map.of(
                    "type", "speed_violation",
                    "severity", "warning",
                    "vehicleId", vehicleId,
                    "message", "Vehicle exceeding speed limit: " + speed + " km/h",
                    "timestamp", System.currentTimeMillis()
                );
                
                webSocketHandler.broadcastAlert(alert);
                sendNotification(Map.of(
                    "type", "speed_alert",
                    "targetType", "admin",
                    "message", "Speed violation detected for vehicle " + vehicleId,
                    "data", alert
                ));
            }

            // Emergency status alert
            if ("emergency".equals(status)) {
                Map<String, Object> emergencyAlert = Map.of(
                    "type", "emergency",
                    "severity", "critical",
                    "vehicleId", vehicleId,
                    "message", "Emergency status activated for vehicle " + vehicleId,
                    "location", Map.of(
                        "lat", locationData.get("lat"),
                        "lng", locationData.get("lng")
                    ),
                    "timestamp", System.currentTimeMillis()
                );
                
                webSocketHandler.broadcastEmergencyAlert(emergencyAlert);
                sendNotification(Map.of(
                    "type", "emergency_alert",
                    "targetType", "all",
                    "message", "Emergency alert: Vehicle " + vehicleId + " needs immediate assistance",
                    "data", emergencyAlert
                ));
            }

        } catch (Exception e) {
            logger.error("Error checking location alerts: {}", e.getMessage());
        }
    }

    /**
     * Get service health status
     */
    public Map<String, Object> getServiceHealthStatus() {
        Map<String, Object> healthStatus = Map.of(
            "rideService", checkServiceHealth(rideServiceUrl),
            "userService", checkServiceHealth(userServiceUrl),
            "fleetService", checkServiceHealth(fleetServiceUrl),
            "paymentService", checkServiceHealth(paymentServiceUrl),
            "parcelService", checkServiceHealth(parcelServiceUrl),
            "notificationService", checkServiceHealth(notificationServiceUrl),
            "analyticsService", checkServiceHealth(analyticsServiceUrl)
        );

        // Broadcast system status update
        webSocketHandler.broadcastSystemStatus(Map.of(
            "type", "service_health",
            "data", healthStatus,
            "timestamp", System.currentTimeMillis()
        ));

        return healthStatus;
    }

    /**
     * Check individual service health
     */
    private Map<String, Object> checkServiceHealth(String serviceUrl) {
        try {
            String healthUrl = serviceUrl + "/actuator/health";
            ResponseEntity<Map> response = restTemplate.getForEntity(healthUrl, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                return Map.of(
                    "status", body != null ? body.get("status") : "UNKNOWN",
                    "lastChecked", System.currentTimeMillis(),
                    "responseTime", "< 1000ms"
                );
            } else {
                return Map.of(
                    "status", "DOWN",
                    "lastChecked", System.currentTimeMillis(),
                    "error", "HTTP " + response.getStatusCode()
                );
            }
        } catch (Exception e) {
            return Map.of(
                "status", "DOWN",
                "lastChecked", System.currentTimeMillis(),
                "error", e.getMessage()
            );
        }
    }
}
