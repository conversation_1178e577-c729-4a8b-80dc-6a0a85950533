# 📋 دليل التثبيت والتشغيل - مولد البيانات اليمنية

## 🎯 الهدف
توليد بيانات افتراضية شاملة لمنصة TecnoDrive مخصصة للسوق اليمني

## 📦 ما تم إنشاؤه

### ملفات مولد البيانات:
- `generate_yemen_data.py` - النسخة الكاملة (200 سجل لكل جدول)
- `simple_yemen_data.py` - النسخة المبسطة (50-100 سجل)
- `sample_yemen_data.json` - عينة جاهزة للاستخدام الفوري

### ملفات التشغيل:
- `run_simple.bat` - تشغيل تلقائي (Windows)
- `run_generator.ps1` - PowerShell script
- `requirements.txt` - المكتبات المطلوبة

### ملفات التوثيق:
- `README.md` - دليل شامل
- `QUICK_START.md` - دليل البدء السريع
- `INSTALLATION_GUIDE.md` - هذا الملف

## 🚀 طرق الاستخدام

### الطريقة 1: استخدام البيانات الجاهزة (الأسرع)
```json
// استخدم ملف sample_yemen_data.json مباشرة
// يحتوي على بيانات يمنية جاهزة للاختبار
```

### الطريقة 2: تثبيت Python وتشغيل المولد

#### خطوة 1: تثبيت Python
1. اذهب إلى https://python.org/downloads/
2. حمل أحدث إصدار (Python 3.8+ مُوصى به)
3. **مهم**: تأكد من تحديد "Add Python to PATH" أثناء التثبيت
4. أعد تشغيل Command Prompt أو PowerShell

#### خطوة 2: التحقق من التثبيت
```bash
python --version
# يجب أن يظهر: Python 3.x.x
```

#### خطوة 3: تشغيل المولد
```bash
# الطريقة الأولى: النسخة المبسطة (بدون مكتبات إضافية)
python simple_yemen_data.py

# الطريقة الثانية: النسخة الكاملة (تحتاج Faker)
pip install faker
python generate_yemen_data.py

# الطريقة الثالثة: تشغيل تلقائي
.\run_simple.bat
```

## 📊 البيانات المولدة

### الجداول المتاحة:
1. **الشركات** (tenants) - شركات النقل والمؤسسات
2. **المستخدمين** (users) - الركاب والمرسلين
3. **السائقين** (drivers) - مع مواقعهم في المدن اليمنية
4. **المركبات** (vehicles) - مرتبطة بالسائقين
5. **الرحلات** (trips) - رحلات بين المدن اليمنية
6. **الطرود** (parcels) - خدمات التوصيل

### خصائص البيانات:
- ✅ أسماء عربية يمنية حقيقية
- ✅ مدن يمنية مع إحداثيات دقيقة
- ✅ أرقام هواتف يمنية (+967...)
- ✅ أسعار بالريال اليمني
- ✅ علاقات صحيحة بين الجداول
- ✅ حالات واقعية (متاح، في رحلة، مكتمل...)

## 🗺️ المدن اليمنية المدعومة
- صنعاء (15.3520, 44.2065)
- عدن (12.7937, 45.0292)
- تعز (13.5772, 44.0270)
- الحديدة (14.8068, 42.9461)
- إب (13.9667, 44.1833)
- المكلا (14.5385, 49.1238)
- مأرب (15.4485, 45.3468)
- ذمار (14.5422, 44.4079)
- سيئون (15.9380, 48.7892)
- عمران (15.6560, 43.9575)

## 🔧 استكشاف الأخطاء

### مشكلة: "python is not recognized"
**السبب**: Python غير مثبت أو غير موجود في PATH
**الحل**: 
1. ثبت Python من python.org
2. تأكد من تحديد "Add to PATH"
3. أعد تشغيل Terminal

### مشكلة: "No module named 'faker'"
**السبب**: مكتبة Faker غير مثبتة
**الحل**: 
```bash
pip install faker
# أو استخدم النسخة المبسطة:
python simple_yemen_data.py
```

### مشكلة: ترميز النص العربي
**السبب**: مشكلة في ترميز UTF-8
**الحل**: استخدم PowerShell بدلاً من CMD

## 📁 النتائج المتوقعة

بعد التشغيل الناجح، ستجد مجلد `generated_data` يحتوي على:
```
generated_data/
├── tenants_data.json      # 50-200 شركة
├── users_data.json        # 100-200 مستخدم
├── drivers_data.json      # 100-200 سائق
├── vehicles_data.json     # 100-200 مركبة
├── trips_data.json        # 200 رحلة
└── parcels_data.json      # 200 طرد
```

## 🎯 للاستخدام الفوري

إذا كنت تريد البدء فوراً بدون تثبيت:
1. استخدم `sample_yemen_data.json`
2. يحتوي على عينة من جميع أنواع البيانات
3. جاهز للاستيراد في قاعدة البيانات

## 📞 الدعم التقني

للمساعدة في:
- تثبيت Python
- حل مشاكل التشغيل  
- تخصيص البيانات
- دمج البيانات مع النظام

يرجى مراجعة الملفات المرفقة أو طلب المساعدة.
