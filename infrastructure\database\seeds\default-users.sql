-- =====================================================
-- Default Users and Test Data for TecnoDrive Platform
-- =====================================================

-- Insert default tenant for testing
INSERT INTO tenants (tenant_id, name, contact_person, contact_email, subscription_plan_id, status) 
VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'TecnoDrive Demo Company',
    '<PERSON>',
    '<EMAIL>',
    (SELECT plan_id FROM subscription_plans WHERE plan_name = 'Premium'),
    'active'
) ON CONFLICT (tenant_id) DO NOTHING;

-- Insert default admin user
INSERT INTO tenant_users (
    user_id,
    tenant_id, 
    username, 
    email, 
    password_hash, 
    first_name, 
    last_name, 
    role, 
    is_active
) VALUES (
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    'admin',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', -- password: admin123
    'Ahmed',
    'Al-Yamani',
    'admin',
    true
) ON CONFLICT (user_id) DO NOTHING;

-- Insert operator user
INSERT INTO tenant_users (
    user_id,
    tenant_id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active
) VALUES (
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440000',
    'operator',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', -- password: admin123
    'Mohammed',
    'Al-Sana\'ani',
    'operator',
    true
) ON CONFLICT (user_id) DO NOTHING;

-- Insert dispatcher user
INSERT INTO tenant_users (
    user_id,
    tenant_id,
    username,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active
) VALUES (
    '660e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440000',
    'dispatcher',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', -- password: admin123
    'Fatima',
    'Al-Hadhrami',
    'dispatcher',
    true
) ON CONFLICT (user_id) DO NOTHING;

-- Insert test customers
INSERT INTO customers (
    customer_id,
    tenant_id,
    phone_number,
    email,
    first_name,
    last_name,
    customer_type,
    is_active
) VALUES 
(
    '770e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    '+967771234567',
    '<EMAIL>',
    'Ali',
    'Al-Zahra',
    'individual',
    true
),
(
    '770e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440000',
    '+967772345678',
    '<EMAIL>',
    'Maryam',
    'Al-Mukalla',
    'individual',
    true
) ON CONFLICT (customer_id) DO NOTHING;

-- Insert test wallets for customers
INSERT INTO wallets (
    wallet_id,
    tenant_id,
    customer_id,
    wallet_number,
    balance,
    status
) VALUES 
(
    '880e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    '770e8400-e29b-41d4-a716-446655440001',
    'TDW-550E8400-1701234567890',
    5000.00,
    'active'
),
(
    '880e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440000',
    '770e8400-e29b-41d4-a716-446655440002',
    'TDW-550E8400-1701234567891',
    3000.00,
    'active'
) ON CONFLICT (wallet_id) DO NOTHING;

-- Insert test warehouse
INSERT INTO warehouses (
    warehouse_id,
    tenant_id,
    name,
    code,
    address,
    location,
    city,
    capacity_cubic_meters,
    warehouse_type,
    is_active
) VALUES (
    '990e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    'مستودع صنعاء الرئيسي',
    'SNW001',
    'شارع الزبيري، صنعاء، اليمن',
    POINT(44.2075, 15.3547),
    'صنعاء',
    1000.00,
    'standard',
    true
) ON CONFLICT (warehouse_id) DO NOTHING;

-- Update tenant settings
INSERT INTO tenant_settings (
    tenant_id,
    setting_key,
    setting_value,
    description
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440000',
    'default_currency',
    '"YER"',
    'Default currency for the tenant'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    'timezone',
    '"Asia/Aden"',
    'Default timezone for the tenant'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    'language',
    '"ar"',
    'Default language for the tenant'
),
(
    '550e8400-e29b-41d4-a716-446655440000',
    'theme_settings',
    '{"primaryColor": "#1976d2", "secondaryColor": "#dc004e", "logo": "/logos/tecnodrive-demo.png"}',
    'Theme and branding settings'
) ON CONFLICT (tenant_id, setting_key) DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_at = CURRENT_TIMESTAMP;

-- Insert audit log entry for setup
INSERT INTO audit_logs (
    tenant_id,
    user_id,
    action,
    entity_type,
    entity_id,
    new_values,
    ip_address,
    timestamp
) VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    '660e8400-e29b-41d4-a716-446655440001',
    'INITIAL_SETUP',
    'tenant',
    '550e8400-e29b-41d4-a716-446655440000',
    '{"action": "Default test data created", "users_created": 3, "customers_created": 2}',
    '127.0.0.1',
    CURRENT_TIMESTAMP
);

-- Display created credentials
DO $$
BEGIN
    RAISE NOTICE '=== TecnoDrive Test Credentials Created ===';
    RAISE NOTICE 'Tenant: TecnoDrive Demo Company';
    RAISE NOTICE 'Admin User: admin / admin123';
    RAISE NOTICE 'Operator User: operator / admin123';
    RAISE NOTICE 'Dispatcher User: dispatcher / admin123';
    RAISE NOTICE 'Login URL: http://localhost:8081/login';
    RAISE NOTICE '==========================================';
END $$;
