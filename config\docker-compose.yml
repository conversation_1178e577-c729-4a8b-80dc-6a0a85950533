services:
  # PostgreSQL Database with Enhanced Configuration
  postgres:
    image: postgres:15-alpine
    container_name: postgres-tecno
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      # Performance tuning
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: 0.9
      POSTGRES_WAL_BUFFERS: 16MB
      POSTGRES_DEFAULT_STATISTICS_TARGET: 100
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/unified-database-config.sql:/docker-entrypoint-initdb.d/01-unified-database-config.sql
      - ./database/fix-schema.sql:/docker-entrypoint-initdb.d/02-fix-schema.sql
      - ./database/backend:/docker-entrypoint-initdb.d/03-backend
      - ./database/migrations:/migrations
      - ./postgres-config/postgresql.conf:/etc/postgresql/postgresql.conf
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tecnodrive_user -d tecnodrive"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_worker_processes=8
      -c max_parallel_workers_per_gather=4
      -c max_parallel_workers=8
      -c max_parallel_maintenance_workers=4

  # Redis Cache with Enhanced Configuration
  redis:
    image: redis:7-alpine
    container_name: tecnodrive-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis-config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-TecnoDrive2025!Redis#Cache}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-TecnoDrive2025!Redis#Cache}

  # Redis Insight for monitoring
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: redis-insight-tecno
    ports:
      - "8001:8001"
    networks:
      - tecnodrive-network
    depends_on:
      - redis
    restart: unless-stopped

  # Database Backup Service
  db-backup:
    image: postgres:15-alpine
    container_name: db-backup-tecno
    environment:
      PGPASSWORD: tecnodrive_pass
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    networks:
      - tecnodrive-network
    depends_on:
      - postgres
    command: >
      sh -c "
        chmod +x /backup.sh &&
        while true; do
          /backup.sh
          sleep 86400
        done
      "
    restart: unless-stopped

  # PostgreSQL Admin (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin-tecno
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - tecnodrive-network

  # Eureka Server Mock
  eureka:
    image: nginx:alpine
    container_name: tecnodrive-eureka
    ports:
      - "8761:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Eureka Server Mock\";
          add_header Content-Type text/plain;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  # API Gateway - Spring Cloud Gateway
  api-gateway:
    build:
      context: ./services/infrastructure/api-gateway
      dockerfile: Dockerfile
    container_name: tecnodrive-api-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka/
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SPRING_MAIN_ALLOW_CIRCULAR_REFERENCES=true
    networks:
      - tecnodrive-network
    depends_on:
      - eureka
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Legacy Gateway Mock (for fallback)
  gateway-mock:
    image: nginx:alpine
    container_name: tecnodrive-gateway-mock
    ports:
      - "8081:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"TecnoDrive API Gateway Mock\";
          add_header Content-Type text/plain;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  # Microservices
  auth-service:
    image: nginx:alpine
    container_name: tecnodrive-auth
    ports:
      - "8081:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Auth Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Auth Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  user-service:
    image: nginx:alpine
    container_name: tecnodrive-user
    ports:
      - "8083:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"User Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"User Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  ride-service:
    image: nginx:alpine
    container_name: tecnodrive-ride
    ports:
      - "8082:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Ride Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Ride Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  fleet-service:
    image: nginx:alpine
    container_name: tecnodrive-fleet
    ports:
      - "8084:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Fleet Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Fleet Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  location-service:
    image: nginx:alpine
    container_name: tecnodrive-location
    ports:
      - "8085:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Location Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Location Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  payment-service:
    image: nginx:alpine
    container_name: tecnodrive-payment
    ports:
      - "8086:80"
    networks:
      - tecnodrive-network
    command: >
      sh -c "echo 'server {
        listen 80;
        location / {
          return 200 \"Payment Service Running\";
          add_header Content-Type text/plain;
        }
        location /actuator/health {
          return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Payment Service\\\"}\";
          add_header Content-Type application/json;
        }
      }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    restart: unless-stopped

  # Advanced Services
  hr-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-hr
    restart: unless-stopped
    ports:
      - "8097:8097"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8097
      - NODE_ENV=production
      - SERVICE_NAME=hr-service
    command: ["npm", "run", "hr"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8097/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  financial-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-financial
    restart: unless-stopped
    ports:
      - "8098:8098"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8098
      - NODE_ENV=production
      - SERVICE_NAME=financial-service
    command: ["npm", "run", "financial"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8098/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  wallet-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-wallet
    restart: unless-stopped
    ports:
      - "8099:8099"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8099
      - NODE_ENV=production
      - SERVICE_NAME=wallet-service
    command: ["npm", "run", "wallet"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8099/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  live-operations-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-live-ops
    restart: unless-stopped
    ports:
      - "8100:8100"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8100
      - NODE_ENV=production
      - SERVICE_NAME=live-operations-service
    command: ["npm", "run", "live-ops"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  operations-management-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-operations
    restart: unless-stopped
    ports:
      - "8101:8101"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8101
      - NODE_ENV=production
      - SERVICE_NAME=operations-management-service
    command: ["npm", "run", "operations"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  trip-tracking-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-tracking
    restart: unless-stopped
    ports:
      - "8102:8102"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8102
      - NODE_ENV=production
      - SERVICE_NAME=trip-tracking-service
    command: ["npm", "run", "tracking"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8102/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  demand-analysis-service:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: tecnodrive-demand
    restart: unless-stopped
    ports:
      - "8103:8103"
    networks:
      - tecnodrive-network
    environment:
      - PORT=8103
      - NODE_ENV=production
      - SERVICE_NAME=demand-analysis-service
    command: ["npm", "run", "demand"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8103/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
  redis_data:

networks:
  tecnodrive-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
