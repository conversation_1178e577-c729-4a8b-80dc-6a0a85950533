-- Database Partitioning Strategy for TecnoDrive Platform
-- Implements time-based and hash-based partitioning for large tables

-- =====================================================
-- 1. LOCATION DATA PARTITIONING (Time-based - Daily)
-- =====================================================

-- Create partitioned location_data table
CREATE TABLE location_data_partitioned (
    id UUID DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL,
    vehicle_id UUID,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2),
    heading DECIMAL(5, 2),
    accuracy DECIMAL(5, 2),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    metadata JSONB,
    CONSTRAINT pk_location_data_partitioned PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Create indexes on partitioned table
CREATE INDEX idx_location_data_driver_time ON location_data_partitioned (driver_id, created_at);
CREATE INDEX idx_location_data_vehicle_time ON location_data_partitioned (vehicle_id, created_at);
CREATE INDEX idx_location_data_timestamp ON location_data_partitioned (timestamp);
CREATE INDEX idx_location_data_location ON location_data_partitioned USING GIST (
    ST_Point(longitude, latitude)
);

-- Function to create daily partitions
CREATE OR REPLACE FUNCTION create_location_partition(partition_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
BEGIN
    partition_name := 'location_data_' || to_char(partition_date, 'YYYY_MM_DD');
    start_date := partition_date::TEXT;
    end_date := (partition_date + INTERVAL '1 day')::TEXT;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF location_data_partitioned
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    -- Create partition-specific indexes
    EXECUTE format('CREATE INDEX %I ON %I (driver_id, created_at)',
                   'idx_' || partition_name || '_driver_time', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I USING GIST (ST_Point(longitude, latitude))',
                   'idx_' || partition_name || '_location', partition_name);
END;
$$ LANGUAGE plpgsql;

-- Create partitions for current month and next month
DO $$
DECLARE
    current_date DATE := CURRENT_DATE;
    end_date DATE := CURRENT_DATE + INTERVAL '2 months';
BEGIN
    WHILE current_date <= end_date LOOP
        PERFORM create_location_partition(current_date);
        current_date := current_date + INTERVAL '1 day';
    END LOOP;
END $$;

-- =====================================================
-- 2. RIDES PARTITIONING (Time-based - Monthly)
-- =====================================================

CREATE TABLE rides_partitioned (
    id UUID DEFAULT gen_random_uuid(),
    passenger_id UUID NOT NULL,
    driver_id UUID,
    vehicle_id UUID,
    pickup_location POINT NOT NULL,
    dropoff_location POINT,
    pickup_address TEXT,
    dropoff_address TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'REQUESTED',
    fare DECIMAL(10, 2),
    distance DECIMAL(8, 2),
    duration INTEGER,
    requested_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    metadata JSONB,
    CONSTRAINT pk_rides_partitioned PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Function to create monthly ride partitions
CREATE OR REPLACE FUNCTION create_rides_partition(partition_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
BEGIN
    partition_name := 'rides_' || to_char(partition_date, 'YYYY_MM');
    start_date := date_trunc('month', partition_date)::TEXT;
    end_date := (date_trunc('month', partition_date) + INTERVAL '1 month')::TEXT;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF rides_partitioned
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    -- Create partition-specific indexes
    EXECUTE format('CREATE INDEX %I ON %I (passenger_id, created_at)',
                   'idx_' || partition_name || '_passenger', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (driver_id, created_at)',
                   'idx_' || partition_name || '_driver', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (status)',
                   'idx_' || partition_name || '_status', partition_name);
END;
$$ LANGUAGE plpgsql;

-- Create ride partitions for current and next 6 months
DO $$
DECLARE
    current_date DATE := date_trunc('month', CURRENT_DATE);
    end_date DATE := current_date + INTERVAL '6 months';
BEGIN
    WHILE current_date < end_date LOOP
        PERFORM create_rides_partition(current_date);
        current_date := current_date + INTERVAL '1 month';
    END LOOP;
END $$;

-- =====================================================
-- 3. PAYMENTS PARTITIONING (Time-based - Monthly)
-- =====================================================

CREATE TABLE payments_partitioned (
    id UUID DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    ride_id UUID,
    parcel_id UUID,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'YER',
    payment_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    gateway_transaction_id VARCHAR(100),
    gateway_response JSONB,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    CONSTRAINT pk_payments_partitioned PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Function to create monthly payment partitions
CREATE OR REPLACE FUNCTION create_payments_partition(partition_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
BEGIN
    partition_name := 'payments_' || to_char(partition_date, 'YYYY_MM');
    start_date := date_trunc('month', partition_date)::TEXT;
    end_date := (date_trunc('month', partition_date) + INTERVAL '1 month')::TEXT;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF payments_partitioned
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    -- Create partition-specific indexes
    EXECUTE format('CREATE INDEX %I ON %I (user_id, created_at)',
                   'idx_' || partition_name || '_user', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (status)',
                   'idx_' || partition_name || '_status', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (gateway_transaction_id)',
                   'idx_' || partition_name || '_gateway', partition_name);
END;
$$ LANGUAGE plpgsql;

-- Create payment partitions
DO $$
DECLARE
    current_date DATE := date_trunc('month', CURRENT_DATE);
    end_date DATE := current_date + INTERVAL '6 months';
BEGIN
    WHILE current_date < end_date LOOP
        PERFORM create_payments_partition(current_date);
        current_date := current_date + INTERVAL '1 month';
    END LOOP;
END $$;

-- =====================================================
-- 4. AUDIT LOG PARTITIONING (Time-based - Weekly)
-- =====================================================

CREATE TABLE audit_log_partitioned (
    id UUID DEFAULT gen_random_uuid(),
    user_id UUID,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    CONSTRAINT pk_audit_log_partitioned PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Function to create weekly audit partitions
CREATE OR REPLACE FUNCTION create_audit_partition(partition_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
    week_start DATE;
BEGIN
    week_start := date_trunc('week', partition_date);
    partition_name := 'audit_log_' || to_char(week_start, 'YYYY_WW');
    start_date := week_start::TEXT;
    end_date := (week_start + INTERVAL '1 week')::TEXT;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF audit_log_partitioned
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    -- Create partition-specific indexes
    EXECUTE format('CREATE INDEX %I ON %I (user_id, created_at)',
                   'idx_' || partition_name || '_user', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (action)',
                   'idx_' || partition_name || '_action', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (resource_type, resource_id)',
                   'idx_' || partition_name || '_resource', partition_name);
END;
$$ LANGUAGE plpgsql;

-- Create audit partitions for current and next 8 weeks
DO $$
DECLARE
    current_date DATE := date_trunc('week', CURRENT_DATE);
    end_date DATE := current_date + INTERVAL '8 weeks';
BEGIN
    WHILE current_date < end_date LOOP
        PERFORM create_audit_partition(current_date);
        current_date := current_date + INTERVAL '1 week';
    END LOOP;
END $$;

-- =====================================================
-- 5. AUTOMATIC PARTITION MANAGEMENT
-- =====================================================

-- Function to automatically create future partitions
CREATE OR REPLACE FUNCTION maintain_partitions()
RETURNS VOID AS $$
DECLARE
    future_date DATE;
BEGIN
    -- Create location partitions for next 7 days
    FOR i IN 1..7 LOOP
        future_date := CURRENT_DATE + (i || ' days')::INTERVAL;
        BEGIN
            PERFORM create_location_partition(future_date);
        EXCEPTION WHEN duplicate_table THEN
            -- Partition already exists, skip
            NULL;
        END;
    END LOOP;
    
    -- Create ride/payment partitions for next 2 months
    FOR i IN 1..2 LOOP
        future_date := date_trunc('month', CURRENT_DATE) + (i || ' months')::INTERVAL;
        BEGIN
            PERFORM create_rides_partition(future_date);
            PERFORM create_payments_partition(future_date);
        EXCEPTION WHEN duplicate_table THEN
            -- Partition already exists, skip
            NULL;
        END;
    END LOOP;
    
    -- Create audit partitions for next 4 weeks
    FOR i IN 1..4 LOOP
        future_date := date_trunc('week', CURRENT_DATE) + (i || ' weeks')::INTERVAL;
        BEGIN
            PERFORM create_audit_partition(future_date);
        EXCEPTION WHEN duplicate_table THEN
            -- Partition already exists, skip
            NULL;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule partition maintenance (run daily)
-- This would typically be set up as a cron job or scheduled task
-- SELECT cron.schedule('maintain-partitions', '0 2 * * *', 'SELECT maintain_partitions();');

-- =====================================================
-- 6. PARTITION PRUNING AND CLEANUP
-- =====================================================

-- Function to drop old partitions
CREATE OR REPLACE FUNCTION cleanup_old_partitions()
RETURNS VOID AS $$
DECLARE
    partition_record RECORD;
    cutoff_date DATE;
BEGIN
    -- Drop location partitions older than 90 days
    cutoff_date := CURRENT_DATE - INTERVAL '90 days';
    
    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'location_data_%'
        AND tablename ~ '\d{4}_\d{2}_\d{2}$'
        AND to_date(substring(tablename from '\d{4}_\d{2}_\d{2}$'), 'YYYY_MM_DD') < cutoff_date
    LOOP
        EXECUTE format('DROP TABLE %I.%I', partition_record.schemaname, partition_record.tablename);
        RAISE NOTICE 'Dropped old partition: %', partition_record.tablename;
    END LOOP;
    
    -- Drop ride/payment partitions older than 2 years
    cutoff_date := date_trunc('month', CURRENT_DATE - INTERVAL '2 years');
    
    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE (tablename LIKE 'rides_%' OR tablename LIKE 'payments_%')
        AND tablename ~ '\d{4}_\d{2}$'
        AND to_date(substring(tablename from '\d{4}_\d{2}$'), 'YYYY_MM') < cutoff_date
    LOOP
        EXECUTE format('DROP TABLE %I.%I', partition_record.schemaname, partition_record.tablename);
        RAISE NOTICE 'Dropped old partition: %', partition_record.tablename;
    END LOOP;
    
    -- Drop audit partitions older than 1 year
    cutoff_date := date_trunc('week', CURRENT_DATE - INTERVAL '1 year');
    
    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'audit_log_%'
        AND tablename ~ '\d{4}_\d{2}$'
        AND date_trunc('week', to_date(substring(tablename from '\d{4}_\d{2}$'), 'YYYY_WW')) < cutoff_date
    LOOP
        EXECUTE format('DROP TABLE %I.%I', partition_record.schemaname, partition_record.tablename);
        RAISE NOTICE 'Dropped old audit partition: %', partition_record.tablename;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. MONITORING AND STATISTICS
-- =====================================================

-- View to monitor partition sizes
CREATE OR REPLACE VIEW partition_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE tablename LIKE '%_20%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- View to monitor partition count
CREATE OR REPLACE VIEW partition_summary AS
SELECT 
    CASE 
        WHEN tablename LIKE 'location_data_%' THEN 'location_data'
        WHEN tablename LIKE 'rides_%' THEN 'rides'
        WHEN tablename LIKE 'payments_%' THEN 'payments'
        WHEN tablename LIKE 'audit_log_%' THEN 'audit_log'
        ELSE 'other'
    END as table_group,
    count(*) as partition_count,
    pg_size_pretty(sum(pg_total_relation_size(schemaname||'.'||tablename))) as total_size
FROM pg_tables 
WHERE tablename LIKE '%_20%'
GROUP BY table_group
ORDER BY table_group;

-- Grant permissions
GRANT SELECT ON partition_sizes TO application_user;
GRANT SELECT ON partition_summary TO application_user;
