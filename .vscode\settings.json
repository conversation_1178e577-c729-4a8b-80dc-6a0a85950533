{"java.configuration.workspaceCacheLimit": 90, "java.import.gradle.enabled": false, "java.import.maven.enabled": true, "java.compile.nullAnalysis.mode": "automatic", "java.configuration.maven.userSettings": null, "maven.executable.path": "mvn", "spring-boot.ls.checkjvm": false, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["./frontend/admin-dashboard", "./frontend/user-apps/driver-app", "./frontend/user-apps/passenger-app", "./frontend/shared-components"], "python.defaultInterpreterPath": "./backend/comprehensive-system/venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "files.exclude": {"**/node_modules": true, "**/target": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/__pycache__": true, "**/*.pyc": true, "**/venv": true, "**/.env": true}, "search.exclude": {"**/node_modules": true, "**/target": true, "**/dist": true, "**/build": true, "**/.git": true, "**/logs": true, "**/__pycache__": true, "**/venv": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/target/**": true, "**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/__pycache__/**": true, "**/venv/**": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "git.ignoreLimitWarning": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts", "*.jsx": "${capture}.js", "*.tsx": "${capture}.ts", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml", "pom.xml": "target/", "Dockerfile": ".dockerignore", "docker-compose.yml": "docker-compose.*.yml"}, "workbench.colorCustomizations": {"titleBar.activeBackground": "#1e3a8a", "titleBar.activeForeground": "#ffffff", "statusBar.background": "#1e40af", "statusBar.foreground": "#ffffff", "activityBar.background": "#1e40af", "activityBar.foreground": "#ffffff"}, "workbench.tree.indent": 20, "workbench.tree.renderIndentGuides": "always", "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": "cmd.exe", "icon": "terminal-cmd"}, "Git Bash": {"source": "<PERSON><PERSON>"}}}