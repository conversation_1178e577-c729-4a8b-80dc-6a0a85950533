import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  LocalShipping,
  Visibility,
  Edit,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchParcels } from '../../store/slices/parcelsSlice';
import LiveParcelTracker from './LiveParcelTracker';

interface Parcel {
  id: string;
  trackingNumber: string;
  senderName: string;
  recipientName: string;
  senderAddress: string;
  recipientAddress: string;
  weight: number;
  status: 'CREATED' | 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED';
  createdAt: string;
  estimatedDelivery?: string;
}

const ParcelsManagement: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { parcels, loading, totalParcels, inTransitParcels, deliveredParcels } = useSelector(
    (state: RootState) => state.parcels
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [newParcel, setNewParcel] = useState({
    senderName: '',
    recipientName: '',
    senderAddress: '',
    recipientAddress: '',
    weight: 0,
    notes: '',
  });

  useEffect(() => {
    dispatch(fetchParcels() as any);
  }, [dispatch]);

  // Data is now loaded from Redux store via useEffect above

  const getStatusChip = (status: string) => {
    const statusConfig = {
      CREATED: { label: 'تم الإنشاء', color: 'info' as const },
      PICKED_UP: { label: 'تم الاستلام', color: 'primary' as const },
      IN_TRANSIT: { label: 'في الطريق', color: 'warning' as const },
      DELIVERED: { label: 'تم التسليم', color: 'success' as const },
      CANCELLED: { label: 'ملغي', color: 'error' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleTrackParcel = (trackingNumber: string) => {
    navigate(`/parcels/track/${trackingNumber}`);
  };

  const handleEditParcel = (parcelId: string) => {
    console.log('Edit parcel:', parcelId);
    // TODO: Implement edit functionality
  };

  const columns: GridColDef[] = [
    {
      field: 'trackingNumber',
      headerName: 'رقم التتبع',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'senderName',
      headerName: 'المرسل',
      width: 150,
    },
    {
      field: 'recipientName',
      headerName: 'المستقبل',
      width: 150,
    },
    {
      field: 'senderAddress',
      headerName: 'من',
      width: 200,
    },
    {
      field: 'recipientAddress',
      headerName: 'إلى',
      width: 200,
    },
    {
      field: 'weight',
      headerName: 'الوزن (كيلو)',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value} كيلو
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الإنشاء',
      width: 150,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Visibility />}
          label="تتبع"
          onClick={() => handleTrackParcel(params.row.trackingNumber)}
        />,
        <GridActionsCellItem
          icon={<Edit />}
          label="تعديل"
          onClick={() => handleEditParcel(params.id as string)}
        />,
      ],
    },
  ];

  const filteredParcels = parcels.filter(parcel => {
    const matchesSearch = parcel.trackingNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         parcel.senderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         parcel.recipientName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'ALL' || parcel.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  const handleAddParcel = () => {
    // TODO: Implement actual API call
    const trackingNumber = `TRK-${Date.now().toString().slice(-6)}`;
    const newParcelData: Parcel = {
      id: Date.now().toString(),
      trackingNumber,
      ...newParcel,
      status: 'CREATED',
      createdAt: new Date().toISOString(),
    };

    setParcels(prev => [newParcelData, ...prev]);
    setOpenAddDialog(false);
    setNewParcel({
      senderName: '',
      recipientName: '',
      senderAddress: '',
      recipientAddress: '',
      weight: 0,
      notes: '',
    });
  };

  return (
    <Routes>
      <Route path="/track/:trackingNumber" element={<LiveParcelTracker />} />
      <Route path="/*" element={
        <Box>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              إدارة الطرود
            </Typography>
            <Typography variant="body1" color="text.secondary">
              عرض وإدارة جميع الطرود والشحنات
            </Typography>
          </Box>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {parcels.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطرود
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                    {parcels.filter(p => p.status === 'IN_TRANSIT').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    في الطريق
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {parcels.filter(p => p.status === 'DELIVERED').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تم التسليم
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {parcels.filter(p => p.status === 'PICKED_UP').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تم الاستلام
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Filters and Search */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <TextField
                  placeholder="البحث في الطرود..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    label="الحالة"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الحالات</MenuItem>
                    <MenuItem value="CREATED">تم الإنشاء</MenuItem>
                    <MenuItem value="PICKED_UP">تم الاستلام</MenuItem>
                    <MenuItem value="IN_TRANSIT">في الطريق</MenuItem>
                    <MenuItem value="DELIVERED">تم التسليم</MenuItem>
                    <MenuItem value="CANCELLED">ملغي</MenuItem>
                  </Select>
                </FormControl>
                <Button
                  variant="outlined"
                  startIcon={<FilterIcon />}
                >
                  تصفية متقدمة
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setOpenAddDialog(true)}
                >
                  إضافة طرد
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Data Grid */}
          <Card>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={filteredParcels}
                columns={columns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                checkboxSelection
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Card>

          {/* Add Parcel Dialog */}
          <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
            <DialogTitle>إضافة طرد جديد</DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="اسم المرسل"
                      value={newParcel.senderName}
                      onChange={(e) => setNewParcel({ ...newParcel, senderName: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="اسم المستقبل"
                      value={newParcel.recipientName}
                      onChange={(e) => setNewParcel({ ...newParcel, recipientName: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="عنوان المرسل"
                      value={newParcel.senderAddress}
                      onChange={(e) => setNewParcel({ ...newParcel, senderAddress: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="عنوان المستقبل"
                      value={newParcel.recipientAddress}
                      onChange={(e) => setNewParcel({ ...newParcel, recipientAddress: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="الوزن (كيلو)"
                      type="number"
                      value={newParcel.weight}
                      onChange={(e) => setNewParcel({ ...newParcel, weight: parseFloat(e.target.value) || 0 })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="ملاحظات"
                      value={newParcel.notes}
                      onChange={(e) => setNewParcel({ ...newParcel, notes: e.target.value })}
                      fullWidth
                      multiline
                      rows={3}
                    />
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
              <Button onClick={handleAddParcel} variant="contained">إضافة</Button>
            </DialogActions>
          </Dialog>
        </Box>
      } />
    </Routes>
  );
};

export default ParcelsManagement;
