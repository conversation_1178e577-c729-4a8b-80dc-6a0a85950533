import React from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardA<PERSON>,
  Typography,
  Box,
  IconButton,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import { MoreVert as MoreVertIcon } from '@mui/icons-material';

interface TecnoCardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  headerAction?: React.ReactNode;
  variant?: 'default' | 'outlined' | 'elevated' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  status?: 'success' | 'warning' | 'error' | 'info';
  loading?: boolean;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  badge?: string | number;
  icon?: React.ReactNode;
}

const TecnoCard: React.FC<TecnoCardProps> = ({
  title,
  subtitle,
  children,
  actions,
  headerAction,
  variant = 'default',
  size = 'medium',
  status,
  loading = false,
  className,
  onClick,
  hoverable = false,
  badge,
  icon,
}) => {
  const theme = useTheme();

  const getCardStyles = () => {
    const baseStyles = {
      borderRadius: theme.shape.borderRadius * 1.5,
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      cursor: onClick || hoverable ? 'pointer' : 'default',
      position: 'relative' as const,
      overflow: 'visible' as const,
    };

    const hoverStyles = (onClick || hoverable) ? {
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: theme.shadows[8],
      },
    } : {};

    const variantStyles = {
      default: {
        backgroundColor: theme.palette.background.paper,
        boxShadow: theme.shadows[2],
      },
      outlined: {
        backgroundColor: theme.palette.background.paper,
        border: `1px solid ${theme.palette.divider}`,
        boxShadow: 'none',
      },
      elevated: {
        backgroundColor: theme.palette.background.paper,
        boxShadow: theme.shadows[4],
      },
      gradient: {
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
      },
    };

    const sizeStyles = {
      small: { padding: theme.spacing(1) },
      medium: { padding: theme.spacing(2) },
      large: { padding: theme.spacing(3) },
    };

    return {
      ...baseStyles,
      ...hoverStyles,
      ...variantStyles[variant],
      ...sizeStyles[size],
    };
  };

  const getStatusColor = () => {
    if (!status) return undefined;
    
    const statusColors = {
      success: theme.palette.success.main,
      warning: theme.palette.warning.main,
      error: theme.palette.error.main,
      info: theme.palette.info.main,
    };
    
    return statusColors[status];
  };

  return (
    <Card
      sx={getCardStyles()}
      className={className}
      onClick={onClick}
    >
      {/* شريط الحالة */}
      {status && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            backgroundColor: getStatusColor(),
            borderRadius: `${theme.shape.borderRadius * 1.5}px ${theme.shape.borderRadius * 1.5}px 0 0`,
          }}
        />
      )}

      {/* Badge */}
      {badge && (
        <Box
          sx={{
            position: 'absolute',
            top: -8,
            right: -8,
            zIndex: 1,
          }}
        >
          <Chip
            label={badge}
            size="small"
            color="primary"
            sx={{
              fontSize: '0.75rem',
              height: 24,
              minWidth: 24,
              '& .MuiChip-label': {
                px: 1,
              },
            }}
          />
        </Box>
      )}

      {/* Header */}
      {(title || subtitle || headerAction) && (
        <CardHeader
          avatar={icon}
          title={
            title && (
              <Typography
                variant="h6"
                component="h3"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                }}
              >
                {title}
              </Typography>
            )
          }
          subheader={
            subtitle && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: 0.5 }}
              >
                {subtitle}
              </Typography>
            )
          }
          action={
            headerAction || (
              <IconButton size="small" aria-label="المزيد من الخيارات">
                <MoreVertIcon />
              </IconButton>
            )
          }
          sx={{
            pb: title || subtitle ? 1 : 0,
          }}
        />
      )}

      {/* Content */}
      <CardContent
        sx={{
          pt: (title || subtitle) ? 0 : undefined,
          '&:last-child': {
            pb: actions ? 2 : undefined,
          },
        }}
      >
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: 100,
            }}
          >
            <Typography color="text.secondary">جاري التحميل...</Typography>
          </Box>
        ) : (
          children
        )}
      </CardContent>

      {/* Actions */}
      {actions && (
        <CardActions
          sx={{
            px: 2,
            pb: 2,
            pt: 0,
            justifyContent: 'flex-end',
          }}
        >
          {actions}
        </CardActions>
      )}
    </Card>
  );
};

export default TecnoCard;
