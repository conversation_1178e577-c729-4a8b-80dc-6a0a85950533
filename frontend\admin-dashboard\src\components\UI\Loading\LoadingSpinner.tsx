import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  message?: string;
  overlay?: boolean;
  color?: 'primary' | 'secondary' | 'inherit';
  variant?: 'circular' | 'dots' | 'pulse';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  message = 'جاري التحميل...',
  overlay = false,
  color = 'primary',
  variant = 'circular',
}) => {
  const theme = useTheme();

  const getSizeValue = () => {
    const sizeMap = {
      small: 24,
      medium: 40,
      large: 56,
    };
    return sizeMap[size];
  };

  const getContainerStyles = () => {
    const baseStyles = {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      gap: 2,
    };

    if (overlay) {
      return {
        ...baseStyles,
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: alpha(theme.palette.background.default, 0.8),
        backdropFilter: 'blur(4px)',
        zIndex: theme.zIndex.modal,
      };
    }

    return {
      ...baseStyles,
      padding: theme.spacing(4),
    };
  };

  const renderCircularLoader = () => (
    <CircularProgress
      size={getSizeValue()}
      color={color}
      thickness={4}
      sx={{
        animationDuration: '1.5s',
      }}
    />
  );

  const renderDotsLoader = () => (
    <Box sx={{ display: 'flex', gap: 1 }}>
      {[0, 1, 2].map((index) => (
        <Box
          key={index}
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: theme.palette.primary.main,
            animation: 'pulse 1.5s ease-in-out infinite',
            animationDelay: `${index * 0.2}s`,
            '@keyframes pulse': {
              '0%, 80%, 100%': {
                transform: 'scale(0.8)',
                opacity: 0.5,
              },
              '40%': {
                transform: 'scale(1)',
                opacity: 1,
              },
            },
          }}
        />
      ))}
    </Box>
  );

  const renderPulseLoader = () => (
    <Box
      sx={{
        width: getSizeValue(),
        height: getSizeValue(),
        borderRadius: '50%',
        backgroundColor: alpha(theme.palette.primary.main, 0.3),
        animation: 'ripple 1.5s ease-in-out infinite',
        '@keyframes ripple': {
          '0%': {
            transform: 'scale(0.8)',
            opacity: 1,
          },
          '100%': {
            transform: 'scale(2)',
            opacity: 0,
          },
        },
      }}
    />
  );

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDotsLoader();
      case 'pulse':
        return renderPulseLoader();
      default:
        return renderCircularLoader();
    }
  };

  return (
    <Box sx={getContainerStyles()}>
      {renderLoader()}
      {message && (
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            textAlign: 'center',
            fontWeight: 500,
            animation: 'fadeIn 0.5s ease-in-out',
            '@keyframes fadeIn': {
              from: { opacity: 0 },
              to: { opacity: 1 },
            },
          }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

export default LoadingSpinner;
