package com.tecnodrive.rideservice.entity;

import jakarta.persistence.*;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Ride Entity for TECNO DRIVE Platform
 */
@Entity
@Table(name = "rides")
public class Ride {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(name = "passenger_id", nullable = false)
    private UUID passengerId;

    @Column(name = "driver_id")
    private UUID driverId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_type_id")
    private VehicleType vehicleType;

    // Location data using PostGIS
    @Column(name = "pickup_location", columnDefinition = "geometry(Point,4326)")
    private Point pickupLocation;

    @Column(name = "pickup_address", nullable = false)
    private String pickupAddress;

    @Column(name = "destination_location", columnDefinition = "geometry(Point,4326)")
    private Point destinationLocation;

    @Column(name = "destination_address", nullable = false)
    private String destinationAddress;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RideStatus status = RideStatus.REQUESTED;

    @Enumerated(EnumType.STRING)
    @Column(name = "ride_type")
    private RideType rideType = RideType.STANDARD;

    // Pricing
    @Column(name = "estimated_fare", precision = 10, scale = 2)
    private BigDecimal estimatedFare;

    @Column(name = "final_fare", precision = 10, scale = 2)
    private BigDecimal finalFare;

    @Column(name = "surge_multiplier", precision = 3, scale = 2)
    private BigDecimal surgeMultiplier = BigDecimal.ONE;

    // Timing
    @Column(name = "requested_at", updatable = false)
    private Instant requestedAt = Instant.now();

    @Column(name = "scheduled_at")
    private Instant scheduledAt;

    @Column(name = "driver_assigned_at")
    private Instant driverAssignedAt;

    @Column(name = "driver_arrived_at")
    private Instant driverArrivedAt;

    @Column(name = "started_at")
    private Instant startedAt;

    @Column(name = "completed_at")
    private Instant completedAt;

    @Column(name = "cancelled_at")
    private Instant cancelledAt;

    // Distance and duration
    @Column(name = "estimated_distance_km", precision = 8, scale = 2)
    private BigDecimal estimatedDistanceKm;

    @Column(name = "actual_distance_km", precision = 8, scale = 2)
    private BigDecimal actualDistanceKm;

    @Column(name = "estimated_duration_minutes")
    private Integer estimatedDurationMinutes;

    @Column(name = "actual_duration_minutes")
    private Integer actualDurationMinutes;

    // Additional info
    @Column(name = "passenger_notes")
    private String passengerNotes;

    @Column(name = "cancellation_reason")
    private String cancellationReason;

    @Column(name = "rating_by_passenger")
    private Integer ratingByPassenger;

    @Column(name = "rating_by_driver")
    private Integer ratingByDriver;

    // Multi-tenant support
    @Column(name = "company_id")
    private UUID companyId;

    @Column(name = "school_id")
    private UUID schoolId;

    @Column(name = "created_at", updatable = false)
    private Instant createdAt = Instant.now();

    @Column(name = "updated_at")
    private Instant updatedAt = Instant.now();

    // Constructors
    public Ride() {}

    public Ride(UUID passengerId, Point pickupLocation, String pickupAddress, 
                Point destinationLocation, String destinationAddress) {
        this.passengerId = passengerId;
        this.pickupLocation = pickupLocation;
        this.pickupAddress = pickupAddress;
        this.destinationLocation = destinationLocation;
        this.destinationAddress = destinationAddress;
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public UUID getPassengerId() { return passengerId; }
    public void setPassengerId(UUID passengerId) { this.passengerId = passengerId; }

    public UUID getDriverId() { return driverId; }
    public void setDriverId(UUID driverId) { this.driverId = driverId; }

    public VehicleType getVehicleType() { return vehicleType; }
    public void setVehicleType(VehicleType vehicleType) { this.vehicleType = vehicleType; }

    public Point getPickupLocation() { return pickupLocation; }
    public void setPickupLocation(Point pickupLocation) { this.pickupLocation = pickupLocation; }

    public String getPickupAddress() { return pickupAddress; }
    public void setPickupAddress(String pickupAddress) { this.pickupAddress = pickupAddress; }

    public Point getDestinationLocation() { return destinationLocation; }
    public void setDestinationLocation(Point destinationLocation) { this.destinationLocation = destinationLocation; }

    public String getDestinationAddress() { return destinationAddress; }
    public void setDestinationAddress(String destinationAddress) { this.destinationAddress = destinationAddress; }

    public RideStatus getStatus() { return status; }
    public void setStatus(RideStatus status) { this.status = status; }

    public RideType getRideType() { return rideType; }
    public void setRideType(RideType rideType) { this.rideType = rideType; }

    public BigDecimal getEstimatedFare() { return estimatedFare; }
    public void setEstimatedFare(BigDecimal estimatedFare) { this.estimatedFare = estimatedFare; }

    public BigDecimal getFinalFare() { return finalFare; }
    public void setFinalFare(BigDecimal finalFare) { this.finalFare = finalFare; }

    public BigDecimal getSurgeMultiplier() { return surgeMultiplier; }
    public void setSurgeMultiplier(BigDecimal surgeMultiplier) { this.surgeMultiplier = surgeMultiplier; }

    public Instant getRequestedAt() { return requestedAt; }
    public void setRequestedAt(Instant requestedAt) { this.requestedAt = requestedAt; }

    public Instant getScheduledAt() { return scheduledAt; }
    public void setScheduledAt(Instant scheduledAt) { this.scheduledAt = scheduledAt; }

    public Instant getDriverAssignedAt() { return driverAssignedAt; }
    public void setDriverAssignedAt(Instant driverAssignedAt) { this.driverAssignedAt = driverAssignedAt; }

    public Instant getDriverArrivedAt() { return driverArrivedAt; }
    public void setDriverArrivedAt(Instant driverArrivedAt) { this.driverArrivedAt = driverArrivedAt; }

    public Instant getStartedAt() { return startedAt; }
    public void setStartedAt(Instant startedAt) { this.startedAt = startedAt; }

    public Instant getCompletedAt() { return completedAt; }
    public void setCompletedAt(Instant completedAt) { this.completedAt = completedAt; }

    public Instant getCancelledAt() { return cancelledAt; }
    public void setCancelledAt(Instant cancelledAt) { this.cancelledAt = cancelledAt; }

    public BigDecimal getEstimatedDistanceKm() { return estimatedDistanceKm; }
    public void setEstimatedDistanceKm(BigDecimal estimatedDistanceKm) { this.estimatedDistanceKm = estimatedDistanceKm; }

    public BigDecimal getActualDistanceKm() { return actualDistanceKm; }
    public void setActualDistanceKm(BigDecimal actualDistanceKm) { this.actualDistanceKm = actualDistanceKm; }

    public Integer getEstimatedDurationMinutes() { return estimatedDurationMinutes; }
    public void setEstimatedDurationMinutes(Integer estimatedDurationMinutes) { this.estimatedDurationMinutes = estimatedDurationMinutes; }

    public Integer getActualDurationMinutes() { return actualDurationMinutes; }
    public void setActualDurationMinutes(Integer actualDurationMinutes) { this.actualDurationMinutes = actualDurationMinutes; }

    public String getPassengerNotes() { return passengerNotes; }
    public void setPassengerNotes(String passengerNotes) { this.passengerNotes = passengerNotes; }

    public String getCancellationReason() { return cancellationReason; }
    public void setCancellationReason(String cancellationReason) { this.cancellationReason = cancellationReason; }

    public Integer getRatingByPassenger() { return ratingByPassenger; }
    public void setRatingByPassenger(Integer ratingByPassenger) { this.ratingByPassenger = ratingByPassenger; }

    public Integer getRatingByDriver() { return ratingByDriver; }
    public void setRatingByDriver(Integer ratingByDriver) { this.ratingByDriver = ratingByDriver; }

    public UUID getCompanyId() { return companyId; }
    public void setCompanyId(UUID companyId) { this.companyId = companyId; }

    public UUID getSchoolId() { return schoolId; }
    public void setSchoolId(UUID schoolId) { this.schoolId = schoolId; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    public Instant getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    // Helper methods
    public boolean isActive() {
        return status == RideStatus.REQUESTED || status == RideStatus.DRIVER_ASSIGNED || 
               status == RideStatus.DRIVER_ARRIVED || status == RideStatus.IN_PROGRESS;
    }

    public boolean isCompleted() {
        return status == RideStatus.COMPLETED;
    }

    public boolean isCancelled() {
        return status == RideStatus.CANCELLED_BY_PASSENGER || 
               status == RideStatus.CANCELLED_BY_DRIVER || 
               status == RideStatus.CANCELLED_BY_SYSTEM;
    }

    public void assignDriver(UUID driverId) {
        this.driverId = driverId;
        this.status = RideStatus.DRIVER_ASSIGNED;
        this.driverAssignedAt = Instant.now();
    }

    public void markDriverArrived() {
        this.status = RideStatus.DRIVER_ARRIVED;
        this.driverArrivedAt = Instant.now();
    }

    public void startRide() {
        this.status = RideStatus.IN_PROGRESS;
        this.startedAt = Instant.now();
    }

    public void completeRide(BigDecimal finalFare, BigDecimal actualDistance, Integer actualDuration) {
        this.status = RideStatus.COMPLETED;
        this.completedAt = Instant.now();
        this.finalFare = finalFare;
        this.actualDistanceKm = actualDistance;
        this.actualDurationMinutes = actualDuration;
    }

    public void cancelRide(RideStatus cancelStatus, String reason) {
        this.status = cancelStatus;
        this.cancelledAt = Instant.now();
        this.cancellationReason = reason;
    }

    @Override
    public String toString() {
        return "Ride{" +
                "id=" + id +
                ", passengerId=" + passengerId +
                ", driverId=" + driverId +
                ", status=" + status +
                ", pickupAddress='" + pickupAddress + '\'' +
                ", destinationAddress='" + destinationAddress + '\'' +
                '}';
    }
}
