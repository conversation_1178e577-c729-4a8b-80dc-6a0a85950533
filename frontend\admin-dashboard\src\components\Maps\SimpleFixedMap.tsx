import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  MyLocation as MyLocationIcon,
  Layers as LayersIcon
} from '@mui/icons-material';

// Leaflet imports with proper initialization
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix Leaflet default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface SimpleFixedMapProps {
  height?: string;
}

// Map providers
const MAP_PROVIDERS = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors'
  },
  {
    id: 'cartodb-light',
    name: 'CartoDB Light',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  }
];

const SimpleFixedMap: React.FC<SimpleFixedMapProps> = ({ height = '600px' }) => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');

  // Fixed center for Riyadh
  const center: [number, number] = [24.7136, 46.6753];
  const zoom = 12;

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
          setError(null);
        }
      } else {
        throw new Error('Failed to load vehicles');
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Fallback to mock data
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'Ahmed Mohamed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'Sara Ahmed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_003',
          lat: 24.7100,
          lng: 46.6700,
          speed: 0,
          heading: 0,
          status: 'idle',
          driver: 'Mohamed Ali',
          lastUpdate: new Date().toISOString()
        }
      ]);
      
      setError('Using mock vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentProvider = () => {
    return MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
  };

  const createVehicleIcon = (vehicle: Vehicle) => {
    const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
    
    return L.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: white;
          font-weight: bold;
        ">
          🚗
        </div>
      `,
      className: 'simple-vehicle-marker',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={height}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          جاري تحميل الخريطة...
        </Typography>
      </Box>
    );
  }

  const currentProvider = getCurrentProvider();

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: 16,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {/* Provider Selection */}
        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <FormControl fullWidth size="small">
              <InputLabel>مزود الخريطة</InputLabel>
              <Select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                label="مزود الخريطة"
              >
                {MAP_PROVIDERS.map(provider => (
                  <MenuItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Card>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Box display="flex" gap={1}>
              <Tooltip title="تحديث">
                <IconButton onClick={loadVehicles} size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="الطبقات">
                <IconButton size="small">
                  <LayersIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Vehicle Stats */}
      {vehicles.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <Typography variant="subtitle2" gutterBottom>
                إحصائيات المركبات
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`المجموع: ${vehicles.length}`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                  size="small"
                  color="success"
                />
                <Chip
                  label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                  size="small"
                  color="warning"
                />
              </Box>
              {error && (
                <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
                  {error}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Simple Map Container with unique key */}
      <MapContainer
        key={`simple-map-${selectedProvider}`}
        center={center}
        zoom={zoom}
        style={{ height: '100%', width: '100%' }}
        zoomControl={true}
        scrollWheelZoom={true}
        doubleClickZoom={true}
        dragging={true}
      >
        {/* Base Tile Layer */}
        <TileLayer
          key={`tiles-${selectedProvider}`}
          url={currentProvider.url}
          attribution={currentProvider.attribution}
          maxZoom={19}
        />

        {/* Vehicle Markers */}
        {vehicles.map(vehicle => (
          <Marker
            key={`vehicle-${vehicle.id}`}
            position={[vehicle.lat, vehicle.lng]}
            icon={createVehicleIcon(vehicle)}
          >
            <Popup>
              <Card sx={{ minWidth: 200 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    مركبة {vehicle.id}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>السائق:</strong> {vehicle.driver}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>السرعة:</strong> {vehicle.speed} كم/س
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>الحالة:</strong> {vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>الإحداثيات:</strong> {vehicle.lat.toFixed(4)}, {vehicle.lng.toFixed(4)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>آخر تحديث:</strong> {new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                      color={vehicle.status === 'active' ? 'success' : 'warning'}
                      size="small"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </Paper>
  );
};

export default SimpleFixedMap;
