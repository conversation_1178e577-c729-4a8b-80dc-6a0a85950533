package com.tecnodrive.userservice.mapper;

import com.tecnodrive.userservice.dto.UserRequest;
import com.tecnodrive.userservice.dto.UserResponse;
import com.tecnodrive.userservice.entity.User;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "٢٠٢٥-٠٧-٣٠T٠٤:٥٩:١٨+0300",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public User toEntity(UserRequest.Create request) {
        if ( request == null ) {
            return null;
        }

        User user = new User();

        user.setAddress( request.getAddress() );
        user.setCity( request.getCity() );
        user.setCompanyId( request.getCompanyId() );
        user.setCountry( request.getCountry() );
        user.setDateOfBirth( request.getDateOfBirth() );
        user.setEmail( request.getEmail() );
        if ( request.getEmailNotifications() != null ) {
            user.setEmailNotifications( request.getEmailNotifications() );
        }
        user.setFirstName( request.getFirstName() );
        user.setGender( request.getGender() );
        user.setLastName( request.getLastName() );
        user.setPhoneNumber( request.getPhoneNumber() );
        user.setPostalCode( request.getPostalCode() );
        user.setPreferredLanguage( request.getPreferredLanguage() );
        if ( request.getPushNotifications() != null ) {
            user.setPushNotifications( request.getPushNotifications() );
        }
        if ( request.getSmsNotifications() != null ) {
            user.setSmsNotifications( request.getSmsNotifications() );
        }
        user.setTimezone( request.getTimezone() );
        user.setUserType( request.getUserType() );

        return user;
    }

    @Override
    public UserResponse toResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserResponse userResponse = new UserResponse();

        userResponse.setAddress( user.getAddress() );
        userResponse.setCity( user.getCity() );
        userResponse.setCompanyId( user.getCompanyId() );
        userResponse.setCountry( user.getCountry() );
        userResponse.setCreatedAt( user.getCreatedAt() );
        userResponse.setDateOfBirth( user.getDateOfBirth() );
        userResponse.setEmail( user.getEmail() );
        userResponse.setEmailNotifications( user.isEmailNotifications() );
        userResponse.setEmailVerified( user.isEmailVerified() );
        userResponse.setFirstName( user.getFirstName() );
        userResponse.setFullName( user.getFullName() );
        userResponse.setGender( user.getGender() );
        userResponse.setId( user.getId() );
        userResponse.setLastLoginAt( user.getLastLoginAt() );
        userResponse.setLastName( user.getLastName() );
        userResponse.setPhoneNumber( user.getPhoneNumber() );
        userResponse.setPhoneVerified( user.isPhoneVerified() );
        userResponse.setPostalCode( user.getPostalCode() );
        userResponse.setPreferredLanguage( user.getPreferredLanguage() );
        userResponse.setProfileImageUrl( user.getProfileImageUrl() );
        userResponse.setPushNotifications( user.isPushNotifications() );
        userResponse.setSmsNotifications( user.isSmsNotifications() );
        userResponse.setStatus( user.getStatus() );
        userResponse.setTimezone( user.getTimezone() );
        userResponse.setUpdatedAt( user.getUpdatedAt() );
        userResponse.setUserType( user.getUserType() );

        return userResponse;
    }

    @Override
    public UserResponse.Summary toSummaryResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserResponse.Summary summary = new UserResponse.Summary();

        summary.setCity( user.getCity() );
        summary.setCountry( user.getCountry() );
        summary.setCreatedAt( user.getCreatedAt() );
        summary.setEmail( user.getEmail() );
        summary.setEmailVerified( user.isEmailVerified() );
        summary.setFirstName( user.getFirstName() );
        summary.setFullName( user.getFullName() );
        summary.setId( user.getId() );
        summary.setLastName( user.getLastName() );
        summary.setPhoneNumber( user.getPhoneNumber() );
        summary.setPhoneVerified( user.isPhoneVerified() );
        summary.setStatus( user.getStatus() );
        summary.setUserType( user.getUserType() );

        return summary;
    }

    @Override
    public UserResponse.PublicProfile toPublicProfileResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserResponse.PublicProfile publicProfile = new UserResponse.PublicProfile();

        publicProfile.setCity( user.getCity() );
        publicProfile.setCountry( user.getCountry() );
        publicProfile.setCreatedAt( user.getCreatedAt() );
        publicProfile.setFirstName( user.getFirstName() );
        publicProfile.setFullName( user.getFullName() );
        publicProfile.setId( user.getId() );
        publicProfile.setLastName( user.getLastName() );
        publicProfile.setProfileImageUrl( user.getProfileImageUrl() );
        publicProfile.setUserType( user.getUserType() );

        return publicProfile;
    }

    @Override
    public UserResponse.Activity toActivityResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserResponse.Activity activity = new UserResponse.Activity();

        activity.setActive( user.isActive() );
        activity.setEmail( user.getEmail() );
        activity.setFullName( user.getFullName() );
        activity.setLastLoginAt( user.getLastLoginAt() );
        activity.setStatus( user.getStatus() );

        return activity;
    }

    @Override
    public void updateEntityFromRequest(UserRequest.Update request, User user) {
        if ( request == null ) {
            return;
        }

        if ( request.getAddress() != null ) {
            user.setAddress( request.getAddress() );
        }
        if ( request.getCity() != null ) {
            user.setCity( request.getCity() );
        }
        if ( request.getCompanyId() != null ) {
            user.setCompanyId( request.getCompanyId() );
        }
        if ( request.getCountry() != null ) {
            user.setCountry( request.getCountry() );
        }
        if ( request.getDateOfBirth() != null ) {
            user.setDateOfBirth( request.getDateOfBirth() );
        }
        if ( request.getEmail() != null ) {
            user.setEmail( request.getEmail() );
        }
        if ( request.getEmailNotifications() != null ) {
            user.setEmailNotifications( request.getEmailNotifications() );
        }
        if ( request.getFirstName() != null ) {
            user.setFirstName( request.getFirstName() );
        }
        if ( request.getGender() != null ) {
            user.setGender( request.getGender() );
        }
        if ( request.getLastName() != null ) {
            user.setLastName( request.getLastName() );
        }
        if ( request.getPhoneNumber() != null ) {
            user.setPhoneNumber( request.getPhoneNumber() );
        }
        if ( request.getPostalCode() != null ) {
            user.setPostalCode( request.getPostalCode() );
        }
        if ( request.getPreferredLanguage() != null ) {
            user.setPreferredLanguage( request.getPreferredLanguage() );
        }
        if ( request.getPushNotifications() != null ) {
            user.setPushNotifications( request.getPushNotifications() );
        }
        if ( request.getSmsNotifications() != null ) {
            user.setSmsNotifications( request.getSmsNotifications() );
        }
        if ( request.getStatus() != null ) {
            user.setStatus( request.getStatus() );
        }
        if ( request.getTimezone() != null ) {
            user.setTimezone( request.getTimezone() );
        }
    }

    @Override
    public List<UserResponse> toResponseList(List<User> users) {
        if ( users == null ) {
            return null;
        }

        List<UserResponse> list = new ArrayList<UserResponse>( users.size() );
        for ( User user : users ) {
            list.add( toResponse( user ) );
        }

        return list;
    }

    @Override
    public List<UserResponse.Summary> toSummaryResponseList(List<User> users) {
        if ( users == null ) {
            return null;
        }

        List<UserResponse.Summary> list = new ArrayList<UserResponse.Summary>( users.size() );
        for ( User user : users ) {
            list.add( toSummaryResponse( user ) );
        }

        return list;
    }
}
