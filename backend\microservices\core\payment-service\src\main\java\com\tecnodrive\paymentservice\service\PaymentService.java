package com.tecnodrive.paymentservice.service;

import com.tecnodrive.paymentservice.dto.PaymentRequest;
import com.tecnodrive.paymentservice.dto.PaymentResponse;
import com.tecnodrive.paymentservice.dto.PaymentUpdateRequest;
import com.tecnodrive.paymentservice.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;

/**
 * Payment Service Interface
 * 
 * Defines business logic operations for payment management
 */
public interface PaymentService {

    /**
     * Create a new payment
     */
    PaymentResponse createPayment(PaymentRequest request);

    /**
     * Get payment by ID
     */
    PaymentResponse getPayment(String id);

    /**
     * Update payment
     */
    PaymentResponse updatePayment(String id, PaymentUpdateRequest request);

    /**
     * Get payments by entity
     */
    List<PaymentResponse> getPaymentsByEntity(String entityId, String entityType);

    /**
     * Get payments by user (payer)
     */
    Page<PaymentResponse> getPaymentsByUser(String userId, Pageable pageable);

    /**
     * Get payments by status
     */
    List<PaymentResponse> getPaymentsByStatus(Payment.PaymentStatus status);

    /**
     * Process payment (update status to PROCESSING)
     */
    PaymentResponse processPayment(String id);

    /**
     * Complete payment (update status to COMPLETED)
     */
    PaymentResponse completePayment(String id, String gatewayTransactionId);

    /**
     * Fail payment (update status to FAILED)
     */
    PaymentResponse failPayment(String id, String reason);

    /**
     * Cancel payment (update status to CANCELLED)
     */
    PaymentResponse cancelPayment(String id);

    /**
     * Refund payment (update status to REFUNDED)
     */
    PaymentResponse refundPayment(String id);

    /**
     * Get payment statistics for a date range
     */
    PaymentStatistics getPaymentStatistics(Instant startDate, Instant endDate);

    /**
     * Payment Statistics DTO
     */
    record PaymentStatistics(
            long totalPayments,
            long completedPayments,
            long failedPayments,
            long pendingPayments,
            long cancelledPayments,
            long refundedPayments
    ) {}
}
