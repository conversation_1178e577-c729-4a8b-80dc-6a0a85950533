package com.tecnodrive.walletservice.service;

import com.tecnodrive.walletservice.dto.WalletDTO;
import com.tecnodrive.walletservice.entity.Wallet;
import com.tecnodrive.walletservice.entity.WalletTransaction;
import com.tecnodrive.walletservice.repository.WalletRepository;
import com.tecnodrive.walletservice.repository.WalletTransactionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Wallet Service
 * Core business logic for wallet management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class WalletService {

    private final WalletRepository walletRepository;
    private final WalletTransactionRepository transactionRepository;
    private final WalletTransactionService transactionService;

    /**
     * Create a new wallet
     */
    public WalletDTO createWallet(UUID userId, String phoneNumber, String createdBy) {
        log.info("Creating wallet for user: {} with phone: {}", userId, phoneNumber);

        // Check if wallet already exists
        if (walletRepository.existsByUserId(userId)) {
            throw new IllegalArgumentException("Wallet already exists for user: " + userId);
        }

        if (walletRepository.existsByPhoneNumber(phoneNumber)) {
            throw new IllegalArgumentException("Wallet already exists for phone number: " + phoneNumber);
        }

        Wallet wallet = Wallet.builder()
                .userId(userId)
                .phoneNumber(phoneNumber)
                .balance(BigDecimal.ZERO)
                .currency("SAR")
                .status(Wallet.WalletStatus.ACTIVE)
                .dailyLimit(new BigDecimal("5000.00"))
                .monthlyLimit(new BigDecimal("50000.00"))
                .dailySpent(BigDecimal.ZERO)
                .monthlySpent(BigDecimal.ZERO)
                .isPinSet(false)
                .failedPinAttempts(0)
                .verificationLevel(Wallet.VerificationLevel.BASIC)
                .createdBy(createdBy)
                .build();

        Wallet savedWallet = walletRepository.save(wallet);
        log.info("Wallet created successfully with ID: {}", savedWallet.getId());

        return WalletDTO.fromEntity(savedWallet);
    }

    /**
     * Get wallet by ID
     */
    @Cacheable(value = "wallets", key = "#walletId")
    public Optional<WalletDTO> getWalletById(UUID walletId) {
        return walletRepository.findById(walletId)
                .map(WalletDTO::fromEntity);
    }

    /**
     * Get wallet by user ID
     */
    @Cacheable(value = "wallets", key = "'user:' + #userId")
    public Optional<WalletDTO> getWalletByUserId(UUID userId) {
        return walletRepository.findByUserId(userId)
                .map(WalletDTO::fromEntity);
    }

    /**
     * Get wallet by phone number
     */
    @Cacheable(value = "wallets", key = "'phone:' + #phoneNumber")
    public Optional<WalletDTO> getWalletByPhoneNumber(String phoneNumber) {
        return walletRepository.findByPhoneNumber(phoneNumber)
                .map(WalletDTO::fromEntity);
    }

    /**
     * Get wallet balance
     */
    public BigDecimal getWalletBalance(UUID walletId) {
        return walletRepository.findById(walletId)
                .map(Wallet::getBalance)
                .orElseThrow(() -> new IllegalArgumentException("Wallet not found: " + walletId));
    }

    /**
     * Update wallet status
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public WalletDTO updateWalletStatus(UUID walletId, Wallet.WalletStatus status, String updatedBy) {
        log.info("Updating wallet status: {} to {}", walletId, status);

        Wallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new IllegalArgumentException("Wallet not found: " + walletId));

        wallet.setStatus(status);
        wallet.setUpdatedBy(updatedBy);

        Wallet savedWallet = walletRepository.save(wallet);
        log.info("Wallet status updated successfully");

        return WalletDTO.fromEntity(savedWallet);
    }

    /**
     * Update wallet limits
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public WalletDTO updateWalletLimits(UUID walletId, BigDecimal dailyLimit, BigDecimal monthlyLimit, String updatedBy) {
        log.info("Updating wallet limits: {} - daily: {}, monthly: {}", walletId, dailyLimit, monthlyLimit);

        Wallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new IllegalArgumentException("Wallet not found: " + walletId));

        if (dailyLimit != null) {
            wallet.setDailyLimit(dailyLimit);
        }
        if (monthlyLimit != null) {
            wallet.setMonthlyLimit(monthlyLimit);
        }
        wallet.setUpdatedBy(updatedBy);

        Wallet savedWallet = walletRepository.save(wallet);
        log.info("Wallet limits updated successfully");

        return WalletDTO.fromEntity(savedWallet);
    }

    /**
     * Add balance to wallet (Top-up)
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public WalletDTO addBalance(UUID walletId, BigDecimal amount, WalletTransaction.TransactionSource source, 
                               String description, UUID agentId, String agentName, String createdBy) {
        log.info("Adding balance to wallet: {} - amount: {}", walletId, amount);

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount must be positive");
        }

        Wallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new IllegalArgumentException("Wallet not found: " + walletId));

        if (!wallet.isUsable()) {
            throw new IllegalStateException("Wallet is not usable");
        }

        BigDecimal balanceBefore = wallet.getBalance();
        wallet.addBalance(amount);
        BigDecimal balanceAfter = wallet.getBalance();

        // Create transaction record
        WalletTransaction transaction = WalletTransaction.builder()
                .walletId(walletId)
                .transactionReference(WalletTransaction.generateReference(source))
                .type(WalletTransaction.TransactionType.CREDIT)
                .amount(amount)
                .currency(wallet.getCurrency())
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .status(WalletTransaction.TransactionStatus.COMPLETED)
                .source(source)
                .description(description)
                .agentId(agentId)
                .agentName(agentName)
                .netAmount(amount)
                .createdBy(createdBy)
                .build();

        transaction.markCompleted();
        transactionRepository.save(transaction);

        Wallet savedWallet = walletRepository.save(wallet);
        log.info("Balance added successfully. New balance: {}", savedWallet.getBalance());

        return WalletDTO.fromEntity(savedWallet);
    }

    /**
     * Deduct balance from wallet (Payment)
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public WalletDTO deductBalance(UUID walletId, BigDecimal amount, WalletTransaction.TransactionSource source,
                                  String description, String referenceId, String referenceType, 
                                  BigDecimal fees, BigDecimal tax, String createdBy) {
        log.info("Deducting balance from wallet: {} - amount: {}", walletId, amount);

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount must be positive");
        }

        Wallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new IllegalArgumentException("Wallet not found: " + walletId));

        if (!wallet.isUsable()) {
            throw new IllegalStateException("Wallet is not usable");
        }

        BigDecimal totalAmount = amount.add(fees != null ? fees : BigDecimal.ZERO)
                                      .add(tax != null ? tax : BigDecimal.ZERO);

        if (!wallet.hasSufficientBalance(totalAmount)) {
            throw new IllegalStateException("Insufficient balance");
        }

        if (wallet.isDailyLimitExceeded(totalAmount)) {
            throw new IllegalStateException("Daily limit exceeded");
        }

        if (wallet.isMonthlyLimitExceeded(totalAmount)) {
            throw new IllegalStateException("Monthly limit exceeded");
        }

        BigDecimal balanceBefore = wallet.getBalance();
        wallet.subtractBalance(totalAmount);
        BigDecimal balanceAfter = wallet.getBalance();

        // Create transaction record
        WalletTransaction transaction = WalletTransaction.builder()
                .walletId(walletId)
                .transactionReference(WalletTransaction.generateReference(source))
                .type(WalletTransaction.TransactionType.DEBIT)
                .amount(amount)
                .currency(wallet.getCurrency())
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .status(WalletTransaction.TransactionStatus.COMPLETED)
                .source(source)
                .description(description)
                .referenceId(referenceId)
                .referenceType(referenceType)
                .fees(fees != null ? fees : BigDecimal.ZERO)
                .tax(tax != null ? tax : BigDecimal.ZERO)
                .netAmount(amount)
                .createdBy(createdBy)
                .build();

        transaction.calculateNetAmount();
        transaction.markCompleted();
        transactionRepository.save(transaction);

        Wallet savedWallet = walletRepository.save(wallet);
        log.info("Balance deducted successfully. New balance: {}", savedWallet.getBalance());

        return WalletDTO.fromEntity(savedWallet);
    }

    /**
     * Get all wallets with pagination
     */
    public Page<WalletDTO> getAllWallets(Pageable pageable) {
        return walletRepository.findAll(pageable)
                .map(WalletDTO::fromEntity);
    }

    /**
     * Get active wallets
     */
    public List<WalletDTO> getActiveWallets() {
        return walletRepository.findActiveWallets()
                .stream()
                .map(WalletDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Search wallets
     */
    public Page<WalletDTO> searchWallets(String phoneNumber, Wallet.WalletStatus status,
                                        Wallet.VerificationLevel verificationLevel,
                                        BigDecimal minBalance, BigDecimal maxBalance,
                                        Pageable pageable) {
        return walletRepository.searchWallets(phoneNumber, status, verificationLevel, 
                                            minBalance, maxBalance, pageable)
                .map(WalletDTO::fromEntity);
    }

    /**
     * Reset daily spending for all wallets
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public void resetDailySpending() {
        log.info("Resetting daily spending for all wallets");
        List<Wallet> wallets = walletRepository.findWalletsNeedingDailyReset();
        
        for (Wallet wallet : wallets) {
            wallet.resetDailySpending();
        }
        
        walletRepository.saveAll(wallets);
        log.info("Daily spending reset completed for {} wallets", wallets.size());
    }

    /**
     * Reset monthly spending for all wallets
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public void resetMonthlySpending() {
        log.info("Resetting monthly spending for all wallets");
        List<Wallet> wallets = walletRepository.findWalletsNeedingMonthlyReset();
        
        for (Wallet wallet : wallets) {
            wallet.resetMonthlySpending();
        }
        
        walletRepository.saveAll(wallets);
        log.info("Monthly spending reset completed for {} wallets", wallets.size());
    }

    /**
     * Unlock wallets that should be unlocked
     */
    @CacheEvict(value = "wallets", allEntries = true)
    public void unlockWallets() {
        log.info("Unlocking wallets that should be unlocked");
        List<Wallet> wallets = walletRepository.findWalletsToUnlock(LocalDateTime.now());
        
        for (Wallet wallet : wallets) {
            wallet.setLockedUntil(null);
            wallet.setFailedPinAttempts(0);
        }
        
        walletRepository.saveAll(wallets);
        log.info("Unlocked {} wallets", wallets.size());
    }
}
