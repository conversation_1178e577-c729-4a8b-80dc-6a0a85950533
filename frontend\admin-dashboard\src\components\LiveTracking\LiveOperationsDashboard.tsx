import React, { useState, useEffect, useCallback } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Refresh,
  Settings,
  Fullscreen,
  FullscreenExit,
  PlayArrow,
  Pause,
  Speed,
  DirectionsCar,
  LocalShipping,
  Warning
} from '@mui/icons-material';

import LiveMap, { VehicleLocation, ParcelLocation } from './LiveMap';
import AlertsPanel from './AlertsPanel';
import { AlertItem } from './AlertsPanel';
import {
  websocketService,
  VehicleLocationUpdate,
  ParcelUpdate,
  AlertUpdate
} from '../../services/websocketService';
import {
  generateMockVehicles,
  generateMockParcels,
  generateMockAlerts,
  DEFAULT_MOCK_DATA
} from '../../utils/mockLiveData';

interface LiveOperationsDashboardProps {
  refreshInterval?: number;
}

const LiveOperationsDashboard: React.FC<LiveOperationsDashboardProps> = ({
  refreshInterval = 5000
}) => {
  // State management
  const [vehicles, setVehicles] = useState<VehicleLocation[]>([]);
  const [parcels, setParcels] = useState<ParcelLocation[]>([]);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [isLiveMode, setIsLiveMode] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Statistics
  const stats = {
    totalVehicles: vehicles.length,
    availableVehicles: vehicles.filter(v => v.status === 'available').length,
    busyVehicles: vehicles.filter(v => v.status === 'busy').length,
    offlineVehicles: vehicles.filter(v => v.status === 'offline').length,
    activeParcels: parcels.filter(p => p.status !== 'delivered').length,
    criticalAlerts: alerts.filter(a => a.severity === 'critical' && !a.resolved).length,
    unacknowledgedAlerts: alerts.filter(a => !a.acknowledged && !a.resolved).length
  };

  // WebSocket event handlers
  const handleVehicleUpdates = useCallback((updates: VehicleLocationUpdate[]) => {
    const mappedVehicles: VehicleLocation[] = updates.map(update => ({
      id: update.vehicleId,
      type: update.vehicleType,
      lat: update.location.lat,
      lng: update.location.lng,
      heading: update.location.heading || 0,
      speed: update.location.speed || 0,
      status: update.status,
      driverName: update.driverName,
      lastUpdate: update.location.timestamp
    }));
    
    setVehicles(mappedVehicles);
    setLastUpdate(new Date());
  }, []);

  const handleParcelUpdate = useCallback((update: ParcelUpdate) => {
    setParcels(prev => {
      const index = prev.findIndex(p => p.id === update.trackingNumber);
      const newParcel: ParcelLocation = {
        id: update.trackingNumber,
        lat: update.location?.lat || 0,
        lng: update.location?.lng || 0,
        status: update.status as any,
        estimatedDelivery: update.estimatedDelivery || '',
        recipientName: update.recipientName || ''
      };

      if (index >= 0) {
        const newParcels = [...prev];
        newParcels[index] = newParcel;
        return newParcels;
      } else {
        return [...prev, newParcel];
      }
    });
    setLastUpdate(new Date());
  }, []);

  const handleAlertUpdate = useCallback((update: AlertUpdate) => {
    const newAlert: AlertItem = {
      id: update.id,
      type: update.type,
      severity: update.severity,
      title: update.title,
      description: update.description,
      entityId: update.entityId,
      entityType: update.entityType,
      location: update.location,
      timestamp: update.timestamp,
      acknowledged: update.acknowledged,
      resolved: update.resolved
    };

    setAlerts(prev => {
      const index = prev.findIndex(a => a.id === update.id);
      if (index >= 0) {
        const newAlerts = [...prev];
        newAlerts[index] = newAlert;
        return newAlerts;
      } else {
        return [newAlert, ...prev];
      }
    });

    // Show notification for new critical alerts
    if (update.severity === 'critical' && !update.acknowledged) {
      setSnackbarMessage(`Critical Alert: ${update.title}`);
    }
  }, []);

  // Initialize WebSocket connection
  useEffect(() => {
    const initializeConnection = async () => {
      try {
        setConnectionStatus('connecting');
        await websocketService.connect();
        setConnectionStatus('connected');

        if (isLiveMode) {
          websocketService.subscribeToLiveTracking({
            onVehicleUpdate: handleVehicleUpdates,
            onParcelUpdate: handleParcelUpdate,
            onAlertUpdate: handleAlertUpdate
          });
        }
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
        setConnectionStatus('disconnected');
        // Fall back to mock data for development
        setVehicles(generateMockVehicles(25));
        setParcels(generateMockParcels(15));
        setAlerts(generateMockAlerts(8));
        setLastUpdate(new Date());
      }
    };

    initializeConnection();

    return () => {
      websocketService.unsubscribeFromLiveTracking();
    };
  }, [isLiveMode, handleVehicleUpdates, handleParcelUpdate, handleAlertUpdate]);



  // Refresh data manually
  const handleRefresh = useCallback(() => {
    if (connectionStatus === 'connected') {
      // Request fresh data from server
      websocketService.emit('request_live_data', {});
    } else {
      // Use mock data
      setVehicles(generateMockVehicles(25));
      setParcels(generateMockParcels(15));
      setAlerts(generateMockAlerts(8));
      setLastUpdate(new Date());
    }
  }, [connectionStatus]);

  // Toggle live mode
  const handleLiveModeToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsLiveMode(event.target.checked);
    if (!event.target.checked) {
      websocketService.unsubscribeFromLiveTracking();
    }
  };

  // Handle vehicle click
  const handleVehicleClick = (vehicle: VehicleLocation) => {
    console.log('Vehicle clicked:', vehicle);
    // TODO: Show vehicle details modal
  };

  // Handle parcel click
  const handleParcelClick = (parcel: ParcelLocation) => {
    console.log('Parcel clicked:', parcel);
    // TODO: Show parcel details modal
  };

  // Handle alert actions
  const handleAlertAcknowledge = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
    websocketService.emit('acknowledge_alert', { alertId });
  };

  const handleAlertResolve = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ));
    websocketService.emit('resolve_alert', { alertId });
  };

  // Auto-refresh for mock data
  useEffect(() => {
    if (connectionStatus === 'disconnected' && isLiveMode) {
      const interval = setInterval(() => {
        setVehicles(generateMockVehicles(25));
        setParcels(generateMockParcels(15));
        setAlerts(generateMockAlerts(8));
        setLastUpdate(new Date());
      }, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [connectionStatus, isLiveMode, refreshInterval]);

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', p: 2 }}>
      {/* Header */}
      <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
            Live Operations Dashboard
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Connection Status */}
            <Chip
              label={connectionStatus}
              color={connectionStatus === 'connected' ? 'success' : 
                     connectionStatus === 'connecting' ? 'warning' : 'error'}
              size="small"
            />
            
            {/* Live Mode Toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={isLiveMode}
                  onChange={handleLiveModeToggle}
                  color="primary"
                />
              }
              label="Live Mode"
            />
            
            {/* Controls */}
            <Tooltip title="Refresh Data">
              <IconButton onClick={handleRefresh}>
                <Refresh />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={() => setIsFullscreen(!isFullscreen)}>
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card variant="outlined">
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DirectionsCar color="primary" />
                  <Box>
                    <Typography variant="h6">{stats.totalVehicles}</Typography>
                    <Typography variant="caption" color="text.secondary">Total Vehicles</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card variant="outlined">
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DirectionsCar sx={{ color: '#4CAF50' }} />
                  <Box>
                    <Typography variant="h6">{stats.availableVehicles}</Typography>
                    <Typography variant="caption" color="text.secondary">Available</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card variant="outlined">
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <LocalShipping color="primary" />
                  <Box>
                    <Typography variant="h6">{stats.activeParcels}</Typography>
                    <Typography variant="caption" color="text.secondary">Active Parcels</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card variant="outlined">
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Warning sx={{ color: '#f44336' }} />
                  <Box>
                    <Typography variant="h6">{stats.criticalAlerts}</Typography>
                    <Typography variant="caption" color="text.secondary">Critical Alerts</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Card variant="outlined">
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Typography variant="caption" color="text.secondary">
                  Last Update: {lastUpdate.toLocaleTimeString()}
                </Typography>
                <Typography variant="body2">
                  {isLiveMode ? 'Live tracking active' : 'Live tracking paused'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Main Content */}
      <Grid container spacing={2} sx={{ flex: 1 }}>
        {/* Map */}
        <Grid item xs={12} lg={8}>
          <LiveMap
            vehicles={vehicles}
            parcels={parcels}
            onVehicleClick={handleVehicleClick}
            onParcelClick={handleParcelClick}
          />
        </Grid>
        
        {/* Alerts Panel */}
        <Grid item xs={12} lg={4}>
          <AlertsPanel
            alerts={alerts}
            onAlertAcknowledge={handleAlertAcknowledge}
            onAlertResolve={handleAlertResolve}
            onRefresh={handleRefresh}
          />
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={!!snackbarMessage}
        autoHideDuration={6000}
        onClose={() => setSnackbarMessage('')}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert severity="warning" onClose={() => setSnackbarMessage('')}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LiveOperationsDashboard;
