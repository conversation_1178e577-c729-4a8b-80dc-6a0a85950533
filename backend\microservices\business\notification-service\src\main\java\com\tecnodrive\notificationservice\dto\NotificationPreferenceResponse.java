package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.UserNotificationPreference;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;

/**
 * Notification Preference Response DTO
 * 
 * Used for returning user notification preferences to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationPreferenceResponse {

    /**
     * User ID
     */
    private String userId;

    /**
     * Global notification settings
     */
    private boolean enableNotifications;

    /**
     * Channel-specific preferences
     */
    private boolean enableEmail;
    private boolean enableSms;
    private boolean enablePush;
    private boolean enableInApp;

    /**
     * Category-specific preferences
     */
    private boolean enableRideNotifications;
    private boolean enableDeliveryNotifications;
    private boolean enablePaymentNotifications;
    private boolean enablePromotionalNotifications;
    private boolean enableSecurityNotifications;

    /**
     * Time-based preferences
     */
    private boolean enableNightTimeNotifications;

    /**
     * Quiet hours start (24-hour format)
     */
    private Integer quietHoursStart;

    /**
     * Quiet hours end (24-hour format)
     */
    private Integer quietHoursEnd;

    /**
     * Timezone for quiet hours
     */
    private String timezone;

    /**
     * Frequency preferences
     */
    private UserNotificationPreference.NotificationFrequency emailFrequency;
    private UserNotificationPreference.NotificationFrequency smsFrequency;

    /**
     * Language preference for notifications
     */
    private String language;

    /**
     * Tenant ID
     */
    private String tenantId;

    /**
     * Creation timestamp
     */
    private Instant createdAt;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;
}
