package com.tecnodrive.parcelservice.dto;

import java.util.UUID;

/**
 * Delivery DTO
 */
public class DeliveryDto {

    private UUID parcelId;
    private UUID driverId;

    // Default constructor
    public DeliveryDto() {}

    // All-args constructor
    public DeliveryDto(UUID parcelId, UUID driverId) {
        this.parcelId = parcelId;
        this.driverId = driverId;
    }

    // Getters and Setters
    public UUID getParcelId() {
        return parcelId;
    }

    public void setParcelId(UUID parcelId) {
        this.parcelId = parcelId;
    }

    public UUID getDriverId() {
        return driverId;
    }

    public void setDriverId(UUID driverId) {
        this.driverId = driverId;
    }
}
