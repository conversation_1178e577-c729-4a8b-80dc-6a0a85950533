# =============================================================================
# TECNO DRIVE API Gateway - Prometheus Configuration
# =============================================================================

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tecno-drive-dev'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 30s

  # API Gateway metrics
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8081']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: api-gateway:8081

  # JVM metrics from API Gateway
  - job_name: 'api-gateway-jvm'
    static_configs:
      - targets: ['api-gateway:8081']
    metrics_path: /actuator/prometheus
    scrape_interval: 30s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'jvm_.*'
        action: keep

  # PostgreSQL metrics (if postgres_exporter is available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    honor_labels: true

  # Redis metrics (if redis_exporter is available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    honor_labels: true

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Spring Boot Actuator endpoints
  - job_name: 'spring-actuator'
    static_configs:
      - targets: ['api-gateway:8081']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    params:
      format: ['prometheus']

  # Custom application metrics
  - job_name: 'tecno-drive-custom'
    static_configs:
      - targets: ['api-gateway:8081']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'tecno_drive_.*'
        action: keep

# Remote write configuration (for production)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration is handled via command line arguments
# --storage.tsdb.retention.time=15d
# --storage.tsdb.retention.size=10GB
