package com.tecnodrive.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Security Filter for API Gateway
 * Implements WAF-like functionality to protect against common attacks
 */
@Slf4j
@Component
public class SecurityFilter implements GlobalFilter, Ordered {

    // SQL Injection patterns
    private static final List<Pattern> SQL_INJECTION_PATTERNS = Arrays.asList(
            Pattern.compile("(?i).*('|(\\-\\-)|(;)|(\\|)|(\\*)|(%27)|(%2D%2D)|(%7C)|(%2A)).*"),
            Pattern.compile("(?i).*(union|select|insert|delete|update|drop|create|alter|exec|execute).*"),
            Pattern.compile("(?i).*(script|javascript|vbscript|onload|onerror|onclick).*")
    );

    // XSS patterns
    private static final List<Pattern> XSS_PATTERNS = Arrays.asList(
            Pattern.compile("(?i).*<script.*>.*</script>.*"),
            Pattern.compile("(?i).*javascript:.*"),
            Pattern.compile("(?i).*on(load|error|click|mouse|focus|blur)\\s*=.*"),
            Pattern.compile("(?i).*<iframe.*>.*"),
            Pattern.compile("(?i).*<object.*>.*"),
            Pattern.compile("(?i).*<embed.*>.*")
    );

    // Path traversal patterns
    private static final List<Pattern> PATH_TRAVERSAL_PATTERNS = Arrays.asList(
            Pattern.compile(".*\\.\\.[\\\\/].*"),
            Pattern.compile(".*[\\\\/]\\.\\..*"),
            Pattern.compile(".*%2e%2e[\\\\/].*"),
            Pattern.compile(".*[\\\\/]%2e%2e.*")
    );

    // Blocked User Agents
    private static final List<String> BLOCKED_USER_AGENTS = Arrays.asList(
            "bot", "crawler", "spider", "scraper", "scanner", "nikto", "sqlmap", "nmap"
    );

    // Suspicious headers
    private static final List<String> SUSPICIOUS_HEADERS = Arrays.asList(
            "x-forwarded-host", "x-cluster-client-ip", "x-real-ip"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // Add security headers
        addSecurityHeaders(response);

        // Check for blocked user agents
        if (isBlockedUserAgent(request)) {
            log.warn("Blocked request from suspicious user agent: {}", 
                    request.getHeaders().getFirst("User-Agent"));
            return blockRequest(exchange, "Blocked User Agent");
        }

        // Check for SQL injection in query parameters
        if (containsSqlInjection(request)) {
            log.warn("Blocked SQL injection attempt from IP: {}", getClientIp(request));
            return blockRequest(exchange, "SQL Injection Detected");
        }

        // Check for XSS in query parameters
        if (containsXss(request)) {
            log.warn("Blocked XSS attempt from IP: {}", getClientIp(request));
            return blockRequest(exchange, "XSS Attack Detected");
        }

        // Check for path traversal
        if (containsPathTraversal(request)) {
            log.warn("Blocked path traversal attempt from IP: {}", getClientIp(request));
            return blockRequest(exchange, "Path Traversal Detected");
        }

        // Check request size
        if (isRequestTooLarge(request)) {
            log.warn("Blocked oversized request from IP: {}", getClientIp(request));
            return blockRequest(exchange, "Request Too Large");
        }

        // Check for suspicious headers
        if (hasSuspiciousHeaders(request)) {
            log.warn("Suspicious headers detected from IP: {}", getClientIp(request));
            // Log but don't block - might be legitimate proxy
        }

        // Add request ID for tracing
        String requestId = java.util.UUID.randomUUID().toString();
        ServerHttpRequest modifiedRequest = request.mutate()
                .header("X-Request-ID", requestId)
                .build();

        log.debug("Processing request {} from IP: {}", requestId, getClientIp(request));

        return chain.filter(exchange.mutate().request(modifiedRequest).build());
    }

    private void addSecurityHeaders(ServerHttpResponse response) {
        response.getHeaders().add("X-Content-Type-Options", "nosniff");
        response.getHeaders().add("X-Frame-Options", "DENY");
        response.getHeaders().add("X-XSS-Protection", "1; mode=block");
        response.getHeaders().add("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
        response.getHeaders().add("Referrer-Policy", "strict-origin-when-cross-origin");
        response.getHeaders().add("Content-Security-Policy", 
                "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
        response.getHeaders().add("X-Permitted-Cross-Domain-Policies", "none");
        response.getHeaders().add("Cache-Control", "no-cache, no-store, must-revalidate");
        response.getHeaders().add("Pragma", "no-cache");
        response.getHeaders().add("Expires", "0");
    }

    private boolean isBlockedUserAgent(ServerHttpRequest request) {
        String userAgent = request.getHeaders().getFirst("User-Agent");
        if (userAgent == null) {
            return false;
        }
        
        return BLOCKED_USER_AGENTS.stream()
                .anyMatch(blocked -> userAgent.toLowerCase().contains(blocked));
    }

    private boolean containsSqlInjection(ServerHttpRequest request) {
        String queryString = request.getURI().getQuery();
        if (queryString == null) {
            return false;
        }

        return SQL_INJECTION_PATTERNS.stream()
                .anyMatch(pattern -> pattern.matcher(queryString).matches());
    }

    private boolean containsXss(ServerHttpRequest request) {
        String queryString = request.getURI().getQuery();
        if (queryString == null) {
            return false;
        }

        return XSS_PATTERNS.stream()
                .anyMatch(pattern -> pattern.matcher(queryString).matches());
    }

    private boolean containsPathTraversal(ServerHttpRequest request) {
        String path = request.getURI().getPath();
        if (path == null) {
            return false;
        }

        return PATH_TRAVERSAL_PATTERNS.stream()
                .anyMatch(pattern -> pattern.matcher(path).matches());
    }

    private boolean isRequestTooLarge(ServerHttpRequest request) {
        String contentLength = request.getHeaders().getFirst("Content-Length");
        if (contentLength == null) {
            return false;
        }

        try {
            long length = Long.parseLong(contentLength);
            return length > 10 * 1024 * 1024; // 10MB limit
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean hasSuspiciousHeaders(ServerHttpRequest request) {
        return SUSPICIOUS_HEADERS.stream()
                .anyMatch(header -> request.getHeaders().containsKey(header));
    }

    private Mono<Void> blockRequest(ServerWebExchange exchange, String reason) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add("Content-Type", "application/json");
        
        String body = String.format(
                "{\"error\":\"Request Blocked\",\"reason\":\"%s\",\"timestamp\":\"%s\"}",
                reason, java.time.Instant.now()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(body.getBytes());
        return response.writeWith(reactor.core.publisher.Mono.just(buffer));
    }

    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        var remoteAddress = request.getRemoteAddress();
        if (remoteAddress != null && remoteAddress.getAddress() != null) {
            return remoteAddress.getAddress().getHostAddress();
        }
        
        return "unknown";
    }

    @Override
    public int getOrder() {
        return -100; // Execute early in the filter chain
    }
}
