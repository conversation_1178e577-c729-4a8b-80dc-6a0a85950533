{"dashboard": {"id": null, "title": "TecnoDrive Platform Overview", "tags": ["tecnodrive", "overview", "platform"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up", "legendFormat": "{{job}} - {{instance}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}}, {"id": 2, "title": "Request Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (job)", "legendFormat": "{{job}}", "refId": "A"}], "yAxes": [{"label": "Requests/sec", "min": 0}, {"show": false}], "legend": {"show": true, "values": true, "current": true}}, {"id": 3, "title": "Response Time P95", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (job, le))", "legendFormat": "{{job}} P95", "refId": "A"}], "yAxes": [{"label": "Seconds", "min": 0}, {"show": false}]}, {"id": 4, "title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (job) / sum(rate(http_requests_total[5m])) by (job)", "legendFormat": "{{job}} Error Rate", "refId": "A"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}, {"show": false}], "thresholds": [{"value": 0.01, "colorMode": "critical", "op": "gt"}]}, {"id": 5, "title": "Active Rides", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 16}, "targets": [{"expr": "sum(rides_active_total)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 6, "title": "Available Drivers", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 16}, "targets": [{"expr": "sum(drivers_available_total)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 7, "title": "Revenue (Last 24h)", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 20}, "targets": [{"expr": "sum(increase(payments_completed_amount_total[24h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "currencyUSD"}}}, {"id": 8, "title": "Completed Rides (Last 24h)", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 20}, "targets": [{"expr": "sum(increase(rides_completed_total[24h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 9, "title": "Database Connection Pool", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "hikaricp_connections_active", "legendFormat": "{{job}} Active", "refId": "A"}, {"expr": "hikaricp_connections_idle", "legendFormat": "{{job}} Idle", "refId": "B"}, {"expr": "hikaricp_connections_max", "legendFormat": "{{job}} Max", "refId": "C"}], "yAxes": [{"label": "Connections", "min": 0}, {"show": false}]}, {"id": 10, "title": "JVM Memory Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "jvm_memory_used_bytes{area=\"heap\"}", "legendFormat": "{{job}} Heap Used", "refId": "A"}, {"expr": "jvm_memory_max_bytes{area=\"heap\"}", "legendFormat": "{{job}} Heap Max", "refId": "B"}], "yAxes": [{"label": "Bytes", "min": 0}, {"show": false}]}, {"id": 11, "title": "Circuit Breaker Status", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "resilience4j_circuitbreaker_state", "format": "table", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "indexByName": {}, "renameByName": {"job": "Service", "name": "Circuit Breaker", "state": "State", "Value": "Status"}}}]}, {"id": 12, "title": "Top Slow Endpoints", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "topk(10, histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (job, uri, le)))", "format": "table", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "le": true}, "renameByName": {"job": "Service", "uri": "Endpoint", "Value": "P95 Latency (s)"}}}]}, {"id": 13, "title": "Distributed Tracing Metrics", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "rate(tracing_traces_generated_total[5m])", "legendFormat": "{{job}} Traces Generated/sec", "refId": "A"}, {"expr": "rate(tracing_traces_dropped_total[5m])", "legendFormat": "{{job}} Traces Dropped/sec", "refId": "B"}], "yAxes": [{"label": "Traces/sec", "min": 0}, {"show": false}]}, {"id": 14, "title": "Business Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 48}, "collapsed": false}, {"id": 15, "title": "Ride Completion Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 49}, "targets": [{"expr": "sum(rate(rides_completed_total[1h])) / sum(rate(rides_requested_total[1h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.9}]}, "unit": "percentunit", "min": 0, "max": 1}}}, {"id": 16, "title": "Average Rating", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 49}, "targets": [{"expr": "avg(ride_rating_average)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 3.5}, {"color": "green", "value": 4.0}]}, "unit": "short", "min": 0, "max": 5}}}, {"id": 17, "title": "Payment Success Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 49}, "targets": [{"expr": "sum(rate(payments_completed_total[1h])) / sum(rate(payments_attempted_total[1h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.95}, {"color": "green", "value": 0.98}]}, "unit": "percentunit", "min": 0, "max": 1}}}, {"id": 18, "title": "Driver Utilization", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 49}, "targets": [{"expr": "sum(drivers_active_total) / sum(drivers_total)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.3}, {"color": "green", "value": 0.6}]}, "unit": "percentunit", "min": 0, "max": 1}}}], "templating": {"list": [{"name": "service", "type": "query", "query": "label_values(up, job)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "instance", "type": "query", "query": "label_values(up{job=~\"$service\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up[1m]) > 0", "titleFormat": "Service {{job}} restarted", "textFormat": "Instance {{instance}} restarted"}]}}}