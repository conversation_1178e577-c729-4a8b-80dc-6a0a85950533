import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Badge,
  CircularProgress
} from '@mui/material';
import {
  Business as OperationsIcon,
  Assignment as AssignmentIcon,
  Route as RouteIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Timer as TimerIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

interface Operation {
  id: string;
  type: string;
  status: string;
  priority: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  assignedDrivers: string[];
  assignedVehicles: string[];
  coverageArea: string;
  expectedRevenue: number;
  actualRevenue: number;
  customerCount: number;
  completionRate: number;
  efficiency: number;
  customerSatisfaction: number;
}

interface OperationalMetrics {
  totalOperations: number;
  activeOperations: number;
  completedOperations: number;
  averageEfficiency: number;
  totalRevenue: number;
  totalCost: number;
  profitMargin: number;
  customerSatisfaction: number;
  onTimePerformance: number;
  driverUtilization: number;
  vehicleUtilization: number;
  completionRate: number;
  cancellationRate: number;
}

interface Route {
  id: string;
  name: string;
  type: string;
  startLocation: string;
  endLocation: string;
  distance: number;
  estimatedTime: number;
  difficulty: string;
  trafficLevel: string;
  isActive: boolean;
  frequency: number;
  averageLoad: number;
}

interface Assignment {
  id: string;
  operationId: string;
  driverId: string;
  vehicleId: string;
  routeId: string;
  status: string;
  assignedAt: string;
  priority: string;
  estimatedDuration: number;
  actualDuration: number;
  performanceRating: number;
}

const OperationsManagement: React.FC = () => {
  const [operations, setOperations] = useState<Operation[]>([]);
  const [routes, setRoutes] = useState<Route[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [metrics, setMetrics] = useState<OperationalMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedOperation, setSelectedOperation] = useState<Operation | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    priority: '',
    zone: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    loadOperationsData();
  }, [filters]);

  const loadOperationsData = async () => {
    try {
      setLoading(true);

      // Load operations
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const operationsResponse = await fetch(`http://localhost:8101/api/operations?${queryParams}`);
      const operationsData = await operationsResponse.json();
      
      if (operationsData.success) {
        setOperations(operationsData.data);
      }

      // Load routes
      const routesResponse = await fetch('http://localhost:8101/api/operations/routes');
      const routesData = await routesResponse.json();
      
      if (routesData.success) {
        setRoutes(routesData.data);
      }

      // Load assignments
      const assignmentsResponse = await fetch('http://localhost:8101/api/operations/assignments');
      const assignmentsData = await assignmentsResponse.json();
      
      if (assignmentsData.success) {
        setAssignments(assignmentsData.data);
      }

      // Load metrics
      const metricsResponse = await fetch('http://localhost:8101/api/operations/metrics');
      const metricsResult = await metricsResponse.json();
      
      if (metricsResult.success) {
        setMetrics(metricsResult.data);
      }

    } catch (error) {
      console.error('Failed to load operations data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleViewDetails = (operation: Operation) => {
    setSelectedOperation(operation);
    setDetailsDialogOpen(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'planned': 'info',
      'active': 'primary',
      'completed': 'success',
      'cancelled': 'error',
      'delayed': 'warning'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'planned': 'مخطط',
      'active': 'نشط',
      'completed': 'مكتمل',
      'cancelled': 'ملغي',
      'delayed': 'متأخر'
    };
    return labels[status] || status;
  };

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'passenger_transport': 'نقل ركاب',
      'parcel_delivery': 'توصيل طرود',
      'mixed_service': 'خدمة مختلطة',
      'emergency_response': 'استجابة طوارئ'
    };
    return labels[type] || type;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'emergency': return '#d32f2f';
      case 'urgent': return '#f44336';
      case 'high': return '#ff9800';
      case 'normal': return '#4caf50';
      case 'low': return '#9e9e9e';
      default: return '#9e9e9e';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل بيانات العمليات...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <OperationsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          إدارة العمليات التشغيلية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة للعمليات والمسارات والتعيينات
        </Typography>
      </Box>

      {/* Metrics Cards */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <OperationsIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.totalOperations}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي العمليات
                    </Typography>
                    <Typography variant="caption" color="primary.main">
                      نشط: {metrics.activeOperations}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.averageEfficiency.toFixed(1)}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط الكفاءة
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      في الوقت: {metrics.onTimePerformance.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CheckIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.completionRate.toFixed(1)}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل الإنجاز
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      مكتمل: {metrics.completedOperations}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <AnalyticsIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.profitMargin.toFixed(1)}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      هامش الربح
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      إيرادات: {formatCurrency(metrics.totalRevenue)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Utilization Metrics */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>استخدام السائقين</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.driverUtilization}
                  sx={{ height: 10, borderRadius: 5, mb: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {metrics.driverUtilization.toFixed(1)}% من السائقين نشطون
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>استخدام المركبات</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.vehicleUtilization}
                  color="secondary"
                  sx={{ height: 10, borderRadius: 5, mb: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {metrics.vehicleUtilization.toFixed(1)}% من المركبات نشطة
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{"رضا العملاء"}</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.customerSatisfaction * 20}
                  color="success"
                  sx={{ height: 10, borderRadius: 5, mb: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {metrics.customerSatisfaction.toFixed(1)} من 5 نجوم
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="planned">مخطط</MenuItem>
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="completed">مكتمل</MenuItem>
                <MenuItem value="cancelled">ملغي</MenuItem>
                <MenuItem value="delayed">متأخر</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>النوع</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="النوع"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="passenger_transport">نقل ركاب</MenuItem>
                <MenuItem value="parcel_delivery">توصيل طرود</MenuItem>
                <MenuItem value="mixed_service">خدمة مختلطة</MenuItem>
                <MenuItem value="emergency_response">استجابة طوارئ</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الأولوية</InputLabel>
              <Select
                value={filters.priority}
                onChange={(e) => handleFilterChange('priority', e.target.value)}
                label="الأولوية"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="low">منخفضة</MenuItem>
                <MenuItem value="normal">عادية</MenuItem>
                <MenuItem value="high">عالية</MenuItem>
                <MenuItem value="urgent">عاجلة</MenuItem>
                <MenuItem value="emergency">طوارئ</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="من تاريخ"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="إلى تاريخ"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadOperationsData}
            >
              تحديث
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs for Different Views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="العمليات" icon={<OperationsIcon />} />
          <Tab label="المسارات" icon={<RouteIcon />} />
          <Tab label="التعيينات" icon={<AssignmentIcon />} />
          <Tab label="الجدولة" icon={<ScheduleIcon />} />
        </Tabs>
      </Paper>

      {/* Operations Tab */}
      {selectedTab === 0 && (
        <Paper>
          {loading && <LinearProgress />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>معرف العملية</TableCell>
                  <TableCell>العنوان</TableCell>
                  <TableCell>النوع</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الأولوية</TableCell>
                  <TableCell>منطقة التغطية</TableCell>
                  <TableCell>الكفاءة</TableCell>
                  <TableCell>الإيرادات</TableCell>
                  <TableCell>العملاء</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {operations
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((operation) => (
                  <TableRow key={operation.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {operation.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {operation.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getTypeLabel(operation.type)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(operation.status)}
                        color={getStatusColor(operation.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getPriorityColor(operation.priority),
                            mr: 1
                          }}
                        />
                        {operation.priority}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {operation.coverageArea}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: 80 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={operation.efficiency}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                        <Typography variant="caption">
                          {operation.efficiency.toFixed(0)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(operation.actualRevenue)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        متوقع: {formatCurrency(operation.expectedRevenue)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {operation.customerCount}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton size="small" onClick={() => handleViewDetails(operation)}>
                          <ViewIcon />
                        </IconButton>
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton size="small">
                          <AssignmentIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={operations.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
          />
        </Paper>
      )}

      {/* Routes Tab */}
      {selectedTab === 1 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم المسار</TableCell>
                  <TableCell>النوع</TableCell>
                  <TableCell>المنشأ</TableCell>
                  <TableCell>الوجهة</TableCell>
                  <TableCell>المسافة</TableCell>
                  <TableCell>الوقت المتوقع</TableCell>
                  <TableCell>الصعوبة</TableCell>
                  <TableCell>حركة المرور</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>التكرار</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {routes.slice(0, 10).map((route) => (
                  <TableRow key={route.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {route.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {route.type}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" noWrap>
                        {route.startLocation}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" noWrap>
                        {route.endLocation}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {route.distance.toFixed(1)} كم
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {route.estimatedTime.toFixed(0)} دقيقة
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={route.difficulty}
                        color={route.difficulty === 'easy' ? 'success' : route.difficulty === 'medium' ? 'warning' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={route.trafficLevel}
                        color={route.trafficLevel === 'low' ? 'success' : route.trafficLevel === 'medium' ? 'warning' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={route.isActive ? 'نشط' : 'غير نشط'}
                        color={route.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {route.frequency} رحلة/يوم
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Assignments Tab */}
      {selectedTab === 2 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>معرف التعيين</TableCell>
                  <TableCell>العملية</TableCell>
                  <TableCell>السائق</TableCell>
                  <TableCell>المركبة</TableCell>
                  <TableCell>المسار</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الأولوية</TableCell>
                  <TableCell>المدة المتوقعة</TableCell>
                  <TableCell>تقييم الأداء</TableCell>
                  <TableCell>تاريخ التعيين</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {assignments.slice(0, 10).map((assignment) => (
                  <TableRow key={assignment.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {assignment.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.operationId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.driverId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.vehicleId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.routeId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={assignment.status}
                        color={getStatusColor(assignment.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getPriorityColor(assignment.priority),
                            mr: 1
                          }}
                        />
                        {assignment.priority}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.estimatedDuration} دقيقة
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {assignment.performanceRating.toFixed(1)} ⭐
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(assignment.assignedAt).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Scheduling Tab */}
      {selectedTab === 3 && (
        <Paper>
          <Box p={3} textAlign="center">
            <ScheduleIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              نظام الجدولة المتقدم
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إدارة جداول العمل والمناوبات (قيد التطوير)
            </Typography>
          </Box>
        </Paper>
      )}

      {/* Operation Details Dialog */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل العملية</DialogTitle>
        <DialogContent>
          {selectedOperation && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">العنوان</Typography>
                <Typography variant="body1">{selectedOperation.title}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">النوع</Typography>
                <Typography variant="body1">{getTypeLabel(selectedOperation.type)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">الحالة</Typography>
                <Chip
                  label={getStatusLabel(selectedOperation.status)}
                  color={getStatusColor(selectedOperation.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">منطقة التغطية</Typography>
                <Typography variant="body1">{selectedOperation.coverageArea}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">الإيرادات المتوقعة</Typography>
                <Typography variant="body1">{formatCurrency(selectedOperation.expectedRevenue)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">الإيرادات الفعلية</Typography>
                <Typography variant="body1" color="success.main">
                  {formatCurrency(selectedOperation.actualRevenue)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">عدد العملاء</Typography>
                <Typography variant="body1">{selectedOperation.customerCount}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">معدل الإنجاز</Typography>
                <Typography variant="body1">{selectedOperation.completionRate.toFixed(1)}%</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">الكفاءة</Typography>
                <Typography variant="body1">{selectedOperation.efficiency.toFixed(1)}%</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">رضا العملاء</Typography>
                <Typography variant="body1">{selectedOperation.customerSatisfaction.toFixed(1)} ⭐</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">الوصف</Typography>
                <Typography variant="body1">{selectedOperation.description}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>إغلاق</Button>
          <Button variant="contained">تعديل</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OperationsManagement;
