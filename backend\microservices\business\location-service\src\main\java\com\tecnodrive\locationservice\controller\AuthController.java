package com.tecnodrive.locationservice.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        String email = loginRequest.get("email");
        String password = loginRequest.get("password");
        
        // For demo purposes, accept any email and password
        Map<String, Object> response = new HashMap<>();
        
        if (email != null && !email.isEmpty() && password != null && !password.isEmpty()) {
            response.put("success", true);
            response.put("message", "تم تسجيل الدخول بنجاح");
            response.put("user", Map.of(
                "id", 1,
                "email", email,
                "name", "مدير النظام",
                "role", "admin"
            ));
            response.put("token", "demo-token-" + System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "البريد الإلكتروني وكلمة المرور مطلوبان");
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "تم تسجيل الخروج بنجاح");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/me")
    public ResponseEntity<Map<String, Object>> getCurrentUser() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("user", Map.of(
            "id", 1,
            "email", "<EMAIL>",
            "name", "مدير النظام",
            "role", "admin"
        ));
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getAuthStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("authenticated", true);
        response.put("service", "auth");
        response.put("status", "UP");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
