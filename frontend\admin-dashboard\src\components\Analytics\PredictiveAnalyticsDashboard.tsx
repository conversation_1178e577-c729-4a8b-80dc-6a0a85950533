import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  PredictiveText,
  Analytics,
  Timeline,
  Speed,
  Warning,
  CheckCircle,
  Error,
  Info,
  Refresh,
  Download,
  Map,
  DirectionsCar,
  People,
  AttachMoney,
  Schedule,
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart, 
  Scatter,
  ComposedChart,
  ReferenceLine
} from 'recharts';
import aiService, { 
  PredictiveMaintenanceResult, 
  DemandPredictionResult, 
  CustomerSegmentationResult, 
  RouteOptimizationResult,
  AnomalyDetectionResult
} from '../../services/aiService';

const PredictiveAnalyticsDashboard: React.FC = () => {
  const [maintenancePredictions, setMaintenancePredictions] = useState<PredictiveMaintenanceResult[]>([]);
  const [demandPredictions, setDemandPredictions] = useState<DemandPredictionResult[]>([]);
  const [customerSegments, setCustomerSegments] = useState<CustomerSegmentationResult[]>([]);
  const [routeOptimizations, setRouteOptimizations] = useState<RouteOptimizationResult[]>([]);
  const [anomalies, setAnomalies] = useState<AnomalyDetectionResult | null>(null);
  const [selectedPrediction, setSelectedPrediction] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [selectedLocation, setSelectedLocation] = useState('all');

  const tenantId = localStorage.getItem('tenantId') || 'default';

  useEffect(() => {
    loadPredictiveData();
  }, [selectedTimeframe, selectedLocation]);

  const loadPredictiveData = async () => {
    try {
      setLoading(true);
      
      // Load maintenance predictions for all vehicles
      const vehicleIds = ['vehicle-1', 'vehicle-2', 'vehicle-3']; // This would come from your vehicle service
      const maintenancePromises = vehicleIds.map(id => 
        aiService.predictMaintenance(id).catch(err => {
          console.error(`Error predicting maintenance for ${id}:`, err);
          return null;
        })
      );
      const maintenanceResults = (await Promise.all(maintenancePromises)).filter(Boolean) as PredictiveMaintenanceResult[];
      setMaintenancePredictions(maintenanceResults);

      // Load demand predictions for key locations
      const locations = [
        { lat: 40.7128, lng: -74.0060 }, // NYC
        { lat: 34.0522, lng: -118.2437 }, // LA
        { lat: 41.8781, lng: -87.6298 }, // Chicago
      ];
      const demandPromises = locations.map(location => 
        aiService.predictDemand(location, selectedTimeframe).catch(err => {
          console.error('Error predicting demand:', err);
          return null;
        })
      );
      const demandResults = (await Promise.all(demandPromises)).filter(Boolean) as DemandPredictionResult[];
      setDemandPredictions(demandResults);

      // Load customer segmentation
      const customerIds = ['customer-1', 'customer-2', 'customer-3']; // This would come from your CRM service
      const segmentationResults = await aiService.batchCustomerSegmentation(customerIds);
      setCustomerSegments(segmentationResults);

      // Load anomaly detection
      const anomalyData = await aiService.detectAnomalies({
        entityType: 'system',
        entityId: 'platform',
        metrics: [], // This would be populated with actual metrics
        timeframe: selectedTimeframe,
      });
      setAnomalies(anomalyData);

    } catch (error) {
      console.error('Error loading predictive data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePredictionClick = (prediction: any) => {
    setSelectedPrediction(prediction);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  const getRiskColor = (risk: number) => {
    if (risk >= 80) return 'error';
    if (risk >= 60) return 'warning';
    if (risk >= 40) return 'info';
    return 'success';
  };

  const formatPredictionData = (predictions: any[]) => {
    return predictions.map((pred, index) => ({
      name: `Prediction ${index + 1}`,
      confidence: pred.confidence * 100,
      value: pred.failureProbability || pred.predictedDemand || pred.lifetimeValue || 0,
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <Typography>Loading Predictive Analytics...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Predictive Analytics Dashboard
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              label="Timeframe"
            >
              <MenuItem value="1d">1 Day</MenuItem>
              <MenuItem value="7d">7 Days</MenuItem>
              <MenuItem value="30d">30 Days</MenuItem>
              <MenuItem value="90d">90 Days</MenuItem>
            </Select>
          </FormControl>
          <IconButton onClick={loadPredictiveData} color="primary">
            <Refresh />
          </IconButton>
          <Button startIcon={<Download />} variant="outlined">
            Export
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <DirectionsCar color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Vehicles at Risk
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {maintenancePredictions.filter(p => p.failureProbability > 0.7).length}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Demand Forecast
                  </Typography>
                  <Typography variant="h5" color="info.main">
                    {demandPredictions.length > 0 
                      ? Math.round(demandPredictions.reduce((sum, p) => sum + p.predictedDemand, 0) / demandPredictions.length)
                      : 0
                    }
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <People color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    VIP Customers
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {customerSegments.filter(c => c.segment === 'vip').length}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Warning color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Anomalies Detected
                  </Typography>
                  <Typography variant="h5" color="error.main">
                    {anomalies?.anomalies.length || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Predictive Maintenance Section */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Predictive Maintenance Alerts
              </Typography>
              <List>
                {maintenancePredictions.slice(0, 5).map((prediction) => (
                  <React.Fragment key={prediction.vehicleId}>
                    <ListItem
                      button
                      onClick={() => handlePredictionClick(prediction)}
                    >
                      <ListItemIcon>
                        <DirectionsCar color={prediction.failureProbability > 0.7 ? 'error' : 'warning'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={`Vehicle ${prediction.vehicleId} - ${prediction.component}`}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Failure Probability: {(prediction.failureProbability * 100).toFixed(1)}%
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Predicted Date: {new Date(prediction.predictedFailureDate).toLocaleDateString()}
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={prediction.failureProbability * 100}
                              color={prediction.failureProbability > 0.7 ? 'error' : 'warning'}
                              sx={{ mt: 1 }}
                            />
                          </Box>
                        }
                      />
                      <Chip
                        label={`${(prediction.confidence * 100).toFixed(0)}%`}
                        size="small"
                        color={getConfidenceColor(prediction.confidence) as any}
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Maintenance Prediction Trends
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={formatPredictionData(maintenancePredictions)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <RechartsTooltip />
                  <Bar yAxisId="left" dataKey="value" fill="#8884d8" name="Failure Probability %" />
                  <Line yAxisId="right" type="monotone" dataKey="confidence" stroke="#ff7300" name="Confidence %" />
                  <ReferenceLine yAxisId="left" y={70} stroke="red" strokeDasharray="5 5" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Demand Prediction Section */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Demand Prediction by Location
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={demandPredictions.map((pred, index) => ({
                  location: pred.location.area || `Location ${index + 1}`,
                  predicted: pred.predictedDemand,
                  confidence: pred.confidence * 100,
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="location" />
                  <YAxis />
                  <RechartsTooltip />
                  <Area type="monotone" dataKey="predicted" stackId="1" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Demand Factors
              </Typography>
              <List>
                {demandPredictions[0]?.factors.slice(0, 5).map((factor, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={factor.factor}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="textSecondary">
                            Impact: {(factor.impact * 100).toFixed(1)}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={Math.abs(factor.impact) * 100}
                            color={factor.impact > 0 ? 'success' : 'error'}
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                )) || []}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Customer Segmentation Section */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Customer Segmentation Analysis
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <ScatterChart data={customerSegments.map(customer => ({
                  lifetimeValue: customer.lifetimeValue,
                  churnProbability: customer.churnProbability * 100,
                  segment: customer.segment,
                }))}>
                  <CartesianGrid />
                  <XAxis dataKey="lifetimeValue" name="Lifetime Value" />
                  <YAxis dataKey="churnProbability" name="Churn Probability %" />
                  <RechartsTooltip cursor={{ strokeDasharray: '3 3' }} />
                  <Scatter name="Customers" dataKey="churnProbability" fill="#8884d8" />
                </ScatterChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Customer Segments Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={Object.entries(
                  customerSegments.reduce((acc, customer) => {
                    acc[customer.segment] = (acc[customer.segment] || 0) + 1;
                    return acc;
                  }, {} as Record<string, number>)
                ).map(([segment, count]) => ({ segment, count }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="segment" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="count" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Anomaly Detection Section */}
      {anomalies && anomalies.anomalies.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Anomaly Detection Results
            </Typography>
            <Grid container spacing={2}>
              {anomalies.anomalies.slice(0, 6).map((anomaly) => (
                <Grid item xs={12} md={6} lg={4} key={anomaly.id}>
                  <Alert
                    severity={anomaly.severity as any}
                    action={
                      <Chip
                        label={`${(anomaly.confidence * 100).toFixed(0)}%`}
                        size="small"
                        variant="outlined"
                      />
                    }
                  >
                    <Typography variant="body2">
                      <strong>{anomaly.type.replace('_', ' ').toUpperCase()}</strong>
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {anomaly.description}
                    </Typography>
                    <Typography variant="caption" display="block">
                      Detected: {new Date(anomaly.detectedAt).toLocaleString()}
                    </Typography>
                  </Alert>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Prediction Detail Dialog */}
      <Dialog
        open={!!selectedPrediction}
        onClose={() => setSelectedPrediction(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Prediction Details
        </DialogTitle>
        <DialogContent>
          {selectedPrediction && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedPrediction.vehicleId ? 
                  `Vehicle ${selectedPrediction.vehicleId} - ${selectedPrediction.component}` :
                  selectedPrediction.location?.area || 'Prediction Details'
                }
              </Typography>
              
              {selectedPrediction.failureProbability && (
                <Box mb={2}>
                  <Typography variant="body2" color="textSecondary">Failure Probability</Typography>
                  <LinearProgress
                    variant="determinate"
                    value={selectedPrediction.failureProbability * 100}
                    color={selectedPrediction.failureProbability > 0.7 ? 'error' : 'warning'}
                    sx={{ mt: 1, mb: 1 }}
                  />
                  <Typography variant="body2">
                    {(selectedPrediction.failureProbability * 100).toFixed(1)}%
                  </Typography>
                </Box>
              )}

              {selectedPrediction.recommendations && (
                <Box mb={2}>
                  <Typography variant="h6" gutterBottom>
                    Recommendations
                  </Typography>
                  <List>
                    {selectedPrediction.recommendations.map((rec: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircle color="success" />
                        </ListItemIcon>
                        <ListItemText primary={rec} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {selectedPrediction.factors && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Contributing Factors
                  </Typography>
                  <List>
                    {selectedPrediction.factors.map((factor: any, index: number) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={factor.factor}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="textSecondary">
                                {factor.description}
                              </Typography>
                              <Typography variant="body2">
                                Impact: {(factor.impact * 100).toFixed(1)}%
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedPrediction(null)}>
            Close
          </Button>
          <Button variant="contained" color="primary">
            Take Action
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PredictiveAnalyticsDashboard;
