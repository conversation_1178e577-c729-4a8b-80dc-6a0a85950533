package com.tecnodrive.parcelservice.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity لعناصر الطرود
 */
@Entity
@Table(name = "parcel_items", indexes = {
    @Index(name = "idx_item_parcel_id", columnList = "parcel_id"),
    @Index(name = "idx_item_category", columnList = "category"),
    @Index(name = "idx_item_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParcelItemEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "item_id")
    private String itemId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parcel_id", nullable = false)
    private ParcelEntity parcel;
    
    @Column(name = "item_name", nullable = false, length = 100)
    private String itemName;
    
    @Column(name = "item_description", length = 500)
    private String itemDescription;
    
    @Column(name = "quantity", nullable = false)
    @Builder.Default
    private Integer quantity = 1;
    
    @Column(name = "weight_kg", precision = 8, scale = 3)
    private BigDecimal weightKg;
    
    @Column(name = "value", precision = 12, scale = 2)
    private BigDecimal value;
    
    @Column(name = "category", length = 50)
    private String category;
    
    @Column(name = "subcategory", length = 50)
    private String subcategory;
    
    @Column(name = "brand", length = 50)
    private String brand;
    
    @Column(name = "model", length = 50)
    private String model;
    
    @Column(name = "color", length = 30)
    private String color;
    
    @Column(name = "size", length = 30)
    private String size;
    
    @Column(name = "material", length = 50)
    private String material;
    
    @Column(name = "fragile")
    @Builder.Default
    private Boolean fragile = false;

    @Column(name = "hazardous")
    @Builder.Default
    private Boolean hazardous = false;

    @Column(name = "perishable")
    @Builder.Default
    private Boolean perishable = false;

    @Column(name = "liquid")
    @Builder.Default
    private Boolean liquid = false;

    @Column(name = "electronic")
    @Builder.Default
    private Boolean electronic = false;

    @Column(name = "requires_special_handling")
    @Builder.Default
    private Boolean requiresSpecialHandling = false;
    
    @Column(name = "temperature_sensitive")
    @Builder.Default
    private Boolean temperatureSensitive = false;
    
    @Column(name = "min_temperature")
    private Double minTemperature;
    
    @Column(name = "max_temperature")
    private Double maxTemperature;
    
    @Column(name = "sku", length = 50)
    private String sku; // Stock Keeping Unit
    
    @Column(name = "barcode", length = 50)
    private String barcode;
    
    @Column(name = "serial_number", length = 100)
    private String serialNumber;
    
    @Column(name = "country_of_origin", length = 50)
    private String countryOfOrigin;
    
    @Column(name = "manufacturer", length = 100)
    private String manufacturer;
    
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;
    
    @Column(name = "warranty_period_months")
    private Integer warrantyPeriodMonths;
    
    @Column(name = "customs_code", length = 20)
    private String customsCode; // HS Code for customs
    
    @Column(name = "customs_value", precision = 12, scale = 2)
    private BigDecimal customsValue;
    
    @Column(name = "insurance_required")
    @Builder.Default
    private Boolean insuranceRequired = false;
    
    @Column(name = "insurance_value", precision = 12, scale = 2)
    private BigDecimal insuranceValue;
    
    @Column(name = "handling_instructions", length = 500)
    private String handlingInstructions;
    
    @Column(name = "storage_instructions", length = 500)
    private String storageInstructions;
    
    @Column(name = "item_condition", length = 20)
    private String itemCondition; // NEW, USED, REFURBISHED, DAMAGED
    
    @Column(name = "packaging_type", length = 50)
    private String packagingType;
    
    @Column(name = "unit_of_measure", length = 20)
    private String unitOfMeasure; // PIECE, KG, LITER, etc.
    
    @Column(name = "dimensions_length_cm")
    private Integer dimensionsLengthCm;
    
    @Column(name = "dimensions_width_cm")
    private Integer dimensionsWidthCm;
    
    @Column(name = "dimensions_height_cm")
    private Integer dimensionsHeightCm;
    
    @Column(name = "photos", columnDefinition = "TEXT")
    private String photos; // JSON array of photo URLs
    
    @Column(name = "documents", columnDefinition = "TEXT")
    private String documents; // JSON array of document URLs
    
    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags; // JSON array of tags
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * إنشاء عنصر طرد جديد
     */
    public static ParcelItemEntity createItem(
            ParcelEntity parcel,
            String itemName,
            String itemDescription,
            Integer quantity,
            BigDecimal weightKg,
            BigDecimal value,
            String category,
            String createdBy) {
        
        return ParcelItemEntity.builder()
                .parcel(parcel)
                .itemName(itemName)
                .itemDescription(itemDescription)
                .quantity(quantity)
                .weightKg(weightKg)
                .value(value)
                .category(category)
                .createdBy(createdBy)
                .fragile(false)
                .hazardous(false)
                .perishable(false)
                .liquid(false)
                .electronic(false)
                .requiresSpecialHandling(false)
                .temperatureSensitive(false)
                .insuranceRequired(false)
                .itemCondition("NEW")
                .unitOfMeasure("PIECE")
                .build();
    }
    
    /**
     * حساب الوزن الإجمالي
     */
    public BigDecimal getTotalWeight() {
        if (weightKg != null && quantity != null) {
            return weightKg.multiply(BigDecimal.valueOf(quantity));
        }
        return BigDecimal.ZERO;
    }
    
    /**
     * حساب القيمة الإجمالية
     */
    public BigDecimal getTotalValue() {
        if (value != null && quantity != null) {
            return value.multiply(BigDecimal.valueOf(quantity));
        }
        return BigDecimal.ZERO;
    }
    
    /**
     * حساب الحجم الإجمالي
     */
    public Double getTotalVolume() {
        if (dimensionsLengthCm != null && dimensionsWidthCm != null && 
            dimensionsHeightCm != null && quantity != null) {
            double volume = dimensionsLengthCm * dimensionsWidthCm * dimensionsHeightCm;
            return volume * quantity;
        }
        return 0.0;
    }
    
    /**
     * التحقق من الحاجة للمعالجة الخاصة
     */
    public boolean needsSpecialHandling() {
        return Boolean.TRUE.equals(fragile) ||
               Boolean.TRUE.equals(hazardous) ||
               Boolean.TRUE.equals(perishable) ||
               Boolean.TRUE.equals(liquid) ||
               Boolean.TRUE.equals(temperatureSensitive) ||
               Boolean.TRUE.equals(requiresSpecialHandling);
    }
    
    /**
     * التحقق من انتهاء الصلاحية
     */
    public boolean isExpired() {
        return expiryDate != null && LocalDateTime.now().isAfter(expiryDate);
    }
    
    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public boolean isNearExpiry(int daysThreshold) {
        if (expiryDate == null) return false;
        return LocalDateTime.now().plusDays(daysThreshold).isAfter(expiryDate);
    }
    
    /**
     * تحديد مستوى المخاطر
     */
    public String getRiskLevel() {
        if (Boolean.TRUE.equals(hazardous)) {
            return "HIGH";
        } else if (Boolean.TRUE.equals(fragile) || Boolean.TRUE.equals(liquid) || 
                   Boolean.TRUE.equals(electronic)) {
            return "MEDIUM";
        } else if (Boolean.TRUE.equals(perishable) || Boolean.TRUE.equals(temperatureSensitive)) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
    
    /**
     * الحصول على تعليمات المعالجة الكاملة
     */
    public String getFullHandlingInstructions() {
        StringBuilder instructions = new StringBuilder();
        
        if (Boolean.TRUE.equals(fragile)) {
            instructions.append("هش - يُرجى التعامل بحذر. ");
        }
        
        if (Boolean.TRUE.equals(hazardous)) {
            instructions.append("مواد خطرة - يتطلب معالجة خاصة. ");
        }
        
        if (Boolean.TRUE.equals(perishable)) {
            instructions.append("قابل للتلف - يُرجى التبريد. ");
        }
        
        if (Boolean.TRUE.equals(liquid)) {
            instructions.append("سائل - يُرجى الحفظ في وضع عمودي. ");
        }
        
        if (Boolean.TRUE.equals(electronic)) {
            instructions.append("إلكتروني - يُرجى الحماية من الرطوبة. ");
        }
        
        if (Boolean.TRUE.equals(temperatureSensitive)) {
            instructions.append("حساس للحرارة");
            if (minTemperature != null && maxTemperature != null) {
                instructions.append(" (").append(minTemperature).append("°C - ")
                          .append(maxTemperature).append("°C)");
            }
            instructions.append(". ");
        }
        
        if (handlingInstructions != null && !handlingInstructions.trim().isEmpty()) {
            instructions.append(handlingInstructions);
        }
        
        return instructions.toString().trim();
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return parcel != null &&
               itemName != null && !itemName.trim().isEmpty() &&
               quantity != null && quantity > 0 &&
               (weightKg == null || weightKg.compareTo(BigDecimal.ZERO) >= 0) &&
               (value == null || value.compareTo(BigDecimal.ZERO) >= 0);
    }
    
    /**
     * تحديث الأبعاد
     */
    public void updateDimensions(Integer length, Integer width, Integer height) {
        this.dimensionsLengthCm = length;
        this.dimensionsWidthCm = width;
        this.dimensionsHeightCm = height;
    }
    
    /**
     * تحديث متطلبات درجة الحرارة
     */
    public void setTemperatureRequirements(Double minTemp, Double maxTemp) {
        this.temperatureSensitive = true;
        this.minTemperature = minTemp;
        this.maxTemperature = maxTemp;
    }
    
    /**
     * إضافة تأمين
     */
    public void addInsurance(BigDecimal insuranceValue) {
        this.insuranceRequired = true;
        this.insuranceValue = insuranceValue;
    }
}
