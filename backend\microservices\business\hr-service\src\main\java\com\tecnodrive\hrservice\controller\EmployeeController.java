package com.tecnodrive.hrservice.controller;

import com.tecnodrive.common.dto.common.ApiResponse;
import com.tecnodrive.hrservice.dto.EmployeeRequest;
import com.tecnodrive.hrservice.dto.EmployeeResponse;
import com.tecnodrive.hrservice.entity.Employee;
import com.tecnodrive.hrservice.service.EmployeeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Employee Controller
 * 
 * REST API endpoints for employee management
 */
@RestController
@RequestMapping("/api/hr/employees")
@RequiredArgsConstructor
@Slf4j
public class EmployeeController {

    private final EmployeeService employeeService;

    /**
     * Create a new employee
     */
    @PostMapping
    public ResponseEntity<ApiResponse<EmployeeResponse>> createEmployee(@Valid @RequestBody EmployeeRequest request) {
        log.info("Creating employee with email: {}", request.getEmail());
        
        try {
            EmployeeResponse response = employeeService.createEmployee(request);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error creating employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create employee: " + e.getMessage()));
        }
    }

    /**
     * Get employee by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<EmployeeResponse>> getEmployee(@PathVariable String id) {
        log.debug("Getting employee by ID: {}", id);
        
        try {
            EmployeeResponse response = employeeService.getEmployee(id);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employee: " + e.getMessage()));
        }
    }

    /**
     * Get employee by email
     */
    @GetMapping("/email/{email}")
    public ResponseEntity<ApiResponse<EmployeeResponse>> getEmployeeByEmail(@PathVariable String email) {
        log.debug("Getting employee by email: {}", email);
        
        try {
            EmployeeResponse response = employeeService.getEmployeeByEmail(email);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employee by email: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employee: " + e.getMessage()));
        }
    }

    /**
     * Get employee by employee number
     */
    @GetMapping("/number/{employeeNumber}")
    public ResponseEntity<ApiResponse<EmployeeResponse>> getEmployeeByEmployeeNumber(@PathVariable String employeeNumber) {
        log.debug("Getting employee by employee number: {}", employeeNumber);
        
        try {
            EmployeeResponse response = employeeService.getEmployeeByEmployeeNumber(employeeNumber);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employee by employee number: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employee: " + e.getMessage()));
        }
    }

    /**
     * Get all employees with pagination
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<EmployeeResponse>>> getEmployees(Pageable pageable) {
        log.debug("Getting employees with pagination: {}", pageable);
        
        try {
            Page<EmployeeResponse> response = employeeService.getEmployees(pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employees: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees: " + e.getMessage()));
        }
    }

    /**
     * Get employees by company
     */
    @GetMapping("/company/{companyId}")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getEmployeesByCompany(@PathVariable String companyId) {
        log.debug("Getting employees by company: {}", companyId);
        
        try {
            List<EmployeeResponse> response = employeeService.getEmployeesByCompany(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employees by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees: " + e.getMessage()));
        }
    }

    /**
     * Get employees by company with pagination
     */
    @GetMapping("/company/{companyId}/paged")
    public ResponseEntity<ApiResponse<Page<EmployeeResponse>>> getEmployeesByCompany(
            @PathVariable String companyId, Pageable pageable) {
        log.debug("Getting employees by company: {} with pagination: {}", companyId, pageable);
        
        try {
            Page<EmployeeResponse> response = employeeService.getEmployeesByCompany(companyId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employees by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees: " + e.getMessage()));
        }
    }

    /**
     * Update employee
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<EmployeeResponse>> updateEmployee(
            @PathVariable String id, @Valid @RequestBody EmployeeRequest request) {
        log.info("Updating employee with ID: {}", id);
        
        try {
            EmployeeResponse response = employeeService.updateEmployee(id, request);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update employee: " + e.getMessage()));
        }
    }

    /**
     * Delete employee
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteEmployee(@PathVariable String id) {
        log.info("Deleting employee with ID: {}", id);
        
        try {
            employeeService.deleteEmployee(id);
            return ResponseEntity.ok(ApiResponse.success(null));
            
        } catch (Exception e) {
            log.error("Error deleting employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete employee: " + e.getMessage()));
        }
    }

    /**
     * Get active employees by company
     */
    @GetMapping("/company/{companyId}/active")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getActiveEmployeesByCompany(@PathVariable String companyId) {
        log.debug("Getting active employees by company: {}", companyId);
        
        try {
            List<EmployeeResponse> response = employeeService.getActiveEmployeesByCompany(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting active employees by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get active employees: " + e.getMessage()));
        }
    }

    /**
     * Get employees by department
     */
    @GetMapping("/company/{companyId}/department/{department}")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getEmployeesByDepartment(
            @PathVariable String companyId, @PathVariable String department) {
        log.debug("Getting employees by company: {} and department: {}", companyId, department);
        
        try {
            List<EmployeeResponse> response = employeeService.getEmployeesByDepartment(companyId, department);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employees by department: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees by department: " + e.getMessage()));
        }
    }

    /**
     * Get employees by manager
     */
    @GetMapping("/manager/{managerId}")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getEmployeesByManager(@PathVariable String managerId) {
        log.debug("Getting employees by manager: {}", managerId);
        
        try {
            List<EmployeeResponse> response = employeeService.getEmployeesByManager(managerId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting employees by manager: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees by manager: " + e.getMessage()));
        }
    }

    /**
     * Update employee status
     */
    @PatchMapping("/{employeeId}/status")
    public ResponseEntity<ApiResponse<EmployeeResponse>> updateEmployeeStatus(
            @PathVariable String employeeId, @RequestParam Employee.EmployeeStatus status) {
        log.info("Updating employee {} status to {}", employeeId, status);
        
        try {
            EmployeeResponse response = employeeService.updateEmployeeStatus(employeeId, status);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating employee status: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update employee status: " + e.getMessage()));
        }
    }

    /**
     * Assign manager to employee
     */
    @PostMapping("/{employeeId}/assign-manager/{managerId}")
    public ResponseEntity<ApiResponse<EmployeeResponse>> assignManager(
            @PathVariable String employeeId, @PathVariable String managerId) {
        log.info("Assigning manager {} to employee {}", managerId, employeeId);
        
        try {
            EmployeeResponse response = employeeService.assignManager(employeeId, managerId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error assigning manager: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to assign manager: " + e.getMessage()));
        }
    }

    /**
     * Update employee salary
     */
    @PatchMapping("/{employeeId}/salary")
    public ResponseEntity<ApiResponse<EmployeeResponse>> updateEmployeeSalary(
            @PathVariable String employeeId, @RequestParam BigDecimal salary) {
        log.info("Updating salary for employee {} to {}", employeeId, salary);
        
        try {
            EmployeeResponse response = employeeService.updateEmployeeSalary(employeeId, salary);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating employee salary: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update employee salary: " + e.getMessage()));
        }
    }

    /**
     * Update performance rating
     */
    @PatchMapping("/{employeeId}/performance")
    public ResponseEntity<ApiResponse<EmployeeResponse>> updatePerformanceRating(
            @PathVariable String employeeId,
            @RequestParam BigDecimal rating,
            @RequestParam(required = false) String notes) {
        log.info("Updating performance rating for employee {} to {}", employeeId, rating);

        try {
            EmployeeResponse response = employeeService.updatePerformanceRating(employeeId, rating, notes);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error updating performance rating: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update performance rating: " + e.getMessage()));
        }
    }

    /**
     * Schedule performance review
     */
    @PostMapping("/{employeeId}/schedule-review")
    public ResponseEntity<ApiResponse<EmployeeResponse>> schedulePerformanceReview(
            @PathVariable String employeeId, @RequestParam LocalDate reviewDate) {
        log.info("Scheduling performance review for employee {} on {}", employeeId, reviewDate);

        try {
            EmployeeResponse response = employeeService.schedulePerformanceReview(employeeId, reviewDate);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error scheduling performance review: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to schedule performance review: " + e.getMessage()));
        }
    }

    /**
     * Get employees on probation
     */
    @GetMapping("/probation")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getEmployeesOnProbation() {
        log.debug("Getting employees on probation");

        try {
            List<EmployeeResponse> response = employeeService.getEmployeesOnProbation();
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error getting employees on probation: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees on probation: " + e.getMessage()));
        }
    }

    /**
     * Get employees with due performance reviews
     */
    @GetMapping("/reviews/due")
    public ResponseEntity<ApiResponse<List<EmployeeResponse>>> getEmployeesWithDuePerformanceReviews() {
        log.debug("Getting employees with due performance reviews");

        try {
            List<EmployeeResponse> response = employeeService.getEmployeesWithDuePerformanceReviews();
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error getting employees with due performance reviews: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employees with due performance reviews: " + e.getMessage()));
        }
    }

    /**
     * Search employees
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<EmployeeResponse>>> searchEmployees(
            @RequestParam String searchTerm, Pageable pageable) {
        log.debug("Searching employees with term: {}", searchTerm);

        try {
            Page<EmployeeResponse> response = employeeService.searchEmployees(searchTerm, pageable);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error searching employees: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to search employees: " + e.getMessage()));
        }
    }

    /**
     * Get employee statistics
     */
    @GetMapping("/company/{companyId}/statistics")
    public ResponseEntity<ApiResponse<EmployeeResponse.EmployeeStatistics>> getEmployeeStatistics(@PathVariable String companyId) {
        log.debug("Getting employee statistics for company: {}", companyId);

        try {
            EmployeeResponse.EmployeeStatistics response = employeeService.getEmployeeStatistics(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error getting employee statistics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employee statistics: " + e.getMessage()));
        }
    }

    /**
     * Get employee summaries
     */
    @GetMapping("/company/{companyId}/summaries")
    public ResponseEntity<ApiResponse<List<EmployeeResponse.EmployeeSummary>>> getEmployeeSummaries(@PathVariable String companyId) {
        log.debug("Getting employee summaries for company: {}", companyId);

        try {
            List<EmployeeResponse.EmployeeSummary> response = employeeService.getEmployeeSummaries(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error getting employee summaries: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get employee summaries: " + e.getMessage()));
        }
    }

    /**
     * Get department statistics
     */
    @GetMapping("/company/{companyId}/departments/statistics")
    public ResponseEntity<ApiResponse<List<EmployeeResponse.DepartmentStatistics>>> getDepartmentStatistics(@PathVariable String companyId) {
        log.debug("Getting department statistics for company: {}", companyId);

        try {
            List<EmployeeResponse.DepartmentStatistics> response = employeeService.getDepartmentStatistics(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error getting department statistics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get department statistics: " + e.getMessage()));
        }
    }

    /**
     * Update leave balance
     */
    @PatchMapping("/{employeeId}/leave")
    public ResponseEntity<ApiResponse<EmployeeResponse>> updateLeaveBalance(
            @PathVariable String employeeId,
            @RequestParam(required = false) Integer usedAnnualLeave,
            @RequestParam(required = false) Integer usedSickLeave) {
        log.info("Updating leave balance for employee {}", employeeId);

        try {
            EmployeeResponse response = employeeService.updateLeaveBalance(employeeId, usedAnnualLeave, usedSickLeave);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error updating leave balance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update leave balance: " + e.getMessage()));
        }
    }

    /**
     * Terminate employee
     */
    @PostMapping("/{employeeId}/terminate")
    public ResponseEntity<ApiResponse<EmployeeResponse>> terminateEmployee(
            @PathVariable String employeeId,
            @RequestParam LocalDate terminationDate,
            @RequestParam(required = false) String reason) {
        log.info("Terminating employee {} on {}", employeeId, terminationDate);

        try {
            EmployeeResponse response = employeeService.terminateEmployee(employeeId, terminationDate, reason);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error terminating employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to terminate employee: " + e.getMessage()));
        }
    }

    /**
     * Reactivate employee
     */
    @PostMapping("/{employeeId}/reactivate")
    public ResponseEntity<ApiResponse<EmployeeResponse>> reactivateEmployee(@PathVariable String employeeId) {
        log.info("Reactivating employee {}", employeeId);

        try {
            EmployeeResponse response = employeeService.reactivateEmployee(employeeId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Error reactivating employee: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to reactivate employee: " + e.getMessage()));
        }
    }

    /**
     * Check if email is available
     */
    @GetMapping("/check-email/{email}")
    public ResponseEntity<ApiResponse<Boolean>> checkEmailAvailability(@PathVariable String email) {
        log.debug("Checking email availability: {}", email);

        try {
            boolean available = employeeService.isEmailAvailable(email);
            return ResponseEntity.ok(ApiResponse.success(available));

        } catch (Exception e) {
            log.error("Error checking email availability: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to check email availability: " + e.getMessage()));
        }
    }

    /**
     * Generate employee number
     */
    @GetMapping("/generate-number/{companyId}")
    public ResponseEntity<ApiResponse<String>> generateEmployeeNumber(@PathVariable String companyId) {
        log.debug("Generating employee number for company: {}", companyId);

        try {
            String employeeNumber = employeeService.generateEmployeeNumber(companyId);
            return ResponseEntity.ok(ApiResponse.success(employeeNumber));

        } catch (Exception e) {
            log.error("Error generating employee number: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to generate employee number: " + e.getMessage()));
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        return ResponseEntity.ok(ApiResponse.success("HR service is running"));
    }
}
