# TecnoDrive Docker Stop Script
# This script stops all TecnoDrive services and cleans up containers

Write-Host "🛑 TecnoDrive Docker Stop Script" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red

# Navigate to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

Write-Host "📁 Working directory: $projectRoot" -ForegroundColor Cyan

# Show current running containers
Write-Host "🐳 Current running containers:" -ForegroundColor Yellow
docker-compose ps

Write-Host ""
Write-Host "🛑 Stopping all TecnoDrive services..." -ForegroundColor Yellow

# Stop and remove containers
docker-compose down

Write-Host "✅ All services stopped successfully!" -ForegroundColor Green

# Optional: Remove volumes (uncomment if you want to clear all data)
# Write-Host "🗑️ Removing volumes..." -ForegroundColor Yellow
# docker-compose down -v

# Optional: Remove images (uncomment if you want to remove built images)
# Write-Host "🗑️ Removing images..." -ForegroundColor Yellow
# docker-compose down --rmi all

# Show remaining containers (should be empty)
Write-Host ""
Write-Host "🐳 Remaining TecnoDrive containers:" -ForegroundColor Cyan
docker ps --filter "name=tecnodrive" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host ""
Write-Host "📝 Cleanup Commands (if needed):" -ForegroundColor Cyan
Write-Host "Remove all containers: docker-compose down --remove-orphans" -ForegroundColor White
Write-Host "Remove volumes: docker-compose down -v" -ForegroundColor White
Write-Host "Remove images: docker-compose down --rmi all" -ForegroundColor White
Write-Host "Full cleanup: docker system prune -a" -ForegroundColor White

Write-Host ""
Write-Host "✨ TecnoDrive services stopped!" -ForegroundColor Green
