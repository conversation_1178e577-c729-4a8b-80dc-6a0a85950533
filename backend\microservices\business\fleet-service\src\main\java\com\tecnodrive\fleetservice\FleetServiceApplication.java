package com.tecnodrive.fleetservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Fleet Service Application
 *
 * This service handles:
 * - Vehicle fleet management and tracking
 * - Vehicle registration and documentation
 * - Maintenance scheduling and tracking
 * - Driver assignment and management
 * - Vehicle availability and status monitoring
 * - Fleet utilization analytics
 * - Insurance and compliance tracking
 * - Fuel consumption monitoring
 * - Route optimization support
 * - Vehicle performance metrics
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class FleetServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FleetServiceApplication.class, args);
    }
}
