import uuid
from faker import Faker
import random
from datetime import datetime, timedelta
import json
import os

# تهيئة مكتبة Faker للغة العربية (السعودية كخيار عام للأسماء والعناوين العربية)
fake = Faker('ar_SA')

# قائمة مبسطة لمدن يمنية وإحداثياتها
# (يمكن توسيعها لتشمل المزيد من المدن والمواقع الدقيقة)
yemen_cities = {
    "صنعاء": {"lat": 15.3520, "lon": 44.2065},
    "عدن": {"lat": 12.7937, "lon": 45.0292},
    "تعز": {"lat": 13.5772, "lon": 44.0270},
    "الحديدة": {"lat": 14.8068, "lon": 42.9461},
    "إب": {"lat": 13.9667, "lon": 44.1833},
    "المكلا": {"lat": 14.5385, "lon": 49.1238},
    "مأرب": {"lat": 15.4485, "lon": 45.3468},
    "ذمار": {"lat": 14.5422, "lon": 44.4079},
    "سيئون": {"lat": 15.9380, "lon": 48.7892},
    "عمران": {"lat": 15.6560, "lon": 43.9575},
}

def generate_tenants(num=200):
    """
    توليد بيانات افتراضية لجدول الشركات (المستأجرين).
    """
    tenants = []
    for _ in range(num):
        # توليد اسم شركة أو مدرسة بشكل عشوائي
        name = fake.company() if random.random() < 0.7 else fake.school_name()
        tenant_type = 'company'
        if 'مدرسة' in name or 'جامعة' in name:
            tenant_type = 'school'
        elif 'شركة' in name or 'مؤسسة' in name or 'للتجارة' in name or 'للخدمات' in name:
            tenant_type = 'company'
        
        tenants.append({
            'id': str(uuid.uuid4()),
            'name': name,
            'type': tenant_type,
            'contact_email': fake.unique.company_email(),
            'phone_number': fake.phone_number(),
            'address': fake.address(),
            'status': random.choice(['active', 'inactive', 'suspended']),
            'created_at': (datetime.now() - timedelta(days=random.randint(30, 700))).strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    return tenants

def generate_users(num=200):
    """
    توليد بيانات افتراضية لجدول المستخدمين (الركاب والمرسلين/المستلمين).
    """
    users = []
    for i in range(num):
        first_name = fake.first_name_male() if random.random() < 0.6 else fake.first_name_female()
        last_name = fake.last_name()
        users.append({
            'id': str(uuid.uuid4()),
            'first_name': first_name,
            'last_name': last_name,
            'email': fake.unique.email(),
            'phone_number': fake.unique.phone_number(),
            'created_at': (datetime.now() - timedelta(days=random.randint(90, 800))).strftime('%Y-%m-%d %H:%M:%S'),
        })
    return users

def generate_drivers(num=200, tenants_data=None):
    """
    توليد بيانات افتراضية لجدول السائقين.
    """
    drivers = []
    for i in range(num):
        # ربط بعض السائقين بشركات (مستأجرين) وترك البقية مستقلين
        tenant_id = random.choice([t['id'] for t in tenants_data]) if random.random() < 0.4 and tenants_data else None 
        
        first_name = fake.first_name_male() if random.random() < 0.5 else fake.first_name_female()
        last_name = fake.last_name()
        
        # تحديد موقع افتراضي للسائق ضمن إحدى المدن اليمنية
        city_name = random.choice(list(yemen_cities.keys()))
        coords = yemen_cities[city_name]
        
        drivers.append({
            'id': str(uuid.uuid4()),
            'tenant_id': tenant_id,
            'first_name': first_name,
            'last_name': last_name,
            'email': fake.unique.email(),
            'phone_number': fake.unique.phone_number(),
            'license_number': f"LIC{i+1:04d}", # رقم رخصة قيادة افتراضي
            'rating': round(random.uniform(3.5, 5.0), 1), # تقييم عشوائي
            'status': random.choice(['on_duty', 'off_duty', 'active']),
            'current_lat': round(coords['lat'] + random.uniform(-0.05, 0.05), 6), # إحداثيات قريبة من المدينة
            'current_lon': round(coords['lon'] + random.uniform(-0.05, 0.05), 6),
            'last_location_update': (datetime.now() - timedelta(minutes=random.randint(1, 60))).strftime('%Y-%m-%d %H:%M:%S'),
            'created_at': (datetime.now() - timedelta(days=random.randint(60, 500))).strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    return drivers

def generate_vehicles(num=200, drivers_data=None):
    """
    توليد بيانات افتراضية لجدول المركبات.
    """
    vehicles = []
    if not drivers_data:
        print("لا توجد بيانات للسائقين لتوليد المركبات.")
        return []

    # التأكد من أن كل مركبة مرتبطة بسائق فريد
    # نختار عدد المركبات بما لا يتجاوز عدد السائقين
    selected_driver_ids = random.sample([d['id'] for d in drivers_data], min(num, len(drivers_data)))
    
    makes = ["تويوتا", "هيونداي", "نيسان", "كيا", "فورد", "شيفروليه"]
    models = {
        "تويوتا": ["كامري", "كورولا", "هايلوكس", "برادو"],
        "هيونداي": ["إلنترا", "سوناتا", "توسان", "سانتا في"],
        "نيسان": ["التيما", "باترول", "صني", "نافارا"],
        "كيا": ["ريو", "سبورتاج", "سورينتو"],
        "فورد": ["فوكس", "إكسبلورر", "ترانزيت"],
        "شيفروليه": ["كروز", "تاهو", "سيلفرادو"],
    }
    vehicle_types = ["سيدان", "دفع رباعي", "شاحنة", "فان", "دراجة نارية"]
    colors = ["أبيض", "أسود", "فضي", "أزرق", "أحمر", "ذهبي"]
    
    for i, driver_id in enumerate(selected_driver_ids):
        make = random.choice(makes)
        model = random.choice(models[make])
        v_type = random.choice(vehicle_types)
        
        vehicles.append({
            'id': str(uuid.uuid4()),
            'driver_id': driver_id,
            'make': make,
            'model': model,
            'year': random.randint(2010, 2024),
            'license_plate': f"{random.randint(10000, 99999)} {random.choice(list(yemen_cities.keys()))}", # لوحة ترخيص افتراضية
            'color': random.choice(colors),
            'vehicle_type': v_type,
            'capacity': random.choice([4, 5, 6, 7, 9]) if v_type in ['سيدان', 'دفع رباعي', 'فان'] else 2, # سعة الركاب
            'status': random.choice(['available', 'in_trip', 'under_maintenance']),
            'created_at': (datetime.now() - timedelta(days=random.randint(30, 400))).strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    return vehicles

def generate_trips(num=200, users_data=None, drivers_data=None, vehicles_data=None, tenants_data=None):
    """
    توليد بيانات افتراضية لجدول الرحلات (عمليات التنقل بين المدن اليمنية).
    """
    trips = []
    if not users_data or not drivers_data or not vehicles_data:
        print("لا توجد بيانات كافية لتوليد الرحلات.")
        return []

    for _ in range(num):
        # اختيار مستخدم وسائق ومركبة عشوائياً
        user = random.choice(users_data)
        driver = random.choice(drivers_data)
        # التأكد من وجود مركبة مرتبطة بالسائق
        vehicle = next((v for v in vehicles_data if v['driver_id'] == driver['id']), None)
        if not vehicle:
            continue # تخطي إذا لم يكن هناك مركبة مرتبطة بالسائق

        # ربط بعض الرحلات بشركات (مستأجرين)
        tenant_id = random.choice([t['id'] for t in tenants_data]) if random.random() < 0.3 else None

        # اختيار مدن عشوائية للالتقاء والتوصيل (التنقل بين المدن)
        pickup_city_name = random.choice(list(yemen_cities.keys()))
        # التأكد من أن مدينة التوصيل مختلفة عن مدينة الالتقاء
        dropoff_city_name = random.choice([c for c in yemen_cities.keys() if c != pickup_city_name])

        pickup_coords = yemen_cities[pickup_city_name]
        dropoff_coords = yemen_cities[dropoff_city_name]

        # محاكاة إحداثيات داخل المدينة للالتقاء/التوصيل
        pickup_lat = round(pickup_coords['lat'] + random.uniform(-0.01, 0.01), 6)
        pickup_lon = round(pickup_coords['lon'] + random.uniform(-0.01, 0.01), 6)
        dropoff_lat = round(dropoff_coords['lat'] + random.uniform(-0.01, 0.01), 6)
        dropoff_lon = round(dropoff_coords['lon'] + random.uniform(-0.01, 0.01), 6)

        distance_km = round(random.uniform(50, 600), 2) # مسافة افتراضية بالكيلومتر
        fare_amount = round(distance_km * random.uniform(80, 150), 2) # حساب أجرة بسيط (بالريال اليمني)

        status = random.choice(['completed', 'in_progress', 'cancelled', 'pending'])
        payment_status = 'paid' if status == 'completed' else 'pending'

        requested_at = datetime.now() - timedelta(days=random.randint(0, 30), minutes=random.randint(0, 1440))
        start_time = requested_at + timedelta(minutes=random.randint(5, 30)) if status in ['in_progress', 'completed'] else None
        end_time = start_time + timedelta(hours=random.randint(1, 8)) if status == 'completed' and start_time else None

        trips.append({
            'trip_id': str(uuid.uuid4()),
            'tenant_id': tenant_id,
            'user_id': user['id'],
            'driver_id': driver['id'],
            'vehicle_id': vehicle['id'],
            'trip_type': 'passenger_ride',
            'status': status,
            'pickup_location': f"{pickup_city_name}, {fake.street_address()}",
            'dropoff_location': f"{dropoff_city_name}, {fake.street_address()}",
            'pickup_lat': pickup_lat,
            'pickup_lon': pickup_lon,
            'dropoff_lat': dropoff_lat,
            'dropoff_lon': dropoff_lon,
            'distance_km': distance_km,
            'fare_amount': fare_amount,
            'payment_status': payment_status,
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S') if start_time else None,
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else None,
            'requested_at': requested_at.strftime('%Y-%m-%d %H:%M:%S'),
            'driver_rating': random.randint(3, 5) if status == 'completed' else None,
            'user_rating': random.randint(3, 5) if status == 'completed' else None,
            'created_at': requested_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    return trips

def generate_parcels(num=200, users_data=None, drivers_data=None, vehicles_data=None, tenants_data=None):
    """
    توليد بيانات افتراضية لجدول الطرود.
    """
    parcels = []
    if not users_data or not drivers_data or not vehicles_data:
        print("لا توجد بيانات كافية لتوليد الطرود.")
        return []

    for _ in range(num):
        # اختيار مرسل وسائق ومركبة عشوائياً
        sender = random.choice(users_data)
        driver = random.choice(drivers_data)
        # التأكد من وجود مركبة مرتبطة بالسائق
        vehicle = next((v for v in vehicles_data if v['driver_id'] == driver['id']), None)
        if not vehicle:
            continue # تخطي إذا لم يكن هناك مركبة مرتبطة بالسائق

        # ربط بعض الطرود بشركات (مستأجرين)
        tenant_id = random.choice([t['id'] for t in tenants_data]) if random.random() < 0.2 else None

        pickup_city_name = random.choice(list(yemen_cities.keys()))
        delivery_city_name = random.choice(list(yemen_cities.keys()))

        pickup_coords = yemen_cities[pickup_city_name]
        delivery_coords = yemen_cities[delivery_city_name]

        pickup_lat = round(pickup_coords['lat'] + random.uniform(-0.01, 0.01), 6)
        pickup_lon = round(pickup_coords['lon'] + random.uniform(-0.01, 0.01), 6)
        delivery_lat = round(delivery_coords['lat'] + random.uniform(-0.01, 0.01), 6)
        delivery_lon = round(delivery_coords['lon'] + random.uniform(-0.01, 0.01), 6)

        delivery_fee = round(random.uniform(3000, 20000), 2) # رسوم توصيل افتراضية

        status = random.choice(['delivered', 'in_transit', 'pending_pickup', 'cancelled'])
        payment_status = 'paid' if status == 'delivered' else 'pending'

        requested_at = datetime.now() - timedelta(days=random.randint(0, 30), minutes=random.randint(0, 1440))
        pickup_time = requested_at + timedelta(minutes=random.randint(10, 60)) if status != 'pending_pickup' else None
        delivery_time = pickup_time + timedelta(hours=random.randint(1, 12)) if status == 'delivered' and pickup_time else None

        parcels.append({
            'parcel_id': str(uuid.uuid4()),
            'tenant_id': tenant_id,
            'sender_id': sender['id'],
            'driver_id': driver['id'],
            'vehicle_id': vehicle['id'],
            'status': status,
            'pickup_address': f"{pickup_city_name}, {fake.street_address()}",
            'pickup_lat': pickup_lat,
            'pickup_lon': pickup_lon,
            'delivery_address': f"{delivery_city_name}, {fake.street_address()}",
            'delivery_lat': delivery_lat,
            'delivery_lon': delivery_lon,
            'recipient_name': fake.name(),
            'recipient_phone': fake.phone_number(),
            'item_description': random.choice(["وثائق", "ملابس", "كتب", "أجزاء إلكترونية", "هدايا", "طعام", "لوازم مكتبية"]),
            'weight_kg': round(random.uniform(0.1, 20.0), 2),
            'dimensions_cm': f"{random.randint(10, 100)}x{random.randint(10, 100)}x{random.randint(5, 50)}",
            'delivery_fee': delivery_fee,
            'payment_status': payment_status,
            'pickup_time': pickup_time.strftime('%Y-%m-%d %H:%M:%S') if pickup_time else None,
            'delivery_time': delivery_time.strftime('%Y-%m-%d %H:%M:%S') if delivery_time else None,
            'requested_at': requested_at.strftime('%Y-%m-%d %H:%M:%S'),
            'created_at': requested_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })
    return parcels

def main():
    """
    الدالة الرئيسية لتوليد جميع البيانات وحفظها في ملفات JSON
    """
    # تحديد عدد السجلات المراد توليدها
    num_records = 200

    print("🚀 بدء توليد البيانات الافتراضية لمنصة TecnoDrive - اليمن")
    print("=" * 60)

    # توليد جميع البيانات
    print("📊 جاري توليد بيانات الشركات (المستأجرين)...")
    tenants_data = generate_tenants(num_records)
    print(f"✅ تم توليد {len(tenants_data)} سجل للشركات.")

    print("👥 جاري توليد بيانات المستخدمين...")
    users_data = generate_users(num_records)
    print(f"✅ تم توليد {len(users_data)} سجل للمستخدمين.")

    print("🚗 جاري توليد بيانات السائقين...")
    drivers_data = generate_drivers(num_records, tenants_data)
    print(f"✅ تم توليد {len(drivers_data)} سجل للسائقين.")

    print("🚙 جاري توليد بيانات المركبات...")
    vehicles_data = generate_vehicles(num_records, drivers_data)
    print(f"✅ تم توليد {len(vehicles_data)} سجل للمركبات.")

    print("🛣️ جاري توليد بيانات الرحلات...")
    trips_data = generate_trips(num_records, users_data, drivers_data, vehicles_data, tenants_data)
    print(f"✅ تم توليد {len(trips_data)} سجل للرحلات.")

    print("📦 جاري توليد بيانات الطرود...")
    parcels_data = generate_parcels(num_records, users_data, drivers_data, vehicles_data, tenants_data)
    print(f"✅ تم توليد {len(parcels_data)} سجل للطرود.")

    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    output_dir = "generated_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 تم إنشاء مجلد {output_dir}")

    print("\n💾 جاري حفظ البيانات في ملفات JSON...")
    print("-" * 40)

    # حفظ البيانات في ملفات JSON
    datasets = [
        (tenants_data, "tenants_data.json", "الشركات"),
        (users_data, "users_data.json", "المستخدمين"),
        (drivers_data, "drivers_data.json", "السائقين"),
        (vehicles_data, "vehicles_data.json", "المركبات"),
        (trips_data, "trips_data.json", "الرحلات"),
        (parcels_data, "parcels_data.json", "الطرود")
    ]

    for data, filename, description in datasets:
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"✅ تم حفظ بيانات {description} في {filepath}")

    print("\n🎉 اكتمل توليد البيانات وحفظها بنجاح!")
    print("=" * 60)
    print("📋 ملخص البيانات المولدة:")
    print(f"   • الشركات: {len(tenants_data)} سجل")
    print(f"   • المستخدمين: {len(users_data)} سجل")
    print(f"   • السائقين: {len(drivers_data)} سجل")
    print(f"   • المركبات: {len(vehicles_data)} سجل")
    print(f"   • الرحلات: {len(trips_data)} سجل")
    print(f"   • الطرود: {len(parcels_data)} سجل")
    print(f"\n📁 جميع الملفات محفوظة في مجلد: {output_dir}/")

if __name__ == "__main__":
    main()
