# Start Core TecnoDrive Services
Write-Host "🚀 Starting Core TecnoDrive Services" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

Write-Host "`n🔍 Checking current service status..." -ForegroundColor Yellow

# Check which services are already running
$coreServices = @(
    @{ Name = "API Gateway"; Port = 8080 },
    @{ Name = "Auth Service"; Port = 8081 },
    @{ Name = "Ride Service"; Port = 8082 },
    @{ Name = "User Service"; Port = 8083 },
    @{ Name = "Fleet Service"; Port = 8084 },
    @{ Name = "Location Service"; Port = 8085 },
    @{ Name = "Payment Service"; Port = 8086 }
)

$runningServices = 0
foreach ($service in $coreServices) {
    if (Test-Port -Port $service.Port) {
        Write-Host "✅ $($service.Name) is running on port $($service.Port)" -ForegroundColor Green
        $runningServices++
    } else {
        Write-Host "❌ $($service.Name) is not running on port $($service.Port)" -ForegroundColor Red
    }
}

$healthPercentage = [math]::Round(($runningServices / $coreServices.Count) * 100, 1)
Write-Host "`n📊 Core Services Status: $runningServices/$($coreServices.Count) running ($healthPercentage%)" -ForegroundColor Cyan

Write-Host "`n🔍 Testing Enhanced Location Service..." -ForegroundColor Yellow

# Test enhanced location service endpoints
if (Test-Port -Port 8085) {
    try {
        # Test service health endpoint
        $healthResponse = Invoke-RestMethod -Uri "http://localhost:8085/api/locations/service-health" -Method GET -TimeoutSec 5
        Write-Host "✅ Enhanced Location Service: Service health endpoint working" -ForegroundColor Green
        
        # Test WebSocket stats
        $wsStats = Invoke-RestMethod -Uri "http://localhost:8085/api/websocket/stats" -Method GET -TimeoutSec 5
        Write-Host "✅ Enhanced Location Service: WebSocket stats endpoint working" -ForegroundColor Green
        Write-Host "   📊 Active connections: $($wsStats.totalConnections)" -ForegroundColor Cyan
        
    } catch {
        Write-Host "⚠️ Enhanced Location Service: Some endpoints may still be starting" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Location Service is not running" -ForegroundColor Red
}

Write-Host "`n🌐 Starting Frontend..." -ForegroundColor Yellow

# Check if frontend is running
if (Test-Port -Port 3000) {
    Write-Host "✅ Frontend is already running on port 3000" -ForegroundColor Green
} else {
    Write-Host "🔄 Starting frontend..." -ForegroundColor Yellow
    
    $frontendPath = "frontend/admin-dashboard"
    if (Test-Path $frontendPath) {
        Set-Location $frontendPath
        
        # Start frontend
        Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Minimized
        Write-Host "✅ Frontend starting in background" -ForegroundColor Green
        
        Set-Location "../.."
    } else {
        Write-Host "❌ Frontend directory not found" -ForegroundColor Red
    }
}

Write-Host "`n🧪 Testing Smart API Integration..." -ForegroundColor Yellow

# Test some API endpoints
$testEndpoints = @(
    @{ Name = "Rides API"; Url = "http://localhost:8082/api/rides" },
    @{ Name = "Users API"; Url = "http://localhost:8083/api/users" },
    @{ Name = "Fleet API"; Url = "http://localhost:8084/api/fleet/vehicles" },
    @{ Name = "Location API"; Url = "http://localhost:8085/api/locations/stats" }
)

$workingEndpoints = 0
foreach ($endpoint in $testEndpoints) {
    try {
        $response = Invoke-RestMethod -Uri $endpoint.Url -Method GET -TimeoutSec 5
        Write-Host "✅ $($endpoint.Name): Working" -ForegroundColor Green
        $workingEndpoints++
    } catch {
        Write-Host "❌ $($endpoint.Name): Not responding" -ForegroundColor Red
    }
}

Write-Host "`n📊 API Integration Status: $workingEndpoints/$($testEndpoints.Count) endpoints working" -ForegroundColor Cyan

Write-Host "`n🎉 Core Services Check Complete!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

Write-Host "`n🌐 Access URLs:" -ForegroundColor Yellow
Write-Host "   • Frontend Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host "   • Live Operations: http://localhost:3000/live-operations" -ForegroundColor White
Write-Host "   • API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "   • Enhanced Location Service: http://localhost:8085" -ForegroundColor White

Write-Host "`n🔧 Enhanced Features Available:" -ForegroundColor Yellow
Write-Host "   ✅ Smart API with automatic fallback" -ForegroundColor White
Write-Host "   ✅ Real-time WebSocket integration" -ForegroundColor White
Write-Host "   ✅ Cross-service communication" -ForegroundColor White
Write-Host "   ✅ Service health monitoring" -ForegroundColor White

Write-Host "`n🧪 Test Commands:" -ForegroundColor Yellow
Write-Host "   • Test integration: .\test-smart-integration.ps1" -ForegroundColor White
Write-Host "   • Generate demo data: .\generate-demo-data.ps1" -ForegroundColor White
Write-Host "   • Check service health: curl http://localhost:8085/api/locations/service-health" -ForegroundColor White

if ($healthPercentage -ge 80) {
    Write-Host "`n🎯 Status: Ready for testing! All core services are running." -ForegroundColor Green
} elseif ($healthPercentage -ge 60) {
    Write-Host "`n🎯 Status: Partially ready. Some services may need to be started." -ForegroundColor Yellow
} else {
    Write-Host "`n🎯 Status: Many services are down. Check Docker containers or start services manually." -ForegroundColor Red
}

Write-Host "`n💡 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Open http://localhost:3000 to access the dashboard" -ForegroundColor White
Write-Host "   2. Check the service status indicator in the dashboard" -ForegroundColor White
Write-Host "   3. Navigate to Live Operations for real-time tracking" -ForegroundColor White
Write-Host "   4. Test the enhanced integration features" -ForegroundColor White
