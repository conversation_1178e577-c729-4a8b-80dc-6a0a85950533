package com.tecnodrive.walletservice.dto;

import com.tecnodrive.walletservice.entity.WalletTransaction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Wallet Transaction Data Transfer Object
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletTransactionDTO {

    private UUID id;
    private UUID walletId;
    private String transactionReference;
    private WalletTransaction.TransactionType type;
    private BigDecimal amount;
    private String currency;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
    private WalletTransaction.TransactionStatus status;
    private WalletTransaction.TransactionSource source;
    private String description;
    private String referenceId;
    private String referenceType;
    private UUID agentId;
    private String agentName;
    private String paymentMethod;
    private String externalTransactionId;
    private BigDecimal fees;
    private BigDecimal tax;
    private BigDecimal netAmount;
    private LocalDateTime processedAt;
    private String failedReason;
    private String metadata;
    private LocalDateTime createdAt;
    private String createdBy;

    // Additional fields for display
    private String phoneNumber;
    private String formattedAmount;
    private String statusDisplay;
    private String typeDisplay;
    private String sourceDisplay;

    /**
     * Create WalletTransactionDTO from WalletTransaction entity
     */
    public static WalletTransactionDTO fromEntity(WalletTransaction transaction) {
        if (transaction == null) {
            return null;
        }

        return WalletTransactionDTO.builder()
                .id(transaction.getId())
                .walletId(transaction.getWalletId())
                .transactionReference(transaction.getTransactionReference())
                .type(transaction.getType())
                .amount(transaction.getAmount())
                .currency(transaction.getCurrency())
                .balanceBefore(transaction.getBalanceBefore())
                .balanceAfter(transaction.getBalanceAfter())
                .status(transaction.getStatus())
                .source(transaction.getSource())
                .description(transaction.getDescription())
                .referenceId(transaction.getReferenceId())
                .referenceType(transaction.getReferenceType())
                .agentId(transaction.getAgentId())
                .agentName(transaction.getAgentName())
                .paymentMethod(transaction.getPaymentMethod())
                .externalTransactionId(transaction.getExternalTransactionId())
                .fees(transaction.getFees())
                .tax(transaction.getTax())
                .netAmount(transaction.getNetAmount())
                .processedAt(transaction.getProcessedAt())
                .failedReason(transaction.getFailedReason())
                .metadata(transaction.getMetadata())
                .createdAt(transaction.getCreatedAt())
                .createdBy(transaction.getCreatedBy())
                // Display fields
                .formattedAmount(formatAmount(transaction.getAmount(), transaction.getCurrency()))
                .statusDisplay(getStatusDisplay(transaction.getStatus()))
                .typeDisplay(getTypeDisplay(transaction.getType()))
                .sourceDisplay(getSourceDisplay(transaction.getSource()))
                .build();
    }

    /**
     * Create WalletTransaction entity from WalletTransactionDTO
     */
    public WalletTransaction toEntity() {
        return WalletTransaction.builder()
                .id(this.id)
                .walletId(this.walletId)
                .transactionReference(this.transactionReference)
                .type(this.type)
                .amount(this.amount)
                .currency(this.currency != null ? this.currency : "SAR")
                .balanceBefore(this.balanceBefore)
                .balanceAfter(this.balanceAfter)
                .status(this.status != null ? this.status : WalletTransaction.TransactionStatus.PENDING)
                .source(this.source)
                .description(this.description)
                .referenceId(this.referenceId)
                .referenceType(this.referenceType)
                .agentId(this.agentId)
                .agentName(this.agentName)
                .paymentMethod(this.paymentMethod)
                .externalTransactionId(this.externalTransactionId)
                .fees(this.fees != null ? this.fees : BigDecimal.ZERO)
                .tax(this.tax != null ? this.tax : BigDecimal.ZERO)
                .netAmount(this.netAmount)
                .processedAt(this.processedAt)
                .failedReason(this.failedReason)
                .metadata(this.metadata)
                .createdBy(this.createdBy)
                .build();
    }

    private static String formatAmount(BigDecimal amount, String currency) {
        if (amount == null) return "0.00";
        return String.format("%.2f %s", amount, currency != null ? currency : "SAR");
    }

    private static String getStatusDisplay(WalletTransaction.TransactionStatus status) {
        if (status == null) return "Unknown";
        return switch (status) {
            case PENDING -> "معلق";
            case COMPLETED -> "مكتمل";
            case FAILED -> "فاشل";
            case CANCELLED -> "ملغي";
            case REFUNDED -> "مسترد";
        };
    }

    private static String getTypeDisplay(WalletTransaction.TransactionType type) {
        if (type == null) return "Unknown";
        return switch (type) {
            case CREDIT -> "إيداع";
            case DEBIT -> "سحب";
        };
    }

    private static String getSourceDisplay(WalletTransaction.TransactionSource source) {
        if (source == null) return "Unknown";
        return switch (source) {
            case CASH_TOPUP -> "تعبئة نقدية";
            case CARD_TOPUP -> "تعبئة بالبطاقة";
            case BANK_TRANSFER -> "تحويل بنكي";
            case RIDE_PAYMENT -> "دفع رحلة";
            case PARCEL_PAYMENT -> "دفع طرد";
            case REFUND -> "استرداد";
            case BONUS -> "مكافأة";
            case PENALTY -> "غرامة";
            case ADMIN_ADJUSTMENT -> "تعديل إداري";
            case TRANSFER_IN -> "تحويل وارد";
            case TRANSFER_OUT -> "تحويل صادر";
        };
    }
}

/**
 * Transaction Creation Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CreateTransactionRequest {
    private UUID walletId;
    private WalletTransaction.TransactionType type;
    private BigDecimal amount;
    private String currency;
    private WalletTransaction.TransactionSource source;
    private String description;
    private String referenceId;
    private String referenceType;
    private UUID agentId;
    private String agentName;
    private String paymentMethod;
    private String externalTransactionId;
    private BigDecimal fees;
    private BigDecimal tax;
    private String metadata;
    private String createdBy;
}

/**
 * Cash Top-up Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CashTopupRequest {
    private String phoneNumber;
    private BigDecimal amount;
    private String currency;
    private UUID agentId;
    private String agentName;
    private String description;
    private String notes;
}

/**
 * Payment Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class PaymentRequest {
    private UUID walletId;
    private BigDecimal amount;
    private String currency;
    private String referenceId;
    private String referenceType;
    private String description;
    private BigDecimal fees;
    private BigDecimal tax;
    private String pin;
}

/**
 * Transaction Summary DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class TransactionSummaryDTO {
    private UUID id;
    private String transactionReference;
    private WalletTransaction.TransactionType type;
    private BigDecimal amount;
    private String currency;
    private WalletTransaction.TransactionStatus status;
    private WalletTransaction.TransactionSource source;
    private String description;
    private LocalDateTime createdAt;
    private String formattedAmount;
    private String statusDisplay;
    private String typeDisplay;
    private String sourceDisplay;
}

/**
 * Transaction Statistics DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class TransactionStatisticsDTO {
    private Long totalTransactions;
    private Long completedTransactions;
    private Long pendingTransactions;
    private Long failedTransactions;
    private BigDecimal totalCredits;
    private BigDecimal totalDebits;
    private BigDecimal totalFees;
    private BigDecimal totalTax;
    private BigDecimal netAmount;
    private LocalDateTime lastUpdated;
}
