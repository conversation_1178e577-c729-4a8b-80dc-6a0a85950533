import { apiMethods, ApiResponse, handleApiError, SERVICE_URLS } from './api';
import { AppUser } from '../store/slices/usersSlice';
import { MockService } from './mockService';
import { smartApiService } from './smartApiService';

export interface UserFilters {
  page?: number;
  limit?: number;
  userType?: 'PASSENGER' | 'DRIVER' | 'ADMIN';
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  search?: string;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  phone: string;
  userType: 'PASSENGER' | 'DRIVER' | 'ADMIN';
  password: string;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
}

class UsersService {
  private baseUrl = SERVICE_URLS.USER_SERVICE;

  async getUsers(filters: UserFilters = {}): Promise<ApiResponse<AppUser[]>> {
    return await smartApiService.getUsers({
      page: filters.page,
      limit: filters.limit,
      userType: filters.userType,
      status: filters.status,
      search: filters.search
    });
  }

  async getUserById(userId: string): Promise<ApiResponse<AppUser>> {
    try {
      const response = await apiMethods.get<ApiResponse<AppUser>>(
        `${this.baseUrl}/${userId}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async createUser(userData: CreateUserRequest): Promise<ApiResponse<AppUser>> {
    try {
      const response = await apiMethods.post<ApiResponse<AppUser>>(
        `${this.baseUrl}`,
        userData
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateUser(userId: string, userData: UpdateUserRequest): Promise<ApiResponse<AppUser>> {
    try {
      const response = await apiMethods.put<ApiResponse<AppUser>>(
        `${this.baseUrl}/${userId}`,
        userData
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deleteUser(userId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.delete<ApiResponse<void>>(
        `${this.baseUrl}/${userId}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async suspendUser(userId: string, reason: string): Promise<ApiResponse<AppUser>> {
    try {
      const response = await apiMethods.post<ApiResponse<AppUser>>(
        `${this.baseUrl}/${userId}/suspend`,
        { reason }
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async activateUser(userId: string): Promise<ApiResponse<AppUser>> {
    try {
      const response = await apiMethods.post<ApiResponse<AppUser>>(
        `${this.baseUrl}/${userId}/activate`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const usersService = new UsersService();
export default usersService;
