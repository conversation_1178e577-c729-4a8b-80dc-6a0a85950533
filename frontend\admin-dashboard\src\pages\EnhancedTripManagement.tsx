import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  Alert,
  Tooltip,
  Badge,
  LinearProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Assignment as AssignIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  DirectionsCar as CarIcon,
  LocalShipping as ParcelIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  Speed as SpeedIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface Trip {
  id: string;
  type: 'passenger_ride' | 'parcel_delivery' | 'mixed';
  status: string;
  driverId?: string;
  vehicleId?: string;
  passengerName?: string;
  senderName?: string;
  receiverName?: string;
  origin: string;
  destination: string;
  currentLocation: { lat: number; lng: number };
  estimatedArrival?: string;
  startTime?: string;
  endTime?: string;
  fareAmount: number;
  distance: number;
  priority: 'normal' | 'high' | 'urgent';
  parcelDetails?: {
    weight: number;
    dimensions: string;
    description: string;
  };
}

interface Driver {
  id: string;
  name: string;
  status: string;
  currentLocation: { lat: number; lng: number };
  vehicleId: string;
  rating: number;
  canDeliverParcels: boolean;
  specializations: string[];
}

interface TripStatistics {
  total: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  totalRevenue: number;
  averageFare: number;
  totalDistance: number;
}

const EnhancedTripManagement: React.FC = () => {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [statistics, setStatistics] = useState<TripStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTrips, setSelectedTrips] = useState<string[]>([]);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedTripForAssign, setSelectedTripForAssign] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    priority: '',
    driverId: '',
    search: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    loadTrips();
    loadDrivers();
  }, [filters]);

  const loadTrips = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(`http://localhost:8095/api/trips?${queryParams}`);
      const data = await response.json();
      
      if (data.success) {
        setTrips(data.data);
        setStatistics(data.statistics);
      }
    } catch (error) {
      console.error('Failed to load trips:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDrivers = async () => {
    try {
      const response = await fetch('http://localhost:8095/api/drivers/available');
      const data = await response.json();
      
      if (data.success) {
        setDrivers(data.data);
      }
    } catch (error) {
      console.error('Failed to load drivers:', error);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAssignDriver = async (tripId: string, driverId?: string) => {
    try {
      const response = await fetch(`http://localhost:8095/api/trips/${tripId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ driverId })
      });

      const data = await response.json();
      
      if (data.success) {
        loadTrips();
        loadDrivers();
        setAssignDialogOpen(false);
        setSelectedTripForAssign(null);
      }
    } catch (error) {
      console.error('Failed to assign driver:', error);
    }
  };

  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedTrips.length === 0) return;

    try {
      const response = await fetch('http://localhost:8095/api/trips/bulk-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tripIds: selectedTrips,
          status
        })
      });

      const data = await response.json();
      
      if (data.success) {
        loadTrips();
        setSelectedTrips([]);
      }
    } catch (error) {
      console.error('Failed to update trips:', error);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'pending_pickup': 'warning',
      'assigned': 'info',
      'in_progress': 'primary',
      'completed': 'success',
      'cancelled': 'error'
    };
    return colors[status] || 'default';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'passenger_ride': return <PersonIcon />;
      case 'parcel_delivery': return <ParcelIcon />;
      case 'mixed': return <CarIcon />;
      default: return <CarIcon />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#f44336';
      case 'high': return '#ff9800';
      case 'normal': return '#4caf50';
      default: return '#9e9e9e';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          إدارة الرحلات المحسنة
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة للرحلات والطرود مع تعيين ذكي للسائقين
        </Typography>
      </Box>

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CarIcon color="primary" sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h4">{statistics.total}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الرحلات
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h4">{statistics.totalRevenue.toFixed(2)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الإيرادات (ريال)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SpeedIcon color="info" sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h4">{statistics.averageFare.toFixed(2)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط الأجرة (ريال)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="warning" sx={{ mr: 1 }} />
                  <Box>
                    <Typography variant="h4">{statistics.totalDistance.toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المسافة (كم)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              placeholder="البحث..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="pending_pickup">في انتظار الالتقاط</MenuItem>
                <MenuItem value="assigned">مُعيَّن</MenuItem>
                <MenuItem value="in_progress">قيد التنفيذ</MenuItem>
                <MenuItem value="completed">مكتمل</MenuItem>
                <MenuItem value="cancelled">ملغي</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>النوع</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="النوع"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="passenger_ride">رحلة ركاب</MenuItem>
                <MenuItem value="parcel_delivery">توصيل طرد</MenuItem>
                <MenuItem value="mixed">مختلط</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الأولوية</InputLabel>
              <Select
                value={filters.priority}
                onChange={(e) => handleFilterChange('priority', e.target.value)}
                label="الأولوية"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="normal">عادية</MenuItem>
                <MenuItem value="high">عالية</MenuItem>
                <MenuItem value="urgent">عاجلة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadTrips}
            >
              تحديث
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<FilterIcon />}
              onClick={() => {/* Open advanced filters */}}
            >
              فلاتر متقدمة
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Bulk Actions */}
      {selectedTrips.length > 0 && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body2">
              تم تحديد {selectedTrips.length} رحلة
            </Typography>
            <Button
              size="small"
              startIcon={<CheckIcon />}
              onClick={() => handleBulkStatusUpdate('completed')}
            >
              تمييز كمكتمل
            </Button>
            <Button
              size="small"
              startIcon={<CancelIcon />}
              onClick={() => handleBulkStatusUpdate('cancelled')}
            >
              إلغاء
            </Button>
            <Button
              size="small"
              startIcon={<AssignIcon />}
              onClick={() => {/* Open bulk assign dialog */}}
            >
              تعيين سائق
            </Button>
          </Box>
        </Paper>
      )}

      {/* Trips Table */}
      <Paper>
        {loading && <LinearProgress />}
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedTrips.length > 0 && selectedTrips.length < trips.length}
                    checked={trips.length > 0 && selectedTrips.length === trips.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTrips(trips.map(t => t.id));
                      } else {
                        setSelectedTrips([]);
                      }
                    }}
                  />
                </TableCell>
                <TableCell>معرف الرحلة</TableCell>
                <TableCell>النوع</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>الأولوية</TableCell>
                <TableCell>المنشأ</TableCell>
                <TableCell>الوجهة</TableCell>
                <TableCell>السائق</TableCell>
                <TableCell>الأجرة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {trips
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((trip) => (
                <TableRow key={trip.id}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedTrips.includes(trip.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTrips(prev => [...prev, trip.id]);
                        } else {
                          setSelectedTrips(prev => prev.filter(id => id !== trip.id));
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {trip.id}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      {getTypeIcon(trip.type)}
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {trip.type === 'passenger_ride' ? 'ركاب' : 
                         trip.type === 'parcel_delivery' ? 'طرد' : 'مختلط'}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={trip.status}
                      color={getStatusColor(trip.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: getPriorityColor(trip.priority),
                        display: 'inline-block',
                        mr: 1
                      }}
                    />
                    {trip.priority}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap>
                      {trip.origin}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap>
                      {trip.destination}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {trip.driverId ? (
                      <Chip label={trip.driverId} size="small" />
                    ) : (
                      <Button
                        size="small"
                        startIcon={<AssignIcon />}
                        onClick={() => {
                          setSelectedTripForAssign(trip.id);
                          setAssignDialogOpen(true);
                        }}
                      >
                        تعيين
                      </Button>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {trip.fareAmount.toFixed(2)} ريال
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="عرض التفاصيل">
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="تعديل">
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          component="div"
          count={trips.length}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
        />
      </Paper>

      {/* Driver Assignment Dialog */}
      <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>تعيين سائق للرحلة</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              sx={{ mb: 2 }}
              onClick={() => {
                if (selectedTripForAssign) {
                  handleAssignDriver(selectedTripForAssign);
                }
              }}
            >
              تعيين تلقائي (أفضل سائق متاح)
            </Button>
            
            <Typography variant="subtitle2" gutterBottom>
              أو اختر سائق محدد:
            </Typography>
            
            {drivers.map((driver) => (
              <Card key={driver.id} sx={{ mb: 1, cursor: 'pointer' }} 
                    onClick={() => {
                      if (selectedTripForAssign) {
                        handleAssignDriver(selectedTripForAssign, driver.id);
                      }
                    }}>
                <CardContent sx={{ py: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box>
                      <Typography variant="body1">{driver.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        تقييم: {driver.rating} ⭐
                      </Typography>
                    </Box>
                    <Box>
                      <Chip
                        label={driver.status}
                        color={driver.status === 'available' ? 'success' : 'default'}
                        size="small"
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>إلغاء</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default EnhancedTripManagement;
