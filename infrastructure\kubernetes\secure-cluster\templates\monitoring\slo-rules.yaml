{{- if .Values.monitoring.slo.enabled }}
# Advanced SLO/SLI Configuration for TECNO DRIVE
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tecno-drive-slo-rules
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: slo-rules
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    prometheus: kube-prometheus
    role: alert-rules
    tecno-drive.com/component: "slo-monitoring"
spec:
  groups:
    # Security Policy Compliance SLO
    - name: security-policy-slo
      interval: 30s
      rules:
        # Security Policy Compliance SLI
        - record: sli:security_policy_compliance:ratio_rate5m
          expr: |
            (
              sum(rate(kube_pod_created{namespace=~"tecno-drive.*"}[5m]))
              -
              sum(rate(gatekeeper_violations_total{constraint="centralsecurity"}[5m]))
            )
            /
            sum(rate(kube_pod_created{namespace=~"tecno-drive.*"}[5m]))
        
        # Security Policy Compliance SLO (99.9%)
        - record: slo:security_policy_compliance:error_budget_remaining
          expr: |
            1 - (
              (1 - sli:security_policy_compliance:ratio_rate5m)
              /
              (1 - {{ .Values.monitoring.slo.securityCompliance.target | default 0.999 }})
            )
        
        # Burn Rate Calculations
        - record: slo:security_policy_compliance:burn_rate_5m
          expr: |
            (
              sum(rate(gatekeeper_violations_total{constraint="centralsecurity"}[5m]))
              /
              sum(rate(kube_pod_created{namespace=~"tecno-drive.*"}[5m]))
            )
            /
            (1 - {{ .Values.monitoring.slo.securityCompliance.target | default 0.999 }})
        
        - record: slo:security_policy_compliance:burn_rate_1h
          expr: |
            (
              sum(rate(gatekeeper_violations_total{constraint="centralsecurity"}[1h]))
              /
              sum(rate(kube_pod_created{namespace=~"tecno-drive.*"}[1h]))
            )
            /
            (1 - {{ .Values.monitoring.slo.securityCompliance.target | default 0.999 }})
        
        - record: slo:security_policy_compliance:burn_rate_6h
          expr: |
            (
              sum(rate(gatekeeper_violations_total{constraint="centralsecurity"}[6h]))
              /
              sum(rate(kube_pod_created{namespace=~"tecno-drive.*"}[6h]))
            )
            /
            (1 - {{ .Values.monitoring.slo.securityCompliance.target | default 0.999 }})
        
        # Security SLO Alerts
        - alert: SecurityPolicyComplianceBurnRateCritical
          expr: |
            slo:security_policy_compliance:burn_rate_5m > 14.4
            and
            slo:security_policy_compliance:burn_rate_1h > 14.4
          for: 2m
          labels:
            severity: critical
            category: security
            slo: security_policy_compliance
            burn_rate: critical
          annotations:
            summary: "🚨 Critical security policy compliance burn rate"
            description: |
              Security policy compliance is burning error budget at {{ "{{ $value | humanizePercentage }}" }} rate.
              At this rate, the monthly error budget will be exhausted in {{ "{{ with $v := $value }}{{ if $v }}{{ div 1 $v | humanizeDuration }}{{ end }}{{ end }}".
              
              Current compliance: {{ "{{ with query \"sli:security_policy_compliance:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              Target: {{ .Values.monitoring.slo.securityCompliance.target | mul 100 }}%
            runbook_url: "https://docs.tecnodrive.com/runbooks/security-slo-burn"
        
        - alert: SecurityPolicyComplianceBurnRateHigh
          expr: |
            slo:security_policy_compliance:burn_rate_5m > 6
            and
            slo:security_policy_compliance:burn_rate_6h > 6
          for: 15m
          labels:
            severity: high
            category: security
            slo: security_policy_compliance
            burn_rate: high
          annotations:
            summary: "⚠️ High security policy compliance burn rate"
            description: |
              Security policy compliance is burning error budget at {{ "{{ $value | humanizePercentage }}" }} rate.
              Current compliance: {{ "{{ with query \"sli:security_policy_compliance:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"

    # Resilience Coverage SLO
    - name: resilience-coverage-slo
      interval: 30s
      rules:
        # PDB Coverage SLI
        - record: sli:pdb_coverage:ratio
          expr: |
            count(kube_poddisruptionbudget_created{namespace=~"tecno-drive.*"})
            /
            count(kube_deployment_created{namespace=~"tecno-drive.*"})
        
        # Health Probe Coverage SLI
        - record: sli:health_probe_coverage:ratio
          expr: |
            count(kube_pod_container_info{namespace=~"tecno-drive.*", container!="POD"})
            /
            count(kube_pod_container_info{namespace=~"tecno-drive.*", container!="POD"})
        
        # Combined Resilience Coverage SLI
        - record: sli:resilience_coverage:ratio
          expr: |
            (
              sli:pdb_coverage:ratio * 0.6 +
              sli:health_probe_coverage:ratio * 0.4
            )
        
        # Resilience SLO Alert
        - alert: ResilienceCoverageBelowSLO
          expr: sli:resilience_coverage:ratio < {{ .Values.monitoring.slo.resilienceCoverage.target | default 0.95 }}
          for: 30m
          labels:
            severity: warning
            category: resilience
            slo: resilience_coverage
          annotations:
            summary: "🔧 Resilience coverage below SLO"
            description: |
              Resilience coverage is {{ "{{ $value | humanizePercentage }}" }}, below target of {{ .Values.monitoring.slo.resilienceCoverage.target | mul 100 }}%.
              
              PDB Coverage: {{ "{{ with query \"sli:pdb_coverage:ratio\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              Health Probe Coverage: {{ "{{ with query \"sli:health_probe_coverage:ratio\" }}{{ . | first | value | humanizePercentage }}{{ end }}"

    # API Gateway Performance SLO
    - name: api-gateway-performance-slo
      interval: 30s
      rules:
        # API Gateway Availability SLI
        - record: sli:api_gateway_availability:ratio_rate5m
          expr: |
            (
              sum(rate(http_requests_total{service="api-gateway"}[5m]))
              -
              sum(rate(http_requests_total{service="api-gateway", status=~"5.."}[5m]))
            )
            /
            sum(rate(http_requests_total{service="api-gateway"}[5m]))
        
        # API Gateway Latency SLI (P99 < 500ms)
        - record: sli:api_gateway_latency:ratio_rate5m
          expr: |
            (
              sum(rate(http_request_duration_seconds_bucket{service="api-gateway", le="0.5"}[5m]))
              /
              sum(rate(http_request_duration_seconds_count{service="api-gateway"}[5m]))
            )
        
        # Combined API Gateway SLI
        - record: sli:api_gateway_performance:ratio_rate5m
          expr: |
            sli:api_gateway_availability:ratio_rate5m * sli:api_gateway_latency:ratio_rate5m
        
        # API Gateway SLO Burn Rate
        - record: slo:api_gateway_performance:burn_rate_5m
          expr: |
            (1 - sli:api_gateway_performance:ratio_rate5m)
            /
            (1 - {{ .Values.monitoring.slo.apiGatewayPerformance.target | default 0.995 }})
        
        # API Gateway SLO Alerts
        - alert: APIGatewayPerformanceBurnRateCritical
          expr: slo:api_gateway_performance:burn_rate_5m > 14.4
          for: 2m
          labels:
            severity: critical
            category: performance
            service: api-gateway
            slo: api_gateway_performance
          annotations:
            summary: "🚪 Critical API Gateway performance burn rate"
            description: |
              API Gateway performance is burning error budget at {{ "{{ $value | humanizePercentage }}" }} rate.
              
              Current availability: {{ "{{ with query \"sli:api_gateway_availability:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              Current latency SLI: {{ "{{ with query \"sli:api_gateway_latency:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"

    # Business Service SLOs
    - name: business-services-slo
      interval: 30s
      rules:
        # Ride Service Availability
        - record: sli:ride_service_availability:ratio_rate5m
          expr: |
            (
              sum(rate(http_requests_total{service="ride-service"}[5m]))
              -
              sum(rate(http_requests_total{service="ride-service", status=~"5.."}[5m]))
            )
            /
            sum(rate(http_requests_total{service="ride-service"}[5m]))
        
        # Payment Service Availability
        - record: sli:payment_service_availability:ratio_rate5m
          expr: |
            (
              sum(rate(http_requests_total{service="payment-service"}[5m]))
              -
              sum(rate(http_requests_total{service="payment-service", status=~"5.."}[5m]))
            )
            /
            sum(rate(http_requests_total{service="payment-service"}[5m]))
        
        # Fleet Service Availability
        - record: sli:fleet_service_availability:ratio_rate5m
          expr: |
            (
              sum(rate(http_requests_total{service="fleet-service"}[5m]))
              -
              sum(rate(http_requests_total{service="fleet-service", status=~"5.."}[5m]))
            )
            /
            sum(rate(http_requests_total{service="fleet-service"}[5m]))
        
        # Overall Business Services Availability
        - record: sli:business_services_availability:ratio_rate5m
          expr: |
            (
              sli:ride_service_availability:ratio_rate5m +
              sli:payment_service_availability:ratio_rate5m +
              sli:fleet_service_availability:ratio_rate5m
            ) / 3
        
        # Business Services SLO Alert
        - alert: BusinessServiceAvailabilityBelowSLO
          expr: sli:business_services_availability:ratio_rate5m < {{ .Values.monitoring.slo.businessServices.target | default 0.99 }}
          for: 5m
          labels:
            severity: critical
            category: business
            slo: business_services_availability
          annotations:
            summary: "💼 Business services availability below SLO"
            description: |
              Business services availability is {{ "{{ $value | humanizePercentage }}" }}, below target of {{ .Values.monitoring.slo.businessServices.target | mul 100 }}%.
              
              Ride Service: {{ "{{ with query \"sli:ride_service_availability:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              Payment Service: {{ "{{ with query \"sli:payment_service_availability:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              Fleet Service: {{ "{{ with query \"sli:fleet_service_availability:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}"

    # Webhook Performance SLO
    - name: webhook-performance-slo
      interval: 30s
      rules:
        # Webhook Latency SLI (P99 < 500ms)
        - record: sli:webhook_latency:p99_5m
          expr: |
            histogram_quantile(0.99,
              sum(rate(gatekeeper_webhook_request_duration_seconds_bucket[5m])) by (le)
            )
        
        # Webhook Success Rate SLI
        - record: sli:webhook_success_rate:ratio_rate5m
          expr: |
            sum(rate(gatekeeper_webhook_request_total{response_code="200"}[5m]))
            /
            sum(rate(gatekeeper_webhook_request_total[5m]))
        
        # Webhook Performance SLO Alert
        - alert: WebhookPerformanceDegraded
          expr: |
            sli:webhook_latency:p99_5m > {{ .Values.monitoring.slo.webhookPerformance.maxLatency | default 0.5 }}
            or
            sli:webhook_success_rate:ratio_rate5m < {{ .Values.monitoring.slo.webhookPerformance.minSuccessRate | default 0.99 }}
          for: 5m
          labels:
            severity: warning
            category: performance
            component: gatekeeper-webhook
            slo: webhook_performance
          annotations:
            summary: "🔗 Webhook performance degraded"
            description: |
              Gatekeeper webhook performance is degraded:
              
              P99 Latency: {{ "{{ with query \"sli:webhook_latency:p99_5m\" }}{{ . | first | value | humanizeDuration }}{{ end }}" }} (target: {{ .Values.monitoring.slo.webhookPerformance.maxLatency | default 0.5 }}s)
              Success Rate: {{ "{{ with query \"sli:webhook_success_rate:ratio_rate5m\" }}{{ . | first | value | humanizePercentage }}{{ end }}" }} (target: {{ .Values.monitoring.slo.webhookPerformance.minSuccessRate | mul 100 | default 99 }}%)

    # Certificate Management SLO
    - name: certificate-management-slo
      interval: 60s
      rules:
        # Certificate Health SLI
        - record: sli:certificate_health:ratio
          expr: |
            count(kube_certificate_expiration_seconds > (86400 * 30)) # More than 30 days
            /
            count(kube_certificate_expiration_seconds)
        
        # Certificate Renewal Success Rate
        - record: sli:certificate_renewal_success:ratio_rate1h
          expr: |
            sum(rate(cert_manager_certificate_renewal_success_total[1h]))
            /
            sum(rate(cert_manager_certificate_renewal_total[1h]))
        
        # Certificate SLO Alert
        - alert: CertificateManagementSLOViolation
          expr: |
            sli:certificate_health:ratio < {{ .Values.monitoring.slo.certificateManagement.healthTarget | default 0.95 }}
            or
            sli:certificate_renewal_success:ratio_rate1h < {{ .Values.monitoring.slo.certificateManagement.renewalSuccessTarget | default 0.98 }}
          for: 15m
          labels:
            severity: warning
            category: infrastructure
            component: cert-manager
            slo: certificate_management
          annotations:
            summary: "🔐 Certificate management SLO violation"
            description: |
              Certificate management SLO violation detected:
              
              Certificate Health: {{ "{{ with query \"sli:certificate_health:ratio\" }}{{ . | first | value | humanizePercentage }}{{ end }}" }}
              Renewal Success Rate: {{ "{{ with query \"sli:certificate_renewal_success:ratio_rate1h\" }}{{ . | first | value | humanizePercentage }}{{ end }}" }}

    # Error Budget Tracking
    - name: error-budget-tracking
      interval: 300s # 5 minutes
      rules:
        # Monthly Error Budget Remaining (Security)
        - record: slo:security_policy_compliance:error_budget_remaining_30d
          expr: |
            1 - (
              (
                sum(increase(gatekeeper_violations_total{constraint="centralsecurity"}[30d]))
                /
                sum(increase(kube_pod_created{namespace=~"tecno-drive.*"}[30d]))
              )
              /
              (1 - {{ .Values.monitoring.slo.securityCompliance.target | default 0.999 }})
            )
        
        # Monthly Error Budget Remaining (API Gateway)
        - record: slo:api_gateway_performance:error_budget_remaining_30d
          expr: |
            1 - (
              (1 - avg_over_time(sli:api_gateway_performance:ratio_rate5m[30d]))
              /
              (1 - {{ .Values.monitoring.slo.apiGatewayPerformance.target | default 0.995 }})
            )
        
        # Error Budget Exhaustion Alerts
        - alert: ErrorBudgetExhaustionRisk
          expr: |
            slo:security_policy_compliance:error_budget_remaining_30d < 0.1
            or
            slo:api_gateway_performance:error_budget_remaining_30d < 0.1
          for: 1h
          labels:
            severity: warning
            category: slo
          annotations:
            summary: "📊 Error budget exhaustion risk"
            description: |
              Error budget is running low and may be exhausted before the end of the month.
              
              Security Compliance Budget Remaining: {{ "{{ with query \"slo:security_policy_compliance:error_budget_remaining_30d\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
              API Gateway Performance Budget Remaining: {{ "{{ with query \"slo:api_gateway_performance:error_budget_remaining_30d\" }}{{ . | first | value | humanizePercentage }}{{ end }}"
{{- end }}
