-- TECNO DRIVE Platform - Database Performance Optimizations
-- Version: 1.0.0
-- Description: Create optimized indexes and partitions for high-performance queries

-- =====================================================
-- LOCATION SERVICE OPTIMIZATIONS
-- =====================================================

-- Composite indexes for location queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_entity_timestamp 
ON locations (entity_id, timestamp DESC) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_entity_type_timestamp 
ON locations (entity_type, timestamp DESC) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_coordinates_gist 
ON locations USING GIST (coordinates) 
WHERE is_active = true;

-- Spatial index for nearby queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_coordinates_spgist 
ON locations USING SPGIST (coordinates) 
WHERE is_active = true;

-- Composite spatial index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_entity_coordinates 
ON locations (entity_id, coordinates) 
WHERE is_active = true;

-- Time-based partitioning for locations table
CREATE TABLE IF NOT EXISTS locations_y2025m01 PARTITION OF locations 
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE IF NOT EXISTS locations_y2025m02 PARTITION OF locations 
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE IF NOT EXISTS locations_y2025m03 PARTITION OF locations 
FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- Add more partitions as needed...

-- =====================================================
-- PARCEL SERVICE OPTIMIZATIONS
-- =====================================================

-- Indexes for parcel tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_parcels_tracking_number 
ON parcels (tracking_number) 
WHERE status != 'DELIVERED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_parcels_status_created 
ON parcels (status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_parcels_sender_created 
ON parcels (sender_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_parcels_recipient_created 
ON parcels (recipient_id, created_at DESC);

-- Delivery optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deliveries_driver_status 
ON deliveries (driver_id, status) 
WHERE status IN ('ASSIGNED', 'IN_PROGRESS');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deliveries_zone_status 
ON deliveries (delivery_zone_id, status, scheduled_date);

-- =====================================================
-- RIDE SERVICE OPTIMIZATIONS
-- =====================================================

-- Ride request indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_customer_status 
ON rides (customer_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_driver_status 
ON rides (driver_id, status) 
WHERE status IN ('ASSIGNED', 'IN_PROGRESS');

-- Geospatial indexes for ride matching
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_pickup_location 
ON rides USING GIST (pickup_coordinates) 
WHERE status = 'REQUESTED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rides_dropoff_location 
ON rides USING GIST (dropoff_coordinates);

-- =====================================================
-- PAYMENT SERVICE OPTIMIZATIONS
-- =====================================================

-- Payment transaction indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status 
ON payments (user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_transaction_id 
ON payments (transaction_id) 
WHERE status != 'FAILED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_amount_date 
ON payments (amount, created_at) 
WHERE status = 'COMPLETED';

-- =====================================================
-- OUTBOX PATTERN OPTIMIZATIONS
-- =====================================================

-- Outbox events indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_outbox_events_processed_created 
ON outbox_events (processed, created_at) 
WHERE processed = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_outbox_events_aggregate 
ON outbox_events (aggregate_id, aggregate_type, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_outbox_events_topic_partition 
ON outbox_events (topic, partition_key) 
WHERE processed = false;

-- =====================================================
-- ANALYTICS OPTIMIZATIONS
-- =====================================================

-- Time-series indexes for analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_type_timestamp 
ON analytics_events (event_type, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_user_timestamp 
ON analytics_events (user_id, timestamp DESC) 
WHERE user_id IS NOT NULL;

-- Materialized view for daily statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_ride_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_rides,
    COUNT(*) FILTER (WHERE status = 'COMPLETED') as completed_rides,
    COUNT(*) FILTER (WHERE status = 'CANCELLED') as cancelled_rides,
    AVG(fare_amount) FILTER (WHERE status = 'COMPLETED') as avg_fare,
    AVG(EXTRACT(EPOCH FROM (completed_at - created_at))/60) FILTER (WHERE status = 'COMPLETED') as avg_duration_minutes
FROM rides 
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

CREATE UNIQUE INDEX ON daily_ride_stats (date);

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_ride_stats;
    -- Add more materialized views here
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Cleanup old processed outbox events (older than 7 days)
    DELETE FROM outbox_events 
    WHERE processed = true 
    AND processed_at < NOW() - INTERVAL '7 days';
    
    -- Cleanup old location data (older than 90 days)
    DELETE FROM locations 
    WHERE created_at < NOW() - INTERVAL '90 days'
    AND is_active = false;
    
    -- Cleanup old analytics events (older than 1 year)
    DELETE FROM analytics_events 
    WHERE timestamp < NOW() - INTERVAL '1 year';
    
    RAISE NOTICE 'Cleanup completed at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- View for monitoring slow queries
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100  -- queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- View for monitoring index usage
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        ELSE 'ACTIVE'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- =====================================================
-- CONFIGURATION RECOMMENDATIONS
-- =====================================================

-- Recommended PostgreSQL settings for production
-- Add these to postgresql.conf:

/*
# Memory settings
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB               # 75% of RAM
work_mem = 4MB                           # Per connection
maintenance_work_mem = 64MB              # For maintenance operations

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Connection settings
max_connections = 200

# Logging settings
log_min_duration_statement = 1000       # Log queries > 1 second
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Performance settings
random_page_cost = 1.1                  # For SSD storage
effective_io_concurrency = 200          # For SSD storage
*/

COMMIT;
