package com.tecnodrive.userservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.cache.annotation.EnableCaching;

/**
 * User Service Application
 * Manages user profiles, preferences, and user-related operations
 */
@SpringBootApplication(scanBasePackages = {
    "com.tecnodrive.userservice",
    "com.tecnodrive.common"
})

@EnableFeignClients
@EnableJpaAuditing
@EnableCaching
public class UserServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
