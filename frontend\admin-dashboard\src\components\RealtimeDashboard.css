.realtime-dashboard {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
  margin: 0;
  color: #1976d2;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.connection-status.connected {
  background: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.connection-status.disconnected {
  background: #ffebee;
  color: #d32f2f;
  border: 1px solid #f44336;
}

.connection-status.error {
  background: #fff3e0;
  color: #f57c00;
  border: 1px solid #ff9800;
}

.last-update {
  font-size: 12px;
  color: #666;
}

.refresh-button {
  background: #1976d2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background: #1565c0;
}

/* Alerts Section */
.alerts-section {
  margin-bottom: 24px;
}

.alert {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert.error {
  background: #ffebee;
  color: #d32f2f;
  border-left: 4px solid #f44336;
}

.alert.warning {
  background: #fff3e0;
  color: #f57c00;
  border-left: 4px solid #ff9800;
}

.alert.info {
  background: #e3f2fd;
  color: #1976d2;
  border-left: 4px solid #2196f3;
}

.alert.success {
  background: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #4caf50;
}

.alert-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* Metrics Cards */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  font-size: 48px;
  opacity: 0.8;
}

.metric-icon.primary {
  color: #1976d2;
}

.metric-icon.success {
  color: #2e7d32;
}

.metric-icon.warning {
  color: #f57c00;
}

.metric-icon.error {
  color: #d32f2f;
}

.metric-value {
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin: 4px 0 0 0;
}

/* Analytics Cards */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.analytics-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analytics-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
}

.analytics-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.analytics-content {
  padding: 24px;
}

.analytics-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.analytics-item:last-child {
  border-bottom: none;
}

.analytics-label {
  font-size: 14px;
  color: #666;
}

.analytics-value {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

/* Memory Usage */
.memory-usage {
  margin-top: 16px;
}

.memory-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.memory-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  transition: width 0.3s ease;
}

/* Subscription List */
.subscription-list {
  max-height: 300px;
  overflow-y: auto;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.subscription-item:last-child {
  border-bottom: none;
}

.subscription-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.subscription-count {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .dashboard-controls {
    width: 100%;
    justify-content: space-between;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .metric-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .metric-icon {
    font-size: 36px;
  }

  .metric-value {
    font-size: 28px;
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Real-time Updates Animation */
.updating {
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Status Indicators */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-dot.online {
  background: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.status-dot.offline {
  background: #f44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.3);
}

.status-dot.warning {
  background: #ff9800;
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.3);
}

/* Scrollbar Styling */
.subscription-list::-webkit-scrollbar {
  width: 6px;
}

.subscription-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.subscription-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.subscription-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
