package com.tecnodrive.locationservice.controller;

import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Simple Map Controller for testing without dependencies
 */
@RestController
@RequestMapping("/api/map")
@CrossOrigin(origins = "*")
public class SimpleMapController {

    /**
     * Get enhanced map configuration
     */
    @GetMapping("/config/enhanced")
    public Map<String, Object> getEnhancedMapConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // Map configuration
        config.put("defaultCenter", Map.of("lat", 24.7136, "lng", 46.6753));
        config.put("defaultZoom", 12);
        config.put("maxZoom", 19);
        config.put("minZoom", 3);
        config.put("provider", "openstreetmap");
        
        // Features
        config.put("features", Map.of(
            "geocoding", true,
            "routing", true,
            "traffic", true,
            "clustering", true
        ));
        
        return Map.of(
            "success", true,
            "data", config,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Get tile servers
     */
    @GetMapping("/tiles")
    public Map<String, Object> getTileServers() {
        Map<String, Object> tileServers = new HashMap<>();
        
        tileServers.put("openstreetmap", Map.of(
            "url", "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            "attribution", "© OpenStreetMap contributors",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c")
        ));
        
        tileServers.put("cartodb-light", Map.of(
            "url", "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
            "attribution", "© CartoDB",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c", "d")
        ));
        
        tileServers.put("cartodb-dark", Map.of(
            "url", "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
            "attribution", "© CartoDB",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c", "d")
        ));
        
        return Map.of(
            "success", true,
            "data", tileServers,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Search locations (mock data)
     */
    @GetMapping("/geocode/search")
    public Map<String, Object> searchLocations(
            @RequestParam String q,
            @RequestParam(defaultValue = "24.7136") double lat,
            @RequestParam(defaultValue = "46.6753") double lng,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Map<String, Object>> suggestions = new ArrayList<>();
        
        // Mock search results based on query
        if (q.toLowerCase().contains("riyadh") || q.toLowerCase().contains("الرياض")) {
            suggestions.add(Map.of(
                "display_name", "الرياض، المملكة العربية السعودية",
                "lat", 24.7136,
                "lng", 46.6753,
                "type", "city",
                "importance", 1.0
            ));
        }
        
        if (q.toLowerCase().contains("jeddah") || q.toLowerCase().contains("جدة")) {
            suggestions.add(Map.of(
                "display_name", "جدة، المملكة العربية السعودية",
                "lat", 21.4858,
                "lng", 39.1925,
                "type", "city",
                "importance", 0.9
            ));
        }
        
        if (q.toLowerCase().contains("mecca") || q.toLowerCase().contains("مكة")) {
            suggestions.add(Map.of(
                "display_name", "مكة المكرمة، المملكة العربية السعودية",
                "lat", 21.3891,
                "lng", 39.8579,
                "type", "city",
                "importance", 0.95
            ));
        }
        
        // Add a generic result if no specific matches
        if (suggestions.isEmpty()) {
            suggestions.add(Map.of(
                "display_name", "نتيجة البحث: " + q,
                "lat", lat + (Math.random() - 0.5) * 0.1,
                "lng", lng + (Math.random() - 0.5) * 0.1,
                "type", "search_result",
                "importance", 0.5
            ));
        }
        
        return Map.of(
            "success", true,
            "data", suggestions,
            "count", suggestions.size(),
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Reverse geocoding (mock data)
     */
    @GetMapping("/geocode/reverse")
    public Map<String, Object> reverseGeocode(
            @RequestParam double lat,
            @RequestParam double lng) {
        
        Map<String, Object> address = new HashMap<>();
        
        // Mock reverse geocoding based on coordinates
        if (Math.abs(lat - 24.7136) < 0.1 && Math.abs(lng - 46.6753) < 0.1) {
            address.put("display_name", "الرياض، منطقة الرياض، المملكة العربية السعودية");
            address.put("city", "الرياض");
            address.put("state", "منطقة الرياض");
            address.put("country", "المملكة العربية السعودية");
        } else {
            address.put("display_name", String.format("موقع عند %.4f, %.4f", lat, lng));
            address.put("city", "مدينة غير محددة");
            address.put("country", "المملكة العربية السعودية");
        }
        
        address.put("lat", lat);
        address.put("lng", lng);
        
        return Map.of(
            "success", true,
            "data", address,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Get route (mock data)
     */
    @GetMapping("/route")
    public Map<String, Object> getRoute(
            @RequestParam double startLat,
            @RequestParam double startLng,
            @RequestParam double endLat,
            @RequestParam double endLng) {
        
        // Calculate simple distance
        double distance = calculateDistance(startLat, startLng, endLat, endLng);
        double duration = distance * 60; // Assume 1 km per minute
        
        // Create simple route
        List<List<Double>> coordinates = Arrays.asList(
            Arrays.asList(startLng, startLat),
            Arrays.asList((startLng + endLng) / 2, (startLat + endLat) / 2),
            Arrays.asList(endLng, endLat)
        );
        
        Map<String, Object> route = new HashMap<>();
        route.put("distance", distance * 1000); // meters
        route.put("duration", duration); // seconds
        route.put("coordinates", coordinates);
        route.put("instructions", Arrays.asList(
            "ابدأ من النقطة الأولى",
            "اتجه نحو الوجهة",
            "وصلت إلى الوجهة"
        ));
        
        return Map.of(
            "success", true,
            "data", route,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Get map styles
     */
    @GetMapping("/styles")
    public Map<String, Object> getMapStyles() {
        Map<String, Object> styles = new HashMap<>();
        
        styles.put("default", Map.of(
            "name", "الافتراضي",
            "description", "النمط الافتراضي للخريطة"
        ));
        
        styles.put("satellite", Map.of(
            "name", "القمر الصناعي",
            "description", "عرض القمر الصناعي"
        ));
        
        styles.put("terrain", Map.of(
            "name", "التضاريس",
            "description", "عرض التضاريس"
        ));
        
        return Map.of(
            "success", true,
            "data", styles,
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * Calculate distance between two points
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final int R = 6371; // Radius of the earth in km
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lngDistance = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lngDistance / 2) * Math.sin(lngDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c; // Distance in km
    }
}
