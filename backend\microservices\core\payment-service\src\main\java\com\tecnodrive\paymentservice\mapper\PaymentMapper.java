package com.tecnodrive.paymentservice.mapper;

import com.tecnodrive.paymentservice.dto.PaymentRequest;
import com.tecnodrive.paymentservice.dto.PaymentResponse;
import com.tecnodrive.paymentservice.dto.PaymentUpdateRequest;
import com.tecnodrive.paymentservice.entity.Payment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Payment Mapper
 * 
 * Maps between Payment entities and DTOs
 */
@Component
public class PaymentMapper {

    /**
     * Convert PaymentRequest to Payment entity
     */
    public Payment toEntity(PaymentRequest request) {
        if (request == null) {
            return null;
        }

        return Payment.builder()
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .payerUserId(request.getPayerUserId())
                .payeeUserId(request.getPayeeUserId())
                .amount(request.getAmount())
                .currency(request.getCurrency())
                .paymentMethod(request.getPaymentMethod())
                .description(request.getDescription())
                .metadata(request.getMetadata())
                .build();
    }

    /**
     * Convert Payment entity to PaymentResponse
     */
    public PaymentResponse toResponse(Payment payment) {
        if (payment == null) {
            return null;
        }

        return PaymentResponse.builder()
                .id(payment.getId().toString())
                .entityId(payment.getEntityId())
                .entityType(payment.getEntityType())
                .payerUserId(payment.getPayerUserId())
                .payeeUserId(payment.getPayeeUserId())
                .amount(payment.getAmount())
                .currency(payment.getCurrency())
                .status(payment.getStatus())
                .paymentMethod(payment.getPaymentMethod())
                .gatewayTransactionId(payment.getGatewayTransactionId())
                .description(payment.getDescription())
                .metadata(payment.getMetadata())
                .createdAt(payment.getCreatedAt())
                .updatedAt(payment.getUpdatedAt())
                .build();
    }

    /**
     * Update Payment entity from PaymentUpdateRequest
     */
    public void updateEntity(Payment payment, PaymentUpdateRequest request) {
        if (payment == null || request == null) {
            return;
        }

        if (request.getStatus() != null) {
            payment.setStatus(request.getStatus());
        }
        if (request.getGatewayTransactionId() != null) {
            payment.setGatewayTransactionId(request.getGatewayTransactionId());
        }
        if (request.getDescription() != null) {
            payment.setDescription(request.getDescription());
        }
        if (request.getMetadata() != null) {
            payment.setMetadata(request.getMetadata());
        }
    }

    /**
     * Convert list of Payment entities to list of PaymentResponse DTOs
     */
    public List<PaymentResponse> toResponseList(List<Payment> payments) {
        if (payments == null) {
            return null;
        }

        return payments.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
