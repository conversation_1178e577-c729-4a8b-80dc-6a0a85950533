package com.tecnodrive.paymentservice.service.impl;

import com.tecnodrive.paymentservice.dto.PaymentRequest;
import com.tecnodrive.paymentservice.dto.PaymentResponse;
import com.tecnodrive.paymentservice.dto.PaymentUpdateRequest;
import com.tecnodrive.paymentservice.entity.Payment;
import com.tecnodrive.paymentservice.exception.PaymentNotFoundException;
import com.tecnodrive.paymentservice.mapper.PaymentMapper;
import com.tecnodrive.paymentservice.repository.PaymentRepository;
import com.tecnodrive.paymentservice.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Payment Service Implementation
 * 
 * Implements business logic for payment management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PaymentServiceImpl implements PaymentService {

    private final PaymentRepository paymentRepository;
    private final PaymentMapper paymentMapper;

    @Override
    public PaymentResponse createPayment(PaymentRequest request) {
        log.info("Creating payment for entity: {} of type: {}", request.getEntityId(), request.getEntityType());
        
        Payment payment = paymentMapper.toEntity(request);
        payment.setStatus(Payment.PaymentStatus.PENDING);
        
        Payment savedPayment = paymentRepository.save(payment);
        
        log.info("Payment created with ID: {}", savedPayment.getId());
        return paymentMapper.toResponse(savedPayment);
    }

    @Override
    @Transactional(readOnly = true)
    public PaymentResponse getPayment(String id) {
        log.debug("Retrieving payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        return paymentMapper.toResponse(payment);
    }

    @Override
    public PaymentResponse updatePayment(String id, PaymentUpdateRequest request) {
        log.info("Updating payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        paymentMapper.updateEntity(payment, request);
        
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment updated: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PaymentResponse> getPaymentsByEntity(String entityId, String entityType) {
        log.debug("Retrieving payments for entity: {} of type: {}", entityId, entityType);
        
        List<Payment> payments = paymentRepository.findByEntityIdAndEntityType(entityId, entityType);
        return paymentMapper.toResponseList(payments);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentResponse> getPaymentsByUser(String userId, Pageable pageable) {
        log.debug("Retrieving payments for user: {}", userId);
        
        Page<Payment> payments = paymentRepository.findByUserId(userId, pageable);
        return payments.map(paymentMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PaymentResponse> getPaymentsByStatus(Payment.PaymentStatus status) {
        log.debug("Retrieving payments with status: {}", status);
        
        List<Payment> payments = paymentRepository.findByStatus(status);
        return paymentMapper.toResponseList(payments);
    }

    @Override
    public PaymentResponse processPayment(String id) {
        log.info("Processing payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        
        if (payment.getStatus() != Payment.PaymentStatus.PENDING) {
            throw new IllegalStateException("Payment must be in PENDING status to process");
        }
        
        payment.setStatus(Payment.PaymentStatus.PROCESSING);
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment processing started: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    public PaymentResponse completePayment(String id, String gatewayTransactionId) {
        log.info("Completing payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        
        if (payment.getStatus() != Payment.PaymentStatus.PROCESSING) {
            throw new IllegalStateException("Payment must be in PROCESSING status to complete");
        }
        
        payment.setStatus(Payment.PaymentStatus.COMPLETED);
        payment.setGatewayTransactionId(gatewayTransactionId);
        
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment completed: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    public PaymentResponse failPayment(String id, String reason) {
        log.info("Failing payment with ID: {} - Reason: {}", id, reason);
        
        Payment payment = findPaymentById(id);
        payment.setStatus(Payment.PaymentStatus.FAILED);
        payment.setDescription(payment.getDescription() + " | Failed: " + reason);
        
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment failed: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    public PaymentResponse cancelPayment(String id) {
        log.info("Cancelling payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        
        if (payment.getStatus() == Payment.PaymentStatus.COMPLETED) {
            throw new IllegalStateException("Cannot cancel completed payment");
        }
        
        payment.setStatus(Payment.PaymentStatus.CANCELLED);
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment cancelled: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    public PaymentResponse refundPayment(String id) {
        log.info("Refunding payment with ID: {}", id);
        
        Payment payment = findPaymentById(id);
        
        if (payment.getStatus() != Payment.PaymentStatus.COMPLETED) {
            throw new IllegalStateException("Can only refund completed payments");
        }
        
        payment.setStatus(Payment.PaymentStatus.REFUNDED);
        Payment updatedPayment = paymentRepository.save(payment);
        
        log.info("Payment refunded: {}", updatedPayment.getId());
        return paymentMapper.toResponse(updatedPayment);
    }

    @Override
    @Transactional(readOnly = true)
    public PaymentStatistics getPaymentStatistics(Instant startDate, Instant endDate) {
        log.debug("Generating payment statistics from {} to {}", startDate, endDate);
        
        List<Payment> payments = paymentRepository.findPaymentsBetweenDates(startDate, endDate);
        
        long total = payments.size();
        long completed = payments.stream().mapToLong(p -> p.getStatus() == Payment.PaymentStatus.COMPLETED ? 1 : 0).sum();
        long failed = payments.stream().mapToLong(p -> p.getStatus() == Payment.PaymentStatus.FAILED ? 1 : 0).sum();
        long pending = payments.stream().mapToLong(p -> p.getStatus() == Payment.PaymentStatus.PENDING ? 1 : 0).sum();
        long cancelled = payments.stream().mapToLong(p -> p.getStatus() == Payment.PaymentStatus.CANCELLED ? 1 : 0).sum();
        long refunded = payments.stream().mapToLong(p -> p.getStatus() == Payment.PaymentStatus.REFUNDED ? 1 : 0).sum();
        
        return new PaymentStatistics(total, completed, failed, pending, cancelled, refunded);
    }

    private Payment findPaymentById(String id) {
        try {
            UUID uuid = UUID.fromString(id);
            return paymentRepository.findById(uuid)
                    .orElseThrow(() -> new PaymentNotFoundException("Payment not found with ID: " + id));
        } catch (IllegalArgumentException e) {
            throw new PaymentNotFoundException("Invalid payment ID format: " + id);
        }
    }
}
