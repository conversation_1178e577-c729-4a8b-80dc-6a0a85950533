import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Chip,
  Divider,
} from '@mui/material';
import {
  DirectionsCar,
  CheckCircle,
  Schedule,
  Cancel,
} from '@mui/icons-material';

interface Ride {
  id: string;
  passengerName: string;
  driverName: string;
  pickup: string;
  destination: string;
  status: 'COMPLETED' | 'IN_PROGRESS' | 'CANCELLED';
  fare: number;
  time: string;
}

const mockRides: Ride[] = [
  {
    id: '1',
    passengerName: 'أحمد محمد',
    driverName: 'علي أحمد',
    pickup: 'شارع الزبيري',
    destination: 'جامعة صنعاء',
    status: 'COMPLETED',
    fare: 150,
    time: 'منذ 5 دقائق',
  },
  {
    id: '2',
    passengerName: 'فاطمة علي',
    driverName: 'محمد سالم',
    pickup: 'السبعين',
    destination: 'شارع هائل',
    status: 'IN_PROGRESS',
    fare: 200,
    time: 'منذ 10 دقائق',
  },
  {
    id: '3',
    passengerName: 'خالد يحيى',
    driverName: 'سعد محمد',
    pickup: 'الحصبة',
    destination: 'المطار',
    status: 'COMPLETED',
    fare: 350,
    time: 'منذ 15 دقيقة',
  },
  {
    id: '4',
    passengerName: 'نور أحمد',
    driverName: 'عبدالله علي',
    pickup: 'شارع الستين',
    destination: 'التحرير',
    status: 'CANCELLED',
    fare: 0,
    time: 'منذ 20 دقيقة',
  },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return <CheckCircle color="success" />;
    case 'IN_PROGRESS':
      return <Schedule color="warning" />;
    case 'CANCELLED':
      return <Cancel color="error" />;
    default:
      return <DirectionsCar />;
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'مكتملة';
    case 'IN_PROGRESS':
      return 'جارية';
    case 'CANCELLED':
      return 'ملغية';
    default:
      return status;
  }
};

const getStatusColor = (status: string): "success" | "warning" | "error" | "default" => {
  switch (status) {
    case 'COMPLETED':
      return 'success';
    case 'IN_PROGRESS':
      return 'warning';
    case 'CANCELLED':
      return 'error';
    default:
      return 'default';
  }
};

const RecentRides: React.FC = () => {
  return (
    <Box sx={{ height: 320, overflow: 'auto' }}>
      <List sx={{ width: '100%' }}>
        {mockRides.map((ride, index) => (
          <React.Fragment key={ride.id}>
            <ListItem alignItems="flex-start" sx={{ px: 0 }}>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  {getStatusIcon(ride.status)}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {ride.passengerName}
                    </Typography>
                    <Chip
                      label={getStatusLabel(ride.status)}
                      size="small"
                      color={getStatusColor(ride.status)}
                      variant="outlined"
                    />
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      السائق: {ride.driverName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      من: {ride.pickup} → إلى: {ride.destination}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {ride.time}
                      </Typography>
                      {ride.fare > 0 && (
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                          {ride.fare} ريال
                        </Typography>
                      )}
                    </Box>
                  </Box>
                }
              />
            </ListItem>
            {index < mockRides.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
    </Box>
  );
};

export default RecentRides;
