# دليل البدء السريع - تكنو درايف

## 🚀 البدء السريع

### 1. تشغيل التطبيق
```bash
npm install
npm start
```
التطبيق سيعمل على: http://localhost:3000

### 2. تسجيل الدخول
- **اسم المستخدم:** `Azal`
- **كلمة المرور:** `password tecno`
- **الرابط:** http://localhost:3000/login

### 3. تفعيل وضع المحاكاة
1. اذهب إلى "حالة الخدمات"
2. فعّل "وضع المحاكاة"
3. ستظهر الخدمات كمتاحة للتطوير

## ⚙️ تعديل عناوين الخدمات

### الطريقة السريعة (واجهة المستخدم)
1. اذه<PERSON> إلى "حالة الخدمات"
2. اضغط "إعدادات الخدمات"
3. عدّل العناوين المطلوبة
4. اضغط "حفظ وفحص الخدمات"

### الطريقة المتقدمة (ملف .env)
```bash
# عدّل هذه القيم في ملف .env
REACT_APP_RIDE_SERVICE_URL=http://your-server:8081
REACT_APP_FLEET_SERVICE_URL=http://your-server:8082
REACT_APP_AUTH_SERVICE_URL=http://your-server:8083
REACT_APP_PAYMENT_SERVICE_URL=http://your-server:8084
REACT_APP_ANALYTICS_SERVICE_URL=http://your-server:8085
REACT_APP_NOTIFICATION_SERVICE_URL=http://your-server:8086
REACT_APP_HR_SERVICE_URL=http://your-server:8087
REACT_APP_PARCEL_SERVICE_URL=http://your-server:8088
REACT_APP_USER_SERVICE_URL=http://your-server:8089
```

## 📊 استيراد/تصدير البيانات

### تصدير البيانات
1. اذهب إلى أي قسم (الرحلات، الموظفين، إلخ)
2. اضغط "المزيد" → "تصدير إلى Excel"
3. سيتم تحميل ملف Excel تلقائياً

### استيراد البيانات
1. اضغط "المزيد" → "استيراد من Excel"
2. اختر ملف Excel المطلوب
3. اضغط "استيراد البيانات"

### قوالب Excel
- يمكن تحميل قوالب جاهزة من قسم "إدارة البيانات"
- القوالب تحتوي على الأعمدة المطلوبة والأمثلة

## 🔧 حل المشاكل الشائعة

### جميع الخدمات تظهر كغير متاحة
**الحل:**
1. فعّل "وضع المحاكاة" من حالة الخدمات
2. أو عدّل عناوين الخدمات في الإعدادات
3. أو تأكد من تشغيل الخوادم الخلفية

### خطأ في تسجيل الدخول
**الحل:**
1. تأكد من البيانات: `Azal` / `password tecno`
2. امسح cache المتصفح
3. تأكد من تشغيل التطبيق على localhost:3000

### مشاكل في استيراد Excel
**الحل:**
1. تأكد من تنسيق الملف (.xlsx أو .xls)
2. استخدم القوالب المتوفرة
3. تحقق من وجود الأعمدة المطلوبة

## 📱 الميزات الرئيسية

### إدارة الرحلات
- عرض وإدارة جميع الرحلات
- تتبع حالة الرحلات (مطلوبة، قيد التنفيذ، مكتملة)
- استيراد/تصدير بيانات الرحلات

### إدارة الأسطول
- مراقبة المركبات والسائقين
- تتبع المواقع على الخريطة
- إدارة الصيانة والأعطال

### الموارد البشرية
- إدارة الموظفين والأقسام
- نظام الصلاحيات الهرمي
- الإدارة التقنية المتخصصة

### الشؤون المالية
- إدارة الفواتير والمدفوعات
- تقارير مالية شاملة
- تتبع الإيرادات والمصروفات

## 🎯 نصائح للاستخدام الأمثل

### للمطورين
- استخدم وضع المحاكاة للتطوير
- راجع ملف `developer-guide.md` للتفاصيل التقنية
- استخدم Oracle APEX للتطبيقات المتقدمة

### للمستخدمين
- راجع `user-guide.md` للدليل الشامل
- استخدم الفلاتر لتسريع البحث
- صدّر البيانات بانتظام للنسخ الاحتياطية

### للإداريين
- راقب حالة الخدمات دورياً
- فعّل المصادقة الثنائية للأمان
- راجع سجلات النظام بانتظام

## 📞 الدعم

### الوثائق
- `user-guide.md` - دليل المستخدم الشامل
- `developer-guide.md` - دليل المطور
- `oracle-apex-guide.md` - دليل Oracle APEX

### المساعدة
- تحقق من حالة الخدمات أولاً
- راجع سجلات الأخطاء في المتصفح
- تواصل مع الفريق التقني عند الحاجة

---

**مرحباً بك في نظام تكنو درايف! 🚗✨**
