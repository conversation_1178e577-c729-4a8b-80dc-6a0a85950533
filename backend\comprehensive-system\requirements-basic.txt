# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database
asyncpg>=0.30.0
sqlalchemy>=2.0.25

# Redis
redis>=5.0.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Data Processing (basic)
pandas>=2.1.0
numpy>=1.26.0

# HTTP Client
httpx>=0.26.0

# Validation
pydantic>=2.5.0

# Configuration
python-dotenv>=1.0.0
pydantic-settings>=2.1.0

# Logging
structlog>=23.2.0

# WebSocket
websockets>=12.0

# Monitoring
prometheus-client>=0.19.0
