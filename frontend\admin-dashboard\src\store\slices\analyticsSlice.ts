import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface AnalyticsData {
  totalRides: number;
  totalRevenue: number;
  activeDrivers: number;
  activePassengers: number;
  averageRating: number;
  ridesGrowth: number;
  revenueGrowth: number;
  dailyRides: Array<{ date: string; count: number }>;
  monthlyRevenue: Array<{ month: string; revenue: number }>;
  topRoutes: Array<{ route: string; count: number }>;
  driverPerformance: Array<{ driverId: string; name: string; rating: number; totalRides: number }>;
}

interface AnalyticsState {
  data: AnalyticsData | null;
  loading: boolean;
  error: string | null;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

const initialState: AnalyticsState = {
  data: null,
  loading: false,
  error: null,
  dateRange: {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  },
};

export const fetchAnalytics = createAsyncThunk(
  'analytics/fetchAnalytics',
  async (params?: { startDate?: string; endDate?: string }) => {
    const { analyticsService } = await import('../../services/analyticsService');
    const response = await analyticsService.getAnalytics(params);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب التحليلات');
    }

    return response;
  }
);

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setDateRange: (state, action) => {
      state.dateRange = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAnalytics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnalytics.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.data;
      })
      .addCase(fetchAnalytics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب التحليلات';
      });
  },
});

export const { clearError, setDateRange } = analyticsSlice.actions;
export default analyticsSlice.reducer;
