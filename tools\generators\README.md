# مولد البيانات الافتراضية لمنصة TecnoDrive - اليمن

## 📋 الوصف

هذا المولد ينشئ بيانات افتراضية شاملة لمنصة TecnoDrive مخصصة للسوق اليمني، ويشمل:

- 🏢 **الشركات والمؤسسات** (المستأجرين)
- 👥 **المستخدمين** (الركاب والمرسلين)
- 🚗 **السائقين** مع مواقعهم في المدن اليمنية
- 🚙 **المركبات** بأنواع مناسبة للسوق اليمني
- 🛣️ **الرحلات** بين المدن اليمنية
- 📦 **الطرود** وخدمات التوصيل

## 🗺️ المدن اليمنية المدعومة

- صنعاء (العاصمة)
- عدن
- تعز
- الحديدة
- إب
- المكلا
- مأرب
- ذمار
- سيئون
- عمران

## 🚀 كيفية الاستخدام

### 1. تثبيت المكتبات المطلوبة

```bash
pip install -r requirements.txt
```

### 2. تشغيل مولد البيانات

```bash
python generate_yemen_data.py
```

### 3. النتائج

سيتم إنشاء مجلد `generated_data` يحتوي على:

- `tenants_data.json` - بيانات الشركات
- `users_data.json` - بيانات المستخدمين
- `drivers_data.json` - بيانات السائقين
- `vehicles_data.json` - بيانات المركبات
- `trips_data.json` - بيانات الرحلات
- `parcels_data.json` - بيانات الطرود

## 📊 إحصائيات البيانات

- **200 شركة ومؤسسة** يمنية
- **200 مستخدم** بأسماء عربية
- **200 سائق** موزعين على المدن اليمنية
- **200 مركبة** بماركات شائعة في اليمن
- **200 رحلة** بين المدن اليمنية
- **200 طرد** لخدمات التوصيل

## 💰 العملة والأسعار

- جميع الأسعار بالريال اليمني
- أسعار الرحلات: 80-150 ريال/كم
- رسوم التوصيل: 3,000-20,000 ريال

## 🔧 تخصيص البيانات

يمكنك تعديل المتغيرات التالية في الكود:

```python
num_records = 200  # عدد السجلات لكل جدول
```

## 📝 ملاحظات

- البيانات مولدة عشوائياً لأغراض التطوير والاختبار
- الإحداثيات الجغرافية دقيقة للمدن اليمنية
- العلاقات بين الجداول محفوظة (مثل ربط المركبات بالسائقين)
- أسماء الشركات والأشخاص مولدة باللغة العربية

## 🛠️ المتطلبات

### الخيار الأول: النسخة الكاملة
- Python 3.7+
- مكتبة Faker للبيانات الافتراضية
- مكتبة uuid (مدمجة في Python)

```bash
# تثبيت Python من https://python.org
# ثم تشغيل:
pip install faker
python generate_yemen_data.py
```

### الخيار الثاني: النسخة المبسطة (بدون مكتبات خارجية)
- Python 3.7+ فقط
- لا تحتاج مكتبات إضافية

```bash
python simple_yemen_data.py
```

## 📁 الملفات المتاحة

1. **`generate_yemen_data.py`** - النسخة الكاملة (تحتاج Faker)
   - 200 سجل لكل جدول
   - بيانات أكثر تفصيلاً وواقعية
   - جميع الجداول (شركات، مستخدمين، سائقين، مركبات، رحلات، طرود)

2. **`simple_yemen_data.py`** - النسخة المبسطة (بدون مكتبات خارجية)
   - 50-100 سجل لكل جدول
   - أسماء وبيانات يمنية أساسية
   - الجداول الأساسية (شركات، مستخدمين، سائقين)

## 🔧 تثبيت Python (إذا لم يكن مثبتاً)

1. اذهب إلى https://python.org/downloads/
2. حمل أحدث إصدار من Python
3. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
4. أعد تشغيل Command Prompt
5. تحقق من التثبيت: `python --version`

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة وثائق المشروع الرئيسي.
