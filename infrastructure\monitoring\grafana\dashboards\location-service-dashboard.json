{"dashboard": {"id": null, "title": "TECNO DRIVE - Location Service Dashboard", "tags": ["tecnodrive", "location", "postgis"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Location Updates per Second", "type": "stat", "targets": [{"expr": "rate(tecnodrive_location_updates_total[5m])", "legendFormat": "Updates/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Active Entities by Type", "type": "piechart", "targets": [{"expr": "tecnodrive_location_updates_total", "legendFormat": "{{entity_type}}"}], "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Response Time", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(tecnodrive_request_duration_bucket{service=\"location-service\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(tecnodrive_request_duration_bucket{service=\"location-service\"}[5m]))", "legendFormat": "50th percentile"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 4, "title": "Database Query Performance", "type": "timeseries", "targets": [{"expr": "rate(tecnodrive_database_query_duration_sum[5m]) / rate(tecnodrive_database_query_duration_count[5m])", "legendFormat": "Avg Query Time"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "PostGIS Spatial Queries", "type": "timeseries", "targets": [{"expr": "rate(tecnodrive_database_query_duration_count{query_type=\"spatial\"}[5m])", "legendFormat": "Spatial Queries/sec"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "Location Accuracy Distribution", "type": "histogram", "targets": [{"expr": "tecnodrive_location_accuracy", "legendFormat": "GPS Accuracy (meters)"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 7, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(tecnodrive_errors_total{service=\"location-service\"}[5m])", "legendFormat": "Errors/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 24}}, {"id": 8, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "jvm_memory_used_bytes{service=\"location-service\"} / jvm_memory_max_bytes{service=\"location-service\"} * 100", "legendFormat": "Memory Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "max": 100}}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 24}}, {"id": 9, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total{service=\"location-service\"}[5m]) * 100", "legendFormat": "CPU Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "max": 100}}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 24}}], "templating": {"list": [{"name": "environment", "type": "query", "query": "label_values(tecnodrive_requests_total, environment)", "current": {"value": "production", "text": "production"}}, {"name": "instance", "type": "query", "query": "label_values(tecnodrive_requests_total{service=\"location-service\"}, instance)", "current": {"value": "all", "text": "All"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up{service=\"location-service\"}[1m])", "iconColor": "blue"}]}}}