# دليل Oracle APEX المفصل لنظام تكنو درايف

## نظرة عامة
هذا الدليل يوضح كيفية إنشاء صفحات Oracle APEX متكاملة لخدمات Payment، Notification، Finance، Analytics، وAudit في نظام تكنو درايف.

## 1. Payment Service - خدمة المدفوعات

### A. REST Data Sources
```sql
-- إنشاء REST Data Sources
/ords/tecno-drive/payment/payments (GET, POST, PUT, DELETE)
/ords/tecno-drive/payment/transactions (GET)
/ords/tecno-drive/payment/refunds (GET, POST)
```

### B. الصفحات المطلوبة

#### 1. Payment History (Interactive Report)
```sql
-- إعدادات الصفحة
Page Type: Interactive Report
Region Type: Interactive Report
Source: REST Data Source - /payment/payments?userId=&status=&from=&to=

-- الأعمدة
Columns: 
- Payment ID
- Date (تاريخ الدفع)
- Ride ID (رقم الرحلة)
- Amount (المبلغ)
- Currency (العملة)
- Status (الحالة)
- Payment Method (طريقة الدفع)
- Paid At (وقت الدفع)

-- الفلاتر
Search Bar: بحث عام
Filters: 
- Status (مكتملة، معلقة، فاشلة)
- Date Range (من تاريخ - إلى تاريخ)
- Payment Method (بطاقة ائتمان، نقد، محفظة إلكترونية)

-- الأزرار
Buttons: "Export CSV/PDF", "New Payment"
```

#### 2. Make Payment (Modal Form)
```sql
-- إعدادات النموذج
Form Type: Modal Dialog
Trigger: Button "New Payment" في IR

-- الحقول
Fields:
- Ride ID (Text Field, Required)
- Amount (Number Field, Required)
- Currency (Select List: SAR, USD, AED)
- Payment Method (Select List: Credit Card, Cash, E-Wallet)
- Notes (Textarea, Optional)

-- Dynamic Action
On Submit → PL/SQL Process:
BEGIN
  apex_web_service.make_rest_request(
    p_url => 'http://localhost:8084/api/payments',
    p_http_method => 'POST',
    p_body => apex_json.stringify(
      apex_json.parse('{
        "rideId": "' || :P_RIDE_ID || '",
        "amount": ' || :P_AMOUNT || ',
        "currency": "' || :P_CURRENCY || '",
        "paymentMethod": "' || :P_PAYMENT_METHOD || '"
      }')
    )
  );
END;

-- On Success
Refresh IR + Show Success Toast
```

#### 3. Payment Analytics (Chart Region)
```sql
-- إعدادات المخطط
Chart Type: Bar Chart
SQL Source: 
SELECT 
  TO_CHAR(payment_date, 'YYYY-MM') as month,
  SUM(amount) as total_amount,
  status
FROM payment_data
GROUP BY TO_CHAR(payment_date, 'YYYY-MM'), status
ORDER BY month;

-- المحاور
X-axis: Month (الشهر)
Y-axis: Total Amount (إجمالي المبالغ)
Series: Status (الحالة)

-- التفاعل
Toggle Button: Group by Status/Method
Drill-down: Click → Filter IR by selected month
```

### C. تحسينات UX للمدفوعات
```sql
-- Conditional Highlighting في IR
CASE 
  WHEN status = 'FAILED' THEN 'u-color-danger'
  WHEN status = 'PENDING' THEN 'u-color-warning'
  WHEN status = 'COMPLETED' THEN 'u-color-success'
END

-- Custom Actions
Row Action "Retry Payment" للمدفوعات الفاشلة:
- Condition: status = 'FAILED'
- Action: Call REST API to retry payment

-- Real-time Updates
WebSocket integration للتحديثات الفورية
```

## 2. Notification Service - خدمة الإشعارات

### A. REST Data Sources
```sql
/ords/tecno-drive/notification/notifications
/ords/tecno-drive/notification/templates
/ords/tecno-drive/notification/settings
/ords/tecno-drive/notification/send
```

### B. الصفحات المطلوبة

#### 1. Notification Center (Interactive Grid)
```sql
-- إعدادات الصفحة
Page Type: Interactive Grid
Source: /notifications?userId=&status=&type=

-- الأعمدة
Columns:
- Time (الوقت)
- Type (النوع: Email, SMS, Push)
- Title (العنوان)
- Message Preview (معاينة الرسالة)
- Status (الحالة: Read, Unread)
- Recipient (المستقبل)
- Action Icons (Mark Read/Delete)

-- Real-Time Update
WebSocket Plugin:
- Event: new_notification
- Action: Add new row to IG
- Refresh: Auto-refresh every 30 seconds

-- Mass Actions
Button "Mark All Read":
UPDATE notifications SET status = 'READ' WHERE user_id = :APP_USER_ID;
```

#### 2. Template Management (Interactive Report + Form)
```sql
-- Template List (IR)
Source: /templates
Columns: Template Name, Type, Created Date, Last Modified, Actions

-- Template Form (Modal)
Fields:
- Template Name (Text, Required)
- Type (Select: Email, SMS, Push)
- Subject (Text, for Email only)
- Body (Rich Text Editor with variables)
- Variables ({{username}}, {{amount}}, {{date}})

-- WYSIWYG Editor Integration
Rich Text Editor: TinyMCE
Variables Toolbar: Insert common variables
Preview Function: Show template with sample data

-- Dynamic Action للمعاينة
On Change Body → AJAX Callback:
- Replace variables with sample data
- Show preview in separate region
```

#### 3. User Notification Settings (Form)
```sql
-- إعدادات المستخدم
Form Source: /settings/{userId}

-- الحقول
Fields:
- Email Notifications (Switch)
- SMS Notifications (Switch)  
- Push Notifications (Switch)
- Frequency (Select: Immediate, Hourly, Daily)
- DND Hours From (Time Picker)
- DND Hours To (Time Picker)
- Notification Types (Checkbox Group)

-- Auto-Save
On Change → AJAX Submit:
PUT /settings/{userId}
Show Toast: "Settings saved"
```

### C. تحسينات UX للإشعارات
```sql
-- Notification Badge
Dynamic Badge Count: SELECT COUNT(*) FROM notifications WHERE status = 'unread'

-- Sound Notifications
JavaScript: Play notification sound for new messages

-- Bulk Operations
Select Multiple → Bulk Delete/Mark Read
```

## 3. Finance Service - خدمة الشؤون المالية

### A. REST Data Sources
```sql
/ords/tecno-drive/finance/invoices
/ords/tecno-drive/finance/invoice_payments
/ords/tecno-drive/finance/expenses
/ords/tecno-drive/finance/revenue
```

### B. الصفحات المطلوبة

#### 1. Invoice Management (Interactive Grid)
```sql
-- إعدادات الصفحة
Source: /invoices?tenantId=&status=&from=&to=

-- الأعمدة
Columns:
- Invoice# (رقم الفاتورة)
- Customer Name (اسم العميل)
- Issue Date (تاريخ الإصدار)
- Due Date (تاريخ الاستحقاق)
- Amount (المبلغ)
- Amount Due (المبلغ المستحق)
- Status (الحالة)
- Actions (Pay/View PDF/Edit)

-- Conditional Highlighting
CASE 
  WHEN due_date < SYSDATE AND status != 'PAID' THEN 'u-color-danger' -- Overdue
  WHEN due_date - SYSDATE <= 7 AND status != 'PAID' THEN 'u-color-warning' -- Due Soon
  WHEN status = 'PAID' THEN 'u-color-success'
END
```

#### 2. Invoice Detail Page (Form + Chart)
```sql
-- Invoice Form Region
Form Fields:
- Invoice Details (Read-only)
- Payment History (Sub-region IG)
- Payment Status Chart

-- Payment Status Chart
Chart Type: Donut Chart
SQL:
SELECT 
  CASE WHEN amount_paid >= total_amount THEN 'Paid'
       WHEN amount_paid > 0 THEN 'Partially Paid'
       ELSE 'Unpaid'
  END as status,
  COUNT(*) as count
FROM invoices
GROUP BY status;

-- PDF Generation
Button "Generate PDF":
PL/SQL Process using APEX_PDF or ORDS PDF Printing
```

#### 3. Financial Dashboard
```sql
-- KPI Cards Region
SELECT 
  SUM(CASE WHEN status = 'PAID' THEN amount ELSE 0 END) as total_revenue,
  SUM(CASE WHEN status != 'PAID' THEN amount ELSE 0 END) as outstanding,
  COUNT(*) as total_invoices
FROM invoices;

-- Revenue Chart (Bar Chart)
SELECT 
  TO_CHAR(payment_date, 'YYYY-MM') as month,
  SUM(amount) as revenue
FROM payments
GROUP BY TO_CHAR(payment_date, 'YYYY-MM')
ORDER BY month;

-- Outstanding Balance (Line Chart)
SELECT 
  date_created,
  SUM(amount - amount_paid) OVER (ORDER BY date_created) as running_balance
FROM invoices
ORDER BY date_created;
```

### C. تحسينات UX للشؤون المالية
```sql
-- Auto-Invoice Generation
APEX Scheduler Job:
- Frequency: Monthly
- Action: Generate recurring invoices
- Notification: Email to finance team

-- Inline PDF Viewer
IFRAME Region: Display PDF invoices inline

-- Payment Reminders
Automated email reminders for overdue invoices
```

## 4. Analytics Service - خدمة التحليلات

### A. REST Data Sources
```sql
/ords/tecno-drive/analytics/events
/ords/tecno-drive/analytics/usage
/ords/tecno-drive/analytics/kpis
/ords/tecno-drive/analytics/reports
```

### B. الصفحات المطلوبة

#### 1. Analytics Dashboard
```sql
-- KPI Cards Region
Cards:
- Total Rides (إجمالي الرحلات)
- Total Revenue (إجمالي الإيرادات)  
- Active Users (المستخدمون النشطون)
- Active Tenants (المستأجرون النشطون)

-- Charts Region
Chart 1 - Line Chart: Rides per Day
Source: events?type=RIDE_COMPLETE&groupBy=date

Chart 2 - Area Chart: Revenue per Day  
Source: usage?metric=revenue&groupBy=date

Chart 3 - Column Chart: New Users per Day
Source: events?type=USER_SIGNUP&groupBy=date

-- Interactive Report: Raw Events
Source: /events with filters for Type, Date Range, User
```

#### 2. Custom Report Builder
```sql
-- Parameter Controls
- Report Type (Select List)
- Date Range (Date Picker)
- Filters (Dynamic based on report type)

-- Dynamic SQL Generation
PL/SQL Function:
FUNCTION build_report_sql(
  p_report_type VARCHAR2,
  p_date_from DATE,
  p_date_to DATE,
  p_filters VARCHAR2
) RETURN VARCHAR2;

-- Export Options
- Excel Export
- PDF Report
- Scheduled Email Reports
```

### C. تحسينات UX للتحليلات
```sql
-- Drill-Down Charts
JavaScript: 
chart.on('click', function(params) {
  // Filter IR by selected data point
  apex.region('events_ir').refresh({
    date: params.name,
    type: params.seriesName
  });
});

-- Chart Annotations
Add manual annotations to charts for important events

-- Real-time Updates
WebSocket: Update charts every minute with new data
```

## 5. Audit Service - خدمة التدقيق

### A. REST Data Sources
```sql
/ords/tecno-drive/audit/audit_logs
/ords/tecno-drive/audit/user_activities
/ords/tecno-drive/audit/system_events
```

### B. الصفحات المطلوبة

#### 1. Audit Logs (Interactive Report)
```sql
-- إعدادات الصفحة
Source: /audit_logs?entity=&userId=&from=&to=&action=

-- الأعمدة
Columns:
- Timestamp (الوقت)
- User (المستخدم)
- Action (الإجراء: CREATE, UPDATE, DELETE)
- Entity (الكيان)
- Entity ID (معرف الكيان)
- IP Address (عنوان IP)
- User Agent (متصفح المستخدم)
- Old Data (البيانات القديمة)
- New Data (البيانات الجديدة)

-- الفلاتر
Filters:
- Entity Type (نوع الكيان)
- User (المستخدم)
- Action Type (نوع الإجراء)
- Date Range (نطاق التاريخ)
- IP Address (عنوان IP)
```

#### 2. Audit Log Detail (Modal Dialog)
```sql
-- Modal Content
Row Action "View Details" → Modal Region:
- JSON Viewer for Old/New Data
- Syntax Highlighting
- Diff View (show changes)

-- JSON Formatting
PL/SQL Function:
FUNCTION format_json(p_json_data CLOB) RETURN CLOB;
-- Use APEX_JSON.prettify for formatting
```

#### 3. Audit Analytics
```sql
-- Actions by Type (Bar Chart)
SELECT action_type, COUNT(*) as count
FROM audit_logs
WHERE timestamp >= SYSDATE - 30
GROUP BY action_type;

-- Activity Timeline (Line Chart)
SELECT 
  TO_CHAR(timestamp, 'YYYY-MM-DD HH24') as hour,
  COUNT(*) as events
FROM audit_logs
GROUP BY TO_CHAR(timestamp, 'YYYY-MM-DD HH24')
ORDER BY hour;

-- Critical Events (IR)
Source: Latest 100 critical events (DELETE, SECURITY_ALERT)
Auto-refresh: Every 5 minutes
```

### C. تحسينات UX للتدقيق
```sql
-- Color-Coded Rows
CASE action_type
  WHEN 'CREATE' THEN 'u-color-success'
  WHEN 'UPDATE' THEN 'u-color-info'  
  WHEN 'DELETE' THEN 'u-color-danger'
  WHEN 'LOGIN' THEN 'u-color-warning'
END

-- Quick Filter Toolbar
Buttons for common filters:
- Today's Activities
- My Activities  
- Critical Events
- Failed Logins

-- Export Single Log
Button: Export individual log entry as JSON file
```

## 6. أفضل الممارسات العامة

### A. Responsive Design
```css
/* استخدام Grid Layouts */
.apex-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

/* Universal Theme Responsive Classes */
.u-hide-sm-down { display: none !important; }
@media (min-width: 641px) {
  .u-hide-sm-down { display: block !important; }
}
```

### B. Dynamic Actions للتفاعل السلس
```javascript
// تحديث المناطق بدون إعادة تحميل
apex.region('my_region').refresh();

// إظهار رسائل النجاح
apex.message.showPageSuccess('تم الحفظ بنجاح');

// تحديث المخططات
apex.region('chart_region').call('refresh');
```

### C. Accessibility
```html
<!-- عناوين ARIA -->
<div role="region" aria-label="قائمة المدفوعات">
  
<!-- تباين لوني كافٍ -->
.high-contrast {
  background-color: #000;
  color: #fff;
}

<!-- دعم لوحة المفاتيح -->
tabindex="0" 
onkeypress="if(event.keyCode==13) handleClick();"
```

### D. Localization
```sql
-- Text Messages في Shared Components
CREATE_SUCCESS_MSG: 'تم الإنشاء بنجاح'
UPDATE_SUCCESS_MSG: 'تم التحديث بنجاح'
DELETE_CONFIRM_MSG: 'هل أنت متأكد من الحذف؟'

-- استخدام في الكود
apex.message.showPageSuccess(apex.lang.getMessage('CREATE_SUCCESS_MSG'));
```

### E. Security
```sql
-- JWT Authentication Scheme
Authentication Type: Custom
PL/SQL Code:
FUNCTION authenticate RETURN BOOLEAN IS
  l_token VARCHAR2(4000) := apex_application.g_x01;
BEGIN
  -- Validate JWT token
  RETURN validate_jwt_token(l_token);
END;

-- CORS Configuration
ORDS Configuration:
ords.security.cors.enabled=true
ords.security.cors.allowed.origins=*
```

### F. Performance
```sql
-- Partial Page Caching
Cache Type: Page
Cache Condition: :APP_USER_ID IS NOT NULL
Cache Timeout: 300 seconds

-- APEX Collections للتخزين المؤقت
apex_collection.create_or_truncate_collection('TEMP_DATA');
apex_collection.add_member('TEMP_DATA', p_c001 => 'value');
```

هذا الدليل المفصل يوفر إطار عمل شامل لبناء تطبيق Oracle APEX متكامل لنظام تكنو درايف مع جميع الخدمات المطلوبة.
