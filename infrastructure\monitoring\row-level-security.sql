-- Row-Level Security (RLS) Configuration for TecnoDrive Platform
-- This script implements comprehensive row-level security policies

-- Enable RLS on sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;
ALTER TABLE parcels ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_data ENABLE ROW LEVEL SECURITY;

-- Create security context function
CREATE OR R<PERSON>LACE FUNCTION get_current_user_id() RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.current_user_id', true)::UUID,
        '00000000-0000-0000-0000-000000000000'::UUID
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create role checking function
CREATE OR REPLACE FUNCTION has_role(required_role TEXT) RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.current_user_role', true) = required_role,
        false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create tenant checking function
CREATE OR REPLACE FUNCTION get_current_tenant_id() RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.current_tenant_id', true)::UUID,
        '00000000-0000-0000-0000-000000000000'::UUID
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY users_own_data ON users
    FOR ALL
    TO application_user
    USING (id = get_current_user_id());

CREATE POLICY users_admin_access ON users
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY users_hr_read ON users
    FOR SELECT
    TO application_user
    USING (has_role('HR_MANAGER'));

-- Rides table policies
CREATE POLICY rides_passenger_access ON rides
    FOR ALL
    TO application_user
    USING (passenger_id = get_current_user_id());

CREATE POLICY rides_driver_access ON rides
    FOR ALL
    TO application_user
    USING (driver_id = get_current_user_id());

CREATE POLICY rides_admin_access ON rides
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY rides_fleet_manager_access ON rides
    FOR SELECT
    TO application_user
    USING (has_role('FLEET_MANAGER'));

-- Parcels table policies
CREATE POLICY parcels_sender_access ON parcels
    FOR ALL
    TO application_user
    USING (sender_id = get_current_user_id());

CREATE POLICY parcels_recipient_access ON parcels
    FOR SELECT
    TO application_user
    USING (recipient_id = get_current_user_id());

CREATE POLICY parcels_driver_access ON parcels
    FOR SELECT
    TO application_user
    USING (driver_id = get_current_user_id());

CREATE POLICY parcels_admin_access ON parcels
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

-- Payments table policies
CREATE POLICY payments_user_access ON payments
    FOR ALL
    TO application_user
    USING (user_id = get_current_user_id());

CREATE POLICY payments_admin_access ON payments
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY payments_finance_access ON payments
    FOR SELECT
    TO application_user
    USING (has_role('FINANCE_MANAGER'));

-- Location data policies (most sensitive)
CREATE POLICY location_driver_own_data ON location_data
    FOR ALL
    TO application_user
    USING (driver_id = get_current_user_id());

CREATE POLICY location_admin_access ON location_data
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY location_fleet_manager_access ON location_data
    FOR SELECT
    TO application_user
    USING (has_role('FLEET_MANAGER'));

-- Vehicles table policies
CREATE POLICY vehicles_driver_assigned ON vehicles
    FOR SELECT
    TO application_user
    USING (
        driver_id = get_current_user_id() OR
        has_role('ADMIN') OR
        has_role('FLEET_MANAGER')
    );

CREATE POLICY vehicles_admin_full_access ON vehicles
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY vehicles_fleet_manager_access ON vehicles
    FOR ALL
    TO application_user
    USING (has_role('FLEET_MANAGER'));

-- Financial transactions policies
CREATE POLICY financial_admin_access ON financial_transactions
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY financial_finance_manager_access ON financial_transactions
    FOR ALL
    TO application_user
    USING (has_role('FINANCE_MANAGER'));

CREATE POLICY financial_user_own_transactions ON financial_transactions
    FOR SELECT
    TO application_user
    USING (user_id = get_current_user_id());

-- Employee data policies (HR sensitive)
CREATE POLICY employee_own_data ON employee_data
    FOR SELECT
    TO application_user
    USING (user_id = get_current_user_id());

CREATE POLICY employee_hr_access ON employee_data
    FOR ALL
    TO application_user
    USING (has_role('HR_MANAGER') OR has_role('ADMIN'));

CREATE POLICY employee_manager_access ON employee_data
    FOR SELECT
    TO application_user
    USING (
        manager_id = get_current_user_id() AND
        has_role('MANAGER')
    );

-- Multi-tenant policies (if using SaaS model)
-- Add tenant_id column to tables if not exists
-- ALTER TABLE users ADD COLUMN IF NOT EXISTS tenant_id UUID;
-- ALTER TABLE rides ADD COLUMN IF NOT EXISTS tenant_id UUID;
-- ALTER TABLE parcels ADD COLUMN IF NOT EXISTS tenant_id UUID;

-- Tenant isolation policies
CREATE POLICY users_tenant_isolation ON users
    FOR ALL
    TO application_user
    USING (
        tenant_id = get_current_tenant_id() OR
        has_role('SUPER_ADMIN')
    );

CREATE POLICY rides_tenant_isolation ON rides
    FOR ALL
    TO application_user
    USING (
        tenant_id = get_current_tenant_id() OR
        has_role('SUPER_ADMIN')
    );

CREATE POLICY parcels_tenant_isolation ON parcels
    FOR ALL
    TO application_user
    USING (
        tenant_id = get_current_tenant_id() OR
        has_role('SUPER_ADMIN')
    );

-- Audit trail policies
CREATE POLICY audit_admin_only ON audit_log
    FOR ALL
    TO application_user
    USING (has_role('ADMIN'));

CREATE POLICY audit_user_own_actions ON audit_log
    FOR SELECT
    TO application_user
    USING (user_id = get_current_user_id());

-- Time-based access policies
CREATE OR REPLACE FUNCTION is_business_hours() RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXTRACT(hour FROM NOW()) BETWEEN 6 AND 22;
END;
$$ LANGUAGE plpgsql;

-- Restrict sensitive operations to business hours
CREATE POLICY financial_business_hours ON financial_transactions
    FOR INSERT
    TO application_user
    WITH CHECK (
        is_business_hours() OR
        has_role('ADMIN')
    );

-- Data retention policies
CREATE OR REPLACE FUNCTION is_data_retention_compliant(created_date TIMESTAMP) RETURNS BOOLEAN AS $$
BEGIN
    -- Keep data for 7 years for compliance
    RETURN created_date > NOW() - INTERVAL '7 years';
END;
$$ LANGUAGE plpgsql;

CREATE POLICY location_data_retention ON location_data
    FOR SELECT
    TO application_user
    USING (
        is_data_retention_compliant(created_at) AND
        (driver_id = get_current_user_id() OR has_role('ADMIN'))
    );

-- Emergency access policy (can be enabled in emergencies)
CREATE OR REPLACE FUNCTION emergency_access_enabled() RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.emergency_access', true)::BOOLEAN,
        false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE POLICY emergency_access ON users
    FOR ALL
    TO application_user
    USING (
        emergency_access_enabled() AND
        has_role('EMERGENCY_RESPONDER')
    );

-- Create application roles
CREATE ROLE application_user;
CREATE ROLE application_admin;
CREATE ROLE application_readonly;

-- Grant permissions
GRANT CONNECT ON DATABASE tecnodrive_db TO application_user;
GRANT USAGE ON SCHEMA public TO application_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO application_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO application_user;

-- Admin role inherits from application_user
GRANT application_user TO application_admin;

-- Readonly role
GRANT CONNECT ON DATABASE tecnodrive_db TO application_readonly;
GRANT USAGE ON SCHEMA public TO application_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO application_readonly;

-- Create function to set security context
CREATE OR REPLACE FUNCTION set_security_context(
    user_id UUID,
    user_role TEXT,
    tenant_id UUID DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_id', user_id::TEXT, true);
    PERFORM set_config('app.current_user_role', user_role, true);
    IF tenant_id IS NOT NULL THEN
        PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, true);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clear security context
CREATE OR REPLACE FUNCTION clear_security_context() RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_id', '', true);
    PERFORM set_config('app.current_user_role', '', true);
    PERFORM set_config('app.current_tenant_id', '', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create monitoring view for RLS policies
CREATE OR REPLACE VIEW rls_policy_status AS
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    (SELECT count(*) FROM pg_policies WHERE schemaname = t.schemaname AND tablename = t.tablename) as policy_count
FROM pg_tables t
WHERE schemaname = 'public'
ORDER BY tablename;

-- Grant access to monitoring view
GRANT SELECT ON rls_policy_status TO application_admin;

COMMENT ON VIEW rls_policy_status IS 'Monitoring view for Row Level Security status';
COMMENT ON FUNCTION set_security_context IS 'Set security context for current session';
COMMENT ON FUNCTION clear_security_context IS 'Clear security context for current session';
COMMENT ON FUNCTION get_current_user_id IS 'Get current user ID from session context';
COMMENT ON FUNCTION has_role IS 'Check if current user has specified role';
COMMENT ON FUNCTION get_current_tenant_id IS 'Get current tenant ID from session context';
