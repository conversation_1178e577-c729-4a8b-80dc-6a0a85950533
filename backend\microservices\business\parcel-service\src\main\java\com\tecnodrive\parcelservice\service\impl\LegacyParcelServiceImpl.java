package com.tecnodrive.parcelservice.service.impl;

import com.tecnodrive.parcelservice.dto.DeliveryDto;
import com.tecnodrive.parcelservice.dto.ParcelMapper;
import com.tecnodrive.parcelservice.dto.ParcelRequestDto;
import com.tecnodrive.parcelservice.dto.ParcelResponseDto;
import com.tecnodrive.parcelservice.entity.Delivery;
import com.tecnodrive.parcelservice.entity.Parcel;
import com.tecnodrive.parcelservice.repository.DeliveryRepository;
import com.tecnodrive.parcelservice.service.ParcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

/**
 * Legacy Parcel Service Implementation
 * Note: Use EnhancedParcelService for new features and better functionality.
 */
@Service("legacyParcelService")
@RequiredArgsConstructor
@Slf4j
public class LegacyParcelServiceImpl implements ParcelService {

    private final DeliveryRepository deliveryRepository;
    private final ParcelMapper mapper;

    @Override
    @Transactional
    public ParcelResponseDto createParcel(ParcelRequestDto dto) {
        log.warn("Using legacy parcel service - consider migrating to EnhancedParcelService");
        
        try {
            Parcel parcel = mapper.toLegacyEntity(dto);
            parcel.setTrackingNumber("TRK-" + UUID.randomUUID());

            // Legacy implementation - limited functionality
            log.info("Legacy parcel created with tracking: {}", parcel.getTrackingNumber());

            return mapper.toLegacyDto(parcel);
        } catch (Exception e) {
            log.error("Error in legacy parcel service: {}", e.getMessage());
            throw new RuntimeException("Legacy service error - use EnhancedParcelService instead");
        }
    }

    @Override
    @Transactional
    public void assignDelivery(DeliveryDto dto) {
        log.info("Assigning delivery using legacy service");
        
        try {
            Delivery delivery = mapper.toDeliveryEntity(dto);
            delivery.setAssignedAt(Instant.now());
            deliveryRepository.save(delivery);
            
            log.info("Delivery assigned successfully");
        } catch (Exception e) {
            log.error("Error assigning delivery: {}", e.getMessage());
            throw new RuntimeException("Error assigning delivery: " + e.getMessage());
        }
    }
}
