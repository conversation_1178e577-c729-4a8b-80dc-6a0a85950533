# 🚀 دليل البدء السريع - مولد البيانات اليمنية

## ⚡ الطريقة الأسرع (بدون تثبيت)

استخدم الملف الجاهز:
```
sample_yemen_data.json
```
يحتوي على عينة من البيانات اليمنية جاهزة للاستخدام.

## 🖱️ تشغيل تلقائي (Windows)

### الخيار 1: ملف Batch
```bash
# انقر مرتين على:
run_generator.bat
```

### الخيار 2: PowerShell
```powershell
# انقر بالزر الأيمن واختر "Run with PowerShell":
run_generator.ps1
```

## 💻 تشغيل يدوي

### إذا كان Python مثبت:
```bash
# النسخة الكاملة (مع Faker):
python generate_yemen_data.py

# النسخة المبسطة (بدون مكتبات):
python simple_yemen_data.py
```

### إذا لم يكن Python مثبت:
1. حمل Python من: https://python.org/downloads/
2. تأكد من تحديد "Add Python to PATH"
3. أعد تشغيل Command Prompt
4. شغل الأوامر أعلاه

## 📁 النتائج

ستجد الملفات في مجلد:
```
generated_data/
├── tenants_data.json      # الشركات
├── users_data.json        # المستخدمين  
├── drivers_data.json      # السائقين
├── vehicles_data.json     # المركبات
├── trips_data.json        # الرحلات
└── parcels_data.json      # الطرود
```

## 🔧 استكشاف الأخطاء

### خطأ: "python is not recognized"
- Python غير مثبت أو غير موجود في PATH
- **الحل**: ثبت Python من الرابط أعلاه

### خطأ: "No module named 'faker'"
- مكتبة Faker غير مثبتة
- **الحل**: `pip install faker` أو استخدم `simple_yemen_data.py`

### خطأ: "Permission denied"
- مشكلة في الصلاحيات
- **الحل**: شغل Command Prompt كـ Administrator

## 📊 محتوى البيانات

- **أسماء يمنية حقيقية** (أحمد، فاطمة، الحوثي، الزبيدي...)
- **مدن يمنية** مع إحداثيات دقيقة
- **أرقام هواتف يمنية** (+967...)
- **أسعار بالريال اليمني**
- **شركات نقل محلية**

## 🎯 للاستخدام المباشر

إذا كنت تريد البيانات فوراً بدون تثبيت أي شيء:
```json
// استخدم sample_yemen_data.json
// يحتوي على بيانات جاهزة للاختبار
```
