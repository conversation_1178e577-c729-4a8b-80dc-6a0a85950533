import axios from 'axios';
import { io, Socket } from 'socket.io-client';

// Types for Risk Management
export interface RiskEvent {
  id: string;
  tenantId: string;
  relatedEntityId: string;
  entityType: 'vehicle' | 'driver' | 'trip' | 'parcel' | 'system';
  riskType: 'security' | 'operational' | 'financial' | 'compliance' | 'safety';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'investigating' | 'mitigating' | 'resolved' | 'closed';
  title: string;
  description: string;
  riskScore: number;
  mitigationActions: MitigationAction[];
  detectedAt: string;
  resolvedAt?: string;
  assignedTo?: string;
  tags: string[];
  metadata: Record<string, any>;
}

export interface MitigationAction {
  id: string;
  action: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  assignedTo: string;
  dueDate: string;
  completedAt?: string;
  notes?: string;
}

export interface RiskMetrics {
  totalRisks: number;
  risksByType: Record<string, number>;
  risksBySeverity: Record<string, number>;
  riskTrend: Array<{ date: string; count: number; severity: string }>;
  averageResolutionTime: number;
  openRisks: number;
  criticalRisks: number;
}

export interface SecurityAlert {
  id: string;
  source: 'siem' | 'ids' | 'firewall' | 'endpoint';
  alertType: string;
  severity: string;
  timestamp: string;
  description: string;
  affectedAssets: string[];
  recommendations: string[];
}

class RiskManagementService {
  private socket: Socket | null = null;
  private baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    this.socket = io(`${this.baseURL}/risk-management`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to Risk Management WebSocket');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from Risk Management WebSocket');
    });
  }

  // Real-time risk event subscriptions
  subscribeToRiskEvents(tenantId: string, callback: (event: RiskEvent) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-risk-events', { tenantId });
      this.socket.on('new-risk-event', callback);
    }
  }

  subscribeToRiskUpdates(callback: (event: RiskEvent) => void) {
    if (this.socket) {
      this.socket.on('risk-event-updated', callback);
    }
  }

  subscribeToSecurityAlerts(tenantId: string, callback: (alert: SecurityAlert) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-security-alerts', { tenantId });
      this.socket.on('new-security-alert', callback);
    }
  }

  // API Methods
  async getRiskEvents(tenantId: string, filters?: {
    riskType?: string;
    severity?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<RiskEvent[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/risk-management/events`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching risk events:', error);
      throw error;
    }
  }

  async getRiskMetrics(tenantId: string, timeframe: string = '30d'): Promise<RiskMetrics> {
    try {
      const response = await axios.get(`${this.baseURL}/api/risk-management/metrics`, {
        params: { tenantId, timeframe },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching risk metrics:', error);
      throw error;
    }
  }

  async createRiskEvent(riskEvent: Omit<RiskEvent, 'id' | 'detectedAt'>): Promise<RiskEvent> {
    try {
      const response = await axios.post(`${this.baseURL}/api/risk-management/events`, riskEvent, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error creating risk event:', error);
      throw error;
    }
  }

  async updateRiskEvent(id: string, updates: Partial<RiskEvent>): Promise<RiskEvent> {
    try {
      const response = await axios.put(`${this.baseURL}/api/risk-management/events/${id}`, updates, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating risk event:', error);
      throw error;
    }
  }

  async addMitigationAction(riskEventId: string, action: Omit<MitigationAction, 'id'>): Promise<MitigationAction> {
    try {
      const response = await axios.post(
        `${this.baseURL}/api/risk-management/events/${riskEventId}/mitigation-actions`,
        action,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error adding mitigation action:', error);
      throw error;
    }
  }

  async updateMitigationAction(
    riskEventId: string,
    actionId: string,
    updates: Partial<MitigationAction>
  ): Promise<MitigationAction> {
    try {
      const response = await axios.put(
        `${this.baseURL}/api/risk-management/events/${riskEventId}/mitigation-actions/${actionId}`,
        updates,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error updating mitigation action:', error);
      throw error;
    }
  }

  async getSecurityAlerts(tenantId: string, timeframe: string = '24h'): Promise<SecurityAlert[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/risk-management/security-alerts`, {
        params: { tenantId, timeframe },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching security alerts:', error);
      throw error;
    }
  }

  async calculateRiskScore(riskEvent: Partial<RiskEvent>): Promise<number> {
    try {
      const response = await axios.post(`${this.baseURL}/api/risk-management/calculate-score`, riskEvent, {
        headers: this.getAuthHeaders(),
      });
      return response.data.score;
    } catch (error) {
      console.error('Error calculating risk score:', error);
      throw error;
    }
  }

  async exportRiskReport(tenantId: string, format: 'pdf' | 'excel', filters?: any): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/api/risk-management/export`, {
        params: { tenantId, format, ...filters },
        headers: this.getAuthHeaders(),
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting risk report:', error);
      throw error;
    }
  }

  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

export const riskManagementService = new RiskManagementService();
export default riskManagementService;
