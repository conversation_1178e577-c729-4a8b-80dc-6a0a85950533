// TECNO DRIVE - Integration Test Suite
// Auto-generated on: 2025-07-26T22:53:56.532Z

import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080';

describe('TECNO DRIVE Integration Tests', () => {
  // Test API Gateway
  test('API Gateway Health Check', async () => {
    const response = await axios.get(`${API_BASE_URL}/health`);
    expect(response.status).toBe(200);
  });

  // Test Authentication
  test('Authentication Service', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };
    
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, loginData);
    expect(response.status).toBe(200);
    expect(response.data).toHaveProperty('token');
  });

  // Test Core Services
  test('User Service Integration', async () => {
    const response = await axios.get(`${API_BASE_URL}/api/users`, {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(response.status).toBe(200);
  });

  test('Ride Service Integration', async () => {
    const response = await axios.get(`${API_BASE_URL}/api/rides`, {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(response.status).toBe(200);
  });

  test('Fleet Service Integration', async () => {
    const response = await axios.get(`${API_BASE_URL}/api/fleet/vehicles`, {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(response.status).toBe(200);
  });

  // Test Business Services
  test('Analytics Service Integration', async () => {
    const response = await axios.get(`${API_BASE_URL}/api/analytics/dashboard`, {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(response.status).toBe(200);
  });

  test('Parcel Service Integration', async () => {
    const response = await axios.get(`${API_BASE_URL}/api/parcels`, {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(response.status).toBe(200);
  });
});

// WebSocket Integration Tests
describe('WebSocket Integration', () => {
  test('Real-time Connection', (done) => {
    const socket = io(`${API_BASE_URL}`);
    
    socket.on('connect', () => {
      expect(socket.connected).toBe(true);
      socket.disconnect();
      done();
    });
  });
});
