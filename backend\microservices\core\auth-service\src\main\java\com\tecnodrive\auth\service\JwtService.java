package com.tecnodrive.auth.service;

import com.tecnodrive.auth.entity.User;

import java.util.Map;

/**
 * JWT Service Interface
 */
public interface JwtService {

    /**
     * Generate access token for user
     */
    String generateAccessToken(User user);

    /**
     * Generate refresh token for user
     */
    String generateRefreshToken(User user);

    /**
     * Generate token with custom claims
     */
    String generateToken(Map<String, Object> extraClaims, User user);

    /**
     * Extract username from token
     */
    String extractUsername(String token);

    /**
     * Extract user ID from token
     */
    String extractUserId(String token);

    /**
     * Extract claim from token
     */
    <T> T extractClaim(String token, String claimName, Class<T> type);

    /**
     * Validate token against user
     */
    boolean isTokenValid(String token, User user);

    /**
     * Check if token is expired
     */
    boolean isTokenExpired(String token);

    /**
     * Get token expiration time in seconds
     */
    long getAccessTokenExpirationTime();

    /**
     * Get refresh token expiration time in seconds
     */
    long getRefreshTokenExpirationTime();

    /**
     * Validate token format and signature
     */
    boolean validateTokenFormat(String token);

    /**
     * Extract all claims from token
     */
    Map<String, Object> extractAllClaims(String token);
}
