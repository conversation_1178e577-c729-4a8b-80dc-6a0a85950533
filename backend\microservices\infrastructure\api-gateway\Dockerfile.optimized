# =============================================================================
# TECNO DRIVE API Gateway - Optimized Multi-stage Dockerfile
# =============================================================================

# Build stage with Maven cache optimization
FROM maven:3.9.4-openjdk-17-slim AS dependencies

WORKDIR /app

# Copy only pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies offline for better caching
RUN mvn dependency:go-offline -B --no-transfer-progress

# =============================================================================
# Build stage
FROM maven:3.9.4-openjdk-17-slim AS builder

WORKDIR /app

# Copy dependencies from previous stage
COPY --from=dependencies /root/.m2 /root/.m2
COPY pom.xml .

# Copy source code
COPY src ./src

# Build application with optimizations
RUN mvn clean package -DskipTests --no-transfer-progress \
    -Dmaven.compile.fork=true \
    -Dmaven.compiler.maxmem=1024m

# Verify JAR was created
RUN ls -la target/ && test -f target/*.jar

# =============================================================================
# Runtime stage with minimal JRE
FROM eclipse-temurin:17-jre-jammy AS runtime

# Metadata labels for better container management
LABEL maintainer="<EMAIL>" \
      org.opencontainers.image.title="TECNO DRIVE API Gateway" \
      org.opencontainers.image.description="High-performance API Gateway for TECNO DRIVE platform" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.source="https://github.com/tecno-drive/api-gateway" \
      org.opencontainers.image.vendor="TECNO DRIVE" \
      org.opencontainers.image.licenses="MIT"

# Install minimal required packages and clean up in single layer
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
        tzdata && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Set timezone
ENV TZ=Asia/Riyadh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application directory and user early
RUN groupadd --system --gid 1001 spring && \
    useradd --system --uid 1001 --gid spring --shell /bin/false spring

# Create app directory with proper permissions
WORKDIR /app
RUN chown -R spring:spring /app

# Copy JAR file with proper ownership
COPY --from=builder --chown=spring:spring /app/target/*.jar app.jar

# Switch to non-root user before any file operations
USER spring:spring

# Expose port
EXPOSE 8080

# Enhanced health check with better error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health/readiness || exit 1

# Optimized JVM settings for containers
ENV JAVA_OPTS="-server \
    -XX:+UseG1GC \
    -XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:InitialRAMPercentage=50.0 \
    -XX:+OptimizeStringConcat \
    -XX:+UseStringDeduplication \
    -XX:+ExitOnOutOfMemoryError \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/tmp/heapdump.hprof \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.main.allow-bean-definition-overriding=true \
    -Dspring.jmx.enabled=false \
    -Dmanagement.endpoint.health.probes.enabled=true"

# Application-specific environment variables
ENV SPRING_PROFILES_ACTIVE=docker
ENV SERVER_PORT=8080
ENV MANAGEMENT_SERVER_PORT=8080

# Use exec form for better signal handling
ENTRYPOINT ["sh", "-c", "exec java $JAVA_OPTS -jar app.jar"]

# =============================================================================
# Alternative Distroless version (uncomment to use)
# =============================================================================

# FROM gcr.io/distroless/java17-debian11 AS distroless
# 
# LABEL maintainer="<EMAIL>"
# 
# WORKDIR /app
# 
# COPY --from=builder /app/target/*.jar app.jar
# 
# EXPOSE 8080
# 
# USER 1001:1001
# 
# ENV JAVA_OPTS="-server -XX:+UseG1GC -XX:MaxRAMPercentage=75.0"
# 
# ENTRYPOINT ["java", "-jar", "app.jar"]
