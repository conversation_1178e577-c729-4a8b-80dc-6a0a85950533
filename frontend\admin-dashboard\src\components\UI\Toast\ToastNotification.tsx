import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  IconButton,
  Box,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

interface ToastNotificationProps {
  open: boolean;
  onClose: () => void;
  message: string;
  title?: string;
  severity?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  position?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'center' | 'right';
  };
  action?: React.ReactNode;
  variant?: 'filled' | 'outlined' | 'standard';
}

const ToastNotification: React.FC<ToastNotificationProps> = ({
  open,
  onClose,
  message,
  title,
  severity = 'info',
  duration = 6000,
  position = { vertical: 'top', horizontal: 'right' },
  action,
  variant = 'filled',
}) => {
  const theme = useTheme();

  const getIcon = () => {
    const iconMap = {
      success: <SuccessIcon />,
      error: <ErrorIcon />,
      warning: <WarningIcon />,
      info: <InfoIcon />,
    };
    return iconMap[severity];
  };

  const getCustomStyles = () => {
    const colorMap = {
      success: theme.palette.success.main,
      error: theme.palette.error.main,
      warning: theme.palette.warning.main,
      info: theme.palette.info.main,
    };

    const baseColor = colorMap[severity];

    return {
      backgroundColor: variant === 'filled' ? baseColor : alpha(baseColor, 0.1),
      color: variant === 'filled' ? '#ffffff' : baseColor,
      border: variant === 'outlined' ? `1px solid ${baseColor}` : 'none',
      borderRadius: theme.shape.borderRadius * 1.5,
      boxShadow: theme.shadows[8],
      backdropFilter: 'blur(10px)',
      '& .MuiAlert-icon': {
        color: variant === 'filled' ? '#ffffff' : baseColor,
      },
      '& .MuiAlert-action': {
        color: variant === 'filled' ? '#ffffff' : baseColor,
      },
    };
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={duration}
      onClose={onClose}
      anchorOrigin={position}
      sx={{
        '& .MuiSnackbarContent-root': {
          padding: 0,
        },
      }}
    >
      <Alert
        severity={severity}
        variant={variant}
        icon={getIcon()}
        sx={getCustomStyles()}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {action}
            <IconButton
              size="small"
              aria-label="إغلاق"
              color="inherit"
              onClick={onClose}
              sx={{
                '&:hover': {
                  backgroundColor: alpha('#ffffff', 0.1),
                },
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        }
      >
        {title && (
          <AlertTitle sx={{ fontWeight: 600, mb: 0.5 }}>
            {title}
          </AlertTitle>
        )}
        {message}
      </Alert>
    </Snackbar>
  );
};

// Hook for easy usage
export const useToast = () => {
  const [toasts, setToasts] = React.useState<Array<{
    id: string;
    message: string;
    title?: string;
    severity?: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
  }>>([]);

  const showToast = React.useCallback((
    message: string,
    options?: {
      title?: string;
      severity?: 'success' | 'error' | 'warning' | 'info';
      duration?: number;
    }
  ) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = {
      id,
      message,
      ...options,
    };

    setToasts(prev => [...prev, newToast]);

    // Auto remove after duration
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, options?.duration || 6000);
  }, []);

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const showSuccess = React.useCallback((message: string, title?: string) => {
    showToast(message, { severity: 'success', title });
  }, [showToast]);

  const showError = React.useCallback((message: string, title?: string) => {
    showToast(message, { severity: 'error', title });
  }, [showToast]);

  const showWarning = React.useCallback((message: string, title?: string) => {
    showToast(message, { severity: 'warning', title });
  }, [showToast]);

  const showInfo = React.useCallback((message: string, title?: string) => {
    showToast(message, { severity: 'info', title });
  }, [showToast]);

  const ToastContainer = React.useCallback(() => (
    <>
      {toasts.map((toast) => (
        <ToastNotification
          key={toast.id}
          open={true}
          onClose={() => removeToast(toast.id)}
          message={toast.message}
          title={toast.title}
          severity={toast.severity}
          duration={toast.duration}
        />
      ))}
    </>
  ), [toasts, removeToast]);

  return {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    ToastContainer,
  };
};

export default ToastNotification;
