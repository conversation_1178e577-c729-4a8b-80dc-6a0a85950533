﻿events {
    worker_connections 1024;
}

http {
    upstream eureka {
        server eureka-server:8761;
    }
    
    upstream auth {
        server auth-service:8081;
    }
    
    upstream user {
        server user-service:8083;
    }
    
    server {
        listen 80;
        
        location / {
            return 200 'TecnoDrive API Gateway - All services are running!';
            add_header Content-Type text/plain;
        }
        
        location /eureka/ {
            proxy_pass http://eureka/;
        }
        
        location /auth/ {
            proxy_pass http://auth/;
        }
        
        location /user/ {
            proxy_pass http://user/;
        }
        
        location /health {
            return 200 '{"status":"UP","gateway":"nginx","timestamp":"07/16/2025 09:07:34"}';
            add_header Content-Type application/json;
        }
    }
}
