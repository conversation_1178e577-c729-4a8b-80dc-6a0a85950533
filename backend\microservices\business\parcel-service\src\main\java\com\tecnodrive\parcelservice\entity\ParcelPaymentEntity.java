package com.tecnodrive.parcelservice.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity لمدفوعات الطرود
 */
@Entity
@Table(name = "parcel_payments", indexes = {
    @Index(name = "idx_payment_parcel_id", columnList = "parcel_id"),
    @Index(name = "idx_payment_status", columnList = "status"),
    @Index(name = "idx_payment_method", columnList = "method"),
    @Index(name = "idx_payment_transaction_id", columnList = "transaction_id"),
    @Index(name = "idx_payment_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParcelPaymentEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "payment_id")
    private String paymentId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parcel_id", nullable = false)
    private ParcelEntity parcel;
    
    @Column(name = "amount", nullable = false, precision = 12, scale = 2)
    private BigDecimal amount;
    
    @Column(name = "currency", length = 3)
    @Builder.Default
    private String currency = "YER";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "method", nullable = false, length = 20)
    private PaymentMethod method;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private PaymentStatus status;
    
    @Column(name = "transaction_id", unique = true, length = 100)
    private String transactionId;
    
    @Column(name = "gateway_reference", length = 100)
    private String gatewayReference;
    
    @Column(name = "payment_date")
    private LocalDateTime paymentDate;
    
    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "payer_name", length = 100)
    private String payerName;
    
    @Column(name = "payer_phone", length = 20)
    private String payerPhone;
    
    @Column(name = "payer_email", length = 100)
    private String payerEmail;
    
    @Column(name = "payment_proof_url", length = 500)
    private String paymentProofUrl;
    
    @Column(name = "refund_amount", precision = 12, scale = 2)
    @Builder.Default
    private BigDecimal refundAmount = BigDecimal.ZERO;

    @Column(name = "refund_date")
    private LocalDateTime refundDate;

    @Column(name = "refund_reason", length = 255)
    private String refundReason;

    @Column(name = "refund_reference", length = 100)
    private String refundReference;

    @Column(name = "fees", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal fees = BigDecimal.ZERO;

    @Column(name = "tax_amount", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @Column(name = "discount_amount", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    @Column(name = "net_amount", precision = 12, scale = 2)
    private BigDecimal netAmount;
    
    @Column(name = "payment_gateway", length = 50)
    private String paymentGateway;
    
    @Column(name = "gateway_response", columnDefinition = "TEXT")
    private String gatewayResponse;
    
    @Column(name = "failure_reason", length = 255)
    private String failureReason;
    
    @Column(name = "retry_count")
    @Builder.Default
    private Integer retryCount = 0;

    @Column(name = "max_retries")
    @Builder.Default
    private Integer maxRetries = 3;

    @Column(name = "is_partial_payment")
    @Builder.Default
    private Boolean isPartialPayment = false;
    
    @Column(name = "installment_number")
    private Integer installmentNumber;
    
    @Column(name = "total_installments")
    private Integer totalInstallments;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * طرق الدفع
     */
    public enum PaymentMethod {
        CASH("نقدي"),
        CARD("بطاقة ائتمان"),
        WALLET("محفظة إلكترونية"),
        BANK_TRANSFER("تحويل بنكي"),
        MOBILE_PAYMENT("دفع محمول"),
        COD("الدفع عند الاستلام");
        
        private final String arabicName;
        
        PaymentMethod(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    /**
     * حالات الدفع
     */
    public enum PaymentStatus {
        PENDING("في الانتظار"),
        PROCESSING("قيد المعالجة"),
        COMPLETED("مكتمل"),
        FAILED("فشل"),
        REFUNDED("مسترد"),
        CANCELLED("ملغي"),
        EXPIRED("منتهي الصلاحية"),
        PARTIAL("جزئي");
        
        private final String arabicName;
        
        PaymentStatus(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    /**
     * إنشاء دفعة جديدة
     */
    public static ParcelPaymentEntity createPayment(
            ParcelEntity parcel,
            BigDecimal amount,
            PaymentMethod method,
            String createdBy) {
        
        ParcelPaymentEntity payment = ParcelPaymentEntity.builder()
                .parcel(parcel)
                .amount(amount)
                .method(method)
                .status(PaymentStatus.PENDING)
                .currency("YER")
                .createdBy(createdBy)
                .build();
        
        payment.calculateNetAmount();
        return payment;
    }
    
    /**
     * حساب المبلغ الصافي
     */
    public void calculateNetAmount() {
        BigDecimal total = amount != null ? amount : BigDecimal.ZERO;
        BigDecimal totalFees = fees != null ? fees : BigDecimal.ZERO;
        BigDecimal totalTax = taxAmount != null ? taxAmount : BigDecimal.ZERO;
        BigDecimal totalDiscount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
        
        this.netAmount = total.add(totalFees).add(totalTax).subtract(totalDiscount);
    }
    
    /**
     * تأكيد الدفعة
     */
    public void confirmPayment(String transactionId, String gatewayReference) {
        this.status = PaymentStatus.COMPLETED;
        this.transactionId = transactionId;
        this.gatewayReference = gatewayReference;
        this.paymentDate = LocalDateTime.now();
    }
    
    /**
     * فشل الدفعة
     */
    public void failPayment(String failureReason) {
        this.status = PaymentStatus.FAILED;
        this.failureReason = failureReason;
        this.retryCount = this.retryCount != null ? this.retryCount + 1 : 1;
    }
    
    /**
     * استرداد الدفعة
     */
    public void refundPayment(BigDecimal refundAmount, String refundReason, String refundReference) {
        this.status = PaymentStatus.REFUNDED;
        this.refundAmount = refundAmount;
        this.refundReason = refundReason;
        this.refundReference = refundReference;
        this.refundDate = LocalDateTime.now();
    }
    
    /**
     * إلغاء الدفعة
     */
    public void cancelPayment(String reason) {
        this.status = PaymentStatus.CANCELLED;
        this.notes = reason;
    }
    
    /**
     * التحقق من إمكانية الاسترداد
     */
    public boolean isRefundable() {
        return status == PaymentStatus.COMPLETED && 
               (refundAmount == null || refundAmount.compareTo(amount) < 0);
    }
    
    /**
     * التحقق من إمكانية إعادة المحاولة
     */
    public boolean canRetry() {
        return status == PaymentStatus.FAILED && 
               retryCount != null && 
               maxRetries != null && 
               retryCount < maxRetries;
    }
    
    /**
     * التحقق من انتهاء صلاحية الدفعة
     */
    public boolean isExpired() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate);
    }
    
    /**
     * الحصول على المبلغ المتبقي للاسترداد
     */
    public BigDecimal getRemainingRefundableAmount() {
        if (!isRefundable()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal currentRefund = refundAmount != null ? refundAmount : BigDecimal.ZERO;
        return amount.subtract(currentRefund);
    }
    
    /**
     * إضافة رسوم
     */
    public void addFees(BigDecimal additionalFees) {
        this.fees = this.fees != null ? this.fees.add(additionalFees) : additionalFees;
        calculateNetAmount();
    }
    
    /**
     * إضافة ضريبة
     */
    public void addTax(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
        calculateNetAmount();
    }
    
    /**
     * إضافة خصم
     */
    public void addDiscount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
        calculateNetAmount();
    }
    
    /**
     * تحديث معلومات الدافع
     */
    public void updatePayerInfo(String payerName, String payerPhone, String payerEmail) {
        this.payerName = payerName;
        this.payerPhone = payerPhone;
        this.payerEmail = payerEmail;
    }
    
    /**
     * إضافة إثبات الدفع
     */
    public void addPaymentProof(String proofUrl) {
        this.paymentProofUrl = proofUrl;
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return parcel != null && 
               amount != null && amount.compareTo(BigDecimal.ZERO) > 0 &&
               method != null && 
               status != null &&
               currency != null && !currency.trim().isEmpty();
    }
}
