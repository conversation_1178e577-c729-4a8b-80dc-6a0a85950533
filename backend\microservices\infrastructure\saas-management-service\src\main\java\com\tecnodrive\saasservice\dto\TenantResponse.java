package com.tecnodrive.saasservice.dto;

import com.tecnodrive.saasservice.entity.Tenant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;

/**
 * Tenant Response DTO
 * 
 * Used for returning tenant information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantResponse {

    /**
     * Tenant ID
     */
    private String id;

    /**
     * Unique tenant name/identifier
     */
    private String name;

    /**
     * Display name for the tenant
     */
    private String displayName;

    /**
     * Tenant type
     */
    private Tenant.TenantType type;

    /**
     * Tenant status
     */
    private Tenant.TenantStatus status;

    /**
     * Contact person user ID
     */
    private String contactPersonId;

    /**
     * Primary contact email
     */
    private String email;

    /**
     * Primary contact phone
     */
    private String phone;

    /**
     * Physical address
     */
    private String address;

    /**
     * Website URL
     */
    private String website;

    /**
     * Service types enabled for this tenant
     */
    private Tenant.ServiceType serviceType;

    /**
     * Current pricing plan ID
     */
    private String pricingPlanId;

    /**
     * Maximum number of users allowed
     */
    private Integer maxUsers;

    /**
     * Maximum number of vehicles allowed
     */
    private Integer maxVehicles;

    /**
     * Custom branding configuration (JSON)
     */
    private String brandingConfig;

    /**
     * Feature flags configuration (JSON)
     */
    private String featureFlags;

    /**
     * Additional metadata (JSON)
     */
    private String metadata;

    /**
     * Subscription start date
     */
    private Instant subscriptionStartDate;

    /**
     * Subscription end date
     */
    private Instant subscriptionEndDate;

    /**
     * Creation timestamp
     */
    private Instant createdAt;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;

    /**
     * Computed fields
     */
    private boolean active;
    private boolean subscriptionValid;
    private long daysUntilExpiry;
}
