package com.tecnodrive.authservice.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Test Authentication Controller for debugging login issues
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestAuthController {

    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    @Autowired(required = false)
    private PasswordEncoder passwordEncoder;

    /**
     * Test endpoint to check if service is running
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "auth-service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * Test database connection and check users
     */
    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> getUsers() {
        Map<String, Object> response = new HashMap<>();

        try {
            if (jdbcTemplate != null) {
                List<Map<String, Object>> users = jdbcTemplate.queryForList(
                        "SELECT username, email, role, is_active FROM users LIMIT 10");
                response.put("status", "success");
                response.put("users", users);
                response.put("count", users.size());
            } else {
                response.put("status", "error");
                response.put("message", "Database connection not available");
            }
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * Test login with simple validation
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> testLogin(@RequestBody Map<String, String> credentials) {
        Map<String, Object> response = new HashMap<>();

        String username = credentials.get("username");
        String password = credentials.get("password");

        if (username == null || password == null) {
            response.put("status", "error");
            response.put("message", "Username and password are required");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            if (jdbcTemplate != null) {
                // Check if user exists
                List<Map<String, Object>> users = jdbcTemplate.queryForList(
                        "SELECT id, username, email, password, role, is_active FROM users WHERE username = ?",
                        username);

                if (users.isEmpty()) {
                    response.put("status", "error");
                    response.put("message", "User not found");
                    return ResponseEntity.ok(response);
                }

                Map<String, Object> user = users.get(0);
                String storedPassword = (String) user.get("password");
                Boolean isActive = (Boolean) user.get("is_active");

                if (!isActive) {
                    response.put("status", "error");
                    response.put("message", "User account is disabled");
                    return ResponseEntity.ok(response);
                }

                // Check password
                boolean passwordMatches = false;
                if (passwordEncoder != null) {
                    passwordMatches = passwordEncoder.matches(password, storedPassword);
                } else {
                    // Fallback: direct comparison (not secure, for testing only)
                    passwordMatches = password.equals("admin123") &&
                            storedPassword.startsWith("$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe");
                }

                if (passwordMatches) {
                    response.put("status", "success");
                    response.put("message", "Login successful");
                    response.put("user", Map.of(
                            "id", user.get("id"),
                            "username", user.get("username"),
                            "email", user.get("email"),
                            "role", user.get("role")));
                } else {
                    response.put("status", "error");
                    response.put("message", "Invalid password");
                    response.put("debug", Map.of(
                            "providedPassword", password,
                            "storedPasswordHash", storedPassword.substring(0, 20) + "...",
                            "passwordEncoderAvailable", passwordEncoder != null));
                }

            } else {
                response.put("status", "error");
                response.put("message", "Database connection not available");
            }
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
            response.put("exception", e.getClass().getSimpleName());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * Create a test user with correct password hash
     */
    @PostMapping("/create-user")
    public ResponseEntity<Map<String, Object>> createTestUser(@RequestBody Map<String, String> userInfo) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (jdbcTemplate != null && passwordEncoder != null) {
                String username = userInfo.getOrDefault("username", "testuser");
                String password = userInfo.getOrDefault("password", "admin123");
                String email = userInfo.getOrDefault("email", "<EMAIL>");
                String role = userInfo.getOrDefault("role", "USER");

                String hashedPassword = passwordEncoder.encode(password);

                jdbcTemplate.update(
                        "INSERT INTO users (username, email, password, first_name, last_name, role, is_active) " +
                                "VALUES (?, ?, ?, ?, ?, ?, ?) ON CONFLICT (username) DO UPDATE SET password = EXCLUDED.password",
                        username, email, hashedPassword, "Test", "User", role, true);

                response.put("status", "success");
                response.put("message", "User created/updated successfully");
                response.put("username", username);
                response.put("password", password);
                response.put("hashedPassword", hashedPassword);

            } else {
                response.put("status", "error");
                response.put("message", "Required services not available");
            }
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * Reset password for existing user
     */
    @PostMapping("/reset-password")
    public ResponseEntity<Map<String, Object>> resetPassword(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (jdbcTemplate != null && passwordEncoder != null) {
                String username = request.get("username");
                String newPassword = request.getOrDefault("password", "admin123");

                String hashedPassword = passwordEncoder.encode(newPassword);

                int result = jdbcTemplate.update(
                        "UPDATE users SET password = ? WHERE username = ?",
                        hashedPassword, username);

                if (result > 0) {
                    response.put("status", "success");
                    response.put("message", "Password updated successfully");
                    response.put("username", username);
                    response.put("newPassword", newPassword);
                } else {
                    response.put("status", "error");
                    response.put("message", "User not found");
                }

            } else {
                response.put("status", "error");
                response.put("message", "Required services not available");
            }
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }
}
