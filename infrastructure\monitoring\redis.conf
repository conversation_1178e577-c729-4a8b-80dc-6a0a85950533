# Redis Configuration for TECNO DRIVE Platform
# Optimized for microservices caching and session management

# Network
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Snapshotting
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security
requirepass ""
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_a1b2c3d4e5f6"
rename-command SHUTDOWN "SHUTDOWN_a1b2c3d4e5f6"

# Memory Management
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Append Only File (AOF)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Client Management
maxclients 10000

# Slow Log Configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Performance Tuning
hz 10

# Keyspace Notifications (for JWT revocation)
notify-keyspace-events Ex

# Latency Monitoring
latency-monitor-threshold 100

# Lazy Freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Threaded I/O
io-threads 4
io-threads-do-reads yes

# Kernel OOM Score
oom-score-adj no

# Disable some commands for security
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_TECNO_DRIVE

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitor
latency-monitor-threshold 100

# Event notification
notify-keyspace-events "Ex"

# Hash
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List
list-max-ziplist-size -2
list-compress-depth 0

# Set
set-max-intset-entries 512

# Sorted Set
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog
hll-sparse-max-bytes 3000

# Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# Frequency
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua
lua-time-limit 5000

# Cluster (disabled for single instance)
# cluster-enabled no

# Slow log for debugging
slowlog-log-slower-than 10000
slowlog-max-len 128

# Memory usage optimization for microservices
tcp-keepalive 300
timeout 300
