package com.tecnodrive.rideservice.service;

import com.tecnodrive.rideservice.dto.FareEstimateDto;
import com.tecnodrive.rideservice.dto.RideRequestDto;
import com.tecnodrive.rideservice.dto.RideResponseDto;
import com.tecnodrive.rideservice.entity.RideStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * Ride Service Interface
 */
public interface RideService {
    
    /**
     * Request a new ride
     */
    RideResponseDto requestRide(RideRequestDto request);
    
    /**
     * Get fare estimate
     */
    FareEstimateDto getFareEstimate(RideRequestDto.LocationDto pickup, 
                                   RideRequestDto.LocationDto destination);
    
    /**
     * Assign driver to ride
     */
    RideResponseDto assignDriver(UUID rideId, UUID driverId);
    
    /**
     * Update ride status
     */
    RideResponseDto updateRideStatus(UUID rideId, RideStatus newStatus, String reason);
    
    /**
     * Complete ride
     */
    RideResponseDto completeRide(UUID rideId, BigDecimal finalFare, 
                                BigDecimal actualDistance, Integer actualDuration);
    
    /**
     * Cancel ride
     */
    RideResponseDto cancelRide(UUID rideId, RideStatus cancelStatus, String reason);
    
    /**
     * Rate ride
     */
    RideResponseDto rateRide(UUID rideId, UUID raterId, Integer rating);
    
    /**
     * Get ride by ID
     */
    RideResponseDto getRideById(UUID rideId);
    
    /**
     * Get rides by passenger
     */
    List<RideResponseDto> getRidesByPassenger(UUID passengerId);
    
    /**
     * Get rides by driver
     */
    List<RideResponseDto> getRidesByDriver(UUID driverId);
    
    /**
     * Get active ride for passenger
     */
    RideResponseDto getActiveRideByPassenger(UUID passengerId);
    
    /**
     * Get active ride for driver
     */
    RideResponseDto getActiveRideByDriver(UUID driverId);
    
    /**
     * Find available rides for driver
     */
    List<RideResponseDto> findAvailableRides(UUID driverId, double latitude, double longitude, double radiusKm);
    
    /**
     * Get ride metrics
     */
    RideMetricsDto getRideMetrics();
    
    public static record RideMetricsDto(
        long totalRides,
        long activeRides,
        long completedRides,
        long cancelledRides,
        double averageRating,
        BigDecimal totalRevenue
    ) {}
}
