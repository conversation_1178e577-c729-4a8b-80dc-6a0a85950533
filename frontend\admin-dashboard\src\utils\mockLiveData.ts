// Mock data for Live Operations Dashboard
import { VehicleLocation, ParcelLocation } from '../components/LiveTracking/LiveMap';
import { AlertItem } from '../components/LiveTracking/AlertsPanel';

// Riyadh coordinates and surrounding areas
const RIYADH_CENTER = { lat: 24.7136, lng: 46.6753 };
const RIYADH_BOUNDS = {
  north: 24.8500,
  south: 24.5500,
  east: 46.9000,
  west: 46.4000
};

// Generate random coordinates within Riyadh
const generateRandomCoordinate = () => ({
  lat: RIYADH_BOUNDS.south + Math.random() * (RIYADH_BOUNDS.north - RIYADH_BOUNDS.south),
  lng: RIYADH_BOUNDS.west + Math.random() * (RIYADH_BOUNDS.east - RIYADH_BOUNDS.west)
});

// Saudi driver names
const DRIVER_NAMES = [
  'أحمد الراشد',
  'محمد الفهد',
  'علي الحسن',
  'عمر الخليل',
  'يوسف أحمد',
  'خالد منصور',
  'سعد العتيبي',
  'فهد القحطاني',
  'عبدالله السعود',
  'ناصر الدوسري',
  'سلطان العنزي',
  'بندر الشمري',
  'طلال الحربي',
  'ماجد المطيري',
  'راشد الزهراني'
];

// Customer names
const CUSTOMER_NAMES = [
  'سارة الزهراء',
  'فاطمة النوري',
  'نورا العلي',
  'ريم الأحمد',
  'هند المحمد',
  'لينا الخالد',
  'دانا السعيد',
  'رنا الحسين',
  'مها العبدالله',
  'شهد الفيصل',
  'غلا الراشد',
  'جود العثمان',
  'لمى الصالح',
  'رهف القاسم',
  'أمل الشهري'
];

// Riyadh locations
const RIYADH_LOCATIONS = [
  'الرياض مول',
  'مطار الملك فهد الدولي',
  'حي النخيل',
  'شارع العليا',
  'برج المملكة',
  'مركز الفيصلية',
  'الحي الدبلوماسي',
  'جامعة الملك سعود',
  'مدينة الملك عبدالعزيز الطبية',
  'حي الملز',
  'حي السليمانية',
  'حي الورود',
  'حي الياسمين',
  'حي الربوة',
  'حي الشفا',
  'مركز الملك عبدالله المالي',
  'واجهة الرياض',
  'بوليفارد الرياض سيتي',
  'حديقة الملك عبدالله',
  'سوق الزل'
];

// Generate mock vehicles
export const generateMockVehicles = (count: number = 25): VehicleLocation[] => {
  return Array.from({ length: count }, (_, index) => {
    const coord = generateRandomCoordinate();
    const statuses: VehicleLocation['status'][] = ['available', 'busy', 'offline'];
    const types: VehicleLocation['type'][] = ['passenger', 'delivery'];
    
    return {
      id: `V${String(index + 1).padStart(3, '0')}`,
      type: types[Math.floor(Math.random() * types.length)],
      lat: coord.lat,
      lng: coord.lng,
      heading: Math.floor(Math.random() * 360),
      speed: Math.floor(Math.random() * 80),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      driverName: DRIVER_NAMES[Math.floor(Math.random() * DRIVER_NAMES.length)],
      lastUpdate: new Date(Date.now() - Math.random() * 300000).toISOString() // Last 5 minutes
    };
  });
};

// Generate mock parcels
export const generateMockParcels = (count: number = 15): ParcelLocation[] => {
  return Array.from({ length: count }, (_, index) => {
    const coord = generateRandomCoordinate();
    const statuses: ParcelLocation['status'][] = ['pickup_pending', 'in_transit', 'delivered'];
    
    return {
      id: `P${String(index + 1).padStart(4, '0')}`,
      lat: coord.lat,
      lng: coord.lng,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      estimatedDelivery: new Date(Date.now() + Math.random() * 7200000).toISOString(), // Next 2 hours
      recipientName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)]
    };
  });
};

// Generate mock alerts
export const generateMockAlerts = (count: number = 8): AlertItem[] => {
  const alertTypes: AlertItem['type'][] = [
    'delay', 'off_route', 'vehicle_breakdown', 'low_battery', 'speeding', 'no_signal', 'delivery_failed'
  ];
  
  const severities: AlertItem['severity'][] = ['critical', 'warning', 'info'];
  const entityTypes: AlertItem['entityType'][] = ['vehicle', 'parcel', 'driver'];

  const alertTemplates = {
    delay: {
      title: 'تأخير في التوصيل',
      description: 'المركبة {entityId} متأخرة عن الموعد المحدد بسبب الازدحام المروري'
    },
    off_route: {
      title: 'خروج عن المسار',
      description: 'المركبة {entityId} خرجت عن المسار المحدد'
    },
    vehicle_breakdown: {
      title: 'عطل في المركبة',
      description: 'المركبة {entityId} تواجه مشكلة تقنية وتحتاج للصيانة'
    },
    low_battery: {
      title: 'بطارية منخفضة',
      description: 'جهاز التتبع في المركبة {entityId} يحتاج للشحن'
    },
    speeding: {
      title: 'تجاوز السرعة المحددة',
      description: 'المركبة {entityId} تتجاوز الحد الأقصى للسرعة'
    },
    no_signal: {
      title: 'فقدان الإشارة',
      description: 'فقدان الاتصال مع المركبة {entityId}'
    },
    delivery_failed: {
      title: 'فشل في التوصيل',
      description: 'فشل في توصيل الطرد {entityId} للعنوان المحدد'
    }
  };

  return Array.from({ length: count }, (_, index) => {
    const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const entityType = entityTypes[Math.floor(Math.random() * entityTypes.length)];
    const entityId = entityType === 'vehicle' ? `V${String(Math.floor(Math.random() * 25) + 1).padStart(3, '0')}` :
                     entityType === 'parcel' ? `P${String(Math.floor(Math.random() * 15) + 1).padStart(4, '0')}` :
                     `D${String(Math.floor(Math.random() * 15) + 1).padStart(3, '0')}`;
    
    const template = alertTemplates[type];
    const coord = generateRandomCoordinate();

    return {
      id: `A${String(index + 1).padStart(3, '0')}`,
      type,
      severity,
      title: template.title,
      description: template.description.replace('{entityId}', entityId),
      entityId,
      entityType,
      location: { lat: coord.lat, lng: coord.lng },
      timestamp: new Date(Date.now() - Math.random() * 1800000).toISOString(), // Last 30 minutes
      acknowledged: Math.random() > 0.7,
      resolved: Math.random() > 0.8
    };
  });
};

// Generate mock ride data
export const generateMockRides = (count: number = 50) => {
  const statuses = ['pending', 'assigned', 'in_progress', 'completed', 'cancelled'];
  const vehicleTypes = ['sedan', 'suv', 'van', 'motorcycle'];
  const paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
  
  return Array.from({ length: count }, (_, index) => ({
    id: `R${String(index + 1).padStart(4, '0')}`,
    customerId: `C${String(Math.floor(Math.random() * 100) + 1).padStart(3, '0')}`,
    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],
    driverId: `D${String(Math.floor(Math.random() * 15) + 1).padStart(3, '0')}`,
    driverName: DRIVER_NAMES[Math.floor(Math.random() * DRIVER_NAMES.length)],
    vehicleId: `V${String(Math.floor(Math.random() * 25) + 1).padStart(3, '0')}`,
    vehicleType: vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    pickupLocation: RIYADH_LOCATIONS[Math.floor(Math.random() * RIYADH_LOCATIONS.length)],
    dropoffLocation: RIYADH_LOCATIONS[Math.floor(Math.random() * RIYADH_LOCATIONS.length)],
    pickupTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    dropoffTime: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString() : undefined,
    estimatedDuration: Math.floor(Math.random() * 60) + 15,
    actualDuration: Math.random() > 0.3 ? Math.floor(Math.random() * 60) + 15 : undefined,
    distance: Math.round((Math.random() * 50 + 5) * 10) / 10,
    fare: Math.round((Math.random() * 100 + 20) * 100) / 100,
    paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
    rating: Math.random() > 0.4 ? Math.round((Math.random() * 2 + 3) * 10) / 10 : undefined,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
  }));
};

// Real-time data simulation
export const simulateRealTimeUpdates = () => {
  const vehicles = generateMockVehicles(25);
  const parcels = generateMockParcels(15);
  const alerts = generateMockAlerts(8);

  // Simulate movement for vehicles
  const updateVehiclePositions = () => {
    vehicles.forEach(vehicle => {
      // Small random movement
      vehicle.lat += (Math.random() - 0.5) * 0.001;
      vehicle.lng += (Math.random() - 0.5) * 0.001;
      vehicle.heading = (vehicle.heading + Math.random() * 10 - 5) % 360;
      vehicle.speed = Math.max(0, vehicle.speed + Math.random() * 10 - 5);
      vehicle.lastUpdate = new Date().toISOString();
    });
  };

  // Update positions every 5 seconds
  setInterval(updateVehiclePositions, 5000);

  return { vehicles, parcels, alerts };
};

// Export default data sets
export const DEFAULT_MOCK_DATA = {
  vehicles: generateMockVehicles(25),
  parcels: generateMockParcels(15),
  alerts: generateMockAlerts(8),
  rides: generateMockRides(50)
};
