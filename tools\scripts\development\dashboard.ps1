#!/usr/bin/env powershell
# =============================================================================
# TecnoDrive Platform - Live Dashboard
# =============================================================================

function Show-ServiceStatus {
    Clear-Host
    
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    TecnoDrive Platform                       ║" -ForegroundColor Cyan
    Write-Host "║                     Live Dashboard                           ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "🕒 Last Update: $timestamp" -ForegroundColor Gray
    Write-Host ""
    
    # Infrastructure Services
    Write-Host "🏗️  INFRASTRUCTURE SERVICES" -ForegroundColor Yellow
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Yellow
    
    # PostgreSQL
    Write-Host "  📊 PostgreSQL (5432)     " -NoNewline
    try {
        $pgResult = docker exec postgres-tecno pg_isready -U postgres 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ HEALTHY" -ForegroundColor Green
        } else {
            Write-Host "❌ FAILED" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ ERROR" -ForegroundColor Red
    }
    
    # Redis
    Write-Host "  🔄 Redis (6379)          " -NoNewline
    try {
        $redisResult = docker exec redis-tecno redis-cli ping 2>$null
        if ($redisResult -eq "PONG") {
            Write-Host "✅ HEALTHY" -ForegroundColor Green
        } else {
            Write-Host "❌ FAILED" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ ERROR" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Application Services
    Write-Host "🚀 APPLICATION SERVICES" -ForegroundColor Yellow
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Yellow
    
    $services = @(
        @{Name="Eureka Server"; Port=8761; Icon="🔍"},
        @{Name="API Gateway"; Port=8080; Icon="🌐"},
        @{Name="Auth Service"; Port=8081; Icon="🔐"}
    )
    
    foreach ($service in $services) {
        Write-Host "  $($service.Icon) $($service.Name) ($($service.Port))" -NoNewline
        $padding = 25 - $service.Name.Length
        Write-Host (" " * $padding) -NoNewline
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)/actuator/health" -UseBasicParsing -TimeoutSec 3 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ HEALTHY" -ForegroundColor Green
            } else {
                Write-Host "⚠️  STATUS: $($response.StatusCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ FAILED" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    
    # Container Status
    Write-Host "📦 CONTAINER STATUS" -ForegroundColor Yellow
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Yellow
    
    $containers = docker ps --format "{{.Names}}\t{{.Status}}" | Where-Object { $_ -match "tecno" }
    if ($containers) {
        foreach ($container in $containers) {
            $parts = $container -split "\t"
            $name = $parts[0]
            $status = $parts[1]
            
            Write-Host "  📦 $name" -NoNewline
            $padding = 25 - $name.Length
            Write-Host (" " * $padding) -NoNewline
            
            if ($status -match "Up.*healthy") {
                Write-Host "✅ $status" -ForegroundColor Green
            } elseif ($status -match "Up") {
                Write-Host "⚠️  $status" -ForegroundColor Yellow
            } else {
                Write-Host "❌ $status" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "  No containers running" -ForegroundColor Gray
    }
    
    Write-Host ""
    
    # Quick Links
    Write-Host "🔗 QUICK LINKS" -ForegroundColor Yellow
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Yellow
    Write-Host "  🌐 Eureka Dashboard: http://localhost:8761" -ForegroundColor Cyan
    Write-Host "  🔐 Auth Service:     http://localhost:8081" -ForegroundColor Cyan
    Write-Host "  🌐 API Gateway:      http://localhost:8080" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "Press Ctrl+C to exit, or wait for auto-refresh..." -ForegroundColor Gray
}

# Main loop
try {
    while ($true) {
        Show-ServiceStatus
        Start-Sleep -Seconds 10
    }
} catch {
    Write-Host "`nDashboard stopped." -ForegroundColor Yellow
}
