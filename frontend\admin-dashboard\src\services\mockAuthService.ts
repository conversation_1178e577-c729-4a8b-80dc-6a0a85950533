// Mock Auth Service for Development
export interface LoginRequest {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: User;
  };
}

// Mock users database
const mockUsers: Array<User & { password: string }> = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON><PERSON>',
    role: 'ADMIN',
    permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'System Admin',
    role: 'ADMIN',
    permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'manager123',
    name: 'Operations Manager',
    role: '<PERSON>NAGE<PERSON>',
    permissions: ['READ', 'WRITE']
  }
];

// Generate mock token (Unicode-safe)
function generateMockToken(user: User): string {
  const payload = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    permissions: user.permissions,
    iat: Date.now(),
    exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  };

  // Unicode-safe base64 encoding
  try {
    const jsonString = JSON.stringify(payload);
    // Use TextEncoder for Unicode support
    const encoder = new TextEncoder();
    const data = encoder.encode(jsonString);

    // Convert to base64 manually for Unicode support
    let binary = '';
    data.forEach(byte => binary += String.fromCharCode(byte));
    return btoa(binary);
  } catch (error) {
    // Fallback: simple token without Unicode
    const simplePayload = {
      id: user.id,
      email: user.email,
      role: user.role,
      iat: Date.now(),
      exp: Date.now() + (24 * 60 * 60 * 1000)
    };
    return btoa(JSON.stringify(simplePayload));
  }
}

// Mock login function
export async function mockLogin(request: LoginRequest): Promise<AuthResponse> {
  console.log('🔐 Mock login attempt:', request.email);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  if (!request.email || !request.password) {
    return {
      success: false,
      message: 'البريد الإلكتروني وكلمة المرور مطلوبان'
    };
  }
  
  const user = mockUsers.find(u => 
    u.email === request.email && u.password === request.password
  );
  
  if (!user) {
    return {
      success: false,
      message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
    };
  }
  
  const token = generateMockToken(user);
  
  // Store in localStorage
  localStorage.setItem('authToken', token);
  localStorage.setItem('user', JSON.stringify({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    permissions: user.permissions
  }));
  
  console.log('✅ Mock login successful:', user.email);
  
  return {
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    data: {
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: user.permissions
      }
    }
  };
}

// Mock logout function
export async function mockLogout(): Promise<AuthResponse> {
  console.log('🚪 Mock logout');
  
  // Clear localStorage
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
  
  return {
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  };
}

// Mock get current user function
export async function mockGetCurrentUser(): Promise<AuthResponse> {
  const token = localStorage.getItem('authToken');
  const userStr = localStorage.getItem('user');
  
  if (!token || !userStr) {
    return {
      success: false,
      message: 'غير مصرح'
    };
  }
  
  try {
    const payload = JSON.parse(atob(token));
    const currentTime = Date.now();
    
    if (payload.exp < currentTime) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      
      return {
        success: false,
        message: 'انتهت صلاحية الجلسة'
      };
    }
    
    const user = JSON.parse(userStr);
    
    return {
      success: true,
      message: 'تم العثور على المستخدم',
      data: {
        token,
        user
      }
    };
    
  } catch (error) {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    
    return {
      success: false,
      message: 'رمز غير صالح'
    };
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  const token = localStorage.getItem('authToken');
  const userStr = localStorage.getItem('user');
  
  if (!token || !userStr) {
    return false;
  }
  
  try {
    const payload = JSON.parse(atob(token));
    const currentTime = Date.now();
    
    return payload.exp > currentTime;
  } catch (error) {
    return false;
  }
}

// Get current user from localStorage
export function getCurrentUser(): User | null {
  const userStr = localStorage.getItem('user');
  
  if (!userStr) {
    return null;
  }
  
  try {
    return JSON.parse(userStr);
  } catch (error) {
    return null;
  }
}

// Get auth token
export function getAuthToken(): string | null {
  return localStorage.getItem('authToken');
}
