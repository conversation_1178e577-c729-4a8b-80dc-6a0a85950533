import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Warning,
  Security,
  TrendingUp,
  Assignment,
  Refresh,
  FilterList,
  Download,
  Notifications,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import riskManagementService, { RiskEvent, RiskMetrics, SecurityAlert, MitigationAction } from '../../services/riskManagementService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const RiskDashboard: React.FC = () => {
  const [riskEvents, setRiskEvents] = useState<RiskEvent[]>([]);
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics | null>(null);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvent, setSelectedEvent] = useState<RiskEvent | null>(null);
  const [newMitigationAction, setNewMitigationAction] = useState<Partial<MitigationAction>>({});
  const [filters, setFilters] = useState({
    riskType: '',
    severity: '',
    status: '',
  });

  const tenantId = localStorage.getItem('tenantId') || 'default';

  // Load initial data
  useEffect(() => {
    loadDashboardData();
    setupRealTimeSubscriptions();

    return () => {
      riskManagementService.disconnect();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [events, metrics, alerts] = await Promise.all([
        riskManagementService.getRiskEvents(tenantId, filters),
        riskManagementService.getRiskMetrics(tenantId),
        riskManagementService.getSecurityAlerts(tenantId),
      ]);

      setRiskEvents(events);
      setRiskMetrics(metrics);
      setSecurityAlerts(alerts);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealTimeSubscriptions = () => {
    // Subscribe to new risk events
    riskManagementService.subscribeToRiskEvents(tenantId, (event: RiskEvent) => {
      setRiskEvents(prev => [event, ...prev]);
      // Show notification for critical risks
      if (event.severity === 'critical') {
        // You can integrate with a notification system here
        console.log('Critical risk detected:', event);
      }
    });

    // Subscribe to risk updates
    riskManagementService.subscribeToRiskUpdates((updatedEvent: RiskEvent) => {
      setRiskEvents(prev => 
        prev.map(event => event.id === updatedEvent.id ? updatedEvent : event)
      );
    });

    // Subscribe to security alerts
    riskManagementService.subscribeToSecurityAlerts(tenantId, (alert: SecurityAlert) => {
      setSecurityAlerts(prev => [alert, ...prev.slice(0, 9)]); // Keep only 10 latest
    });
  };

  const handleEventClick = (event: RiskEvent) => {
    setSelectedEvent(event);
  };

  const handleAddMitigationAction = async () => {
    if (!selectedEvent || !newMitigationAction.action) return;

    try {
      const action = await riskManagementService.addMitigationAction(selectedEvent.id, {
        action: newMitigationAction.action!,
        status: 'pending',
        assignedTo: newMitigationAction.assignedTo || '',
        dueDate: newMitigationAction.dueDate || new Date().toISOString(),
      });

      setSelectedEvent(prev => prev ? {
        ...prev,
        mitigationActions: [...prev.mitigationActions, action]
      } : null);

      setNewMitigationAction({});
    } catch (error) {
      console.error('Error adding mitigation action:', error);
    }
  };

  const handleUpdateEventStatus = async (eventId: string, status: RiskEvent['status']) => {
    try {
      const updatedEvent = await riskManagementService.updateRiskEvent(eventId, { status });
      setRiskEvents(prev => 
        prev.map(event => event.id === eventId ? updatedEvent : event)
      );
      if (selectedEvent?.id === eventId) {
        setSelectedEvent(updatedEvent);
      }
    } catch (error) {
      console.error('Error updating event status:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <Error color="error" />;
      case 'high': return <Warning color="warning" />;
      case 'medium': return <Info color="info" />;
      case 'low': return <CheckCircle color="success" />;
      default: return <Info />;
    }
  };

  const exportReport = async () => {
    try {
      const blob = await riskManagementService.exportRiskReport(tenantId, 'excel', filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `risk-report-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <Typography>Loading Risk Dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Risk Management Dashboard
        </Typography>
        <Box>
          <IconButton onClick={loadDashboardData} color="primary">
            <Refresh />
          </IconButton>
          <Button
            startIcon={<Download />}
            onClick={exportReport}
            variant="outlined"
            sx={{ ml: 1 }}
          >
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Metrics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Security color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Risks
                  </Typography>
                  <Typography variant="h5">
                    {riskMetrics?.totalRisks || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Error color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Critical Risks
                  </Typography>
                  <Typography variant="h5" color="error">
                    {riskMetrics?.criticalRisks || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Warning color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Open Risks
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {riskMetrics?.openRisks || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Resolution Time
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {riskMetrics?.averageResolutionTime || 0}h
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Risk Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={riskMetrics?.riskTrend || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Risks by Severity
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={Object.entries(riskMetrics?.risksBySeverity || {}).map(([key, value]) => ({
                      name: key,
                      value,
                    }))}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {Object.entries(riskMetrics?.risksBySeverity || {}).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Security Alerts */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Security Alerts
          </Typography>
          <Box>
            {securityAlerts.slice(0, 5).map((alert) => (
              <Alert
                key={alert.id}
                severity={alert.severity as any}
                sx={{ mb: 1 }}
                action={
                  <Chip
                    label={alert.source}
                    size="small"
                    variant="outlined"
                  />
                }
              >
                <strong>{alert.alertType}</strong>: {alert.description}
              </Alert>
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Risk Events Table */}
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Risk Events
            </Typography>
            <Button startIcon={<FilterList />} variant="outlined" size="small">
              Filters
            </Button>
          </Box>

          <Grid container spacing={2}>
            {riskEvents.map((event) => (
              <Grid item xs={12} md={6} lg={4} key={event.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { boxShadow: 3 },
                    border: event.severity === 'critical' ? '2px solid #f44336' : 'none',
                  }}
                  onClick={() => handleEventClick(event)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="h6" noWrap>
                        {event.title}
                      </Typography>
                      {getSeverityIcon(event.severity)}
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" mb={1}>
                      {event.description}
                    </Typography>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={event.riskType}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={event.severity}
                        size="small"
                        color={getSeverityColor(event.severity) as any}
                      />
                      <Chip
                        label={event.status}
                        size="small"
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="caption" color="textSecondary">
                      Risk Score: {event.riskScore}/100
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Risk Event Detail Dialog */}
      <Dialog
        open={!!selectedEvent}
        onClose={() => setSelectedEvent(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Risk Event Details
        </DialogTitle>
        <DialogContent>
          {selectedEvent && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedEvent.title}
              </Typography>
              
              <Typography variant="body1" paragraph>
                {selectedEvent.description}
              </Typography>

              <Box display="flex" gap={1} mb={2}>
                <Chip label={selectedEvent.riskType} color="primary" />
                <Chip label={selectedEvent.severity} color={getSeverityColor(selectedEvent.severity) as any} />
                <Chip label={selectedEvent.status} variant="outlined" />
              </Box>

              <Typography variant="h6" gutterBottom>
                Mitigation Actions
              </Typography>

              {selectedEvent.mitigationActions.map((action) => (
                <Card key={action.id} sx={{ mb: 1 }}>
                  <CardContent>
                    <Typography variant="body1">{action.action}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      Assigned to: {action.assignedTo} | Due: {new Date(action.dueDate).toLocaleDateString()}
                    </Typography>
                  </CardContent>
                </Card>
              ))}

              <Box mt={2}>
                <Typography variant="h6" gutterBottom>
                  Add Mitigation Action
                </Typography>
                <TextField
                  fullWidth
                  label="Action Description"
                  value={newMitigationAction.action || ''}
                  onChange={(e) => setNewMitigationAction(prev => ({ ...prev, action: e.target.value }))}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="Assigned To"
                  value={newMitigationAction.assignedTo || ''}
                  onChange={(e) => setNewMitigationAction(prev => ({ ...prev, assignedTo: e.target.value }))}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  type="date"
                  label="Due Date"
                  value={newMitigationAction.dueDate?.split('T')[0] || ''}
                  onChange={(e) => setNewMitigationAction(prev => ({ ...prev, dueDate: e.target.value }))}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedEvent(null)}>
            Close
          </Button>
          <Button onClick={handleAddMitigationAction} variant="contained">
            Add Action
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RiskDashboard;
