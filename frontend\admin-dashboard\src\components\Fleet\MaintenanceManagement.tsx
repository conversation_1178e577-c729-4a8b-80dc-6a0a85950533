import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Calendar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Build as MaintenanceIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CalendarToday as CalendarIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Cancel as CancelIcon,
  DirectionsCar as CarIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { fleetService, MaintenanceTaskDto, CreateMaintenanceTaskRequest, VehicleDto } from '../../services/fleetService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`maintenance-tabpanel-${index}`}
      aria-labelledby={`maintenance-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MaintenanceManagement: React.FC = () => {
  const [vehicles, setVehicles] = useState<VehicleDto[]>([]);
  const [allMaintenanceTasks, setAllMaintenanceTasks] = useState<MaintenanceTaskDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterPriority, setFilterPriority] = useState('ALL');
  const [filterVehicle, setFilterVehicle] = useState('ALL');
  const [tabValue, setTabValue] = useState(0);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [newTask, setNewTask] = useState<CreateMaintenanceTaskRequest>({
    vehicleId: '',
    taskType: 'OIL_CHANGE',
    description: '',
    priority: 'MEDIUM',
    scheduledDate: new Date().toISOString().split('T')[0],
    assignedTo: '',
    notes: '',
  });

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load vehicles
      const vehiclesResponse = await fleetService.getVehicles();
      if (vehiclesResponse.success && vehiclesResponse.data) {
        setVehicles(vehiclesResponse.data);
      }

      // Load maintenance tasks for all vehicles
      const allTasks: MaintenanceTaskDto[] = [];
      for (const vehicle of vehiclesResponse.data || []) {
        const tasksResponse = await fleetService.getMaintenanceTasks(vehicle.id);
        if (tasksResponse.success && tasksResponse.data) {
          allTasks.push(...tasksResponse.data);
        }
      }
      setAllMaintenanceTasks(allTasks);

    } catch (error) {
      console.error('Error loading maintenance data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      SCHEDULED: { label: 'مجدولة', color: 'info' as const, icon: <ScheduleIcon fontSize="small" /> },
      IN_PROGRESS: { label: 'قيد التنفيذ', color: 'warning' as const, icon: <MaintenanceIcon fontSize="small" /> },
      COMPLETED: { label: 'مكتملة', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      CANCELLED: { label: 'ملغية', color: 'default' as const, icon: <CancelIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getPriorityChip = (priority: string) => {
    const priorityConfig = {
      LOW: { label: 'منخفضة', color: 'info' as const },
      MEDIUM: { label: 'متوسطة', color: 'warning' as const },
      HIGH: { label: 'عالية', color: 'error' as const },
      URGENT: { label: 'عاجلة', color: 'error' as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || { 
      label: priority, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const getTaskTypeLabel = (taskType: string) => {
    const taskTypes = {
      OIL_CHANGE: 'تغيير الزيت',
      TIRE_ROTATION: 'تدوير الإطارات',
      BRAKE_CHECK: 'فحص الفرامل',
      ENGINE_SERVICE: 'صيانة المحرك',
      GENERAL_INSPECTION: 'فحص عام',
      CUSTOM: 'مخصص',
    };
    return taskTypes[taskType as keyof typeof taskTypes] || taskType;
  };

  const handleEditTask = (taskId: string) => {
    console.log('Edit task:', taskId);
    // TODO: Implement edit functionality
  };

  const handleDeleteTask = async (taskId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
      try {
        // TODO: Implement delete API call
        console.log('Delete task:', taskId);
        loadData();
      } catch (error) {
        console.error('Error deleting task:', error);
      }
    }
  };

  const handleCompleteTask = async (taskId: string) => {
    try {
      await fleetService.updateMaintenanceTask(taskId, { 
        status: 'COMPLETED',
        completedDate: new Date().toISOString(),
      });
      loadData();
    } catch (error) {
      console.error('Error completing task:', error);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'vehicleInfo',
      headerName: 'المركبة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => {
        const vehicle = vehicles.find(v => v.id === params.row.vehicleId);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              <CarIcon fontSize="small" />
            </Avatar>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {vehicle?.plateNumber || 'غير محدد'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'taskType',
      headerName: 'نوع المهمة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => getTaskTypeLabel(params.value),
    },
    {
      field: 'description',
      headerName: 'الوصف',
      width: 200,
    },
    {
      field: 'priority',
      headerName: 'الأولوية',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getPriorityChip(params.value),
    },
    {
      field: 'scheduledDate',
      headerName: 'التاريخ المجدول',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'assignedTo',
      headerName: 'المسؤول',
      width: 120,
      renderCell: (params: GridRenderCellParams) => params.value || '-',
    },
    {
      field: 'cost',
      headerName: 'التكلفة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => 
        params.value ? `${params.value.toLocaleString()} ريال` : '-',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEditTask(params.id as string)}
        />,
        ...(params.row.status === 'SCHEDULED' || params.row.status === 'IN_PROGRESS' ? [
          <GridActionsCellItem
            icon={
              <Tooltip title="إكمال">
                <CheckCircleIcon />
              </Tooltip>
            }
            label="إكمال"
            onClick={() => handleCompleteTask(params.id as string)}
          />
        ] : []),
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteTask(params.id as string)}
        />,
      ],
    },
  ];

  const filteredTasks = allMaintenanceTasks.filter(task => {
    const vehicle = vehicles.find(v => v.id === task.vehicleId);
    const matchesSearch = task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle?.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.assignedTo?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'ALL' || task.status === filterStatus;
    const matchesPriority = filterPriority === 'ALL' || task.priority === filterPriority;
    const matchesVehicle = filterVehicle === 'ALL' || task.vehicleId === filterVehicle;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesVehicle;
  });

  const handleAddTask = async () => {
    try {
      await fleetService.createMaintenanceTask(newTask);
      setOpenAddDialog(false);
      setNewTask({
        vehicleId: '',
        taskType: 'OIL_CHANGE',
        description: '',
        priority: 'MEDIUM',
        scheduledDate: new Date().toISOString().split('T')[0],
        assignedTo: '',
        notes: '',
      });
      loadData();
    } catch (error) {
      console.error('Error creating maintenance task:', error);
    }
  };

  // Calculate stats
  const totalTasks = allMaintenanceTasks.length;
  const scheduledTasks = allMaintenanceTasks.filter(t => t.status === 'SCHEDULED').length;
  const inProgressTasks = allMaintenanceTasks.filter(t => t.status === 'IN_PROGRESS').length;
  const completedTasks = allMaintenanceTasks.filter(t => t.status === 'COMPLETED').length;
  const urgentTasks = allMaintenanceTasks.filter(t => t.priority === 'URGENT' && t.status !== 'COMPLETED').length;

  // Get overdue tasks (scheduled date passed but not completed)
  const overdueTasks = allMaintenanceTasks.filter(t => 
    t.status !== 'COMPLETED' && 
    new Date(t.scheduledDate) < new Date()
  ).length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الصيانة
        </Typography>
        <Typography variant="body1" color="text.secondary">
          جدولة ومتابعة مهام الصيانة الوقائية والتنبؤية
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي المهام
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {scheduledTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مجدولة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {inProgressTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                قيد التنفيذ
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {completedTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مكتملة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                {urgentTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                عاجلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                {overdueTasks}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                متأخرة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="قائمة المهام" />
            <Tab label="التقويم" />
            <Tab label="التحليلات" />
          </Tabs>
        </Box>

        {/* Tab 1: Task List */}
        <TabPanel value={tabValue} index={0}>
          {/* Filters and Search */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
              <TextField
                placeholder="البحث في المهام..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 300 }}
              />
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={filterStatus}
                  label="الحالة"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="ALL">جميع الحالات</MenuItem>
                  <MenuItem value="SCHEDULED">مجدولة</MenuItem>
                  <MenuItem value="IN_PROGRESS">قيد التنفيذ</MenuItem>
                  <MenuItem value="COMPLETED">مكتملة</MenuItem>
                  <MenuItem value="CANCELLED">ملغية</MenuItem>
                </Select>
              </FormControl>
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>الأولوية</InputLabel>
                <Select
                  value={filterPriority}
                  label="الأولوية"
                  onChange={(e) => setFilterPriority(e.target.value)}
                >
                  <MenuItem value="ALL">جميع الأولويات</MenuItem>
                  <MenuItem value="LOW">منخفضة</MenuItem>
                  <MenuItem value="MEDIUM">متوسطة</MenuItem>
                  <MenuItem value="HIGH">عالية</MenuItem>
                  <MenuItem value="URGENT">عاجلة</MenuItem>
                </Select>
              </FormControl>
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>المركبة</InputLabel>
                <Select
                  value={filterVehicle}
                  label="المركبة"
                  onChange={(e) => setFilterVehicle(e.target.value)}
                >
                  <MenuItem value="ALL">جميع المركبات</MenuItem>
                  {vehicles.map((vehicle) => (
                    <MenuItem key={vehicle.id} value={vehicle.id}>
                      {vehicle.plateNumber}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
              >
                تصفية متقدمة
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenAddDialog(true)}
              >
                إضافة مهمة
              </Button>
            </Box>
          </Box>

          {/* Data Grid */}
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredTasks}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{
                border: 0,
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
              }}
            />
          </Box>
        </TabPanel>

        {/* Tab 2: Calendar */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.100' }}>
            <Typography variant="h6" color="text.secondary">
              📅 تقويم الصيانة
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
              (سيتم تطويره في المرحلة التالية)
            </Typography>
          </Box>
        </TabPanel>

        {/* Tab 3: Analytics */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.100' }}>
            <Typography variant="h6" color="text.secondary">
              📊 تحليلات الصيانة
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
              (سيتم تطويره في المرحلة التالية)
            </Typography>
          </Box>
        </TabPanel>
      </Card>

      {/* Add Task Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة مهمة صيانة جديدة</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>المركبة</InputLabel>
                  <Select
                    value={newTask.vehicleId}
                    label="المركبة"
                    onChange={(e) => setNewTask({ ...newTask, vehicleId: e.target.value })}
                  >
                    {vehicles.map((vehicle) => (
                      <MenuItem key={vehicle.id} value={vehicle.id}>
                        {vehicle.plateNumber} - {vehicle.make} {vehicle.model}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>نوع المهمة</InputLabel>
                  <Select
                    value={newTask.taskType}
                    label="نوع المهمة"
                    onChange={(e) => setNewTask({ ...newTask, taskType: e.target.value as any })}
                  >
                    <MenuItem value="OIL_CHANGE">تغيير الزيت</MenuItem>
                    <MenuItem value="TIRE_ROTATION">تدوير الإطارات</MenuItem>
                    <MenuItem value="BRAKE_CHECK">فحص الفرامل</MenuItem>
                    <MenuItem value="ENGINE_SERVICE">صيانة المحرك</MenuItem>
                    <MenuItem value="GENERAL_INSPECTION">فحص عام</MenuItem>
                    <MenuItem value="CUSTOM">مخصص</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="الوصف"
                  value={newTask.description}
                  onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                  fullWidth
                  required
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>الأولوية</InputLabel>
                  <Select
                    value={newTask.priority}
                    label="الأولوية"
                    onChange={(e) => setNewTask({ ...newTask, priority: e.target.value as any })}
                  >
                    <MenuItem value="LOW">منخفضة</MenuItem>
                    <MenuItem value="MEDIUM">متوسطة</MenuItem>
                    <MenuItem value="HIGH">عالية</MenuItem>
                    <MenuItem value="URGENT">عاجلة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="التاريخ المجدول"
                  type="date"
                  value={newTask.scheduledDate}
                  onChange={(e) => setNewTask({ ...newTask, scheduledDate: e.target.value })}
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="المسؤول"
                  value={newTask.assignedTo}
                  onChange={(e) => setNewTask({ ...newTask, assignedTo: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="ملاحظات"
                  value={newTask.notes}
                  onChange={(e) => setNewTask({ ...newTask, notes: e.target.value })}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddTask} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaintenanceManagement;
