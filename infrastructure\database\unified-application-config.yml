# TECNODRIVE Platform - Unified Database Configuration Template
# This file contains the standardized database configuration for all services
# Copy the relevant sections to each service's application.yml

# Common Database Configuration
database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  username: ${DB_USERNAME:tecnodrive_admin}
  password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
  
  # Connection Pool Settings
  hikari:
    maximum-pool-size: 20
    minimum-idle: 5
    idle-timeout: 300000
    connection-timeout: 20000
    leak-detection-threshold: 60000
    max-lifetime: 1800000

# Service-specific Database URLs
services:
  auth-service:
    database-name: tecnodrive_auth
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_auth
    
  user-service:
    database-name: tecnodrive_users
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_users
    
  ride-service:
    database-name: tecnodrive_rides
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_rides
    
  fleet-service:
    database-name: tecnodrive_fleet
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_fleet
    
  parcel-service:
    database-name: tecnodrive_parcels
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_parcels
    
  payment-service:
    database-name: tecnodrive_payments
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_payments
    
  notification-service:
    database-name: tecnodrive_notifications
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_notifications
    
  financial-service:
    database-name: tecnodrive_financial
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_financial
    
  hr-service:
    database-name: tecnodrive_hr
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_hr
    
  analytics-service:
    database-name: tecnodrive_analytics
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_analytics
    
  saas-management-service:
    database-name: tecnodrive_saas
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_saas
    
  location-service:
    database-name: tecnodrive_location
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_location
    
  tracking-service:
    database-name: tecnodrive_tracking
    url: jdbc:postgresql://${database.host}:${database.port}/tecnodrive_tracking

# Standard JPA Configuration
jpa:
  hibernate:
    ddl-auto: validate  # Use 'update' for development, 'validate' for production
  show-sql: false
  properties:
    hibernate:
      dialect: org.hibernate.dialect.PostgreSQLDialect
      format_sql: true
      use_sql_comments: true
      jdbc:
        batch_size: 20
      order_inserts: true
      order_updates: true
  open-in-view: false

# Standard Redis Configuration
redis:
  host: ${REDIS_HOST:localhost}
  port: ${REDIS_PORT:6379}
  password: ${REDIS_PASSWORD:TecnoDrive2025!Redis#Cache}
  timeout: 2000ms
  lettuce:
    pool:
      max-active: 8
      max-idle: 8
      min-idle: 0
      max-wait: -1ms

# Standard Eureka Configuration
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URL:http://localhost:8761/eureka/}
    fetch-registry: true
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# Standard Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Standard Security Configuration
security:
  jwt:
    secret: ${JWT_SECRET:TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2025!}
    access-token-expiration: ${JWT_ACCESS_EXPIRATION:86400000}  # 24 hours
    refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days
    issuer: ${JWT_ISSUER:tecnodrive-platform}
    audience: ${JWT_AUDIENCE:tecnodrive-users}

# Standard Logging Configuration
logging:
  level:
    com.tecnodrive: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Environment-specific profiles
---
spring:
  config:
    activate:
      on-profile: development
      
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    
logging:
  level:
    com.tecnodrive: DEBUG
    org.hibernate.SQL: DEBUG

---
spring:
  config:
    activate:
      on-profile: production
      
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
logging:
  level:
    com.tecnodrive: INFO
    org.hibernate.SQL: WARN
