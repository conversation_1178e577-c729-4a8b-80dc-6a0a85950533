import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, Polyline, Circle, useMap, LayersControl, FeatureGroup } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './InteractiveMap.css';

// Fix for default markers in React Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// Custom vehicle icon
const vehicleIcon = new L.Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
  shadowSize: [41, 41],
});

// Traffic colors
const trafficColors = {
  'FREE_FLOW': '#00FF00',
  'LIGHT': '#FFFF00',
  'MODERATE': '#FFA500',
  'HEAVY': '#FF0000'
};

const InteractiveMap = () => {
  // Basic map state
  const [vehicles, setVehicles] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [traffic, setTraffic] = useState([]);
  const [geofences, setGeofences] = useState([]);
  const [clusters, setClusters] = useState([]);
  const [mapCenter, setMapCenter] = useState([24.7136, 46.6753]); // Riyadh
  const [zoomLevel, setZoomLevel] = useState(12);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // Advanced map features
  const [streetSegments, setStreetSegments] = useState([]);
  const [heatmapData, setHeatmapData] = useState([]);
  const [driverPerformance, setDriverPerformance] = useState([]);
  const [routeOptimizations, setRouteOptimizations] = useState([]);
  const [trafficAnalytics, setTrafficAnalytics] = useState([]);
  const [zoneAlerts, setZoneAlerts] = useState([]);
  const [demandAnalytics, setDemandAnalytics] = useState([]);

  // Layer visibility controls
  const [showTraffic, setShowTraffic] = useState(true);
  const [showGeofences, setShowGeofences] = useState(true);
  const [showClusters, setShowClusters] = useState(true);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [showPerformance, setShowPerformance] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showStreetSegments, setShowStreetSegments] = useState(false);

  // Advanced controls
  const [selectedLayers, setSelectedLayers] = useState(['vehicles', 'routes']);
  const [analysisMode, setAnalysisMode] = useState('realtime');
  const [timeWindow, setTimeWindow] = useState('1h');
  const [filterCriteria, setFilterCriteria] = useState({});
  const [collaborativeMode, setCollaborativeMode] = useState(false);
  
  const wsRef = useRef(null);
  const mapRef = useRef(null);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      const wsUrl = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8085';
      wsRef.current = new WebSocket(`${wsUrl}/ws`);

      wsRef.current.onopen = () => {
        console.log('Connected to map WebSocket');
        setIsConnected(true);
        
        // Subscribe to advanced map updates
        wsRef.current.send(JSON.stringify({
          type: 'subscribe',
          subscriptions: [
            'map:all',
            'map:street',
            'map:realtime',
            'map:layers',
            'map:heatmap',
            'map:analytics',
            'vehicle_tracking',
            'route_update',
            'traffic_update',
            'geofence_alert',
            'location_cluster',
            'heatmap_update',
            'navigation_update',
            'traffic_analytics',
            'zone_alert',
            'demand_analytics',
            'driver_performance',
            'route_optimization',
            'map_layer_update'
          ]
        }));
      };

      wsRef.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      wsRef.current.onclose = () => {
        console.log('Disconnected from map WebSocket');
        setIsConnected(false);
        // Reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'vehicle_tracking':
        updateVehiclePosition(message.data);
        break;
      case 'route_update':
        updateRoute(message.data);
        break;
      case 'traffic_update':
        updateTraffic(message.data);
        break;
      case 'geofence_alert':
        handleGeofenceAlert(message.data);
        break;
      case 'location_cluster':
        updateClusters(message.data);
        break;
      case 'map_update':
        handleMapUpdate(message.data);
        break;
      case 'heatmap_update':
        updateHeatmap(message.data);
        break;
      case 'navigation_update':
        updateNavigation(message.data);
        break;
      case 'traffic_analytics':
        updateTrafficAnalytics(message.data);
        break;
      case 'zone_alert':
        handleZoneAlert(message.data);
        break;
      case 'demand_analytics':
        updateDemandAnalytics(message.data);
        break;
      case 'driver_performance':
        updateDriverPerformance(message.data);
        break;
      case 'route_optimization':
        updateRouteOptimization(message.data);
        break;
      case 'map_layer_update':
        updateMapLayer(message.data);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  };

  // Update vehicle position
  const updateVehiclePosition = (vehicleData) => {
    setVehicles(prev => {
      const index = prev.findIndex(v => v.vehicleId === vehicleData.vehicleId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = { ...updated[index], ...vehicleData };
        return updated;
      } else {
        return [...prev, vehicleData];
      }
    });
  };

  // Update route
  const updateRoute = (routeData) => {
    setRoutes(prev => {
      const index = prev.findIndex(r => r.routeId === routeData.routeId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = routeData;
        return updated;
      } else {
        return [...prev, routeData];
      }
    });
  };

  // Update traffic
  const updateTraffic = (trafficData) => {
    setTraffic(prev => {
      const index = prev.findIndex(t => t.streetId === trafficData.streetId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = trafficData;
        return updated;
      } else {
        return [...prev, trafficData];
      }
    });
  };

  // Handle geofence alert
  const handleGeofenceAlert = (alertData) => {
    // Show notification or highlight on map
    console.log('Geofence alert:', alertData);
    // You can add notification logic here
  };

  // Update clusters
  const updateClusters = (clusterData) => {
    setClusters(clusterData.clusters || []);
  };

  // Handle general map updates
  const handleMapUpdate = (updateData) => {
    if (updateData.type === 'geofence_update') {
      setGeofences(prev => {
        const index = prev.findIndex(g => g.geofenceId === updateData.data.geofenceId);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = updateData.data;
          return updated;
        } else {
          return [...prev, updateData.data];
        }
      });
    }
  };

  // Advanced update handlers
  const updateHeatmap = useCallback((heatmapData) => {
    setHeatmapData(prev => {
      const index = prev.findIndex(h => h.demandType === heatmapData.demandType);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = heatmapData;
        return updated;
      } else {
        return [...prev, heatmapData];
      }
    });
  }, []);

  const updateNavigation = useCallback((navData) => {
    setRoutes(prev => {
      const index = prev.findIndex(r => r.routeId === navData.routeId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = { ...updated[index], ...navData };
        return updated;
      }
      return prev;
    });
  }, []);

  const updateTrafficAnalytics = useCallback((analyticsData) => {
    setTrafficAnalytics(prev => {
      const index = prev.findIndex(a => a.zoneId === analyticsData.zoneId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = analyticsData;
        return updated;
      } else {
        return [...prev, analyticsData];
      }
    });
  }, []);

  const handleZoneAlert = useCallback((alertData) => {
    setZoneAlerts(prev => [...prev, alertData]);

    // Show notification for high priority alerts
    if (alertData.priority === 'high') {
      console.warn('High priority zone alert:', alertData);
      // You can add notification logic here
    }
  }, []);

  const updateDemandAnalytics = useCallback((demandData) => {
    setDemandAnalytics(prev => {
      const index = prev.findIndex(d => d.zoneId === demandData.zoneId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = demandData;
        return updated;
      } else {
        return [...prev, demandData];
      }
    });
  }, []);

  const updateDriverPerformance = useCallback((perfData) => {
    setDriverPerformance(prev => {
      const index = prev.findIndex(p => p.driverId === perfData.driverId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = perfData;
        return updated;
      } else {
        return [...prev, perfData];
      }
    });
  }, []);

  const updateRouteOptimization = useCallback((optData) => {
    setRouteOptimizations(prev => {
      const index = prev.findIndex(o => o.routeId === optData.routeId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = optData;
        return updated;
      } else {
        return [...prev, optData];
      }
    });
  }, []);

  const updateMapLayer = useCallback((layerData) => {
    switch (layerData.layerType) {
      case 'street_segments':
        setStreetSegments(prev => {
          const index = prev.findIndex(s => s.segmentId === layerData.data.segmentId);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = layerData.data;
            return updated;
          } else {
            return [...prev, layerData.data];
          }
        });
        break;
      default:
        console.log('Unknown layer type:', layerData.layerType);
    }
  }, []);

  // Map event handlers
  const handleMapMove = (center, zoom) => {
    setMapCenter(center);
    setZoomLevel(zoom);
    
    // Send viewport update to server
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'viewport_update',
        data: {
          sessionId: 'admin-dashboard',
          centerLat: center[0],
          centerLng: center[1],
          zoomLevel: zoom,
          bounds: {
            // Calculate bounds based on center and zoom
          }
        }
      }));
    }
  };

  // Vehicle click handler
  const handleVehicleClick = (vehicle) => {
    setSelectedVehicle(vehicle);
  };

  // Map component to handle events
  const MapEvents = () => {
    const map = useMap();
    
    useEffect(() => {
      const handleMoveEnd = () => {
        const center = map.getCenter();
        const zoom = map.getZoom();
        handleMapMove([center.lat, center.lng], zoom);
      };

      map.on('moveend', handleMoveEnd);
      map.on('zoomend', handleMoveEnd);

      return () => {
        map.off('moveend', handleMoveEnd);
        map.off('zoomend', handleMoveEnd);
      };
    }, [map]);

    return null;
  };

  return (
    <div className="interactive-map-container">
      {/* Map Controls */}
      <div className="map-controls">
        <div className="connection-status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢 متصل' : '🔴 غير متصل'}
          </span>
        </div>
        
        <div className="layer-controls">
          <label>
            <input
              type="checkbox"
              checked={showTraffic}
              onChange={(e) => setShowTraffic(e.target.checked)}
            />
            عرض حركة المرور
          </label>
          <label>
            <input
              type="checkbox"
              checked={showGeofences}
              onChange={(e) => setShowGeofences(e.target.checked)}
            />
            عرض المناطق المحددة
          </label>
          <label>
            <input
              type="checkbox"
              checked={showClusters}
              onChange={(e) => setShowClusters(e.target.checked)}
            />
            عرض التجميعات
          </label>
        </div>

        <div className="map-stats">
          <div>المركبات النشطة: {vehicles.length}</div>
          <div>الطرق النشطة: {routes.length}</div>
          <div>مستوى التكبير: {zoomLevel}</div>
        </div>
      </div>

      {/* Interactive Map */}
      <MapContainer
        center={mapCenter}
        zoom={zoomLevel}
        style={{ height: '600px', width: '100%' }}
        ref={mapRef}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        
        <MapEvents />

        {/* Vehicle Markers */}
        {vehicles.map(vehicle => (
          <Marker
            key={vehicle.vehicleId}
            position={[vehicle.lat, vehicle.lng]}
            icon={vehicleIcon}
            eventHandlers={{
              click: () => handleVehicleClick(vehicle)
            }}
          >
            <Popup>
              <div className="vehicle-popup">
                <h4>مركبة {vehicle.vehicleId}</h4>
                <p>الحالة: {vehicle.status}</p>
                <p>السرعة: {vehicle.speed} كم/س</p>
                <p>الاتجاه: {vehicle.heading}°</p>
                <p>آخر تحديث: {new Date(vehicle.timestamp).toLocaleTimeString('ar-SA')}</p>
              </div>
            </Popup>
          </Marker>
        ))}

        {/* Route Polylines */}
        {routes.map(route => (
          route.waypoints && (
            <Polyline
              key={route.routeId}
              positions={route.waypoints.map(wp => [wp.lat, wp.lng])}
              color="blue"
              weight={4}
              opacity={0.7}
            >
              <Popup>
                <div className="route-popup">
                  <h4>طريق {route.routeId}</h4>
                  <p>المركبة: {route.vehicleId}</p>
                  {route.routeInfo && (
                    <>
                      <p>المسافة: {route.routeInfo.distance} كم</p>
                      <p>الوقت المتوقع: {route.routeInfo.duration} دقيقة</p>
                    </>
                  )}
                </div>
              </Popup>
            </Polyline>
          )
        ))}

        {/* Traffic Indicators */}
        {showTraffic && traffic.map(trafficItem => (
          <Circle
            key={trafficItem.streetId}
            center={[trafficItem.lat || mapCenter[0], trafficItem.lng || mapCenter[1]]}
            radius={100}
            color={trafficColors[trafficItem.trafficLevel] || '#808080'}
            fillOpacity={0.3}
          >
            <Popup>
              <div className="traffic-popup">
                <h4>حركة المرور</h4>
                <p>الشارع: {trafficItem.streetId}</p>
                <p>المستوى: {trafficItem.trafficLevel}</p>
                <p>السرعة المتوسطة: {trafficItem.avgSpeed} كم/س</p>
                <p>{trafficItem.description}</p>
              </div>
            </Popup>
          </Circle>
        ))}

        {/* Geofences */}
        {showGeofences && geofences.map(geofence => (
          geofence.coordinates && (
            <Polyline
              key={geofence.geofenceId}
              positions={geofence.coordinates.map(coord => [coord.lat, coord.lng])}
              color="red"
              weight={2}
              opacity={0.8}
              fill={true}
              fillOpacity={0.1}
            >
              <Popup>
                <div className="geofence-popup">
                  <h4>{geofence.name}</h4>
                  <p>النوع: {geofence.type}</p>
                  <p>الحالة: {geofence.active ? 'نشط' : 'غير نشط'}</p>
                </div>
              </Popup>
            </Polyline>
          )
        ))}

        {/* Clusters */}
        {showClusters && clusters.map((cluster, index) => (
          cluster.type === 'cluster' ? (
            <Circle
              key={`cluster-${index}`}
              center={[cluster.lat, cluster.lng]}
              radius={50}
              color="purple"
              fillOpacity={0.5}
            >
              <Popup>
                <div className="cluster-popup">
                  <h4>تجميع المركبات</h4>
                  <p>عدد المركبات: {cluster.count}</p>
                  <p>المركبات: {cluster.vehicles.join(', ')}</p>
                </div>
              </Popup>
            </Circle>
          ) : (
            <Marker
              key={`vehicle-${cluster.vehicleId}`}
              position={[cluster.lat, cluster.lng]}
              icon={vehicleIcon}
            >
              <Popup>
                <div className="vehicle-popup">
                  <h4>مركبة {cluster.vehicleId}</h4>
                  <p>الحالة: {cluster.status}</p>
                </div>
              </Popup>
            </Marker>
          )
        ))}
      </MapContainer>

      {/* Selected Vehicle Details */}
      {selectedVehicle && (
        <div className="vehicle-details-panel">
          <div className="panel-header">
            <h3>تفاصيل المركبة {selectedVehicle.vehicleId}</h3>
            <button onClick={() => setSelectedVehicle(null)}>×</button>
          </div>
          <div className="panel-content">
            <p><strong>الموقع:</strong> {selectedVehicle.lat.toFixed(6)}, {selectedVehicle.lng.toFixed(6)}</p>
            <p><strong>السرعة:</strong> {selectedVehicle.speed} كم/س</p>
            <p><strong>الاتجاه:</strong> {selectedVehicle.heading}°</p>
            <p><strong>الحالة:</strong> {selectedVehicle.status}</p>
            <p><strong>آخر تحديث:</strong> {new Date(selectedVehicle.timestamp).toLocaleString('ar-SA')}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default InteractiveMap;
