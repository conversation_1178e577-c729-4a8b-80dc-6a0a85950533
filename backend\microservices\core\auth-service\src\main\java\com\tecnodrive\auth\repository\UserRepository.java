package com.tecnodrive.auth.repository;

import com.tecnodrive.auth.entity.User;
import com.tecnodrive.auth.entity.UserStatus;
import com.tecnodrive.auth.entity.UserType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * User Repository for Authentication Service
 */
@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);

    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by username or email
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Find users by status
     */
    List<User> findByStatus(UserStatus status);

    /**
     * Find users by type
     */
    List<User> findByUserType(UserType userType);

    /**
     * Find users by company
     */
    List<User> findByCompanyId(UUID companyId);

    /**
     * Find user by reset password token
     */
    Optional<User> findByResetPasswordToken(String token);

    /**
     * Find user by email verification token
     */
    Optional<User> findByEmailVerificationToken(String token);

    /**
     * Find users created after a specific date
     */
    List<User> findByCreatedAtAfter(Instant date);

    /**
     * Find active users by type
     */
    List<User> findByStatusAndUserType(UserStatus status, UserType userType);

    /**
     * Count users by status
     */
    long countByStatus(UserStatus status);

    /**
     * Count users by type
     */
    long countByUserType(UserType userType);

    /**
     * Find users with expired reset tokens
     */
    @Query("SELECT u FROM User u WHERE u.resetPasswordTokenExpiry < :now AND u.resetPasswordToken IS NOT NULL")
    List<User> findUsersWithExpiredResetTokens(@Param("now") Instant now);
}
