import { apiMeth<PERSON>, handleApiError, createMockResponse, simulateApiDelay } from './api';
import { SERVICE_URLS } from './api';
// Note: Using local type definitions instead of imports to avoid conflicts

// Enhanced Types for SaaS Service
export interface TenantDto {
  id: string;
  name: string;
  domain: string;
  phone: string;
  email: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  createdAt: string;
  updatedAt: string;
  subscriptionsCount?: number;
  totalUsers?: number;
  billingBalance?: number;
  monthlyRevenue?: number;
  dataUsage?: number; // in GB
  apiCalls?: number;
  lastActivity?: string;
}

export interface CreateTenantRequest {
  name: string;
  domain: string;
  phone: string;
  email: string;
  contactPerson: string;
  address?: string;
}

export interface UpdateTenantRequest {
  name?: string;
  domain?: string;
  phone?: string;
  email?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  address?: string;
}

export interface SubscriptionDto {
  id: string;
  tenantId: string;
  planId: string;
  planName: string;
  startDate: string;
  endDate: string;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'PENDING';
  seats: number;
  usedSeats: number;
  monthlyPrice: number;
  currency: string;
  features: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubscriptionRequest {
  tenantId: string;
  planId: string;
  startDate: string;
  endDate: string;
  seats: number;
  customPrice?: number;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  endDate?: string;
  seats?: number;
  status?: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
}

export interface BillingInvoiceDto {
  id: string;
  tenantId: string;
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  items: BillingItemDto[];
  createdAt: string;
  paidAt?: string;
}

export interface BillingItemDto {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  subscriptionId?: string;
}

export interface UsageAnalyticsDto {
  tenantId: string;
  period: string;
  activeUsers: number;
  totalApiCalls: number;
  dataUsageGB: number;
  peakUsers: number;
  peakDate: string;
  moduleUsage: ModuleUsageDto[];
  dailyStats: DailyUsageDto[];
}

export interface ModuleUsageDto {
  moduleName: string;
  usageCount: number;
  percentage: number;
}

export interface DailyUsageDto {
  date: string;
  activeUsers: number;
  apiCalls: number;
  dataUsageGB: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class SaasService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = SERVICE_URLS.SAAS_SERVICE;
  }

  // Tenant Management
  async getTenants(search?: string, status?: string): Promise<ApiResponse<TenantDto[]>> {
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (status && status !== 'ALL') params.append('status', status);

      const response = await apiMethods.get<ApiResponse<TenantDto[]>>(
        `${this.baseUrl}?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.getMockTenants(search, status);
      }

      throw new Error(handleApiError(error));
    }
  }

  async getTenantById(tenantId: string): Promise<ApiResponse<TenantDto>> {
    try {
      const response = await apiMethods.get<ApiResponse<TenantDto>>(
        `${this.baseUrl}/${tenantId}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.getMockTenantById(tenantId);
      }

      throw new Error(handleApiError(error));
    }
  }

  async createTenant(tenantData: CreateTenantRequest): Promise<ApiResponse<TenantDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<TenantDto>>(
        this.baseUrl,
        tenantData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.createMockTenant(tenantData);
      }

      throw new Error(handleApiError(error));
    }
  }

  async updateTenant(tenantId: string, tenantData: UpdateTenantRequest): Promise<ApiResponse<TenantDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<TenantDto>>(
        `${this.baseUrl}/${tenantId}`,
        tenantData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.updateMockTenant(tenantId, tenantData);
      }

      throw new Error(handleApiError(error));
    }
  }

  async deleteTenant(tenantId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.delete<ApiResponse<void>>(
        `${this.baseUrl}/${tenantId}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.deleteMockTenant(tenantId);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Subscription Management
  async getSubscriptions(tenantId: string): Promise<ApiResponse<SubscriptionDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<SubscriptionDto[]>>(
        `${this.baseUrl}/${tenantId}/subscriptions`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.getMockSubscriptions(tenantId);
      }

      throw new Error(handleApiError(error));
    }
  }

  async createSubscription(subscriptionData: CreateSubscriptionRequest): Promise<ApiResponse<SubscriptionDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<SubscriptionDto>>(
        `${this.baseUrl}/${subscriptionData.tenantId}/subscriptions`,
        subscriptionData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.createMockSubscription(subscriptionData);
      }

      throw new Error(handleApiError(error));
    }
  }

  async updateSubscription(subscriptionId: string, subscriptionData: UpdateSubscriptionRequest): Promise<ApiResponse<SubscriptionDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<SubscriptionDto>>(
        `${this.baseUrl}/subscriptions/${subscriptionId}`,
        subscriptionData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.updateMockSubscription(subscriptionId, subscriptionData);
      }

      throw new Error(handleApiError(error));
    }
  }

  async deleteSubscription(subscriptionId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.delete<ApiResponse<void>>(
        `${this.baseUrl}/subscriptions/${subscriptionId}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.deleteMockSubscription(subscriptionId);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Billing Management
  async getBillingInvoices(tenantId: string): Promise<ApiResponse<BillingInvoiceDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<BillingInvoiceDto[]>>(
        `${this.baseUrl}/${tenantId}/billing/invoices`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.getMockBillingInvoices(tenantId);
      }

      throw new Error(handleApiError(error));
    }
  }

  async generateInvoice(tenantId: string): Promise<ApiResponse<BillingInvoiceDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<BillingInvoiceDto>>(
        `${this.baseUrl}/${tenantId}/billing/invoices`,
        {}
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.generateMockInvoice(tenantId);
      }

      throw new Error(handleApiError(error));
    }
  }

  async payInvoice(invoiceId: string, paymentData: any): Promise<ApiResponse<BillingInvoiceDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<BillingInvoiceDto>>(
        `${this.baseUrl}/billing/invoices/${invoiceId}/pay`,
        paymentData
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.payMockInvoice(invoiceId, paymentData);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Usage Analytics
  async getUsageAnalytics(tenantId: string, from?: string, to?: string): Promise<ApiResponse<UsageAnalyticsDto>> {
    try {
      const params = new URLSearchParams();
      if (from) params.append('from', from);
      if (to) params.append('to', to);

      const response = await apiMethods.get<ApiResponse<UsageAnalyticsDto>>(
        `${this.baseUrl}/${tenantId}/usage?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return await this.getMockUsageAnalytics(tenantId, from, to);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Mock Data Methods (for development/testing)
  private async getMockTenants(search?: string, status?: string): Promise<ApiResponse<TenantDto[]>> {
    await simulateApiDelay();

    const mockTenants: TenantDto[] = [
      {
        id: '1',
        name: 'شركة أكمي',
        domain: 'acme.tecno-drive.com',
        phone: '+967712345678',
        email: '<EMAIL>',
        status: 'ACTIVE',
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-07-07T10:00:00Z',
        subscriptionsCount: 3,
        totalUsers: 120,
        billingBalance: 5000,
      },
      {
        id: '2',
        name: 'مؤسسة جلوبكس',
        domain: 'globex.tecno-drive.com',
        phone: '+967712345679',
        email: '<EMAIL>',
        status: 'ACTIVE',
        createdAt: '2025-02-15T00:00:00Z',
        updatedAt: '2025-07-07T10:00:00Z',
        subscriptionsCount: 2,
        totalUsers: 85,
        billingBalance: 3200,
      },
      {
        id: '3',
        name: 'شركة التقنية المتقدمة',
        domain: 'advanced-tech.tecno-drive.com',
        phone: '+967712345680',
        email: '<EMAIL>',
        status: 'INACTIVE',
        createdAt: '2025-03-10T00:00:00Z',
        updatedAt: '2025-07-07T10:00:00Z',
        subscriptionsCount: 1,
        totalUsers: 25,
        billingBalance: 0,
      },
    ];

    let filteredTenants = mockTenants;

    if (search) {
      filteredTenants = filteredTenants.filter(tenant =>
        tenant.name.toLowerCase().includes(search.toLowerCase()) ||
        tenant.domain.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (status && status !== 'ALL') {
      filteredTenants = filteredTenants.filter(tenant => tenant.status === status);
    }

    return createMockResponse(filteredTenants);
  }

  private async getMockTenantById(tenantId: string): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();

    const mockTenant: TenantDto = {
      id: tenantId,
      name: 'شركة أكمي',
      domain: 'acme.tecno-drive.com',
      phone: '+967712345678',
      email: '<EMAIL>',
      status: 'ACTIVE',
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: '2025-07-07T10:00:00Z',
      subscriptionsCount: 3,
      totalUsers: 120,
      billingBalance: 5000,
    };

    return createMockResponse(mockTenant);
  }

  private async createMockTenant(tenantData: CreateTenantRequest): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();

    const newTenant: TenantDto = {
      id: Date.now().toString(),
      ...tenantData,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subscriptionsCount: 0,
      totalUsers: 0,
      billingBalance: 0,
    };

    return createMockResponse(newTenant);
  }

  private async updateMockTenant(tenantId: string, tenantData: UpdateTenantRequest): Promise<ApiResponse<TenantDto>> {
    await simulateApiDelay();

    const updatedTenant: TenantDto = {
      id: tenantId,
      name: tenantData.name || 'شركة أكمي',
      domain: tenantData.domain || 'acme.tecno-drive.com',
      phone: tenantData.phone || '+967712345678',
      email: tenantData.email || '<EMAIL>',
      status: tenantData.status || 'ACTIVE',
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: new Date().toISOString(),
      subscriptionsCount: 3,
      totalUsers: 120,
      billingBalance: 5000,
    };

    return createMockResponse(updatedTenant);
  }

  private async deleteMockTenant(_tenantId: string): Promise<ApiResponse<void>> {
    await simulateApiDelay();
    return createMockResponse(undefined);
  }

  private async getMockSubscriptions(tenantId: string): Promise<ApiResponse<SubscriptionDto[]>> {
    await simulateApiDelay();

    const mockSubscriptions: SubscriptionDto[] = [
      {
        id: '1',
        tenantId,
        planId: 'basic',
        planName: 'الخطة الأساسية',
        startDate: '2025-01-01',
        endDate: '2026-01-01',
        status: 'ACTIVE',
        seats: 50,
        usedSeats: 35,
        monthlyPrice: 2500,
        currency: 'YER',
        features: ['إدارة الرحلات', 'تتبع الأسطول', 'التقارير الأساسية'],
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-07-07T10:00:00Z',
      },
      {
        id: '2',
        tenantId,
        planId: 'premium',
        planName: 'الخطة المتقدمة',
        startDate: '2025-03-15',
        endDate: '2026-03-15',
        status: 'ACTIVE',
        seats: 100,
        usedSeats: 85,
        monthlyPrice: 5000,
        currency: 'YER',
        features: ['جميع مميزات الخطة الأساسية', 'تحليلات متقدمة', 'دعم 24/7', 'API مخصص'],
        createdAt: '2025-03-15T00:00:00Z',
        updatedAt: '2025-07-07T10:00:00Z',
      },
    ];

    return createMockResponse(mockSubscriptions);
  }

  private async createMockSubscription(subscriptionData: CreateSubscriptionRequest): Promise<ApiResponse<SubscriptionDto>> {
    await simulateApiDelay();

    const newSubscription: SubscriptionDto = {
      id: Date.now().toString(),
      ...subscriptionData,
      planName: 'خطة جديدة',
      status: 'ACTIVE',
      usedSeats: 0,
      monthlyPrice: 2500,
      currency: 'YER',
      features: ['مميزات أساسية'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return createMockResponse(newSubscription);
  }

  private async updateMockSubscription(subscriptionId: string, subscriptionData: UpdateSubscriptionRequest): Promise<ApiResponse<SubscriptionDto>> {
    await simulateApiDelay();

    const updatedSubscription: SubscriptionDto = {
      id: subscriptionId,
      tenantId: '1',
      planId: subscriptionData.planId || 'basic',
      planName: 'الخطة الأساسية',
      startDate: '2025-01-01',
      endDate: subscriptionData.endDate || '2026-01-01',
      status: subscriptionData.status || 'ACTIVE',
      seats: subscriptionData.seats || 50,
      usedSeats: 35,
      monthlyPrice: 2500,
      currency: 'YER',
      features: ['إدارة الرحلات', 'تتبع الأسطول'],
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: new Date().toISOString(),
    };

    return createMockResponse(updatedSubscription);
  }

  private async deleteMockSubscription(_subscriptionId: string): Promise<ApiResponse<void>> {
    await simulateApiDelay();
    return createMockResponse(undefined);
  }

  private async getMockBillingInvoices(tenantId: string): Promise<ApiResponse<BillingInvoiceDto[]>> {
    await simulateApiDelay();

    const mockInvoices: BillingInvoiceDto[] = [
      {
        id: '1',
        tenantId,
        invoiceNumber: 'INV-2025-001',
        issueDate: '2025-07-01',
        dueDate: '2025-07-31',
        amount: 7500,
        currency: 'YER',
        status: 'PENDING',
        items: [
          {
            id: '1',
            description: 'الخطة الأساسية - يوليو 2025',
            quantity: 1,
            unitPrice: 2500,
            totalPrice: 2500,
            subscriptionId: '1',
          },
          {
            id: '2',
            description: 'الخطة المتقدمة - يوليو 2025',
            quantity: 1,
            unitPrice: 5000,
            totalPrice: 5000,
            subscriptionId: '2',
          },
        ],
        createdAt: '2025-07-01T00:00:00Z',
      },
      {
        id: '2',
        tenantId,
        invoiceNumber: 'INV-2025-002',
        issueDate: '2025-06-01',
        dueDate: '2025-06-30',
        amount: 7500,
        currency: 'YER',
        status: 'PAID',
        items: [
          {
            id: '3',
            description: 'الخطة الأساسية - يونيو 2025',
            quantity: 1,
            unitPrice: 2500,
            totalPrice: 2500,
            subscriptionId: '1',
          },
          {
            id: '4',
            description: 'الخطة المتقدمة - يونيو 2025',
            quantity: 1,
            unitPrice: 5000,
            totalPrice: 5000,
            subscriptionId: '2',
          },
        ],
        createdAt: '2025-06-01T00:00:00Z',
        paidAt: '2025-06-15T10:30:00Z',
      },
    ];

    return createMockResponse(mockInvoices);
  }

  private async generateMockInvoice(tenantId: string): Promise<ApiResponse<BillingInvoiceDto>> {
    await simulateApiDelay();

    const newInvoice: BillingInvoiceDto = {
      id: Date.now().toString(),
      tenantId,
      invoiceNumber: `INV-2025-${Date.now().toString().slice(-3)}`,
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      amount: 7500,
      currency: 'YER',
      status: 'PENDING',
      items: [
        {
          id: '1',
          description: 'اشتراك شهري',
          quantity: 1,
          unitPrice: 7500,
          totalPrice: 7500,
        },
      ],
      createdAt: new Date().toISOString(),
    };

    return createMockResponse(newInvoice);
  }

  private async payMockInvoice(invoiceId: string, _paymentData: any): Promise<ApiResponse<BillingInvoiceDto>> {
    await simulateApiDelay();

    const paidInvoice: BillingInvoiceDto = {
      id: invoiceId,
      tenantId: '1',
      invoiceNumber: 'INV-2025-001',
      issueDate: '2025-07-01',
      dueDate: '2025-07-31',
      amount: 7500,
      currency: 'YER',
      status: 'PAID',
      items: [],
      createdAt: '2025-07-01T00:00:00Z',
      paidAt: new Date().toISOString(),
    };

    return createMockResponse(paidInvoice);
  }

  private async getMockUsageAnalytics(tenantId: string, from?: string, to?: string): Promise<ApiResponse<UsageAnalyticsDto>> {
    await simulateApiDelay();

    const mockAnalytics: UsageAnalyticsDto = {
      tenantId,
      period: `${from || '2025-06-07'} to ${to || '2025-07-07'}`,
      activeUsers: 120,
      totalApiCalls: 15000,
      dataUsageGB: 80,
      peakUsers: 145,
      peakDate: '2025-07-05',
      moduleUsage: [
        { moduleName: 'إدارة الرحلات', usageCount: 8500, percentage: 56.7 },
        { moduleName: 'تتبع الأسطول', usageCount: 4200, percentage: 28.0 },
        { moduleName: 'التقارير', usageCount: 1800, percentage: 12.0 },
        { moduleName: 'إدارة المدفوعات', usageCount: 500, percentage: 3.3 },
      ],
      dailyStats: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        activeUsers: Math.floor(Math.random() * 50) + 100,
        apiCalls: Math.floor(Math.random() * 200) + 400,
        dataUsageGB: Math.floor(Math.random() * 5) + 2,
      })),
    };

    return createMockResponse(mockAnalytics);
  }
}

// Create singleton instance
export const saasService = new SaasService();

export default saasService;
