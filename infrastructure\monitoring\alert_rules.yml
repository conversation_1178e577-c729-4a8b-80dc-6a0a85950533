# Alert Rules for TecnoDrive Platform
groups:
  # Service Health Alerts
  - name: service_health
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.job }}"
          description: "Error rate is {{ $value }} errors per second on {{ $labels.job }}."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s on {{ $labels.job }}."

  # Circuit Breaker Alerts
  - name: circuit_breaker
    rules:
      - alert: CircuitBreakerOpen
        expr: resilience4j_circuitbreaker_state{state="open"} == 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Circuit breaker {{ $labels.name }} is open"
          description: "Circuit breaker {{ $labels.name }} on {{ $labels.job }} has been open for more than 1 minute."

      - alert: CircuitBreakerHalfOpen
        expr: resilience4j_circuitbreaker_state{state="half_open"} == 1
        for: 5m
        labels:
          severity: info
        annotations:
          summary: "Circuit breaker {{ $labels.name }} is half-open"
          description: "Circuit breaker {{ $labels.name }} on {{ $labels.job }} has been half-open for more than 5 minutes."

  # Database Alerts
  - name: database
    rules:
      - alert: DatabaseConnectionsHigh
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage on {{ $labels.job }}"
          description: "Database connection pool usage is {{ $value | humanizePercentage }} on {{ $labels.job }}."

      - alert: DatabaseSlowQueries
        expr: rate(hikaricp_connections_timeout_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Database connection timeouts on {{ $labels.job }}"
          description: "Database connections are timing out at {{ $value }} per second on {{ $labels.job }}."

  # Memory and CPU Alerts
  - name: resource_usage
    rules:
      - alert: HighMemoryUsage
        expr: (jvm_memory_used_bytes / jvm_memory_max_bytes) > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.job }}"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.job }}."

      - alert: HighCPUUsage
        expr: process_cpu_usage > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.job }}"
          description: "CPU usage is {{ $value | humanizePercentage }} on {{ $labels.job }}."

      - alert: HighGCTime
        expr: rate(jvm_gc_pause_seconds_sum[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High GC time on {{ $labels.job }}"
          description: "Garbage collection is taking {{ $value }}s per second on {{ $labels.job }}."

  # Security Alerts
  - name: security
    rules:
      - alert: HighFailedLoginAttempts
        expr: rate(auth_login_attempts_total{status="failed"}[5m]) > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High failed login attempts"
          description: "Failed login attempts rate is {{ $value }} per second."

      - alert: AccountLockouts
        expr: rate(auth_account_lockouts_total[5m]) > 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Account lockouts detected"
          description: "Account lockout rate is {{ $value }} per second."

      - alert: RateLimitExceeded
        expr: rate(gateway_rate_limit_exceeded_total[5m]) > 5
        for: 2m
        labels:
          severity: info
        annotations:
          summary: "Rate limit frequently exceeded"
          description: "Rate limit exceeded {{ $value }} times per second."

  # Business Logic Alerts
  - name: business_metrics
    rules:
      - alert: LowRideCompletionRate
        expr: (rate(rides_completed_total[10m]) / rate(rides_requested_total[10m])) < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low ride completion rate"
          description: "Ride completion rate is {{ $value | humanizePercentage }}."

      - alert: HighPaymentFailureRate
        expr: rate(payments_failed_total[5m]) / rate(payments_attempted_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High payment failure rate"
          description: "Payment failure rate is {{ $value | humanizePercentage }}."

      - alert: LocationTrackingGaps
        expr: rate(location_updates_total[5m]) < 10
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Low location update frequency"
          description: "Location updates are only {{ $value }} per second."

  # Infrastructure Alerts
  - name: infrastructure
    rules:
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis instance is not responding."

      - alert: KafkaDown
        expr: kafka_server_brokertopicmetrics_messagesinpersec == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Kafka is down or not processing messages"
          description: "Kafka broker is not processing any messages."

      - alert: DiskSpaceHigh
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full on {{ $labels.device }}."

  # Custom Application Alerts
  - name: application_specific
    rules:
      - alert: DriverOfflineHigh
        expr: (drivers_offline / drivers_total) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High percentage of drivers offline"
          description: "{{ $value | humanizePercentage }} of drivers are currently offline."

      - alert: VehicleMaintenanceOverdue
        expr: vehicles_maintenance_overdue > 10
        for: 1h
        labels:
          severity: info
        annotations:
          summary: "Vehicles overdue for maintenance"
          description: "{{ $value }} vehicles are overdue for maintenance."

      - alert: RideRequestBacklog
        expr: ride_requests_pending > 50
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High number of pending ride requests"
          description: "{{ $value }} ride requests are pending assignment."

      - alert: PaymentProcessingDelay
        expr: avg(payment_processing_duration_seconds) > 30
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Payment processing delays detected"
          description: "Average payment processing time is {{ $value }}s."

      - alert: LocationUpdateGap
        expr: time() - max(location_last_update_timestamp) > 300
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Location updates stopped"
          description: "No location updates received for more than 5 minutes."

      - alert: HighCancellationRate
        expr: rate(rides_cancelled_total[10m]) / rate(rides_requested_total[10m]) > 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High ride cancellation rate"
          description: "Ride cancellation rate is {{ $value | humanizePercentage }}."

  # Advanced Performance Alerts
  - name: performance_advanced
    rules:
      - alert: APIResponseTimeP99High
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High P99 response time on {{ $labels.job }}"
          description: "99th percentile response time is {{ $value }}s on {{ $labels.job }}."

      - alert: DatabaseConnectionPoolExhaustion
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Connection pool usage is {{ $value | humanizePercentage }} on {{ $labels.job }}."

      - alert: JVMMemoryPressure
        expr: (jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"}) > 0.85
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High JVM heap memory usage"
          description: "JVM heap memory usage is {{ $value | humanizePercentage }} on {{ $labels.job }}."

      - alert: GarbageCollectionHigh
        expr: rate(jvm_gc_pause_seconds_sum[5m]) > 0.2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High garbage collection time"
          description: "GC is consuming {{ $value | humanizePercentage }} of CPU time on {{ $labels.job }}."

  # Distributed Tracing Alerts
  - name: tracing_alerts
    rules:
      - alert: TracingErrorRateHigh
        expr: rate(tracing_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High tracing error rate"
          description: "Tracing error rate is {{ $value }} errors per second."

      - alert: TracingLatencyHigh
        expr: histogram_quantile(0.95, rate(tracing_processing_duration_seconds_bucket[5m])) > 0.5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High tracing processing latency"
          description: "95th percentile tracing latency is {{ $value }}s."

      - alert: SpanDropRateHigh
        expr: rate(tracing_spans_dropped_total[5m]) / rate(tracing_spans_generated_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High span drop rate"
          description: "{{ $value | humanizePercentage }} of spans are being dropped."

  # Business Logic Alerts (Enhanced)
  - name: business_metrics_enhanced
    rules:
      - alert: RevenueDropSignificant
        expr: (sum(rate(payments_completed_amount_total[1h])) - sum(rate(payments_completed_amount_total[1h] offset 24h))) / sum(rate(payments_completed_amount_total[1h] offset 24h)) < -0.2
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Significant revenue drop detected"
          description: "Revenue has dropped by {{ $value | humanizePercentage }} compared to 24h ago."

      - alert: CustomerSatisfactionLow
        expr: avg(ride_rating_average) < 3.5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low customer satisfaction scores"
          description: "Average ride rating is {{ $value }} (below 3.5)."

      - alert: DriverUtilizationLow
        expr: (sum(drivers_active) / sum(drivers_total)) < 0.3
        for: 10m
        labels:
          severity: info
        annotations:
          summary: "Low driver utilization"
          description: "Only {{ $value | humanizePercentage }} of drivers are active."

      - alert: PeakHourCapacityIssue
        expr: (sum(ride_requests_pending) > 100) and (hour() >= 7 and hour() <= 9 or hour() >= 17 and hour() <= 19)
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Capacity issues during peak hours"
          description: "{{ $value }} ride requests pending during peak hours."
