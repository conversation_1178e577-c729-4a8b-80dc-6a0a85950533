server:
  port: 8088

spring:
  application:
    name: notification-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:postgres}:${DB_PORT:5432}/tecnodrive_notifications
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10 # تقليل حجم المجمع لتحسين الأداء
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 10000 # تقليل timeout
      leak-detection-threshold: 30000 # تقليل threshold
      max-lifetime: 900000 # تقليل max-lifetime
      validation-timeout: 5000 # إضافة validation timeout

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  thymeleaf:
    cache: false
eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics

# Logging
logging:
  level:
    com.tecnodrive.notificationservice: DEBUG

# Notification Configuration
notification:
  channels:
    email:
      enabled: true
      from-address: ${MAIL_FROM:<EMAIL>}
      from-name: "TecnoDrive Platform"
    sms:
      enabled: true
      provider: twilio
      twilio:
        account-sid: ${TWILIO_ACCOUNT_SID:your-account-sid}
        auth-token: ${TWILIO_AUTH_TOKEN:your-auth-token}
        from-number: ${TWILIO_FROM_NUMBER:+**********}
    push:
      enabled: true
      firebase:
        credentials-path: ${FIREBASE_CREDENTIALS_PATH:firebase-credentials.json}
    in-app:
      enabled: true
      retention-days: 30

# Async Configuration
async:
  core-pool-size: 5
  max-pool-size: 10
  queue-capacity: 100
