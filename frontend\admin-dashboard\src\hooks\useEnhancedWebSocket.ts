import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

export interface WebSocketConfig {
  url: string;
  namespace?: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  maxReconnectionDelay?: number;
  timeout?: number;
  forceNew?: boolean;
}

export interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempts: number;
  lastConnected: Date | null;
  latency: number;
}

export interface WebSocketMessage {
  id: string;
  event: string;
  data: any;
  timestamp: Date;
  acknowledged: boolean;
}

export interface UseEnhancedWebSocketReturn {
  socket: Socket | null;
  state: WebSocketState;
  messages: WebSocketMessage[];
  subscribe: (event: string, callback: (data: any) => void) => () => void;
  emit: (event: string, data?: any) => Promise<any>;
  emitWithAck: (event: string, data?: any, timeout?: number) => Promise<any>;
  connect: () => void;
  disconnect: () => void;
  clearMessages: () => void;
  getMessageHistory: (event?: string) => WebSocketMessage[];
  isEventSubscribed: (event: string) => boolean;
}

const useEnhancedWebSocket = (config: WebSocketConfig): UseEnhancedWebSocketReturn => {
  const socketRef = useRef<Socket | null>(null);
  const subscriptionsRef = useRef<Map<string, Set<Function>>>(new Map());
  const messageHistoryRef = useRef<WebSocketMessage[]>([]);
  const latencyTimerRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [state, setState] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0,
    lastConnected: null,
    latency: 0,
  });

  const [messages, setMessages] = useState<WebSocketMessage[]>([]);

  const generateMessageId = () => {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  const addMessage = useCallback((event: string, data: any, acknowledged = false) => {
    const message: WebSocketMessage = {
      id: generateMessageId(),
      event,
      data,
      timestamp: new Date(),
      acknowledged,
    };

    messageHistoryRef.current = [message, ...messageHistoryRef.current.slice(0, 999)]; // Keep last 1000 messages
    setMessages(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 in state

    return message;
  }, []);

  const measureLatency = useCallback(() => {
    if (!socketRef.current?.connected) return;

    const startTime = Date.now();
    socketRef.current.emit('ping', startTime, (response: any) => {
      const latency = Date.now() - startTime;
      setState(prev => ({ ...prev, latency }));
    });
  }, []);

  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      measureLatency();
    }, 30000); // Every 30 seconds
  }, [measureLatency]);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  const setupEventHandlers = useCallback((socket: Socket) => {
    socket.on('connect', () => {
      setState(prev => ({
        ...prev,
        connected: true,
        connecting: false,
        error: null,
        reconnectAttempts: 0,
        lastConnected: new Date(),
      }));
      
      addMessage('system', { type: 'connected' }, true);
      startHeartbeat();
      measureLatency();
    });

    socket.on('disconnect', (reason: string) => {
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: reason,
      }));
      
      addMessage('system', { type: 'disconnected', reason }, true);
      stopHeartbeat();
    });

    socket.on('connect_error', (error: Error) => {
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: error.message,
      }));
      
      addMessage('system', { type: 'error', error: error.message }, true);
    });

    socket.on('reconnect_attempt', (attemptNumber: number) => {
      setState(prev => ({
        ...prev,
        connecting: true,
        reconnectAttempts: attemptNumber,
      }));
      
      addMessage('system', { type: 'reconnecting', attempt: attemptNumber }, true);
    });

    socket.on('reconnect', (attemptNumber: number) => {
      setState(prev => ({
        ...prev,
        connected: true,
        connecting: false,
        error: null,
        reconnectAttempts: 0,
      }));
      
      addMessage('system', { type: 'reconnected', attempts: attemptNumber }, true);
    });

    socket.on('reconnect_failed', () => {
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: 'Reconnection failed',
      }));
      
      addMessage('system', { type: 'reconnect_failed' }, true);
    });

    // Handle pong responses for latency measurement
    socket.on('pong', (startTime: number) => {
      const latency = Date.now() - startTime;
      setState(prev => ({ ...prev, latency }));
    });

    // Generic message handler for all events
    const originalOn = socket.on.bind(socket);
    socket.on = (event: string, listener: (...args: any[]) => void) => {
      const wrappedListener = (...args: any[]) => {
        // Add message to history
        addMessage(event, args.length === 1 ? args[0] : args);
        
        // Call original listener
        listener(...args);
      };
      
      return originalOn(event, wrappedListener);
    };
  }, [addMessage, startHeartbeat, stopHeartbeat, measureLatency]);

  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    setState(prev => ({ ...prev, connecting: true, error: null }));

    const socketUrl = config.namespace ? `${config.url}/${config.namespace}` : config.url;
    
    const socket = io(socketUrl, {
      autoConnect: config.autoConnect ?? true,
      reconnection: config.reconnection ?? true,
      reconnectionAttempts: config.reconnectionAttempts ?? 5,
      reconnectionDelay: config.reconnectionDelay ?? 1000,
      maxReconnectionDelay: config.maxReconnectionDelay ?? 5000,
      timeout: config.timeout ?? 20000,
      forceNew: config.forceNew ?? false,
      transports: ['websocket'],
    });

    setupEventHandlers(socket);
    socketRef.current = socket;

    if (config.autoConnect !== false) {
      socket.connect();
    }
  }, [config, setupEventHandlers]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    stopHeartbeat();
    
    setState(prev => ({
      ...prev,
      connected: false,
      connecting: false,
    }));
  }, [stopHeartbeat]);

  const subscribe = useCallback((event: string, callback: (data: any) => void) => {
    if (!subscriptionsRef.current.has(event)) {
      subscriptionsRef.current.set(event, new Set());
    }
    
    subscriptionsRef.current.get(event)!.add(callback);

    // Set up socket listener if this is the first subscription for this event
    if (subscriptionsRef.current.get(event)!.size === 1 && socketRef.current) {
      socketRef.current.on(event, (data: any) => {
        subscriptionsRef.current.get(event)?.forEach(cb => {
          try {
            cb(data);
          } catch (error) {
            console.error(`Error in WebSocket event handler for ${event}:`, error);
          }
        });
      });
    }

    // Return unsubscribe function
    return () => {
      const eventSubscriptions = subscriptionsRef.current.get(event);
      if (eventSubscriptions) {
        eventSubscriptions.delete(callback);
        
        // Remove socket listener if no more subscriptions
        if (eventSubscriptions.size === 0) {
          subscriptionsRef.current.delete(event);
          if (socketRef.current) {
            socketRef.current.off(event);
          }
        }
      }
    };
  }, []);

  const emit = useCallback((event: string, data?: any) => {
    return new Promise<any>((resolve, reject) => {
      if (!socketRef.current?.connected) {
        reject(new Error('Socket not connected'));
        return;
      }

      try {
        socketRef.current.emit(event, data);
        addMessage(`emit:${event}`, data, true);
        resolve(undefined);
      } catch (error) {
        reject(error);
      }
    });
  }, [addMessage]);

  const emitWithAck = useCallback((event: string, data?: any, timeout = 5000) => {
    return new Promise<any>((resolve, reject) => {
      if (!socketRef.current?.connected) {
        reject(new Error('Socket not connected'));
        return;
      }

      const timer = setTimeout(() => {
        reject(new Error(`Acknowledgment timeout for event: ${event}`));
      }, timeout);

      try {
        socketRef.current.emit(event, data, (response: any) => {
          clearTimeout(timer);
          addMessage(`emit:${event}`, data, true);
          resolve(response);
        });
      } catch (error) {
        clearTimeout(timer);
        reject(error);
      }
    });
  }, [addMessage]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    messageHistoryRef.current = [];
  }, []);

  const getMessageHistory = useCallback((event?: string) => {
    if (!event) return messageHistoryRef.current;
    return messageHistoryRef.current.filter(msg => msg.event === event);
  }, []);

  const isEventSubscribed = useCallback((event: string) => {
    return subscriptionsRef.current.has(event) && 
           subscriptionsRef.current.get(event)!.size > 0;
  }, []);

  // Initialize connection
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopHeartbeat();
      if (latencyTimerRef.current) {
        clearTimeout(latencyTimerRef.current);
      }
    };
  }, [stopHeartbeat]);

  return {
    socket: socketRef.current,
    state,
    messages,
    subscribe,
    emit,
    emitWithAck,
    connect,
    disconnect,
    clearMessages,
    getMessageHistory,
    isEventSubscribed,
  };
};

export default useEnhancedWebSocket;
