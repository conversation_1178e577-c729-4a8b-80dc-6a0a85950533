#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Health Monitoring Script
# This script continuously monitors the health of all services

param(
    [int]$IntervalSeconds = 30,
    [switch]$Continuous = $false,
    [switch]$Detailed = $false
)

# Service definitions
$services = @(
    @{Name="PostgreSQL"; Url=""; Port=5432; Type="Database"; Container="infra-postgres-1"},
    @{Name="Redis"; Url=""; Port=6379; Type="Cache"; Container="infra-redis-1"},
    @{Name="Eureka Server"; Url="http://localhost:8761/actuator/health"; Port=8761; Type="Discovery"; Container="infra-eureka-server-1"},
    @{Name="Auth Service"; Url="http://localhost:8081/actuator/health"; Port=8081; Type="Business"; Container="infra-auth-service-1"},
    @{Name="Ride Service"; Url="http://localhost:8082/actuator/health"; Port=8082; Type="Business"; Container="infra-ride-service-1"},
    @{Name="Fleet Service"; Url="http://localhost:8083/actuator/health"; Port=8083; Type="Business"; Container="infra-fleet-service-1"},
    @{Name="Parcel Service"; Url="http://localhost:8084/actuator/health"; Port=8084; Type="Business"; Container="infra-parcel-service-1"},
    @{Name="Payment Service"; Url="http://localhost:8085/actuator/health"; Port=8085; Type="Business"; Container="infra-payment-service-1"},
    @{Name="Location Service"; Url="http://localhost:8086/actuator/health"; Port=8086; Type="Business"; Container="infra-location-service-1"},
    @{Name="Notification Service"; Url="http://localhost:8087/actuator/health"; Port=8087; Type="Support"; Container="infra-notification-service-1"},
    @{Name="Financial Service"; Url="http://localhost:8088/actuator/health"; Port=8088; Type="Support"; Container="infra-financial-service-1"},
    @{Name="HR Service"; Url="http://localhost:8089/actuator/health"; Port=8089; Type="Support"; Container="infra-hr-service-1"},
    @{Name="Analytics Service"; Url="http://localhost:8090/actuator/health"; Port=8090; Type="Analytics"; Container="infra-analytics-service-1"},
    @{Name="SaaS Management"; Url="http://localhost:8091/actuator/health"; Port=8091; Type="Management"; Container="infra-saas-management-service-1"},
    @{Name="API Gateway"; Url="http://localhost:8080/actuator/health"; Port=8080; Type="Gateway"; Container="infra-api-gateway-1"}
)

function Test-ContainerHealth {
    param($Container)
    
    try {
        $status = docker inspect $Container --format='{{.State.Status}}' 2>$null
        return $status -eq "running"
    }
    catch {
        return $false
    }
}

function Test-ServiceEndpoint {
    param($Url)
    
    if ([string]::IsNullOrEmpty($Url)) {
        return $null
    }
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Get-ServiceStatus {
    param($Service)
    
    $containerRunning = Test-ContainerHealth $Service.Container
    $endpointHealthy = Test-ServiceEndpoint $Service.Url
    
    if (-not $containerRunning) {
        return @{Status="STOPPED"; Color="Red"; Symbol="⏹️"}
    }
    elseif ($endpointHealthy -eq $true) {
        return @{Status="HEALTHY"; Color="Green"; Symbol="✅"}
    }
    elseif ($endpointHealthy -eq $false) {
        return @{Status="UNHEALTHY"; Color="Yellow"; Symbol="⚠️"}
    }
    else {
        return @{Status="RUNNING"; Color="Cyan"; Symbol="🔄"}
    }
}

function Show-HealthReport {
    param($ShowDetailed = $false)
    
    Clear-Host
    Write-Host "🏥 TECNO DRIVE Platform - Health Monitor" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Cyan
    Write-Host "📅 $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host ""
    
    $statusCounts = @{
        HEALTHY = 0
        UNHEALTHY = 0
        RUNNING = 0
        STOPPED = 0
    }
    
    # Group services by type
    $serviceGroups = $services | Group-Object Type
    
    foreach ($group in $serviceGroups) {
        Write-Host "📂 $($group.Name) Services:" -ForegroundColor Magenta
        Write-Host "─────────────────────────" -ForegroundColor Gray
        
        foreach ($service in $group.Group) {
            $status = Get-ServiceStatus $service
            $statusCounts[$status.Status]++
            
            $statusText = "$($status.Symbol) $($service.Name)"
            $portText = if ($service.Port) { " (Port $($service.Port))" } else { "" }
            $containerText = if ($ShowDetailed) { " [$($service.Container)]" } else { "" }
            
            Write-Host "  $statusText$portText$containerText" -ForegroundColor $status.Color
            
            if ($ShowDetailed -and $status.Status -eq "UNHEALTHY") {
                try {
                    $logs = docker logs $service.Container --tail 3 2>$null
                    if ($logs) {
                        Write-Host "    Last logs: $($logs[-1])" -ForegroundColor Gray
                    }
                }
                catch {
                    # Ignore log errors
                }
            }
        }
        Write-Host ""
    }
    
    # Summary
    $total = $services.Count
    $healthy = $statusCounts.HEALTHY
    $running = $statusCounts.RUNNING
    $unhealthy = $statusCounts.UNHEALTHY
    $stopped = $statusCounts.STOPPED
    
    Write-Host "📊 Summary:" -ForegroundColor Cyan
    Write-Host "─────────" -ForegroundColor Gray
    Write-Host "  ✅ Healthy: $healthy/$total" -ForegroundColor Green
    Write-Host "  🔄 Running: $running/$total" -ForegroundColor Cyan
    Write-Host "  ⚠️ Unhealthy: $unhealthy/$total" -ForegroundColor Yellow
    Write-Host "  ⏹️ Stopped: $stopped/$total" -ForegroundColor Red
    
    $healthPercentage = [math]::Round(($healthy / $total) * 100, 1)
    Write-Host "  🎯 Health Score: $healthPercentage%" -ForegroundColor White
    
    if ($healthPercentage -ge 90) {
        Write-Host "  🎉 Excellent! Platform is running smoothly." -ForegroundColor Green
    }
    elseif ($healthPercentage -ge 70) {
        Write-Host "  👍 Good! Most services are operational." -ForegroundColor Yellow
    }
    elseif ($healthPercentage -ge 50) {
        Write-Host "  ⚠️ Warning! Several services need attention." -ForegroundColor Yellow
    }
    else {
        Write-Host "  🚨 Critical! Platform needs immediate attention." -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "💡 Commands:" -ForegroundColor Blue
    Write-Host "  • View logs: docker-compose logs [service-name]" -ForegroundColor Gray
    Write-Host "  • Restart service: docker-compose restart [service-name]" -ForegroundColor Gray
    Write-Host "  • Check containers: docker-compose ps" -ForegroundColor Gray
}

# Main execution
if ($Continuous) {
    Write-Host "🔄 Starting continuous monitoring (Interval: $IntervalSeconds seconds)" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop..." -ForegroundColor Yellow
    Write-Host ""
    
    while ($true) {
        Show-HealthReport -ShowDetailed $Detailed
        Start-Sleep -Seconds $IntervalSeconds
    }
}
else {
    Show-HealthReport -ShowDetailed $Detailed
}
