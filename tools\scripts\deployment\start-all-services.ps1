# Start All TecnoDrive Services
Write-Host "🚀 Starting TecnoDrive Platform - All Services" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Gray

# Check Docker
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Start infrastructure first
Write-Host "`n📦 Starting Infrastructure Services..." -ForegroundColor Cyan
docker compose -f docker-compose.all-services.yml up -d postgres redis

Write-Host "⏳ Waiting for infrastructure..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Start Eureka Server
Write-Host "`n🔧 Starting Service Discovery..." -ForegroundColor Cyan
docker compose -f docker-compose.all-services.yml up -d eureka-server

Write-Host "⏳ Waiting for Eureka..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Start API Gateway
Write-Host "`n🌐 Starting API Gateway..." -ForegroundColor Cyan
docker compose -f docker-compose.all-services.yml up -d api-gateway

Write-Host "⏳ Waiting for API Gateway..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Start all microservices
Write-Host "`n🎯 Starting All Microservices..." -ForegroundColor Cyan
$services = @(
    "auth-service",
    "ride-service", 
    "user-service",
    "fleet-service",
    "location-service",
    "payment-service",
    "parcel-service",
    "notification-service",
    "analytics-service",
    "hr-service",
    "financial-service",
    "saas-management-service"
)

foreach ($service in $services) {
    Write-Host "  🔄 Starting $service..." -ForegroundColor Yellow
    docker compose -f docker-compose.all-services.yml up -d $service
    Start-Sleep -Seconds 3
}

# Wait for services to start
Write-Host "`n⏳ Waiting for all services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Check status
Write-Host "`n📊 Service Status:" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "tecno" }

# Service URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • Eureka Dashboard:    http://localhost:8761" -ForegroundColor White
Write-Host "  • API Gateway:         http://localhost:8080" -ForegroundColor White
Write-Host "  • Auth Service:        http://localhost:8081" -ForegroundColor White
Write-Host "  • Ride Service:        http://localhost:8082" -ForegroundColor White
Write-Host "  • User Service:        http://localhost:8083" -ForegroundColor White
Write-Host "  • Fleet Service:       http://localhost:8084" -ForegroundColor White
Write-Host "  • Location Service:    http://localhost:8085" -ForegroundColor White
Write-Host "  • Payment Service:     http://localhost:8086" -ForegroundColor White
Write-Host "  • Parcel Service:      http://localhost:8087" -ForegroundColor White
Write-Host "  • Notification Service: http://localhost:8088" -ForegroundColor White
Write-Host "  • Analytics Service:   http://localhost:8089" -ForegroundColor White
Write-Host "  • HR Service:          http://localhost:8090" -ForegroundColor White
Write-Host "  • Financial Service:   http://localhost:8091" -ForegroundColor White
Write-Host "  • SaaS Management:     http://localhost:8092" -ForegroundColor White

Write-Host "`n🎉 All services started!" -ForegroundColor Green
Write-Host "💡 Use 'docker ps' to check service status" -ForegroundColor Cyan
Write-Host "💡 Use 'docker logs [service-name]' to check logs" -ForegroundColor Cyan
