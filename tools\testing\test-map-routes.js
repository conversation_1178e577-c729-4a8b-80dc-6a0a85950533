// Test Map Routes - Copy and paste in browser console
console.log('🗺️ Testing Map Routes Navigation');
console.log('=================================');

// Function to test navigation to map routes
function testMapRoutes() {
    console.log('🧪 Starting Map Routes Test...');
    
    // Test different map routes
    const mapRoutes = [
        '/map',
        '/map/simple', 
        '/map/test',
        '/map/real',
        '/map/street'
    ];
    
    console.log('📋 Available Map Routes:');
    mapRoutes.forEach((route, index) => {
        console.log(`${index + 1}. ${route}`);
    });
    
    // Function to navigate to a specific route
    window.testNavigateToMap = function(routeIndex) {
        const route = mapRoutes[routeIndex - 1];
        if (route) {
            console.log(`🔄 Navigating to: ${route}`);
            window.location.href = route;
        } else {
            console.log('❌ Invalid route index. Use 1-5.');
        }
    };
    
    // Function to check current authentication status
    window.checkAuthStatus = function() {
        console.log('🔍 Checking Authentication Status...');
        
        const authToken = localStorage.getItem('tecnodrive_auth_token') || localStorage.getItem('authToken');
        const userData = localStorage.getItem('tecnodrive_user_data') || localStorage.getItem('user');
        const rememberMe = localStorage.getItem('tecnodrive_remember_me');
        const autoLogin = localStorage.getItem('tecnodrive_auto_login');
        
        console.log('📊 Auth Status:');
        console.log('- Token exists:', !!authToken);
        console.log('- User data exists:', !!userData);
        console.log('- Remember me:', rememberMe === 'true');
        console.log('- Auto login:', autoLogin === 'true');
        
        if (authToken && userData) {
            try {
                const user = JSON.parse(userData);
                console.log('👤 Current User:', user.email, `(${user.role})`);
                
                // Check token expiration
                const expiresAt = localStorage.getItem('tecnodrive_expires_at');
                if (expiresAt) {
                    const expiration = new Date(parseInt(expiresAt));
                    const now = new Date();
                    const isExpired = now > expiration;
                    
                    console.log('⏰ Token expires:', expiration.toLocaleString());
                    console.log('🔍 Token status:', isExpired ? 'EXPIRED' : 'VALID');
                    
                    if (isExpired) {
                        console.log('⚠️ Token is expired, you may need to login again');
                    }
                } else {
                    console.log('⚠️ No expiration time found');
                }
                
                return true;
            } catch (error) {
                console.log('❌ Error parsing user data:', error);
                return false;
            }
        } else {
            console.log('❌ Not authenticated');
            return false;
        }
    };
    
    // Function to perform quick login for testing
    window.quickLoginForMaps = function() {
        console.log('⚡ Performing quick login for map testing...');
        
        const userData = {
            id: '1',
            email: '<EMAIL>',
            name: 'Azal Mohamed',
            role: 'ADMIN',
            permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
        };
        
        const now = Date.now();
        const expiresIn = 24 * 60 * 60 * 1000; // 24 hours
        
        const token = btoa(JSON.stringify({
            ...userData,
            iat: now,
            exp: now + expiresIn,
            rememberMe: false
        }));
        
        // Store auth data
        localStorage.setItem('tecnodrive_auth_token', token);
        localStorage.setItem('tecnodrive_user_data', JSON.stringify(userData));
        localStorage.setItem('tecnodrive_remember_me', 'false');
        localStorage.setItem('tecnodrive_login_time', now.toString());
        localStorage.setItem('tecnodrive_expires_at', (now + expiresIn).toString());
        
        console.log('✅ Quick login completed!');
        console.log('👤 Logged in as:', userData.email);
        console.log('⏰ Valid for 24 hours');
        
        return true;
    };
    
    // Function to clear auth data
    window.clearAuthForTesting = function() {
        console.log('🧹 Clearing auth data for testing...');
        
        const keysToRemove = [
            'tecnodrive_auth_token', 'tecnodrive_user_data', 'tecnodrive_remember_me',
            'tecnodrive_login_time', 'tecnodrive_expires_at', 'tecnodrive_auto_login',
            'authToken', 'token', 'user', 'currentUser', 'isAuthenticated'
        ];
        
        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
        
        console.log('✅ Auth data cleared');
    };
    
    // Check current authentication status
    const isAuthenticated = checkAuthStatus();
    
    console.log('\n💡 Available Test Functions:');
    console.log('- testNavigateToMap(1-5) - Navigate to map route');
    console.log('- checkAuthStatus() - Check authentication');
    console.log('- quickLoginForMaps() - Quick login for testing');
    console.log('- clearAuthForTesting() - Clear auth data');
    
    console.log('\n📋 Quick Test Steps:');
    if (!isAuthenticated) {
        console.log('1. Run: quickLoginForMaps()');
        console.log('2. Run: testNavigateToMap(1) for /map');
        console.log('3. Run: testNavigateToMap(3) for /map/test');
    } else {
        console.log('✅ Already authenticated!');
        console.log('1. Run: testNavigateToMap(1) for /map');
        console.log('2. Run: testNavigateToMap(3) for /map/test');
        console.log('3. Run: testNavigateToMap(4) for /map/real');
    }
    
    return {
        authenticated: isAuthenticated,
        routes: mapRoutes,
        functions: ['testNavigateToMap', 'checkAuthStatus', 'quickLoginForMaps', 'clearAuthForTesting']
    };
}

// Auto-execute
const testResult = testMapRoutes();

console.log('\n🎯 Test Result:', testResult);

// Auto-login if not authenticated
if (!testResult.authenticated) {
    console.log('\n⚡ Auto-executing quick login...');
    setTimeout(() => {
        quickLoginForMaps();
        console.log('✅ Ready to test map routes!');
        console.log('🔗 Try: testNavigateToMap(1)');
    }, 2000);
}
