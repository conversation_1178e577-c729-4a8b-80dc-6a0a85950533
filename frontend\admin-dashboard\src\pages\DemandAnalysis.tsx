import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  CircularProgress,
  Tabs,
  Tab,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon,
  Map as MapIcon,
  Timeline as TimelineIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Whatshot as HotIcon,
  AcUnit as ColdIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';

interface DemandZone {
  id: string;
  name: string;
  district: string;
  demandLevel: string;
  currentDemand: number;
  averageDemand: number;
  peakDemand: number;
  lowDemand: number;
  population: number;
  area: number;
  density: number;
  tripCount24h: number;
  averageWaitTime: number;
  supplyDemandRatio: number;
  priceMultiplier: number;
  economicLevel: string;
}

interface DemandSummary {
  totalZones: number;
  highDemandZones: number;
  mediumDemandZones: number;
  lowDemandZones: number;
  averageDemand: number;
  totalTrips24h: number;
  averageWaitTime: number;
  overallSupplyDemandRatio: number;
  zonesNeedingAttention: number;
}

interface DemandForecast {
  timestamp: string;
  hour: number;
  predictedDemand: number;
  confidence: number;
  recommendedSupply: number;
  priceRecommendation: number;
  alertLevel: string;
}

interface PeakHour {
  day: string;
  startHour: number;
  endHour: number;
  peakType: string;
  averageDemand: number;
  demandIncrease: number;
  description: string;
}

const DemandAnalysis: React.FC = () => {
  const [zones, setZones] = useState<DemandZone[]>([]);
  const [summary, setSummary] = useState<DemandSummary | null>(null);
  const [forecast, setForecast] = useState<DemandForecast[]>([]);
  const [peakHours, setPeakHours] = useState<PeakHour[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [filters, setFilters] = useState({
    demandLevel: '',
    minDemand: '',
    maxDemand: ''
  });

  useEffect(() => {
    loadDemandData();
  }, [filters]);

  const loadDemandData = async () => {
    try {
      setLoading(true);

      // Load demand zones
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const zonesResponse = await fetch(`http://localhost:8103/api/demand/zones?${queryParams}`);
      const zonesData = await zonesResponse.json();
      
      if (zonesData.success) {
        setZones(zonesData.data);
      }

      // Load summary
      const summaryResponse = await fetch('http://localhost:8103/api/demand/summary');
      const summaryData = await summaryResponse.json();
      
      if (summaryData.success) {
        setSummary(summaryData.data);
      }

      // Load forecast
      const forecastResponse = await fetch('http://localhost:8103/api/demand/forecast?hours=24');
      const forecastData = await forecastResponse.json();
      
      if (forecastData.success) {
        setForecast(forecastData.data);
      }

      // Load peak hours
      const peakResponse = await fetch('http://localhost:8103/api/demand/peak-hours');
      const peakData = await peakResponse.json();
      
      if (peakData.success) {
        setPeakHours(peakData.data);
      }

    } catch (error) {
      console.error('Failed to load demand data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getDemandLevelColor = (level: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'high': 'error',
      'medium': 'warning',
      'low': 'success',
      'variable': 'info'
    };
    return colors[level] || 'default';
  };

  const getDemandLevelLabel = (level: string) => {
    const labels: Record<string, string> = {
      'high': 'مرتفع',
      'medium': 'متوسط',
      'low': 'منخفض',
      'variable': 'متغير'
    };
    return labels[level] || level;
  };

  const getAlertLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'error';
      case 'low': return 'info';
      default: return 'success';
    }
  };

  const getSupplyDemandStatus = (ratio: number) => {
    if (ratio < 0.8) return { label: 'نقص في العرض', color: 'error' };
    if (ratio > 1.2) return { label: 'فائض في العرض', color: 'warning' };
    return { label: 'متوازن', color: 'success' };
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل بيانات تحليل الطلب...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          تحليل الطلب والخرائط الحرارية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          تحليل شامل لأنماط الطلب والتنبؤ بالاحتياجات المستقبلية
        </Typography>
      </Box>

      {/* Summary Cards */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <LocationIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{summary.totalZones}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المناطق
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      مرتفع الطلب: {summary.highDemandZones}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatNumber(summary.averageDemand)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط الطلب
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      رحلات 24س: {formatNumber(summary.totalTrips24h)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <ScheduleIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{summary.averageWaitTime.toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط الانتظار (دقيقة)
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      نسبة العرض/الطلب: {summary.overallSupplyDemandRatio.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{summary.zonesNeedingAttention}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      مناطق تحتاج انتباه
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      نقص في العرض
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>مستوى الطلب</InputLabel>
              <Select
                value={filters.demandLevel}
                onChange={(e) => handleFilterChange('demandLevel', e.target.value)}
                label="مستوى الطلب"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="high">مرتفع</MenuItem>
                <MenuItem value="medium">متوسط</MenuItem>
                <MenuItem value="low">منخفض</MenuItem>
                <MenuItem value="variable">متغير</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadDemandData}
            >
              تحديث البيانات
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="المناطق" icon={<LocationIcon />} />
          <Tab label="التنبؤات" icon={<TimelineIcon />} />
          <Tab label="الساعات الذروة" icon={<ScheduleIcon />} />
          <Tab label="الخريطة الحرارية" icon={<MapIcon />} />
        </Tabs>
      </Paper>

      {/* Zones Tab */}
      {selectedTab === 0 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>المنطقة</TableCell>
                  <TableCell>مستوى الطلب</TableCell>
                  <TableCell>الطلب الحالي</TableCell>
                  <TableCell>متوسط الطلب</TableCell>
                  <TableCell>الرحلات (24س)</TableCell>
                  <TableCell>وقت الانتظار</TableCell>
                  <TableCell>نسبة العرض/الطلب</TableCell>
                  <TableCell>الكثافة السكانية</TableCell>
                  <TableCell>الحالة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {zones.slice(0, 15).map((zone) => {
                  const supplyStatus = getSupplyDemandStatus(zone.supplyDemandRatio);
                  
                  return (
                    <TableRow key={zone.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {zone.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {zone.district}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getDemandLevelLabel(zone.demandLevel)}
                          color={getDemandLevelColor(zone.demandLevel)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: 100 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={Math.min(zone.currentDemand, 100)}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                          <Typography variant="caption">
                            {zone.currentDemand.toFixed(0)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {zone.averageDemand.toFixed(0)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {formatNumber(zone.tripCount24h)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color={zone.averageWaitTime > 10 ? 'error.main' : 'text.primary'}>
                          {zone.averageWaitTime.toFixed(1)} دقيقة
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {zone.supplyDemandRatio.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatNumber(zone.density)} نسمة/كم²
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={supplyStatus.label}
                          color={supplyStatus.color as any}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Forecast Tab */}
      {selectedTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توقعات الطلب للـ 24 ساعة القادمة
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    رسم بياني لتوقعات الطلب (يتطلب مكتبة رسوم بيانية)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تنبيهات التوقعات
                </Typography>
                <List>
                  {forecast.filter(f => f.alertLevel !== 'normal').slice(0, 5).map((item, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {item.alertLevel === 'high' ? 
                          <HotIcon color="error" /> : 
                          <ColdIcon color="info" />
                        }
                      </ListItemIcon>
                      <ListItemText
                        primary={`الساعة ${item.hour}:00`}
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              طلب متوقع: {item.predictedDemand}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ثقة: {item.confidence.toFixed(1)}%
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Peak Hours Tab */}
      {selectedTab === 2 && (
        <Grid container spacing={3}>
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day, dayIndex) => {
            const dayPeaks = peakHours.filter(p => p.day === day);
            
            return (
              <Grid item xs={12} md={6} lg={4} key={day}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {day === 'Sunday' ? 'الأحد' :
                       day === 'Monday' ? 'الاثنين' :
                       day === 'Tuesday' ? 'الثلاثاء' :
                       day === 'Wednesday' ? 'الأربعاء' :
                       day === 'Thursday' ? 'الخميس' :
                       day === 'Friday' ? 'الجمعة' : 'السبت'}
                    </Typography>
                    
                    {dayPeaks.map((peak, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="body2" fontWeight="bold">
                            {peak.startHour}:00 - {peak.endHour}:00
                          </Typography>
                          <Chip
                            label={`+${peak.demandIncrease.toFixed(0)}%`}
                            color="warning"
                            size="small"
                          />
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {peak.description}
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={Math.min(peak.averageDemand / 2, 100)}
                          sx={{ height: 6, borderRadius: 3, mt: 1 }}
                        />
                        {index < dayPeaks.length - 1 && <Divider sx={{ mt: 2 }} />}
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Heatmap Tab */}
      {selectedTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              الخريطة الحرارية للطلب
            </Typography>
            <Box 
              sx={{ 
                height: 500, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: 'grey.100',
                borderRadius: 1
              }}
            >
              <Typography variant="h6" color="text.secondary">
                خريطة حرارية تفاعلية لتوزيع الطلب
                <br />
                (يتطلب تكامل مع خدمة الخرائط)
              </Typography>
            </Box>
            
            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom>
                مفتاح الألوان:
              </Typography>
              <Box display="flex" gap={2} flexWrap="wrap">
                <Box display="flex" alignItems="center">
                  <Box sx={{ width: 20, height: 20, backgroundColor: '#f44336', mr: 1, borderRadius: 1 }} />
                  <Typography variant="caption">طلب مرتفع جداً</Typography>
                </Box>
                <Box display="flex" alignItems="center">
                  <Box sx={{ width: 20, height: 20, backgroundColor: '#ff9800', mr: 1, borderRadius: 1 }} />
                  <Typography variant="caption">طلب مرتفع</Typography>
                </Box>
                <Box display="flex" alignItems="center">
                  <Box sx={{ width: 20, height: 20, backgroundColor: '#ffeb3b', mr: 1, borderRadius: 1 }} />
                  <Typography variant="caption">طلب متوسط</Typography>
                </Box>
                <Box display="flex" alignItems="center">
                  <Box sx={{ width: 20, height: 20, backgroundColor: '#4caf50', mr: 1, borderRadius: 1 }} />
                  <Typography variant="caption">طلب منخفض</Typography>
                </Box>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Container>
  );
};

export default DemandAnalysis;
