# =============================================================================
# TECNO DRIVE API Gateway - Development Docker Compose
# =============================================================================

version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        - BUILD_DATE=${BUILD_DATE:-}
        - VCS_REF=${VCS_REF:-}
        - VERSION=${VERSION:-latest}
    image: tecno-drive/api-gateway:dev
    container_name: tecno-drive-api-gateway
    ports:
      - "8080:8080"
      - "8081:8081"  # Management port
    environment:
      # Spring profiles
      - SPRING_PROFILES_ACTIVE=dev,docker
      
      # Database configuration
      - SPRING_DATASOURCE_URL=******************************************
      - SPRING_DATASOURCE_USERNAME=tecnodrive
      - SPRING_DATASOURCE_PASSWORD=tecnodrive123
      
      # Redis configuration
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=redis123
      
      # Eureka configuration
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka/
      
      # Management endpoints
      - MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=*
      - MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always
      - MANAGEMENT_SERVER_PORT=8081
      
      # Logging
      - LOGGING_LEVEL_COM_TECNODRIVE=DEBUG
      - LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_CLOUD_GATEWAY=DEBUG
      
      # JVM options for development
      - JAVA_OPTS=-server 
        -XX:+UseG1GC 
        -XX:MaxRAMPercentage=50.0 
        -XX:+UseContainerSupport 
        -Djava.security.egd=file:/dev/./urandom
        -Dspring.devtools.restart.enabled=true
        -Dspring.devtools.livereload.enabled=true
    volumes:
      # Mount source code for hot reload (development only)
      - ./src:/app/src:ro
      - ./target:/app/target
      # Logs volume
      - api-gateway-logs:/app/logs
    networks:
      - tecno-drive-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      eureka:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-gateway.rule=Host(`api.tecnodrive.local`)"
      - "traefik.http.services.api-gateway.loadbalancer.server.port=8080"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tecno-drive-postgres
    environment:
      - POSTGRES_DB=tecnodrive
      - POSTGRES_USER=tecnodrive
      - POSTGRES_PASSWORD=tecnodrive123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - tecno-drive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tecnodrive -d tecnodrive"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tecno-drive-redis
    command: redis-server --requirepass redis123 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - tecno-drive-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Eureka Service Discovery
  eureka:
    image: steeltoeoss/eureka-server:latest
    container_name: tecno-drive-eureka
    ports:
      - "8761:8761"
    environment:
      - EUREKA_CLIENT_REGISTER_WITH_EUREKA=false
      - EUREKA_CLIENT_FETCH_REGISTRY=false
    networks:
      - tecno-drive-network
    restart: unless-stopped

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: tecno-drive-zipkin
    ports:
      - "9411:9411"
    environment:
      - STORAGE_TYPE=mem
    networks:
      - tecno-drive-network
    restart: unless-stopped

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: tecno-drive-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tecno-drive-network
    restart: unless-stopped

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: tecno-drive-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - tecno-drive-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Traefik Reverse Proxy (for development)
  traefik:
    image: traefik:v3.0
    container_name: tecno-drive-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - tecno-drive-network
    restart: unless-stopped

# Networks
networks:
  tecno-drive-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  api-gateway-logs:
    driver: local
