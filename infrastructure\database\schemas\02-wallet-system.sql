-- =====================================================
-- TecnoDrive Platform - Digital Wallet System Schema
-- =====================================================

-- =====================================================
-- 1. CUSTOMERS TABLE (Enhanced for wallet integration)
-- =====================================================
CREATE TABLE customers (
    customer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Yemen',
    customer_type VARCHAR(50) DEFAULT 'individual' CHECK (customer_type IN ('individual', 'business', 'corporate')),
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    kyc_status VARCHAR(50) DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected')),
    kyc_documents JSONB DEFAULT '{}',
    preferred_language VARCHAR(10) DEFAULT 'ar',
    notification_preferences JSONB DEFAULT '{"sms": true, "email": true, "push": true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, phone_number)
);

-- =====================================================
-- 2. WALLETS TABLE
-- =====================================================
CREATE TABLE wallets (
    wallet_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    wallet_number VARCHAR(50) NOT NULL UNIQUE,
    balance DECIMAL(15,2) DEFAULT 0.00 CHECK (balance >= 0),
    currency VARCHAR(10) DEFAULT 'YER',
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'frozen', 'closed')),
    daily_limit DECIMAL(15,2) DEFAULT 10000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 100000.00,
    pin_hash VARCHAR(255),
    pin_attempts INTEGER DEFAULT 0,
    pin_locked_until TIMESTAMP WITH TIME ZONE,
    last_transaction_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, customer_id)
);

-- =====================================================
-- 3. WALLET TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE wallet_transactions (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    wallet_id UUID NOT NULL REFERENCES wallets(wallet_id) ON DELETE CASCADE,
    transaction_number VARCHAR(50) NOT NULL UNIQUE,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('top_up', 'payment', 'refund', 'adjustment', 'transfer_in', 'transfer_out', 'cashback', 'fee')),
    amount DECIMAL(15,2) NOT NULL,
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    net_amount DECIMAL(15,2) GENERATED ALWAYS AS (
        CASE 
            WHEN transaction_type IN ('top_up', 'refund', 'adjustment', 'transfer_in', 'cashback') THEN amount - fee_amount
            ELSE -(amount + fee_amount)
        END
    ) STORED,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'YER',
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'reversed')),
    payment_method VARCHAR(100),
    external_transaction_id VARCHAR(255),
    external_reference VARCHAR(255),
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. PAYMENT METHODS TABLE
-- =====================================================
CREATE TABLE payment_methods (
    method_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    method_code VARCHAR(50) NOT NULL,
    method_name VARCHAR(100) NOT NULL,
    method_type VARCHAR(50) CHECK (method_type IN ('cash', 'mobile_wallet', 'bank_card', 'bank_transfer', 'crypto')),
    provider_name VARCHAR(100),
    provider_config JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(15,2) DEFAULT 1.00,
    max_amount DECIMAL(15,2),
    fee_type VARCHAR(20) DEFAULT 'fixed' CHECK (fee_type IN ('fixed', 'percentage', 'tiered')),
    fee_value DECIMAL(10,4) DEFAULT 0.00,
    processing_time_minutes INTEGER DEFAULT 0,
    supported_currencies TEXT[] DEFAULT ARRAY['YER'],
    icon_url VARCHAR(500),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, method_code)
);

-- Insert default payment methods for Yemen
INSERT INTO payment_methods (tenant_id, method_code, method_name, method_type, provider_name, min_amount, max_amount, fee_type, fee_value, icon_url) 
SELECT 
    t.tenant_id,
    unnest(ARRAY['cash', 'jawaly', 'onecash', 'jaib_wallet', 'fulusak_wallet', 'bank_card', 'bank_transfer']),
    unnest(ARRAY['نقداً', 'جوالي', 'ون كاش', 'محفظة جيب', 'محفظة فلوسك', 'بطاقة بنكية', 'تحويل بنكي']),
    unnest(ARRAY['cash', 'mobile_wallet', 'mobile_wallet', 'mobile_wallet', 'mobile_wallet', 'bank_card', 'bank_transfer']),
    unnest(ARRAY['Cash', 'Jawaly', 'OneCash', 'Jaib', 'Fulusak', 'Bank', 'Bank']),
    unnest(ARRAY[1.00, 10.00, 10.00, 10.00, 10.00, 100.00, 1000.00]),
    unnest(ARRAY[1000000.00, 50000.00, 50000.00, 30000.00, 30000.00, 200000.00, 500000.00]),
    unnest(ARRAY['fixed', 'percentage', 'percentage', 'percentage', 'percentage', 'percentage', 'fixed']),
    unnest(ARRAY[0.00, 1.5, 2.0, 1.0, 1.5, 2.5, 50.00]),
    unnest(ARRAY['/icons/cash.png', '/icons/jawaly.png', '/icons/onecash.png', '/icons/jaib.png', '/icons/fulusak.png', '/icons/card.png', '/icons/bank.png'])
FROM tenants t;

-- =====================================================
-- 5. WALLET LIMITS TABLE
-- =====================================================
CREATE TABLE wallet_limits (
    limit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    wallet_id UUID NOT NULL REFERENCES wallets(wallet_id) ON DELETE CASCADE,
    limit_type VARCHAR(50) NOT NULL CHECK (limit_type IN ('daily', 'weekly', 'monthly', 'yearly', 'per_transaction')),
    limit_category VARCHAR(50) NOT NULL CHECK (limit_category IN ('top_up', 'payment', 'transfer', 'withdrawal', 'total')),
    limit_amount DECIMAL(15,2) NOT NULL,
    used_amount DECIMAL(15,2) DEFAULT 0.00,
    reset_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, wallet_id, limit_type, limit_category)
);

-- =====================================================
-- 6. LOYALTY POINTS TABLE
-- =====================================================
CREATE TABLE loyalty_points (
    point_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    points_earned INTEGER DEFAULT 0,
    points_redeemed INTEGER DEFAULT 0,
    points_balance INTEGER GENERATED ALWAYS AS (points_earned - points_redeemed) STORED,
    transaction_type VARCHAR(50) CHECK (transaction_type IN ('ride', 'parcel', 'referral', 'bonus', 'redemption')),
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    expiry_date DATE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Customers indexes
CREATE INDEX idx_customers_tenant ON customers(tenant_id);
CREATE INDEX idx_customers_phone ON customers(tenant_id, phone_number);
CREATE INDEX idx_customers_email ON customers(tenant_id, email);
CREATE INDEX idx_customers_active ON customers(tenant_id, is_active);

-- Wallets indexes
CREATE INDEX idx_wallets_tenant ON wallets(tenant_id);
CREATE INDEX idx_wallets_customer ON wallets(customer_id);
CREATE INDEX idx_wallets_status ON wallets(tenant_id, status);
CREATE INDEX idx_wallets_number ON wallets(wallet_number);

-- Wallet transactions indexes
CREATE INDEX idx_wallet_transactions_tenant ON wallet_transactions(tenant_id);
CREATE INDEX idx_wallet_transactions_wallet ON wallet_transactions(wallet_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX idx_wallet_transactions_status ON wallet_transactions(status);
CREATE INDEX idx_wallet_transactions_date ON wallet_transactions(created_at);
CREATE INDEX idx_wallet_transactions_number ON wallet_transactions(transaction_number);
CREATE INDEX idx_wallet_transactions_external ON wallet_transactions(external_transaction_id);

-- Payment methods indexes
CREATE INDEX idx_payment_methods_tenant ON payment_methods(tenant_id);
CREATE INDEX idx_payment_methods_active ON payment_methods(tenant_id, is_active);
CREATE INDEX idx_payment_methods_type ON payment_methods(method_type);

-- Loyalty points indexes
CREATE INDEX idx_loyalty_points_tenant ON loyalty_points(tenant_id);
CREATE INDEX idx_loyalty_points_customer ON loyalty_points(customer_id);
CREATE INDEX idx_loyalty_points_type ON loyalty_points(transaction_type);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE loyalty_points ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- TRIGGERS
-- =====================================================

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallet_transactions_updated_at BEFORE UPDATE ON wallet_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallet_limits_updated_at BEFORE UPDATE ON wallet_limits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
