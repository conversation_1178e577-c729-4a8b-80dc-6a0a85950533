import { apiMethods, ApiResponse, handleApiError, SERVICE_URLS } from './api';
import { Vehicle } from '../store/slices/fleetSlice';

// Types for Fleet Service
export interface VehicleRequest {
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  color: string;
  status: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE';
  driverId?: string;
  vehicleType?: string;
  capacity?: number;
  fuelType?: string;
  registrationExpiry?: string;
  insuranceExpiry?: string;
}

export interface VehicleResponse extends Vehicle {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleFilters {
  page?: number;
  limit?: number;
  status?: string;
  make?: string;
  model?: string;
  driverId?: string;
  vehicleType?: string;
  search?: string;
}

export interface VehicleMetrics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  outOfServiceVehicles: number;
  availableVehicles: number;
  assignedVehicles: number;
}

export interface MaintenanceRecord {
  id: string;
  vehicleId: string;
  type: string;
  description: string;
  cost: number;
  scheduledDate: string;
  completedDate?: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  serviceProvider?: string;
  notes?: string;
}

class FleetService {
  private baseUrl = SERVICE_URLS.FLEET_SERVICE;

  // Get all vehicles with filters
  async getVehicles(filters: VehicleFilters = {}): Promise<ApiResponse<VehicleResponse[]>> {
    try {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await apiMethods.get<ApiResponse<VehicleResponse[]>>(
        `${this.baseUrl}?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        return await mockApiResponses.getVehicles(filters);
      }

      throw new Error(handleApiError(error));
    }
  }

  // Get vehicle by ID
  async getVehicleById(vehicleId: string): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.get<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}/${vehicleId}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Create new vehicle
  async createVehicle(vehicleData: VehicleRequest): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.post<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}`,
        vehicleData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update vehicle
  async updateVehicle(vehicleId: string, vehicleData: Partial<VehicleRequest>): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.put<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}/${vehicleId}`,
        vehicleData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete vehicle
  async deleteVehicle(vehicleId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.delete<ApiResponse<void>>(
        `${this.baseUrl}/${vehicleId}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update vehicle status
  async updateVehicleStatus(vehicleId: string, status: string, reason?: string): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.patch<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}/${vehicleId}/status`,
        { status, reason }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Assign driver to vehicle
  async assignDriver(vehicleId: string, driverId: string): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.post<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}/${vehicleId}/assign-driver`,
        { driverId }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Unassign driver from vehicle
  async unassignDriver(vehicleId: string): Promise<ApiResponse<VehicleResponse>> {
    try {
      const response = await apiMethods.post<ApiResponse<VehicleResponse>>(
        `${this.baseUrl}/${vehicleId}/unassign-driver`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get vehicle location history
  async getVehicleLocationHistory(vehicleId: string, startDate?: string, endDate?: string): Promise<ApiResponse<any[]>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiMethods.get<ApiResponse<any[]>>(
        `${this.baseUrl}/${vehicleId}/location-history?${params.toString()}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get vehicles by status
  async getVehiclesByStatus(status: string): Promise<ApiResponse<VehicleResponse[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<VehicleResponse[]>>(
        `${this.baseUrl}/status/${status}`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get available vehicles
  async getAvailableVehicles(): Promise<ApiResponse<VehicleResponse[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<VehicleResponse[]>>(
        `${this.baseUrl}/available`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get fleet metrics
  async getFleetMetrics(): Promise<ApiResponse<VehicleMetrics>> {
    try {
      const response = await apiMethods.get<ApiResponse<VehicleMetrics>>(
        `${this.baseUrl}/metrics`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Schedule maintenance
  async scheduleMaintenance(vehicleId: string, maintenanceData: Omit<MaintenanceRecord, 'id' | 'vehicleId'>): Promise<ApiResponse<MaintenanceRecord>> {
    try {
      const response = await apiMethods.post<ApiResponse<MaintenanceRecord>>(
        `${this.baseUrl}/${vehicleId}/maintenance`,
        maintenanceData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get maintenance records
  async getMaintenanceRecords(vehicleId: string): Promise<ApiResponse<MaintenanceRecord[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<MaintenanceRecord[]>>(
        `${this.baseUrl}/${vehicleId}/maintenance`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update maintenance record
  async updateMaintenanceRecord(vehicleId: string, maintenanceId: string, data: Partial<MaintenanceRecord>): Promise<ApiResponse<MaintenanceRecord>> {
    try {
      const response = await apiMethods.put<ApiResponse<MaintenanceRecord>>(
        `${this.baseUrl}/${vehicleId}/maintenance/${maintenanceId}`,
        data
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const fleetService = new FleetService();
export default fleetService;
