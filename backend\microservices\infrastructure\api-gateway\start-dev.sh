#!/bin/bash

# =============================================================================
# TECNO DRIVE API Gateway - Development Environment Starter
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="tecno-drive"
COMPOSE_FILE="docker-compose.dev.yml"
BUILD_TYPE=${1:-"optimized"}
CLEAN_START=${2:-"false"}

# Function to print colored output
print_header() {
    echo -e "\n${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${NC}${CYAN}                    TECNO DRIVE API GATEWAY                   ${NC}${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}${CYAN}                   Development Environment                    ${NC}${BLUE}║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
}

print_step() {
    echo -e "\n${GREEN}🚀 $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}" >&2
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_step "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_success "Docker is available"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    print_success "Docker Compose is available"
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    print_success "Docker daemon is running"
    
    # Check available disk space
    AVAILABLE_SPACE=$(df . | awk 'NR==2 {print $4}')
    if [ "$AVAILABLE_SPACE" -lt 2097152 ]; then  # 2GB in KB
        print_warning "Low disk space. At least 2GB recommended"
    fi
}

# Function to clean up existing containers
cleanup_containers() {
    if [ "$CLEAN_START" = "true" ]; then
        print_step "Cleaning Up Existing Containers"
        
        # Stop and remove containers
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down -v --remove-orphans 2>/dev/null || true
        
        # Remove dangling images
        docker image prune -f &> /dev/null || true
        
        # Remove unused volumes
        docker volume prune -f &> /dev/null || true
        
        print_success "Cleanup completed"
    fi
}

# Function to build images
build_images() {
    print_step "Building Images"
    
    # Set build arguments
    export BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
    export VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')
    export VERSION="dev-$(date +%Y%m%d-%H%M%S)"
    
    print_info "Build Date: $BUILD_DATE"
    print_info "VCS Ref: $VCS_REF"
    print_info "Version: $VERSION"
    
    # Build with the specified type
    case $BUILD_TYPE in
        optimized)
            print_info "Building optimized image..."
            docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" build --build-arg DOCKERFILE=Dockerfile.optimized
            ;;
        distroless)
            print_info "Building distroless image..."
            docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" build --build-arg DOCKERFILE=Dockerfile.distroless
            ;;
        debug)
            print_info "Building debug image..."
            docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" build --build-arg DOCKERFILE=Dockerfile
            ;;
        *)
            print_error "Invalid build type: $BUILD_TYPE"
            exit 1
            ;;
    esac
    
    print_success "Images built successfully"
}

# Function to start services
start_services() {
    print_step "Starting Services"
    
    # Start infrastructure services first
    print_info "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d postgres redis eureka
    
    # Wait for infrastructure to be ready
    print_info "Waiting for infrastructure services..."
    sleep 10
    
    # Start monitoring services
    print_info "Starting monitoring services..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d prometheus grafana zipkin
    
    # Start the API Gateway
    print_info "Starting API Gateway..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d api-gateway
    
    # Start Traefik
    print_info "Starting Traefik..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d traefik
    
    print_success "All services started"
}

# Function to wait for services to be ready
wait_for_services() {
    print_step "Waiting for Services to be Ready"
    
    local services=("postgres:5432" "redis:6379" "eureka:8761" "api-gateway:8080")
    
    for service in "${services[@]}"; do
        local host=$(echo $service | cut -d: -f1)
        local port=$(echo $service | cut -d: -f2)
        
        print_info "Waiting for $host:$port..."
        
        local retries=30
        while [ $retries -gt 0 ]; do
            if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec -T "$host" nc -z localhost "$port" 2>/dev/null; then
                print_success "$host:$port is ready"
                break
            fi
            
            retries=$((retries - 1))
            if [ $retries -eq 0 ]; then
                print_error "$host:$port is not responding"
                return 1
            fi
            
            sleep 2
        done
    done
}

# Function to show service status
show_status() {
    print_step "Service Status"
    
    echo -e "${CYAN}Container Status:${NC}"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps
    
    echo -e "\n${CYAN}Service Health:${NC}"
    
    # Check API Gateway health
    if curl -s http://localhost:8080/actuator/health > /dev/null; then
        print_success "API Gateway: Healthy"
    else
        print_warning "API Gateway: Not responding"
    fi
    
    # Check PostgreSQL
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec -T postgres pg_isready -U tecnodrive > /dev/null 2>&1; then
        print_success "PostgreSQL: Ready"
    else
        print_warning "PostgreSQL: Not ready"
    fi
    
    # Check Redis
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis: Ready"
    else
        print_warning "Redis: Not ready"
    fi
}

# Function to show access URLs
show_urls() {
    print_step "Access URLs"
    
    echo -e "${CYAN}🌐 Service URLs:${NC}"
    echo -e "  ${GREEN}API Gateway:${NC}     http://localhost:8080"
    echo -e "  ${GREEN}Management:${NC}      http://localhost:8081/actuator"
    echo -e "  ${GREEN}Eureka:${NC}         http://localhost:8761"
    echo -e "  ${GREEN}Grafana:${NC}        http://localhost:3000 (admin/admin123)"
    echo -e "  ${GREEN}Prometheus:${NC}     http://localhost:9090"
    echo -e "  ${GREEN}Zipkin:${NC}         http://localhost:9411"
    echo -e "  ${GREEN}Traefik:${NC}        http://localhost:8080 (dashboard)"
    
    echo -e "\n${CYAN}🗄️  Database Access:${NC}"
    echo -e "  ${GREEN}PostgreSQL:${NC}     localhost:5432 (tecnodrive/tecnodrive123)"
    echo -e "  ${GREEN}Redis:${NC}          localhost:6379 (password: redis123)"
    
    echo -e "\n${CYAN}🔍 Health Checks:${NC}"
    echo -e "  ${GREEN}API Health:${NC}     http://localhost:8080/actuator/health"
    echo -e "  ${GREEN}API Info:${NC}       http://localhost:8080/actuator/info"
    echo -e "  ${GREEN}API Metrics:${NC}    http://localhost:8081/actuator/metrics"
}

# Function to show logs
show_logs() {
    print_step "Recent Logs"
    
    echo -e "${CYAN}API Gateway Logs (last 20 lines):${NC}"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs --tail=20 api-gateway
}

# Function to show useful commands
show_commands() {
    print_step "Useful Commands"
    
    echo -e "${CYAN}📋 Management Commands:${NC}"
    echo -e "  ${GREEN}View logs:${NC}           docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f [service]"
    echo -e "  ${GREEN}Restart service:${NC}     docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME restart [service]"
    echo -e "  ${GREEN}Stop all:${NC}            docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down"
    echo -e "  ${GREEN}Rebuild:${NC}             docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up --build -d"
    echo -e "  ${GREEN}Shell access:${NC}        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec api-gateway sh"
    
    echo -e "\n${CYAN}🔧 Development Commands:${NC}"
    echo -e "  ${GREEN}Clean restart:${NC}       ./start-dev.sh $BUILD_TYPE true"
    echo -e "  ${GREEN}Build only:${NC}          ./build-optimized.sh dev $BUILD_TYPE"
    echo -e "  ${GREEN}Test API:${NC}            curl http://localhost:8080/actuator/health"
}

# Main execution
main() {
    print_header
    
    print_info "Build Type: $BUILD_TYPE"
    print_info "Clean Start: $CLEAN_START"
    
    check_prerequisites
    cleanup_containers
    build_images
    start_services
    wait_for_services
    show_status
    show_urls
    show_logs
    show_commands
    
    echo -e "\n${GREEN}🎉 Development environment is ready!${NC}"
    echo -e "${BLUE}💡 Use 'docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f' to follow logs${NC}"
}

# Handle script arguments
case "${1:-help}" in
    optimized|distroless|debug)
        main
        ;;
    stop)
        print_step "Stopping Development Environment"
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down
        print_success "Environment stopped"
        ;;
    clean)
        print_step "Cleaning Development Environment"
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down -v --remove-orphans
        docker system prune -f
        print_success "Environment cleaned"
        ;;
    logs)
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs -f "${2:-api-gateway}"
        ;;
    status)
        show_status
        show_urls
        ;;
    help|*)
        echo -e "${CYAN}TECNO DRIVE API Gateway Development Environment${NC}"
        echo -e "\n${GREEN}Usage:${NC}"
        echo -e "  $0 [optimized|distroless|debug] [clean]  - Start environment"
        echo -e "  $0 stop                                   - Stop environment"
        echo -e "  $0 clean                                  - Clean environment"
        echo -e "  $0 logs [service]                         - Show logs"
        echo -e "  $0 status                                 - Show status"
        echo -e "  $0 help                                   - Show this help"
        echo -e "\n${GREEN}Examples:${NC}"
        echo -e "  $0 optimized        - Start with optimized build"
        echo -e "  $0 distroless true  - Clean start with distroless build"
        echo -e "  $0 logs api-gateway - Show API Gateway logs"
        ;;
esac
