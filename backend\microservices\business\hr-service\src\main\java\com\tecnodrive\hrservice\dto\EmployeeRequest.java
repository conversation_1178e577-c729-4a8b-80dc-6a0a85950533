package com.tecnodrive.hrservice.dto;

import com.tecnodrive.hrservice.entity.Employee;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Employee Request DTO
 * 
 * Used for creating and updating employees
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeRequest {

    /**
     * Personal information
     */
    @NotBlank(message = "First name is required")
    @Size(max = 100, message = "First name cannot exceed 100 characters")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(max = 100, message = "Last name cannot exceed 100 characters")
    private String lastName;

    @NotBlank(message = "Email is required")
    @Email(message = "Email must be valid")
    @Size(max = 150, message = "Email cannot exceed 150 characters")
    private String email;

    @Size(max = 20, message = "Employee number cannot exceed 20 characters")
    private String employeeNumber;

    @Pattern(regexp = "^[+]?[0-9\\-\\s()]+$", message = "Phone number format is invalid")
    @Size(max = 20, message = "Phone number cannot exceed 20 characters")
    private String phoneNumber;

    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    private Employee.Gender gender;

    @Size(max = 500, message = "Address cannot exceed 500 characters")
    private String address;

    /**
     * Employment information
     */
    @NotBlank(message = "Position is required")
    @Size(max = 100, message = "Position cannot exceed 100 characters")
    private String position;

    @NotBlank(message = "Department is required")
    @Size(max = 100, message = "Department cannot exceed 100 characters")
    private String department;

    private Employee.EmployeeStatus status;

    @NotNull(message = "Employment type is required")
    private Employee.EmploymentType employmentType;

    @NotNull(message = "Hire date is required")
    @PastOrPresent(message = "Hire date cannot be in the future")
    private LocalDate hireDate;

    @Future(message = "Termination date must be in the future")
    private LocalDate terminationDate;

    @Future(message = "Probation end date must be in the future")
    private LocalDate probationEndDate;

    /**
     * Compensation information
     */
    @DecimalMin(value = "0.0", message = "Salary cannot be negative")
    @Digits(integer = 10, fraction = 2, message = "Salary must have at most 10 integer digits and 2 decimal places")
    private BigDecimal salary;

    @DecimalMin(value = "0.0", message = "Hourly rate cannot be negative")
    @Digits(integer = 8, fraction = 2, message = "Hourly rate must have at most 8 integer digits and 2 decimal places")
    private BigDecimal hourlyRate;

    private Employee.PayFrequency payFrequency;

    @Size(max = 3, message = "Currency code cannot exceed 3 characters")
    private String currency;

    /**
     * Manager and reporting structure
     */
    private String managerId;

    @Size(max = 100, message = "Manager name cannot exceed 100 characters")
    private String managerName;

    /**
     * Leave and benefits
     */
    @Min(value = 0, message = "Annual leave days cannot be negative")
    @Max(value = 365, message = "Annual leave days cannot exceed 365")
    private Integer annualLeaveDays;

    @Min(value = 0, message = "Sick leave days cannot be negative")
    @Max(value = 365, message = "Sick leave days cannot exceed 365")
    private Integer sickLeaveDays;

    @Min(value = 0, message = "Used annual leave cannot be negative")
    private Integer usedAnnualLeave;

    @Min(value = 0, message = "Used sick leave cannot be negative")
    private Integer usedSickLeave;

    /**
     * Performance and development
     */
    @PastOrPresent(message = "Last performance review cannot be in the future")
    private LocalDate lastPerformanceReview;

    @Future(message = "Next performance review must be in the future")
    private LocalDate nextPerformanceReview;

    @DecimalMin(value = "0.0", message = "Performance rating cannot be negative")
    @DecimalMax(value = "5.0", message = "Performance rating cannot exceed 5.0")
    @Digits(integer = 1, fraction = 2, message = "Performance rating must have at most 1 integer digit and 2 decimal places")
    private BigDecimal performanceRating;

    @Size(max = 1000, message = "Performance notes cannot exceed 1000 characters")
    private String performanceNotes;

    /**
     * Emergency contact
     */
    @Size(max = 100, message = "Emergency contact name cannot exceed 100 characters")
    private String emergencyContactName;

    @Pattern(regexp = "^[+]?[0-9\\-\\s()]+$", message = "Emergency contact phone format is invalid")
    @Size(max = 20, message = "Emergency contact phone cannot exceed 20 characters")
    private String emergencyContactPhone;

    @Size(max = 50, message = "Emergency contact relation cannot exceed 50 characters")
    private String emergencyContactRelation;

    /**
     * Company/Tenant information
     */
    @NotBlank(message = "Company ID is required")
    private String companyId;

    /**
     * Additional information
     */
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    private String notes;

    @Size(max = 500, message = "Skills cannot exceed 500 characters")
    private String skills;

    @Size(max = 500, message = "Certifications cannot exceed 500 characters")
    private String certifications;

    @Size(max = 200, message = "Education cannot exceed 200 characters")
    private String education;

    /**
     * System fields
     */
    private Boolean isActive;
}
