import { apiMethods, ApiResponse, handleApiError, SERVICE_URLS } from './api';
import { Payment } from '../store/slices/paymentsSlice';

export interface PaymentFilters {
  page?: number;
  limit?: number;
  status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  method?: 'CASH' | 'CARD' | 'WALLET' | 'BANK_TRANSFER';
  startDate?: string;
  endDate?: string;
  rideId?: string;
}

export interface PaymentMetrics {
  totalRevenue: number;
  todayRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  completedPayments: number;
  failedPayments: number;
}

class PaymentsService {
  private baseUrl = SERVICE_URLS.PAYMENT_SERVICE;

  async getPayments(filters: PaymentFilters = {}): Promise<ApiResponse<Payment[]>> {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await apiMethods.get<ApiResponse<Payment[]>>(
        `${this.baseUrl}?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        return await mockApiResponses.getPayments(filters);
      }

      throw new Error(handleApiError(error));
    }
  }

  async getPaymentById(paymentId: string): Promise<ApiResponse<Payment>> {
    try {
      const response = await apiMethods.get<ApiResponse<Payment>>(
        `${this.baseUrl}/${paymentId}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async processPayment(paymentData: {
    rideId: string;
    amount: number;
    method: string;
    currency?: string;
  }): Promise<ApiResponse<Payment>> {
    try {
      const response = await apiMethods.post<ApiResponse<Payment>>(
        `${this.baseUrl}/process`,
        paymentData
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async refundPayment(paymentId: string, reason: string): Promise<ApiResponse<Payment>> {
    try {
      const response = await apiMethods.post<ApiResponse<Payment>>(
        `${this.baseUrl}/${paymentId}/refund`,
        { reason }
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getPaymentMetrics(): Promise<ApiResponse<PaymentMetrics>> {
    try {
      const response = await apiMethods.get<ApiResponse<PaymentMetrics>>(
        `${this.baseUrl}/metrics`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getRevenueReport(startDate: string, endDate: string): Promise<ApiResponse<any>> {
    try {
      const response = await apiMethods.get<ApiResponse<any>>(
        `${this.baseUrl}/reports/revenue?startDate=${startDate}&endDate=${endDate}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const paymentsService = new PaymentsService();
export default paymentsService;
