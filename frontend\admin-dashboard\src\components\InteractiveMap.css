.interactive-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Map Controls */
.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 250px;
  backdrop-filter: blur(10px);
}

.connection-status {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.status-indicator {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.layer-controls {
  margin-bottom: 15px;
}

.layer-controls label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
}

.layer-controls input[type="checkbox"] {
  margin-left: 8px;
  margin-right: 0;
}

.map-stats {
  font-size: 12px;
  color: #666;
}

.map-stats div {
  margin-bottom: 4px;
}

/* Leaflet Map Customizations */
.leaflet-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
  margin: 12px 16px;
  line-height: 1.4;
}

/* Vehicle Popup */
.vehicle-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.vehicle-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

/* Route Popup */
.route-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 2px solid #2980b9;
  padding-bottom: 5px;
}

.route-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

/* Traffic Popup */
.traffic-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 5px;
}

.traffic-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

/* Geofence Popup */
.geofence-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 2px solid #e67e22;
  padding-bottom: 5px;
}

.geofence-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

/* Cluster Popup */
.cluster-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 2px solid #9b59b6;
  padding-bottom: 5px;
}

.cluster-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

/* Vehicle Details Panel */
.vehicle-details-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  overflow: hidden;
}

.panel-header {
  background: #3498db;
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-header button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.panel-header button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.panel-content {
  padding: 16px;
}

.panel-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #555;
}

.panel-content strong {
  color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-controls {
    position: relative;
    top: 0;
    right: 0;
    margin-bottom: 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .vehicle-details-panel {
    position: relative;
    bottom: 0;
    left: 0;
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .interactive-map-container .leaflet-container {
    height: 400px !important;
  }
}

/* Custom Marker Styles */
.vehicle-marker {
  background: #3498db;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.vehicle-marker.active {
  background: #27ae60;
}

.vehicle-marker.busy {
  background: #f39c12;
}

.vehicle-marker.offline {
  background: #95a5a6;
}

/* Traffic Layer Styles */
.traffic-heavy {
  background: #e74c3c;
}

.traffic-moderate {
  background: #f39c12;
}

.traffic-light {
  background: #f1c40f;
}

.traffic-free {
  background: #27ae60;
}

/* Animation for real-time updates */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.vehicle-marker.updating {
  animation: pulse 1s ease-in-out;
}

/* Geofence styles */
.geofence-boundary {
  stroke-dasharray: 5, 5;
  stroke-width: 2;
  fill-opacity: 0.1;
}

.geofence-boundary.active {
  stroke: #e74c3c;
  fill: #e74c3c;
}

.geofence-boundary.inactive {
  stroke: #95a5a6;
  fill: #95a5a6;
}

/* Cluster styles */
.marker-cluster {
  background: rgba(181, 226, 140, 0.6);
  border: 2px solid rgba(110, 204, 57, 0.6);
  border-radius: 50%;
  color: #333;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
}

.marker-cluster div {
  background: rgba(110, 204, 57, 0.6);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;
  text-align: center;
  line-height: 30px;
}

/* Loading states */
.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
