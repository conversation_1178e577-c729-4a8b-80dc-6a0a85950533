Write-Host "Checking TecnoDrive Services..." -ForegroundColor Green

$services = @(
    @{Port=8097; Name="HR Service"},
    @{Port=8098; Name="Financial Service"},
    @{Port=8099; Name="Wallet Service"},
    @{Port=8100; Name="Live Operations Service"},
    @{Port=8101; Name="Operations Management Service"},
    @{Port=8102; Name="Trip Tracking Service"},
    @{Port=8103; Name="Demand Analysis Service"},
    @{Port=8104; Name="Trip Planning Service"},
    @{Port=8105; Name="Invoicing Service"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:$($service.Port)/health" -TimeoutSec 3
        Write-Host "✓ $($service.Name) (Port $($service.Port)): $($response.status)" -ForegroundColor Green
    } catch {
        Write-Host "✗ $($service.Name) (Port $($service.Port)): Not Ready" -ForegroundColor Red
    }
}

Write-Host "`nChecking Frontend..." -ForegroundColor Green
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 3
    Write-Host "✓ Frontend (Port 3000): Ready (Status: $($frontend.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Frontend (Port 3000): Not Ready" -ForegroundColor Red
}

Write-Host "`nChecking Map Service..." -ForegroundColor Green
try {
    $map = Invoke-RestMethod -Uri "http://localhost:8085/health" -TimeoutSec 3
    Write-Host "✓ Map Service (Port 8085): $($map.status)" -ForegroundColor Green
} catch {
    Write-Host "✗ Map Service (Port 8085): Not Ready" -ForegroundColor Red
}

Write-Host "`nService Check Complete!" -ForegroundColor Yellow
