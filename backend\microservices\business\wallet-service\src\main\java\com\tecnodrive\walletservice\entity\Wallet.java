package com.tecnodrive.walletservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Digital Wallet Entity
 */
@Entity
@Table(name = "wallets")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Wallet {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "user_id", nullable = false, unique = true)
    private UUID userId;

    @Column(name = "phone_number", nullable = false, unique = true, length = 20)
    private String phoneNumber;

    @Column(name = "balance", nullable = false, precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "currency", nullable = false, length = 3)
    @Builder.Default
    private String currency = "SAR";

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private WalletStatus status = WalletStatus.ACTIVE;

    @Column(name = "daily_limit", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal dailyLimit = new BigDecimal("5000.00");

    @Column(name = "monthly_limit", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal monthlyLimit = new BigDecimal("50000.00");

    @Column(name = "daily_spent", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal dailySpent = BigDecimal.ZERO;

    @Column(name = "monthly_spent", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal monthlySpent = BigDecimal.ZERO;

    @Column(name = "last_transaction_date")
    private LocalDateTime lastTransactionDate;

    @Column(name = "pin_hash", length = 255)
    private String pinHash;

    @Column(name = "is_pin_set")
    @Builder.Default
    private Boolean isPinSet = false;

    @Column(name = "failed_pin_attempts")
    @Builder.Default
    private Integer failedPinAttempts = 0;

    @Column(name = "locked_until")
    private LocalDateTime lockedUntil;

    @Column(name = "verification_level")
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private VerificationLevel verificationLevel = VerificationLevel.BASIC;

    @Column(name = "notes", length = 500)
    private String notes;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by", length = 100)
    private String createdBy;

    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * Wallet Status Enum
     */
    public enum WalletStatus {
        ACTIVE,
        SUSPENDED,
        BLOCKED,
        CLOSED
    }

    /**
     * Verification Level Enum
     */
    public enum VerificationLevel {
        BASIC,      // Phone verified
        VERIFIED,   // ID verified
        PREMIUM     // Full KYC verified
    }

    /**
     * Check if wallet is active and can be used
     */
    public boolean isUsable() {
        return status == WalletStatus.ACTIVE && 
               (lockedUntil == null || lockedUntil.isBefore(LocalDateTime.now()));
    }

    /**
     * Check if daily limit is exceeded
     */
    public boolean isDailyLimitExceeded(BigDecimal amount) {
        return dailySpent.add(amount).compareTo(dailyLimit) > 0;
    }

    /**
     * Check if monthly limit is exceeded
     */
    public boolean isMonthlyLimitExceeded(BigDecimal amount) {
        return monthlySpent.add(amount).compareTo(monthlyLimit) > 0;
    }

    /**
     * Check if wallet has sufficient balance
     */
    public boolean hasSufficientBalance(BigDecimal amount) {
        return balance.compareTo(amount) >= 0;
    }

    /**
     * Add amount to balance
     */
    public void addBalance(BigDecimal amount) {
        this.balance = this.balance.add(amount);
        this.lastTransactionDate = LocalDateTime.now();
    }

    /**
     * Subtract amount from balance
     */
    public void subtractBalance(BigDecimal amount) {
        this.balance = this.balance.subtract(amount);
        this.dailySpent = this.dailySpent.add(amount);
        this.monthlySpent = this.monthlySpent.add(amount);
        this.lastTransactionDate = LocalDateTime.now();
    }

    /**
     * Reset daily spending
     */
    public void resetDailySpending() {
        this.dailySpent = BigDecimal.ZERO;
    }

    /**
     * Reset monthly spending
     */
    public void resetMonthlySpending() {
        this.monthlySpent = BigDecimal.ZERO;
    }
}
