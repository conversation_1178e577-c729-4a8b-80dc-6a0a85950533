# Test Maps and Persistent Auth
Write-Host "🎉 Testing Maps + Persistent Auth - TecnoDrive Platform" -ForegroundColor Green
Write-Host "======================================================" -ForegroundColor Green

# Test Frontend
Write-Host "`n🌐 Testing Frontend..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "✅ Frontend: Ready (Status: $($frontendResponse.StatusCode))" -ForegroundColor Green
    $frontendWorking = $true
} catch {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
    $frontendWorking = $false
}

# Test Map Service
Write-Host "`n🗺️ Testing Map Service..." -ForegroundColor Cyan
try {
    $mapHealth = Invoke-RestMethod -Uri "http://localhost:8085/health" -TimeoutSec 5
    Write-Host "✅ Map Service: $($mapHealth.status)" -ForegroundColor Green
    Write-Host "📊 Service: $($mapHealth.service)" -ForegroundColor Cyan
    $mapWorking = $true
} catch {
    Write-Host "❌ Map Service: Not Ready" -ForegroundColor Red
    $mapWorking = $false
}

# Test Map APIs
if ($mapWorking) {
    Write-Host "`n🧪 Testing Map APIs..." -ForegroundColor Cyan
    
    $mapApis = @(
        @{ Name = "Config"; Url = "http://localhost:8085/api/map/config/enhanced" },
        @{ Name = "Vehicles"; Url = "http://localhost:8085/api/map/vehicles" },
        @{ Name = "Tiles"; Url = "http://localhost:8085/api/map/tiles" }
    )
    
    $workingMapApis = 0
    foreach ($api in $mapApis) {
        try {
            $response = Invoke-RestMethod -Uri $api.Url -TimeoutSec 5
            Write-Host "✅ $($api.Name) API: Working" -ForegroundColor Green
            
            if ($api.Name -eq "Config" -and $response.success) {
                Write-Host "   🎯 Default Center: $($response.data.defaultCenter.lat), $($response.data.defaultCenter.lng)" -ForegroundColor Gray
                Write-Host "   🗺️ Providers: $($response.data.providers.Count)" -ForegroundColor Gray
            }
            elseif ($api.Name -eq "Vehicles" -and $response.success) {
                Write-Host "   🚗 Vehicle Count: $($response.data.Count)" -ForegroundColor Gray
            }
            
            $workingMapApis++
        } catch {
            Write-Host "❌ $($api.Name) API: Failed" -ForegroundColor Red
        }
    }
}

# Summary
Write-Host "`n📊 System Status" -ForegroundColor Green
Write-Host "================" -ForegroundColor Green

if ($frontendWorking) {
    Write-Host "✅ Frontend: Ready" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
}

if ($mapWorking) {
    Write-Host "✅ Map Service: Ready ($workingMapApis/3 APIs working)" -ForegroundColor Green
} else {
    Write-Host "❌ Map Service: Not Ready" -ForegroundColor Red
}

# Test URLs
Write-Host "`n🔗 Test URLs:" -ForegroundColor Yellow
Write-Host "=============" -ForegroundColor Yellow
Write-Host "🔐 Login (with Remember Me): http://localhost:3000/login" -ForegroundColor White
Write-Host "📊 Dashboard: http://localhost:3000/dashboard" -ForegroundColor White
Write-Host "🗺️ Enhanced Map Test: http://localhost:3000/map/test" -ForegroundColor White
Write-Host "🗺️ Simple Map: http://localhost:3000/map" -ForegroundColor White

# New Features
Write-Host "`n🆕 New Features Added:" -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow
Write-Host "✅ Persistent Login (Remember Me for 30 days)" -ForegroundColor Green
Write-Host "✅ Auto-Login on browser restart" -ForegroundColor Green
Write-Host "✅ Enhanced Map Service with multiple providers" -ForegroundColor Green
Write-Host "✅ Interactive vehicle markers" -ForegroundColor Green
Write-Host "✅ Map layer switching" -ForegroundColor Green
Write-Host "✅ Satellite view toggle" -ForegroundColor Green

# Testing Instructions
Write-Host "`n📋 Testing Instructions:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

Write-Host "`n🔐 Test Persistent Login:" -ForegroundColor Cyan
Write-Host "1. Go to: http://localhost:3000/login" -ForegroundColor White
Write-Host "2. Enter: <EMAIL> / password123" -ForegroundColor White
Write-Host "3. ✅ Check 'تذكرني (البقاء مسجلاً لمدة 30 يوماً)'" -ForegroundColor White
Write-Host "4. Click 'تسجيل الدخول'" -ForegroundColor White
Write-Host "5. Should redirect to dashboard" -ForegroundColor White
Write-Host "6. Close browser completely" -ForegroundColor White
Write-Host "7. Reopen browser and go to http://localhost:3000" -ForegroundColor White
Write-Host "8. Should auto-login and go to dashboard" -ForegroundColor White

Write-Host "`n🗺️ Test Enhanced Maps:" -ForegroundColor Cyan
Write-Host "1. Go to: http://localhost:3000/map/test" -ForegroundColor White
Write-Host "2. Verify map loads with OpenStreetMap" -ForegroundColor White
Write-Host "3. Check API status indicators (should be green)" -ForegroundColor White
Write-Host "4. Try changing map provider (dropdown)" -ForegroundColor White
Write-Host "5. Click on vehicle markers" -ForegroundColor White
Write-Host "6. Toggle satellite view" -ForegroundColor White
Write-Host "7. Use map controls (zoom, refresh)" -ForegroundColor White

# Browser Console Tests
Write-Host "`n🧪 Browser Console Quick Tests:" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

Write-Host "`n📋 Quick Persistent Login:" -ForegroundColor Cyan
Write-Host @"
// Copy to browser console (F12):
const userData = {id:'1',email:'<EMAIL>',name:'Azal Mohamed',role:'ADMIN'};
const now = Date.now();
const token = btoa(JSON.stringify({...userData,iat:now,exp:now+**********,rememberMe:true}));
localStorage.setItem('tecnodrive_auth_token', token);
localStorage.setItem('tecnodrive_user_data', JSON.stringify(userData));
localStorage.setItem('tecnodrive_remember_me', 'true');
localStorage.setItem('tecnodrive_auto_login', 'true');
localStorage.setItem('tecnodrive_expires_at', (now+**********).toString());
console.log('✅ Persistent login enabled!'); window.location.href='/dashboard';
"@ -ForegroundColor Gray

Write-Host "`n📋 Test Map APIs:" -ForegroundColor Cyan
Write-Host @"
// Test Map APIs:
fetch('http://localhost:8085/api/map/config/enhanced')
  .then(r=>r.json()).then(d=>console.log('✅ Map Config:',d))
  .catch(e=>console.log('❌ Map Error:',e));
"@ -ForegroundColor Gray

# Expected Results
Write-Host "`n🎯 Expected Results:" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "✅ Login form shows 'تذكرني' checkbox" -ForegroundColor White
Write-Host "✅ Persistent login works for 30 days" -ForegroundColor White
Write-Host "✅ Auto-login on browser restart" -ForegroundColor White
Write-Host "✅ Maps load with interactive features" -ForegroundColor White
Write-Host "✅ Vehicle markers with detailed popups" -ForegroundColor White
Write-Host "✅ Multiple map providers available" -ForegroundColor White
Write-Host "✅ All API status indicators green" -ForegroundColor White

# Final Status
if ($frontendWorking -and $mapWorking) {
    Write-Host "`n🎉 System Fully Ready!" -ForegroundColor Green
    Write-Host "🔗 Start testing at: http://localhost:3000/login" -ForegroundColor Cyan
    Write-Host "📝 Credentials: <EMAIL> / password123" -ForegroundColor Cyan
    Write-Host "✅ Remember to check 'تذكرني' for persistent login!" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️ System Partially Ready" -ForegroundColor Yellow
    if (-not $frontendWorking) {
        Write-Host "❌ Start Frontend: cd frontend/admin-dashboard && npm start" -ForegroundColor Red
    }
    if (-not $mapWorking) {
        Write-Host "❌ Start Map Service: powershell -File map-service.ps1" -ForegroundColor Red
    }
}

Write-Host "`n🚀 Maps + Persistent Auth Testing Ready!" -ForegroundColor Green
