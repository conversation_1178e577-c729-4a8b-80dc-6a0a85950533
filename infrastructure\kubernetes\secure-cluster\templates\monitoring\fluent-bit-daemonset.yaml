{{- if .Values.logging.fluentBit.enabled }}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit-siem
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: fluent-bit-siem
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    k8s-app: fluent-bit-logging
    version: v1
    kubernetes.io/cluster-service: "true"
spec:
  selector:
    matchLabels:
      k8s-app: fluent-bit-logging
  template:
    metadata:
      labels:
        k8s-app: fluent-bit-logging
        version: v1
        kubernetes.io/cluster-service: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: /api/v1/metrics/prometheus
    spec:
      serviceAccount: fluent-bit
      serviceAccountName: fluent-bit
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
        - operator: "Exists"
          effect: "NoExecute"
        - operator: "Exists"
          effect: "NoSchedule"
      containers:
        - name: fluent-bit
          image: fluent/fluent-bit:{{ .Values.logging.fluentBit.image.tag | default "2.2.0" }}
          imagePullPolicy: Always
          ports:
            - containerPort: 2020
              name: metrics
              protocol: TCP
          env:
            - name: FLUENT_CONF
              value: "fluent-bit.conf"
            - name: SIEM_ENDPOINT
              value: "{{ .Values.siem.elasticsearch.endpoint | default "elasticsearch.logging.svc.cluster.local:9200" }}"
            {{- if .Values.siem.splunk.enabled }}
            - name: SPLUNK_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.siem.splunk.tokenSecret | default "splunk-token" }}
                  key: token
            {{- end }}
            {{- if .Values.siem.elasticsearch.enabled }}
            - name: ES_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.siem.elasticsearch.passwordSecret | default "elasticsearch-credentials" }}
                  key: username
                  optional: true
            - name: ES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.siem.elasticsearch.passwordSecret | default "elasticsearch-credentials" }}
                  key: password
                  optional: true
            {{- end }}
          resources:
            limits:
              memory: 200Mi
              cpu: 100m
            requests:
              memory: 100Mi
              cpu: 50m
          volumeMounts:
            - name: config
              mountPath: /fluent-bit/etc/
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - name: mnt
              mountPath: /mnt
              readOnly: true
          securityContext:
            runAsNonRoot: false
            runAsUser: 0
            readOnlyRootFilesystem: true
            allowPrivilegeEscalation: false
            capabilities:
              drop: ["ALL"]
              add: ["DAC_OVERRIDE", "CHOWN"]
      terminationGracePeriodSeconds: 10
      volumes:
        - name: config
          configMap:
            name: fluent-bit-config
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
        - name: varlog
          hostPath:
            path: /var/log
        - name: mnt
          hostPath:
            path: /mnt

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: fluent-bit-config
    app.kubernetes.io/instance: {{ .Release.Name }}
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020
        Health_Check  On

    # Kubernetes logs input
    [INPUT]
        Name              tail
        Tag               kube.*
        Path              /var/log/containers/*.log
        Parser            docker
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10

    # Gatekeeper specific logs
    [INPUT]
        Name              tail
        Tag               gatekeeper.*
        Path              /var/log/containers/*gatekeeper*.log
        Parser            docker
        DB                /var/log/flb_gatekeeper.db
        Mem_Buf_Limit     5MB

    # TECNO DRIVE application logs
    [INPUT]
        Name              tail
        Tag               tecnodrive.*
        Path              /var/log/containers/*tecno-drive*.log
        Parser            docker
        DB                /var/log/flb_tecnodrive.db
        Mem_Buf_Limit     20MB

    # Kubernetes metadata filter
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Kube_Tag_Prefix     kube.var.log.containers.
        Merge_Log           On
        Merge_Log_Key       log_processed
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off
        Annotations         Off
        Labels              On

    # Security events filter
    [FILTER]
        Name             grep
        Match            gatekeeper.*
        Regex            log (violation|security|unauthorized|forbidden|denied|error)

    # TECNO DRIVE business events filter
    [FILTER]
        Name             grep
        Match            tecnodrive.*
        Regex            log (error|exception|failed|timeout|unauthorized|forbidden)

    # Add security classification
    [FILTER]
        Name             modify
        Match            gatekeeper.*
        Add              event_type security_violation
        Add              source gatekeeper
        Add              platform tecnodrive

    # Add business classification
    [FILTER]
        Name             modify
        Match            tecnodrive.*
        Add              event_type business_event
        Add              source application
        Add              platform tecnodrive

    # Parse JSON logs
    [FILTER]
        Name             parser
        Match            *
        Key_Name         log
        Parser           json
        Reserve_Data     On

    {{- if .Values.siem.elasticsearch.enabled }}
    # Output to Elasticsearch
    [OUTPUT]
        Name            es
        Match           *
        Host            ${SIEM_ENDPOINT}
        Port            9200
        {{- if .Values.siem.elasticsearch.username }}
        HTTP_User       ${ES_USERNAME}
        HTTP_Passwd     ${ES_PASSWORD}
        {{- end }}
        Index           {{ .Values.siem.elasticsearch.index | default "tecno-drive-logs" }}
        Type            _doc
        Logstash_Format On
        Logstash_Prefix {{ .Values.siem.elasticsearch.index | default "tecno-drive-logs" }}
        Logstash_DateFormat %Y.%m.%d
        Include_Tag_Key On
        Tag_Key         @tag
        Time_Key        @timestamp
        Time_Key_Format %Y-%m-%dT%H:%M:%S.%L%z
        Retry_Limit     5
        Buffer_Size     4KB
        Workers         1
    {{- end }}

    {{- if .Values.siem.splunk.enabled }}
    # Output to Splunk
    [OUTPUT]
        Name            splunk
        Match           *
        Host            {{ .Values.siem.splunk.endpoint }}
        Port            {{ .Values.siem.splunk.port | default 8088 }}
        Splunk_Token    ${SPLUNK_TOKEN}
        Splunk_Send_Raw On
        tls             On
        tls.verify      Off
    {{- end }}

    {{- if .Values.logging.outputs.loki.enabled }}
    # Output to Loki
    [OUTPUT]
        Name            loki
        Match           *
        Host            {{ .Values.logging.outputs.loki.host | default "loki.logging.svc.cluster.local" }}
        Port            {{ .Values.logging.outputs.loki.port | default 3100 }}
        Labels          job=fluent-bit, platform=tecnodrive
        Auto_Kubernetes_Labels On
    {{- end }}

  parsers.conf: |
    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On

    [PARSER]
        Name        json
        Format      json
        Time_Key    timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z

    [PARSER]
        Name        gatekeeper
        Format      regex
        Regex       ^(?<time>[^ ]*) (?<stream>stdout|stderr) (?<logtag>[^ ]*) (?<log>.*)$
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z

    [PARSER]
        Name        spring-boot
        Format      regex
        Regex       ^(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+(?<level>\w+)\s+(?<pid>\d+)\s+---\s+\[(?<thread>.*?)\]\s+(?<logger>\S+)\s*:\s+(?<message>.*)$
        Time_Key    timestamp
        Time_Format %Y-%m-%d %H:%M:%S.%L

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: fluent-bit-serviceaccount
    app.kubernetes.io/instance: {{ .Release.Name }}

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit-read
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: fluent-bit-clusterrole
    app.kubernetes.io/instance: {{ .Release.Name }}
rules:
  - apiGroups: [""]
    resources:
      - namespaces
      - pods
      - pods/logs
      - nodes
      - nodes/proxy
    verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit-read
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: fluent-bit-clusterrolebinding
    app.kubernetes.io/instance: {{ .Release.Name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit-read
subjects:
  - kind: ServiceAccount
    name: fluent-bit
    namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}

---
# Service for metrics
apiVersion: v1
kind: Service
metadata:
  name: fluent-bit-metrics
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: fluent-bit-metrics
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  ports:
    - name: metrics
      port: 2020
      protocol: TCP
      targetPort: 2020
  selector:
    k8s-app: fluent-bit-logging

---
# ServiceMonitor for Prometheus
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: fluent-bit-metrics
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: fluent-bit-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit-metrics
  endpoints:
    - port: metrics
      interval: 30s
      path: /api/v1/metrics/prometheus
{{- end }}
{{- end }}
