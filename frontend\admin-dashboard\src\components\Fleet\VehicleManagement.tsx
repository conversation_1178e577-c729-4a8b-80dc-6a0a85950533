import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  DirectionsCar as CarIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  LocationOn as LocationIcon,
  Build as MaintenanceIcon,
  LocalGasStation as FuelIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { fleetService, VehicleDto, CreateVehicleRequest } from '../../services/fleetService';
import VehicleDetail from './VehicleDetail';

const VehicleManagement: React.FC = () => {
  const navigate = useNavigate();
  
  const [vehicles, setVehicles] = useState<VehicleDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterMake, setFilterMake] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [newVehicle, setNewVehicle] = useState<CreateVehicleRequest>({
    plateNumber: '',
    make: '',
    model: '',
    year: new Date().getFullYear(),
    color: '',
    capacity: 4,
    vin: '',
    fuelType: 'GASOLINE',
    status: 'ACTIVE',
  });

  // Load vehicles data
  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fleetService.getVehicles({
        search: searchTerm,
        status: filterStatus === 'ALL' ? undefined : filterStatus,
        make: filterMake === 'ALL' ? undefined : filterMake,
      });
      
      if (response.success && response.data) {
        setVehicles(response.data);
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadVehicles();
  }, [searchTerm, filterStatus, filterMake]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: '🟢' },
      MAINTENANCE: { label: 'صيانة', color: 'warning' as const, icon: '🟠' },
      OFFLINE: { label: 'غير متصل', color: 'error' as const, icon: '🔴' },
      OUT_OF_SERVICE: { label: 'خارج الخدمة', color: 'default' as const, icon: '⚫' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: '⚪' 
    };
    
    return (
      <Chip
        label={`${config.icon} ${config.label}`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getFuelTypeIcon = (fuelType: string) => {
    switch (fuelType) {
      case 'ELECTRIC': return '🔋';
      case 'HYBRID': return '⚡';
      case 'DIESEL': return '⛽';
      default: return '⛽';
    }
  };

  const handleViewVehicle = (vehicleId: string) => {
    navigate(`/fleet/vehicles/${vehicleId}`);
  };

  const handleEditVehicle = (vehicleId: string) => {
    navigate(`/fleet/vehicles/${vehicleId}/edit`);
  };

  const handleDeleteVehicle = async (vehicleId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المركبة؟')) {
      try {
        await fleetService.deleteVehicle(vehicleId);
        loadVehicles();
      } catch (error) {
        console.error('Error deleting vehicle:', error);
      }
    }
  };

  const handleViewLocation = (vehicleId: string) => {
    navigate(`/fleet/map?vehicle=${vehicleId}`);
  };

  const columns: GridColDef[] = [
    {
      field: 'plateNumber',
      headerName: 'رقم اللوحة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <CarIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'bold', fontFamily: 'monospace' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'make',
      headerName: 'الماركة',
      width: 100,
    },
    {
      field: 'model',
      headerName: 'الموديل',
      width: 120,
    },
    {
      field: 'year',
      headerName: 'السنة',
      width: 80,
    },
    {
      field: 'color',
      headerName: 'اللون',
      width: 100,
    },
    {
      field: 'capacity',
      headerName: 'السعة',
      width: 80,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value} راكب
        </Typography>
      ),
    },
    {
      field: 'fuelType',
      headerName: 'نوع الوقود',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <span>{getFuelTypeIcon(params.value)}</span>
          <Typography variant="body2">
            {params.value === 'GASOLINE' ? 'بنزين' :
             params.value === 'DIESEL' ? 'ديزل' :
             params.value === 'ELECTRIC' ? 'كهربائي' :
             params.value === 'HYBRID' ? 'هجين' : params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'mileage',
      headerName: 'المسافة المقطوعة',
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SpeedIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value?.toLocaleString()} كم
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'currentLocation',
      headerName: 'الموقع الحالي',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <IconButton
          size="small"
          onClick={() => handleViewLocation(params.row.id)}
          color="primary"
        >
          <Tooltip title="عرض الموقع">
            <LocationIcon />
          </Tooltip>
        </IconButton>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض التفاصيل">
              <VisibilityIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => handleViewVehicle(params.id as string)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEditVehicle(params.id as string)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteVehicle(params.id as string)}
        />,
      ],
    },
  ];

  const handleAddVehicle = async () => {
    try {
      await fleetService.createVehicle(newVehicle);
      setOpenAddDialog(false);
      setNewVehicle({
        plateNumber: '',
        make: '',
        model: '',
        year: new Date().getFullYear(),
        color: '',
        capacity: 4,
        vin: '',
        fuelType: 'GASOLINE',
        status: 'ACTIVE',
      });
      loadVehicles();
    } catch (error) {
      console.error('Error creating vehicle:', error);
    }
  };

  // Calculate stats
  const totalVehicles = vehicles.length;
  const activeVehicles = vehicles.filter(v => v.status === 'ACTIVE').length;
  const maintenanceVehicles = vehicles.filter(v => v.status === 'MAINTENANCE').length;
  const offlineVehicles = vehicles.filter(v => v.status === 'OFFLINE').length;
  const totalMileage = vehicles.reduce((sum, v) => sum + (v.mileage || 0), 0);

  // Get unique makes for filter
  const uniqueMakes = Array.from(new Set(vehicles.map(v => v.make))).sort();

  return (
    <Routes>
      <Route path="/:vehicleId/*" element={<VehicleDetail />} />
      <Route path="/*" element={
        <Box>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              إدارة المركبات
            </Typography>
            <Typography variant="body1" color="text.secondary">
              إدارة أسطول المركبات وتتبع حالتها ومواقعها
            </Typography>
          </Box>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <CarIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        {totalVehicles}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        إجمالي المركبات
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <span>🟢</span>
                    </Avatar>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        {activeVehicles}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        المركبات النشطة
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'warning.main' }}>
                      <MaintenanceIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        {maintenanceVehicles}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        في الصيانة
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'info.main' }}>
                      <SpeedIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        {totalMileage.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        إجمالي المسافة (كم)
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Filters and Search */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <TextField
                  placeholder="البحث في المركبات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    label="الحالة"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الحالات</MenuItem>
                    <MenuItem value="ACTIVE">نشط</MenuItem>
                    <MenuItem value="MAINTENANCE">صيانة</MenuItem>
                    <MenuItem value="OFFLINE">غير متصل</MenuItem>
                    <MenuItem value="OUT_OF_SERVICE">خارج الخدمة</MenuItem>
                  </Select>
                </FormControl>
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>الماركة</InputLabel>
                  <Select
                    value={filterMake}
                    label="الماركة"
                    onChange={(e) => setFilterMake(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الماركات</MenuItem>
                    {uniqueMakes.map((make) => (
                      <MenuItem key={make} value={make}>
                        {make}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Button
                  variant="outlined"
                  startIcon={<FilterIcon />}
                >
                  تصفية متقدمة
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setOpenAddDialog(true)}
                >
                  إضافة مركبة
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Data Grid */}
          <Card>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={vehicles}
                columns={columns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                checkboxSelection
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Card>

          {/* Add Vehicle Dialog */}
          <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
            <DialogTitle>إضافة مركبة جديدة</DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="رقم اللوحة"
                      value={newVehicle.plateNumber}
                      onChange={(e) => setNewVehicle({ ...newVehicle, plateNumber: e.target.value })}
                      fullWidth
                      required
                      placeholder="YE-001"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="الماركة"
                      value={newVehicle.make}
                      onChange={(e) => setNewVehicle({ ...newVehicle, make: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="الموديل"
                      value={newVehicle.model}
                      onChange={(e) => setNewVehicle({ ...newVehicle, model: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="السنة"
                      type="number"
                      value={newVehicle.year}
                      onChange={(e) => setNewVehicle({ ...newVehicle, year: parseInt(e.target.value) || 0 })}
                      fullWidth
                      required
                      inputProps={{ min: 1990, max: new Date().getFullYear() + 1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="اللون"
                      value={newVehicle.color}
                      onChange={(e) => setNewVehicle({ ...newVehicle, color: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="السعة (عدد الركاب)"
                      type="number"
                      value={newVehicle.capacity}
                      onChange={(e) => setNewVehicle({ ...newVehicle, capacity: parseInt(e.target.value) || 0 })}
                      fullWidth
                      required
                      inputProps={{ min: 1, max: 50 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="رقم الهيكل (VIN)"
                      value={newVehicle.vin}
                      onChange={(e) => setNewVehicle({ ...newVehicle, vin: e.target.value })}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>نوع الوقود</InputLabel>
                      <Select
                        value={newVehicle.fuelType}
                        label="نوع الوقود"
                        onChange={(e) => setNewVehicle({ ...newVehicle, fuelType: e.target.value as any })}
                      >
                        <MenuItem value="GASOLINE">بنزين</MenuItem>
                        <MenuItem value="DIESEL">ديزل</MenuItem>
                        <MenuItem value="ELECTRIC">كهربائي</MenuItem>
                        <MenuItem value="HYBRID">هجين</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
              <Button onClick={handleAddVehicle} variant="contained">إضافة</Button>
            </DialogActions>
          </Dialog>
        </Box>
      } />
    </Routes>
  );
};

export default VehicleManagement;
