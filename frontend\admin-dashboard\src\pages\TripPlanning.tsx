import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  LinearProgress,
  CircularProgress,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert
} from '@mui/material';
import {
  EventNote as PlanningIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  Route as RouteIcon,
  Person as PersonIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

interface TripPlan {
  id: string;
  name: string;
  type: string;
  status: string;
  startDate: string;
  endDate: string;
  totalTrips: number;
  completedTrips: number;
  assignedDrivers: number;
  assignedVehicles: number;
  estimatedRevenue: number;
  actualRevenue: number;
  efficiency: number;
  onTimePerformance: number;
  customerSatisfaction: number;
}

interface Schedule {
  id: string;
  date: string;
  driverName: string;
  vehiclePlate: string;
  shiftType: string;
  startTime: string;
  endTime: string;
  plannedTrips: number;
  actualTrips: number;
  status: string;
  efficiency: number;
  onTimeRate: number;
  customerRating: number;
}

interface KPIs {
  tripCompletion: { current: number; target: number; trend: string; change: number };
  onTimePerformance: { current: number; target: number; trend: string; change: number };
  customerSatisfaction: { current: number; target: number; trend: string; change: number };
  fuelEfficiency: { current: number; target: number; trend: string; change: number };
  revenuePerTrip: { current: number; target: number; trend: string; change: number };
  driverUtilization: { current: number; target: number; trend: string; change: number };
  vehicleUtilization: { current: number; target: number; trend: string; change: number };
  cancellationRate: { current: number; target: number; trend: string; change: number };
}

const TripPlanning: React.FC = () => {
  const [tripPlans, setTripPlans] = useState<TripPlan[]>([]);
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [kpis, setKpis] = useState<KPIs | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    loadPlanningData();
  }, [filters]);

  const loadPlanningData = async () => {
    try {
      setLoading(true);

      // Load trip plans
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const plansResponse = await fetch(`http://localhost:8104/api/planning/plans?${queryParams}`);
      const plansData = await plansResponse.json();
      
      if (plansData.success) {
        setTripPlans(plansData.data);
      }

      // Load schedules
      const schedulesResponse = await fetch('http://localhost:8104/api/planning/schedules');
      const schedulesData = await schedulesResponse.json();
      
      if (schedulesData.success) {
        setSchedules(schedulesData.data);
      }

      // Load KPIs
      const kpisResponse = await fetch('http://localhost:8104/api/planning/kpis');
      const kpisData = await kpisResponse.json();
      
      if (kpisData.success) {
        setKpis(kpisData.data);
      }

    } catch (error) {
      console.error('Failed to load planning data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'draft': 'default',
      'active': 'primary',
      'completed': 'success',
      'cancelled': 'error',
      'scheduled': 'info'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'draft': 'مسودة',
      'active': 'نشط',
      'completed': 'مكتمل',
      'cancelled': 'ملغي',
      'scheduled': 'مجدول'
    };
    return labels[status] || status;
  };

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'daily': 'يومي',
      'weekly': 'أسبوعي',
      'monthly': 'شهري',
      'custom': 'مخصص'
    };
    return labels[type] || type;
  };

  const getShiftLabel = (shift: string) => {
    const labels: Record<string, string> = {
      'morning': 'صباحي',
      'afternoon': 'بعد الظهر',
      'evening': 'مسائي',
      'night': 'ليلي',
      'split': 'مقسم'
    };
    return labels[shift] || shift;
  };

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? <TrendingUpIcon color="success" /> : <TrendingDownIcon color="error" />;
  };

  const getKPIColor = (current: number, target: number) => {
    const ratio = current / target;
    if (ratio >= 0.95) return 'success';
    if (ratio >= 0.85) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل بيانات التخطيط...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <PlanningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          تخطيط الرحلات والتحليلات
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة لخطط الرحلات والجداول مع تحليلات الأداء
        </Typography>
      </Box>

      {/* KPI Cards */}
      {kpis && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color={getKPIColor(kpis.tripCompletion.current, kpis.tripCompletion.target)}>
                      {kpis.tripCompletion.current.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل إنجاز الرحلات
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      الهدف: {kpis.tripCompletion.target}%
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    {getTrendIcon(kpis.tripCompletion.trend)}
                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                      {kpis.tripCompletion.change > 0 ? '+' : ''}{kpis.tripCompletion.change.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color={getKPIColor(kpis.onTimePerformance.current, kpis.onTimePerformance.target)}>
                      {kpis.onTimePerformance.current.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الأداء في الوقت المحدد
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      الهدف: {kpis.onTimePerformance.target}%
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    {getTrendIcon(kpis.onTimePerformance.trend)}
                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                      {kpis.onTimePerformance.change > 0 ? '+' : ''}{kpis.onTimePerformance.change.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color={getKPIColor(kpis.customerSatisfaction.current, kpis.customerSatisfaction.target)}>
                      {kpis.customerSatisfaction.current.toFixed(1)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      رضا العملاء
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      الهدف: {kpis.customerSatisfaction.target} ⭐
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    {getTrendIcon(kpis.customerSatisfaction.trend)}
                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                      {kpis.customerSatisfaction.change > 0 ? '+' : ''}{kpis.customerSatisfaction.change.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color={getKPIColor(kpis.fuelEfficiency.current, kpis.fuelEfficiency.target)}>
                      {kpis.fuelEfficiency.current.toFixed(1)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      كفاءة الوقود (كم/لتر)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      الهدف: {kpis.fuelEfficiency.target} كم/لتر
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    {getTrendIcon(kpis.fuelEfficiency.trend)}
                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                      {kpis.fuelEfficiency.change > 0 ? '+' : ''}{kpis.fuelEfficiency.change.toFixed(1)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="draft">مسودة</MenuItem>
                <MenuItem value="active">نشط</MenuItem>
                <MenuItem value="completed">مكتمل</MenuItem>
                <MenuItem value="cancelled">ملغي</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>النوع</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="النوع"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="daily">يومي</MenuItem>
                <MenuItem value="weekly">أسبوعي</MenuItem>
                <MenuItem value="monthly">شهري</MenuItem>
                <MenuItem value="custom">مخصص</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="من تاريخ"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="إلى تاريخ"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadPlanningData}
            >
              تحديث
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
            >
              خطة جديدة
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="خطط الرحلات" icon={<PlanningIcon />} />
          <Tab label="الجداول" icon={<ScheduleIcon />} />
          <Tab label="تحليلات الأداء" icon={<AnalyticsIcon />} />
          <Tab label="تحسين المسارات" icon={<RouteIcon />} />
        </Tabs>
      </Paper>

      {/* Trip Plans Tab */}
      {selectedTab === 0 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم الخطة</TableCell>
                  <TableCell>النوع</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ البداية</TableCell>
                  <TableCell>إجمالي الرحلات</TableCell>
                  <TableCell>الرحلات المكتملة</TableCell>
                  <TableCell>الكفاءة</TableCell>
                  <TableCell>الإيرادات</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tripPlans
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((plan) => (
                  <TableRow key={plan.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {plan.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getTypeLabel(plan.type)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(plan.status)}
                        color={getStatusColor(plan.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(plan.startDate).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatNumber(plan.totalTrips)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: 100 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={(plan.completedTrips / plan.totalTrips) * 100}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                        <Typography variant="caption">
                          {formatNumber(plan.completedTrips)} ({((plan.completedTrips / plan.totalTrips) * 100).toFixed(0)}%)
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color={plan.efficiency >= 85 ? 'success.main' : plan.efficiency >= 70 ? 'warning.main' : 'error.main'}>
                        {plan.efficiency.toFixed(1)}%
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(plan.actualRevenue)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        متوقع: {formatCurrency(plan.estimatedRevenue)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Button size="small" startIcon={<ViewIcon />}>
                          عرض
                        </Button>
                        <Button size="small" startIcon={<EditIcon />}>
                          تعديل
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={tripPlans.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
          />
        </Paper>
      )}

      {/* Schedules Tab */}
      {selectedTab === 1 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>التاريخ</TableCell>
                  <TableCell>السائق</TableCell>
                  <TableCell>المركبة</TableCell>
                  <TableCell>نوع المناوبة</TableCell>
                  <TableCell>وقت العمل</TableCell>
                  <TableCell>الرحلات المخططة</TableCell>
                  <TableCell>الرحلات الفعلية</TableCell>
                  <TableCell>الكفاءة</TableCell>
                  <TableCell>التقييم</TableCell>
                  <TableCell>الحالة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {schedules.slice(0, 15).map((schedule) => (
                  <TableRow key={schedule.id}>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(schedule.date).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {schedule.driverName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {schedule.vehiclePlate}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getShiftLabel(schedule.shiftType)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {schedule.startTime} - {schedule.endTime}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {schedule.plannedTrips}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {schedule.actualTrips}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color={schedule.efficiency >= 85 ? 'success.main' : 'warning.main'}>
                        {schedule.efficiency.toFixed(1)}%
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <StarIcon sx={{ color: '#ffc107', fontSize: 16, mr: 0.5 }} />
                        <Typography variant="body2">
                          {schedule.customerRating.toFixed(1)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(schedule.status)}
                        color={getStatusColor(schedule.status)}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Performance Analytics Tab */}
      {selectedTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  اتجاهات الأداء
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    رسم بياني لاتجاهات الأداء (يتطلب مكتبة رسوم بيانية)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  مؤشرات الأداء الرئيسية
                </Typography>
                {kpis && Object.entries(kpis).slice(0, 4).map(([key, value]) => (
                  <Box key={key} sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">
                        {key === 'tripCompletion' ? 'إنجاز الرحلات' :
                         key === 'onTimePerformance' ? 'الأداء في الوقت' :
                         key === 'customerSatisfaction' ? 'رضا العملاء' :
                         key === 'fuelEfficiency' ? 'كفاءة الوقود' : key}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {value.current.toFixed(1)}{key === 'customerSatisfaction' ? '' : '%'}
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.min((value.current / value.target) * 100, 100)}
                      color={getKPIColor(value.current, value.target) as any}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      الهدف: {value.target}{key === 'customerSatisfaction' ? '' : '%'}
                    </Typography>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Route Optimization Tab */}
      {selectedTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              تحسين المسارات
            </Typography>
            <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                أدوات تحسين المسارات والخوارزميات المتقدمة (قيد التطوير)
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Create Plan Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>إنشاء خطة رحلات جديدة</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="اسم الخطة"
                placeholder="أدخل اسم الخطة"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>نوع الخطة</InputLabel>
                <Select label="نوع الخطة">
                  <MenuItem value="daily">يومي</MenuItem>
                  <MenuItem value="weekly">أسبوعي</MenuItem>
                  <MenuItem value="monthly">شهري</MenuItem>
                  <MenuItem value="custom">مخصص</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="تاريخ البداية"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="تاريخ النهاية"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="ملاحظات"
                placeholder="أدخل ملاحظات إضافية"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained">إنشاء الخطة</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TripPlanning;
