import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Rating
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  Speed as SpeedIcon,
  Star as StarIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface DashboardStats {
  totalTrips: number;
  completedTrips: number;
  totalRevenue: number;
  averageRating: number;
  activeDrivers: number;
  activeVehicles: number;
  totalDistance: number;
  averageTripDuration: number;
}

interface RevenueData {
  period: string;
  totalRevenue: number;
  ridesRevenue: number;
  parcelsRevenue: number;
  totalTrips: number;
}

interface DriverStats {
  totalDrivers: number;
  activeDrivers: number;
  averageRating: number;
  topPerformers: Array<{
    driverId: string;
    name: string;
    rating: number;
    totalTrips: number;
    completionRate: number;
  }>;
  driverDistribution: {
    available: number;
    busy: number;
    offline: number;
  };
}

interface VehicleStats {
  totalVehicles: number;
  activeVehicles: number;
  vehicleTypes: Record<string, number>;
  utilizationRate: number;
  maintenanceAlerts: number;
  fuelEfficiency: number;
}

const AnalyticsDashboard: React.FC = () => {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [driverStats, setDriverStats] = useState<DriverStats | null>(null);
  const [vehicleStats, setVehicleStats] = useState<VehicleStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [dateRange, setDateRange] = useState('30');
  const [revenuePeriod, setRevenuePeriod] = useState('daily');

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange, revenuePeriod]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load dashboard stats
      const dashboardResponse = await fetch(`http://localhost:8096/api/analytics/dashboard?dateRange=${dateRange}`);
      const dashboardData = await dashboardResponse.json();
      
      if (dashboardData.success) {
        setDashboardStats(dashboardData.data);
      }

      // Load revenue data
      const revenueResponse = await fetch(`http://localhost:8096/api/analytics/revenue?period=${revenuePeriod}`);
      const revenueResult = await revenueResponse.json();
      
      if (revenueResult.success) {
        setRevenueData(revenueResult.data);
      }

      // Load driver stats
      const driverResponse = await fetch('http://localhost:8096/api/analytics/drivers');
      const driverResult = await driverResponse.json();
      
      if (driverResult.success) {
        setDriverStats(driverResult.data);
      }

      // Load vehicle stats
      const vehicleResponse = await fetch('http://localhost:8096/api/analytics/vehicles');
      const vehicleResult = await vehicleResponse.json();
      
      if (vehicleResult.success) {
        setVehicleStats(vehicleResult.data);
      }

    } catch (err) {
      console.error('Failed to load analytics data:', err);
      setError('فشل في تحميل بيانات التحليلات');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getVehicleTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      sedan: 'سيدان',
      suv: 'دفع رباعي',
      van: 'فان',
      motorcycle: 'دراجة نارية'
    };
    return labels[type] || type;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل التحليلات...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={loadAnalyticsData}>
            إعادة المحاولة
          </Button>
        }>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          لوحة التحليلات
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          تحليلات شاملة للأداء والإحصائيات المتقدمة
        </Typography>
      </Box>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>فترة البيانات</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                label="فترة البيانات"
              >
                <MenuItem value="7">آخر 7 أيام</MenuItem>
                <MenuItem value="30">آخر 30 يوم</MenuItem>
                <MenuItem value="90">آخر 90 يوم</MenuItem>
                <MenuItem value="365">آخر سنة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>فترة الإيرادات</InputLabel>
              <Select
                value={revenuePeriod}
                onChange={(e) => setRevenuePeriod(e.target.value)}
                label="فترة الإيرادات"
              >
                <MenuItem value="daily">يومي</MenuItem>
                <MenuItem value="weekly">أسبوعي</MenuItem>
                <MenuItem value="monthly">شهري</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadAnalyticsData}
            >
              تحديث البيانات
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Dashboard Stats Cards */}
      {dashboardStats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CarIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatNumber(dashboardStats.totalTrips)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الرحلات
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      مكتمل: {formatNumber(dashboardStats.completedTrips)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(dashboardStats.totalRevenue)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الإيرادات
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      متوسط الرحلة: {formatCurrency(dashboardStats.totalRevenue / dashboardStats.totalTrips)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PersonIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{dashboardStats.activeDrivers}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      السائقون النشطون
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <StarIcon sx={{ fontSize: 16, color: 'warning.main', mr: 0.5 }} />
                      <Typography variant="caption">
                        {dashboardStats.averageRating.toFixed(1)}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SpeedIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatNumber(dashboardStats.totalDistance)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المسافة (كم)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      متوسط الرحلة: {(dashboardStats.averageTripDuration / 60).toFixed(1)} ساعة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs for Different Analytics */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="الإيرادات" icon={<TrendingUpIcon />} />
          <Tab label="السائقون" icon={<PersonIcon />} />
          <Tab label="المركبات" icon={<CarIcon />} />
          <Tab label="الأداء" icon={<BarChartIcon />} />
        </Tabs>
      </Paper>

      {/* Revenue Tab */}
      {selectedTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  اتجاه الإيرادات
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    رسم بياني للإيرادات (يتطلب مكتبة رسوم بيانية)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توزيع الإيرادات
                </Typography>
                {revenueData.length > 0 && (
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      آخر {revenueData.length} فترة
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={75} 
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption">
                      رحلات الركاب: 75%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={25} 
                      color="secondary"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption">
                      توصيل الطرود: 25%
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Drivers Tab */}
      {selectedTab === 1 && driverStats && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  إحصائيات السائقين
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">إجمالي السائقين</Typography>
                  <Typography variant="h4">{driverStats.totalDrivers}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">السائقون النشطون</Typography>
                  <Typography variant="h5" color="success.main">
                    {driverStats.activeDrivers}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2">متوسط التقييم</Typography>
                  <Box display="flex" alignItems="center">
                    <Rating value={driverStats.averageRating} readOnly precision={0.1} />
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      ({driverStats.averageRating.toFixed(1)})
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  أفضل السائقين أداءً
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>السائق</TableCell>
                        <TableCell>التقييم</TableCell>
                        <TableCell>الرحلات</TableCell>
                        <TableCell>معدل الإنجاز</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {driverStats.topPerformers.slice(0, 5).map((driver) => (
                        <TableRow key={driver.driverId}>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                                {driver.name.charAt(0)}
                              </Avatar>
                              {driver.name}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <StarIcon sx={{ fontSize: 16, color: 'warning.main', mr: 0.5 }} />
                              {driver.rating.toFixed(1)}
                            </Box>
                          </TableCell>
                          <TableCell>{driver.totalTrips}</TableCell>
                          <TableCell>
                            <Chip 
                              label={`${(driver.completionRate * 100).toFixed(1)}%`}
                              color={driver.completionRate > 0.9 ? 'success' : 'warning'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Vehicles Tab */}
      {selectedTab === 2 && vehicleStats && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  إحصائيات المركبات
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">إجمالي المركبات</Typography>
                    <Typography variant="h4">{vehicleStats.totalVehicles}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">المركبات النشطة</Typography>
                    <Typography variant="h4" color="success.main">
                      {vehicleStats.activeVehicles}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">معدل الاستخدام</Typography>
                    <Typography variant="h5">
                      {(vehicleStats.utilizationRate * 100).toFixed(1)}%
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">تنبيهات الصيانة</Typography>
                    <Typography variant="h5" color="warning.main">
                      {vehicleStats.maintenanceAlerts}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توزيع أنواع المركبات
                </Typography>
                {Object.entries(vehicleStats.vehicleTypes).map(([type, count]) => (
                  <Box key={type} sx={{ mb: 1 }}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2">{getVehicleTypeLabel(type)}</Typography>
                      <Typography variant="body2">{count}</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={(count / vehicleStats.totalVehicles) * 100}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Performance Tab */}
      {selectedTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  مؤشرات الأداء الرئيسية
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  مؤشرات الأداء التفصيلية (يتطلب تطوير إضافي)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default AnalyticsDashboard;
