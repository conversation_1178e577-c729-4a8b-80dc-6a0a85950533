import React, { useState, useEffect } from 'react';
import SimpleInteractiveMap from '../components/SimpleInteractiveMap';
import { 
  Card, 
  CardContent,
  CardHeader,
  Grid, 
  Typography,
  Button, 
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import { 
  DirectionsCar as CarIcon, 
  LocationOn as LocationIcon, 
  Schedule as ClockIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Download as DownloadIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';

const SimpleInteractiveMapPage = () => {
  const [mapStats, setMapStats] = useState({
    activeVehicles: 0,
    totalRoutes: 0,
    trafficAlerts: 0,
    geofenceViolations: 0
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);

  // Fetch map statistics
  useEffect(() => {
    const fetchMapStats = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8085'}/api/map/stats`);
        if (response.ok) {
          const data = await response.json();
          setMapStats(data);
          setLastUpdate(new Date());
        }
      } catch (error) {
        console.error('Error fetching map stats:', error);
        // Set mock data for demo
        setMapStats({
          activeVehicles: 25,
          totalRoutes: 8,
          trafficAlerts: 3,
          geofenceViolations: 1
        });
        setLastUpdate(new Date());
      }
    };

    fetchMapStats();

    // Auto refresh every 30 seconds
    const interval = autoRefresh ? setInterval(fetchMapStats, 30000) : null;

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle export map data
  const exportMapData = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8085'}/api/map/export`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `map-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log('Map data exported successfully');
      }
    } catch (error) {
      console.error('Error exporting map data:', error);
    }
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Page Header */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LocationIcon color="primary" />
              <Typography variant="h5" component="h1">
                الخرائط التفاعلية
              </Typography>
            </Box>
          }
          subheader="تتبع المركبات والطرق في الوقت الفعلي"
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                startIcon={<RefreshIcon />} 
                onClick={() => window.location.reload()}
                variant="outlined"
                size="small"
              >
                تحديث
              </Button>
              
              <IconButton onClick={handleMenuClick}>
                <MoreIcon />
              </IconButton>
              
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={() => { setAutoRefresh(!autoRefresh); handleMenuClose(); }}>
                  {autoRefresh ? 'إيقاف التحديث التلقائي' : 'تفعيل التحديث التلقائي'}
                </MenuItem>
                <MenuItem onClick={() => { exportMapData(); handleMenuClose(); }}>
                  تصدير بيانات الخريطة
                </MenuItem>
                <MenuItem onClick={() => { toggleFullscreen(); handleMenuClose(); }}>
                  ملء الشاشة
                </MenuItem>
              </Menu>
            </Box>
          }
        />
      </Card>

      {/* Statistics Cards */}
      {!isFullscreen && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CarIcon sx={{ fontSize: 40, color: '#1976d2' }} />
                  <Box>
                    <Typography variant="h4" component="div" color="#1976d2">
                      {mapStats.activeVehicles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      المركبات النشطة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <LocationIcon sx={{ fontSize: 40, color: '#2e7d32' }} />
                  <Box>
                    <Typography variant="h4" component="div" color="#2e7d32">
                      {mapStats.totalRoutes}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الطرق النشطة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <WarningIcon sx={{ fontSize: 40, color: '#ed6c02' }} />
                  <Box>
                    <Typography variant="h4" component="div" color="#ed6c02">
                      {mapStats.trafficAlerts}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      تنبيهات المرور
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <ClockIcon sx={{ fontSize: 40, color: '#d32f2f' }} />
                  <Box>
                    <Typography variant="h4" component="div" color="#d32f2f">
                      {mapStats.geofenceViolations}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      انتهاكات المناطق
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Interactive Map */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
              <Typography variant="h6">الخريطة التفاعلية</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
                </Typography>
                <Chip 
                  label={autoRefresh ? '🟢 تحديث تلقائي' : '🔴 تحديث يدوي'}
                  size="small"
                  color={autoRefresh ? 'success' : 'error'}
                  variant="outlined"
                />
              </Box>
            </Box>
          }
        />
        <CardContent sx={{ p: 0 }}>
          <SimpleInteractiveMap />
        </CardContent>
      </Card>

      {/* Map Legend */}
      {!isFullscreen && (
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="دليل الخريطة" />
              <CardContent>
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Box sx={{ fontSize: '16px' }}>🚗</Box>
                      <Typography variant="body2">مركبة نشطة</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Box sx={{ fontSize: '16px' }}>🚦</Box>
                      <Typography variant="body2">حركة المرور</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Box sx={{ fontSize: '16px' }}>📍</Box>
                      <Typography variant="body2">موقع مركزي</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Box sx={{ width: '16px', height: '2px', backgroundColor: 'blue' }}></Box>
                      <Typography variant="body2">طريق نشط</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="إجراءات سريعة" />
              <CardContent>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Button 
                    startIcon={<CarIcon />} 
                    variant="outlined" 
                    size="small"
                  >
                    إضافة مركبة جديدة
                  </Button>
                  <Button 
                    startIcon={<LocationIcon />} 
                    variant="outlined" 
                    size="small"
                  >
                    إنشاء منطقة محددة
                  </Button>
                  <Button 
                    startIcon={<WarningIcon />} 
                    variant="outlined" 
                    size="small"
                  >
                    عرض التنبيهات
                  </Button>
                  <Button 
                    startIcon={<DownloadIcon />} 
                    variant="outlined" 
                    size="small"
                    onClick={exportMapData}
                  >
                    تصدير البيانات
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default SimpleInteractiveMapPage;
