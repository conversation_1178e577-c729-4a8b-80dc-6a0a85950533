package com.tecnodrive.analyticsservice.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Report Response DTO
 * 
 * Used for returning generated report data to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportResponse {

    /**
     * Report metadata
     */
    private ReportMetadata metadata;

    /**
     * Report data
     */
    private Map<String, Object> data;

    /**
     * Summary statistics
     */
    private Map<String, Object> summary;

    /**
     * Chart data (if requested)
     */
    private List<ChartData> charts;

    /**
     * Pagination information
     */
    private PaginationInfo pagination;

    /**
     * Report Metadata
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ReportMetadata {
        private String reportId;
        private ReportRequest.ReportType reportType;
        private String title;
        private String description;
        private LocalDate startDate;
        private LocalDate endDate;
        private String period;
        private String companyId;
        private String requestedBy;
        private Instant generatedAt;
        private long executionTimeMs;
        private int totalRecords;
        private ReportRequest.Format format;
    }

    /**
     * Chart Data
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChartData {
        private String chartType; // bar, line, pie, area, etc.
        private String title;
        private String xAxisLabel;
        private String yAxisLabel;
        private List<DataPoint> dataPoints;
        private Map<String, Object> options;
    }

    /**
     * Data Point for Charts
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DataPoint {
        private String label;
        private Object value;
        private String color;
        private Map<String, Object> metadata;
    }

    /**
     * Pagination Information
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PaginationInfo {
        private int currentPage;
        private int pageSize;
        private long totalElements;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;
    }

    /**
     * Financial Summary Data
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FinancialSummary {
        private double totalRevenue;
        private double totalExpenses;
        private double netProfit;
        private double profitMargin;
        private double averageTransactionValue;
        private long totalTransactions;
        private Map<String, Double> revenueByCategory;
        private Map<String, Double> expensesByCategory;
    }

    /**
     * Operational Metrics Data
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OperationalMetrics {
        private long totalRides;
        private long totalDeliveries;
        private double averageRideDistance;
        private double averageRideDuration;
        private double customerSatisfactionScore;
        private long activeDrivers;
        private long activeVehicles;
        private double vehicleUtilizationRate;
        private Map<String, Long> ridesByHour;
        private Map<String, Long> ridesByDay;
    }

    /**
     * User Analytics Data
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserAnalytics {
        private long totalUsers;
        private long activeUsers;
        private long newUsers;
        private double userRetentionRate;
        private double averageSessionDuration;
        private Map<String, Long> usersByRegion;
        private Map<String, Long> usersByAge;
        private Map<String, Double> engagementMetrics;
    }

    // Getters
    public ReportMetadata getMetadata() { return metadata; }
    public Map<String, Object> getSummary() { return summary; }
    public Map<String, Object> getData() { return data; }

    // Setters
    public void setMetadata(ReportMetadata metadata) { this.metadata = metadata; }
    public void setSummary(Map<String, Object> summary) { this.summary = summary; }
    public void setData(Map<String, Object> data) { this.data = data; }

    // Builder pattern support
    public static ReportResponseBuilder builder() {
        return new ReportResponseBuilder();
    }

    public static class ReportResponseBuilder {
        private ReportResponse response = new ReportResponse();

        public ReportResponseBuilder metadata(ReportMetadata metadata) {
            response.metadata = metadata;
            return this;
        }

        public ReportResponseBuilder summary(Map<String, Object> summary) {
            response.summary = summary;
            return this;
        }

        public ReportResponseBuilder data(Map<String, Object> data) {
            response.data = data;
            return this;
        }

        public ReportResponse build() {
            return response;
        }
    }
}
