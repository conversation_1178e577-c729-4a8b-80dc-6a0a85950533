import {
  RouteDefinitionDto,
  RateLimitConfigDto,
  CreateConsumerRequest,
  UpdateRouteRequest,
  ApiConsumerDto,
  ApiResponse
} from '../types/api';
import { createMockResponse, simulateApiDelay, generateMockId } from '../services/api';

// Mock Routes Data
const mockRoutes: RouteDefinitionDto[] = [
  {
    id: 'auth-service',
    uri: 'lb://auth-service',
    predicates: ['Path=/api/auth/**'],
    filters: ['StripPrefix=1'],
    authMethod: 'NONE',
    enabled: true,
    order: 1,
    metadata: { description: 'Authentication service routes' },
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'fleet-service',
    uri: 'lb://fleet-service',
    predicates: ['Path=/api/fleet/**'],
    filters: ['StripPrefix=1', 'RequestRateLimiter'],
    authMethod: 'JWT',
    enabled: true,
    order: 2,
    metadata: { description: 'Fleet management service routes' },
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'rides-service',
    uri: 'lb://rides-service',
    predicates: ['Path=/api/rides/**'],
    filters: ['StripPrefix=1', 'RequestRateLimiter'],
    authMethod: 'JWT',
    enabled: true,
    order: 3,
    metadata: { description: 'Rides management service routes' },
    createdAt: '2024-02-15T10:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'saas-service',
    uri: 'lb://saas-service',
    predicates: ['Path=/api/saas/**'],
    filters: ['StripPrefix=1', 'RequestRateLimiter'],
    authMethod: 'JWT',
    enabled: true,
    order: 4,
    metadata: { description: 'SaaS management service routes' },
    createdAt: '2024-03-01T11:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'notifications-service',
    uri: 'lb://notifications-service',
    predicates: ['Path=/api/notifications/**'],
    filters: ['StripPrefix=1'],
    authMethod: 'JWT',
    enabled: true,
    order: 5,
    metadata: { description: 'Notifications service routes' },
    createdAt: '2024-03-15T12:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'external-api',
    uri: 'lb://external-api-service',
    predicates: ['Path=/api/external/**'],
    filters: ['StripPrefix=1', 'RequestRateLimiter'],
    authMethod: 'API_KEY',
    enabled: true,
    order: 6,
    metadata: { description: 'External API access routes' },
    createdAt: '2024-04-01T13:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
];

// Mock Auth Methods Data
const mockAuthMethods: AuthMethodDto[] = [
  {
    id: 'jwt-default',
    name: 'JWT Default',
    type: 'JWT',
    config: {
      jwkSetUri: 'http://auth-service/.well-known/jwks.json',
      issuer: 'tecno-drive-auth',
      audience: 'tecno-drive-api',
    },
    enabled: true,
    description: 'Default JWT authentication method',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'api-key-external',
    name: 'API Key for External Apps',
    type: 'API_KEY',
    config: {
      headerName: 'X-API-KEY',
      queryParamName: 'api_key',
      validateInDatabase: true,
    },
    enabled: true,
    description: 'API Key authentication for external applications',
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'oauth2-google',
    name: 'Google OAuth2',
    type: 'OAUTH2',
    config: {
      clientId: 'google-client-id',
      clientSecret: 'google-client-secret',
      authorizationUri: 'https://accounts.google.com/o/oauth2/auth',
      tokenUri: 'https://oauth2.googleapis.com/token',
      userInfoUri: 'https://www.googleapis.com/oauth2/v2/userinfo',
      scopes: ['openid', 'profile', 'email'],
    },
    enabled: true,
    description: 'Google OAuth2 authentication',
    createdAt: '2024-03-01T10:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'basic-auth-internal',
    name: 'Basic Auth Internal',
    type: 'BASIC',
    config: {
      realm: 'TecnoDrive Internal',
      userDetailsService: 'databaseUserDetailsService',
    },
    enabled: false,
    description: 'Basic authentication for internal services',
    createdAt: '2024-04-01T11:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
];

// Mock API Consumers Data
const mockConsumers: ApiConsumerDto[] = [
  {
    id: 'mobile-app',
    appName: 'TecnoDrive Mobile App',
    clientId: 'mobile-app-client-id',
    clientSecret: 'mobile-app-secret-key',
    callbackUrl: 'https://mobile.tecno-drive.com/callback',
    authType: 'JWT',
    scopes: ['rides.read', 'rides.write', 'fleet.read', 'profile.read'],
    rateLimitRpm: 1000,
    enabled: true,
    lastUsed: '2025-07-09T14:25:00Z',
    totalRequests: 125000,
    createdAt: '2024-01-20T08:00:00Z',
    updatedAt: '2025-07-09T14:25:00Z',
  },
  {
    id: 'web-dashboard',
    appName: 'Web Admin Dashboard',
    clientId: 'web-dashboard-client-id',
    clientSecret: 'web-dashboard-secret-key',
    callbackUrl: 'https://admin.tecno-drive.com/callback',
    authType: 'JWT',
    scopes: ['*'],
    rateLimitRpm: 500,
    enabled: true,
    lastUsed: '2025-07-09T14:30:00Z',
    totalRequests: 89000,
    createdAt: '2024-01-25T09:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'partner-api',
    appName: 'Partner Integration API',
    clientId: 'partner-api-client-id',
    clientSecret: 'partner-api-secret-key',
    authType: 'API_KEY',
    scopes: ['rides.read', 'fleet.read'],
    rateLimitRpm: 200,
    enabled: true,
    lastUsed: '2025-07-09T13:45:00Z',
    totalRequests: 45000,
    createdAt: '2024-03-10T10:00:00Z',
    updatedAt: '2025-07-09T13:45:00Z',
  },
  {
    id: 'analytics-service',
    appName: 'Analytics Service',
    clientId: 'analytics-service-client-id',
    clientSecret: 'analytics-service-secret-key',
    authType: 'JWT',
    scopes: ['analytics.read', 'metrics.read'],
    rateLimitRpm: 100,
    enabled: true,
    lastUsed: '2025-07-09T14:00:00Z',
    totalRequests: 23000,
    createdAt: '2024-04-15T11:00:00Z',
    updatedAt: '2025-07-09T14:00:00Z',
  },
];

// Mock Rate Limits Data
const mockRateLimits: RateLimitConfigDto[] = [
  {
    id: 'global-default',
    name: 'Global Default Limit',
    tier: 'Standard',
    consumerIdOrIp: '*',
    requestsPerMinute: 1000,
    requestsPerHour: 60000,
    requestsPerDay: 1440000,
    burstCapacity: 2000,
    enabled: true,
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'mobile-app-limit',
    name: 'Mobile App Limit',
    tier: 'Premium',
    consumerIdOrIp: 'mobile-app',
    requestsPerMinute: 1000,
    requestsPerHour: 50000,
    requestsPerDay: 1200000,
    burstCapacity: 1500,
    enabled: true,
    createdAt: '2024-01-20T08:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: 'partner-api-limit',
    consumerIdOrIp: 'partner-api',
    requestsPerMinute: 200,
    burstCapacity: 300,
    enabled: true,
    createdAt: '2024-03-10T10:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
];

// Mock Gateway Metrics Data
const mockGatewayMetrics: GatewayMetricsDto = {
  totalRequests: 1250000,
  successfulRequests: 1187500,
  failedRequests: 62500,
  averageResponseTime: 245,
  requestsPerMinute: 2500,
  topEndpoints: [
    { path: '/api/rides/**', requests: 450000, avgResponseTime: 180 },
    { path: '/api/auth/**', requests: 320000, avgResponseTime: 120 },
    { path: '/api/fleet/**', requests: 280000, avgResponseTime: 300 },
    { path: '/api/saas/**', requests: 150000, avgResponseTime: 220 },
    { path: '/api/notifications/**', requests: 50000, avgResponseTime: 90 },
  ],
  errorRates: [
    { service: 'rides-service', errorRate: 2.1 },
    { service: 'fleet-service', errorRate: 1.8 },
    { service: 'auth-service', errorRate: 0.5 },
    { service: 'saas-service', errorRate: 3.2 },
    { service: 'notifications-service', errorRate: 1.2 },
  ],
  period: 'last_24_hours',
};

// Mock API Gateway Data Service
export const mockApiGatewayData = {
  // Routes Management
  async getRoutes(): Promise<ApiResponse<RouteDefinitionDto[]>> {
    await simulateApiDelay();
    return createMockResponse([...mockRoutes]);
  },

  async getRouteById(routeId: string): Promise<ApiResponse<RouteDefinitionDto>> {
    await simulateApiDelay();
    const route = mockRoutes.find(r => r.id === routeId);
    
    if (!route) {
      throw new Error('Route not found');
    }
    
    return createMockResponse(route);
  },

  async createRoute(routeData: CreateRouteRequest): Promise<ApiResponse<RouteDefinitionDto>> {
    await simulateApiDelay();
    
    const newRoute: RouteDefinitionDto = {
      ...routeData,
      enabled: true,
      order: routeData.order || mockRoutes.length + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockRoutes.push(newRoute);
    return createMockResponse(newRoute);
  },

  async updateRoute(routeId: string, routeData: Partial<CreateRouteRequest>): Promise<ApiResponse<RouteDefinitionDto>> {
    await simulateApiDelay();
    
    const routeIndex = mockRoutes.findIndex(r => r.id === routeId);
    if (routeIndex === -1) {
      throw new Error('Route not found');
    }
    
    const updatedRoute = {
      ...mockRoutes[routeIndex],
      ...routeData,
      updatedAt: new Date().toISOString(),
    };
    
    mockRoutes[routeIndex] = updatedRoute;
    return createMockResponse(updatedRoute);
  },

  async deleteRoute(routeId: string): Promise<ApiResponse<void>> {
    await simulateApiDelay();
    
    const routeIndex = mockRoutes.findIndex(r => r.id === routeId);
    if (routeIndex === -1) {
      throw new Error('Route not found');
    }
    
    mockRoutes.splice(routeIndex, 1);
    return createMockResponse(undefined);
  },

  // Auth Methods Management
  async getAuthMethods(): Promise<ApiResponse<AuthMethodDto[]>> {
    await simulateApiDelay();
    return createMockResponse([...mockAuthMethods]);
  },

  async createAuthMethod(authMethodData: CreateAuthMethodRequest): Promise<ApiResponse<AuthMethodDto>> {
    await simulateApiDelay();
    
    const newAuthMethod: AuthMethodDto = {
      id: generateMockId(),
      ...authMethodData,
      enabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockAuthMethods.push(newAuthMethod);
    return createMockResponse(newAuthMethod);
  },

  async updateAuthMethod(authMethodId: string, authMethodData: Partial<CreateAuthMethodRequest>): Promise<ApiResponse<AuthMethodDto>> {
    await simulateApiDelay();
    
    const authMethodIndex = mockAuthMethods.findIndex(a => a.id === authMethodId);
    if (authMethodIndex === -1) {
      throw new Error('Auth method not found');
    }
    
    const updatedAuthMethod = {
      ...mockAuthMethods[authMethodIndex],
      ...authMethodData,
      updatedAt: new Date().toISOString(),
    };
    
    mockAuthMethods[authMethodIndex] = updatedAuthMethod;
    return createMockResponse(updatedAuthMethod);
  },

  // API Consumers Management
  async getConsumers(): Promise<ApiResponse<ApiConsumerDto[]>> {
    await simulateApiDelay();
    return createMockResponse([...mockConsumers]);
  },

  async createConsumer(consumerData: CreateConsumerRequest): Promise<ApiResponse<ApiConsumerDto>> {
    await simulateApiDelay();
    
    const newConsumer: ApiConsumerDto = {
      id: generateMockId(),
      ...consumerData,
      clientId: `${consumerData.appName.toLowerCase().replace(/\s+/g, '-')}-${generateMockId()}`,
      clientSecret: `secret-${generateMockId()}`,
      rateLimitRpm: consumerData.rateLimitRpm || 100,
      enabled: true,
      totalRequests: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockConsumers.push(newConsumer);
    return createMockResponse(newConsumer);
  },

  async updateConsumer(consumerId: string, consumerData: Partial<CreateConsumerRequest>): Promise<ApiResponse<ApiConsumerDto>> {
    await simulateApiDelay();
    
    const consumerIndex = mockConsumers.findIndex(c => c.id === consumerId);
    if (consumerIndex === -1) {
      throw new Error('Consumer not found');
    }
    
    const updatedConsumer = {
      ...mockConsumers[consumerIndex],
      ...consumerData,
      updatedAt: new Date().toISOString(),
    };
    
    mockConsumers[consumerIndex] = updatedConsumer;
    return createMockResponse(updatedConsumer);
  },

  async regenerateConsumerCredentials(consumerId: string): Promise<ApiResponse<{ clientId: string; clientSecret: string }>> {
    await simulateApiDelay();
    
    const consumerIndex = mockConsumers.findIndex(c => c.id === consumerId);
    if (consumerIndex === -1) {
      throw new Error('Consumer not found');
    }
    
    const newCredentials = {
      clientId: `${mockConsumers[consumerIndex].appName.toLowerCase().replace(/\s+/g, '-')}-${generateMockId()}`,
      clientSecret: `secret-${generateMockId()}`,
    };
    
    mockConsumers[consumerIndex] = {
      ...mockConsumers[consumerIndex],
      ...newCredentials,
      updatedAt: new Date().toISOString(),
    };
    
    return createMockResponse(newCredentials);
  },

  // Gateway Metrics
  async getGatewayMetrics(period: string): Promise<ApiResponse<GatewayMetricsDto>> {
    await simulateApiDelay();
    
    const metrics = {
      ...mockGatewayMetrics,
      period,
    };
    
    return createMockResponse(metrics);
  },

  // Rate Limits Management
  async getRateLimits(): Promise<ApiResponse<RateLimitConfigDto[]>> {
    await simulateApiDelay();
    return createMockResponse([...mockRateLimits]);
  },

  async createRateLimit(rateLimitData: Omit<RateLimitConfigDto, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<RateLimitConfigDto>> {
    await simulateApiDelay();
    
    const newRateLimit: RateLimitConfigDto = {
      id: generateMockId(),
      ...rateLimitData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockRateLimits.push(newRateLimit);
    return createMockResponse(newRateLimit);
  },

  // Gateway Health
  async getGatewayHealth(): Promise<ApiResponse<{ status: string; services: Array<{ name: string; status: string; responseTime: number }> }>> {
    await simulateApiDelay();
    
    const healthData = {
      status: 'UP',
      services: [
        { name: 'auth-service', status: 'UP', responseTime: 45 },
        { name: 'fleet-service', status: 'UP', responseTime: 78 },
        { name: 'rides-service', status: 'UP', responseTime: 62 },
        { name: 'saas-service', status: 'UP', responseTime: 89 },
        { name: 'notifications-service', status: 'UP', responseTime: 34 },
      ],
    };
    
    return createMockResponse(healthData);
  },
};
