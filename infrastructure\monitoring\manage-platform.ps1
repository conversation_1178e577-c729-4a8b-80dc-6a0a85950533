#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Advanced Management Script
# This script provides comprehensive platform management capabilities

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "cleanup", "backup", "restore")]
    [string]$Action = "status"
)

Write-Host "🚀 TECNO DRIVE Platform Manager" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Cyan

# Service definitions
$BackendServices = @(
    @{Name="postgres"; Container="infra-postgres-1"; Port=5432},
    @{Name="redis"; Container="infra-redis-1"; Port=6379},
    @{Name="eureka-server"; Container="infra-eureka-server-1"; Port=8761},
    @{Name="auth-service"; Container="auth-service-fixed"; Port=8081},
    @{Name="parcel-service"; Container="infra-parcel-service-1"; Port=8084},
    @{Name="location-service"; Container="infra-location-service-1"; Port=8086},
    @{Name="api-gateway"; Container="api-gateway-fixed"; Port=8080}
)

$FrontendServices = @(
    @{Name="admin-dashboard"; Port=3000; Path="frontend/admin-dashboard"},
    @{Name="hr-frontend"; Port=3002; Path="frontend/hr-frontend"}
)

function Start-AllServices {
    Write-Host "🔄 Starting all services..." -ForegroundColor Blue
    
    # Start backend services
    foreach ($service in $BackendServices) {
        Write-Host "Starting $($service.Name)..." -ForegroundColor Yellow
        docker start $service.Container 2>$null
    }
    
    Write-Host "✅ All backend services started!" -ForegroundColor Green
}

function Stop-AllServices {
    Write-Host "🛑 Stopping all services..." -ForegroundColor Blue
    
    # Stop backend services
    foreach ($service in $BackendServices) {
        Write-Host "Stopping $($service.Name)..." -ForegroundColor Yellow
        docker stop $service.Container 2>$null
    }
    
    Write-Host "✅ All services stopped!" -ForegroundColor Green
}

function Get-ServiceStatus {
    Write-Host "📊 Service Status Report:" -ForegroundColor Blue
    Write-Host ""
    
    foreach ($service in $BackendServices) {
        $status = docker inspect --format='{{.State.Status}}' $service.Container 2>$null
        if ($status -eq "running") {
            Write-Host "✅ $($service.Name) - Running (Port $($service.Port))" -ForegroundColor Green
        } else {
            Write-Host "❌ $($service.Name) - $status" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "🌐 Frontend Services:" -ForegroundColor Blue
    foreach ($service in $FrontendServices) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)" -UseBasicParsing -TimeoutSec 3 -ErrorAction Stop
            Write-Host "✅ $($service.Name) - Running (Port $($service.Port))" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ $($service.Name) - Not responding" -ForegroundColor Red
        }
    }
}

function Show-ServiceLogs {
    Write-Host "📋 Recent Service Logs:" -ForegroundColor Blue
    
    foreach ($service in $BackendServices) {
        Write-Host ""
        Write-Host "--- $($service.Name) Logs ---" -ForegroundColor Cyan
        docker logs --tail 5 $service.Container 2>$null
    }
}

function Cleanup-Platform {
    Write-Host "🧹 Cleaning up platform..." -ForegroundColor Blue
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    Write-Host "✅ Cleanup completed!" -ForegroundColor Green
}

# Execute action
switch ($Action) {
    "start" { Start-AllServices }
    "stop" { Stop-AllServices }
    "restart" { 
        Stop-AllServices
        Start-Sleep -Seconds 5
        Start-AllServices
    }
    "status" { Get-ServiceStatus }
    "logs" { Show-ServiceLogs }
    "cleanup" { Cleanup-Platform }
    default { Get-ServiceStatus }
}

Write-Host ""
Write-Host "🔗 Quick Access URLs:" -ForegroundColor Cyan
Write-Host "• Admin Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host "• HR Frontend: http://localhost:3002" -ForegroundColor White
Write-Host "• Eureka Dashboard: http://localhost:8761" -ForegroundColor White
Write-Host "• API Gateway: http://localhost:8080" -ForegroundColor White
