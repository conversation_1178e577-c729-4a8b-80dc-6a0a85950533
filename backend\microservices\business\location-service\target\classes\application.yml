server:
  port: 8085

spring:
  application:
    name: location-service
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_location
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
    database-platform: org.hibernate.dialect.PostgreSQLDialect
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus

# TecnoDrive Configuration
tecnodrive:
  maps:
    default-provider: openstreetmap
    google:
      api-key: ${GOOGLE_MAPS_API_KEY:}
    mapbox:
      access-token: ${MAPBOX_ACCESS_TOKEN:}
    osm:
      tile-server: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
      nominatim-url: https://nominatim.openstreetmap.org
      osrm-url: https://router.project-osrm.org
    default-center:
      lat: 24.7136
      lng: 46.6753
    default-zoom: 12
    max-zoom: 19

logging:
  level:
    com.tecnodrive.locationservice: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/location-service.log
