# TECNODRIVE Platform Startup Script
# This script starts the entire platform in the correct order

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [switch]$BuildImages = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SetupDatabase = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipHealthCheck = $false
)

# Function to log messages
function Write-StartupLog {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Type) {
        "INFO" { "White" }
        "SUCCESS" { "Green" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "STEP" { "Cyan" }
    }
    
    Write-Host "[$timestamp] [$Type] $Message" -ForegroundColor $color
}

# Function to check if service is healthy
function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$HealthUrl,
        [int]$MaxRetries = 30,
        [int]$RetryInterval = 10
    )
    
    Write-StartupLog "Checking health of $ServiceName..." "INFO"
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $HealthUrl -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-StartupLog "$ServiceName is healthy!" "SUCCESS"
                return $true
            }
        }
        catch {
            Write-StartupLog "Attempt $i/$MaxRetries - $ServiceName not ready yet..." "WARNING"
        }
        
        Start-Sleep -Seconds $RetryInterval
    }
    
    Write-StartupLog "$ServiceName failed health check after $MaxRetries attempts" "ERROR"
    return $false
}

# Function to wait for database
function Wait-ForDatabase {
    Write-StartupLog "Waiting for PostgreSQL to be ready..." "INFO"
    
    $maxRetries = 30
    for ($i = 1; $i -le $maxRetries; $i++) {
        try {
            $env:PGPASSWORD = "postgres"
            $result = psql -h localhost -p 5432 -U postgres -d postgres -c "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-StartupLog "PostgreSQL is ready!" "SUCCESS"
                return $true
            }
        }
        catch {
            Write-StartupLog "Attempt $i/$maxRetries - PostgreSQL not ready yet..." "WARNING"
        }
        
        Start-Sleep -Seconds 5
    }
    
    Write-StartupLog "PostgreSQL failed to start after $maxRetries attempts" "ERROR"
    return $false
}

# Main startup sequence
try {
    Write-StartupLog "🚀 Starting TECNODRIVE Platform..." "STEP"
    Write-StartupLog "Environment: $Environment" "INFO"
    
    # Step 1: Check prerequisites
    Write-StartupLog "📋 Checking prerequisites..." "STEP"
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-StartupLog "Docker is not installed or not in PATH" "ERROR"
        exit 1
    }
    
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-StartupLog "Docker Compose is not installed or not in PATH" "ERROR"
        exit 1
    }
    
    # Step 2: Create environment file if it doesn't exist
    if (-not (Test-Path ".env")) {
        Write-StartupLog "Creating .env file from template..." "INFO"
        Copy-Item ".env.example" ".env"
        Write-StartupLog "⚠️  Please review and update .env file with your settings" "WARNING"
    }
    
    # Step 3: Create Docker network
    Write-StartupLog "🌐 Creating Docker network..." "STEP"
    docker network create tecnodrive-network 2>$null
    
    # Step 4: Build images if requested
    if ($BuildImages) {
        Write-StartupLog "🔨 Building Docker images..." "STEP"
        docker-compose build --no-cache
        if ($LASTEXITCODE -ne 0) {
            Write-StartupLog "Failed to build images" "ERROR"
            exit 1
        }
    }
    
    # Step 5: Start infrastructure services
    Write-StartupLog "🗄️ Starting infrastructure services..." "STEP"
    docker-compose up -d postgres redis
    
    if ($LASTEXITCODE -ne 0) {
        Write-StartupLog "Failed to start infrastructure services" "ERROR"
        exit 1
    }
    
    # Step 6: Wait for database and setup if requested
    if (-not (Wait-ForDatabase)) {
        Write-StartupLog "Database startup failed" "ERROR"
        exit 1
    }
    
    if ($SetupDatabase) {
        Write-StartupLog "🔧 Setting up database schemas..." "STEP"
        $env:PGPASSWORD = "postgres"
        psql -h localhost -p 5432 -U postgres -d postgres -f "database/create-all-schemas.sql"
        
        if ($LASTEXITCODE -eq 0) {
            Write-StartupLog "Database setup completed successfully!" "SUCCESS"
        } else {
            Write-StartupLog "Database setup failed" "ERROR"
            exit 1
        }
    }
    
    # Step 7: Start monitoring services
    Write-StartupLog "📊 Starting monitoring services..." "STEP"
    docker-compose up -d redis-insight pgadmin
    
    # Step 8: Start application services
    Write-StartupLog "🚀 Starting application services..." "STEP"
    docker-compose -f docker-compose.services.yml up -d
    
    if ($LASTEXITCODE -ne 0) {
        Write-StartupLog "Failed to start application services" "ERROR"
        exit 1
    }
    
    # Step 9: Health checks
    if (-not $SkipHealthCheck) {
        Write-StartupLog "🏥 Performing health checks..." "STEP"
        
        $services = @(
            @{ Name = "Eureka Server"; Url = "http://localhost:8761/actuator/health" },
            @{ Name = "API Gateway"; Url = "http://localhost:8080/actuator/health" },
            @{ Name = "Auth Service"; Url = "http://localhost:8081/actuator/health" },
            @{ Name = "User Service"; Url = "http://localhost:8082/actuator/health" },
            @{ Name = "Ride Service"; Url = "http://localhost:8083/actuator/health" }
        )
        
        $allHealthy = $true
        foreach ($service in $services) {
            if (-not (Test-ServiceHealth -ServiceName $service.Name -HealthUrl $service.Url)) {
                $allHealthy = $false
            }
        }
        
        if (-not $allHealthy) {
            Write-StartupLog "Some services failed health checks" "WARNING"
        }
    }
    
    # Step 10: Display status
    Write-StartupLog "✅ TECNODRIVE Platform startup completed!" "SUCCESS"
    Write-StartupLog "" "INFO"
    Write-StartupLog "🌐 Service URLs:" "STEP"
    Write-StartupLog "  • Eureka Dashboard: http://localhost:8761" "INFO"
    Write-StartupLog "  • API Gateway: http://localhost:8080" "INFO"
    Write-StartupLog "  • Redis Insight: http://localhost:8001" "INFO"
    Write-StartupLog "  • pgAdmin: http://localhost:5050" "INFO"
    Write-StartupLog "" "INFO"
    Write-StartupLog "📊 To view logs: docker-compose logs -f [service-name]" "INFO"
    Write-StartupLog "🛑 To stop platform: docker-compose down" "INFO"
    Write-StartupLog "" "INFO"
    Write-StartupLog "🎉 Platform is ready for use!" "SUCCESS"
    
} catch {
    Write-StartupLog "Startup failed: $_" "ERROR"
    Write-StartupLog "Rolling back..." "WARNING"
    docker-compose down
    exit 1
}
