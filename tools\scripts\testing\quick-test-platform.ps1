#!/usr/bin/env powershell
# =============================================================================
# TecnoDrive Platform - Quick Test Script
# =============================================================================
# اختبار سريع لجميع خدمات منصة TecnoDrive
# Quick test for all TecnoDrive platform services
# =============================================================================

param(
    [Parameter(Mandatory=$false)]
    [switch]$Detailed,
    
    [Parameter(Mandatory=$false)]
    [int]$TimeoutSeconds = 10
)

# =============================================================================
# Configuration / الإعدادات
# =============================================================================

$SERVICES_ENDPOINTS = @{
    "Eureka Server" = @{
        "url" = "http://localhost:8761"
        "health" = "http://localhost:8761/actuator/health"
        "port" = 8761
    }
    "API Gateway" = @{
        "url" = "http://localhost:8080"
        "health" = "http://localhost:8080/actuator/health"
        "port" = 8080
    }
    "Auth Service" = @{
        "url" = "http://localhost:8081"
        "health" = "http://localhost:8081/actuator/health"
        "port" = 8081
    }
    "Ride Service" = @{
        "url" = "http://localhost:8082"
        "health" = "http://localhost:8082/actuator/health"
        "port" = 8082
    }
    "User Service" = @{
        "url" = "http://localhost:8083"
        "health" = "http://localhost:8083/actuator/health"
        "port" = 8083
    }
    "Fleet Service" = @{
        "url" = "http://localhost:8084"
        "health" = "http://localhost:8084/actuator/health"
        "port" = 8084
    }
    "Location Service" = @{
        "url" = "http://localhost:8085"
        "health" = "http://localhost:8085/actuator/health"
        "port" = 8085
    }
    "Payment Service" = @{
        "url" = "http://localhost:8086"
        "health" = "http://localhost:8086/actuator/health"
        "port" = 8086
    }
    "Parcel Service" = @{
        "url" = "http://localhost:8087"
        "health" = "http://localhost:8087/actuator/health"
        "port" = 8087
    }
    "Notification Service" = @{
        "url" = "http://localhost:8088"
        "health" = "http://localhost:8088/actuator/health"
        "port" = 8088
    }
    "Analytics Service" = @{
        "url" = "http://localhost:8089"
        "health" = "http://localhost:8089/actuator/health"
        "port" = 8089
    }
    "HR Service" = @{
        "url" = "http://localhost:8090"
        "health" = "http://localhost:8090/actuator/health"
        "port" = 8090
    }
    "Financial Service" = @{
        "url" = "http://localhost:8091"
        "health" = "http://localhost:8091/actuator/health"
        "port" = 8091
    }
    "SaaS Management Service" = @{
        "url" = "http://localhost:8092"
        "health" = "http://localhost:8092/actuator/health"
        "port" = 8092
    }
}

$INFRASTRUCTURE_SERVICES = @{
    "PostgreSQL" = @{
        "container" = "postgres-tecno"
        "port" = 5432
        "test_command" = "pg_isready -U postgres"
    }
    "Redis" = @{
        "container" = "redis-tecno"
        "port" = 6379
        "test_command" = "redis-cli -a TecnoDrive2025!Redis#Cache ping"
    }
}

# =============================================================================
# Utility Functions / الدوال المساعدة
# =============================================================================

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-Host "=" * 80 -ForegroundColor Cyan
    Write-Host "  $Title" -ForegroundColor Yellow
    Write-Host "=" * 80 -ForegroundColor Cyan
    Write-Host ""
}

function Write-TestResult {
    param(
        [string]$ServiceName,
        [string]$Status,
        [string]$Details = ""
    )
    
    $statusIcon = switch ($Status) {
        "HEALTHY" { "✅" }
        "UNHEALTHY" { "❌" }
        "WARNING" { "⚠️" }
        "UNKNOWN" { "❓" }
        default { "❓" }
    }
    
    $statusColor = switch ($Status) {
        "HEALTHY" { "Green" }
        "UNHEALTHY" { "Red" }
        "WARNING" { "Yellow" }
        "UNKNOWN" { "Gray" }
        default { "Gray" }
    }
    
    $message = "$statusIcon $ServiceName - $Status"
    if ($Details) {
        $message += " ($Details)"
    }
    
    Write-Host $message -ForegroundColor $statusColor
}

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [hashtable]$ServiceConfig
    )
    
    try {
        $response = Invoke-WebRequest -Uri $ServiceConfig.health -TimeoutSec $TimeoutSeconds -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            if ($Detailed) {
                $healthData = $response.Content | ConvertFrom-Json
                $status = $healthData.status
                Write-TestResult -ServiceName $ServiceName -Status "HEALTHY" -Details "Status: $status"
            } else {
                Write-TestResult -ServiceName $ServiceName -Status "HEALTHY"
            }
            return $true
        } else {
            Write-TestResult -ServiceName $ServiceName -Status "WARNING" -Details "HTTP $($response.StatusCode)"
            return $false
        }
    }
    catch {
        # Try basic connectivity test
        try {
            $tcpTest = Test-NetConnection -ComputerName "localhost" -Port $ServiceConfig.port -WarningAction SilentlyContinue
            if ($tcpTest.TcpTestSucceeded) {
                Write-TestResult -ServiceName $ServiceName -Status "WARNING" -Details "Port open but health check failed"
                return $false
            } else {
                Write-TestResult -ServiceName $ServiceName -Status "UNHEALTHY" -Details "Port not accessible"
                return $false
            }
        }
        catch {
            Write-TestResult -ServiceName $ServiceName -Status "UNHEALTHY" -Details "Connection failed"
            return $false
        }
    }
}

function Test-InfrastructureService {
    param(
        [string]$ServiceName,
        [hashtable]$ServiceConfig
    )
    
    try {
        $result = docker exec $ServiceConfig.container $ServiceConfig.test_command 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestResult -ServiceName $ServiceName -Status "HEALTHY"
            return $true
        } else {
            Write-TestResult -ServiceName $ServiceName -Status "UNHEALTHY" -Details "Command failed"
            return $false
        }
    }
    catch {
        Write-TestResult -ServiceName $ServiceName -Status "UNHEALTHY" -Details "Container not accessible"
        return $false
    }
}

function Test-ContainerStatus {
    Write-Header "Container Status Check / فحص حالة الحاويات"
    
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=tecno"
    
    if ($containers) {
        Write-Host $containers
        Write-Host ""
        
        # Count running containers
        $runningCount = (docker ps --filter "name=tecno" --format "{{.Names}}").Count
        Write-ColoredOutput "Total running containers: $runningCount" "Green"
    } else {
        Write-ColoredOutput "No TecnoDrive containers are running!" "Red"
        return $false
    }
    
    return $true
}

function Test-NetworkConnectivity {
    Write-Header "Network Connectivity Test / اختبار الاتصال بالشبكة"
    
    try {
        $network = docker network inspect tecnodrive-network 2>$null
        if ($network) {
            Write-TestResult -ServiceName "Docker Network" -Status "HEALTHY" -Details "tecnodrive-network exists"
        } else {
            Write-TestResult -ServiceName "Docker Network" -Status "UNHEALTHY" -Details "tecnodrive-network not found"
            return $false
        }
    }
    catch {
        Write-TestResult -ServiceName "Docker Network" -Status "UNHEALTHY" -Details "Network check failed"
        return $false
    }
    
    return $true
}

function Show-Summary {
    param(
        [int]$HealthyServices,
        [int]$TotalServices,
        [int]$HealthyInfra,
        [int]$TotalInfra
    )
    
    Write-Header "Test Summary / ملخص الاختبار"
    
    $overallHealth = ($HealthyServices -eq $TotalServices) -and ($HealthyInfra -eq $TotalInfra)
    
    Write-Host "Infrastructure Services: $HealthyInfra/$TotalInfra" -ForegroundColor $(if ($HealthyInfra -eq $TotalInfra) { "Green" } else { "Red" })
    Write-Host "Application Services: $HealthyServices/$TotalServices" -ForegroundColor $(if ($HealthyServices -eq $TotalServices) { "Green" } else { "Red" })
    
    if ($overallHealth) {
        Write-Host "`n🎉 All services are healthy! Platform is ready for use." -ForegroundColor Green
        Write-Host "🌐 Dashboard: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "🔍 Eureka: http://localhost:8761" -ForegroundColor Cyan
    } else {
        Write-Host "`n⚠️ Some services are not healthy. Check the details above." -ForegroundColor Yellow
        Write-Host "💡 Try running: .\build-and-run-all.ps1 -Action restart" -ForegroundColor Cyan
    }
}

# =============================================================================
# Main Test Execution / التنفيذ الرئيسي للاختبار
# =============================================================================

function Start-PlatformTest {
    Write-Header "TecnoDrive Platform Health Check / فحص صحة منصة TecnoDrive"
    
    # Test container status
    if (-not (Test-ContainerStatus)) {
        Write-Host "❌ No containers running. Please start the platform first:" -ForegroundColor Red
        Write-Host "   .\build-and-run-all.ps1 -Action start" -ForegroundColor Cyan
        return
    }
    
    # Test network connectivity
    Test-NetworkConnectivity | Out-Null
    
    # Test infrastructure services
    Write-Header "Infrastructure Services Test / اختبار خدمات البنية التحتية"
    $healthyInfra = 0
    foreach ($serviceName in $INFRASTRUCTURE_SERVICES.Keys) {
        if (Test-InfrastructureService -ServiceName $serviceName -ServiceConfig $INFRASTRUCTURE_SERVICES[$serviceName]) {
            $healthyInfra++
        }
    }
    
    # Test application services
    Write-Header "Application Services Test / اختبار خدمات التطبيق"
    $healthyServices = 0
    foreach ($serviceName in $SERVICES_ENDPOINTS.Keys) {
        if (Test-ServiceHealth -ServiceName $serviceName -ServiceConfig $SERVICES_ENDPOINTS[$serviceName]) {
            $healthyServices++
        }
        Start-Sleep -Milliseconds 500  # Small delay between requests
    }
    
    # Show summary
    Show-Summary -HealthyServices $healthyServices -TotalServices $SERVICES_ENDPOINTS.Count -HealthyInfra $healthyInfra -TotalInfra $INFRASTRUCTURE_SERVICES.Count
}

# Run the test
try {
    Start-PlatformTest
}
catch {
    Write-Host "❌ Test execution failed: $_" -ForegroundColor Red
    exit 1
}
