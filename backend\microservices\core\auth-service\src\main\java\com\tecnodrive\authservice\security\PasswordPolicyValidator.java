package com.tecnodrive.authservice.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Enhanced Password Policy Validator
 * Implements comprehensive password security policies
 */
@Slf4j
@Component
public class PasswordPolicyValidator {

    private final int minLength;
    private final boolean requireUppercase;
    private final boolean requireLowercase;
    private final boolean requireDigits;
    private final boolean requireSpecialChars;
    private final String specialChars;
    private final int historySize;
    private final BCryptPasswordEncoder passwordEncoder;

    // Common weak passwords
    private static final List<String> COMMON_WEAK_PASSWORDS = List.of(
            "password", "123456", "password123", "admin", "qwerty", "letmein",
            "welcome", "monkey", "1234567890", "password1", "123456789",
            "12345678", "1234567", "123123", "111111", "1234", "12345",
            "dragon", "123", "baseball", "abc123", "football", "master",
            "jordan", "harley", "ranger", "shadow", "superman", "696969",
            "mustang", "michael", "beer", "fuckyou", "trustno1", "hunter",
            "2000", "test", "batman", "pass", "killer", "hockey", "george",
            "charlie", "andrew", "michelle", "love", "sunshine", "jessica"
    );

    // Sequential patterns
    private static final List<Pattern> SEQUENTIAL_PATTERNS = List.of(
            Pattern.compile(".*(?:012|123|234|345|456|567|678|789|890).*"),
            Pattern.compile(".*(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz).*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*(?:qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm).*", Pattern.CASE_INSENSITIVE)
    );

    // Repetitive patterns
    private static final List<Pattern> REPETITIVE_PATTERNS = List.of(
            Pattern.compile(".*(.)\\1{2,}.*"), // Same character repeated 3+ times
            Pattern.compile(".*(..)\\1{2,}.*"), // Same 2-character sequence repeated 3+ times
            Pattern.compile(".*(...)\\1{1,}.*") // Same 3-character sequence repeated 2+ times
    );

    public PasswordPolicyValidator(
            @Value("${security.password.min-length:12}") int minLength,
            @Value("${security.password.require-uppercase:true}") boolean requireUppercase,
            @Value("${security.password.require-lowercase:true}") boolean requireLowercase,
            @Value("${security.password.require-digits:true}") boolean requireDigits,
            @Value("${security.password.require-special-chars:true}") boolean requireSpecialChars,
            @Value("${security.password.special-chars:!@#$%^&*()_+-=[]{}|;:,.<>?}") String specialChars,
            @Value("${security.password.history-size:5}") int historySize) {
        
        this.minLength = minLength;
        this.requireUppercase = requireUppercase;
        this.requireLowercase = requireLowercase;
        this.requireDigits = requireDigits;
        this.requireSpecialChars = requireSpecialChars;
        this.specialChars = specialChars;
        this.historySize = historySize;
        this.passwordEncoder = new BCryptPasswordEncoder(12); // Strong cost factor
    }

    /**
     * Validate password against all policies
     */
    public PasswordValidationResult validatePassword(String password, String username, List<String> passwordHistory) {
        List<String> violations = new ArrayList<>();
        int score = 0;

        // Length check
        if (password.length() < minLength) {
            violations.add(String.format("Password must be at least %d characters long", minLength));
        } else {
            score += Math.min(password.length() - minLength + 1, 5);
        }

        // Character type requirements
        if (requireUppercase && !containsUppercase(password)) {
            violations.add("Password must contain at least one uppercase letter");
        } else if (containsUppercase(password)) {
            score += 2;
        }

        if (requireLowercase && !containsLowercase(password)) {
            violations.add("Password must contain at least one lowercase letter");
        } else if (containsLowercase(password)) {
            score += 2;
        }

        if (requireDigits && !containsDigits(password)) {
            violations.add("Password must contain at least one digit");
        } else if (containsDigits(password)) {
            score += 2;
        }

        if (requireSpecialChars && !containsSpecialChars(password)) {
            violations.add("Password must contain at least one special character: " + specialChars);
        } else if (containsSpecialChars(password)) {
            score += 3;
        }

        // Advanced security checks
        if (isCommonWeakPassword(password)) {
            violations.add("Password is too common and easily guessable");
        }

        if (containsUsername(password, username)) {
            violations.add("Password must not contain the username");
        }

        if (containsSequentialPattern(password)) {
            violations.add("Password must not contain sequential patterns (e.g., 123, abc)");
        }

        if (containsRepetitivePattern(password)) {
            violations.add("Password must not contain repetitive patterns");
        }

        if (isInPasswordHistory(password, passwordHistory)) {
            violations.add(String.format("Password must not match any of the last %d passwords", historySize));
        }

        // Calculate final score
        score += calculateComplexityBonus(password);
        
        // Determine strength
        PasswordStrength strength = determinePasswordStrength(score, violations.isEmpty());

        return new PasswordValidationResult(violations.isEmpty(), violations, strength, score);
    }

    private boolean containsUppercase(String password) {
        return password.chars().anyMatch(Character::isUpperCase);
    }

    private boolean containsLowercase(String password) {
        return password.chars().anyMatch(Character::isLowerCase);
    }

    private boolean containsDigits(String password) {
        return password.chars().anyMatch(Character::isDigit);
    }

    private boolean containsSpecialChars(String password) {
        return password.chars().anyMatch(ch -> specialChars.indexOf(ch) >= 0);
    }

    private boolean isCommonWeakPassword(String password) {
        String lowerPassword = password.toLowerCase();
        return COMMON_WEAK_PASSWORDS.stream()
                .anyMatch(weak -> lowerPassword.contains(weak) || weak.contains(lowerPassword));
    }

    private boolean containsUsername(String password, String username) {
        if (username == null || username.length() < 3) {
            return false;
        }
        return password.toLowerCase().contains(username.toLowerCase()) ||
               username.toLowerCase().contains(password.toLowerCase());
    }

    private boolean containsSequentialPattern(String password) {
        return SEQUENTIAL_PATTERNS.stream()
                .anyMatch(pattern -> pattern.matcher(password).matches());
    }

    private boolean containsRepetitivePattern(String password) {
        return REPETITIVE_PATTERNS.stream()
                .anyMatch(pattern -> pattern.matcher(password).matches());
    }

    private boolean isInPasswordHistory(String password, List<String> passwordHistory) {
        if (passwordHistory == null || passwordHistory.isEmpty()) {
            return false;
        }
        
        return passwordHistory.stream()
                .anyMatch(hashedPassword -> passwordEncoder.matches(password, hashedPassword));
    }

    private int calculateComplexityBonus(String password) {
        int bonus = 0;
        
        // Length bonus
        if (password.length() >= 16) bonus += 2;
        if (password.length() >= 20) bonus += 2;
        
        // Character variety bonus
        long uniqueChars = password.chars().distinct().count();
        if (uniqueChars >= password.length() * 0.7) bonus += 2;
        
        // Mixed case bonus
        if (containsUppercase(password) && containsLowercase(password)) bonus += 1;
        
        // Multiple special characters bonus
        long specialCharCount = password.chars().filter(ch -> specialChars.indexOf(ch) >= 0).count();
        if (specialCharCount >= 2) bonus += 1;
        if (specialCharCount >= 4) bonus += 1;
        
        return bonus;
    }

    private PasswordStrength determinePasswordStrength(int score, boolean isValid) {
        if (!isValid) {
            return PasswordStrength.INVALID;
        }
        
        if (score >= 15) return PasswordStrength.VERY_STRONG;
        if (score >= 12) return PasswordStrength.STRONG;
        if (score >= 8) return PasswordStrength.MODERATE;
        if (score >= 5) return PasswordStrength.WEAK;
        return PasswordStrength.VERY_WEAK;
    }

    /**
     * Generate password strength recommendations
     */
    public List<String> generatePasswordRecommendations() {
        return List.of(
                "Use at least " + minLength + " characters",
                "Include uppercase and lowercase letters",
                "Include numbers and special characters",
                "Avoid common words and patterns",
                "Don't use personal information",
                "Consider using a passphrase with random words",
                "Use a password manager for unique passwords"
        );
    }

    /**
     * Hash password securely
     */
    public String hashPassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * Verify password against hash
     */
    public boolean verifyPassword(String password, String hashedPassword) {
        return passwordEncoder.matches(password, hashedPassword);
    }

    // Inner classes for result types
    public static class PasswordValidationResult {
        private final boolean valid;
        private final List<String> violations;
        private final PasswordStrength strength;
        private final int score;

        public PasswordValidationResult(boolean valid, List<String> violations, PasswordStrength strength, int score) {
            this.valid = valid;
            this.violations = violations;
            this.strength = strength;
            this.score = score;
        }

        // Getters
        public boolean isValid() { return valid; }
        public List<String> getViolations() { return violations; }
        public PasswordStrength getStrength() { return strength; }
        public int getScore() { return score; }
    }

    public enum PasswordStrength {
        INVALID, VERY_WEAK, WEAK, MODERATE, STRONG, VERY_STRONG
    }
}
