{
  "recommendations": [
    // Java Development
    "vscjava.vscode-java-pack",
    "vscjava.vscode-spring-initializr",
    "vscjava.vscode-spring-boot-dashboard",
    "pivotal.vscode-spring-boot",
    "vscjava.vscode-maven",
    "redhat.java",
    
    // Python Development
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "ms-python.pylint",
    "ms-python.autopep8",
    
    // Frontend Development
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // React & TypeScript
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    
    // DevOps & Infrastructure
    "ms-kubernetes-tools.vscode-kubernetes-tools",
    "ms-vscode-remote.remote-containers",
    "ms-azuretools.vscode-docker",
    "hashicorp.terraform",
    "redhat.vscode-yaml",
    
    // Documentation & Markdown
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "davidanson.vscode-markdownlint",
    
    // Utilities
    "ms-vscode.powershell",
    "ms-vscode.hexeditor",
    "ms-vscode-remote.remote-ssh",
    "ms-vscode.remote-explorer",
    "ms-vscode.remote-repositories",
    
    // Git & Version Control
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // Code Quality
    "sonarsource.sonarlint-vscode",
    "ms-vscode.vscode-json",
    
    // Productivity
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-yaml",
    "redhat.vscode-xml",
    "ms-vscode.vscode-json",
    
    // Database
    "ms-mssql.mssql",
    "cweijan.vscode-postgresql-client2",
    
    // API Development
    "humao.rest-client",
    "42crunch.vscode-openapi",
    
    // Monitoring & Logs
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
  ]
}
