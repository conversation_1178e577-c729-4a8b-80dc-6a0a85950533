import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  ArrowBack,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Business as BusinessIcon,
  Subscriptions as SubscriptionsIcon,
  Payment as PaymentIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { saasService, TenantDto, SubscriptionDto, UsageAnalyticsDto } from '../../services/saasService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenant-tabpanel-${index}`}
      aria-labelledby={`tenant-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TenantDashboard: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  const navigate = useNavigate();
  
  const [tenant, setTenant] = useState<TenantDto | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionDto[]>([]);
  const [usageAnalytics, setUsageAnalytics] = useState<UsageAnalyticsDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // Load tenant data
  const loadTenantData = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);
      setError(null);

      // Load tenant details
      const tenantResponse = await saasService.getTenantById(tenantId);
      if (tenantResponse.success && tenantResponse.data) {
        setTenant(tenantResponse.data);
      }

      // Load subscriptions
      const subscriptionsResponse = await saasService.getSubscriptions(tenantId);
      if (subscriptionsResponse.success && subscriptionsResponse.data) {
        setSubscriptions(subscriptionsResponse.data);
      }

      // Load usage analytics
      const analyticsResponse = await saasService.getUsageAnalytics(tenantId);
      if (analyticsResponse.success && analyticsResponse.data) {
        setUsageAnalytics(analyticsResponse.data);
      }

    } catch (err) {
      setError('حدث خطأ في جلب بيانات العميل');
      console.error('Error loading tenant data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTenantData();
  }, [tenantId]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const },
      INACTIVE: { label: 'غير نشط', color: 'default' as const },
      SUSPENDED: { label: 'معلق', color: 'error' as const },
      EXPIRED: { label: 'منتهي', color: 'warning' as const },
      CANCELLED: { label: 'ملغي', color: 'error' as const },
      PENDING: { label: 'معلق', color: 'info' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>جاري تحميل بيانات العميل...</Typography>
      </Box>
    );
  }

  if (error || !tenant) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'لم يتم العثور على العميل'}</Alert>
        <Button onClick={() => navigate('/saas/tenants')} sx={{ mt: 2 }}>
          العودة إلى قائمة العملاء
        </Button>
      </Box>
    );
  }

  const activeSubscriptions = subscriptions.filter(s => s.status === 'ACTIVE');
  const totalSeats = activeSubscriptions.reduce((sum, s) => sum + s.seats, 0);
  const usedSeats = activeSubscriptions.reduce((sum, s) => sum + s.usedSeats, 0);
  const monthlyRevenue = activeSubscriptions.reduce((sum, s) => sum + s.monthlyPrice, 0);

  // Chart colors
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <Box>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/saas/tenants')}>
                <ArrowBack />
              </IconButton>
              <BusinessIcon sx={{ fontSize: 40, color: 'primary.main' }} />
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  {tenant.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {tenant.domain}
                </Typography>
              </Box>
              {getStatusChip(tenant.status)}
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton color="primary">
                <EditIcon />
              </IconButton>
              <IconButton color="error">
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>
          
          <Grid container spacing={3} sx={{ mt: 2 }}>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">الهاتف</Typography>
              <Typography variant="body1">{tenant.phone}</Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">البريد الإلكتروني</Typography>
              <Typography variant="body1">{tenant.email}</Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">تاريخ الإنشاء</Typography>
              <Typography variant="body1">
                {new Date(tenant.createdAt).toLocaleDateString('ar-SA')}
              </Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">آخر تحديث</Typography>
              <Typography variant="body1">
                {new Date(tenant.updatedAt).toLocaleDateString('ar-SA')}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SubscriptionsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {subscriptions.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الاشتراكات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <BusinessIcon sx={{ fontSize: 40, color: 'success.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {usedSeats}/{totalSeats}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    المستخدمين النشطين
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PaymentIcon sx={{ fontSize: 40, color: 'warning.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {monthlyRevenue.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    الإيرادات الشهرية (ريال)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AnalyticsIcon sx={{ fontSize: 40, color: 'info.main' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {usageAnalytics?.totalApiCalls?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    استدعاءات API
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="الاشتراكات" />
            <Tab label="تحليلات الاستخدام" />
            <Tab label="الفوترة" />
          </Tabs>
        </Box>

        {/* Subscriptions Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">الاشتراكات النشطة</Typography>
            <Button variant="contained" startIcon={<AddIcon />}>
              إضافة اشتراك
            </Button>
          </Box>
          
          <Grid container spacing={3}>
            {subscriptions.map((subscription) => (
              <Grid item xs={12} md={6} key={subscription.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">{subscription.planName}</Typography>
                      {getStatusChip(subscription.status)}
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      من {new Date(subscription.startDate).toLocaleDateString('ar-SA')} 
                      إلى {new Date(subscription.endDate).toLocaleDateString('ar-SA')}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      المقاعد: {subscription.usedSeats}/{subscription.seats}
                    </Typography>
                    <Typography variant="h6" sx={{ color: 'success.main' }}>
                      {subscription.monthlyPrice} {subscription.currency}/شهر
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      {subscription.features.map((feature, index) => (
                        <Chip
                          key={index}
                          label={feature}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 1, mb: 1 }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Usage Analytics Tab */}
        <TabPanel value={tabValue} index={1}>
          {usageAnalytics && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>المستخدمين النشطين (آخر 30 يوم)</Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={usageAnalytics.dailyStats}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="activeUsers" stroke="#8884d8" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>استخدام الوحدات</Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={usageAnalytics.moduleUsage}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ moduleName, percentage }) => `${moduleName} (${percentage}%)`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="usageCount"
                        >
                          {usageAnalytics.moduleUsage.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>استدعاءات API اليومية</Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={usageAnalytics.dailyStats}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="apiCalls" fill="#82ca9d" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="primary.main">
                      {usageAnalytics.activeUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      المستخدمين النشطين
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="success.main">
                      {usageAnalytics.peakUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      ذروة المستخدمين ({new Date(usageAnalytics.peakDate).toLocaleDateString('ar-SA')})
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="info.main">
                      {usageAnalytics.dataUsageGB} GB
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      استهلاك البيانات
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Billing Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">الفوترة والمدفوعات</Typography>
            <Button variant="contained" startIcon={<AddIcon />}>
              إنشاء فاتورة
            </Button>
          </Box>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            الرصيد الحالي: {tenant.billingBalance?.toLocaleString() || 0} ريال
          </Alert>
          
          <Typography variant="body1" color="text.secondary">
            سيتم إضافة تفاصيل الفوترة والفواتير هنا...
          </Typography>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default TenantDashboard;
