#!/bin/bash

# =====================================================
# TECNO DRIVE - Database Backup Script
# سكريبت النسخ الاحتياطي لقاعدة البيانات
# =====================================================

set -e

# إعدادات قاعدة البيانات
DB_HOST="postgres"
DB_PORT="5432"
DB_NAME="tecnodrive"
DB_USER="tecnodrive_user"
DB_PASSWORD="tecnodrive_pass"

# إعدادات النسخ الاحتياطي
BACKUP_DIR="/backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="tecnodrive_backup_${DATE}.sql"
COMPRESSED_FILE="tecnodrive_backup_${DATE}.sql.gz"

# إعدادات الاحتفاظ بالنسخ
RETENTION_DAYS=30
MAX_BACKUPS=50

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# دالة التحقق من الاتصال بقاعدة البيانات
check_db_connection() {
    log_info "التحقق من الاتصال بقاعدة البيانات..."
    
    if pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME; then
        log_success "الاتصال بقاعدة البيانات ناجح"
        return 0
    else
        log_error "فشل الاتصال بقاعدة البيانات"
        return 1
    fi
}

# دالة إنشاء مجلد النسخ الاحتياطي
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        log_info "إنشاء مجلد النسخ الاحتياطي..."
        mkdir -p "$BACKUP_DIR"
        log_success "تم إنشاء مجلد النسخ الاحتياطي: $BACKUP_DIR"
    fi
}

# دالة إنشاء النسخة الاحتياطية
create_backup() {
    log_info "بدء إنشاء النسخة الاحتياطية..."
    
    # إنشاء النسخة الاحتياطية الكاملة
    if pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
        --verbose \
        --clean \
        --if-exists \
        --create \
        --format=plain \
        --encoding=UTF8 \
        --no-owner \
        --no-privileges \
        > "$BACKUP_DIR/$BACKUP_FILE"; then
        
        log_success "تم إنشاء النسخة الاحتياطية: $BACKUP_FILE"
        
        # ضغط النسخة الاحتياطية
        log_info "ضغط النسخة الاحتياطية..."
        if gzip "$BACKUP_DIR/$BACKUP_FILE"; then
            log_success "تم ضغط النسخة الاحتياطية: $COMPRESSED_FILE"
            
            # عرض حجم الملف
            BACKUP_SIZE=$(du -h "$BACKUP_DIR/$COMPRESSED_FILE" | cut -f1)
            log_info "حجم النسخة الاحتياطية: $BACKUP_SIZE"
            
            return 0
        else
            log_error "فشل في ضغط النسخة الاحتياطية"
            return 1
        fi
    else
        log_error "فشل في إنشاء النسخة الاحتياطية"
        return 1
    fi
}

# دالة إنشاء نسخة احتياطية للبيانات فقط
create_data_backup() {
    DATA_BACKUP_FILE="tecnodrive_data_${DATE}.sql"
    DATA_COMPRESSED_FILE="tecnodrive_data_${DATE}.sql.gz"
    
    log_info "إنشاء نسخة احتياطية للبيانات فقط..."
    
    if pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
        --verbose \
        --data-only \
        --format=plain \
        --encoding=UTF8 \
        --no-owner \
        --no-privileges \
        > "$BACKUP_DIR/$DATA_BACKUP_FILE"; then
        
        log_success "تم إنشاء نسخة احتياطية للبيانات: $DATA_BACKUP_FILE"
        
        # ضغط النسخة الاحتياطية
        if gzip "$BACKUP_DIR/$DATA_BACKUP_FILE"; then
            log_success "تم ضغط نسخة البيانات: $DATA_COMPRESSED_FILE"
            return 0
        else
            log_error "فشل في ضغط نسخة البيانات"
            return 1
        fi
    else
        log_error "فشل في إنشاء نسخة احتياطية للبيانات"
        return 1
    fi
}

# دالة إنشاء نسخة احتياطية للهيكل فقط
create_schema_backup() {
    SCHEMA_BACKUP_FILE="tecnodrive_schema_${DATE}.sql"
    SCHEMA_COMPRESSED_FILE="tecnodrive_schema_${DATE}.sql.gz"
    
    log_info "إنشاء نسخة احتياطية للهيكل فقط..."
    
    if pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
        --verbose \
        --schema-only \
        --format=plain \
        --encoding=UTF8 \
        --no-owner \
        --no-privileges \
        > "$BACKUP_DIR/$SCHEMA_BACKUP_FILE"; then
        
        log_success "تم إنشاء نسخة احتياطية للهيكل: $SCHEMA_BACKUP_FILE"
        
        # ضغط النسخة الاحتياطية
        if gzip "$BACKUP_DIR/$SCHEMA_BACKUP_FILE"; then
            log_success "تم ضغط نسخة الهيكل: $SCHEMA_COMPRESSED_FILE"
            return 0
        else
            log_error "فشل في ضغط نسخة الهيكل"
            return 1
        fi
    else
        log_error "فشل في إنشاء نسخة احتياطية للهيكل"
        return 1
    fi
}

# دالة تنظيف النسخ القديمة
cleanup_old_backups() {
    log_info "تنظيف النسخ الاحتياطية القديمة..."
    
    # حذف النسخ الأقدم من RETENTION_DAYS يوم
    if find "$BACKUP_DIR" -name "tecnodrive_backup_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete; then
        log_success "تم حذف النسخ الاحتياطية الأقدم من $RETENTION_DAYS يوم"
    fi
    
    # الاحتفاظ بآخر MAX_BACKUPS نسخة فقط
    BACKUP_COUNT=$(find "$BACKUP_DIR" -name "tecnodrive_backup_*.sql.gz" -type f | wc -l)
    if [ $BACKUP_COUNT -gt $MAX_BACKUPS ]; then
        EXCESS_COUNT=$((BACKUP_COUNT - MAX_BACKUPS))
        log_warning "عدد النسخ الاحتياطية ($BACKUP_COUNT) يتجاوز الحد الأقصى ($MAX_BACKUPS)"
        
        find "$BACKUP_DIR" -name "tecnodrive_backup_*.sql.gz" -type f -printf '%T@ %p\n' | \
        sort -n | head -n $EXCESS_COUNT | cut -d' ' -f2- | xargs rm -f
        
        log_success "تم حذف $EXCESS_COUNT نسخة احتياطية قديمة"
    fi
}

# دالة التحقق من سلامة النسخة الاحتياطية
verify_backup() {
    log_info "التحقق من سلامة النسخة الاحتياطية..."
    
    if [ -f "$BACKUP_DIR/$COMPRESSED_FILE" ]; then
        # التحقق من سلامة ملف الضغط
        if gzip -t "$BACKUP_DIR/$COMPRESSED_FILE"; then
            log_success "النسخة الاحتياطية سليمة"
            return 0
        else
            log_error "النسخة الاحتياطية تالفة"
            return 1
        fi
    else
        log_error "ملف النسخة الاحتياطية غير موجود"
        return 1
    fi
}

# دالة إنشاء تقرير النسخ الاحتياطي
create_backup_report() {
    REPORT_FILE="$BACKUP_DIR/backup_report_${DATE}.txt"
    
    log_info "إنشاء تقرير النسخ الاحتياطي..."
    
    cat > "$REPORT_FILE" << EOF
==============================================
TECNO DRIVE - تقرير النسخ الاحتياطي
==============================================

التاريخ والوقت: $(date)
اسم قاعدة البيانات: $DB_NAME
خادم قاعدة البيانات: $DB_HOST:$DB_PORT

ملفات النسخ الاحتياطي:
- النسخة الكاملة: $COMPRESSED_FILE
- حجم الملف: $(du -h "$BACKUP_DIR/$COMPRESSED_FILE" 2>/dev/null | cut -f1 || echo "غير متوفر")

إحصائيات قاعدة البيانات:
EOF

    # إضافة إحصائيات قاعدة البيانات
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT 'عدد الجداول: ' || count(*) 
        FROM information_schema.tables 
        WHERE table_schema = 'public';
    " >> "$REPORT_FILE" 2>/dev/null || echo "- عدد الجداول: غير متوفر" >> "$REPORT_FILE"
    
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT 'حجم قاعدة البيانات: ' || pg_size_pretty(pg_database_size('$DB_NAME'));
    " >> "$REPORT_FILE" 2>/dev/null || echo "- حجم قاعدة البيانات: غير متوفر" >> "$REPORT_FILE"
    
    echo "" >> "$REPORT_FILE"
    echo "حالة النسخ الاحتياطي: مكتمل بنجاح" >> "$REPORT_FILE"
    echo "===============================================" >> "$REPORT_FILE"
    
    log_success "تم إنشاء تقرير النسخ الاحتياطي: $REPORT_FILE"
}

# الدالة الرئيسية
main() {
    log_info "بدء عملية النسخ الاحتياطي لقاعدة بيانات TECNO DRIVE"
    log_info "التاريخ والوقت: $(date)"
    
    # التحقق من الاتصال
    if ! check_db_connection; then
        log_error "فشل في الاتصال بقاعدة البيانات. إنهاء العملية."
        exit 1
    fi
    
    # إنشاء مجلد النسخ الاحتياطي
    create_backup_dir
    
    # إنشاء النسخة الاحتياطية الكاملة
    if create_backup; then
        # التحقق من سلامة النسخة
        if verify_backup; then
            # إنشاء نسخ إضافية (البيانات والهيكل)
            create_data_backup
            create_schema_backup
            
            # تنظيف النسخ القديمة
            cleanup_old_backups
            
            # إنشاء التقرير
            create_backup_report
            
            log_success "تمت عملية النسخ الاحتياطي بنجاح!"
            log_info "ملف النسخة الاحتياطية: $BACKUP_DIR/$COMPRESSED_FILE"
        else
            log_error "فشل في التحقق من سلامة النسخة الاحتياطية"
            exit 1
        fi
    else
        log_error "فشل في إنشاء النسخة الاحتياطية"
        exit 1
    fi
}

# تشغيل الدالة الرئيسية
main "$@"
