package com.tecnodrive.walletservice.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

/**
 * Wallet Top-up Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopUpRequest {
    
    @NotNull(message = "Wallet ID is required")
    private UUID walletId;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "1.0", message = "Amount must be at least 1.0")
    @DecimalMax(value = "1000000.0", message = "Amount cannot exceed 1,000,000")
    private BigDecimal amount;
    
    @NotBlank(message = "Payment method is required")
    @Pattern(regexp = "^(cash|jawaly|onecash|jaib_wallet|fulusak_wallet|bank_card|bank_transfer)$", 
             message = "Invalid payment method")
    private String paymentMethod;
    
    private String description;
    
    // For electronic payments
    private String customerPhone;
    private String customerEmail;
    private String returnUrl;
    private String cancelUrl;
    
    // Additional metadata
    private Map<String, Object> metadata;
    
    // Agent information for cash top-ups
    private UUID agentId;
    private String agentName;
    private String agentLocation;
}
