{{- if .Values.argocd.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecno-drive-security-system
  namespace: argocd
  labels:
    app.kubernetes.io/name: tecno-drive-security
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  project: default
  source:
    repoURL: {{ .Values.argocd.repoURL | default "https://github.com/tecnodrive/platform" }}
    targetRevision: {{ .Values.argocd.targetRevision | default "HEAD" }}
    path: kubernetes/secure-cluster
    helm:
      valueFiles:
        - values.yaml
        {{- if .Values.argocd.environment }}
        - values-{{ .Values.argocd.environment }}.yaml
        {{- end }}
  
  destination:
    server: https://kubernetes.default.svc
    namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  
  syncPolicy:
    automated:
      prune: {{ .Values.argocd.syncPolicy.automated.prune | default true }}
      selfHeal: {{ .Values.argocd.syncPolicy.automated.selfHeal | default true }}
      allowEmpty: false
    
    syncOptions:
      - CreateNamespace=true
      - ApplyOutOfSyncOnly=true
      - RespectIgnoreDifferences=true
      - ServerSideApply=true
    
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  # Sync Waves Configuration
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas
    - group: ""
      kind: Secret
      jsonPointers:
        - /data
  
  # Health checks
  health:
    - group: gatekeeper.sh
      kind: ConstraintTemplate
      check: |
        health_status = {}
        if obj.status ~= nil then
          if obj.status.created == true then
            health_status.status = "Healthy"
            health_status.message = "ConstraintTemplate is created and ready"
          else
            health_status.status = "Progressing"
            health_status.message = "ConstraintTemplate is being created"
          end
        else
          health_status.status = "Progressing"
          health_status.message = "ConstraintTemplate status is unknown"
        end
        return health_status

---
# Sync Waves Application for TECNO DRIVE Services
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecno-drive-services
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "3"
  labels:
    app.kubernetes.io/name: tecno-drive-services
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  project: default
  source:
    repoURL: {{ .Values.argocd.repoURL | default "https://github.com/tecnodrive/platform" }}
    targetRevision: {{ .Values.argocd.targetRevision | default "HEAD" }}
    path: kubernetes/applications
  
  destination:
    server: https://kubernetes.default.svc
    namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    
    syncOptions:
      - CreateNamespace=true
      - ApplyOutOfSyncOnly=true
    
    # Sync Waves for Services
    managedNamespaceMetadata:
      labels:
        argocd.argoproj.io/managed-by: argocd
        tecno-drive.com/managed: "true"
      annotations:
        argocd.argoproj.io/sync-wave: "2"

---
# ConfigMap for Sync Waves Documentation
apiVersion: v1
kind: ConfigMap
metadata:
  name: sync-waves-guide
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  labels:
    app.kubernetes.io/name: sync-waves-guide
    app.kubernetes.io/instance: {{ .Release.Name }}
data:
  sync-waves.md: |
    # TECNO DRIVE Sync Waves Guide
    
    ## Wave Execution Order:
    
    ### Wave -1: Security Foundation
    - ConstraintTemplates (Gatekeeper policies)
    - Config objects
    - CRDs (Custom Resource Definitions)
    
    ### Wave 0: Security Policies
    - Constraints (Policy enforcement)
    - NetworkPolicies (Network security)
    - PodSecurityPolicies
    - RBAC configurations
    
    ### Wave 1: Resilience Infrastructure
    - PodDisruptionBudgets
    - ServiceMonitors
    - PrometheusRules
    - ConfigMaps and Secrets
    
    ### Wave 2: Application Infrastructure
    - Services
    - Ingress
    - PersistentVolumeClaims
    - ServiceAccounts
    
    ### Wave 3: Applications
    - Deployments
    - StatefulSets
    - DaemonSets
    - Jobs and CronJobs
    
    ### Wave 4: Monitoring and Observability
    - Grafana Dashboards
    - Alert configurations
    - Log forwarding rules
    
    ## Best Practices:
    1. Always use negative waves for foundational components
    2. Ensure security policies are applied before applications
    3. Test sync waves in development environment first
    4. Monitor ArgoCD sync status and health checks
    5. Use health checks for custom resources
    
    ## Troubleshooting:
    - Check ArgoCD application status: `kubectl get applications -n argocd`
    - View sync status: `argocd app get tecno-drive-security-system`
    - Force sync: `argocd app sync tecno-drive-security-system --force`
    - Check resource health: `argocd app get tecno-drive-security-system --show-params`

  wave-annotations.yaml: |
    # Example annotations for different waves:
    
    # Wave -1 (Security Foundation)
    metadata:
      annotations:
        argocd.argoproj.io/sync-wave: "-1"
    
    # Wave 0 (Security Policies)
    metadata:
      annotations:
        argocd.argoproj.io/sync-wave: "0"
    
    # Wave 1 (Resilience)
    metadata:
      annotations:
        argocd.argoproj.io/sync-wave: "1"
    
    # Wave 2 (Infrastructure)
    metadata:
      annotations:
        argocd.argoproj.io/sync-wave: "2"
    
    # Wave 3 (Applications)
    metadata:
      annotations:
        argocd.argoproj.io/sync-wave: "3"

---
# Application Set for Multi-Environment Deployment
{{- if .Values.argocd.multiEnvironment.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: tecno-drive-environments
  namespace: argocd
  labels:
    app.kubernetes.io/name: tecno-drive-environments
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  generators:
  - list:
      elements:
      {{- range .Values.argocd.multiEnvironment.environments }}
      - cluster: {{ .cluster }}
        environment: {{ .name }}
        namespace: {{ .namespace | default "tecno-drive-system" }}
        repoURL: {{ .repoURL | default $.Values.argocd.repoURL }}
        targetRevision: {{ .targetRevision | default "HEAD" }}
      {{- end }}
  
  template:
    metadata:
      name: 'tecno-drive-{{`{{environment}}`}}'
      labels:
        environment: '{{`{{environment}}`}}'
    spec:
      project: default
      source:
        repoURL: '{{`{{repoURL}}`}}'
        targetRevision: '{{`{{targetRevision}}`}}'
        path: kubernetes/secure-cluster
        helm:
          valueFiles:
            - values.yaml
            - 'values-{{`{{environment}}`}}.yaml'
      destination:
        server: '{{`{{cluster}}`}}'
        namespace: '{{`{{namespace}}`}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ApplyOutOfSyncOnly=true
{{- end }}
{{- end }}
