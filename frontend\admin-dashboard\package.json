{"name": "admin-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@apollo/client": "^3.8.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@googlemaps/react-wrapper": "^1.2.0", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/x-charts": "^8.7.0", "@mui/x-data-grid": "^8.7.0", "@mui/x-date-pickers": "^8.9.0", "@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/google.maps": "^3.58.1", "@types/jest": "^27.5.2", "@types/leaflet": "^1.9.20", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "antd": "^5.21.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "graphql": "^16.8.1", "graphql-ws": "^5.14.2", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^4.2.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "recharts": "^3.0.2", "socket.io-client": "^4.8.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "lodash": "^4.17.21", "moment": "^2.29.4", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-error-boundary": "^4.0.11", "ws": "^8.14.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/ws": "^8.5.10"}}