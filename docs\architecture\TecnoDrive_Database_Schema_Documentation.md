# TecnoDrive Platform - Database Schema & Data Flow Documentation

## 📋 نظرة عامة

منصة TecnoDrive تستخدم معمارية الخدمات المصغرة (Microservices) مع قواعد بيانات منفصلة لكل خدمة، مما يضمن الفصل الكامل للبيانات وقابلية التوسع.

## 🗄️ قواعد البيانات الرئيسية

### 1. **tecnodrive_auth** - خدمة المصادقة
- **المنفذ**: 8081
- **الغرض**: إدارة المصادقة والتفويض
- **الجداول الرئيسية**:
  - `users` - بيانات المستخدمين للمصادقة
  - `roles` - الأدوار والصلاحيات
  - `sessions` - جلسات المستخدمين

### 2. **tecnodrive_users** - خدمة المستخدمين
- **المنفذ**: 8083
- **الغرض**: إدارة ملفات المستخدمين الشخصية
- **الجداول الرئيسية**:
  - `users` - البيانات الشخصية الكاملة
  - `user_profiles` - الملفات الشخصية المفصلة
  - `user_documents` - الوثائق والهويات

### 3. **tecnodrive_rides** - خدمة الرحلات
- **المنفذ**: 8082
- **الغرض**: إدارة طلبات الرحلات والحجوزات
- **الجداول الرئيسية**:
  - `ride_requests` - طلبات الرحلات
  - `rides` - الرحلات النشطة
  - `ride_ratings` - تقييمات الرحلات

### 4. **tecnodrive_fleet** - خدمة الأسطول
- **المنفذ**: 8084
- **الغرض**: إدارة المركبات والسائقين
- **الجداول الرئيسية**:
  - `vehicles` - بيانات المركبات
  - `vehicle_types` - أنواع المركبات
  - `drivers` - بيانات السائقين

### 5. **tecnodrive_location** - خدمة المواقع
- **المنفذ**: 8085
- **الغرض**: تتبع المواقع الجغرافية
- **الجداول الرئيسية**:
  - `locations` - المواقع الحالية
  - `location_history` - تاريخ المواقع
  - `geofences` - المناطق الجغرافية

### 6. **tecnodrive_payments** - خدمة المدفوعات
- **المنفذ**: 8086
- **الغرض**: معالجة المدفوعات والفواتير
- **الجداول الرئيسية**:
  - `payments` - المعاملات المالية
  - `invoices` - الفواتير
  - `wallets` - محافظ المستخدمين

### 7. **tecnodrive_parcels** - خدمة الطرود
- **المنفذ**: 8087
- **الغرض**: إدارة شحن الطرود
- **الجداول الرئيسية**:
  - `parcels` - بيانات الطرود
  - `parcel_tracking` - تتبع الطرود
  - `delivery_routes` - مسارات التوصيل

### 8. **tecnodrive_notifications** - خدمة الإشعارات
- **المنفذ**: 8088
- **الغرض**: إرسال الإشعارات والرسائل
- **الجداول الرئيسية**:
  - `notifications` - الإشعارات
  - `notification_templates` - قوالب الإشعارات
  - `user_preferences` - تفضيلات المستخدمين

### 9. **tecnodrive_analytics** - خدمة التحليلات
- **المنفذ**: 8089
- **الغرض**: تحليل البيانات والتقارير
- **الجداول الرئيسية**:
  - `analytics_events` - أحداث التحليل
  - `reports` - التقارير
  - `metrics` - المقاييس

## 🔗 العلاقات بين الخدمات

### العلاقات الأساسية:
1. **User Service ↔ Auth Service**: التحقق من الهوية
2. **Ride Service ↔ User Service**: التحقق من بيانات المستخدم
3. **Ride Service ↔ Fleet Service**: تخصيص المركبات
4. **Ride Service ↔ Location Service**: تتبع المواقع
5. **Ride Service ↔ Payment Service**: معالجة المدفوعات
6. **All Services ↔ Notification Service**: إرسال الإشعارات

## 📊 تدفق البيانات الرئيسي

### 1. تدفق طلب الرحلة:
```
Mobile App → API Gateway → Ride Service → User Service (التحقق)
                                      → Fleet Service (تخصيص مركبة)
                                      → Location Service (تحديد المسار)
                                      → Payment Service (حساب التكلفة)
                                      → Notification Service (إشعار السائق)
```

### 2. تدفق تتبع الموقع:
```
GPS Device → Location Service → Redis Cache → Ride Service → WebSocket → Mobile App
```

### 3. تدفق المدفوعات:
```
Mobile App → Payment Service → Payment Gateway → Bank → Payment Service → Ride Service
```

## 🔧 التقنيات المستخدمة

### قواعد البيانات:
- **PostgreSQL 15**: قاعدة البيانات الرئيسية
- **PostGIS**: للبيانات الجغرافية
- **Redis**: للتخزين المؤقت
- **SQLite**: للتطبيق المحمول (Room Database)

### أدوات التطوير:
- **Spring Boot**: إطار العمل الرئيسي
- **JPA/Hibernate**: لإدارة قواعد البيانات
- **Flyway**: لإدارة هجرة قواعد البيانات
- **Docker**: للحاويات
- **Eureka**: لاكتشاف الخدمات

## 📈 الأداء والتحسين

### الفهارس المحسنة:
- فهارس على المواقع الجغرافية
- فهارس على التواريخ والأوقات
- فهارس على معرفات المستخدمين
- فهارس مركبة للاستعلامات المعقدة

### التخزين المؤقت:
- بيانات المستخدمين في Redis
- المواقع الحالية في Redis
- نتائج البحث المتكررة

## 🔒 الأمان

### حماية البيانات:
- تشفير كلمات المرور باستخدام BCrypt
- تشفير البيانات الحساسة
- استخدام JWT للمصادقة
- HTTPS لجميع الاتصالات

### التحكم في الوصول:
- نظام الأدوار والصلاحيات
- التحقق من الهوية على مستوى الخدمة
- تسجيل جميع العمليات الحساسة

## 📱 قاعدة البيانات المحلية (Mobile)

### Room Database (SQLite):
- `user_info` - معلومات المستخدم المحلية
- `booking_info` - الحجوزات المحلية
- `cache_data` - البيانات المؤقتة
- `offline_requests` - الطلبات غير المتصلة

## 🚀 التوسع المستقبلي

### خطط التطوير:
1. إضافة خدمة الذكاء الاصطناعي
2. تحسين خوارزميات التوجيه
3. إضافة دعم للمدفوعات الرقمية
4. تطوير نظام الولاء والمكافآت
5. إضافة التحليلات المتقدمة

## 📊 جدول ملخص الجداول الرئيسية

| الجدول | الخدمة | الغرض | المفاتيح الرئيسية | العلاقات |
|--------|--------|-------|------------------|----------|
| `users` | User Service | إدارة المستخدمين | `id (UUID)` | → user_profiles, bookings, rides |
| `user_profiles` | User Service | الملفات الشخصية | `id (UUID)`, `user_id (FK)` | users ← |
| `ride_requests` | Ride Service | طلبات الرحلات | `id (BIGINT)`, `customer_id (FK)` | → rides |
| `rides` | Ride Service | الرحلات النشطة | `id (BIGINT)`, `customer_id (FK)`, `driver_id (FK)` | ride_requests ←, payments → |
| `vehicles` | Fleet Service | المركبات | `id (BIGINT)`, `vehicle_type_id (FK)` | vehicle_types ←, rides → |
| `vehicle_types` | Fleet Service | أنواع المركبات | `id (BIGINT)` | → vehicles |
| `locations` | Location Service | المواقع الحالية | `id (UUID)`, `entity_id` | تتبع جميع الكيانات |
| `tracking_devices` | Tracking Service | أجهزة التتبع | `id (BIGINT)`, `vehicle_id (FK)` | → tracking_data |
| `tracking_data` | Tracking Service | بيانات التتبع | `id (BIGINT)`, `device_id (FK)` | tracking_devices ← |
| `payments` | Payment Service | المدفوعات | `payment_id (UUID)`, `user_id (FK)` | users ←, rides ← |
| `parcels` | Parcel Service | الطرود | `parcel_id (TEXT)`, `user_id (FK)` | users ← |
| `bookings` | Booking Service | الحجوزات | `booking_id (TEXT)`, `user_id (FK)` | users ←, payments → |

## 🔄 أنماط تدفق البيانات الشائعة

### 1. نمط Command Query Responsibility Segregation (CQRS):
- **الكتابة**: عبر API Gateway إلى الخدمة المناسبة
- **القراءة**: من Redis Cache أو قاعدة البيانات مباشرة

### 2. نمط Event Sourcing:
- تسجيل جميع الأحداث في `analytics_events`
- إعادة بناء الحالة من الأحداث المسجلة

### 3. نمط Saga Pattern:
- للمعاملات الموزعة عبر عدة خدمات
- مثال: إنشاء رحلة → تخصيص مركبة → معالجة دفع

## 🎯 نصائح للتطوير

### استعلامات محسنة:
```sql
-- البحث عن المركبات القريبة
SELECT v.* FROM vehicles v
JOIN locations l ON v.id = l.entity_id::bigint
WHERE ST_DWithin(l.coordinates, ST_Point(?, ?), 5000)
AND v.status = 'available';

-- تتبع الرحلات النشطة
SELECT r.*, u1.first_name as passenger_name, u2.first_name as driver_name
FROM rides r
JOIN users u1 ON r.customer_id = u1.id
JOIN users u2 ON r.driver_id = u2.id
WHERE r.status IN ('assigned', 'in_progress');
```

### فهارس مهمة:
```sql
-- فهارس جغرافية
CREATE INDEX idx_locations_coordinates ON locations USING GIST(coordinates);

-- فهارس زمنية
CREATE INDEX idx_rides_created_at ON rides(created_at);
CREATE INDEX idx_tracking_data_timestamp ON tracking_data(timestamp);

-- فهارس مركبة
CREATE INDEX idx_rides_status_customer ON rides(status, customer_id);
```

---
**تم إنشاؤه بواسطة**: Augment Agent
**التاريخ**: 2025-07-17
**الإصدار**: 1.0
