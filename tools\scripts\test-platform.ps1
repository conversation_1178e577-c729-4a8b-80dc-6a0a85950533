# TECNODRIVE Platform Testing Script
# This script performs comprehensive testing of all platform components

param(
    [Parameter(Mandatory=$false)]
    [switch]$DatabaseOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$ServicesOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# Function to log test results
function Write-TestLog {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Type) {
        "INFO" { "White" }
        "SUCCESS" { "Green" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        "TEST" { "Cyan" }
        "PASS" { "Green" }
        "FAIL" { "Red" }
    }
    
    Write-Host "[$timestamp] [$Type] $Message" -ForegroundColor $color
}

# Function to test database connectivity
function Test-DatabaseConnectivity {
    Write-TestLog "Testing database connectivity..." "TEST"
    
    $databases = @(
        "tecnodrive_auth",
        "tecnodrive_users", 
        "tecnodrive_rides",
        "tecnodrive_fleet",
        "tecnodrive_payments",
        "tecnodrive_notifications",
        "tecnodrive_financial",
        "tecnodrive_hr",
        "tecnodrive_analytics",
        "tecnodrive_saas",
        "tecnodrive_location",
        "tecnodrive_tracking",
        "tecnodrive_parcels"
    )
    
    $passCount = 0
    $totalCount = $databases.Count
    
    foreach ($db in $databases) {
        try {
            $env:PGPASSWORD = "TecnoDrive2025!Secure#Platform"
            $result = psql -h localhost -p 5432 -U tecnodrive_admin -d $db -c "SELECT 1;" 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "✅ $db - Connection successful" "PASS"
                $passCount++
            } else {
                Write-TestLog "❌ $db - Connection failed" "FAIL"
            }
        }
        catch {
            Write-TestLog "❌ $db - Connection error: $_" "FAIL"
        }
    }
    
    Write-TestLog "Database connectivity: $passCount/$totalCount passed" "INFO"
    return ($passCount -eq $totalCount)
}

# Function to test Redis connectivity
function Test-RedisConnectivity {
    Write-TestLog "Testing Redis connectivity..." "TEST"
    
    try {
        $result = redis-cli -h localhost -p 6379 -a "TecnoDrive2025!Redis#Cache" ping 2>$null
        
        if ($result -eq "PONG") {
            Write-TestLog "✅ Redis - Connection successful" "PASS"
            return $true
        } else {
            Write-TestLog "❌ Redis - Connection failed" "FAIL"
            return $false
        }
    }
    catch {
        Write-TestLog "❌ Redis - Connection error: $_" "FAIL"
        return $false
    }
}

# Function to test service endpoints
function Test-ServiceEndpoints {
    Write-TestLog "Testing service endpoints..." "TEST"
    
    $services = @(
        @{ Name = "Eureka Server"; Url = "http://localhost:8761/actuator/health"; Port = 8761 },
        @{ Name = "API Gateway"; Url = "http://localhost:8080/actuator/health"; Port = 8080 },
        @{ Name = "Auth Service"; Url = "http://localhost:8081/actuator/health"; Port = 8081 },
        @{ Name = "User Service"; Url = "http://localhost:8082/actuator/health"; Port = 8082 },
        @{ Name = "Ride Service"; Url = "http://localhost:8083/actuator/health"; Port = 8083 },
        @{ Name = "Fleet Service"; Url = "http://localhost:8084/actuator/health"; Port = 8084 },
        @{ Name = "Payment Service"; Url = "http://localhost:8085/actuator/health"; Port = 8085 }
    )
    
    $passCount = 0
    $totalCount = $services.Count
    
    foreach ($service in $services) {
        # First check if port is listening
        $portOpen = Test-NetConnection -ComputerName localhost -Port $service.Port -InformationLevel Quiet -WarningAction SilentlyContinue
        
        if (-not $portOpen) {
            Write-TestLog "❌ $($service.Name) - Port $($service.Port) not listening" "FAIL"
            continue
        }
        
        try {
            $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 10 -ErrorAction Stop
            
            if ($response.StatusCode -eq 200) {
                $content = $response.Content | ConvertFrom-Json
                if ($content.status -eq "UP") {
                    Write-TestLog "✅ $($service.Name) - Health check passed" "PASS"
                    $passCount++
                } else {
                    Write-TestLog "❌ $($service.Name) - Service unhealthy: $($content.status)" "FAIL"
                }
            } else {
                Write-TestLog "❌ $($service.Name) - HTTP $($response.StatusCode)" "FAIL"
            }
        }
        catch {
            Write-TestLog "❌ $($service.Name) - Health check failed: $($_.Exception.Message)" "FAIL"
        }
    }
    
    Write-TestLog "Service endpoints: $passCount/$totalCount passed" "INFO"
    return ($passCount -eq $totalCount)
}

# Function to test database schemas
function Test-DatabaseSchemas {
    Write-TestLog "Testing database schemas..." "TEST"
    
    $schemaTests = @(
        @{ Database = "tecnodrive_auth"; Table = "users" },
        @{ Database = "tecnodrive_users"; Table = "user_profiles" },
        @{ Database = "tecnodrive_rides"; Table = "rides" },
        @{ Database = "tecnodrive_fleet"; Table = "vehicles" },
        @{ Database = "tecnodrive_payments"; Table = "payment_transactions" }
    )
    
    $passCount = 0
    $totalCount = $schemaTests.Count
    
    foreach ($test in $schemaTests) {
        try {
            $env:PGPASSWORD = "TecnoDrive2025!Secure#Platform"
            $result = psql -h localhost -p 5432 -U tecnodrive_admin -d $test.Database -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '$($test.Table)';" -t 2>$null
            
            if ($LASTEXITCODE -eq 0 -and $result.Trim() -eq "1") {
                Write-TestLog "✅ $($test.Database).$($test.Table) - Table exists" "PASS"
                $passCount++
            } else {
                Write-TestLog "❌ $($test.Database).$($test.Table) - Table missing" "FAIL"
            }
        }
        catch {
            Write-TestLog "❌ $($test.Database).$($test.Table) - Schema test error: $_" "FAIL"
        }
    }
    
    Write-TestLog "Database schemas: $passCount/$totalCount passed" "INFO"
    return ($passCount -eq $totalCount)
}

# Function to test API authentication
function Test-APIAuthentication {
    Write-TestLog "Testing API authentication..." "TEST"
    
    try {
        # Test unauthenticated request (should fail)
        $response = Invoke-WebRequest -Uri "http://localhost:8080/api/users/profile" -ErrorAction SilentlyContinue
        
        if ($response.StatusCode -eq 401 -or $response.StatusCode -eq 403) {
            Write-TestLog "✅ API Gateway - Authentication required (expected)" "PASS"
            return $true
        } else {
            Write-TestLog "❌ API Gateway - Authentication not enforced" "FAIL"
            return $false
        }
    }
    catch {
        if ($_.Exception.Response.StatusCode -eq 401 -or $_.Exception.Response.StatusCode -eq 403) {
            Write-TestLog "✅ API Gateway - Authentication required (expected)" "PASS"
            return $true
        } else {
            Write-TestLog "❌ API Gateway - Unexpected error: $_" "FAIL"
            return $false
        }
    }
}

# Function to generate test report
function Generate-TestReport {
    param(
        [hashtable]$Results
    )
    
    $reportPath = "test-results-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
    
    $report = @"
TECNODRIVE Platform Test Report
Generated: $(Get-Date)
Environment: $env:COMPUTERNAME

TEST RESULTS:
=============
Database Connectivity: $(if($Results.Database) { "PASS" } else { "FAIL" })
Redis Connectivity: $(if($Results.Redis) { "PASS" } else { "FAIL" })
Service Endpoints: $(if($Results.Services) { "PASS" } else { "FAIL" })
Database Schemas: $(if($Results.Schemas) { "PASS" } else { "FAIL" })
API Authentication: $(if($Results.Auth) { "PASS" } else { "FAIL" })

OVERALL STATUS: $(if($Results.Values -contains $false) { "FAILED" } else { "PASSED" })

RECOMMENDATIONS:
===============
"@

    if (-not $Results.Database) {
        $report += "`n- Check PostgreSQL service and database configurations"
    }
    if (-not $Results.Redis) {
        $report += "`n- Check Redis service and authentication settings"
    }
    if (-not $Results.Services) {
        $report += "`n- Check application services startup and health endpoints"
    }
    if (-not $Results.Schemas) {
        $report += "`n- Run database schema creation scripts"
    }
    if (-not $Results.Auth) {
        $report += "`n- Check API Gateway authentication configuration"
    }
    
    Set-Content -Path $reportPath -Value $report
    Write-TestLog "Test report saved: $reportPath" "INFO"
}

# Main testing execution
try {
    Write-TestLog "🧪 Starting TECNODRIVE Platform Tests..." "TEST"
    
    $testResults = @{
        Database = $false
        Redis = $false
        Services = $false
        Schemas = $false
        Auth = $false
    }
    
    # Run tests based on parameters
    if ($DatabaseOnly) {
        $testResults.Database = Test-DatabaseConnectivity
        $testResults.Redis = Test-RedisConnectivity
        $testResults.Schemas = Test-DatabaseSchemas
    }
    elseif ($ServicesOnly) {
        $testResults.Services = Test-ServiceEndpoints
        $testResults.Auth = Test-APIAuthentication
    }
    else {
        # Run all tests
        $testResults.Database = Test-DatabaseConnectivity
        $testResults.Redis = Test-RedisConnectivity
        $testResults.Schemas = Test-DatabaseSchemas
        $testResults.Services = Test-ServiceEndpoints
        $testResults.Auth = Test-APIAuthentication
    }
    
    # Generate report
    Generate-TestReport -Results $testResults
    
    # Summary
    $passedTests = ($testResults.Values | Where-Object { $_ -eq $true }).Count
    $totalTests = $testResults.Count
    
    Write-TestLog "" "INFO"
    Write-TestLog "📊 Test Summary: $passedTests/$totalTests tests passed" "INFO"
    
    if ($testResults.Values -contains $false) {
        Write-TestLog "❌ Some tests failed. Check the logs above for details." "ERROR"
        exit 1
    } else {
        Write-TestLog "✅ All tests passed! Platform is working correctly." "SUCCESS"
        exit 0
    }
    
} catch {
    Write-TestLog "Testing failed: $_" "ERROR"
    exit 1
}
