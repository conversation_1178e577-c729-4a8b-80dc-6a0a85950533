package com.tecnodrive.paymentservice.dto;

import com.tecnodrive.paymentservice.entity.Payment;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * Payment Response DTO
 * 
 * Used for returning payment information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentResponse {

    /**
     * Payment ID
     */
    private String id;

    /**
     * ID of the entity being paid for
     */
    private String entityId;

    /**
     * Type of entity being paid for
     */
    private String entityType;

    /**
     * ID of the user making the payment
     */
    private String payerUserId;

    /**
     * ID of the user receiving the payment
     */
    private String payeeUserId;

    /**
     * Payment amount
     */
    private BigDecimal amount;

    /**
     * Currency code
     */
    private String currency;

    /**
     * Payment status
     */
    private Payment.PaymentStatus status;

    /**
     * Payment method used
     */
    private Payment.PaymentMethod paymentMethod;

    /**
     * External payment gateway transaction ID
     */
    private String gatewayTransactionId;

    /**
     * Payment description or notes
     */
    private String description;

    /**
     * Additional metadata
     */
    private String metadata;

    /**
     * Creation timestamp
     */
    private Instant createdAt;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;
}
