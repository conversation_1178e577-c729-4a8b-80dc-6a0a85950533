// Persistent Auth Service - Remember Login
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
}

export interface AuthData {
  token: string;
  user: User;
  rememberMe: boolean;
  loginTime: number;
  expiresAt: number;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: User;
  };
}

// Storage keys
const STORAGE_KEYS = {
  AUTH_TOKEN: 'tecnodrive_auth_token',
  USER_DATA: 'tecnodrive_user_data',
  REMEMBER_ME: 'tecnodrive_remember_me',
  LOGIN_TIME: 'tecnodrive_login_time',
  EXPIRES_AT: 'tecnodrive_expires_at',
  AUTO_LOGIN: 'tecnodrive_auto_login'
};

// Mock users database
const mockUsers: Array<User & { password: string }> = [
  {
    id: '1',
    email: 'a<PERSON>@tecnodrive.com',
    password: 'password123',
    name: '<PERSON><PERSON> <PERSON>',
    role: 'ADMIN',
    permissions: ['READ', 'WRIT<PERSON>', 'DELETE', 'ADMIN']
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'System Admin',
    role: 'ADMIN',
    permissions: ['READ', 'WRITE', 'DELETE', 'ADMIN']
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'manager123',
    name: 'Operations Manager',
    role: 'MANAGER',
    permissions: ['READ', 'WRITE']
  }
];

// Generate secure token
function generateSecureToken(user: User, rememberMe: boolean): string {
  const now = Date.now();
  const expiresIn = rememberMe ? (30 * 24 * 60 * 60 * 1000) : (24 * 60 * 60 * 1000); // 30 days or 24 hours
  
  const payload = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    permissions: user.permissions,
    iat: now,
    exp: now + expiresIn,
    rememberMe: rememberMe
  };
  
  try {
    // Use simple encoding for demo (in production, use proper JWT)
    const jsonString = JSON.stringify(payload);
    const encoder = new TextEncoder();
    const data = encoder.encode(jsonString);
    
    let binary = '';
    data.forEach(byte => binary += String.fromCharCode(byte));
    return btoa(binary);
  } catch (error) {
    // Fallback: simple token
    return `tecnodrive_token_${user.id}_${now}_${rememberMe ? 'persistent' : 'session'}`;
  }
}

// Store auth data
function storeAuthData(authData: AuthData): void {
  const storage = authData.rememberMe ? localStorage : sessionStorage;
  
  try {
    storage.setItem(STORAGE_KEYS.AUTH_TOKEN, authData.token);
    storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authData.user));
    storage.setItem(STORAGE_KEYS.REMEMBER_ME, authData.rememberMe.toString());
    storage.setItem(STORAGE_KEYS.LOGIN_TIME, authData.loginTime.toString());
    storage.setItem(STORAGE_KEYS.EXPIRES_AT, authData.expiresAt.toString());
    
    // Also store in localStorage for auto-login check
    if (authData.rememberMe) {
      localStorage.setItem(STORAGE_KEYS.AUTO_LOGIN, 'true');
    }
    
    console.log('✅ Auth data stored successfully', {
      storage: authData.rememberMe ? 'localStorage' : 'sessionStorage',
      rememberMe: authData.rememberMe,
      expiresAt: new Date(authData.expiresAt).toLocaleString()
    });
    
  } catch (error) {
    console.error('❌ Failed to store auth data:', error);
  }
}

// Retrieve auth data
function retrieveAuthData(): AuthData | null {
  try {
    // Check localStorage first (for persistent login)
    let token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    let userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    let rememberMe = localStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
    let loginTime = localStorage.getItem(STORAGE_KEYS.LOGIN_TIME);
    let expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
    
    // If not found in localStorage, check sessionStorage
    if (!token) {
      token = sessionStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      userData = sessionStorage.getItem(STORAGE_KEYS.USER_DATA);
      rememberMe = sessionStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
      loginTime = sessionStorage.getItem(STORAGE_KEYS.LOGIN_TIME);
      expiresAt = sessionStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
    }
    
    if (!token || !userData || !loginTime || !expiresAt) {
      return null;
    }
    
    const authData: AuthData = {
      token,
      user: JSON.parse(userData),
      rememberMe: rememberMe === 'true',
      loginTime: parseInt(loginTime),
      expiresAt: parseInt(expiresAt)
    };
    
    // Check if token is expired
    if (Date.now() > authData.expiresAt) {
      console.log('⚠️ Auth token expired, clearing data');
      clearAuthData();
      return null;
    }
    
    console.log('✅ Auth data retrieved successfully', {
      user: authData.user.email,
      rememberMe: authData.rememberMe,
      expiresAt: new Date(authData.expiresAt).toLocaleString()
    });
    
    return authData;
    
  } catch (error) {
    console.error('❌ Failed to retrieve auth data:', error);
    clearAuthData();
    return null;
  }
}

// Clear auth data
function clearAuthData(): void {
  const keysToRemove = Object.values(STORAGE_KEYS);
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  });
  
  // Also clear legacy keys
  const legacyKeys = ['authToken', 'token', 'user', 'currentUser', 'isAuthenticated'];
  legacyKeys.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  });
  
  console.log('🧹 Auth data cleared');
}

// Check if auto-login is enabled
export function shouldAutoLogin(): boolean {
  const autoLogin = localStorage.getItem(STORAGE_KEYS.AUTO_LOGIN);
  const authData = retrieveAuthData();
  
  return autoLogin === 'true' && authData !== null && authData.rememberMe;
}

// Persistent login function
export async function persistentLogin(request: LoginRequest): Promise<AuthResponse> {
  console.log('🔐 Persistent login attempt:', request.email, 'Remember:', request.rememberMe);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  if (!request.email || !request.password) {
    return {
      success: false,
      message: 'البريد الإلكتروني وكلمة المرور مطلوبان'
    };
  }
  
  const user = mockUsers.find(u => 
    u.email === request.email && u.password === request.password
  );
  
  if (!user) {
    return {
      success: false,
      message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
    };
  }
  
  const rememberMe = request.rememberMe || false;
  const token = generateSecureToken(user, rememberMe);
  const now = Date.now();
  const expiresIn = rememberMe ? (30 * 24 * 60 * 60 * 1000) : (24 * 60 * 60 * 1000);
  
  const authData: AuthData = {
    token,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      permissions: user.permissions
    },
    rememberMe,
    loginTime: now,
    expiresAt: now + expiresIn
  };
  
  // Store auth data
  storeAuthData(authData);
  
  console.log('✅ Persistent login successful:', {
    user: user.email,
    rememberMe: rememberMe,
    expiresAt: new Date(authData.expiresAt).toLocaleString()
  });
  
  return {
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    data: {
      token: authData.token,
      user: authData.user
    }
  };
}

// Auto-login function
export async function autoLogin(): Promise<AuthResponse | null> {
  console.log('🔄 Attempting auto-login...');
  
  const authData = retrieveAuthData();
  
  if (!authData) {
    console.log('❌ No valid auth data found for auto-login');
    return null;
  }
  
  if (!authData.rememberMe) {
    console.log('❌ Remember me not enabled, skipping auto-login');
    return null;
  }
  
  console.log('✅ Auto-login successful:', authData.user.email);
  
  return {
    success: true,
    message: 'تم تسجيل الدخول تلقائياً',
    data: {
      token: authData.token,
      user: authData.user
    }
  };
}

// Logout function
export async function persistentLogout(): Promise<AuthResponse> {
  console.log('🚪 Persistent logout');
  
  clearAuthData();
  
  return {
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  };
}

// Get current user
export function getCurrentUser(): User | null {
  const authData = retrieveAuthData();
  return authData ? authData.user : null;
}

// Check if authenticated
export function isAuthenticated(): boolean {
  const authData = retrieveAuthData();
  return authData !== null;
}

// Get auth token
export function getAuthToken(): string | null {
  const authData = retrieveAuthData();
  return authData ? authData.token : null;
}

// Extend session (refresh token)
export async function extendSession(): Promise<boolean> {
  const authData = retrieveAuthData();
  
  if (!authData) {
    return false;
  }
  
  // Extend expiration time
  const now = Date.now();
  const additionalTime = authData.rememberMe ? (30 * 24 * 60 * 60 * 1000) : (24 * 60 * 60 * 1000);
  
  authData.expiresAt = now + additionalTime;
  
  storeAuthData(authData);
  
  console.log('✅ Session extended until:', new Date(authData.expiresAt).toLocaleString());
  
  return true;
}
