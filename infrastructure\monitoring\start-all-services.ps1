#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Enhanced Startup Script
# This script starts all services with proper health checks and monitoring

Write-Host "🚀 Starting TECNO DRIVE Platform..." -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Cyan

# Function to check if a service is healthy
function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxRetries = 30,
        [int]$DelaySeconds = 10
    )
    
    Write-Host "🔍 Checking health of $ServiceName..." -ForegroundColor Yellow
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $ServiceName is healthy!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "⏳ Attempt $i/$MaxRetries - $ServiceName not ready yet..." -ForegroundColor Yellow
            Start-Sleep -Seconds $DelaySeconds
        }
    }
    
    Write-Host "❌ $ServiceName failed to become healthy after $MaxRetries attempts" -ForegroundColor Red
    return $false
}

# Step 1: Clean up any existing containers
Write-Host "🧹 Cleaning up existing containers..." -ForegroundColor Blue
docker-compose down --remove-orphans

# Step 2: Create all required databases
Write-Host "🗄️ Setting up databases..." -ForegroundColor Blue
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Create all databases
$databases = @(
    "auth_db", "ride_db", "fleet_db", "parcel_db", "payment_db",
    "notification_db", "financial_db", "hr_db", "analytics_db", 
    "saas_db", "location_db"
)

foreach ($db in $databases) {
    Write-Host "📊 Creating database: $db" -ForegroundColor Cyan
    docker exec infra-postgres-1 createdb -U postgres $db 2>$null
}

# Step 3: Start core services
Write-Host "🔍 Starting Eureka Server..." -ForegroundColor Blue
docker-compose up -d eureka-server

# Wait for Eureka to be healthy
if (-not (Test-ServiceHealth "Eureka Server" "http://localhost:8761/actuator/health")) {
    Write-Host "❌ Failed to start Eureka Server. Exiting..." -ForegroundColor Red
    exit 1
}

# Step 4: Start authentication service
Write-Host "🔐 Starting Auth Service..." -ForegroundColor Blue
docker-compose up -d auth-service

if (-not (Test-ServiceHealth "Auth Service" "http://localhost:8081/actuator/health")) {
    Write-Host "⚠️ Auth Service may have issues, but continuing..." -ForegroundColor Yellow
}

# Step 5: Start business services
Write-Host "🚗 Starting Business Services..." -ForegroundColor Blue
$businessServices = @(
    "parcel-service", "location-service", "ride-service", 
    "fleet-service", "payment-service"
)

foreach ($service in $businessServices) {
    Write-Host "🔄 Starting $service..." -ForegroundColor Cyan
    docker-compose up -d $service
    Start-Sleep -Seconds 15
}

# Step 6: Start support services
Write-Host "📱 Starting Support Services..." -ForegroundColor Blue
$supportServices = @(
    "notification-service", "financial-service", "hr-service",
    "analytics-service", "saas-management-service"
)

foreach ($service in $supportServices) {
    Write-Host "🔄 Starting $service..." -ForegroundColor Cyan
    docker-compose up -d $service
    Start-Sleep -Seconds 10
}

# Step 7: Start API Gateway
Write-Host "🌐 Starting API Gateway..." -ForegroundColor Blue
docker-compose up -d api-gateway

# Step 8: Final health check
Write-Host "🏥 Performing final health checks..." -ForegroundColor Blue
Start-Sleep -Seconds 30

$services = @(
    @{Name="Eureka Server"; Url="http://localhost:8761"; Port=8761},
    @{Name="Auth Service"; Url="http://localhost:8081/actuator/health"; Port=8081},
    @{Name="Parcel Service"; Url="http://localhost:8084/actuator/health"; Port=8084},
    @{Name="Location Service"; Url="http://localhost:8086/actuator/health"; Port=8086},
    @{Name="Analytics Service"; Url="http://localhost:8090/actuator/health"; Port=8090},
    @{Name="API Gateway"; Url="http://localhost:8080/actuator/health"; Port=8080}
)

$healthyServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) (Port $($service.Port)) - HEALTHY" -ForegroundColor Green
            $healthyServices++
        }
    }
    catch {
        Write-Host "❌ $($service.Name) (Port $($service.Port)) - UNHEALTHY" -ForegroundColor Red
    }
}

# Final summary
Write-Host "`n🎉 TECNO DRIVE Platform Startup Complete!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "📊 Services Status: $healthyServices/$totalServices healthy" -ForegroundColor White

if ($healthyServices -eq $totalServices) {
    Write-Host "🎯 All services are running perfectly!" -ForegroundColor Green
} elseif ($healthyServices -gt ($totalServices / 2)) {
    Write-Host "⚠️ Most services are running. Check logs for failed services." -ForegroundColor Yellow
} else {
    Write-Host "❌ Many services failed to start. Check Docker logs." -ForegroundColor Red
}

Write-Host "`n🌐 Access Points:" -ForegroundColor Cyan
Write-Host "• Eureka Dashboard: http://localhost:8761" -ForegroundColor White
Write-Host "• API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "• Auth Service: http://localhost:8081" -ForegroundColor White

Write-Host "`n📋 To check service status: docker-compose ps" -ForegroundColor Blue
Write-Host "📋 To view logs: docker-compose logs [service-name]" -ForegroundColor Blue
Write-Host "📋 To stop all: docker-compose down" -ForegroundColor Blue
