import React, { useState } from 'react';
import {
  A<PERSON>B<PERSON>,
  Too<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Box,
  InputBase,
  useTheme,
  alpha,
  Tooltip,
  Chip,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon,
  Logout as LogoutIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Language as LanguageIcon,
  Help as HelpIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';

interface EnhancedHeaderProps {
  sidebarCollapsed?: boolean;
  onToggleSidebar?: () => void;
  onToggleTheme?: () => void;
  isDarkMode?: boolean;
}

const EnhancedHeader: React.FC<EnhancedHeaderProps> = ({
  sidebarCollapsed = false,
  onToggleSidebar,
  onToggleTheme,
  isDarkMode = false,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationsAnchor, setNotificationsAnchor] = useState<null | HTMLElement>(null);
  const [searchValue, setSearchValue] = useState('');

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationsAnchor(null);
  };

  const getHeaderStyles = () => ({
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    boxShadow: `0 1px 3px ${alpha(theme.palette.common.black, 0.1)}`,
    borderBottom: `1px solid ${theme.palette.divider}`,
    position: 'fixed' as const,
    top: 0,
    left: sidebarCollapsed ? 80 : 280,
    right: 0,
    zIndex: theme.zIndex.appBar,
    transition: theme.transitions.create(['left'], {
      duration: theme.transitions.duration.standard,
    }),
  });

  const getSearchStyles = () => ({
    position: 'relative',
    borderRadius: theme.shape.borderRadius * 2,
    backgroundColor: alpha(theme.palette.common.black, 0.05),
    '&:hover': {
      backgroundColor: alpha(theme.palette.common.black, 0.08),
    },
    marginLeft: 0,
    width: '100%',
    maxWidth: 400,
    transition: theme.transitions.create(['background-color', 'box-shadow'], {
      duration: theme.transitions.duration.short,
    }),
    '&:focus-within': {
      backgroundColor: alpha(theme.palette.primary.main, 0.05),
      boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
    },
  });

  const mockNotifications = [
    { id: 1, title: 'رحلة جديدة', message: 'تم إنشاء رحلة جديدة #12345', time: 'منذ 5 دقائق', unread: true },
    { id: 2, title: 'دفعة مكتملة', message: 'تم استلام دفعة بقيمة 250 ريال', time: 'منذ 15 دقيقة', unread: true },
    { id: 3, title: 'تحديث النظام', message: 'تم تحديث النظام بنجاح', time: 'منذ ساعة', unread: false },
  ];

  const unreadCount = mockNotifications.filter(n => n.unread).length;

  return (
    <AppBar sx={getHeaderStyles()}>
      <Toolbar sx={{ justifyContent: 'space-between', px: 3 }}>
        {/* Left Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            edge="start"
            color="inherit"
            aria-label="toggle sidebar"
            onClick={onToggleSidebar}
            sx={{
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
              },
            }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" sx={{ fontWeight: 600, display: { xs: 'none', sm: 'block' } }}>
            لوحة التحكم الرئيسية
          </Typography>

          <Chip
            label="مباشر"
            size="small"
            color="success"
            variant="outlined"
            sx={{ display: { xs: 'none', md: 'flex' } }}
          />
        </Box>

        {/* Center Section - Search */}
        <Box sx={getSearchStyles()}>
          <Box sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1 }}>
            <SearchIcon sx={{ color: theme.palette.text.secondary, mr: 1 }} />
            <InputBase
              placeholder="البحث في النظام..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              sx={{
                flex: 1,
                '& .MuiInputBase-input': {
                  padding: 0,
                  fontSize: '0.9rem',
                },
              }}
            />
          </Box>
        </Box>

        {/* Right Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Theme Toggle */}
          <Tooltip title={isDarkMode ? 'الوضع الفاتح' : 'الوضع الداكن'} arrow>
            <IconButton
              color="inherit"
              onClick={onToggleTheme}
              sx={{
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
          </Tooltip>

          {/* Language */}
          <Tooltip title="تغيير اللغة" arrow>
            <IconButton
              color="inherit"
              sx={{
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              <LanguageIcon />
            </IconButton>
          </Tooltip>

          {/* Help */}
          <Tooltip title="المساعدة" arrow>
            <IconButton
              color="inherit"
              sx={{
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              <HelpIcon />
            </IconButton>
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="الإشعارات" arrow>
            <IconButton
              color="inherit"
              onClick={handleNotificationsOpen}
              sx={{
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              <Badge badgeContent={unreadCount} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Profile */}
          <Tooltip title="الملف الشخصي" arrow>
            <IconButton
              edge="end"
              aria-label="account of current user"
              aria-controls="primary-search-account-menu"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
              sx={{ ml: 1 }}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  backgroundColor: theme.palette.primary.main,
                  fontSize: '0.9rem',
                }}
              >
                أ
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              boxShadow: theme.shadows[8],
            },
          }}
        >
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              أحمد محمد
            </Typography>
            <Typography variant="caption" color="text.secondary">
              مدير النظام
            </Typography>
          </Box>
          <Divider />
          <MenuItem onClick={handleMenuClose}>
            <AccountIcon sx={{ mr: 2 }} />
            الملف الشخصي
          </MenuItem>
          <MenuItem onClick={handleMenuClose}>
            <SettingsIcon sx={{ mr: 2 }} />
            الإعدادات
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleMenuClose} sx={{ color: theme.palette.error.main }}>
            <LogoutIcon sx={{ mr: 2 }} />
            تسجيل الخروج
          </MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationsAnchor}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          open={Boolean(notificationsAnchor)}
          onClose={handleMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 320,
              maxHeight: 400,
              borderRadius: 2,
              boxShadow: theme.shadows[8],
            },
          }}
        >
          <Box sx={{ px: 2, py: 1, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              الإشعارات
            </Typography>
          </Box>
          {mockNotifications.map((notification) => (
            <MenuItem key={notification.id} onClick={handleMenuClose}>
              <Box sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: notification.unread ? 600 : 400 }}>
                    {notification.title}
                  </Typography>
                  {notification.unread && (
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: theme.palette.primary.main,
                        ml: 1,
                      }}
                    />
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                  {notification.message}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {notification.time}
                </Typography>
              </Box>
            </MenuItem>
          ))}
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default EnhancedHeader;
