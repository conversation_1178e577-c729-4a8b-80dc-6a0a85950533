package com.tecnodrive.userservice.controller;

import com.tecnodrive.common.dto.common.ApiResponse;
import com.tecnodrive.userservice.dto.UserRequest;
import com.tecnodrive.userservice.dto.UserResponse;
import com.tecnodrive.userservice.entity.User;
import com.tecnodrive.userservice.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * User Management REST Controller
 */
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;

    /**
     * Create a new user
     */
    @PostMapping
    public ResponseEntity<ApiResponse<UserResponse>> createUser(@Valid @RequestBody UserRequest.Create request) {
        log.info("Creating user with email: {}", request.getEmail());
        
        try {
            UserResponse response = userService.createUser(request);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error creating user: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create user: " + e.getMessage()));
        }
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{userId}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable UUID userId) {
        log.debug("Getting user by ID: {}", userId);
        
        try {
            UserResponse response = userService.getUserById(userId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting user: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get user by email
     */
    @GetMapping("/email/{email}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserByEmail(@PathVariable String email) {
        log.debug("Getting user by email: {}", email);
        
        try {
            UserResponse response = userService.getUserByEmail(email);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting user by email: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get user by phone number
     */
    @GetMapping("/phone/{phoneNumber}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserByPhoneNumber(@PathVariable String phoneNumber) {
        log.debug("Getting user by phone: {}", phoneNumber);
        
        try {
            UserResponse response = userService.getUserByPhoneNumber(phoneNumber);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting user by phone: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update user
     */
    @PutMapping("/{userId}")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable UUID userId,
            @Valid @RequestBody UserRequest.Update request) {
        log.info("Updating user: {}", userId);
        
        try {
            UserResponse response = userService.updateUser(userId, request);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error updating user: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update user: " + e.getMessage()));
        }
    }

    /**
     * Update user status
     */
    @PatchMapping("/{userId}/status")
    public ResponseEntity<ApiResponse<UserResponse>> updateUserStatus(
            @PathVariable UUID userId,
            @Valid @RequestBody UserRequest.StatusUpdate request) {
        log.info("Updating user status: {} to {}", userId, request.getStatus());
        
        try {
            UserResponse response = userService.updateUserStatus(userId, request);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error updating user status: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update user status: " + e.getMessage()));
        }
    }

    /**
     * Update notification preferences
     */
    @PatchMapping("/{userId}/notifications")
    public ResponseEntity<ApiResponse<UserResponse>> updateNotificationPreferences(
            @PathVariable UUID userId,
            @Valid @RequestBody UserRequest.NotificationPreferences request) {
        log.info("Updating notification preferences for user: {}", userId);
        
        try {
            UserResponse response = userService.updateNotificationPreferences(userId, request);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error updating notification preferences: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update notification preferences: " + e.getMessage()));
        }
    }

    /**
     * Delete user
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable UUID userId) {
        log.info("Deleting user: {}", userId);

        try {
            userService.deleteUser(userId);
            return ResponseEntity.ok(ApiResponse.success("User deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting user: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete user: " + e.getMessage()));
        }
    }

    /**
     * Search users with filters
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<UserResponse.Summary>>> searchUsers(@Valid UserRequest.Search request) {
        log.debug("Searching users with filters: {}", request);
        
        try {
            Page<UserResponse.Summary> response = userService.searchUsers(request);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error searching users: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to search users: " + e.getMessage()));
        }
    }

    /**
     * Get users by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<List<UserResponse.Summary>>> getUsersByStatus(@PathVariable User.UserStatus status) {
        log.debug("Getting users by status: {}", status);
        
        try {
            List<UserResponse.Summary> response = userService.getUsersByStatus(status);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting users by status: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get users by status: " + e.getMessage()));
        }
    }

    /**
     * Get users by type
     */
    @GetMapping("/type/{userType}")
    public ResponseEntity<ApiResponse<List<UserResponse.Summary>>> getUsersByType(@PathVariable User.UserType userType) {
        log.debug("Getting users by type: {}", userType);
        
        try {
            List<UserResponse.Summary> response = userService.getUsersByType(userType);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting users by type: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get users by type: " + e.getMessage()));
        }
    }

    /**
     * Get users by company
     */
    @GetMapping("/company/{companyId}")
    public ResponseEntity<ApiResponse<List<UserResponse.Summary>>> getUsersByCompany(@PathVariable UUID companyId) {
        log.debug("Getting users by company: {}", companyId);
        
        try {
            List<UserResponse.Summary> response = userService.getUsersByCompany(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting users by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get users by company: " + e.getMessage()));
        }
    }

    /**
     * Get public profile
     */
    @GetMapping("/{userId}/public")
    public ResponseEntity<ApiResponse<UserResponse.PublicProfile>> getPublicProfile(@PathVariable UUID userId) {
        log.debug("Getting public profile for user: {}", userId);
        
        try {
            UserResponse.PublicProfile response = userService.getPublicProfile(userId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting public profile: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Verify email
     */
    @PostMapping("/{userId}/verify-email")
    public ResponseEntity<ApiResponse<UserResponse>> verifyEmail(@PathVariable UUID userId) {
        log.info("Verifying email for user: {}", userId);
        
        try {
            UserResponse response = userService.verifyEmail(userId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error verifying email: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to verify email: " + e.getMessage()));
        }
    }

    /**
     * Verify phone
     */
    @PostMapping("/{userId}/verify-phone")
    public ResponseEntity<ApiResponse<UserResponse>> verifyPhone(@PathVariable UUID userId) {
        log.info("Verifying phone for user: {}", userId);
        
        try {
            UserResponse response = userService.verifyPhone(userId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error verifying phone: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to verify phone: " + e.getMessage()));
        }
    }

    /**
     * Update last login
     */
    @PostMapping("/{userId}/last-login")
    public ResponseEntity<ApiResponse<String>> updateLastLogin(@PathVariable UUID userId) {
        log.debug("Updating last login for user: {}", userId);
        
        try {
            userService.updateLastLogin(userId);
            return ResponseEntity.ok(ApiResponse.success("Last login updated successfully"));
        } catch (Exception e) {
            log.error("Error updating last login: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update last login: " + e.getMessage()));
        }
    }

    /**
     * Check if email exists
     */
    @GetMapping("/check-email/{email}")
    public ResponseEntity<ApiResponse<Boolean>> emailExists(@PathVariable String email) {
        log.debug("Checking if email exists: {}", email);
        
        boolean exists = userService.emailExists(email);
        return ResponseEntity.ok(ApiResponse.success(exists));
    }

    /**
     * Check if phone exists
     */
    @GetMapping("/check-phone/{phoneNumber}")
    public ResponseEntity<ApiResponse<Boolean>> phoneExists(@PathVariable String phoneNumber) {
        log.debug("Checking if phone exists: {}", phoneNumber);
        
        boolean exists = userService.phoneExists(phoneNumber);
        return ResponseEntity.ok(ApiResponse.success(exists));
    }

    /**
     * Get user statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<UserResponse.Statistics>> getUserStatistics() {
        log.debug("Getting user statistics");
        
        try {
            UserResponse.Statistics response = userService.getUserStatistics();
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting user statistics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get user statistics: " + e.getMessage()));
        }
    }
}
