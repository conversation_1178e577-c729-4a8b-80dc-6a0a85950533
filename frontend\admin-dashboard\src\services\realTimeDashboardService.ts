import { io, Socket } from 'socket.io-client';
import { EventEmitter } from 'events';

// Enhanced Real-time Dashboard Types
export interface DashboardMetrics {
  timestamp: string;
  activeTrips: number;
  activeDrivers: number;
  activeVehicles: number;
  totalRevenue: number;
  averageWaitTime: number;
  customerSatisfaction: number;
  systemHealth: number;
  alerts: Alert[];
}

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  source: string;
  acknowledged: boolean;
  actions?: AlertAction[];
}

export interface AlertAction {
  id: string;
  label: string;
  action: string;
  parameters?: Record<string, any>;
}

export interface LiveEvent {
  id: string;
  type: 'trip_started' | 'trip_completed' | 'driver_online' | 'vehicle_maintenance' | 'payment_processed' | 'risk_detected';
  timestamp: string;
  data: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  tenantId: string;
}

export interface PerformanceMetrics {
  timestamp: string;
  cpu: number;
  memory: number;
  responseTime: number;
  throughput: number;
  errorRate: number;
  activeConnections: number;
}

export interface GeospatialEvent {
  id: string;
  type: 'vehicle_location' | 'trip_route' | 'geofence_entry' | 'geofence_exit' | 'traffic_incident';
  coordinates: [number, number];
  data: Record<string, any>;
  timestamp: string;
}

// Event Sourcing Types
export interface DomainEvent {
  id: string;
  aggregateId: string;
  aggregateType: string;
  eventType: string;
  eventData: Record<string, any>;
  timestamp: string;
  version: number;
  userId?: string;
  tenantId: string;
}

export interface EventStream {
  aggregateId: string;
  events: DomainEvent[];
  version: number;
}

// CQRS Types
export interface Command {
  id: string;
  type: string;
  aggregateId: string;
  payload: Record<string, any>;
  timestamp: string;
  userId: string;
  tenantId: string;
}

export interface QueryResult<T> {
  data: T;
  timestamp: string;
  cached: boolean;
  source: 'database' | 'cache' | 'realtime';
}

class RealTimeDashboardService extends EventEmitter {
  private socket: Socket | null = null;
  private baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8080';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventBuffer: LiveEvent[] = [];
  private maxBufferSize = 1000;
  private subscriptions = new Map<string, Set<Function>>();

  constructor() {
    super();
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    this.socket = io(`${this.baseURL}/dashboard`, {
      transports: ['websocket'],
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: 1000,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to Real-time Dashboard');
      this.reconnectAttempts = 0;
      this.emit('connected');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from Real-time Dashboard:', reason);
      this.emit('disconnected', reason);
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.reconnectAttempts = attemptNumber;
      this.emit('reconnecting', attemptNumber);
    });

    // Dashboard metrics updates
    this.socket.on('dashboard-metrics', (metrics: DashboardMetrics) => {
      this.emit('metrics-updated', metrics);
      this.notifySubscribers('metrics', metrics);
    });

    // Live events
    this.socket.on('live-event', (event: LiveEvent) => {
      this.addToEventBuffer(event);
      this.emit('live-event', event);
      this.notifySubscribers('events', event);
    });

    // Performance metrics
    this.socket.on('performance-metrics', (metrics: PerformanceMetrics) => {
      this.emit('performance-updated', metrics);
      this.notifySubscribers('performance', metrics);
    });

    // Geospatial events
    this.socket.on('geospatial-event', (event: GeospatialEvent) => {
      this.emit('geospatial-event', event);
      this.notifySubscribers('geospatial', event);
    });

    // Alerts
    this.socket.on('new-alert', (alert: Alert) => {
      this.emit('new-alert', alert);
      this.notifySubscribers('alerts', alert);
    });

    // Domain events (Event Sourcing)
    this.socket.on('domain-event', (event: DomainEvent) => {
      this.emit('domain-event', event);
      this.notifySubscribers('domain-events', event);
    });
  }

  // Subscription Management
  subscribe(channel: string, callback: Function): () => void {
    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set());
    }
    this.subscriptions.get(channel)!.add(callback);

    // Return unsubscribe function
    return () => {
      const channelSubscriptions = this.subscriptions.get(channel);
      if (channelSubscriptions) {
        channelSubscriptions.delete(callback);
        if (channelSubscriptions.size === 0) {
          this.subscriptions.delete(channel);
        }
      }
    };
  }

  private notifySubscribers(channel: string, data: any) {
    const channelSubscriptions = this.subscriptions.get(channel);
    if (channelSubscriptions) {
      channelSubscriptions.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in subscription callback for channel ${channel}:`, error);
        }
      });
    }
  }

  // Dashboard Subscriptions
  subscribeToDashboardMetrics(tenantId: string): void {
    if (this.socket) {
      this.socket.emit('subscribe-dashboard-metrics', { tenantId });
    }
  }

  subscribeToLiveEvents(tenantId: string, eventTypes?: string[]): void {
    if (this.socket) {
      this.socket.emit('subscribe-live-events', { tenantId, eventTypes });
    }
  }

  subscribeToPerformanceMetrics(): void {
    if (this.socket) {
      this.socket.emit('subscribe-performance-metrics');
    }
  }

  subscribeToGeospatialEvents(tenantId: string, bounds?: { north: number; south: number; east: number; west: number }): void {
    if (this.socket) {
      this.socket.emit('subscribe-geospatial-events', { tenantId, bounds });
    }
  }

  subscribeToAlerts(tenantId: string, severity?: string[]): void {
    if (this.socket) {
      this.socket.emit('subscribe-alerts', { tenantId, severity });
    }
  }

  // Event Sourcing Methods
  subscribeToDomainEvents(tenantId: string, aggregateTypes?: string[]): void {
    if (this.socket) {
      this.socket.emit('subscribe-domain-events', { tenantId, aggregateTypes });
    }
  }

  async getEventStream(aggregateId: string): Promise<EventStream> {
    return new Promise((resolve, reject) => {
      if (this.socket) {
        this.socket.emit('get-event-stream', { aggregateId }, (response: any) => {
          if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve(response.data);
          }
        });
      } else {
        reject(new Error('Socket not connected'));
      }
    });
  }

  async sendCommand(command: Omit<Command, 'id' | 'timestamp'>): Promise<{ success: boolean; eventId?: string; error?: string }> {
    return new Promise((resolve, reject) => {
      if (this.socket) {
        const fullCommand: Command = {
          ...command,
          id: this.generateId(),
          timestamp: new Date().toISOString(),
        };

        this.socket.emit('send-command', fullCommand, (response: any) => {
          if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      } else {
        reject(new Error('Socket not connected'));
      }
    });
  }

  // CQRS Query Methods
  async executeQuery<T>(queryType: string, parameters: Record<string, any>): Promise<QueryResult<T>> {
    return new Promise((resolve, reject) => {
      if (this.socket) {
        this.socket.emit('execute-query', { queryType, parameters }, (response: any) => {
          if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      } else {
        reject(new Error('Socket not connected'));
      }
    });
  }

  // Event Buffer Management
  private addToEventBuffer(event: LiveEvent) {
    this.eventBuffer.push(event);
    if (this.eventBuffer.length > this.maxBufferSize) {
      this.eventBuffer.shift(); // Remove oldest event
    }
  }

  getEventBuffer(): LiveEvent[] {
    return [...this.eventBuffer];
  }

  clearEventBuffer(): void {
    this.eventBuffer = [];
  }

  // Alert Management
  async acknowledgeAlert(alertId: string): Promise<void> {
    if (this.socket) {
      this.socket.emit('acknowledge-alert', { alertId });
    }
  }

  async executeAlertAction(alertId: string, actionId: string, parameters?: Record<string, any>): Promise<void> {
    if (this.socket) {
      this.socket.emit('execute-alert-action', { alertId, actionId, parameters });
    }
  }

  // Circuit Breaker Pattern
  private circuitBreakerState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private circuitBreakerTimeout = 60000; // 1 minute

  private checkCircuitBreaker(): boolean {
    const now = Date.now();
    
    if (this.circuitBreakerState === 'open') {
      if (now - this.lastFailureTime > this.circuitBreakerTimeout) {
        this.circuitBreakerState = 'half-open';
        this.failureCount = 0;
        return true;
      }
      return false;
    }
    
    return true;
  }

  private recordFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= 5) {
      this.circuitBreakerState = 'open';
      this.emit('circuit-breaker-open');
    }
  }

  private recordSuccess() {
    this.failureCount = 0;
    this.circuitBreakerState = 'closed';
  }

  // Utility Methods
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    circuitBreakerState: string;
  } {
    return {
      connected: this.socket?.connected || false,
      reconnectAttempts: this.reconnectAttempts,
      circuitBreakerState: this.circuitBreakerState,
    };
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
    this.subscriptions.clear();
    this.eventBuffer = [];
  }
}

export const realTimeDashboardService = new RealTimeDashboardService();
export default realTimeDashboardService;
