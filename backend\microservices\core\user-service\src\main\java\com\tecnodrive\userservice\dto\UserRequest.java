package com.tecnodrive.userservice.dto;

import com.tecnodrive.userservice.entity.User;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

/**
 * User Request DTOs
 */
public class UserRequest {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Create {
        
        @NotBlank(message = "Email is required")
        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;

        @NotBlank(message = "First name is required")
        @Size(min = 2, max = 100, message = "First name must be between 2 and 100 characters")
        private String firstName;

        @NotBlank(message = "Last name is required")
        @Size(min = 2, max = 100, message = "Last name must be between 2 and 100 characters")
        private String lastName;

        @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
        private String phoneNumber;

        @NotNull(message = "User type is required")
        private User.UserType userType;

        private UUID companyId;

        @Size(max = 500, message = "Address must not exceed 500 characters")
        private String address;

        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;

        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;

        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;

        private Instant dateOfBirth;

        private User.Gender gender;

        @Size(max = 10, message = "Preferred language must not exceed 10 characters")
        private String preferredLanguage = "en";

        @Size(max = 50, message = "Timezone must not exceed 50 characters")
        private String timezone = "UTC";

        private Boolean emailNotifications = true;
        private Boolean smsNotifications = true;
        private Boolean pushNotifications = true;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Update {
        
        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;

        @Size(min = 2, max = 100, message = "First name must be between 2 and 100 characters")
        private String firstName;

        @Size(min = 2, max = 100, message = "Last name must be between 2 and 100 characters")
        private String lastName;

        @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
        private String phoneNumber;

        private User.UserStatus status;

        private UUID companyId;

        @Size(max = 500, message = "Address must not exceed 500 characters")
        private String address;

        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;

        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;

        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;

        private Instant dateOfBirth;

        private User.Gender gender;

        @Size(max = 10, message = "Preferred language must not exceed 10 characters")
        private String preferredLanguage;

        @Size(max = 50, message = "Timezone must not exceed 50 characters")
        private String timezone;

        private Boolean emailNotifications;
        private Boolean smsNotifications;
        private Boolean pushNotifications;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Search {
        
        private String search;
        private User.UserStatus status;
        private User.UserType userType;
        private UUID companyId;
        private String city;
        private String country;
        
        @Min(value = 0, message = "Page must be non-negative")
        private Integer page = 0;
        
        @Min(value = 1, message = "Size must be positive")
        @Max(value = 100, message = "Size must not exceed 100")
        private Integer size = 20;
        
        private String sortBy = "createdAt";
        private String sortDirection = "desc";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusUpdate {
        
        @NotNull(message = "Status is required")
        private User.UserStatus status;
        
        private String reason;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationPreferences {
        
        private Boolean emailNotifications;
        private Boolean smsNotifications;
        private Boolean pushNotifications;
    }
}
