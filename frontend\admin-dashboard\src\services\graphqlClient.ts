// إصلاح استيراد Apollo Client - استخدام mock implementation إذا لم تكن المكتبة متاحة
let ApolloClient: any, InMemoryCache: any, createHttpLink: any, from: any, split: any;
let setContext: any, onError: any, getMainDefinition: any, GraphQLWsLink: any, createClient: any, RetryLink: any;

try {
  const apolloClient = require('@apollo/client');
  ApolloClient = apolloClient.ApolloClient;
  InMemoryCache = apolloClient.InMemoryCache;
  createHttpLink = apolloClient.createHttpLink;
  from = apolloClient.from;
  split = apolloClient.split;

  const apolloContext = require('@apollo/client/link/context');
  setContext = apolloContext.setContext;

  const apolloError = require('@apollo/client/link/error');
  onError = apolloError.onError;

  const apolloUtilities = require('@apollo/client/utilities');
  getMainDefinition = apolloUtilities.getMainDefinition;

  const apolloSubscriptions = require('@apollo/client/link/subscriptions');
  GraphQLWsLink = apolloSubscriptions.GraphQLWsLink;

  const graphqlWs = require('graphql-ws');
  createClient = graphqlWs.createClient;

  const apolloRetry = require('@apollo/client/link/retry');
  RetryLink = apolloRetry.RetryLink;
} catch (error) {
  console.warn('Apollo Client not available, using mock implementation');
  // Mock implementations
  ApolloClient = class MockApolloClient {
    constructor() {}
    query() { return Promise.resolve({ data: {} }); }
    mutate() { return Promise.resolve({ data: {} }); }
    subscribe() { return { subscribe: () => ({ unsubscribe: () => {} }) }; }
  };
  InMemoryCache = class MockInMemoryCache { constructor() {} };
  createHttpLink = () => ({});
  from = (links: any[]) => links[0];
  split = () => ({});
  setContext = () => ({});
  onError = () => ({});
  getMainDefinition = () => ({ kind: 'OperationDefinition', operation: 'query' });
  GraphQLWsLink = class MockGraphQLWsLink { constructor() {} };
  createClient = () => ({});
  RetryLink = class MockRetryLink { constructor() {} };
}

// GraphQL endpoint configuration
const HTTP_ENDPOINT = process.env.REACT_APP_GRAPHQL_HTTP_URL || 'http://localhost:8080/graphql';
const WS_ENDPOINT = process.env.REACT_APP_GRAPHQL_WS_URL || 'ws://localhost:8080/graphql';

// HTTP Link for queries and mutations
const httpLink = createHttpLink({
  uri: HTTP_ENDPOINT,
});

// WebSocket Link for subscriptions
const wsLink = new GraphQLWsLink(
  createClient({
    url: WS_ENDPOINT,
    connectionParams: () => {
      const token = localStorage.getItem('authToken');
      return {
        authorization: token ? `Bearer ${token}` : '',
        tenantId: localStorage.getItem('tenantId') || '',
      };
    },
    retryAttempts: 5,
    shouldRetry: () => true,
  })
);

// Auth link to add authentication headers
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('authToken');
  const tenantId = localStorage.getItem('tenantId');
  
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'x-tenant-id': tenantId || '',
      'x-client-version': process.env.REACT_APP_VERSION || '1.0.0',
    },
  };
});

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.error(
        `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
      );
      
      // Handle specific error types
      if (extensions?.code === 'UNAUTHENTICATED') {
        localStorage.removeItem('authToken');
        localStorage.removeItem('tenantId');
        window.location.href = '/login';
      }
      
      if (extensions?.code === 'FORBIDDEN') {
        console.error('Access denied for operation:', operation.operationName);
      }
    });
  }

  if (networkError) {
    console.error(`Network error: ${networkError}`);
    
    // Handle network errors
    if (networkError.statusCode === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
  }
});

// Retry link for failed requests
const retryLink = new RetryLink({
  delay: {
    initial: 300,
    max: Infinity,
    jitter: true,
  },
  attempts: {
    max: 3,
    retryIf: (error, _operation) => !!error,
  },
});

// Split link to route queries/mutations to HTTP and subscriptions to WebSocket
const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    );
  },
  wsLink,
  from([retryLink, errorLink, authLink, httpLink])
);

// Apollo Client configuration
export const apolloClient = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache({
    typePolicies: {
      RiskEvent: {
        fields: {
          mitigationActions: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
        },
      },
      Customer: {
        fields: {
          interactions: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
        },
      },
      Vehicle: {
        fields: {
          maintenanceHistory: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
          sensorConfiguration: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
        },
      },
      Query: {
        fields: {
          riskEvents: {
            keyArgs: ['tenantId', 'filters'],
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
          customers: {
            keyArgs: ['tenantId', 'filters'],
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
          vehicles: {
            keyArgs: ['tenantId', 'filters'],
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
        },
      },
    },
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-and-network',
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: process.env.NODE_ENV === 'development',
});

// GraphQL Queries and Mutations
export const RISK_MANAGEMENT_QUERIES = {
  GET_RISK_EVENTS: `
    query GetRiskEvents($tenantId: ID!, $filters: RiskEventFilters) {
      riskEvents(tenantId: $tenantId, filters: $filters) {
        id
        tenantId
        relatedEntityId
        entityType
        riskType
        severity
        status
        title
        description
        riskScore
        mitigationActions {
          id
          action
          status
          assignedTo
          dueDate
          completedAt
          notes
        }
        detectedAt
        resolvedAt
        assignedTo
        tags
        metadata
      }
    }
  `,
  
  GET_RISK_METRICS: `
    query GetRiskMetrics($tenantId: ID!, $timeframe: String!) {
      riskMetrics(tenantId: $tenantId, timeframe: $timeframe) {
        totalRisks
        risksByType
        risksBySeverity
        riskTrend {
          date
          count
          severity
        }
        averageResolutionTime
        openRisks
        criticalRisks
      }
    }
  `,
  
  CREATE_RISK_EVENT: `
    mutation CreateRiskEvent($input: CreateRiskEventInput!) {
      createRiskEvent(input: $input) {
        id
        tenantId
        riskType
        severity
        status
        title
        description
        riskScore
        detectedAt
      }
    }
  `,
  
  SUBSCRIBE_RISK_EVENTS: `
    subscription SubscribeRiskEvents($tenantId: ID!) {
      riskEventUpdated(tenantId: $tenantId) {
        id
        tenantId
        riskType
        severity
        status
        title
        description
        riskScore
        detectedAt
      }
    }
  `,
};

export const CRM_QUERIES = {
  GET_CUSTOMERS: `
    query GetCustomers($tenantId: ID!, $filters: CustomerFilters, $pagination: PaginationInput) {
      customers(tenantId: $tenantId, filters: $filters, pagination: $pagination) {
        data {
          id
          tenantId
          firstName
          lastName
          email
          phone
          address
          segment
          status
          registrationDate
          lastActivity
          totalTrips
          totalSpent
          averageRating
          preferences {
            preferredVehicleType
            preferredPaymentMethod
            communicationChannel
            language
            timezone
            notifications {
              tripUpdates
              promotions
              newsletters
            }
          }
          tags
          notes
        }
        total
        hasMore
      }
    }
  `,
  
  GET_SUPPORT_TICKETS: `
    query GetSupportTickets($tenantId: ID!, $filters: TicketFilters) {
      supportTickets(tenantId: $tenantId, filters: $filters) {
        id
        tenantId
        customerId
        title
        description
        category
        priority
        status
        assignedTo
        createdAt
        updatedAt
        resolvedAt
        sentimentScore
        aiSuggestions
        attachments
        interactions {
          id
          type
          content
          author
          timestamp
          isInternal
        }
      }
    }
  `,
  
  CREATE_SUPPORT_TICKET: `
    mutation CreateSupportTicket($input: CreateSupportTicketInput!) {
      createSupportTicket(input: $input) {
        id
        tenantId
        customerId
        title
        description
        category
        priority
        status
        sentimentScore
        aiSuggestions
        createdAt
      }
    }
  `,
  
  ANALYZE_SENTIMENT: `
    mutation AnalyzeSentiment($text: String!) {
      analyzeSentiment(text: $text) {
        score
        label
        confidence
      }
    }
  `,
};

export const MAINTENANCE_QUERIES = {
  GET_VEHICLES: `
    query GetVehicles($tenantId: ID!, $filters: VehicleFilters) {
      vehicles(tenantId: $tenantId, filters: $filters) {
        id
        tenantId
        plateNumber
        make
        model
        year
        vin
        status
        mileage
        fuelType
        nextMaintenanceDue
        lastMaintenanceDate
        sensorConfiguration {
          id
          type
          location
          calibrationStatus
          lastReading {
            timestamp
            value
            unit
            status
          }
        }
        maintenanceHistory {
          id
          performedAt
          workPerformed
          actualCost
          mechanicNotes
          qualityRating
        }
        healthScore
      }
    }
  `,
  
  GET_MAINTENANCE_SCHEDULES: `
    query GetMaintenanceSchedules($tenantId: ID!, $filters: MaintenanceFilters) {
      maintenanceSchedules(tenantId: $tenantId, filters: $filters) {
        id
        tenantId
        vehicleId
        type
        title
        description
        dueDate
        estimatedDuration
        priority
        status
        assignedMechanic
        requiredParts {
          partId
          partName
          quantity
          estimatedCost
          availability
          supplier
        }
        requiredCertifications
        estimatedCost
        createdAt
        scheduledBy
      }
    }
  `,
  
  GET_PREDICTIVE_ALERTS: `
    query GetPredictiveAlerts($tenantId: ID!, $filters: PredictiveAlertFilters) {
      predictiveAlerts(tenantId: $tenantId, filters: $filters) {
        id
        vehicleId
        component
        alertType
        severity
        predictedFailureDate
        confidence
        recommendations
        sensorData {
          timestamp
          value
          unit
          status
        }
        createdAt
      }
    }
  `,
  
  CREATE_MAINTENANCE_SCHEDULE: `
    mutation CreateMaintenanceSchedule($input: CreateMaintenanceScheduleInput!) {
      createMaintenanceSchedule(input: $input) {
        id
        tenantId
        vehicleId
        type
        title
        description
        dueDate
        priority
        status
        estimatedCost
        createdAt
      }
    }
  `,
  
  COMPLETE_MAINTENANCE: `
    mutation CompleteMaintenance($scheduleId: ID!, $input: CompleteMaintenanceInput!) {
      completeMaintenance(scheduleId: $scheduleId, input: $input) {
        id
        scheduleId
        tenantId
        vehicleId
        performedAt
        performedBy
        actualDuration
        actualCost
        workPerformed
        partsUsed {
          partId
          partName
          quantity
          actualCost
          condition
        }
        beforePhotos
        afterPhotos
        mechanicNotes
        qualityRating
        nextMaintenanceRecommendation
        issuesFound
        status
      }
    }
  `,
  
  SUBSCRIBE_SENSOR_DATA: `
    subscription SubscribeSensorData($vehicleId: ID!) {
      sensorDataUpdated(vehicleId: $vehicleId) {
        timestamp
        value
        unit
        status
      }
    }
  `,
};

// Helper functions for GraphQL operations
export const executeQuery = async (query: string, variables?: any) => {
  try {
    const result = await apolloClient.query({
      query: query,
      variables,
      fetchPolicy: 'cache-first',
    });
    return result.data;
  } catch (error) {
    console.error('GraphQL Query Error:', error);
    throw error;
  }
};

export const executeMutation = async (mutation: string, variables?: any) => {
  try {
    const result = await apolloClient.mutate({
      mutation: mutation,
      variables,
    });
    return result.data;
  } catch (error) {
    console.error('GraphQL Mutation Error:', error);
    throw error;
  }
};

export const subscribeToUpdates = (subscription: string, variables?: any, callback?: (data: any) => void) => {
  const observable = apolloClient.subscribe({
    query: subscription,
    variables,
  });

  return observable.subscribe({
    next: (result) => {
      if (callback) {
        callback(result.data);
      }
    },
    error: (error) => {
      console.error('GraphQL Subscription Error:', error);
    },
  });
};

export default apolloClient;
