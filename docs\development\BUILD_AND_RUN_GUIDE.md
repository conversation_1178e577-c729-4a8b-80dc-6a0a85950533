# TecnoDrive Platform - دليل البناء والتشغيل الشامل

## نظرة عامة / Overview

هذا الدليل يوضح كيفية بناء وتشغيل جميع خدمات منصة TecnoDrive باستخدام الـ script الشامل الجديد.

This guide explains how to build and run all TecnoDrive platform services using the new comprehensive script.

## المتطلبات / Prerequisites

### البرامج المطلوبة / Required Software
- **Docker Desktop** (latest version)
- **Docker Compose** (included with Docker Desktop)
- **PowerShell** 5.1 or later
- **Java 17** (for building services)
- **Maven 3.8+** (for building Java services)

### فحص المتطلبات / Check Prerequisites
```powershell
# Check Docker
docker --version
docker-compose --version

# Check Java
java -version

# Check Maven
mvn -version
```

## الملفات الجديدة / New Files

### 1. `build-and-run-all.ps1`
الـ script الرئيسي الشامل لبناء وتشغيل جميع الخدمات
Main comprehensive script for building and running all services

### 2. `docker-compose.unified.yml`
ملف Docker Compose موحد يحتوي على جميع الخدمات
Unified Docker Compose file containing all services

## كيفية الاستخدام / How to Use

### 1. التشغيل الكامل (البناء + التشغيل) / Full Run (Build + Start)
```powershell
# البناء والتشغيل الكامل
.\build-and-run-all.ps1

# أو بشكل صريح
.\build-and-run-all.ps1 -Action full
```

### 2. البناء فقط / Build Only
```powershell
.\build-and-run-all.ps1 -Action build
```

### 3. التشغيل فقط / Start Only
```powershell
.\build-and-run-all.ps1 -Action start
```

### 4. إيقاف الخدمات / Stop Services
```powershell
.\build-and-run-all.ps1 -Action stop
```

### 5. إعادة التشغيل / Restart
```powershell
.\build-and-run-all.ps1 -Action restart
```

### 6. فحص حالة الخدمات / Check Status
```powershell
.\build-and-run-all.ps1 -Action status
```

### 7. عرض السجلات / Show Logs
```powershell
# جميع السجلات
.\build-and-run-all.ps1 -Action logs

# سجلات خدمة معينة
.\build-and-run-all.ps1 -Action logs -Service auth-service
```

### 8. تنظيف البيئة / Clean Environment
```powershell
# تنظيف عادي
.\build-and-run-all.ps1 -Action clean

# تنظيف شامل (حذف الصور أيضاً)
.\build-and-run-all.ps1 -Action clean -Force
```

## ترتيب تشغيل الخدمات / Service Startup Order

الـ script يتبع الترتيب التالي لضمان التشغيل الصحيح:

1. **البنية التحتية / Infrastructure**
   - PostgreSQL (Port 5432)
   - Redis (Port 6379)

2. **اكتشاف الخدمات / Service Discovery**
   - Eureka Server (Port 8761)

3. **الخدمات الأساسية / Core Services**
   - API Gateway (Port 8080)
   - Auth Service (Port 8081)

4. **خدمات الأعمال / Business Services**
   - User Service (Port 8083)
   - Ride Service (Port 8082)
   - Fleet Service (Port 8084)
   - Location Service (Port 8085)

5. **الخدمات الإضافية / Additional Services**
   - Payment Service (Port 8086)
   - Parcel Service (Port 8087)
   - Notification Service (Port 8088)
   - Analytics Service (Port 8089)
   - HR Service (Port 8090)
   - Financial Service (Port 8091)
   - SaaS Management Service (Port 8092)

## الروابط المهمة / Important URLs

بعد التشغيل الناجح، ستكون الخدمات متاحة على:

- **Dashboard**: http://localhost:8080
- **Eureka Server**: http://localhost:8761
- **Auth Service**: http://localhost:8081
- **User Service**: http://localhost:8083
- **API Gateway**: http://localhost:8080

## فحص الصحة / Health Checks

جميع الخدمات تحتوي على فحص صحة على:
```
http://localhost:[PORT]/actuator/health
```

## استكشاف الأخطاء / Troubleshooting

### 1. فشل في بناء الخدمات / Build Failures
```powershell
# فحص سجلات البناء
.\build-and-run-all.ps1 -Action logs -Service [service-name] -Verbose
```

### 2. فشل في الاتصال بقاعدة البيانات / Database Connection Issues
```powershell
# فحص حالة PostgreSQL
docker exec postgres-tecno pg_isready -U postgres

# فحص سجلات قاعدة البيانات
docker logs postgres-tecno
```

### 3. فشل في الاتصال بـ Redis / Redis Connection Issues
```powershell
# فحص حالة Redis
docker exec redis-tecno redis-cli -a "TecnoDrive2025!Redis#Cache" ping

# فحص سجلات Redis
docker logs redis-tecno
```

### 4. مشاكل الشبكة / Network Issues
```powershell
# فحص الشبكة
docker network ls | findstr tecnodrive

# إعادة إنشاء الشبكة
docker network rm tecnodrive-network
.\build-and-run-all.ps1 -Action start
```

### 5. مشاكل المنافذ / Port Issues
```powershell
# فحص المنافذ المستخدمة
netstat -an | findstr :8080
netstat -an | findstr :8761
```

## الخيارات المتقدمة / Advanced Options

### تشغيل مع تفاصيل إضافية / Verbose Mode
```powershell
.\build-and-run-all.ps1 -Action full -Verbose
```

### تشغيل خدمة واحدة / Single Service
```powershell
# تشغيل خدمة واحدة فقط
docker-compose -f docker-compose.unified.yml up -d auth-service
```

### مراقبة السجلات المباشرة / Live Log Monitoring
```powershell
# مراقبة السجلات المباشرة
docker-compose -f docker-compose.unified.yml logs -f
```

## النسخ الاحتياطي والاستعادة / Backup and Restore

### نسخ احتياطي لقاعدة البيانات / Database Backup
```powershell
docker exec postgres-tecno pg_dumpall -U postgres > backup.sql
```

### استعادة قاعدة البيانات / Database Restore
```powershell
docker exec -i postgres-tecno psql -U postgres < backup.sql
```

## الأمان / Security

### كلمات المرور الافتراضية / Default Passwords
- **PostgreSQL**: `postgres`
- **Redis**: `TecnoDrive2025!Redis#Cache`
- **JWT Secret**: `TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2025!`

⚠️ **تحذير**: يجب تغيير كلمات المرور في بيئة الإنتاج

## الدعم / Support

للحصول على المساعدة:
1. فحص السجلات باستخدام `.\build-and-run-all.ps1 -Action logs`
2. فحص حالة الخدمات باستخدام `.\build-and-run-all.ps1 -Action status`
3. مراجعة هذا الدليل للحلول الشائعة

---

## ملاحظات إضافية / Additional Notes

- يتم حفظ البيانات في Docker volumes للاستمرارية
- جميع الخدمات تستخدم نفس الشبكة للتواصل
- الـ script يدعم إعادة التشغيل التلقائي للحاويات
- يمكن تشغيل الخدمات بشكل منفصل حسب الحاجة
