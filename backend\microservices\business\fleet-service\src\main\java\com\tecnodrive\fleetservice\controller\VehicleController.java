package com.tecnodrive.fleetservice.controller;

import com.tecnodrive.common.dto.common.ApiResponse;
import com.tecnodrive.fleetservice.dto.VehicleRequest;
import com.tecnodrive.fleetservice.dto.VehicleResponse;
import com.tecnodrive.fleetservice.entity.Vehicle;
import com.tecnodrive.fleetservice.service.VehicleService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Vehicle Controller
 * 
 * REST API endpoints for vehicle fleet management
 */
@RestController
@RequestMapping("/api/fleet/vehicles")
@RequiredArgsConstructor
@Slf4j
public class VehicleController {

    private final VehicleService vehicleService;

    /**
     * Create a new vehicle
     */
    @PostMapping
    public ResponseEntity<ApiResponse<VehicleResponse>> createVehicle(@Valid @RequestBody VehicleRequest request) {
        log.info("Creating vehicle with plate number: {}", request.getPlateNumber());
        
        try {
            VehicleResponse response = vehicleService.createVehicle(request);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error creating vehicle: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create vehicle: " + e.getMessage()));
        }
    }

    /**
     * Get vehicle by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<VehicleResponse>> getVehicle(@PathVariable String id) {
        log.debug("Getting vehicle by ID: {}", id);
        
        try {
            VehicleResponse response = vehicleService.getVehicle(id);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicle: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicle: " + e.getMessage()));
        }
    }

    /**
     * Get vehicle by plate number
     */
    @GetMapping("/plate/{plateNumber}")
    public ResponseEntity<ApiResponse<VehicleResponse>> getVehicleByPlateNumber(@PathVariable String plateNumber) {
        log.debug("Getting vehicle by plate number: {}", plateNumber);
        
        try {
            VehicleResponse response = vehicleService.getVehicleByPlateNumber(plateNumber);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicle by plate number: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicle: " + e.getMessage()));
        }
    }

    /**
     * Get all vehicles with pagination
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<VehicleResponse>>> getVehicles(Pageable pageable) {
        log.debug("Getting vehicles with pagination: {}", pageable);
        
        try {
            Page<VehicleResponse> response = vehicleService.getVehicles(pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles: " + e.getMessage()));
        }
    }

    /**
     * Get vehicles by company
     */
    @GetMapping("/company/{companyId}")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getVehiclesByCompany(@PathVariable String companyId) {
        log.debug("Getting vehicles by company: {}", companyId);
        
        try {
            List<VehicleResponse> response = vehicleService.getVehiclesByCompany(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles: " + e.getMessage()));
        }
    }

    /**
     * Get vehicles by company with pagination
     */
    @GetMapping("/company/{companyId}/paged")
    public ResponseEntity<ApiResponse<Page<VehicleResponse>>> getVehiclesByCompany(
            @PathVariable String companyId, Pageable pageable) {
        log.debug("Getting vehicles by company: {} with pagination: {}", companyId, pageable);
        
        try {
            Page<VehicleResponse> response = vehicleService.getVehiclesByCompany(companyId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles: " + e.getMessage()));
        }
    }

    /**
     * Update vehicle
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<VehicleResponse>> updateVehicle(
            @PathVariable String id, @Valid @RequestBody VehicleRequest request) {
        log.info("Updating vehicle with ID: {}", id);
        
        try {
            VehicleResponse response = vehicleService.updateVehicle(id, request);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating vehicle: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update vehicle: " + e.getMessage()));
        }
    }

    /**
     * Delete vehicle
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteVehicle(@PathVariable String id) {
        log.info("Deleting vehicle with ID: {}", id);
        
        try {
            vehicleService.deleteVehicle(id);
            return ResponseEntity.ok(ApiResponse.success(null));
            
        } catch (Exception e) {
            log.error("Error deleting vehicle: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete vehicle: " + e.getMessage()));
        }
    }

    /**
     * Get available vehicles
     */
    @GetMapping("/available")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getAvailableVehicles() {
        log.debug("Getting available vehicles");
        
        try {
            List<VehicleResponse> response = vehicleService.getAvailableVehicles();
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting available vehicles: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get available vehicles: " + e.getMessage()));
        }
    }

    /**
     * Get available vehicles by company
     */
    @GetMapping("/company/{companyId}/available")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getAvailableVehiclesByCompany(@PathVariable String companyId) {
        log.debug("Getting available vehicles by company: {}", companyId);
        
        try {
            List<VehicleResponse> response = vehicleService.getAvailableVehiclesByCompany(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting available vehicles by company: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get available vehicles: " + e.getMessage()));
        }
    }

    /**
     * Assign driver to vehicle
     */
    @PostMapping("/{vehicleId}/assign-driver/{driverId}")
    public ResponseEntity<ApiResponse<VehicleResponse>> assignDriver(
            @PathVariable String vehicleId, @PathVariable String driverId) {
        log.info("Assigning driver {} to vehicle {}", driverId, vehicleId);
        
        try {
            VehicleResponse response = vehicleService.assignDriver(vehicleId, driverId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error assigning driver: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to assign driver: " + e.getMessage()));
        }
    }

    /**
     * Unassign driver from vehicle
     */
    @PostMapping("/{vehicleId}/unassign-driver")
    public ResponseEntity<ApiResponse<VehicleResponse>> unassignDriver(@PathVariable String vehicleId) {
        log.info("Unassigning driver from vehicle {}", vehicleId);
        
        try {
            VehicleResponse response = vehicleService.unassignDriver(vehicleId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error unassigning driver: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to unassign driver: " + e.getMessage()));
        }
    }

    /**
     * Update vehicle status
     */
    @PatchMapping("/{vehicleId}/status")
    public ResponseEntity<ApiResponse<VehicleResponse>> updateVehicleStatus(
            @PathVariable String vehicleId, @RequestParam Vehicle.VehicleStatus status) {
        log.info("Updating vehicle {} status to {}", vehicleId, status);
        
        try {
            VehicleResponse response = vehicleService.updateVehicleStatus(vehicleId, status);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating vehicle status: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update vehicle status: " + e.getMessage()));
        }
    }

    /**
     * Update odometer reading
     */
    @PatchMapping("/{vehicleId}/odometer")
    public ResponseEntity<ApiResponse<VehicleResponse>> updateOdometerReading(
            @PathVariable String vehicleId, @RequestParam BigDecimal odometerReading) {
        log.info("Updating odometer reading for vehicle {} to {}", vehicleId, odometerReading);
        
        try {
            VehicleResponse response = vehicleService.updateOdometerReading(vehicleId, odometerReading);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error updating odometer reading: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update odometer reading: " + e.getMessage()));
        }
    }

    /**
     * Schedule maintenance
     */
    @PostMapping("/{vehicleId}/schedule-maintenance")
    public ResponseEntity<ApiResponse<VehicleResponse>> scheduleMaintenance(
            @PathVariable String vehicleId, @RequestParam LocalDate maintenanceDate) {
        log.info("Scheduling maintenance for vehicle {} on {}", vehicleId, maintenanceDate);
        
        try {
            VehicleResponse response = vehicleService.scheduleMaintenance(vehicleId, maintenanceDate);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error scheduling maintenance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to schedule maintenance: " + e.getMessage()));
        }
    }

    /**
     * Complete maintenance
     */
    @PostMapping("/{vehicleId}/complete-maintenance")
    public ResponseEntity<ApiResponse<VehicleResponse>> completeMaintenance(
            @PathVariable String vehicleId,
            @RequestParam BigDecimal odometerReading,
            @RequestParam LocalDate nextMaintenanceDate) {
        log.info("Completing maintenance for vehicle {}", vehicleId);
        
        try {
            VehicleResponse response = vehicleService.completeMaintenance(vehicleId, odometerReading, nextMaintenanceDate);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error completing maintenance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to complete maintenance: " + e.getMessage()));
        }
    }

    /**
     * Get vehicles needing maintenance
     */
    @GetMapping("/maintenance/needed")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getVehiclesNeedingMaintenance() {
        log.debug("Getting vehicles needing maintenance");
        
        try {
            List<VehicleResponse> response = vehicleService.getVehiclesNeedingMaintenance();
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles needing maintenance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles needing maintenance: " + e.getMessage()));
        }
    }

    /**
     * Get vehicles with expiring insurance
     */
    @GetMapping("/insurance/expiring")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getVehiclesWithExpiringInsurance(
            @RequestParam(defaultValue = "30") int warningDays) {
        log.debug("Getting vehicles with expiring insurance within {} days", warningDays);
        
        try {
            List<VehicleResponse> response = vehicleService.getVehiclesWithExpiringInsurance(warningDays);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles with expiring insurance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles with expiring insurance: " + e.getMessage()));
        }
    }

    /**
     * Search vehicles
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<VehicleResponse>>> searchVehicles(
            @RequestParam String searchTerm, Pageable pageable) {
        log.debug("Searching vehicles with term: {}", searchTerm);
        
        try {
            Page<VehicleResponse> response = vehicleService.searchVehicles(searchTerm, pageable);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error searching vehicles: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to search vehicles: " + e.getMessage()));
        }
    }

    /**
     * Get vehicle statistics
     */
    @GetMapping("/company/{companyId}/statistics")
    public ResponseEntity<ApiResponse<VehicleResponse.VehicleStatistics>> getVehicleStatistics(@PathVariable String companyId) {
        log.debug("Getting vehicle statistics for company: {}", companyId);
        
        try {
            VehicleResponse.VehicleStatistics response = vehicleService.getVehicleStatistics(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicle statistics: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicle statistics: " + e.getMessage()));
        }
    }

    /**
     * Get vehicle summaries
     */
    @GetMapping("/company/{companyId}/summaries")
    public ResponseEntity<ApiResponse<List<VehicleResponse.VehicleSummary>>> getVehicleSummaries(@PathVariable String companyId) {
        log.debug("Getting vehicle summaries for company: {}", companyId);
        
        try {
            List<VehicleResponse.VehicleSummary> response = vehicleService.getVehicleSummaries(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicle summaries: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicle summaries: " + e.getMessage()));
        }
    }

    /**
     * Get unassigned vehicles
     */
    @GetMapping("/company/{companyId}/unassigned")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getUnassignedVehicles(@PathVariable String companyId) {
        log.debug("Getting unassigned vehicles for company: {}", companyId);
        
        try {
            List<VehicleResponse> response = vehicleService.getUnassignedVehicles(companyId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting unassigned vehicles: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get unassigned vehicles: " + e.getMessage()));
        }
    }

    /**
     * Get vehicles by driver
     */
    @GetMapping("/driver/{driverId}")
    public ResponseEntity<ApiResponse<List<VehicleResponse>>> getVehiclesByDriver(@PathVariable String driverId) {
        log.debug("Getting vehicles by driver: {}", driverId);
        
        try {
            List<VehicleResponse> response = vehicleService.getVehiclesByDriver(driverId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("Error getting vehicles by driver: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get vehicles by driver: " + e.getMessage()));
        }
    }

    /**
     * Check if plate number is available
     */
    @GetMapping("/check-plate/{plateNumber}")
    public ResponseEntity<ApiResponse<Boolean>> checkPlateNumberAvailability(@PathVariable String plateNumber) {
        log.debug("Checking plate number availability: {}", plateNumber);
        
        try {
            boolean available = vehicleService.isPlateNumberAvailable(plateNumber);
            return ResponseEntity.ok(ApiResponse.success(available));
            
        } catch (Exception e) {
            log.error("Error checking plate number availability: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to check plate number availability: " + e.getMessage()));
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        return ResponseEntity.ok(ApiResponse.success("Fleet service is running"));
    }
}
