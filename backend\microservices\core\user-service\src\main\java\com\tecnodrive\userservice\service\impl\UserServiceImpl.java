package com.tecnodrive.userservice.service.impl;

import com.tecnodrive.userservice.dto.UserRequest;
import com.tecnodrive.userservice.dto.UserResponse;
import com.tecnodrive.userservice.entity.User;
import com.tecnodrive.userservice.mapper.UserMapper;
import com.tecnodrive.userservice.repository.UserRepository;
import com.tecnodrive.userservice.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

/**
 * User Service Implementation
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;

    @Override
    @CacheEvict(value = "users", allEntries = true)
    public UserResponse createUser(UserRequest.Create request) {
        log.info("Creating user with email: {}", request.getEmail());

        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + request.getEmail());
        }

        // Check if phone already exists
        if (request.getPhoneNumber() != null && userRepository.existsByPhoneNumber(request.getPhoneNumber())) {
            throw new IllegalArgumentException("Phone number already exists: " + request.getPhoneNumber());
        }

        User user = userMapper.toEntity(request);
        user = userRepository.save(user);

        log.info("User created successfully with ID: {}", user.getId());
        return userMapper.toResponse(user);
    }

    @Override
    @Cacheable(value = "users", key = "#userId")
    @Transactional(readOnly = true)
    public UserResponse getUserById(UUID userId) {
        log.debug("Getting user by ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        return userMapper.toResponse(user);
    }

    @Override
    @Cacheable(value = "users", key = "#email")
    @Transactional(readOnly = true)
    public UserResponse getUserByEmail(String email) {
        log.debug("Getting user by email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        return userMapper.toResponse(user);
    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getUserByPhoneNumber(String phoneNumber) {
        log.debug("Getting user by phone: {}", phoneNumber);

        User user = userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("User not found with phone: " + phoneNumber));

        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse updateUser(UUID userId, UserRequest.Update request) {
        log.info("Updating user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        // Check email uniqueness if email is being updated
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new IllegalArgumentException("Email already exists: " + request.getEmail());
            }
            user.setEmailVerified(false); // Reset email verification
        }

        // Check phone uniqueness if phone is being updated
        if (request.getPhoneNumber() != null && !request.getPhoneNumber().equals(user.getPhoneNumber())) {
            if (userRepository.existsByPhoneNumber(request.getPhoneNumber())) {
                throw new IllegalArgumentException("Phone number already exists: " + request.getPhoneNumber());
            }
            user.setPhoneVerified(false); // Reset phone verification
        }

        userMapper.updateEntityFromRequest(request, user);
        user = userRepository.save(user);

        log.info("User updated successfully: {}", userId);
        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse updateUserStatus(UUID userId, UserRequest.StatusUpdate request) {
        log.info("Updating user status: {} to {}", userId, request.getStatus());

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        user.setStatus(request.getStatus());
        user = userRepository.save(user);

        log.info("User status updated successfully: {} to {}", userId, request.getStatus());
        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse updateNotificationPreferences(UUID userId, UserRequest.NotificationPreferences request) {
        log.info("Updating notification preferences for user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        if (request.getEmailNotifications() != null) {
            user.setEmailNotifications(request.getEmailNotifications());
        }
        if (request.getSmsNotifications() != null) {
            user.setSmsNotifications(request.getSmsNotifications());
        }
        if (request.getPushNotifications() != null) {
            user.setPushNotifications(request.getPushNotifications());
        }

        user = userRepository.save(user);

        log.info("Notification preferences updated successfully for user: {}", userId);
        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public void deleteUser(UUID userId) {
        log.info("Deleting user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        // Soft delete by setting status to INACTIVE
        user.setStatus(User.UserStatus.INACTIVE);
        userRepository.save(user);

        log.info("User deleted successfully: {}", userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserResponse.Summary> searchUsers(UserRequest.Search request) {
        log.debug("Searching users with filters: {}", request);

        Sort sort = Sort.by(
            "desc".equalsIgnoreCase(request.getSortDirection()) ? Sort.Direction.DESC : Sort.Direction.ASC,
            request.getSortBy()
        );

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize(), sort);

        Page<User> users = userRepository.findUsersWithFilters(
            request.getStatus(),
            request.getUserType(),
            request.getCompanyId(),
            request.getCity(),
            request.getCountry(),
            request.getSearch(),
            pageable
        );

        return users.map(userMapper::toSummaryResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersByStatus(User.UserStatus status) {
        log.debug("Getting users by status: {}", status);

        List<User> users = userRepository.findByStatus(status);
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersByType(User.UserType userType) {
        log.debug("Getting users by type: {}", userType);

        List<User> users = userRepository.findByUserType(userType);
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersByCompany(UUID companyId) {
        log.debug("Getting users by company: {}", companyId);

        List<User> users = userRepository.findByCompanyId(companyId);
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @Cacheable(value = "publicProfiles", key = "#userId")
    @Transactional(readOnly = true)
    public UserResponse.PublicProfile getPublicProfile(UUID userId) {
        log.debug("Getting public profile for user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        return userMapper.toPublicProfileResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse verifyEmail(UUID userId) {
        log.info("Verifying email for user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        user.setEmailVerified(true);
        user = userRepository.save(user);

        log.info("Email verified successfully for user: {}", userId);
        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse verifyPhone(UUID userId) {
        log.info("Verifying phone for user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        user.setPhoneVerified(true);
        user = userRepository.save(user);

        log.info("Phone verified successfully for user: {}", userId);
        return userMapper.toResponse(user);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public void updateLastLogin(UUID userId) {
        log.debug("Updating last login for user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        user.setLastLoginAt(Instant.now());
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean phoneExists(String phoneNumber) {
        return userRepository.existsByPhoneNumber(phoneNumber);
    }

    @Override
    @Cacheable(value = "userStatistics")
    @Transactional(readOnly = true)
    public UserResponse.Statistics getUserStatistics() {
        log.debug("Getting user statistics");

        UserResponse.Statistics stats = new UserResponse.Statistics();

        stats.setTotalUsers(userRepository.count());
        stats.setActiveUsers(userRepository.countByStatus(User.UserStatus.ACTIVE));
        stats.setInactiveUsers(userRepository.countByStatus(User.UserStatus.INACTIVE));
        stats.setSuspendedUsers(userRepository.countByStatus(User.UserStatus.SUSPENDED));
        stats.setPendingVerificationUsers(userRepository.countByStatus(User.UserStatus.PENDING_VERIFICATION));
        stats.setBannedUsers(userRepository.countByStatus(User.UserStatus.BANNED));

        stats.setPassengersCount(userRepository.countByUserType(User.UserType.PASSENGER));
        stats.setDriversCount(userRepository.countByUserType(User.UserType.DRIVER));
        stats.setAdminsCount(userRepository.countByUserType(User.UserType.ADMIN));
        stats.setFleetManagersCount(userRepository.countByUserType(User.UserType.FLEET_MANAGER));
        stats.setSupportAgentsCount(userRepository.countByUserType(User.UserType.SUPPORT_AGENT));

        List<User> verifiedUsers = userRepository.findByEmailVerifiedTrueAndPhoneVerifiedTrue();
        List<User> unverifiedUsers = userRepository.findByEmailVerifiedFalseOrPhoneVerifiedFalse();

        stats.setVerifiedUsers(verifiedUsers.size());
        stats.setUnverifiedUsers(unverifiedUsers.size());

        stats.setUsersWithEmailNotifications(userRepository.findByEmailNotificationsTrue().size());
        stats.setUsersWithSmsNotifications(userRepository.findBySmsNotificationsTrue().size());

        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Activity> getInactiveUsers(int daysSinceLastLogin) {
        log.debug("Getting inactive users since {} days", daysSinceLastLogin);

        Instant cutoffDate = Instant.now().minus(daysSinceLastLogin, ChronoUnit.DAYS);
        List<User> inactiveUsers = userRepository.findByLastLoginAtBeforeOrLastLoginAtIsNull(cutoffDate);

        return inactiveUsers.stream()
                .map(userMapper::toActivityResponse)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersForEmailNotification() {
        log.debug("Getting users for email notification");

        List<User> users = userRepository.findByEmailNotificationsTrue();
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersForSmsNotification() {
        log.debug("Getting users for SMS notification");

        List<User> users = userRepository.findBySmsNotificationsTrue();
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersForPushNotification() {
        log.debug("Getting users for push notification");

        // For now, return all active users since we don't have a specific field for push notifications
        List<User> users = userRepository.findByStatus(User.UserStatus.ACTIVE);
        return userMapper.toSummaryResponseList(users);
    }

    @Override
    @CacheEvict(value = "users", allEntries = true)
    public void bulkUpdateUserStatus(List<UUID> userIds, User.UserStatus status, String reason) {
        log.info("Bulk updating status for {} users to {}", userIds.size(), status);

        List<User> users = userRepository.findAllById(userIds);
        users.forEach(user -> user.setStatus(status));
        userRepository.saveAll(users);

        log.info("Bulk status update completed for {} users", users.size());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse> exportUsers(UserRequest.Search filters) {
        log.info("Exporting users with filters: {}", filters);

        // Use a large page size for export
        filters.setSize(10000);

        Page<User> users = userRepository.findUsersWithFilters(
            filters.getStatus(),
            filters.getUserType(),
            filters.getCompanyId(),
            filters.getCity(),
            filters.getCountry(),
            filters.getSearch(),
            PageRequest.of(0, filters.getSize())
        );

        return userMapper.toResponseList(users.getContent());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse.Summary> getUsersCreatedBetween(Instant startDate, Instant endDate) {
        log.debug("Getting users created between {} and {}", startDate, endDate);

        List<User> users = userRepository.findByCreatedAtBetween(startDate, endDate);
        return userMapper.toSummaryResponseList(users);
    }
}
