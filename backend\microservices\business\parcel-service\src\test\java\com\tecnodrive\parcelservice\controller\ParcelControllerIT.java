package com.tecnodrive.parcelservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for ParcelController
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.flyway.enabled=false"
})
@Transactional
class ParcelControllerIT {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void createParcel_ShouldReturnParcelResponse_WhenValidRequest() throws Exception {
        // Given
        Map<String, Object> parcelRequest = Map.of(
            "senderId", UUID.randomUUID().toString(),
            "receiverId", UUID.randomUUID().toString(),
            "weight", 2.5,
            "dimensions", "30x20x10"
        );

        // When & Then
        mockMvc.perform(post("/api/v1/parcels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(parcelRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("CREATED"))
                .andExpect(jsonPath("$.trackingNumber").exists())
                .andExpect(jsonPath("$.id").exists());
    }

    @Test
    void assignDelivery_ShouldAssignDelivery_WhenValidRequest() throws Exception {
        // Given
        Map<String, String> deliveryRequest = Map.of(
            "parcelId", UUID.randomUUID().toString(),
            "driverId", UUID.randomUUID().toString()
        );

        // When & Then
        mockMvc.perform(post("/api/v1/parcels/deliveries")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deliveryRequest)))
                .andExpect(status().isOk());
    }

    @Test
    void health_ShouldReturnUp() throws Exception {
        mockMvc.perform(get("/api/v1/parcels/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Parcel Service is UP"));
    }
}
