import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Security as SecurityIcon,
  Key as KeyIcon,
  VpnKey as TokenIcon,
  AccountCircle as UserIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';

interface AuthProvider {
  id: string;
  name: string;
  type: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC' | 'LDAP' | 'SAML';
  enabled: boolean;
  config: Record<string, any>;
  lastUsed: string;
  usageCount: number;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  userId: string;
  userName: string;
  permissions: string[];
  expiresAt?: string;
  lastUsed?: string;
  enabled: boolean;
  createdAt: string;
}

interface JwtToken {
  id: string;
  userId: string;
  userName: string;
  tokenType: 'ACCESS' | 'REFRESH';
  expiresAt: string;
  issuedAt: string;
  lastUsed?: string;
  deviceInfo?: string;
  ipAddress?: string;
}

const MultiAuthSystem: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [authProviders, setAuthProviders] = useState<AuthProvider[]>([]);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [jwtTokens, setJwtTokens] = useState<JwtToken[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [openProviderDialog, setOpenProviderDialog] = useState(false);
  const [openApiKeyDialog, setOpenApiKeyDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});

  // Mock data
  const mockAuthProviders: AuthProvider[] = [
    {
      id: 'jwt-provider',
      name: 'JWT Authentication',
      type: 'JWT',
      enabled: true,
      config: {
        issuer: 'https://auth.tecno-drive.com',
        audience: 'tecno-drive-api',
        algorithm: 'RS256',
        tokenExpiry: 3600,
        refreshExpiry: 604800,
      },
      lastUsed: '2025-07-09T14:30:00Z',
      usageCount: 15420,
    },
    {
      id: 'oauth2-provider',
      name: 'OAuth2 Provider',
      type: 'OAUTH2',
      enabled: true,
      config: {
        clientId: 'tecno-drive-client',
        authorizationUrl: 'https://auth.tecno-drive.com/oauth/authorize',
        tokenUrl: 'https://auth.tecno-drive.com/oauth/token',
        scopes: ['read', 'write', 'admin'],
      },
      lastUsed: '2025-07-09T13:45:00Z',
      usageCount: 8750,
    },
    {
      id: 'apikey-provider',
      name: 'API Key Authentication',
      type: 'API_KEY',
      enabled: true,
      config: {
        headerName: 'X-API-Key',
        keyLength: 32,
        allowMultipleKeys: true,
      },
      lastUsed: '2025-07-09T14:25:00Z',
      usageCount: 5230,
    },
  ];

  const mockApiKeys: ApiKey[] = [
    {
      id: 'key-1',
      name: 'Mobile App Key',
      key: 'td_ak_1234567890abcdef1234567890abcdef',
      userId: 'user-123',
      userName: 'أحمد محمد',
      permissions: ['fleet:read', 'rides:read', 'rides:create'],
      expiresAt: '2025-12-31T23:59:59Z',
      lastUsed: '2025-07-09T14:30:00Z',
      enabled: true,
      createdAt: '2025-01-15T08:00:00Z',
    },
    {
      id: 'key-2',
      name: 'Dashboard Integration',
      key: 'td_ak_abcdef1234567890abcdef1234567890',
      userId: 'user-456',
      userName: 'فاطمة علي',
      permissions: ['analytics:read', 'reports:read'],
      expiresAt: '2025-10-15T23:59:59Z',
      lastUsed: '2025-07-09T12:15:00Z',
      enabled: true,
      createdAt: '2025-02-01T10:00:00Z',
    },
  ];

  const mockJwtTokens: JwtToken[] = [
    {
      id: 'token-1',
      userId: 'user-123',
      userName: 'أحمد محمد',
      tokenType: 'ACCESS',
      expiresAt: '2025-07-09T15:30:00Z',
      issuedAt: '2025-07-09T14:30:00Z',
      lastUsed: '2025-07-09T14:30:00Z',
      deviceInfo: 'Chrome 91.0 on Windows 10',
      ipAddress: '*************',
    },
    {
      id: 'token-2',
      userId: 'user-456',
      userName: 'فاطمة علي',
      tokenType: 'REFRESH',
      expiresAt: '2025-07-16T14:30:00Z',
      issuedAt: '2025-07-09T14:30:00Z',
      lastUsed: '2025-07-09T14:25:00Z',
      deviceInfo: 'Safari 14.1 on macOS',
      ipAddress: '*************',
    },
  ];

  useEffect(() => {
    setAuthProviders(mockAuthProviders);
    setApiKeys(mockApiKeys);
    setJwtTokens(mockJwtTokens);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getTypeChip = (type: string) => {
    const typeConfig = {
      JWT: { label: 'JWT', color: 'primary' as const, icon: <TokenIcon fontSize="small" /> },
      API_KEY: { label: 'API Key', color: 'secondary' as const, icon: <KeyIcon fontSize="small" /> },
      OAUTH2: { label: 'OAuth2', color: 'info' as const, icon: <ShieldIcon fontSize="small" /> },
      BASIC: { label: 'Basic', color: 'warning' as const, icon: <LockIcon fontSize="small" /> },
      LDAP: { label: 'LDAP', color: 'success' as const, icon: <UserIcon fontSize="small" /> },
      SAML: { label: 'SAML', color: 'error' as const, icon: <SecurityIcon fontSize="small" /> },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      color: 'default' as const, 
      icon: <SecurityIcon fontSize="small" /> 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
        icon={config.icon}
      />
    );
  };

  const getStatusChip = (enabled: boolean) => {
    return (
      <Chip
        label={enabled ? 'مفعل' : 'معطل'}
        color={enabled ? 'success' : 'default'}
        size="small"
        variant="outlined"
        icon={enabled ? <CheckCircleIcon fontSize="small" /> : <ErrorIcon fontSize="small" />}
      />
    );
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const maskApiKey = (key: string, show: boolean) => {
    if (show) return key;
    return key.substring(0, 8) + '...' + key.substring(key.length - 8);
  };

  const handleRevokeToken = async (tokenId: string) => {
    if (window.confirm('هل أنت متأكد من إلغاء هذا الرمز المميز؟')) {
      try {
        // TODO: Implement actual API call
        console.log('Revoking token:', tokenId);
        setSnackbarMessage('تم إلغاء الرمز المميز بنجاح');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error revoking token:', error);
      }
    }
  };

  const handleRevokeApiKey = async (keyId: string) => {
    if (window.confirm('هل أنت متأكد من إلغاء مفتاح API؟')) {
      try {
        // TODO: Implement actual API call
        console.log('Revoking API key:', keyId);
        setSnackbarMessage('تم إلغاء مفتاح API بنجاح');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error revoking API key:', error);
      }
    }
  };

  const providerColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم المزود',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <SecurityIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'type',
      headerName: 'النوع',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getTypeChip(params.value),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'usageCount',
      headerName: 'عدد الاستخدامات',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'lastUsed',
      headerName: 'آخر استخدام',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="تعديل"><EditIcon /></Tooltip>}
          label="تعديل"
          onClick={() => console.log('Edit provider:', params.id)}
        />,
        <GridActionsCellItem
          icon={<Tooltip title="حذف"><DeleteIcon /></Tooltip>}
          label="حذف"
          onClick={() => console.log('Delete provider:', params.id)}
        />,
      ],
    },
  ];

  const apiKeyColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم المفتاح',
      width: 150,
    },
    {
      field: 'userName',
      headerName: 'المستخدم',
      width: 150,
    },
    {
      field: 'key',
      headerName: 'المفتاح',
      width: 300,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
            {maskApiKey(params.value, showApiKey[params.row.id] || false)}
          </Typography>
          <IconButton
            size="small"
            onClick={() => toggleApiKeyVisibility(params.row.id)}
          >
            {showApiKey[params.row.id] ? <VisibilityOffIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
          </IconButton>
        </Box>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'expiresAt',
      headerName: 'تاريخ الانتهاء',
      width: 150,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'لا ينتهي',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="إلغاء"><DeleteIcon /></Tooltip>}
          label="إلغاء"
          onClick={() => handleRevokeApiKey(params.id as string)}
        />,
      ],
    },
  ];

  const tokenColumns: GridColDef[] = [
    {
      field: 'userName',
      headerName: 'المستخدم',
      width: 150,
    },
    {
      field: 'tokenType',
      headerName: 'نوع الرمز',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value === 'ACCESS' ? 'وصول' : 'تحديث'}
          color={params.value === 'ACCESS' ? 'primary' : 'secondary'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'deviceInfo',
      headerName: 'معلومات الجهاز',
      width: 200,
    },
    {
      field: 'ipAddress',
      headerName: 'عنوان IP',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'expiresAt',
      headerName: 'تاريخ الانتهاء',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="إلغاء"><DeleteIcon /></Tooltip>}
          label="إلغاء"
          onClick={() => handleRevokeToken(params.id as string)}
        />,
      ],
    },
  ];

  // Calculate stats
  const totalProviders = authProviders.length;
  const enabledProviders = authProviders.filter(p => p.enabled).length;
  const totalApiKeys = apiKeys.length;
  const activeTokens = jwtTokens.filter(t => new Date(t.expiresAt) > new Date()).length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          نظام المصادقة المتعدد
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة مزودي المصادقة ومفاتيح API والرموز المميزة
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <SecurityIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {enabledProviders}/{totalProviders}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    مزودي المصادقة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main' }}>
                  <KeyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalApiKeys}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    مفاتيح API
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TokenIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {activeTokens}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    الرموز النشطة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <ShieldIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    99.9%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل الأمان
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<SecurityIcon />} label="مزودي المصادقة" iconPosition="start" />
            <Tab icon={<KeyIcon />} label="مفاتيح API" iconPosition="start" />
            <Tab icon={<TokenIcon />} label="الرموز المميزة" iconPosition="start" />
            <Tab icon={<ShieldIcon />} label="الإعدادات" iconPosition="start" />
          </Tabs>
        </Box>

        {/* Auth Providers Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">مزودي المصادقة</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenProviderDialog(true)}
              >
                إضافة مزود
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={authProviders}
                columns={providerColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* API Keys Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">مفاتيح API</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenApiKeyDialog(true)}
              >
                إنشاء مفتاح API
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={apiKeys}
                columns={apiKeyColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* JWT Tokens Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>الرموز المميزة النشطة</Typography>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={jwtTokens}
                columns={tokenColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Settings Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>إعدادات الأمان</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>إعدادات JWT</Typography>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="مدة صلاحية الرمز"
                          secondary="3600 ثانية (ساعة واحدة)"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="مدة صلاحية رمز التحديث"
                          secondary="604800 ثانية (أسبوع واحد)"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="خوارزمية التشفير"
                          secondary="RS256"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>إعدادات الأمان</Typography>
                    <List>
                      <ListItem>
                        <ListItemText primary="فرض HTTPS" />
                        <Switch defaultChecked />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="تسجيل محاولات الدخول" />
                        <Switch defaultChecked />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="حد محاولات الدخول" />
                        <Switch defaultChecked />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="المصادقة ثنائية العامل" />
                        <Switch />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}
      </Card>

      {/* Snackbar */}
      {/* Add Provider Dialog */}
      <Dialog open={openProviderDialog} onClose={() => setOpenProviderDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة مزود مصادقة جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم المزود"
                variant="outlined"
                placeholder="مثال: Google OAuth"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>نوع المصادقة</InputLabel>
                <Select label="نوع المصادقة">
                  <MenuItem value="JWT">JWT</MenuItem>
                  <MenuItem value="OAUTH2">OAuth2</MenuItem>
                  <MenuItem value="LDAP">LDAP</MenuItem>
                  <MenuItem value="SAML">SAML</MenuItem>
                  <MenuItem value="API_KEY">API Key</MenuItem>
                  <MenuItem value="BASIC">Basic Auth</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="الوصف"
                multiline
                rows={3}
                variant="outlined"
                placeholder="وصف مزود المصادقة..."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client ID"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client Secret"
                type="password"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Redirect URL"
                variant="outlined"
                placeholder="https://your-app.com/auth/callback"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch defaultChecked />}
                label="تفعيل المزود"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenProviderDialog(false)}>إلغاء</Button>
          <Button
            variant="contained"
            onClick={() => {
              setOpenProviderDialog(false);
              setSnackbarMessage('تم إضافة مزود المصادقة بنجاح');
              setSnackbarOpen(true);
            }}
          >
            إضافة المزود
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default MultiAuthSystem;
