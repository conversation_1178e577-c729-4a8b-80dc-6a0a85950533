package com.tecnodrive.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * TECNO DRIVE API Gateway Application
 * 
 * This gateway handles:
 * - Request routing to microservices
 * - Authentication and authorization
 * - Rate limiting and throttling
 * - Request/response transformation
 * - Circuit breaker patterns
 * - Load balancing
 * - CORS handling
 * - Request logging and monitoring
 * - API versioning
 * - Security headers
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
public class ApiGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiGatewayApplication.class, args);
    }
}
