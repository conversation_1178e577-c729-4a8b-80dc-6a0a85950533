.interactive-map-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.interactive-map-page.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 0;
}

.interactive-map-page.fullscreen .map-section {
  height: 100vh;
}

.interactive-map-page.fullscreen .map-card {
  height: 100vh;
  border: none;
  border-radius: 0;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.header-actions .ant-btn {
  margin-left: 8px;
}

/* Statistics Row */
.stats-row {
  margin-bottom: 24px;
}

.stats-row .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-row .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Map Section */
.map-section {
  margin-bottom: 24px;
}

.map-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.map-card .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.map-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.map-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
}

.last-update {
  color: #666;
}

.auto-refresh {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.auto-refresh.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.auto-refresh.inactive {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

/* Map Legend */
.map-legend {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.legend-icon.vehicle-active {
  background: #52c41a;
}

.legend-icon.vehicle-busy {
  background: #faad14;
}

.legend-icon.vehicle-offline {
  background: #d9d9d9;
}

.legend-icon.traffic-heavy {
  background: #ff4d4f;
}

.legend-icon.traffic-moderate {
  background: #faad14;
}

.legend-icon.traffic-light {
  background: #fadb14;
}

.legend-icon.traffic-free {
  background: #52c41a;
}

.legend-icon.geofence-active {
  background: transparent;
  border: 2px solid #ff4d4f;
  border-radius: 2px;
}

.legend-icon.route-active {
  background: #1890ff;
  border-radius: 2px;
  height: 4px;
}

/* Quick Actions */
.quick-actions {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions .ant-btn {
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .interactive-map-page {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content h1 {
    font-size: 20px;
  }

  .header-actions .ant-btn {
    margin-left: 4px;
    padding: 4px 8px;
  }

  .map-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .map-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .stats-row .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .interactive-map-page {
    padding: 8px;
  }

  .page-header {
    padding: 12px;
  }

  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-actions .ant-space {
    width: 100%;
  }

  .quick-actions .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* Animation for real-time updates */
@keyframes mapUpdate {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.map-updating {
  animation: mapUpdate 2s ease-in-out infinite;
}

/* Loading states */
.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.map-loading-spinner {
  text-align: center;
}

.map-loading-spinner .ant-spin {
  font-size: 24px;
}

/* Error states */
.map-error {
  padding: 40px;
  text-align: center;
  color: #ff4d4f;
}

.map-error .ant-result {
  padding: 20px;
}

/* Success states */
.map-success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px 16px;
  color: #52c41a;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar for panels */
.map-panel::-webkit-scrollbar {
  width: 6px;
}

.map-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.map-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.map-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Print styles */
@media print {
  .interactive-map-page {
    background: white;
    padding: 0;
  }

  .page-header,
  .stats-row,
  .map-legend,
  .quick-actions {
    display: none;
  }

  .map-section {
    margin: 0;
    height: 100vh;
  }

  .map-card {
    border: none;
    box-shadow: none;
  }
}
