package com.tecnodrive.notificationservice.exception;

/**
 * Template Not Found Exception
 * 
 * Thrown when a requested notification template cannot be found
 */
public class TemplateNotFoundException extends RuntimeException {

    public TemplateNotFoundException() {
        super("Notification template not found");
    }

    public TemplateNotFoundException(String message) {
        super(message);
    }

    public TemplateNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
