server:
  port: 8091

spring:
  application:
    name: analytics-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_analytics
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  cache:
    type: simple
eureka:
  client:
    service-url:
      defaultZone: http://eureka:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics

# Logging
logging:
  level:
    com.tecnodrive.analyticsservice: DEBUG

# Analytics Configuration
analytics:
  reports:
    cache-duration: 300 # 5 minutes
    max-export-size: 100000 # Maximum rows for export
    default-page-size: 50
  export:
    pdf:
      page-size: A4
      orientation: PORTRAIT
    excel:
      max-rows: 65536
      sheet-name: "Analytics Report"
  dashboard:
    refresh-interval: 30 # seconds
    real-time-enabled: true

# Feign Configuration
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000

# Async Configuration
async:
  core-pool-size: 5
  max-pool-size: 10
  queue-capacity: 100

# Scheduling Configuration
scheduling:
  daily-reports-cron: "0 0 6 * * *"
  weekly-reports-cron: "0 0 6 * * MON"
  monthly-reports-cron: "0 0 6 1 * *"
