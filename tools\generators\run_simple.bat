@echo off
echo ========================================
echo    TecnoDrive Data Generator - Yemen
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

echo Python found! Checking Faker library...
python -c "import faker" >nul 2>&1
if %errorlevel% neq 0 (
    echo Faker not installed. Installing...
    python -m pip install faker
    if %errorlevel% neq 0 (
        echo Failed to install Faker. Using simple version...
        echo.
        echo Running simple data generator...
        python simple_yemen_data.py
        goto :end
    )
)

echo Running full data generator...
python generate_yemen_data.py

:end
echo.
echo Done! Check the generated_data folder
pause
