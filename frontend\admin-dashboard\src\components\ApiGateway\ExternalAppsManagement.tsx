import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Apps as AppsIcon,
  Api as ApiIcon,
  Code as CodeIcon,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Key as KeyIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';

interface ExternalApp {
  id: string;
  name: string;
  description: string;
  clientId: string;
  clientSecret: string;
  redirectUris: string[];
  scopes: string[];
  status: 'ACTIVE' | 'SUSPENDED' | 'PENDING';
  tier: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  owner: string;
  ownerEmail: string;
  createdAt: string;
  lastUsed?: string;
  requestCount: number;
  errorRate: number;
}

interface ApiConsumer {
  id: string;
  name: string;
  appId: string;
  appName: string;
  apiKey: string;
  quotaLimit: number;
  quotaUsed: number;
  rateLimit: number;
  permissions: string[];
  enabled: boolean;
  createdAt: string;
  lastRequest?: string;
}

interface ApiUsage {
  appId: string;
  appName: string;
  requests: number;
  errors: number;
  avgResponseTime: number;
  date: string;
}

const ExternalAppsManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [externalApps, setExternalApps] = useState<ExternalApp[]>([]);
  const [apiConsumers, setApiConsumers] = useState<ApiConsumer[]>([]);
  const [usageData, setUsageData] = useState<ApiUsage[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterTier, setFilterTier] = useState('ALL');
  const [openAppDialog, setOpenAppDialog] = useState(false);
  const [openConsumerDialog, setOpenConsumerDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Mock data
  const mockExternalApps: ExternalApp[] = [
    {
      id: 'app-1',
      name: 'تطبيق الهاتف المحمول',
      description: 'تطبيق الركاب الرسمي لنظام تكنو درايف',
      clientId: 'mobile_app_client_123',
      clientSecret: 'secret_abc123def456',
      redirectUris: ['https://mobile.tecno-drive.com/callback'],
      scopes: ['rides:read', 'rides:create', 'profile:read'],
      status: 'ACTIVE',
      tier: 'ENTERPRISE',
      owner: 'فريق التطوير',
      ownerEmail: '<EMAIL>',
      createdAt: '2025-01-15T08:00:00Z',
      lastUsed: '2025-07-09T14:30:00Z',
      requestCount: 125000,
      errorRate: 0.5,
    },
    {
      id: 'app-2',
      name: 'لوحة تحكم الشركاء',
      description: 'واجهة إدارة للشركاء وشركات التوصيل',
      clientId: 'partner_dashboard_456',
      clientSecret: 'secret_def456ghi789',
      redirectUris: ['https://partners.tecno-drive.com/auth'],
      scopes: ['fleet:read', 'analytics:read', 'reports:read'],
      status: 'ACTIVE',
      tier: 'PREMIUM',
      owner: 'قسم الشراكات',
      ownerEmail: '<EMAIL>',
      createdAt: '2025-02-01T10:00:00Z',
      lastUsed: '2025-07-09T13:45:00Z',
      requestCount: 45000,
      errorRate: 1.2,
    },
    {
      id: 'app-3',
      name: 'تطبيق السائقين',
      description: 'تطبيق مخصص للسائقين لإدارة الرحلات',
      clientId: 'driver_app_789',
      clientSecret: 'secret_ghi789jkl012',
      redirectUris: ['https://driver.tecno-drive.com/callback'],
      scopes: ['rides:read', 'rides:update', 'location:update'],
      status: 'PENDING',
      tier: 'BASIC',
      owner: 'فريق السائقين',
      ownerEmail: '<EMAIL>',
      createdAt: '2025-06-15T12:00:00Z',
      requestCount: 0,
      errorRate: 0,
    },
  ];

  const mockApiConsumers: ApiConsumer[] = [
    {
      id: 'consumer-1',
      name: 'Mobile App API Consumer',
      appId: 'app-1',
      appName: 'تطبيق الهاتف المحمول',
      apiKey: 'ak_mobile_123456789abcdef',
      quotaLimit: 100000,
      quotaUsed: 85000,
      rateLimit: 1000,
      permissions: ['rides:read', 'rides:create', 'profile:read'],
      enabled: true,
      createdAt: '2025-01-15T08:00:00Z',
      lastRequest: '2025-07-09T14:30:00Z',
    },
    {
      id: 'consumer-2',
      name: 'Partner Dashboard Consumer',
      appId: 'app-2',
      appName: 'لوحة تحكم الشركاء',
      apiKey: 'ak_partner_abcdef123456789',
      quotaLimit: 50000,
      quotaUsed: 32000,
      rateLimit: 500,
      permissions: ['fleet:read', 'analytics:read'],
      enabled: true,
      createdAt: '2025-02-01T10:00:00Z',
      lastRequest: '2025-07-09T13:45:00Z',
    },
  ];

  const mockUsageData: ApiUsage[] = [
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 12000, errors: 60, avgResponseTime: 150, date: '2025-07-03' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 13500, errors: 70, avgResponseTime: 145, date: '2025-07-04' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 11800, errors: 55, avgResponseTime: 160, date: '2025-07-05' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 14200, errors: 80, avgResponseTime: 140, date: '2025-07-06' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 13800, errors: 65, avgResponseTime: 155, date: '2025-07-07' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 15000, errors: 75, avgResponseTime: 135, date: '2025-07-08' },
    { appId: 'app-1', appName: 'تطبيق الهاتف المحمول', requests: 14500, errors: 70, avgResponseTime: 150, date: '2025-07-09' },
  ];

  useEffect(() => {
    setExternalApps(mockExternalApps);
    setApiConsumers(mockApiConsumers);
    setUsageData(mockUsageData);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> },
      SUSPENDED: { label: 'معلق', color: 'error' as const, icon: <ErrorIcon fontSize="small" /> },
      PENDING: { label: 'في الانتظار', color: 'warning' as const, icon: <WarningIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: null 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getTierChip = (tier: string) => {
    const tierConfig = {
      FREE: { label: 'مجاني', color: 'default' as const },
      BASIC: { label: 'أساسي', color: 'info' as const },
      PREMIUM: { label: 'مميز', color: 'warning' as const },
      ENTERPRISE: { label: 'مؤسسي', color: 'success' as const },
    };

    const config = tierConfig[tier as keyof typeof tierConfig] || { 
      label: tier, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const handleApproveApp = async (appId: string) => {
    try {
      // TODO: Implement actual API call
      console.log('Approving app:', appId);
      setSnackbarMessage('تم الموافقة على التطبيق');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error approving app:', error);
    }
  };

  const handleSuspendApp = async (appId: string) => {
    if (window.confirm('هل أنت متأكد من تعليق هذا التطبيق؟')) {
      try {
        // TODO: Implement actual API call
        console.log('Suspending app:', appId);
        setSnackbarMessage('تم تعليق التطبيق');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error suspending app:', error);
      }
    }
  };

  const appColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم التطبيق',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <AppsIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'owner',
      headerName: 'المالك',
      width: 150,
    },
    {
      field: 'tier',
      headerName: 'الخطة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getTierChip(params.value),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'requestCount',
      headerName: 'عدد الطلبات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'errorRate',
      headerName: 'معدل الخطأ',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color={params.value > 1 ? 'error.main' : 'success.main'}>
          {params.value}%
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="عرض"><VisibilityIcon /></Tooltip>}
          label="عرض"
          onClick={() => console.log('View app:', params.id)}
        />,
        <GridActionsCellItem
          icon={<Tooltip title="تعديل"><EditIcon /></Tooltip>}
          label="تعديل"
          onClick={() => console.log('Edit app:', params.id)}
        />,
        ...(params.row.status === 'PENDING' ? [
          <GridActionsCellItem
            icon={<Tooltip title="موافقة"><CheckCircleIcon /></Tooltip>}
            label="موافقة"
            onClick={() => handleApproveApp(params.id as string)}
          />
        ] : []),
        ...(params.row.status === 'ACTIVE' ? [
          <GridActionsCellItem
            icon={<Tooltip title="تعليق"><ErrorIcon /></Tooltip>}
            label="تعليق"
            onClick={() => handleSuspendApp(params.id as string)}
          />
        ] : []),
      ],
    },
  ];

  const consumerColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم المستهلك',
      width: 200,
    },
    {
      field: 'appName',
      headerName: 'التطبيق',
      width: 180,
    },
    {
      field: 'quotaUsage',
      headerName: 'استخدام الحصة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const percentage = (params.row.quotaUsed / params.row.quotaLimit) * 100;
        return (
          <Box sx={{ width: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                {params.row.quotaUsed.toLocaleString()} / {params.row.quotaLimit.toLocaleString()}
              </Typography>
              <Typography variant="body2">{percentage.toFixed(1)}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={percentage}
              color={percentage > 90 ? 'error' : percentage > 70 ? 'warning' : 'success'}
            />
          </Box>
        );
      },
    },
    {
      field: 'rateLimit',
      headerName: 'حد المعدل',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.value}/دقيقة
        </Typography>
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Switch checked={params.value} size="small" />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="تعديل"><EditIcon /></Tooltip>}
          label="تعديل"
          onClick={() => console.log('Edit consumer:', params.id)}
        />,
        <GridActionsCellItem
          icon={<Tooltip title="حذف"><DeleteIcon /></Tooltip>}
          label="حذف"
          onClick={() => console.log('Delete consumer:', params.id)}
        />,
      ],
    },
  ];

  const filteredApps = externalApps.filter(app => {
    const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.owner.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'ALL' || app.status === filterStatus;
    const matchesTier = filterTier === 'ALL' || app.tier === filterTier;
    return matchesSearch && matchesStatus && matchesTier;
  });

  // Calculate stats
  const totalApps = externalApps.length;
  const activeApps = externalApps.filter(app => app.status === 'ACTIVE').length;
  const pendingApps = externalApps.filter(app => app.status === 'PENDING').length;
  const totalRequests = externalApps.reduce((sum, app) => sum + app.requestCount, 0);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة التطبيقات الخارجية
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة التطبيقات الخارجية ومستهلكي API
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AppsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalApps}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي التطبيقات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <CheckCircleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {activeApps}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    التطبيقات النشطة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <WarningIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {pendingApps}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    في الانتظار
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {(totalRequests / 1000).toFixed(0)}K
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الطلبات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<AppsIcon />} label="التطبيقات الخارجية" iconPosition="start" />
            <Tab icon={<ApiIcon />} label="مستهلكي API" iconPosition="start" />
            <Tab icon={<AssessmentIcon />} label="تحليلات الاستخدام" iconPosition="start" />
            <Tab icon={<SecurityIcon />} label="الأمان والصلاحيات" iconPosition="start" />
          </Tabs>
        </Box>

        {/* External Apps Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  placeholder="البحث في التطبيقات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <FormControl sx={{ minWidth: 120 }}>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    label="الحالة"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الحالات</MenuItem>
                    <MenuItem value="ACTIVE">نشط</MenuItem>
                    <MenuItem value="PENDING">في الانتظار</MenuItem>
                    <MenuItem value="SUSPENDED">معلق</MenuItem>
                  </Select>
                </FormControl>
                <FormControl sx={{ minWidth: 120 }}>
                  <InputLabel>الخطة</InputLabel>
                  <Select
                    value={filterTier}
                    label="الخطة"
                    onChange={(e) => setFilterTier(e.target.value)}
                  >
                    <MenuItem value="ALL">جميع الخطط</MenuItem>
                    <MenuItem value="FREE">مجاني</MenuItem>
                    <MenuItem value="BASIC">أساسي</MenuItem>
                    <MenuItem value="PREMIUM">مميز</MenuItem>
                    <MenuItem value="ENTERPRISE">مؤسسي</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenAppDialog(true)}
              >
                تسجيل تطبيق جديد
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={filteredApps}
                columns={appColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* API Consumers Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">مستهلكي API</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenConsumerDialog(true)}
              >
                إضافة مستهلك API
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={apiConsumers}
                columns={consumerColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Usage Analytics Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>تحليلات استخدام API</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      طلبات API خلال الأسبوع الماضي
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={usageData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartsTooltip />
                        <Line 
                          type="monotone" 
                          dataKey="requests" 
                          stroke="#2196f3" 
                          strokeWidth={3}
                          name="الطلبات"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="errors" 
                          stroke="#f44336" 
                          strokeWidth={2}
                          name="الأخطاء"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Security Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>الأمان والصلاحيات</Typography>
            <Alert severity="info">
              قريباً - إدارة الصلاحيات والأمان للتطبيقات الخارجية
            </Alert>
          </Box>
        )}
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default ExternalAppsManagement;
