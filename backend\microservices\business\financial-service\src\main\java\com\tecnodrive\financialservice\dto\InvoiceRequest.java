package com.tecnodrive.financialservice.dto;

import com.tecnodrive.financialservice.entity.Invoice;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

/**
 * Invoice Request DTO
 * 
 * Used for creating and updating invoices
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceRequest {

    /**
     * Unique invoice number (optional - auto-generated if not provided)
     */
    @Size(max = 50, message = "Invoice number cannot exceed 50 characters")
    private String invoiceNumber;

    /**
     * Entity type being billed (CUSTOMER, DRIVER, COMPANY)
     */
    @NotBlank(message = "Billed entity type is required")
    @Size(max = 50, message = "Billed entity type cannot exceed 50 characters")
    private String billedEntityType;

    /**
     * Entity ID being billed
     */
    @NotBlank(message = "Billed entity ID is required")
    private String billedEntityId;

    /**
     * Invoice type
     */
    @NotNull(message = "Invoice type is required")
    private Invoice.InvoiceType invoiceType;

    /**
     * Issue date
     */
    @NotNull(message = "Issue date is required")
    private LocalDate issueDate;

    /**
     * Due date
     */
    @NotNull(message = "Due date is required")
    private LocalDate dueDate;

    /**
     * Subtotal amount (before tax)
     */
    @NotNull(message = "Subtotal amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Subtotal amount must be greater than 0")
    @Digits(integer = 17, fraction = 2, message = "Subtotal amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal subtotalAmount;

    /**
     * Tax amount
     */
    @DecimalMin(value = "0.0", message = "Tax amount cannot be negative")
    @Digits(integer = 17, fraction = 2, message = "Tax amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal taxAmount;

    /**
     * Discount amount
     */
    @DecimalMin(value = "0.0", message = "Discount amount cannot be negative")
    @Digits(integer = 17, fraction = 2, message = "Discount amount must have at most 17 integer digits and 2 decimal places")
    private BigDecimal discountAmount;

    /**
     * Currency code
     */
    @Size(min = 3, max = 3, message = "Currency must be 3 characters")
    private String currency;

    /**
     * Invoice description/notes
     */
    private String description;

    /**
     * Terms and conditions
     */
    private String terms;

    /**
     * Company/Tenant ID
     */
    @NotBlank(message = "Company ID is required")
    private String companyId;

    /**
     * Created by user ID
     */
    private String createdBy;

    /**
     * Additional metadata
     */
    private Map<String, Object> metadata;
}
