{{- if .Values.security.policies.networkPolicies.enabled }}
# Default Deny All Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: default-deny-all
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress

---
# Allow DNS Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: allow-dns
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector: {}
  policyTypes:
    - Egress
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53

---
# API Gateway Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-network-policy
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: api-gateway-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow traffic from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow traffic from other TECNO DRIVE services
    - from:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: frontend
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: business
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow monitoring traffic
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8081
  
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication with Eureka
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: eureka
      ports:
        - protocol: TCP
          port: 8761
    
    # Allow communication with business services
    - to:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: business
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow communication with core services
    - to:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: core
      ports:
        - protocol: TCP
          port: 8080

---
# Business Services Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: business-services-network-policy
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: business-services-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector:
    matchLabels:
      tecno-drive.com/tier: business
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow traffic from API Gateway
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: api-gateway
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow traffic from other business services
    - from:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: business
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow monitoring traffic
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8081
  
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication with database
    - to:
        - namespaceSelector:
            matchLabels:
              name: database
      ports:
        - protocol: TCP
          port: 5432
        - protocol: TCP
          port: 6379
    
    # Allow communication with core services
    - to:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: core
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow communication with other business services
    - to:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: business
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow communication with Eureka
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: eureka
      ports:
        - protocol: TCP
          port: 8761

---
# Core Services Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: core-services-network-policy
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: core-services-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector:
    matchLabels:
      tecno-drive.com/tier: core
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow traffic from API Gateway
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: api-gateway
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow traffic from business services
    - from:
        - podSelector:
            matchLabels:
              tecno-drive.com/tier: business
      ports:
        - protocol: TCP
          port: 8080
    
    # Allow monitoring traffic
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8081
  
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication with database
    - to:
        - namespaceSelector:
            matchLabels:
              name: database
      ports:
        - protocol: TCP
          port: 5432
        - protocol: TCP
          port: 6379
    
    # Allow communication with Eureka
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: eureka
      ports:
        - protocol: TCP
          port: 8761

---
# Monitoring Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-network-policy
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: monitoring-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector:
    matchLabels:
      tecno-drive.com/component: monitoring
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow traffic from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 3000
        - protocol: TCP
          port: 9090
    
    # Allow internal monitoring traffic
    - from:
        - podSelector:
            matchLabels:
              tecno-drive.com/component: monitoring
      ports:
        - protocol: TCP
          port: 3000
        - protocol: TCP
          port: 9090
        - protocol: TCP
          port: 9093
  
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow scraping metrics from all services
    - to: []
      ports:
        - protocol: TCP
          port: 8080
        - protocol: TCP
          port: 8081
        - protocol: TCP
          port: 2020

---
# Gatekeeper Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gatekeeper-network-policy
  namespace: gatekeeper-system
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: gatekeeper-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/policy-type: "network-security"
spec:
  podSelector:
    matchLabels:
      gatekeeper.sh/system: "yes"
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow webhook traffic from API server
    - from: []
      ports:
        - protocol: TCP
          port: 8443
    
    # Allow monitoring traffic
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8888
  
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication with Kubernetes API
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 6443
{{- end }}
