package com.tecnodrive.authservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Authentication Service Application
 *
 * This service handles:
 * - User authentication and authorization
 * - JWT token generation and validation
 * - User registration and management
 * - Password reset functionality
 * - Role-based access control
 * - Session management
 * - OAuth2 integration
 * - Multi-factor authentication
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
@EnableTransactionManagement
public class AuthServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }
}
