package com.tecnodrive.fleetservice.repository;

import com.tecnodrive.fleetservice.entity.Vehicle;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Vehicle Repository
 * 
 * Data access layer for Vehicle entities
 */
@Repository
public interface VehicleRepository extends JpaRepository<Vehicle, UUID> {

    /**
     * Find vehicle by plate number
     */
    Optional<Vehicle> findByPlateNumber(String plateNumber);

    /**
     * Find vehicle by VIN
     */
    Optional<Vehicle> findByVin(String vin);

    /**
     * Find vehicles by company
     */
    List<Vehicle> findByCompanyId(String companyId);

    /**
     * Find vehicles by company with pagination
     */
    Page<Vehicle> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find vehicles by status
     */
    List<Vehicle> findByStatus(Vehicle.VehicleStatus status);

    /**
     * Find vehicles by company and status
     */
    List<Vehicle> findByCompanyIdAndStatus(String companyId, Vehicle.VehicleStatus status);

    /**
     * Find vehicles by type
     */
    List<Vehicle> findByVehicleType(Vehicle.VehicleType vehicleType);

    /**
     * Find vehicles by company and type
     */
    List<Vehicle> findByCompanyIdAndVehicleType(String companyId, Vehicle.VehicleType vehicleType);

    /**
     * Find vehicles by fuel type
     */
    List<Vehicle> findByFuelType(Vehicle.FuelType fuelType);

    /**
     * Find vehicles by assigned driver
     */
    List<Vehicle> findByAssignedDriverId(String driverId);

    /**
     * Find active vehicles
     */
    List<Vehicle> findByIsActiveTrue();

    /**
     * Find active vehicles by company
     */
    List<Vehicle> findByCompanyIdAndIsActiveTrue(String companyId);

    /**
     * Find available vehicles
     */
    @Query("SELECT v FROM Vehicle v WHERE v.status = 'AVAILABLE' AND v.isActive = true")
    List<Vehicle> findAvailableVehicles();

    /**
     * Find available vehicles by company
     */
    @Query("SELECT v FROM Vehicle v WHERE v.companyId = :companyId AND v.status = 'AVAILABLE' AND v.isActive = true")
    List<Vehicle> findAvailableVehiclesByCompany(@Param("companyId") String companyId);

    /**
     * Find vehicles needing maintenance
     */
    @Query("SELECT v FROM Vehicle v WHERE v.nextMaintenanceDate <= :currentDate AND v.isActive = true")
    List<Vehicle> findVehiclesNeedingMaintenance(@Param("currentDate") LocalDate currentDate);

    /**
     * Find vehicles with expiring insurance
     */
    @Query("SELECT v FROM Vehicle v WHERE v.insuranceExpiryDate BETWEEN :startDate AND :endDate AND v.isActive = true")
    List<Vehicle> findVehiclesWithExpiringInsurance(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find vehicles with expiring registration
     */
    @Query("SELECT v FROM Vehicle v WHERE v.registrationExpiryDate BETWEEN :startDate AND :endDate AND v.isActive = true")
    List<Vehicle> findVehiclesWithExpiringRegistration(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find vehicles by make and model
     */
    List<Vehicle> findByMakeAndModel(String make, String model);

    /**
     * Find vehicles by year range
     */
    @Query("SELECT v FROM Vehicle v WHERE v.year BETWEEN :startYear AND :endYear")
    List<Vehicle> findByYearRange(@Param("startYear") int startYear, @Param("endYear") int endYear);

    /**
     * Find vehicles by odometer range
     */
    @Query("SELECT v FROM Vehicle v WHERE v.odometerReading BETWEEN :minOdometer AND :maxOdometer")
    List<Vehicle> findByOdometerRange(@Param("minOdometer") BigDecimal minOdometer, @Param("maxOdometer") BigDecimal maxOdometer);

    /**
     * Search vehicles by plate number, make, or model
     */
    @Query("SELECT v FROM Vehicle v WHERE " +
           "LOWER(v.plateNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.make) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.model) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Vehicle> searchVehicles(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Search vehicles by company
     */
    @Query("SELECT v FROM Vehicle v WHERE v.companyId = :companyId AND (" +
           "LOWER(v.plateNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.make) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.model) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Vehicle> searchVehiclesByCompany(@Param("companyId") String companyId, @Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Count vehicles by status
     */
    long countByStatus(Vehicle.VehicleStatus status);

    /**
     * Count vehicles by company and status
     */
    long countByCompanyIdAndStatus(String companyId, Vehicle.VehicleStatus status);

    /**
     * Count active vehicles by company
     */
    long countByCompanyIdAndIsActiveTrue(String companyId);

    /**
     * Calculate average vehicle age by company
     */
    @Query("SELECT AVG(YEAR(CURRENT_DATE) - v.year) FROM Vehicle v WHERE v.companyId = :companyId AND v.isActive = true")
    Double calculateAverageAgeByCompany(@Param("companyId") String companyId);

    /**
     * Calculate total odometer reading by company
     */
    @Query("SELECT COALESCE(SUM(v.odometerReading), 0) FROM Vehicle v WHERE v.companyId = :companyId AND v.isActive = true")
    BigDecimal calculateTotalOdometerByCompany(@Param("companyId") String companyId);

    /**
     * Calculate average odometer reading by company
     */
    @Query("SELECT COALESCE(AVG(v.odometerReading), 0) FROM Vehicle v WHERE v.companyId = :companyId AND v.isActive = true")
    BigDecimal calculateAverageOdometerByCompany(@Param("companyId") String companyId);

    /**
     * Get vehicle statistics by type
     */
    @Query("SELECT v.vehicleType, COUNT(v) FROM Vehicle v WHERE v.companyId = :companyId AND v.isActive = true GROUP BY v.vehicleType")
    List<Object[]> getVehicleStatisticsByType(@Param("companyId") String companyId);

    /**
     * Get vehicle statistics by fuel type
     */
    @Query("SELECT v.fuelType, COUNT(v) FROM Vehicle v WHERE v.companyId = :companyId AND v.isActive = true GROUP BY v.fuelType")
    List<Object[]> getVehicleStatisticsByFuelType(@Param("companyId") String companyId);

    /**
     * Get vehicle statistics by status
     */
    @Query("SELECT v.status, COUNT(v) FROM Vehicle v WHERE v.companyId = :companyId GROUP BY v.status")
    List<Object[]> getVehicleStatisticsByStatus(@Param("companyId") String companyId);

    /**
     * Find vehicles with high mileage
     */
    @Query("SELECT v FROM Vehicle v WHERE v.odometerReading > :mileageThreshold AND v.isActive = true")
    List<Vehicle> findHighMileageVehicles(@Param("mileageThreshold") BigDecimal mileageThreshold);

    /**
     * Find unassigned vehicles
     */
    @Query("SELECT v FROM Vehicle v WHERE v.assignedDriverId IS NULL AND v.status = 'AVAILABLE' AND v.isActive = true")
    List<Vehicle> findUnassignedVehicles();

    /**
     * Find unassigned vehicles by company
     */
    @Query("SELECT v FROM Vehicle v WHERE v.companyId = :companyId AND v.assignedDriverId IS NULL AND v.status = 'AVAILABLE' AND v.isActive = true")
    List<Vehicle> findUnassignedVehiclesByCompany(@Param("companyId") String companyId);

    /**
     * Check if plate number exists
     */
    boolean existsByPlateNumber(String plateNumber);

    /**
     * Check if VIN exists
     */
    boolean existsByVin(String vin);

    /**
     * Count vehicles by company
     */
    long countByCompanyId(String companyId);

    /**
     * Find vehicles by GPS device ID
     */
    Optional<Vehicle> findByGpsDeviceId(String gpsDeviceId);
}
