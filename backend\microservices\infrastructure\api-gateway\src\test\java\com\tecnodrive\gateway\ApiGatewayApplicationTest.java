package com.tecnodrive.gateway;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * Basic test for API Gateway Application
 */
@SpringBootTest
@TestPropertySource(properties = {
    "eureka.client.enabled=false",
    "spring.cloud.gateway.enabled=false",
    "jwt.secret=test-secret-key-for-testing-purposes-only",
    "spring.data.redis.repositories.enabled=false",
    "spring.cloud.gateway.redis-rate-limiter.enabled=false"
})
class ApiGatewayApplicationTest {

    @Test
    void contextLoads() {
        // Test that the application context loads successfully
    }
}
