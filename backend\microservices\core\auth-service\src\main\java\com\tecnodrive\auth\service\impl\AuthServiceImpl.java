package com.tecnodrive.auth.service.impl;

import com.tecnodrive.auth.dto.*;
import com.tecnodrive.auth.entity.User;
import com.tecnodrive.auth.entity.UserStatus;
import com.tecnodrive.auth.entity.UserType;
import com.tecnodrive.auth.repository.UserRepository;
import com.tecnodrive.auth.service.AuthService;
import com.tecnodrive.auth.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

/**
 * Authentication Service Implementation
 */
@Service
@Transactional
public class AuthServiceImpl implements AuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public AuthResponse login(LoginRequest request) {
        log.info("Login attempt for {}", request.getUsernameOrEmail());

        // Find user by username or email
        User user = userRepository.findByUsernameOrEmail(request.getUsernameOrEmail())
                .orElseThrow(() -> new RuntimeException("Invalid credentials"));

        // Verify password
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            log.warn("Invalid password for user: {}", request.getUsernameOrEmail());
            throw new RuntimeException("Invalid credentials");
        }

        // Check user status
        if (user.getStatus() != UserStatus.ACTIVE) {
            log.warn("Inactive user login attempt: {}", request.getUsernameOrEmail());
            throw new RuntimeException("Account is not active");
        }

        // Update last login time
        user.setLastLoginAt(Instant.now());
        userRepository.save(user);

        // Generate token
        String token = jwtService.generateAccessToken(user);

        // Create user response
        UserResponse userResponse = convertToUserResponse(user);

        log.info("Successful login for user: {}", user.getEmail());

        return new AuthResponse(token, "Bearer", jwtService.getAccessTokenExpirationTime(), userResponse);
    }

    @Override
    public AuthResponse register(RegisterRequest request) {
        log.info("Registration attempt for {}", request.getEmail());

        // Check if username already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("Username already exists");
        }

        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Email already exists");
        }

        // Create new user
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setUserType(request.getUserType() != null ? request.getUserType() : UserType.CUSTOMER);
        user.setStatus(UserStatus.ACTIVE);
        user.setCompanyId(request.getCompanyId());
        user.setAddress(request.getAddress());
        user.setCity(request.getCity());
        user.setCountry(request.getCountry());
        user.setPostalCode(request.getPostalCode());
        user.setEmailVerified(false);

        // Save user
        user = userRepository.save(user);

        log.info("User registered successfully: {}", user.getEmail());

        // Generate token
        String token = jwtService.generateAccessToken(user);

        // Create user response
        UserResponse userResponse = convertToUserResponse(user);

        return new AuthResponse(token, "Bearer", jwtService.getAccessTokenExpirationTime(), userResponse);
    }

    @Override
    public UserResponse getProfile(String usernameOrEmail) {
        log.info("Getting profile for user: {}", usernameOrEmail);

        User user = userRepository.findByUsernameOrEmail(usernameOrEmail)
                .orElseThrow(() -> new RuntimeException("User not found"));

        return convertToUserResponse(user);
    }

    @Override
    public UserResponse updateProfile(String usernameOrEmail, UpdateProfileRequest request) {
        log.info("Updating profile for user: {}", usernameOrEmail);

        User user = userRepository.findByUsernameOrEmail(usernameOrEmail)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Update fields if provided
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new RuntimeException("Email already exists");
            }
            user.setEmail(request.getEmail());
        }

        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }

        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }

        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }

        if (request.getProfileImageUrl() != null) {
            user.setProfileImageUrl(request.getProfileImageUrl());
        }

        if (request.getAddress() != null) {
            user.setAddress(request.getAddress());
        }

        if (request.getCity() != null) {
            user.setCity(request.getCity());
        }

        if (request.getCountry() != null) {
            user.setCountry(request.getCountry());
        }

        if (request.getPostalCode() != null) {
            user.setPostalCode(request.getPostalCode());
        }

        user = userRepository.save(user);

        log.info("Profile updated successfully for user: {}", usernameOrEmail);

        return convertToUserResponse(user);
    }

    @Override
    public void changePassword(ChangePasswordRequest request) {
        log.info("Changing password for user: {}", request.getUsernameOrEmail());

        User user = userRepository.findByUsernameOrEmail(request.getUsernameOrEmail())
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Verify current password
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPasswordHash())) {
            throw new RuntimeException("Current password is incorrect");
        }

        // Verify password confirmation
        if (!request.isPasswordConfirmed()) {
            throw new RuntimeException("Password confirmation does not match");
        }

        // Update password
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        log.info("Password changed successfully for user: {}", request.getUsernameOrEmail());
    }

    @Override
    public AuthResponse refreshToken(String refreshToken) {
        log.info("Attempting to refresh token");

        // Extract username from refresh token
        String username = jwtService.extractUsername(refreshToken);
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Validate refresh token
        if (!jwtService.isTokenValid(refreshToken, user)) {
            throw new RuntimeException("Invalid refresh token");
        }

        // Generate new access token
        String newAccessToken = jwtService.generateAccessToken(user);

        log.info("Token refreshed successfully for user: {}", user.getEmail());

        // Create user response
        UserResponse userResponse = convertToUserResponse(user);

        return new AuthResponse(newAccessToken, "Bearer", jwtService.getAccessTokenExpirationTime(), userResponse);
    }

    @Override
    public void logout(String refreshToken) {
        log.info("User logout");
        // In a real implementation, you would blacklist the token
        // For now, we'll just log the logout
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setFirstName(user.getFirstName());
        response.setLastName(user.getLastName());
        response.setPhoneNumber(user.getPhoneNumber());
        response.setUserType(user.getUserType());
        response.setStatus(user.getStatus());
        response.setCompanyId(user.getCompanyId());
        response.setProfileImageUrl(user.getProfileImageUrl());
        response.setAddress(user.getAddress());
        response.setCity(user.getCity());
        response.setCountry(user.getCountry());
        response.setPostalCode(user.getPostalCode());
        response.setEmailVerified(user.isEmailVerified());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        return response;
    }
}
