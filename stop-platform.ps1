# TecnoDrive Platform - سكريبت إيقاف الخدمات
# Stop Script for TecnoDrive Platform

param(
    [switch]$Force,
    [switch]$KeepDatabase,
    [switch]$Help
)

if ($Help) {
    Write-Host "=== TecnoDrive Platform Stop Script ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\stop-platform.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Force            Force stop all processes"
    Write-Host "  -KeepDatabase     Keep database services running"
    Write-Host "  -Help             Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\stop-platform.ps1              # Stop all services gracefully"
    Write-Host "  .\stop-platform.ps1 -Force       # Force stop all processes"
    Write-Host "  .\stop-platform.ps1 -KeepDatabase # Stop services but keep database"
    exit 0
}

Write-Host "🛑 Stopping TecnoDrive Platform..." -ForegroundColor Red

# Function to check if port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $Port -WarningAction SilentlyContinue
        return $connection.TcpTestSucceeded
    } catch {
        return $false
    }
}

# Function to stop process by port
function Stop-ProcessByPort {
    param([int]$Port, [string]$ServiceName)
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    Get-Process -Id { $_ } -ErrorAction SilentlyContinue
        
        if ($processes) {
            foreach ($process in $processes) {
                Write-Host "Stopping $ServiceName (PID: $($process.Id))..." -ForegroundColor Yellow
                if ($Force) {
                    Stop-Process -Id $process.Id -Force
                } else {
                    Stop-Process -Id $process.Id
                }
                Write-Host "✅ $ServiceName stopped" -ForegroundColor Green
            }
        } else {
            Write-Host "ℹ️ $ServiceName not running" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️ Could not stop $ServiceName`: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 1. Stop Frontend Applications
Write-Host "`n🌐 Stopping Frontend Applications..." -ForegroundColor Cyan

# Stop Admin Dashboard (port 3000)
if (Test-Port -Port 3000) {
    Stop-ProcessByPort -Port 3000 -ServiceName "Admin Dashboard"
}

# Stop other frontend apps if running
$frontendPorts = @(3001, 3002, 3003)
foreach ($port in $frontendPorts) {
    if (Test-Port -Port $port) {
        Stop-ProcessByPort -Port $port -ServiceName "Frontend App (Port $port)"
    }
}

# 2. Stop Comprehensive Python System
Write-Host "`n🐍 Stopping Comprehensive Python System..." -ForegroundColor Cyan

if (Test-Port -Port 8000) {
    Stop-ProcessByPort -Port 8000 -ServiceName "Comprehensive System"
}

# 3. Stop Java Microservices
Write-Host "`n🔧 Stopping Java Microservices..." -ForegroundColor Cyan

$microservices = @(
    @{Name="SaaS Management"; Port=8092},
    @{Name="Financial Service"; Port=8091},
    @{Name="HR Service"; Port=8090},
    @{Name="Analytics Service"; Port=8089},
    @{Name="Notification Service"; Port=8088},
    @{Name="Parcel Service"; Port=8087},
    @{Name="Payment Service"; Port=8086},
    @{Name="Location Service"; Port=8085},
    @{Name="Fleet Service"; Port=8084},
    @{Name="User Service"; Port=8083},
    @{Name="Ride Service"; Port=8082},
    @{Name="Auth Service"; Port=8081},
    @{Name="API Gateway"; Port=8080},
    @{Name="Eureka Server"; Port=8761}
)

foreach ($service in $microservices) {
    if (Test-Port -Port $service.Port) {
        Stop-ProcessByPort -Port $service.Port -ServiceName $service.Name
    }
}

# 4. Stop Database Services (if not keeping them)
if (-not $KeepDatabase) {
    Write-Host "`n📊 Stopping Database Services..." -ForegroundColor Cyan
    
    # Stop Docker containers
    try {
        $containers = docker ps --format "table {{.Names}}" | Where-Object { $_ -match "postgres|redis|kafka|zookeeper|flink" }
        if ($containers) {
            Write-Host "Stopping Docker containers..." -ForegroundColor Yellow
            docker stop $containers
            Write-Host "✅ Docker containers stopped" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Could not stop Docker containers: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    # Stop any remaining database processes
    if (Test-Port -Port 5433) {
        Stop-ProcessByPort -Port 5433 -ServiceName "PostgreSQL"
    }
    
    if (Test-Port -Port 6379) {
        Stop-ProcessByPort -Port 6379 -ServiceName "Redis"
    }
} else {
    Write-Host "`n📊 Keeping Database Services running..." -ForegroundColor Yellow
}

# 5. Clean up Java processes
Write-Host "`n☕ Cleaning up Java processes..." -ForegroundColor Cyan

try {
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | 
                    Where-Object { $_.CommandLine -like "*spring-boot*" -or $_.CommandLine -like "*maven*" }
    
    if ($javaProcesses) {
        foreach ($process in $javaProcesses) {
            Write-Host "Stopping Java process (PID: $($process.Id))..." -ForegroundColor Yellow
            if ($Force) {
                Stop-Process -Id $process.Id -Force
            } else {
                Stop-Process -Id $process.Id
            }
        }
        Write-Host "✅ Java processes cleaned up" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ No Java processes to clean up" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ Could not clean up Java processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 6. Clean up Node.js processes
Write-Host "`n📦 Cleaning up Node.js processes..." -ForegroundColor Cyan

try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | 
                    Where-Object { $_.CommandLine -like "*react-scripts*" -or $_.CommandLine -like "*npm*" }
    
    if ($nodeProcesses) {
        foreach ($process in $nodeProcesses) {
            Write-Host "Stopping Node.js process (PID: $($process.Id))..." -ForegroundColor Yellow
            if ($Force) {
                Stop-Process -Id $process.Id -Force
            } else {
                Stop-Process -Id $process.Id
            }
        }
        Write-Host "✅ Node.js processes cleaned up" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ No Node.js processes to clean up" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ Could not clean up Node.js processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 7. Clean up Python processes
Write-Host "`n🐍 Cleaning up Python processes..." -ForegroundColor Cyan

try {
    $pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue | 
                      Where-Object { $_.CommandLine -like "*main.py*" -or $_.CommandLine -like "*fastapi*" }
    
    if ($pythonProcesses) {
        foreach ($process in $pythonProcesses) {
            Write-Host "Stopping Python process (PID: $($process.Id))..." -ForegroundColor Yellow
            if ($Force) {
                Stop-Process -Id $process.Id -Force
            } else {
                Stop-Process -Id $process.Id
            }
        }
        Write-Host "✅ Python processes cleaned up" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ No Python processes to clean up" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ Could not clean up Python processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 8. Final Status Check
Write-Host "`n📋 Final Status Check:" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

$checkPorts = @(8761, 8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089, 8090, 8091, 8092, 8000, 3000)

if (-not $KeepDatabase) {
    $checkPorts += @(5433, 6379)
}

$stillRunning = @()
foreach ($port in $checkPorts) {
    if (Test-Port -Port $port) {
        $stillRunning += $port
    }
}

if ($stillRunning.Count -eq 0) {
    Write-Host "✅ All services stopped successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some services are still running on ports: $($stillRunning -join ', ')" -ForegroundColor Yellow
    if (-not $Force) {
        Write-Host "💡 Use -Force parameter to forcefully stop remaining processes" -ForegroundColor Cyan
    }
}

if ($KeepDatabase) {
    Write-Host "📊 Database services kept running as requested" -ForegroundColor Yellow
}

Write-Host "`n🎉 TecnoDrive Platform shutdown completed!" -ForegroundColor Green
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
