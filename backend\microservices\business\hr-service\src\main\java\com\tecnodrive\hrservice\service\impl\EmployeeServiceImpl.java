package com.tecnodrive.hrservice.service.impl;

import com.tecnodrive.hrservice.dto.EmployeeRequest;
import com.tecnodrive.hrservice.dto.EmployeeResponse;
import com.tecnodrive.hrservice.entity.Employee;
import com.tecnodrive.hrservice.exception.EmployeeNotFoundException;
import com.tecnodrive.hrservice.repository.EmployeeRepository;
import com.tecnodrive.hrservice.service.EmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Employee Service Implementation
 * 
 * Implements business logic for employee management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class EmployeeServiceImpl implements EmployeeService {

    private final EmployeeRepository employeeRepository;

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse createEmployee(EmployeeRequest request) {
        log.info("Creating employee with email: {}", request.getEmail());
        
        validateEmployeeRequest(request);
        
        // Check if email already exists
        if (!isEmailAvailable(request.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + request.getEmail());
        }
        
        // Check if employee number already exists (if provided)
        if (request.getEmployeeNumber() != null && !isEmployeeNumberAvailable(request.getEmployeeNumber())) {
            throw new IllegalArgumentException("Employee number already exists: " + request.getEmployeeNumber());
        }
        
        // Generate employee number if not provided
        if (request.getEmployeeNumber() == null || request.getEmployeeNumber().trim().isEmpty()) {
            request.setEmployeeNumber(generateEmployeeNumber(request.getCompanyId()));
        }
        
        Employee employee = mapToEntity(request);
        employee = employeeRepository.save(employee);
        
        log.info("Employee created successfully with ID: {}", employee.getId());
        return mapToResponse(employee);
    }

    @Override
    @Cacheable(value = "employees", key = "#id")
    public EmployeeResponse getEmployee(String id) {
        log.debug("Getting employee by ID: {}", id);
        
        Employee employee = employeeRepository.findById(UUID.fromString(id))
                .orElseThrow(() -> new EmployeeNotFoundException(id));
        
        return mapToResponse(employee);
    }

    @Override
    @Cacheable(value = "employees", key = "#email")
    public EmployeeResponse getEmployeeByEmail(String email) {
        log.debug("Getting employee by email: {}", email);
        
        Employee employee = employeeRepository.findByEmail(email)
                .orElseThrow(() -> new EmployeeNotFoundException("Employee not found with email: " + email));
        
        return mapToResponse(employee);
    }

    @Override
    @Cacheable(value = "employees", key = "#employeeNumber")
    public EmployeeResponse getEmployeeByEmployeeNumber(String employeeNumber) {
        log.debug("Getting employee by employee number: {}", employeeNumber);
        
        Employee employee = employeeRepository.findByEmployeeNumber(employeeNumber)
                .orElseThrow(() -> new EmployeeNotFoundException("Employee not found with employee number: " + employeeNumber));
        
        return mapToResponse(employee);
    }

    @Override
    @Cacheable(value = "employees", key = "'all'")
    public List<EmployeeResponse> getAllEmployees() {
        log.debug("Getting all employees");
        
        return employeeRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<EmployeeResponse> getEmployees(Pageable pageable) {
        log.debug("Getting employees with pagination: {}", pageable);
        
        return employeeRepository.findAll(pageable)
                .map(this::mapToResponse);
    }

    @Override
    @Cacheable(value = "employees", key = "'company_' + #companyId")
    public List<EmployeeResponse> getEmployeesByCompany(String companyId) {
        log.debug("Getting employees by company: {}", companyId);
        
        return employeeRepository.findByCompanyId(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<EmployeeResponse> getEmployeesByCompany(String companyId, Pageable pageable) {
        log.debug("Getting employees by company: {} with pagination: {}", companyId, pageable);
        
        return employeeRepository.findByCompanyId(companyId, pageable)
                .map(this::mapToResponse);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse updateEmployee(String id, EmployeeRequest request) {
        log.info("Updating employee with ID: {}", id);
        
        Employee employee = employeeRepository.findById(UUID.fromString(id))
                .orElseThrow(() -> new EmployeeNotFoundException(id));
        
        validateEmployeeRequest(request);
        
        // Check if email is being changed and if it's available
        if (!employee.getEmail().equals(request.getEmail()) && 
            !isEmailAvailable(request.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + request.getEmail());
        }
        
        // Check if employee number is being changed and if it's available
        if (request.getEmployeeNumber() != null && 
            !request.getEmployeeNumber().equals(employee.getEmployeeNumber()) && 
            !isEmployeeNumberAvailable(request.getEmployeeNumber())) {
            throw new IllegalArgumentException("Employee number already exists: " + request.getEmployeeNumber());
        }
        
        updateEntityFromRequest(employee, request);
        employee = employeeRepository.save(employee);
        
        log.info("Employee updated successfully with ID: {}", employee.getId());
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public void deleteEmployee(String id) {
        log.info("Deleting employee with ID: {}", id);
        
        UUID employeeId = UUID.fromString(id);
        if (!employeeRepository.existsById(employeeId)) {
            throw new EmployeeNotFoundException(id);
        }
        
        employeeRepository.deleteById(employeeId);
        log.info("Employee deleted successfully with ID: {}", id);
    }

    @Override
    public List<EmployeeResponse> getEmployeesByStatus(Employee.EmployeeStatus status) {
        log.debug("Getting employees by status: {}", status);
        
        return employeeRepository.findByStatus(status).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesByCompanyAndStatus(String companyId, Employee.EmployeeStatus status) {
        log.debug("Getting employees by company: {} and status: {}", companyId, status);
        
        return employeeRepository.findByCompanyIdAndStatus(companyId, status).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesByDepartment(String companyId, String department) {
        log.debug("Getting employees by company: {} and department: {}", companyId, department);
        
        return employeeRepository.findByCompanyIdAndDepartment(companyId, department).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesByPosition(String position) {
        log.debug("Getting employees by position: {}", position);
        
        return employeeRepository.findByPosition(position).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesByManager(String managerId) {
        log.debug("Getting employees by manager: {}", managerId);
        
        return employeeRepository.findByManagerId(managerId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "employees", key = "'active'")
    public List<EmployeeResponse> getActiveEmployees() {
        log.debug("Getting active employees");
        
        return employeeRepository.findByIsActiveTrue().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "employees", key = "'active_' + #companyId")
    public List<EmployeeResponse> getActiveEmployeesByCompany(String companyId) {
        log.debug("Getting active employees by company: {}", companyId);
        
        return employeeRepository.findByCompanyIdAndIsActiveTrue(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    // Helper methods for mapping
    private Employee mapToEntity(EmployeeRequest request) {
        return Employee.builder()
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .email(request.getEmail())
                .employeeNumber(request.getEmployeeNumber())
                .phoneNumber(request.getPhoneNumber())
                .dateOfBirth(request.getDateOfBirth())
                .gender(request.getGender())
                .address(request.getAddress())
                .position(request.getPosition())
                .department(request.getDepartment())
                .status(request.getStatus() != null ? request.getStatus() : Employee.EmployeeStatus.ACTIVE)
                .employmentType(request.getEmploymentType())
                .hireDate(request.getHireDate())
                .terminationDate(request.getTerminationDate())
                .probationEndDate(request.getProbationEndDate())
                .salary(request.getSalary())
                .hourlyRate(request.getHourlyRate())
                .payFrequency(request.getPayFrequency())
                .currency(request.getCurrency() != null ? request.getCurrency() : "RY")
                .managerId(request.getManagerId())
                .managerName(request.getManagerName())
                .annualLeaveDays(request.getAnnualLeaveDays() != null ? request.getAnnualLeaveDays() : 21)
                .sickLeaveDays(request.getSickLeaveDays() != null ? request.getSickLeaveDays() : 10)
                .usedAnnualLeave(request.getUsedAnnualLeave() != null ? request.getUsedAnnualLeave() : 0)
                .usedSickLeave(request.getUsedSickLeave() != null ? request.getUsedSickLeave() : 0)
                .lastPerformanceReview(request.getLastPerformanceReview())
                .nextPerformanceReview(request.getNextPerformanceReview())
                .performanceRating(request.getPerformanceRating())
                .performanceNotes(request.getPerformanceNotes())
                .emergencyContactName(request.getEmergencyContactName())
                .emergencyContactPhone(request.getEmergencyContactPhone())
                .emergencyContactRelation(request.getEmergencyContactRelation())
                .companyId(request.getCompanyId())
                .notes(request.getNotes())
                .skills(request.getSkills())
                .certifications(request.getCertifications())
                .education(request.getEducation())
                .isActive(request.getIsActive() != null ? request.getIsActive() : true)
                .build();
    }

    private void updateEntityFromRequest(Employee employee, EmployeeRequest request) {
        employee.setFirstName(request.getFirstName());
        employee.setLastName(request.getLastName());
        employee.setEmail(request.getEmail());
        employee.setEmployeeNumber(request.getEmployeeNumber());
        employee.setPhoneNumber(request.getPhoneNumber());
        employee.setDateOfBirth(request.getDateOfBirth());
        employee.setGender(request.getGender());
        employee.setAddress(request.getAddress());
        employee.setPosition(request.getPosition());
        employee.setDepartment(request.getDepartment());
        
        if (request.getStatus() != null) {
            employee.setStatus(request.getStatus());
        }
        
        employee.setEmploymentType(request.getEmploymentType());
        employee.setHireDate(request.getHireDate());
        employee.setTerminationDate(request.getTerminationDate());
        employee.setProbationEndDate(request.getProbationEndDate());
        employee.setSalary(request.getSalary());
        employee.setHourlyRate(request.getHourlyRate());
        employee.setPayFrequency(request.getPayFrequency());
        
        if (request.getCurrency() != null) {
            employee.setCurrency(request.getCurrency());
        }
        
        employee.setManagerId(request.getManagerId());
        employee.setManagerName(request.getManagerName());
        
        if (request.getAnnualLeaveDays() != null) {
            employee.setAnnualLeaveDays(request.getAnnualLeaveDays());
        }
        
        if (request.getSickLeaveDays() != null) {
            employee.setSickLeaveDays(request.getSickLeaveDays());
        }
        
        if (request.getUsedAnnualLeave() != null) {
            employee.setUsedAnnualLeave(request.getUsedAnnualLeave());
        }
        
        if (request.getUsedSickLeave() != null) {
            employee.setUsedSickLeave(request.getUsedSickLeave());
        }
        
        employee.setLastPerformanceReview(request.getLastPerformanceReview());
        employee.setNextPerformanceReview(request.getNextPerformanceReview());
        employee.setPerformanceRating(request.getPerformanceRating());
        employee.setPerformanceNotes(request.getPerformanceNotes());
        employee.setEmergencyContactName(request.getEmergencyContactName());
        employee.setEmergencyContactPhone(request.getEmergencyContactPhone());
        employee.setEmergencyContactRelation(request.getEmergencyContactRelation());
        employee.setNotes(request.getNotes());
        employee.setSkills(request.getSkills());
        employee.setCertifications(request.getCertifications());
        employee.setEducation(request.getEducation());
        
        if (request.getIsActive() != null) {
            employee.setActive(request.getIsActive());
        }
    }

    private EmployeeResponse mapToResponse(Employee employee) {
        return EmployeeResponse.builder()
                .id(employee.getId().toString())
                .firstName(employee.getFirstName())
                .lastName(employee.getLastName())
                .fullName(employee.getFullName())
                .email(employee.getEmail())
                .employeeNumber(employee.getEmployeeNumber())
                .phoneNumber(employee.getPhoneNumber())
                .dateOfBirth(employee.getDateOfBirth())
                .gender(employee.getGender())
                .address(employee.getAddress())
                .position(employee.getPosition())
                .department(employee.getDepartment())
                .status(employee.getStatus())
                .employmentType(employee.getEmploymentType())
                .hireDate(employee.getHireDate())
                .terminationDate(employee.getTerminationDate())
                .probationEndDate(employee.getProbationEndDate())
                .salary(employee.getSalary())
                .hourlyRate(employee.getHourlyRate())
                .payFrequency(employee.getPayFrequency())
                .currency(employee.getCurrency())
                .monthlySalary(employee.getMonthlySalary())
                .annualSalary(employee.getAnnualSalary())
                .managerId(employee.getManagerId())
                .managerName(employee.getManagerName())
                .annualLeaveDays(employee.getAnnualLeaveDays())
                .sickLeaveDays(employee.getSickLeaveDays())
                .usedAnnualLeave(employee.getUsedAnnualLeave())
                .usedSickLeave(employee.getUsedSickLeave())
                .remainingAnnualLeave(employee.getRemainingAnnualLeave())
                .remainingSickLeave(employee.getRemainingSickLeave())
                .lastPerformanceReview(employee.getLastPerformanceReview())
                .nextPerformanceReview(employee.getNextPerformanceReview())
                .performanceRating(employee.getPerformanceRating())
                .performanceNotes(employee.getPerformanceNotes())
                .emergencyContactName(employee.getEmergencyContactName())
                .emergencyContactPhone(employee.getEmergencyContactPhone())
                .emergencyContactRelation(employee.getEmergencyContactRelation())
                .companyId(employee.getCompanyId())
                .notes(employee.getNotes())
                .skills(employee.getSkills())
                .certifications(employee.getCertifications())
                .education(employee.getEducation())
                .isActive(employee.isActive())
                .createdAt(employee.getCreatedAt())
                .updatedAt(employee.getUpdatedAt())
                .onProbation(employee.isOnProbation())
                .performanceReviewDue(employee.isPerformanceReviewDue())
                .yearsOfService(employee.getYearsOfService())
                .eligibleForBenefits(employee.isEligibleForBenefits())
                .build();
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse updateEmployeeStatus(String employeeId, Employee.EmployeeStatus status) {
        log.info("Updating employee {} status to {}", employeeId, status);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setStatus(status);
        employee = employeeRepository.save(employee);

        log.info("Employee status updated successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse assignManager(String employeeId, String managerId) {
        log.info("Assigning manager {} to employee {}", managerId, employeeId);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        // Get manager details
        Employee manager = employeeRepository.findById(UUID.fromString(managerId))
                .orElseThrow(() -> new EmployeeNotFoundException("Manager not found: " + managerId));

        employee.setManagerId(managerId);
        employee.setManagerName(manager.getFullName());
        employee = employeeRepository.save(employee);

        log.info("Manager assigned successfully to employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse updateEmployeeSalary(String employeeId, BigDecimal salary) {
        log.info("Updating salary for employee {} to {}", employeeId, salary);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setSalary(salary);
        employee = employeeRepository.save(employee);

        log.info("Salary updated successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse updatePerformanceRating(String employeeId, BigDecimal rating, String notes) {
        log.info("Updating performance rating for employee {} to {}", employeeId, rating);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setPerformanceRating(rating);
        employee.setPerformanceNotes(notes);
        employee.setLastPerformanceReview(LocalDate.now());
        employee = employeeRepository.save(employee);

        log.info("Performance rating updated successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse schedulePerformanceReview(String employeeId, LocalDate reviewDate) {
        log.info("Scheduling performance review for employee {} on {}", employeeId, reviewDate);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setNextPerformanceReview(reviewDate);
        employee = employeeRepository.save(employee);

        log.info("Performance review scheduled successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse completePerformanceReview(String employeeId, BigDecimal rating, String notes, LocalDate nextReviewDate) {
        log.info("Completing performance review for employee {}", employeeId);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setPerformanceRating(rating);
        employee.setPerformanceNotes(notes);
        employee.setLastPerformanceReview(LocalDate.now());
        employee.setNextPerformanceReview(nextReviewDate);
        employee = employeeRepository.save(employee);

        log.info("Performance review completed successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    public List<EmployeeResponse> getEmployeesOnProbation() {
        log.debug("Getting employees on probation");

        return employeeRepository.findEmployeesOnProbation(LocalDate.now()).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesWithDuePerformanceReviews() {
        log.debug("Getting employees with due performance reviews");

        return employeeRepository.findEmployeesWithDuePerformanceReviews(LocalDate.now()).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesByEmploymentType(String companyId, Employee.EmploymentType employmentType) {
        log.debug("Getting employees by company: {} and employment type: {}", companyId, employmentType);

        return employeeRepository.findByCompanyIdAndEmploymentType(companyId, employmentType).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<EmployeeResponse> searchEmployees(String searchTerm, Pageable pageable) {
        log.debug("Searching employees with term: {}", searchTerm);

        return employeeRepository.searchEmployees(searchTerm, pageable)
                .map(this::mapToResponse);
    }

    @Override
    public Page<EmployeeResponse> searchEmployeesByCompany(String companyId, String searchTerm, Pageable pageable) {
        log.debug("Searching employees by company: {} with term: {}", companyId, searchTerm);

        return employeeRepository.searchEmployeesByCompany(companyId, searchTerm, pageable)
                .map(this::mapToResponse);
    }

    @Override
    @Cacheable(value = "employeeStats", key = "#companyId")
    public EmployeeResponse.EmployeeStatistics getEmployeeStatistics(String companyId) {
        log.debug("Getting employee statistics for company: {}", companyId);

        long totalEmployees = employeeRepository.countByCompanyId(companyId);
        long activeEmployees = employeeRepository.countByCompanyIdAndStatus(companyId, Employee.EmployeeStatus.ACTIVE);
        long inactiveEmployees = employeeRepository.countByCompanyIdAndStatus(companyId, Employee.EmployeeStatus.INACTIVE);
        long employeesOnProbation = employeeRepository.findEmployeesOnProbation(LocalDate.now()).stream()
                .filter(e -> companyId.equals(e.getCompanyId()))
                .count();
        long employeesOnLeave = employeeRepository.countByCompanyIdAndStatus(companyId, Employee.EmployeeStatus.ON_LEAVE);
        long terminatedEmployees = employeeRepository.countByCompanyIdAndStatus(companyId, Employee.EmployeeStatus.TERMINATED);

        Double averageYearsOfService = employeeRepository.calculateAverageYearsOfServiceByCompany(companyId);
        BigDecimal averageSalary = employeeRepository.calculateAverageSalaryByCompany(companyId);
        BigDecimal totalPayroll = employeeRepository.calculateTotalPayrollByCompany(companyId);

        long employeesWithDueReviews = employeeRepository.findEmployeesWithDuePerformanceReviews(LocalDate.now()).stream()
                .filter(e -> companyId.equals(e.getCompanyId()))
                .count();

        long fullTimeEmployees = employeeRepository.countByCompanyIdAndStatus(companyId, Employee.EmployeeStatus.ACTIVE);
        long partTimeEmployees = employeeRepository.findByCompanyIdAndEmploymentType(companyId, Employee.EmploymentType.PART_TIME).size();
        long contractEmployees = employeeRepository.findByCompanyIdAndEmploymentType(companyId, Employee.EmploymentType.CONTRACT).size();

        return EmployeeResponse.EmployeeStatistics.builder()
                .totalEmployees(totalEmployees)
                .activeEmployees(activeEmployees)
                .inactiveEmployees(inactiveEmployees)
                .employeesOnProbation(employeesOnProbation)
                .employeesOnLeave(employeesOnLeave)
                .terminatedEmployees(terminatedEmployees)
                .averageYearsOfService(averageYearsOfService != null ? averageYearsOfService : 0.0)
                .averageSalary(averageSalary != null ? averageSalary : BigDecimal.ZERO)
                .totalPayroll(totalPayroll != null ? totalPayroll : BigDecimal.ZERO)
                .employeesWithDueReviews(employeesWithDueReviews)
                .fullTimeEmployees(fullTimeEmployees)
                .partTimeEmployees(partTimeEmployees)
                .contractEmployees(contractEmployees)
                .build();
    }

    @Override
    public List<EmployeeResponse.EmployeeSummary> getEmployeeSummaries(String companyId) {
        log.debug("Getting employee summaries for company: {}", companyId);

        return employeeRepository.findByCompanyId(companyId).stream()
                .map(this::mapToSummary)
                .collect(Collectors.toList());
    }

    private EmployeeResponse.EmployeeSummary mapToSummary(Employee employee) {
        return EmployeeResponse.EmployeeSummary.builder()
                .id(employee.getId().toString())
                .fullName(employee.getFullName())
                .email(employee.getEmail())
                .employeeNumber(employee.getEmployeeNumber())
                .position(employee.getPosition())
                .department(employee.getDepartment())
                .status(employee.getStatus())
                .employmentType(employee.getEmploymentType())
                .hireDate(employee.getHireDate())
                .salary(employee.getSalary())
                .onProbation(employee.isOnProbation())
                .performanceReviewDue(employee.isPerformanceReviewDue())
                .yearsOfService(employee.getYearsOfService())
                .build();
    }

    @Override
    public List<EmployeeResponse.DepartmentStatistics> getDepartmentStatistics(String companyId) {
        log.debug("Getting department statistics for company: {}", companyId);

        List<Object[]> results = employeeRepository.getDepartmentStatistics(companyId);
        return results.stream()
                .map(result -> EmployeeResponse.DepartmentStatistics.builder()
                        .department((String) result[0])
                        .employeeCount((Long) result[1])
                        .averageSalary((BigDecimal) result[2])
                        .totalSalary((BigDecimal) result[3])
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesWithBirthdaysInRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Getting employees with birthdays between {} and {}", startDate, endDate);

        return employeeRepository.findEmployeesWithBirthdaysInRange(startDate, endDate).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesWithAnniversariesInRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Getting employees with anniversaries between {} and {}", startDate, endDate);

        return employeeRepository.findEmployeesWithAnniversariesInRange(startDate, endDate).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesBySalaryRange(BigDecimal minSalary, BigDecimal maxSalary) {
        log.debug("Getting employees with salary between {} and {}", minSalary, maxSalary);

        return employeeRepository.findBySalaryRange(minSalary, maxSalary).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse> getEmployeesEligibleForBenefits() {
        log.debug("Getting employees eligible for benefits");

        return employeeRepository.findEmployeesEligibleForBenefits(LocalDate.now()).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeResponse.PayrollSummary> getPayrollSummary(String companyId, Employee.PayFrequency payFrequency) {
        log.debug("Getting payroll summary for company: {} and pay frequency: {}", companyId, payFrequency);

        return employeeRepository.findByCompanyIdAndPayFrequency(companyId, payFrequency).stream()
                .map(employee -> EmployeeResponse.PayrollSummary.builder()
                        .employeeId(employee.getId().toString())
                        .fullName(employee.getFullName())
                        .employeeNumber(employee.getEmployeeNumber())
                        .department(employee.getDepartment())
                        .baseSalary(employee.getSalary())
                        .overtimePay(BigDecimal.ZERO) // Would be calculated based on time tracking
                        .bonuses(BigDecimal.ZERO) // Would be calculated based on bonus records
                        .deductions(BigDecimal.ZERO) // Would be calculated based on deduction records
                        .netPay(employee.getSalary()) // Simplified calculation
                        .payFrequency(employee.getPayFrequency())
                        .currency(employee.getCurrency())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse updateLeaveBalance(String employeeId, Integer usedAnnualLeave, Integer usedSickLeave) {
        log.info("Updating leave balance for employee {}", employeeId);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        if (usedAnnualLeave != null) {
            employee.setUsedAnnualLeave(usedAnnualLeave);
        }

        if (usedSickLeave != null) {
            employee.setUsedSickLeave(usedSickLeave);
        }

        employee = employeeRepository.save(employee);

        log.info("Leave balance updated successfully for employee {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse terminateEmployee(String employeeId, LocalDate terminationDate, String reason) {
        log.info("Terminating employee {} on {}", employeeId, terminationDate);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setStatus(Employee.EmployeeStatus.TERMINATED);
        employee.setTerminationDate(terminationDate);
        employee.setActive(false);

        if (reason != null) {
            String currentNotes = employee.getNotes() != null ? employee.getNotes() : "";
            employee.setNotes(currentNotes + "\nTermination reason: " + reason);
        }

        employee = employeeRepository.save(employee);

        log.info("Employee terminated successfully: {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    @CacheEvict(value = "employees", allEntries = true)
    public EmployeeResponse reactivateEmployee(String employeeId) {
        log.info("Reactivating employee {}", employeeId);

        Employee employee = employeeRepository.findById(UUID.fromString(employeeId))
                .orElseThrow(() -> new EmployeeNotFoundException(employeeId));

        employee.setStatus(Employee.EmployeeStatus.ACTIVE);
        employee.setTerminationDate(null);
        employee.setActive(true);

        employee = employeeRepository.save(employee);

        log.info("Employee reactivated successfully: {}", employeeId);
        return mapToResponse(employee);
    }

    @Override
    public void validateEmployeeRequest(EmployeeRequest request) {
        if (request.getFirstName() == null || request.getFirstName().trim().isEmpty()) {
            throw new IllegalArgumentException("First name is required");
        }

        if (request.getLastName() == null || request.getLastName().trim().isEmpty()) {
            throw new IllegalArgumentException("Last name is required");
        }

        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email is required");
        }

        if (request.getPosition() == null || request.getPosition().trim().isEmpty()) {
            throw new IllegalArgumentException("Position is required");
        }

        if (request.getDepartment() == null || request.getDepartment().trim().isEmpty()) {
            throw new IllegalArgumentException("Department is required");
        }

        if (request.getHireDate() == null) {
            throw new IllegalArgumentException("Hire date is required");
        }

        if (request.getEmploymentType() == null) {
            throw new IllegalArgumentException("Employment type is required");
        }

        if (request.getCompanyId() == null || request.getCompanyId().trim().isEmpty()) {
            throw new IllegalArgumentException("Company ID is required");
        }
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return !employeeRepository.existsByEmail(email);
    }

    @Override
    public boolean isEmployeeNumberAvailable(String employeeNumber) {
        return employeeNumber == null || !employeeRepository.existsByEmployeeNumber(employeeNumber);
    }

    @Override
    public String generateEmployeeNumber(String companyId) {
        // Simple employee number generation - can be enhanced based on business rules
        long count = employeeRepository.countByCompanyId(companyId);
        return String.format("EMP%s%04d", companyId.substring(0, Math.min(3, companyId.length())).toUpperCase(), count + 1);
    }
}
