import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Alert, Box, Typography, LinearProgress, Chip } from '@mui/material';
import { Speed, Warning, Block } from '@mui/icons-material';

interface RateLimiterProps {
  children: React.ReactNode;
  maxRequests: number;
  windowMs: number;
  onLimitExceeded?: () => void;
  showStatus?: boolean;
  blockOnLimit?: boolean;
}

interface RequestRecord {
  timestamp: number;
  count: number;
}

const RateLimiter: React.FC<RateLimiterProps> = ({
  children,
  maxRequests,
  windowMs,
  onLimitExceeded,
  showStatus = false,
  blockOnLimit = true,
}) => {
  const [requestCount, setRequestCount] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockEndTime, setBlockEndTime] = useState(0);
  const requestHistory = useRef<RequestRecord[]>([]);
  const lastCleanup = useRef<number>(Date.now());

  const cleanupOldRequests = useCallback(() => {
    const now = Date.now();
    const cutoff = now - windowMs;
    
    requestHistory.current = requestHistory.current.filter(
      record => record.timestamp > cutoff
    );
    
    const currentCount = requestHistory.current.reduce(
      (sum, record) => sum + record.count, 0
    );
    
    setRequestCount(currentCount);
    lastCleanup.current = now;
  }, [windowMs]);

  const addRequest = useCallback(() => {
    const now = Date.now();
    
    // Cleanup old requests if needed
    if (now - lastCleanup.current > windowMs / 4) {
      cleanupOldRequests();
    }
    
    // Add new request
    requestHistory.current.push({
      timestamp: now,
      count: 1,
    });
    
    const newCount = requestCount + 1;
    setRequestCount(newCount);
    
    // Check if limit exceeded
    if (newCount > maxRequests) {
      if (blockOnLimit) {
        setIsBlocked(true);
        setBlockEndTime(now + windowMs);
      }
      
      if (onLimitExceeded) {
        onLimitExceeded();
      }
      
      return false; // Request blocked
    }
    
    return true; // Request allowed
  }, [requestCount, maxRequests, windowMs, blockOnLimit, onLimitExceeded, cleanupOldRequests]);

  const checkBlockStatus = useCallback(() => {
    const now = Date.now();
    if (isBlocked && now >= blockEndTime) {
      setIsBlocked(false);
      setBlockEndTime(0);
      cleanupOldRequests();
    }
  }, [isBlocked, blockEndTime, cleanupOldRequests]);

  const getRemainingTime = () => {
    if (!isBlocked) return 0;
    return Math.max(0, Math.ceil((blockEndTime - Date.now()) / 1000));
  };

  const getUsagePercentage = () => {
    return Math.min(100, (requestCount / maxRequests) * 100);
  };

  const getStatusColor = () => {
    const percentage = getUsagePercentage();
    if (percentage >= 100) return 'error';
    if (percentage >= 80) return 'warning';
    if (percentage >= 60) return 'info';
    return 'success';
  };

  const getStatusSeverity = () => {
    const percentage = getUsagePercentage();
    if (percentage >= 100) return 'error';
    if (percentage >= 80) return 'warning';
    return 'info';
  };

  // Periodic cleanup and block status check
  useEffect(() => {
    const interval = setInterval(() => {
      cleanupOldRequests();
      checkBlockStatus();
    }, 1000);

    return () => clearInterval(interval);
  }, [cleanupOldRequests, checkBlockStatus]);

  // Intercept API calls and apply rate limiting
  useEffect(() => {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      if (!addRequest()) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }
      
      return originalFetch(...args);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, [addRequest]);

  const StatusDisplay = () => {
    if (!showStatus) return null;

    const percentage = getUsagePercentage();
    const remainingTime = getRemainingTime();

    return (
      <Box mb={2}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Speed color={getStatusColor() as any} />
            <Typography variant="body2">
              Rate Limit Status
            </Typography>
          </Box>
          <Chip
            label={`${requestCount}/${maxRequests}`}
            color={getStatusColor() as any}
            size="small"
          />
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={percentage}
          color={getStatusColor() as any}
          sx={{ mb: 1 }}
        />
        
        <Typography variant="caption" color="textSecondary">
          Window: {Math.round(windowMs / 1000)}s | 
          Usage: {percentage.toFixed(1)}%
          {remainingTime > 0 && ` | Blocked for ${remainingTime}s`}
        </Typography>
      </Box>
    );
  };

  if (isBlocked) {
    const remainingTime = getRemainingTime();
    
    return (
      <Box>
        <StatusDisplay />
        <Alert 
          severity="error" 
          icon={<Block />}
        >
          <Typography variant="h6" gutterBottom>
            Rate Limit Exceeded
          </Typography>
          <Typography variant="body2" gutterBottom>
            Too many requests in a short period. Please wait before trying again.
          </Typography>
          <Typography variant="body2">
            {remainingTime > 0 
              ? `Access will be restored in ${remainingTime} seconds.`
              : 'Access is being restored...'
            }
          </Typography>
          {remainingTime > 0 && (
            <Box mt={2}>
              <LinearProgress 
                variant="determinate" 
                value={((windowMs - (blockEndTime - Date.now())) / windowMs) * 100}
                color="error"
              />
            </Box>
          )}
        </Alert>
      </Box>
    );
  }

  const percentage = getUsagePercentage();
  
  return (
    <Box>
      <StatusDisplay />
      
      {percentage >= 80 && (
        <Alert 
          severity={getStatusSeverity()} 
          icon={<Warning />}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            {percentage >= 95 
              ? 'Rate limit almost reached. Slow down to avoid blocking.'
              : 'High request rate detected. Consider reducing frequency.'
            }
          </Typography>
        </Alert>
      )}
      
      {children}
    </Box>
  );
};

export default RateLimiter;
