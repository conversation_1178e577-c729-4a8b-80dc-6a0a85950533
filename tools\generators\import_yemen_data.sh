#!/bin/bash

echo "==================================================================="
echo "TECNODRIVE Platform - استيراد البيانات اليمنية إلى قاعدة البيانات"
echo "==================================================================="

echo ""
echo "🔄 بدء عملية استيراد البيانات اليمنية..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت"
        echo "يرجى تثبيت Python أولاً"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"

# التحقق من وجود ملف البيانات
if [ ! -f "import_to_database.py" ]; then
    echo "❌ خطأ: ملف import_to_database.py غير موجود"
    echo "تأكد من وجود الملف في نفس المجلد"
    exit 1
fi

echo "✅ تم العثور على ملف التحويل"

# تشغيل script Python لتحويل البيانات
echo ""
echo "🔧 جاري تحويل البيانات من JSON إلى SQL..."
$PYTHON_CMD import_to_database.py

if [ $? -ne 0 ]; then
    echo "❌ فشل في تحويل البيانات"
    exit 1
fi

echo "✅ تم تحويل البيانات بنجاح"

# التحقق من وجود ملف SQL المولد
if [ ! -f "yemen_data_import.sql" ]; then
    echo "❌ خطأ: لم يتم إنشاء ملف SQL"
    exit 1
fi

echo "✅ تم إنشاء ملف SQL"

# البحث عن PostgreSQL
echo ""
echo "🔍 جاري البحث عن PostgreSQL..."

if command -v psql &> /dev/null; then
    PSQL_CMD="psql"
    echo "✅ تم العثور على psql"
else
    echo "❌ خطأ: psql غير موجود في PATH"
    echo "يرجى تثبيت PostgreSQL client أولاً"
    exit 1
fi

echo ""
echo "📋 معلومات الاتصال بقاعدة البيانات:"
echo "   المضيف: localhost"
echo "   المنفذ: 5432"
echo "   المستخدم: postgres"
echo "   قاعدة البيانات: tecnodrive_main"
echo ""

# السؤال عن المتابعة
read -p "هل تريد المتابعة مع استيراد البيانات إلى قاعدة البيانات؟ (y/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "تم إلغاء العملية"
    exit 0
fi

# تشغيل استيراد البيانات
echo ""
echo "🚀 جاري استيراد البيانات إلى قاعدة البيانات..."
echo "سيتم طلب كلمة مرور PostgreSQL..."
echo ""

$PSQL_CMD -h localhost -p 5432 -U postgres -d tecnodrive_main -f yemen_data_import.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم استيراد البيانات اليمنية بنجاح!"
    echo ""
    echo "📊 البيانات المستوردة:"
    echo "   • الشركات والمؤسسات اليمنية"
    echo "   • المستخدمين والسائقين"
    echo "   • المركبات المسجلة"
    echo "   • الرحلات التجريبية"
    echo "   • الطرود والشحنات"
    echo ""
    echo "🎯 يمكنك الآن:"
    echo "   1. تشغيل تطبيق TECNODRIVE"
    echo "   2. اختبار الوظائف مع البيانات اليمنية"
    echo "   3. عرض البيانات في لوحة التحكم"
    echo ""
else
    echo ""
    echo "❌ فشل في استيراد البيانات"
    echo ""
    echo "🔧 تحقق من:"
    echo "   1. تشغيل PostgreSQL على المنفذ 5432"
    echo "   2. وجود قاعدة البيانات tecnodrive_main"
    echo "   3. صحة كلمة مرور المستخدم postgres"
    echo "   4. تطبيق schema قاعدة البيانات أولاً"
    echo ""
    echo "💡 لتطبيق schema قاعدة البيانات:"
    echo "   psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f ../services/database/tecnodrive_schema.sql"
    echo ""
fi

echo ""
read -p "اضغط Enter للمتابعة..."
