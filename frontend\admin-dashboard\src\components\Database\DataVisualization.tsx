import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
} from 'recharts';
import {
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as ChartIcon,
  TableChart as TableIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import databaseService from '../../services/databaseService';
import { QueryResult, DatabaseStats } from '../../types/api';

interface DataVisualizationProps {
  database: string;
  tableName: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`data-tabpanel-${index}`}
      aria-labelledby={`data-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const DataVisualization: React.FC<DataVisualizationProps> = ({ database, tableName }) => {
  const [data, setData] = useState<QueryResult | null>(null);
  const [stats, setStats] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [selectedChart, setSelectedChart] = useState<'bar' | 'pie' | 'line' | 'area'>('bar');
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);

  useEffect(() => {
    if (database && tableName) {
      loadData();
      generateStats();
    }
  }, [database, tableName]);

  const loadData = async () => {
    if (!database || !tableName) return;

    try {
      setLoading(true);
      const result = await databaseService.getTableData(database, tableName, 1, 100);
      setData(result);
      generateChartData(result);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateStats = async () => {
    if (!database || !tableName) return;

    try {
      // إحصائيات أساسية
      const countQuery = `SELECT COUNT(*) as total_rows FROM ${tableName}`;
      const countResult = await databaseService.executeQuery(database, countQuery);
      
      // إحصائيات متقدمة حسب نوع البيانات
      const statsQueries = [
        `SELECT COUNT(*) as total_records FROM ${tableName}`,
        `SELECT COUNT(DISTINCT *) as unique_records FROM ${tableName}`,
      ];

      const statsResults = await Promise.all(
        statsQueries.map(query => 
          databaseService.executeQuery(database, query).catch(() => null)
        )
      );

      const generatedStats = [
        {
          label: 'إجمالي السجلات',
          value: countResult.rows[0]?.[0] || 0,
          icon: <TableIcon />,
          color: 'primary'
        },
        {
          label: 'السجلات الفريدة',
          value: statsResults[1]?.rows[0]?.[0] || 0,
          icon: <TrendingUpIcon />,
          color: 'success'
        },
        {
          label: 'الأعمدة',
          value: data?.columns.length || 0,
          icon: <ChartIcon />,
          color: 'info'
        }
      ];

      setStats(generatedStats);
    } catch (error) {
      console.error('Error generating stats:', error);
    }
  };

  const generateChartData = (result: QueryResult) => {
    if (!result || result.rows.length === 0) return;

    // تحويل البيانات لتناسب الرسوم البيانية
    const processedData = result.rows.slice(0, 20).map((row, index) => {
      const item: any = { index: index + 1 };
      
      result.columns.forEach((column, colIndex) => {
        const value = row[colIndex];
        
        // تحويل القيم الرقمية
        if (typeof value === 'number') {
          item[column] = value;
        } else if (typeof value === 'string' && !isNaN(Number(value))) {
          item[column] = Number(value);
        } else {
          // للقيم النصية، نحسب التكرار
          item[column] = value?.toString().length || 0;
        }
      });
      
      return item;
    });

    setChartData(processedData);
  };

  const getChartColors = () => [
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
    '#ff0000', '#00ffff', '#ff00ff', '#ffff00', '#000080'
  ];

  const renderChart = () => {
    if (!chartData.length || !selectedColumns.length) {
      return (
        <Alert severity="info">
          اختر الأعمدة لعرض الرسم البياني
        </Alert>
      );
    }

    const colors = getChartColors();

    switch (selectedChart) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="index" />
              <YAxis />
              <RechartsTooltip />
              {selectedColumns.map((column, index) => (
                <Bar 
                  key={column} 
                  dataKey={column} 
                  fill={colors[index % colors.length]} 
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="index" />
              <YAxis />
              <RechartsTooltip />
              {selectedColumns.map((column, index) => (
                <Line 
                  key={column} 
                  type="monotone" 
                  dataKey={column} 
                  stroke={colors[index % colors.length]} 
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="index" />
              <YAxis />
              <RechartsTooltip />
              {selectedColumns.map((column, index) => (
                <Area 
                  key={column} 
                  type="monotone" 
                  dataKey={column} 
                  stackId="1"
                  stroke={colors[index % colors.length]} 
                  fill={colors[index % colors.length]} 
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
        // للرسم الدائري، نأخذ أول عمود فقط
        const pieData = chartData.map((item, index) => ({
          name: `Row ${index + 1}`,
          value: item[selectedColumns[0]] || 0
        })).slice(0, 10);

        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <RechartsTooltip />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  const getNumericColumns = () => {
    if (!data) return [];
    
    return data.columns.filter((column, index) => {
      // تحقق من أن معظم القيم في هذا العمود رقمية
      const sampleValues = data.rows.slice(0, 10).map(row => row[index]);
      const numericCount = sampleValues.filter(val => 
        typeof val === 'number' || (typeof val === 'string' && !isNaN(Number(val)))
      ).length;
      
      return numericCount > sampleValues.length / 2;
    });
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          تصور البيانات: {tableName}
        </Typography>
        <IconButton onClick={loadData} color="primary">
          <RefreshIcon />
        </IconButton>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={2} mb={3}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Box color={`${stat.color}.main`} mr={2}>
                    {stat.icon}
                  </Box>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      {stat.label}
                    </Typography>
                    <Typography variant="h5">
                      {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Tabs */}
      <Card>
        <CardContent>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="الرسوم البيانية" />
            <Tab label="البيانات الخام" />
            <Tab label="الإحصائيات المتقدمة" />
          </Tabs>

          {/* Charts Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>نوع الرسم البياني</InputLabel>
                  <Select
                    value={selectedChart}
                    onChange={(e) => setSelectedChart(e.target.value as any)}
                    label="نوع الرسم البياني"
                  >
                    <MenuItem value="bar">أعمدة</MenuItem>
                    <MenuItem value="line">خطي</MenuItem>
                    <MenuItem value="area">منطقة</MenuItem>
                    <MenuItem value="pie">دائري</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>الأعمدة</InputLabel>
                  <Select
                    multiple
                    value={selectedColumns}
                    onChange={(e) => setSelectedColumns(e.target.value as string[])}
                    label="الأعمدة"
                  >
                    {getNumericColumns().map((column) => (
                      <MenuItem key={column} value={column}>
                        {column}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={9}>
                <Paper sx={{ p: 2, height: 450 }}>
                  {loading ? (
                    <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                      <CircularProgress />
                    </Box>
                  ) : (
                    renderChart()
                  )}
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Raw Data Tab */}
          <TabPanel value={tabValue} index={1}>
            {loading && <LinearProgress sx={{ mb: 2 }} />}
            
            {data ? (
              <TableContainer component={Paper} sx={{ maxHeight: 500 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      {data.columns.map((column) => (
                        <TableCell key={column} sx={{ fontWeight: 'bold' }}>
                          {column}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.rows.slice(0, 50).map((row, index) => (
                      <TableRow key={index} hover>
                        {row.map((cell, cellIndex) => (
                          <TableCell key={cellIndex}>
                            {cell !== null && cell !== undefined ? String(cell) : (
                              <Chip label="NULL" size="small" variant="outlined" />
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">لا توجد بيانات متاحة</Alert>
            )}
          </TabPanel>

          {/* Advanced Statistics Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              {data?.columns.map((column, index) => (
                <Grid item xs={12} md={6} key={column}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6">{column}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box>
                        <Typography variant="body2" gutterBottom>
                          نوع البيانات: {typeof data.rows[0]?.[index] || 'غير محدد'}
                        </Typography>
                        <Typography variant="body2" gutterBottom>
                          القيم الفارغة: {data.rows.filter(row => row[index] === null || row[index] === undefined).length}
                        </Typography>
                        <Typography variant="body2" gutterBottom>
                          القيم الفريدة: {new Set(data.rows.map(row => row[index])).size}
                        </Typography>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              ))}
            </Grid>
          </TabPanel>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DataVisualization;
