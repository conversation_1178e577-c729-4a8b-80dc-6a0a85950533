# Test Real Interactive OpenStreetMap
Write-Host "🗺️ Testing Real Interactive OpenStreetMap - TecnoDrive" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Test Frontend
Write-Host "`n🌐 Testing Frontend..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "✅ Frontend: Ready (Status: $($frontendResponse.StatusCode))" -ForegroundColor Green
    $frontendWorking = $true
} catch {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
    $frontendWorking = $false
}

# Test Enhanced Map Service
Write-Host "`n🗺️ Testing Enhanced Map Service..." -ForegroundColor Cyan
try {
    $mapHealth = Invoke-RestMethod -Uri "http://localhost:8085/health" -TimeoutSec 5
    Write-Host "✅ Map Service: $($mapHealth.status)" -ForegroundColor Green
    Write-Host "📊 Service: $($mapHealth.service)" -ForegroundColor Cyan
    $mapWorking = $true
} catch {
    Write-Host "❌ Map Service: Not Ready" -ForegroundColor Red
    $mapWorking = $false
}

# Test Enhanced Vehicle Data
if ($mapWorking) {
    Write-Host "`n🚗 Testing Enhanced Vehicle Data..." -ForegroundColor Cyan
    try {
        $vehicleData = Invoke-RestMethod -Uri "http://localhost:8085/api/map/vehicles" -TimeoutSec 5
        
        if ($vehicleData.success -and $vehicleData.data) {
            Write-Host "✅ Vehicle Data: $($vehicleData.count) vehicles loaded" -ForegroundColor Green
            
            # Show vehicle details
            foreach ($vehicle in $vehicleData.data) {
                $statusColor = if ($vehicle.status -eq "active") { "Green" } else { "Yellow" }
                Write-Host "   🚗 $($vehicle.id): $($vehicle.driver) - $($vehicle.speed) km/h ($($vehicle.status))" -ForegroundColor $statusColor
                Write-Host "      📍 Location: $($vehicle.lat), $($vehicle.lng)" -ForegroundColor Gray
                if ($vehicle.route) {
                    Write-Host "      🛣️ Route: $($vehicle.route) → $($vehicle.destination)" -ForegroundColor Gray
                }
            }
        }
    } catch {
        Write-Host "❌ Vehicle Data: Failed to load" -ForegroundColor Red
    }
}

# Test Map Configuration
Write-Host "`n⚙️ Testing Map Configuration..." -ForegroundColor Cyan
try {
    $mapConfig = Invoke-RestMethod -Uri "http://localhost:8085/api/map/config/enhanced" -TimeoutSec 5
    
    if ($mapConfig.success -and $mapConfig.data) {
        Write-Host "✅ Map Config: Loaded successfully" -ForegroundColor Green
        Write-Host "   🎯 Default Center: $($mapConfig.data.defaultCenter.lat), $($mapConfig.data.defaultCenter.lng)" -ForegroundColor Cyan
        Write-Host "   🔍 Zoom Range: $($mapConfig.data.minZoom) - $($mapConfig.data.maxZoom)" -ForegroundColor Cyan
        Write-Host "   🗺️ Providers: $($mapConfig.data.providers.Count)" -ForegroundColor Cyan
        
        # List providers
        foreach ($provider in $mapConfig.data.providers) {
            Write-Host "      - $($provider.name) (Max Zoom: $($provider.maxZoom))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Map Config: Failed to load" -ForegroundColor Red
}

# Test Map Statistics
Write-Host "`n📊 Testing Map Statistics..." -ForegroundColor Cyan
try {
    $mapStats = Invoke-RestMethod -Uri "http://localhost:8085/api/locations/stats" -TimeoutSec 5
    
    if ($mapStats.success -and $mapStats.data) {
        Write-Host "✅ Map Stats: Loaded successfully" -ForegroundColor Green
        Write-Host "   🚗 Total Vehicles: $($mapStats.data.totalVehicles)" -ForegroundColor Cyan
        Write-Host "   🟢 Active Vehicles: $($mapStats.data.activeVehicles)" -ForegroundColor Green
        Write-Host "   🟡 Idle Vehicles: $($mapStats.data.idleVehicles)" -ForegroundColor Yellow
        Write-Host "   ⚡ Average Speed: $($mapStats.data.averageSpeed) km/h" -ForegroundColor Cyan
        Write-Host "   📍 Coverage Area:" -ForegroundColor Cyan
        Write-Host "      Lat: $($mapStats.data.coverage.lat.min) to $($mapStats.data.coverage.lat.max)" -ForegroundColor Gray
        Write-Host "      Lng: $($mapStats.data.coverage.lng.min) to $($mapStats.data.coverage.lng.max)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Map Stats: Failed to load" -ForegroundColor Red
}

# Check Leaflet Dependencies
Write-Host "`n📦 Checking Leaflet Dependencies..." -ForegroundColor Cyan
$packageJsonPath = "frontend/admin-dashboard/package.json"
if (Test-Path $packageJsonPath) {
    $packageContent = Get-Content $packageJsonPath -Raw | ConvertFrom-Json
    
    $leafletDeps = @("leaflet", "react-leaflet", "@types/leaflet")
    foreach ($dep in $leafletDeps) {
        if ($packageContent.dependencies.$dep -or $packageContent.devDependencies.$dep) {
            $version = if ($packageContent.dependencies.$dep) { $packageContent.dependencies.$dep } else { $packageContent.devDependencies.$dep }
            Write-Host "✅ ${dep}: $version" -ForegroundColor Green
        } else {
            Write-Host "❌ ${dep}: Not installed" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ package.json not found" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Real Maps Test Summary" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

if ($frontendWorking) {
    Write-Host "✅ Frontend: Ready" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
}

if ($mapWorking) {
    Write-Host "✅ Enhanced Map Service: Ready" -ForegroundColor Green
} else {
    Write-Host "❌ Enhanced Map Service: Not Ready" -ForegroundColor Red
}

# Real Map Features
Write-Host "`n🆕 Real Map Features:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host "✅ Real OpenStreetMap tiles from official servers" -ForegroundColor Green
Write-Host "✅ Multiple map providers (OSM, CartoDB, Satellite)" -ForegroundColor Green
Write-Host "✅ Interactive controls (zoom, pan, locate)" -ForegroundColor Green
Write-Host "✅ Real-time vehicle tracking (6 vehicles)" -ForegroundColor Green
Write-Host "✅ Vehicle details with routes and destinations" -ForegroundColor Green
Write-Host "✅ User geolocation support" -ForegroundColor Green
Write-Host "✅ Click-to-get-coordinates functionality" -ForegroundColor Green
Write-Host "✅ Responsive design with floating controls" -ForegroundColor Green

# Test URLs
Write-Host "`n🔗 Test URLs:" -ForegroundColor Yellow
Write-Host "=============" -ForegroundColor Yellow
Write-Host "🗺️ Real Interactive Map: http://localhost:3000/map/real" -ForegroundColor White
Write-Host "🗺️ Map Test Page: http://localhost:3000/map/test" -ForegroundColor White
Write-Host "🗺️ Simple Map: http://localhost:3000/map" -ForegroundColor White

# API Test URLs
Write-Host "`n🔗 API Test URLs:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow
Write-Host "🚗 Enhanced Vehicles: http://localhost:8085/api/map/vehicles" -ForegroundColor White
Write-Host "⚙️ Map Configuration: http://localhost:8085/api/map/config/enhanced" -ForegroundColor White
Write-Host "📊 Map Statistics: http://localhost:8085/api/locations/stats" -ForegroundColor White
Write-Host "🗺️ Tile Servers: http://localhost:8085/api/map/tiles" -ForegroundColor White

# Testing Instructions
Write-Host "`n📋 Testing Instructions:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

Write-Host "`n🗺️ Test Real Interactive Map:" -ForegroundColor Cyan
Write-Host "1. Go to: http://localhost:3000/map/real" -ForegroundColor White
Write-Host "2. Verify OpenStreetMap tiles load correctly" -ForegroundColor White
Write-Host "3. Check that 6 vehicle markers appear" -ForegroundColor White
Write-Host "4. Click on vehicle markers to see details" -ForegroundColor White
Write-Host "5. Use floating controls (settings, zoom, locate)" -ForegroundColor White
Write-Host "6. Try different map providers from settings" -ForegroundColor White
Write-Host "7. Click on map to get coordinates" -ForegroundColor White
Write-Host "8. Test user location (allow location access)" -ForegroundColor White

Write-Host "`n🎛️ Test Interactive Features:" -ForegroundColor Cyan
Write-Host "1. 🔍 Zoom in/out using controls or mouse wheel" -ForegroundColor White
Write-Host "2. 🖱️ Pan around by dragging the map" -ForegroundColor White
Write-Host "3. ⚙️ Open settings panel (gear icon)" -ForegroundColor White
Write-Host "4. 🗺️ Switch between map providers" -ForegroundColor White
Write-Host "5. 📍 Use 'My Location' button" -ForegroundColor White
Write-Host "6. 🔄 Use refresh button to update data" -ForegroundColor White

# Expected Results
Write-Host "`n🎯 Expected Results:" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "✅ Real OpenStreetMap tiles load smoothly" -ForegroundColor White
Write-Host "✅ 6 vehicle markers with different colors" -ForegroundColor White
Write-Host "✅ Vehicle popups show detailed information" -ForegroundColor White
Write-Host "✅ Map controls work perfectly" -ForegroundColor White
Write-Host "✅ Multiple map providers available" -ForegroundColor White
Write-Host "✅ Real-time statistics update" -ForegroundColor White
Write-Host "✅ Responsive design on all screen sizes" -ForegroundColor White

# Browser Console Test
Write-Host "`n🧪 Browser Console Test:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow
Write-Host @"
// Copy to browser console (F12):
console.log('🗺️ Testing Real Map APIs...');

// Test Enhanced Vehicle Data
fetch('http://localhost:8085/api/map/vehicles')
  .then(r => r.json())
  .then(d => {
    console.log('✅ Enhanced Vehicles:', d);
    console.log('🚗 Vehicle Count:', d.count);
    d.data.forEach(v => console.log(`   ${v.id}: ${v.driver} (${v.status})`));
  });

// Test Map Configuration
fetch('http://localhost:8085/api/map/config/enhanced')
  .then(r => r.json())
  .then(d => {
    console.log('✅ Map Config:', d);
    console.log('🗺️ Providers:', d.data.providers.length);
  });
"@ -ForegroundColor Gray

# Final Status
if ($frontendWorking -and $mapWorking) {
    Write-Host "`n🎉 Real Interactive Maps Fully Ready!" -ForegroundColor Green
    Write-Host "🔗 Start testing at: http://localhost:3000/map/real" -ForegroundColor Cyan
    Write-Host "🗺️ Experience real OpenStreetMap with full interactivity!" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️ System Partially Ready" -ForegroundColor Yellow
    if (-not $frontendWorking) {
        Write-Host "❌ Start Frontend: cd frontend/admin-dashboard && npm start" -ForegroundColor Red
    }
    if (-not $mapWorking) {
        Write-Host "❌ Start Map Service: powershell -File map-service.ps1" -ForegroundColor Red
    }
}

Write-Host "`n🚀 Real Interactive OpenStreetMap Testing Complete!" -ForegroundColor Green
