#!/bin/bash

# TECNO DRIVE - System Status Check Script

echo "🔍 TECNO DRIVE Platform - System Status Check"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a service is responding
check_service() {
    local service_name=$1
    local url=$2
    local expected_code=${3:-200}
    
    echo -n "🔍 Checking $service_name... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_code" ]; then
        echo -e "${GREEN}✅ OK (HTTP $response)${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED (HTTP $response)${NC}"
        return 1
    fi
}

# Function to check Docker status
check_docker() {
    echo -n "🐳 Checking Docker... "
    if docker info > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Running${NC}"
        return 0
    else
        echo -e "${RED}❌ Not running${NC}"
        return 1
    fi
}

# Function to check container status
check_containers() {
    echo ""
    echo "📦 Container Status:"
    echo "==================="
    
    if docker-compose ps > /dev/null 2>&1; then
        docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    else
        echo -e "${RED}❌ Cannot check containers (Docker Compose not available)${NC}"
    fi
}

# Main checks
echo ""

# Check Docker
if ! check_docker; then
    echo ""
    echo -e "${RED}❌ Docker is not running!${NC}"
    echo ""
    echo "📋 To start Docker:"
    echo "  1. Open Docker Desktop"
    echo "  2. Wait for it to start"
    echo "  3. Run this script again"
    echo ""
    echo "🚀 Or use the automated script:"
    echo "  ./start-docker-and-system.sh"
    exit 1
fi

# Check containers
check_containers

echo ""
echo "🌐 Service Health Checks:"
echo "========================"

# Infrastructure services
check_service "PostgreSQL" "http://localhost:5432" "000"  # Connection refused is expected for HTTP to PostgreSQL
check_service "Redis" "http://localhost:6379" "000"       # Connection refused is expected for HTTP to Redis

# Core services
check_service "Eureka Server" "http://localhost:8761/actuator/health"
check_service "Auth Service" "http://localhost:8081/actuator/health"
check_service "Ride Service" "http://localhost:8082/actuator/health"
check_service "Parcel Service" "http://localhost:8084/actuator/health"
check_service "API Gateway" "http://localhost:8080/actuator/health"

echo ""
echo "🔗 Gateway Routes Check:"
echo "======================="

echo -n "🔍 Checking Gateway routes... "
routes=$(curl -s "http://localhost:8080/actuator/gateway/routes" 2>/dev/null | grep -o '"route_id"' | wc -l)
if [ "$routes" -gt 0 ]; then
    echo -e "${GREEN}✅ Found $routes routes${NC}"
else
    echo -e "${RED}❌ No routes found${NC}"
fi

echo ""
echo "🧪 Quick API Tests:"
echo "=================="

# Test public endpoint
check_service "Auth Health (Public)" "http://localhost:8080/api/v1/auth/health"

echo ""
echo "📊 System Summary:"
echo "=================="

# Count running services
running_services=0
total_services=5

services=(
    "http://localhost:8761/actuator/health"
    "http://localhost:8081/actuator/health"
    "http://localhost:8082/actuator/health"
    "http://localhost:8084/actuator/health"
    "http://localhost:8080/actuator/health"
)

for service in "${services[@]}"; do
    if curl -s -o /dev/null -w "%{http_code}" "$service" 2>/dev/null | grep -q "200"; then
        running_services=$((running_services + 1))
    fi
done

echo "📈 Services Running: $running_services/$total_services"

if [ "$running_services" -eq "$total_services" ]; then
    echo -e "${GREEN}✅ All services are healthy!${NC}"
    echo ""
    echo "🎉 TECNO DRIVE Platform is ready!"
    echo ""
    echo "🌐 Access URLs:"
    echo "  - Eureka Dashboard: http://localhost:8761"
    echo "  - API Gateway:      http://localhost:8080"
    echo "  - Auth Service:     http://localhost:8081"
    echo "  - Ride Service:     http://localhost:8082"
    echo "  - Parcel Service:   http://localhost:8084"
    echo ""
    echo "🧪 Next steps:"
    echo "  ./test-system.sh        # Run comprehensive tests"
    echo "  ./test-rate-limiting.sh # Test rate limiting"
elif [ "$running_services" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Some services are not ready yet${NC}"
    echo ""
    echo "⏳ Services may still be starting up..."
    echo "   Wait a few minutes and run this script again"
    echo ""
    echo "🔍 Check logs:"
    echo "  docker-compose logs -f"
else
    echo -e "${RED}❌ No services are running${NC}"
    echo ""
    echo "🚀 Start the system:"
    echo "  ./start-docker-and-system.sh"
    echo ""
    echo "🔧 Or manually:"
    echo "  docker-compose up -d --build"
fi

echo ""
echo "🔧 Useful commands:"
echo "  docker-compose ps              # Check container status"
echo "  docker-compose logs -f         # View all logs"
echo "  docker-compose logs -f [name]  # View specific service logs"
echo "  docker-compose down            # Stop all services"
echo "  docker-compose restart [name]  # Restart specific service"
