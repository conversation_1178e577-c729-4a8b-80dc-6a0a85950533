package com.tecnodrive.notificationservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

/**
 * User Notification Preference Entity
 * 
 * Stores user preferences for different notification channels and types.
 * Allows users to control how they receive notifications.
 */
@Entity
@Table(name = "user_notification_preferences")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class UserNotificationPreference {

    @Id
    private UUID userId;

    /**
     * Global notification settings
     */
    @Builder.Default
    private boolean enableNotifications = true;

    /**
     * Channel-specific preferences
     */
    @Builder.Default
    private boolean enableEmail = true;

    @Builder.Default
    private boolean enableSms = true;

    @Builder.Default
    private boolean enablePush = true;

    @Builder.Default
    private boolean enableInApp = true;

    /**
     * Category-specific preferences
     */
    @Builder.Default
    private boolean enableRideNotifications = true;

    @Builder.Default
    private boolean enableDeliveryNotifications = true;

    @Builder.Default
    private boolean enablePaymentNotifications = true;

    @Builder.Default
    private boolean enablePromotionalNotifications = true;

    @Builder.Default
    private boolean enableSecurityNotifications = true;

    /**
     * Time-based preferences
     */
    @Builder.Default
    private boolean enableNightTimeNotifications = false;

    /**
     * Quiet hours start (24-hour format, e.g., 22 for 10 PM)
     */
    @Builder.Default
    private Integer quietHoursStart = 22;

    /**
     * Quiet hours end (24-hour format, e.g., 8 for 8 AM)
     */
    @Builder.Default
    private Integer quietHoursEnd = 8;

    /**
     * Timezone for quiet hours
     */
    @Builder.Default
    private String timezone = "UTC";

    /**
     * Frequency preferences
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private NotificationFrequency emailFrequency = NotificationFrequency.IMMEDIATE;

    @Enumerated(EnumType.STRING)
    @Builder.Default
    private NotificationFrequency smsFrequency = NotificationFrequency.IMMEDIATE;

    /**
     * Language preference for notifications
     */
    @Builder.Default
    private String language = "en";

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Notification Frequency Enum
     */
    public enum NotificationFrequency {
        IMMEDIATE,
        HOURLY,
        DAILY,
        WEEKLY,
        NEVER
    }

    /**
     * Check if notifications are enabled for a specific channel
     */
    public boolean isChannelEnabled(NotificationTemplate.NotificationChannel channel) {
        if (!enableNotifications) {
            return false;
        }

        return switch (channel) {
            case EMAIL -> enableEmail;
            case SMS -> enableSms;
            case PUSH -> enablePush;
            case IN_APP -> enableInApp;
            case WEBHOOK -> true; // Webhooks are always enabled if notifications are on
        };
    }

    /**
     * Check if notifications are enabled for a specific category
     */
    public boolean isCategoryEnabled(String category) {
        if (!enableNotifications) {
            return false;
        }

        return switch (category.toLowerCase()) {
            case "ride" -> enableRideNotifications;
            case "delivery" -> enableDeliveryNotifications;
            case "payment" -> enablePaymentNotifications;
            case "promotional" -> enablePromotionalNotifications;
            case "security" -> enableSecurityNotifications;
            default -> true; // Default to enabled for unknown categories
        };
    }
}
