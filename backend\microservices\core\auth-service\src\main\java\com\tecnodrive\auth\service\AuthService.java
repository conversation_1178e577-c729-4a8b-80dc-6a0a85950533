package com.tecnodrive.auth.service;

import com.tecnodrive.auth.dto.*;

/**
 * Authentication Service Interface
 */
public interface AuthService {

    /**
     * Authenticate user and return tokens
     */
    AuthResponse login(LoginRequest request);

    /**
     * Register new user
     */
    AuthResponse register(RegisterRequest request);

    /**
     * Get user profile by username or email
     */
    UserResponse getProfile(String usernameOrEmail);

    /**
     * Update user profile
     */
    UserResponse updateProfile(String usernameOrEmail, UpdateProfileRequest request);

    /**
     * Change user password
     */
    void changePassword(ChangePasswordRequest request);

    /**
     * Refresh access token using refresh token
     */
    AuthResponse refreshToken(String refreshToken);

    /**
     * Logout user (invalidate tokens)
     */
    void logout(String refreshToken);
}
