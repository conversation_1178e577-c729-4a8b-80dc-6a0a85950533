apiVersion: v1
kind: Namespace
metadata:
  name: tecnodrive
  labels:
    name: tecnodrive
    app.kubernetes.io/name: tecnodrive
    app.kubernetes.io/part-of: tecnodrive-platform
    istio-injection: enabled
  annotations:
    app.kubernetes.io/description: "TECNO DRIVE platform namespace"
---
apiVersion: v1
kind: Namespace
metadata:
  name: tecnodrive-monitoring
  labels:
    name: tecnodrive-monitoring
    app.kubernetes.io/name: tecnodrive-monitoring
    app.kubernetes.io/part-of: tecnodrive-platform
  annotations:
    app.kubernetes.io/description: "TECNO DRIVE monitoring namespace"
---
apiVersion: v1
kind: Namespace
metadata:
  name: tecnodrive-infrastructure
  labels:
    name: tecnodrive-infrastructure
    app.kubernetes.io/name: tecnodrive-infrastructure
    app.kubernetes.io/part-of: tecnodrive-platform
  annotations:
    app.kubernetes.io/description: "TECNO DRIVE infrastructure namespace"
