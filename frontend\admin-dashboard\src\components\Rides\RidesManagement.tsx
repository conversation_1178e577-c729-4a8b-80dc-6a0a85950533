import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Button,
  TextField,
  InputAdornment,
  Chip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Visibility as VisibilityIcon,
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import ExcelService from '../../services/excelService';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
} from '@mui/material';
import { fetchRides } from '../../store/slices/ridesSlice';
import LiveRideTracker from './LiveRideTracker';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rides-tabpanel-${index}`}
      aria-labelledby={`rides-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

const RidesManagement: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { rides, loading, totalRides, activeRides, completedRides } = useSelector(
    (state: RootState) => state.rides
  );

  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] = useState<{ success: boolean; message: string; data?: any[] } | null>(null);

  useEffect(() => {
    dispatch(fetchRides() as any);
  }, [dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    // Update filter based on tab
    switch (newValue) {
      case 0:
        setFilterStatus('ALL');
        break;
      case 1:
        setFilterStatus('REQUESTED');
        break;
      case 2:
        setFilterStatus('IN_PROGRESS');
        break;
      case 3:
        setFilterStatus('COMPLETED');
        break;
      default:
        setFilterStatus('ALL');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleExportRides = () => {
    try {
      ExcelService.exportRides(rides);
      handleMenuClose();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (ExcelService.validateExcelFile(file)) {
        setSelectedFile(file);
        setImportResult(null);
      } else {
        setImportResult({
          success: false,
          message: 'يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)',
        });
      }
    }
  };

  const handleImportRides = async () => {
    if (!selectedFile) return;

    setImportLoading(true);
    try {
      const data = await ExcelService.importFromExcel(selectedFile);
      const ridesSheet = data['الرحلات'] || data['Rides'] || Object.values(data)[0];

      if (!ridesSheet || ridesSheet.length === 0) {
        throw new Error('لم يتم العثور على بيانات الرحلات في الملف');
      }

      setImportResult({
        success: true,
        message: `تم استيراد ${ridesSheet.length} رحلة بنجاح`,
        data: ridesSheet,
      });
    } catch (error) {
      setImportResult({
        success: false,
        message: error instanceof Error ? error.message : 'فشل في استيراد البيانات',
      });
    } finally {
      setImportLoading(false);
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      REQUESTED: { label: 'مطلوبة', color: 'info' as const },
      ACCEPTED: { label: 'مقبولة', color: 'primary' as const },
      IN_PROGRESS: { label: 'جارية', color: 'warning' as const },
      COMPLETED: { label: 'مكتملة', color: 'success' as const },
      CANCELLED: { label: 'ملغية', color: 'error' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'رقم الرحلة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          #{params.value.slice(-6)}
        </Typography>
      ),
    },
    {
      field: 'passengerName',
      headerName: 'اسم الراكب',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {params.value || 'غير محدد'}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.passengerPhone || ''}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'driverName',
      headerName: 'اسم السائق',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {params.value || 'لم يتم التعيين'}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.driverPhone || ''}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'pickupLocation',
      headerName: 'نقطة الانطلاق',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {params.value?.address || 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'destination',
      headerName: 'الوجهة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {params.value?.address || 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'fare',
      headerName: 'التكلفة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          {params.row?.fare ? `${params.row.fare} ر.س` : 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'driverName',
      headerName: 'السائق',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.row?.driverName || 'غير مخصص'}
        </Typography>
      ),
    },
    {
      field: 'pickupLocation',
      headerName: 'نقطة الانطلاق',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {params.row?.pickupLocation?.address || 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'destination',
      headerName: 'الوجهة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {params.row?.destination?.address || 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'fare',
      headerName: 'التكلفة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
          {params.value} ريال
        </Typography>
      ),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الطلب',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.row?.createdAt ? new Date(params.row.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Button
          size="small"
          startIcon={<VisibilityIcon />}
          onClick={() => navigate(`/rides/track/${params.row.id}`)}
          disabled={params.row.status === 'COMPLETED' || params.row.status === 'CANCELLED'}
        >
          تتبع
        </Button>
      ),
    },
  ];

  // Mock data for demonstration
  const mockRides = [
    {
      id: '1',
      passengerId: 'أحمد محمد',
      driverId: 'علي أحمد',
      pickupLocation: { address: 'شارع الزبيري' },
      destination: { address: 'جامعة صنعاء' },
      status: 'COMPLETED',
      fare: 150,
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      passengerId: 'فاطمة علي',
      driverId: 'محمد سالم',
      pickupLocation: { address: 'السبعين' },
      destination: { address: 'شارع هائل' },
      status: 'IN_PROGRESS',
      fare: 200,
      createdAt: new Date().toISOString(),
    },
    {
      id: '3',
      passengerId: 'خالد يحيى',
      driverId: null,
      pickupLocation: { address: 'الحصبة' },
      destination: { address: 'المطار' },
      status: 'REQUESTED',
      fare: 350,
      createdAt: new Date().toISOString(),
    },
  ];

  const filteredRides = mockRides.filter(ride => {
    const matchesSearch = ride.passengerId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ride.pickupLocation.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ride.destination.address.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'ALL' || ride.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <Routes>
      <Route path="/track/:rideId" element={<LiveRideTracker />} />
      <Route path="/*" element={
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الرحلات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          عرض وإدارة جميع الرحلات في النظام
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalRides || mockRides.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الرحلات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {activeRides || mockRides.filter(r => r.status === 'IN_PROGRESS').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                رحلات جارية
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {completedRides || mockRides.filter(r => r.status === 'COMPLETED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                رحلات مكتملة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {mockRides.filter(r => r.status === 'REQUESTED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                رحلات مطلوبة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في الرحلات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
            >
              رحلة جديدة
            </Button>
            <Button
              variant="outlined"
              startIcon={<MoreIcon />}
              onClick={handleMenuOpen}
            >
              المزيد
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="جميع الرحلات" />
            <Tab label="مطلوبة" />
            <Tab label="جارية" />
            <Tab label="مكتملة" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredRides}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{
                border: 0,
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
              }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredRides.filter(r => r.status === 'REQUESTED')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredRides.filter(r => r.status === 'IN_PROGRESS')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={filteredRides.filter(r => r.status === 'COMPLETED')}
              columns={columns}
              loading={loading}
              pageSizeOptions={[10, 25, 50]}
              checkboxSelection
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </TabPanel>
      </Card>

      {/* Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleExportRides}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>تصدير إلى Excel</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { setImportDialogOpen(true); handleMenuClose(); }}>
          <ListItemIcon>
            <UploadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>استيراد من Excel</ListItemText>
        </MenuItem>
      </Menu>

      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onClose={() => setImportDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>استيراد الرحلات من Excel</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            يمكنك استيراد بيانات الرحلات من ملف Excel. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
          </Alert>

          <Box sx={{ mb: 3 }}>
            <input
              accept=".xlsx,.xls"
              style={{ display: 'none' }}
              id="rides-file-input"
              type="file"
              onChange={handleFileSelect}
            />
            <label htmlFor="rides-file-input">
              <Button
                variant="contained"
                component="span"
                startIcon={<UploadIcon />}
                fullWidth
              >
                اختيار ملف Excel
              </Button>
            </label>
          </Box>

          {selectedFile && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">
                الملف المحدد: {selectedFile.name}
              </Typography>
            </Box>
          )}

          {importLoading && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress />
              <Typography variant="body2" sx={{ mt: 1 }}>
                جاري استيراد البيانات...
              </Typography>
            </Box>
          )}

          {importResult && (
            <Alert severity={importResult.success ? 'success' : 'error'} sx={{ mb: 2 }}>
              {importResult.message}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImportDialogOpen(false)}>إلغاء</Button>
          <Button
            variant="contained"
            onClick={handleImportRides}
            disabled={!selectedFile || importLoading}
          >
            استيراد البيانات
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
      } />
    </Routes>
  );
};

export default RidesManagement;
