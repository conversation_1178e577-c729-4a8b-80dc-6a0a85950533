import axios from 'axios';
import { io, Socket } from 'socket.io-client';

// SIEM Integration Types
export interface SIEMAlert {
  id: string;
  source: 'azure_sentinel' | 'splunk' | 'qradar' | 'elastic_siem';
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  title: string;
  description: string;
  affectedAssets: string[];
  sourceIP?: string;
  destinationIP?: string;
  userAccount?: string;
  recommendations: string[];
  rawData: Record<string, any>;
  correlationId?: string;
  mitreTactics?: string[];
  mitreAttackId?: string;
}

export interface SecurityEvent {
  id: string;
  eventType: 'login_attempt' | 'data_access' | 'system_change' | 'network_anomaly' | 'malware_detection';
  timestamp: string;
  userId?: string;
  tenantId: string;
  sourceIP: string;
  userAgent?: string;
  location?: {
    country: string;
    city: string;
    coordinates: [number, number];
  };
  riskScore: number;
  details: Record<string, any>;
  status: 'investigating' | 'confirmed' | 'false_positive' | 'resolved';
}

export interface ThreatIntelligence {
  id: string;
  indicatorType: 'ip' | 'domain' | 'hash' | 'url' | 'email';
  indicatorValue: string;
  threatType: 'malware' | 'phishing' | 'botnet' | 'apt' | 'ransomware';
  confidence: number;
  firstSeen: string;
  lastSeen: string;
  sources: string[];
  tags: string[];
  description: string;
}

export interface SIEMMetrics {
  totalAlerts: number;
  alertsBySeverity: Record<string, number>;
  alertsByType: Record<string, number>;
  meanTimeToDetection: number;
  meanTimeToResponse: number;
  falsePositiveRate: number;
  threatTrends: Array<{
    date: string;
    count: number;
    severity: string;
  }>;
  topThreats: Array<{
    type: string;
    count: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }>;
}

export interface IncidentResponse {
  id: string;
  alertId: string;
  title: string;
  description: string;
  severity: string;
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  assignedTo: string;
  createdAt: string;
  updatedAt: string;
  timeline: Array<{
    timestamp: string;
    action: string;
    performedBy: string;
    details: string;
  }>;
  artifacts: Array<{
    type: 'file' | 'network_capture' | 'memory_dump' | 'log';
    name: string;
    hash: string;
    size: number;
    uploadedAt: string;
  }>;
  playbook?: string;
  lessons_learned?: string;
}

class SIEMIntegrationService {
  private socket: Socket | null = null;
  private baseURL = process.env.REACT_APP_SIEM_API_URL || 'http://localhost:8080';
  private azureSentinelConfig = {
    workspaceId: process.env.REACT_APP_AZURE_WORKSPACE_ID,
    subscriptionId: process.env.REACT_APP_AZURE_SUBSCRIPTION_ID,
    resourceGroup: process.env.REACT_APP_AZURE_RESOURCE_GROUP,
  };

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    this.socket = io(`${this.baseURL}/siem`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to SIEM Integration WebSocket');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from SIEM Integration WebSocket');
    });
  }

  // Real-time SIEM alert subscriptions
  subscribeToSIEMAlerts(tenantId: string, callback: (alert: SIEMAlert) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-siem-alerts', { tenantId });
      this.socket.on('new-siem-alert', callback);
    }
  }

  subscribeToSecurityEvents(tenantId: string, callback: (event: SecurityEvent) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-security-events', { tenantId });
      this.socket.on('new-security-event', callback);
    }
  }

  subscribeToThreatIntelligence(callback: (threat: ThreatIntelligence) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-threat-intel');
      this.socket.on('new-threat-intel', callback);
    }
  }

  // Azure Sentinel Integration
  async getAzureSentinelAlerts(tenantId: string, timeframe: string = '24h'): Promise<SIEMAlert[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/azure-sentinel/alerts`, {
        params: { 
          tenantId, 
          timeframe,
          workspaceId: this.azureSentinelConfig.workspaceId 
        },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching Azure Sentinel alerts:', error);
      throw error;
    }
  }

  async queryAzureSentinelKQL(query: string, timespan: string = 'P1D'): Promise<any[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/siem/azure-sentinel/query`, {
        query,
        timespan,
        workspaceId: this.azureSentinelConfig.workspaceId,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.tables[0]?.rows || [];
    } catch (error) {
      console.error('Error executing KQL query:', error);
      throw error;
    }
  }

  // Security Events Management
  async getSecurityEvents(tenantId: string, filters?: {
    eventType?: string;
    severity?: string;
    dateFrom?: string;
    dateTo?: string;
    sourceIP?: string;
    userId?: string;
  }): Promise<SecurityEvent[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/security-events`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching security events:', error);
      throw error;
    }
  }

  async createSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<SecurityEvent> {
    try {
      const response = await axios.post(`${this.baseURL}/api/siem/security-events`, event, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error creating security event:', error);
      throw error;
    }
  }

  async updateSecurityEventStatus(eventId: string, status: SecurityEvent['status'], notes?: string): Promise<SecurityEvent> {
    try {
      const response = await axios.put(`${this.baseURL}/api/siem/security-events/${eventId}/status`, {
        status,
        notes,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating security event status:', error);
      throw error;
    }
  }

  // Threat Intelligence
  async getThreatIntelligence(filters?: {
    indicatorType?: string;
    threatType?: string;
    confidence?: number;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ThreatIntelligence[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/threat-intelligence`, {
        params: filters,
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching threat intelligence:', error);
      throw error;
    }
  }

  async checkThreatIndicator(indicator: string, type: string): Promise<ThreatIntelligence | null> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/threat-intelligence/check`, {
        params: { indicator, type },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error checking threat indicator:', error);
      return null;
    }
  }

  // SIEM Metrics and Analytics
  async getSIEMMetrics(tenantId: string, timeframe: string = '30d'): Promise<SIEMMetrics> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/metrics`, {
        params: { tenantId, timeframe },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching SIEM metrics:', error);
      throw error;
    }
  }

  async getSecurityDashboardData(tenantId: string): Promise<{
    recentAlerts: SIEMAlert[];
    criticalEvents: SecurityEvent[];
    threatSummary: any;
    complianceStatus: any;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/dashboard`, {
        params: { tenantId },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching security dashboard data:', error);
      throw error;
    }
  }

  // Incident Response
  async getIncidents(tenantId: string, filters?: {
    status?: string;
    severity?: string;
    assignedTo?: string;
  }): Promise<IncidentResponse[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/incidents`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching incidents:', error);
      throw error;
    }
  }

  async createIncident(incident: Omit<IncidentResponse, 'id' | 'createdAt' | 'updatedAt' | 'timeline' | 'artifacts'>): Promise<IncidentResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/api/siem/incidents`, incident, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error creating incident:', error);
      throw error;
    }
  }

  async updateIncident(incidentId: string, updates: Partial<IncidentResponse>): Promise<IncidentResponse> {
    try {
      const response = await axios.put(`${this.baseURL}/api/siem/incidents/${incidentId}`, updates, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating incident:', error);
      throw error;
    }
  }

  async addIncidentTimeline(incidentId: string, action: string, details: string): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/siem/incidents/${incidentId}/timeline`, {
        action,
        details,
        performedBy: this.getCurrentUser(),
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error adding incident timeline:', error);
      throw error;
    }
  }

  // Automated Response Actions
  async executePlaybook(incidentId: string, playbookId: string): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/siem/incidents/${incidentId}/playbook`, {
        playbookId,
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error executing playbook:', error);
      throw error;
    }
  }

  async blockIPAddress(ip: string, reason: string, duration?: number): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/siem/actions/block-ip`, {
        ip,
        reason,
        duration,
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error blocking IP address:', error);
      throw error;
    }
  }

  async quarantineUser(userId: string, reason: string): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/siem/actions/quarantine-user`, {
        userId,
        reason,
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error quarantining user:', error);
      throw error;
    }
  }

  // Compliance and Reporting
  async generateComplianceReport(tenantId: string, framework: 'SOC2' | 'ISO27001' | 'GDPR' | 'HIPAA', period: string): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/compliance/report`, {
        params: { tenantId, framework, period },
        headers: this.getAuthHeaders(),
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error generating compliance report:', error);
      throw error;
    }
  }

  async getComplianceStatus(tenantId: string): Promise<{
    framework: string;
    overallScore: number;
    controls: Array<{
      id: string;
      name: string;
      status: 'compliant' | 'non_compliant' | 'partial';
      lastAssessed: string;
      evidence: string[];
    }>;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/api/siem/compliance/status`, {
        params: { tenantId },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching compliance status:', error);
      throw error;
    }
  }

  // Helper methods
  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  private getCurrentUser(): string {
    return localStorage.getItem('userId') || 'unknown';
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

export const siemIntegrationService = new SIEMIntegrationService();
export default siemIntegrationService;
