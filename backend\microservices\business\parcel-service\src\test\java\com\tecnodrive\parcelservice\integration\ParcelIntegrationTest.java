package com.tecnodrive.parcelservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tecnodrive.parcelservice.dto.ParcelDto;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import com.tecnodrive.parcelservice.repository.ParcelRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Parcel Service
 */
@SpringBootTest(properties = {
        "spring.datasource.url=jdbc:h2:mem:testdb",
        "spring.datasource.driver-class-name=org.h2.Driver",
        "spring.datasource.username=sa",
        "spring.datasource.password=",
        "spring.jpa.database-platform=org.hibernate.dialect.H2Dialect",
        "spring.jpa.hibernate.ddl-auto=create-drop"
})
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class ParcelIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ParcelRepository parcelRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        parcelRepository.deleteAll();
    }

    @Test
    void createParcel_ShouldReturnCreatedParcel_WhenValidRequest() throws Exception {
        // Given
        ParcelDto parcelDto = createSampleParcelDto();
        String requestJson = objectMapper.writeValueAsString(parcelDto);

        // When & Then
        mockMvc.perform(post("/parcels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.barcode", is(parcelDto.getBarcode())))
                .andExpect(jsonPath("$.senderName", is(parcelDto.getSenderName())))
                .andExpect(jsonPath("$.receiverName", is(parcelDto.getReceiverName())))
                .andExpect(jsonPath("$.status", is("CREATED")))
                .andExpect(jsonPath("$.parcelId", notNullValue()));
    }

    @Test
    void getParcelById_ShouldReturnParcel_WhenParcelExists() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels/{parcelId}", savedParcel.getParcelId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.parcelId", is(savedParcel.getParcelId())))
                .andExpect(jsonPath("$.barcode", is(savedParcel.getBarcode())))
                .andExpect(jsonPath("$.senderName", is(savedParcel.getSenderName())));
    }

    @Test
    void getParcelById_ShouldReturnNotFound_WhenParcelDoesNotExist() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/{parcelId}", "NON_EXISTENT_ID"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getParcelByBarcode_ShouldReturnParcel_WhenBarcodeExists() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels/barcode/{barcode}", savedParcel.getBarcode()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.barcode", is(savedParcel.getBarcode())))
                .andExpect(jsonPath("$.parcelId", is(savedParcel.getParcelId())));
    }

    @Test
    void updateParcelStatus_ShouldUpdateStatus_WhenValidRequest() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        String statusUpdateJson = """
            {
                "status": "IN_TRANSIT",
                "updatedBy": "user123"
            }
            """;

        // When & Then
        mockMvc.perform(patch("/parcels/{parcelId}/status", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(statusUpdateJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("IN_TRANSIT")));
    }

    @Test
    void getParcelsByUser_ShouldReturnPagedResults() throws Exception {
        // Given
        String userId = "user123";
        createAndSaveParcelForUser(userId);
        createAndSaveParcelForUser(userId);

        // When & Then
        mockMvc.perform(get("/parcels/user/{userId}", userId)
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(2)))
                .andExpect(jsonPath("$.totalElements", is(2)))
                .andExpect(jsonPath("$.content[0].userId", is(userId)));
    }

    @Test
    void searchParcels_ShouldReturnMatchingParcels() throws Exception {
        // Given
        ParcelEntity parcel = createAndSaveParcel();
        String searchTerm = parcel.getSenderName().substring(0, 3);

        // When & Then
        mockMvc.perform(get("/parcels/search")
                .param("query", searchTerm)
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[0].senderName", containsString(searchTerm)));
    }

    @Test
    void cancelParcel_ShouldCancelParcel_WhenCancellable() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        String cancellationJson = """
            {
                "reason": "Customer request",
                "cancelledBy": "user123"
            }
            """;

        // When & Then
        mockMvc.perform(post("/parcels/{parcelId}/cancel", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancellationJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("CANCELLED")));
    }

    @Test
    void getParcelStatistics_ShouldReturnStatistics() throws Exception {
        // Given
        createAndSaveParcel();
        createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalParcels", is(2)))
                .andExpect(jsonPath("$.statusCounts", notNullValue()));
    }

    @Test
    void getParcelStatuses_ShouldReturnAllStatuses() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/statuses"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$", hasItem("CREATED")))
                .andExpect(jsonPath("$", hasItem("DELIVERED")));
    }

    @Test
    void healthCheck_ShouldReturnHealthStatus() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("UP")))
                .andExpect(jsonPath("$.service", is("parcel-service")));
    }

    @Test
    void createParcel_ShouldReturnBadRequest_WhenBarcodeExists() throws Exception {
        // Given
        ParcelEntity existingParcel = createAndSaveParcel();
        ParcelDto duplicateParcel = createSampleParcelDto();
        duplicateParcel.setBarcode(existingParcel.getBarcode());
        String requestJson = objectMapper.writeValueAsString(duplicateParcel);

        // When & Then
        mockMvc.perform(post("/parcels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateParcel_ShouldReturnUpdatedParcel_WhenValidRequest() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        ParcelDto updateDto = createSampleParcelDto();
        updateDto.setSenderName("محمد أحمد المحدث");
        updateDto.setNotes("Updated notes");
        String requestJson = objectMapper.writeValueAsString(updateDto);

        // When & Then
        mockMvc.perform(put("/parcels/{parcelId}", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.senderName", is("محمد أحمد المحدث")))
                .andExpect(jsonPath("$.notes", is("Updated notes")));
    }

    @Test
    void updateParcel_ShouldReturnNotFound_WhenParcelDoesNotExist() throws Exception {
        // Given
        ParcelDto updateDto = createSampleParcelDto();
        String requestJson = objectMapper.writeValueAsString(updateDto);

        // When & Then
        mockMvc.perform(put("/parcels/{parcelId}", "NON_EXISTENT_ID")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isNotFound());
    }

    @Test
    void getParcelsByStatus_ShouldReturnParcelsWithStatus() throws Exception {
        // Given
        createAndSaveParcel(); // Creates with CREATED status
        createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels/status/{status}", "CREATED")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(2)))
                .andExpect(jsonPath("$.content[0].status", is("CREATED")));
    }

    @Test
    void getParcelsByStatus_ShouldReturnBadRequest_WhenInvalidStatus() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/status/{status}", "INVALID_STATUS"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getAllParcels_ShouldReturnAllParcels() throws Exception {
        // Given
        createAndSaveParcel();
        createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)));
    }

    @Test
    void deleteParcel_ShouldDeleteParcel_WhenParcelExists() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();

        // When & Then
        mockMvc.perform(delete("/parcels/{parcelId}", savedParcel.getParcelId()))
                .andExpect(status().isNoContent());
    }

    @Test
    void deleteParcel_ShouldReturnNotFound_WhenParcelDoesNotExist() throws Exception {
        // When & Then
        mockMvc.perform(delete("/parcels/{parcelId}", "NON_EXISTENT_ID"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getParcelPriorities_ShouldReturnAllPriorities() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/priorities"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$", hasItem("HIGH")))
                .andExpect(jsonPath("$", hasItem("MEDIUM")))
                .andExpect(jsonPath("$", hasItem("LOW")));
    }

    @Test
    void createParcel_ShouldReturnBadRequest_WhenInvalidData() throws Exception {
        // Given
        String invalidJson = """
            {
                "userId": "",
                "barcode": "",
                "senderName": "",
                "receiverName": ""
            }
            """;

        // When & Then
        mockMvc.perform(post("/parcels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateParcelStatus_ShouldReturnBadRequest_WhenMissingFields() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        String incompleteJson = """
            {
                "status": "IN_TRANSIT"
            }
            """;

        // When & Then
        mockMvc.perform(patch("/parcels/{parcelId}/status", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(incompleteJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateParcelStatus_ShouldReturnBadRequest_WhenInvalidStatus() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        String invalidStatusJson = """
            {
                "status": "INVALID_STATUS",
                "updatedBy": "user123"
            }
            """;

        // When & Then
        mockMvc.perform(patch("/parcels/{parcelId}/status", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidStatusJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void cancelParcel_ShouldReturnBadRequest_WhenMissingFields() throws Exception {
        // Given
        ParcelEntity savedParcel = createAndSaveParcel();
        String incompleteJson = """
            {
                "reason": "Customer request"
            }
            """;

        // When & Then
        mockMvc.perform(post("/parcels/{parcelId}/cancel", savedParcel.getParcelId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(incompleteJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void searchParcels_ShouldReturnEmptyResults_WhenNoMatches() throws Exception {
        // Given
        createAndSaveParcel();

        // When & Then
        mockMvc.perform(get("/parcels/search")
                .param("query", "NONEXISTENT_SEARCH_TERM")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }

    @Test
    void getParcelsByUser_ShouldReturnEmptyResults_WhenUserHasNoParcels() throws Exception {
        // When & Then
        mockMvc.perform(get("/parcels/user/{userId}", "NONEXISTENT_USER")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(0)))
                .andExpect(jsonPath("$.totalElements", is(0)));
    }

    @Test
    void getParcelsByUser_ShouldSupportSorting() throws Exception {
        // Given
        String userId = "user123";
        createAndSaveParcelForUser(userId);
        Thread.sleep(10); // Ensure different timestamps
        createAndSaveParcelForUser(userId);

        // When & Then - Test ascending sort
        mockMvc.perform(get("/parcels/user/{userId}", userId)
                .param("page", "0")
                .param("size", "10")
                .param("sortBy", "createdAt")
                .param("sortDir", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(2)));
    }

    // Helper methods
    private ParcelDto createSampleParcelDto() {
        return ParcelDto.builder()
                .userId("user123")
                .barcode("TEST-" + System.currentTimeMillis())
                .senderName("أحمد محمد")
                .receiverName("خالد علي")
                .senderAddress("شارع بغداد، صنعاء")
                .receiverAddress("شارع الزبيري، صنعاء")
                .weightKg(2.5)
                .dimensions(ParcelDto.DimensionsDto.builder()
                        .lengthCm(30)
                        .widthCm(20)
                        .heightCm(10)
                        .build())
                .priority("MEDIUM")
                .fragile(false)
                .notes("Test parcel")
                .build();
    }

    private ParcelEntity createAndSaveParcel() {
        return createAndSaveParcelForUser("user123");
    }

    private ParcelEntity createAndSaveParcelForUser(String userId) {
        ParcelEntity parcel = ParcelEntity.builder()
                .parcelId("PCL-" + System.currentTimeMillis())
                .userId(userId)
                .barcode("TEST-" + System.currentTimeMillis() + "-" + Math.random())
                .senderName("أحمد محمد")
                .receiverName("خالد علي")
                .senderAddress("شارع بغداد، صنعاء")
                .receiverAddress("شارع الزبيري، صنعاء")
                .weightKg(2.5)
                .dimensions(ParcelEntity.Dimensions.builder()
                        .lengthCm(30)
                        .widthCm(20)
                        .heightCm(10)
                        .build())
                .status(ParcelEntity.ParcelStatus.CREATED)
                .priority(ParcelEntity.ParcelPriority.MEDIUM)
                .fragile(false)
                .notes("Test parcel")
                .build();

        return parcelRepository.save(parcel);
    }
}
