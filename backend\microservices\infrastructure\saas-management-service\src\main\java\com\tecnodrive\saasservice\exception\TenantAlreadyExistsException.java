package com.tecnodrive.saasservice.exception;

/**
 * Tenant Already Exists Exception
 * 
 * Thrown when attempting to create a tenant with a name that already exists
 */
public class TenantAlreadyExistsException extends RuntimeException {

    public TenantAlreadyExistsException() {
        super("Tenant already exists");
    }

    public TenantAlreadyExistsException(String message) {
        super(message);
    }

    public TenantAlreadyExistsException(String message, Throwable cause) {
        super(message, cause);
    }
}
