package com.tecnodrive.locationservice.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * Interactive Map Service
 * Handles map-related operations and integrations
 */
@Service
public class InteractiveMapService {

    private static final Logger log = LoggerFactory.getLogger(InteractiveMapService.class);

    private final RestTemplate restTemplate;

    @Autowired
    public InteractiveMapService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${tecnodrive.maps.default-provider:openstreetmap}")
    private String defaultMapProvider;

    @Value("${tecnodrive.maps.google.api-key:}")
    private String googleMapsApiKey;

    @Value("${tecnodrive.maps.mapbox.access-token:}")
    private String mapboxAccessToken;

    /**
     * Get map configuration for frontend
     */
    public Map<String, Object> getMapConfiguration() {
        Map<String, Object> config = new HashMap<>();
        
        // Default map settings
        config.put("defaultProvider", defaultMapProvider);
        config.put("defaultZoom", 12);
        config.put("defaultCenter", Map.of(
            "lat", 24.7136,
            "lng", 46.6753
        )); // Riyadh coordinates
        
        // Available map providers
        List<Map<String, Object>> providers = new ArrayList<>();
        
        // OpenStreetMap (Free)
        providers.add(Map.of(
            "id", "openstreetmap",
            "name", "OpenStreetMap",
            "type", "tile",
            "url", "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            "attribution", "© OpenStreetMap contributors",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c")
        ));
        
        // Google Maps (if API key available)
        if (!googleMapsApiKey.isEmpty()) {
            providers.add(Map.of(
                "id", "google",
                "name", "Google Maps",
                "type", "google",
                "apiKey", googleMapsApiKey,
                "maxZoom", 20
            ));
        }
        
        // Mapbox (if access token available)
        if (!mapboxAccessToken.isEmpty()) {
            providers.add(Map.of(
                "id", "mapbox",
                "name", "Mapbox",
                "type", "mapbox",
                "accessToken", mapboxAccessToken,
                "styleUrl", "mapbox://styles/mapbox/streets-v11",
                "maxZoom", 22
            ));
        }
        
        config.put("providers", providers);
        
        // Map controls
        config.put("controls", Map.of(
            "zoom", true,
            "fullscreen", true,
            "layers", true,
            "search", true,
            "geolocation", true,
            "traffic", true,
            "satellite", true
        ));
        
        // Real-time features
        config.put("realtime", Map.of(
            "enabled", true,
            "updateInterval", 5000, // 5 seconds
            "trackVehicles", true,
            "showTraffic", true,
            "showRoutes", true
        ));
        
        return config;
    }

    /**
     * Get tile server URLs for different map providers
     */
    public Map<String, Object> getTileServers() {
        Map<String, Object> tileServers = new HashMap<>();
        
        // OpenStreetMap
        tileServers.put("openstreetmap", Map.of(
            "url", "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            "attribution", "© OpenStreetMap contributors",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c")
        ));
        
        // OpenStreetMap Hot
        tileServers.put("openstreetmap-hot", Map.of(
            "url", "https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png",
            "attribution", "© OpenStreetMap contributors, Tiles style by Humanitarian OpenStreetMap Team",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c")
        ));
        
        // CartoDB Positron (Light theme)
        tileServers.put("cartodb-light", Map.of(
            "url", "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
            "attribution", "© OpenStreetMap contributors © CARTO",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c", "d")
        ));
        
        // CartoDB Dark Matter (Dark theme)
        tileServers.put("cartodb-dark", Map.of(
            "url", "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
            "attribution", "© OpenStreetMap contributors © CARTO",
            "maxZoom", 19,
            "subdomains", Arrays.asList("a", "b", "c", "d")
        ));
        
        return tileServers;
    }

    /**
     * Get geocoding suggestions
     */
    public List<Map<String, Object>> getGeocodingSuggestions(String query, double lat, double lng, int limit) {
        List<Map<String, Object>> suggestions = new ArrayList<>();
        
        try {
            // Use Nominatim for geocoding (free OpenStreetMap service)
            String url = String.format(
                "https://nominatim.openstreetmap.org/search?q=%s&format=json&limit=%d&countrycodes=sa&addressdetails=1&lat=%f&lon=%f",
                query, limit, lat, lng
            );
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> results = restTemplate.getForObject(url, List.class);
            
            if (results != null) {
                for (Map<String, Object> result : results) {
                    Map<String, Object> suggestion = new HashMap<>();
                    suggestion.put("display_name", result.get("display_name"));
                    suggestion.put("lat", Double.parseDouble((String) result.get("lat")));
                    suggestion.put("lng", Double.parseDouble((String) result.get("lon")));
                    suggestion.put("type", result.get("type"));
                    suggestion.put("importance", result.get("importance"));
                    
                    // Extract address components
                    @SuppressWarnings("unchecked")
                    Map<String, Object> address = (Map<String, Object>) result.get("address");
                    if (address != null) {
                        suggestion.put("address", address);
                    }
                    
                    suggestions.add(suggestion);
                }
            }
        } catch (Exception e) {
            log.error("Error getting geocoding suggestions: {}", e.getMessage());
        }
        
        return suggestions;
    }

    /**
     * Reverse geocoding - get address from coordinates
     */
    public Map<String, Object> reverseGeocode(double lat, double lng) {
        try {
            String url = String.format(
                "https://nominatim.openstreetmap.org/reverse?lat=%f&lon=%f&format=json&addressdetails=1",
                lat, lng
            );
            
            @SuppressWarnings("unchecked")
            Map<String, Object> result = restTemplate.getForObject(url, Map.class);
            
            if (result != null) {
                Map<String, Object> response = new HashMap<>();
                response.put("display_name", result.get("display_name"));
                response.put("lat", lat);
                response.put("lng", lng);
                
                @SuppressWarnings("unchecked")
                Map<String, Object> address = (Map<String, Object>) result.get("address");
                if (address != null) {
                    response.put("address", address);
                    
                    // Extract common address components
                    response.put("street", address.get("road"));
                    response.put("neighborhood", address.get("neighbourhood"));
                    response.put("city", address.get("city"));
                    response.put("state", address.get("state"));
                    response.put("country", address.get("country"));
                    response.put("postcode", address.get("postcode"));
                }
                
                return response;
            }
        } catch (Exception e) {
            log.error("Error in reverse geocoding: {}", e.getMessage());
        }
        
        return Map.of(
            "display_name", "Unknown location",
            "lat", lat,
            "lng", lng
        );
    }

    /**
     * Get route between two points
     */
    public Map<String, Object> getRoute(double startLat, double startLng, double endLat, double endLng) {
        try {
            // Use OSRM for routing (free OpenStreetMap routing service)
            String url = String.format(
                "https://router.project-osrm.org/route/v1/driving/%f,%f;%f,%f?overview=full&geometries=geojson",
                startLng, startLat, endLng, endLat
            );
            
            @SuppressWarnings("unchecked")
            Map<String, Object> result = restTemplate.getForObject(url, Map.class);
            
            if (result != null && "Ok".equals(result.get("code"))) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> routes = (List<Map<String, Object>>) result.get("routes");
                
                if (!routes.isEmpty()) {
                    Map<String, Object> route = routes.get(0);
                    Map<String, Object> response = new HashMap<>();
                    
                    response.put("distance", route.get("distance")); // meters
                    response.put("duration", route.get("duration")); // seconds
                    response.put("geometry", route.get("geometry"));
                    
                    return response;
                }
            }
        } catch (Exception e) {
            log.error("Error getting route: {}", e.getMessage());
        }
        
        return Map.of(
            "error", "Could not calculate route",
            "distance", 0,
            "duration", 0
        );
    }

    /**
     * Get map style configuration
     */
    public Map<String, Object> getMapStyles() {
        Map<String, Object> styles = new HashMap<>();
        
        // Default style
        styles.put("default", Map.of(
            "vehicle", Map.of(
                "color", "#007bff",
                "size", 12,
                "icon", "car"
            ),
            "route", Map.of(
                "color", "#28a745",
                "weight", 4,
                "opacity", 0.8
            ),
            "pickup", Map.of(
                "color", "#ffc107",
                "size", 10,
                "icon", "pickup"
            ),
            "dropoff", Map.of(
                "color", "#dc3545",
                "size", 10,
                "icon", "dropoff"
            )
        ));
        
        return styles;
    }
}
