// TECNO DRIVE - API Types and Interfaces
// Fixed TypeScript definitions for better type safety

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  total?: number;
  // إضافة خصائص إحصائية للبيانات
  totalParcels?: number;
  inTransitParcels?: number;
  deliveredParcels?: number;
  totalPayments?: number;
  pendingPayments?: number;
  completedPayments?: number;
  totalUsers?: number;
  activeUsers?: number;
  inactiveUsers?: number;
  // خصائص عامة للإحصائيات
  stats?: Record<string, number>;
  metadata?: Record<string, any>;
}

// User Types with proper role definitions
export type UserRole = "ADMIN" | "MANAGER" | "OPERATOR" | "DRIVER" | "CUSTOMER";
export type UserType = "ADMIN" | "DRIVER" | "CUSTOMER" | "OPERATOR";

export interface LoginResponse {
  user: {
    id: string;
    name: string;
    email: string;
    role: UserRole; // استخدام النوع المحدد بدلاً من string
    avatar: string; // إلزامي الآن
  };
  token: string;
  expiresIn: number;
  refreshToken?: string; // إضافة refreshToken كخاصية اختيارية
}

// Ride Types with proper status definitions
export type RideStatus = "COMPLETED" | "IN_PROGRESS" | "REQUESTED" | "ACCEPTED" | "CANCELLED";

export interface RideDto {
  id: string;
  passengerId: string;
  driverId?: string;
  status: RideStatus; // استخدام النوع المحدد
  pickupLocation: {
    lat: number;
    lng: number;
    address: string;
  };
  destination: {
    lat: number;
    lng: number;
    address: string;
  };
  fare?: number;
  distance?: number;
  duration?: number;
  createdAt: string;
  updatedAt: string;
}

// Parcel Types with proper status definitions
export type ParcelStatus = "PENDING" | "IN_TRANSIT" | "DELIVERED" | "CANCELLED" | "RETURNED";

export interface Parcel {
  id: string;
  trackingNumber: string;
  senderId: string;
  receiverId: string;
  status: ParcelStatus; // استخدام النوع المحدد
  pickupAddress: string;
  deliveryAddress: string;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  createdAt: string;
  estimatedDelivery: string;
}

export interface ParcelFilters {
  page?: number;
  limit?: number;
  status?: ParcelStatus; // استخدام النوع المحدد بدلاً من string
  senderId?: string;
  receiverId?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Payment Types with proper status definitions
export type PaymentStatus = "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED" | "CANCELLED";

export interface Payment {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: PaymentStatus; // استخدام النوع المحدد
  method: string;
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentFilters {
  page?: number;
  limit?: number;
  status?: PaymentStatus; // استخدام النوع المحدد بدلاً من string
  userId?: string;
  method?: string;
  dateFrom?: string;
  dateTo?: string;
}

// User Filters
export interface UserFilters {
  page?: number;
  limit?: number;
  status?: "ACTIVE" | "INACTIVE" | "SUSPENDED"; // استخدام النوع المحدد
  userType?: UserType; // استخدام النوع المحدد بدلاً من string
  role?: UserRole;
  dateFrom?: string;
  dateTo?: string;
}

// API Gateway Types
export interface ApiConsumerDto {
  id: string;
  name: string;
  apiKey: string;
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  rateLimitTier: string;
  createdAt: string;
  lastUsed?: string;
}

// إضافة ConsumerDto كبديل
export type ConsumerDto = ApiConsumerDto;

export interface RateLimitConfigDto {
  id: string;
  name: string;
  tier: string;
  consumerIdOrIp?: string; // إضافة خاصيات مفقودة
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit?: number;
  burstCapacity?: number; // بديل لـ burstLimit
  enabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface RouteDefinitionDto {
  id: string;
  path: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  targetService: string;
  targetPath: string;
  authRequired: boolean;
  rateLimitTier?: string;
  enabled: boolean;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

// إضافة أنواع الطلبات المفقودة
export interface CreateConsumerRequest {
  appName: string;
  description?: string;
  rateLimitTier?: string;
}

export interface UpdateRouteRequest {
  path?: string;
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  targetService?: string;
  targetPath?: string;
  authRequired?: boolean;
  rateLimitTier?: string;
  enabled?: boolean;
  description?: string;
}

// Fleet Service Types
export interface VehicleDto {
  id: string;
  licensePlate: string;
  make: string;
  model: string;
  year: number;
  status: "AVAILABLE" | "IN_USE" | "MAINTENANCE" | "OUT_OF_SERVICE";
  location?: LocationDto;
  driverId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LocationDto {
  lat: number;
  lng: number;
  address?: string;
  timestamp: string;
}

export interface DriverDto {
  id: string;
  userId: string;
  licenseNumber: string;
  licenseExpiry: string;
  status: "AVAILABLE" | "BUSY" | "OFFLINE" | "SUSPENDED";
  rating: number;
  totalTrips: number;
  vehicleId?: string;
  location?: LocationDto;
}

export interface MaintenanceTaskDto {
  id: string;
  vehicleId: string;
  type: "ROUTINE" | "REPAIR" | "INSPECTION" | "EMERGENCY";
  description: string;
  status: "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
  scheduledDate: string;
  completedDate?: string;
  cost?: number;
  notes?: string;
}

export interface MaintenanceRecordDto {
  id: string;
  vehicleId: string;
  taskId: string;
  performedBy: string;
  date: string;
  description: string;
  cost: number;
  partsUsed: string[];
  nextMaintenanceDate?: string;
}

export interface VehicleAssignmentDto {
  id: string;
  vehicleId: string;
  driverId: string;
  assignedAt: string;
  assignedBy: string;
  status: "ACTIVE" | "INACTIVE";
  notes?: string;
}

export interface FuelRecordDto {
  id: string;
  vehicleId: string;
  amount: number;
  cost: number;
  fuelType: string;
  odometer: number;
  date: string;
  location: string;
  receiptNumber?: string;
}

// إضافة أنواع Fleet المفقودة
export interface FleetAnalyticsDto {
  totalVehicles: number;
  activeVehicles: number;
  maintenanceVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  totalTrips: number;
  totalDistance: number;
  fuelConsumption: number;
  maintenanceCosts: number;
  utilizationRate: number;
}

export interface CreateVehicleRequest {
  licensePlate: string;
  make: string;
  model: string;
  year: number;
  color?: string;
  fuelType: string;
  capacity: number;
}

export interface UpdateVehicleRequest {
  licensePlate?: string;
  make?: string;
  model?: string;
  year?: number;
  color?: string;
  status?: "AVAILABLE" | "IN_USE" | "MAINTENANCE" | "OUT_OF_SERVICE";
  fuelType?: string;
  capacity?: number;
}

// Service URLs Configuration
export interface ServiceUrls {
  API_GATEWAY: string;
  AUTH_SERVICE: string;
  USER_SERVICE: string;
  RIDE_SERVICE: string;
  FLEET_SERVICE: string;
  PAYMENT_SERVICE: string;
  NOTIFICATION_SERVICE: string;
  ANALYTICS_SERVICE: string;
  PARCEL_SERVICE: string;
  HR_SERVICE: string;
  FINANCIAL_SERVICE: string;
  WALLET_SERVICE: string;
}

// Request Configuration
export interface RequestConfig extends RequestInit {
  timeout?: number; // إضافة timeout كخاصية اختيارية
  retries?: number;
  retryDelay?: number;
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Database Types for Database Explorer
export interface DatabaseConnection {
  id: string;
  name: string;
  type: 'postgresql' | 'mongodb' | 'redis';
  host: string;
  port: number;
  database: string;
  status: 'connected' | 'disconnected' | 'error';
  lastChecked: string;
}

export interface TableInfo {
  tableName: string;
  database: string;
  rowCount: number;
  columns: ColumnInfo[];
  indexes: string[];
  size: string;
  lastUpdated: string;
}

export interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  primaryKey: boolean;
  foreignKey?: string;
  defaultValue?: any;
}

export interface QueryResult {
  columns: string[];
  rows: any[][];
  totalRows: number;
  executionTime: number;
  query: string;
}

export interface DatabaseStats {
  totalDatabases: number;
  totalTables: number;
  totalRows: number;
  totalSize: string;
  connectionStatus: Record<string, string>;
}
