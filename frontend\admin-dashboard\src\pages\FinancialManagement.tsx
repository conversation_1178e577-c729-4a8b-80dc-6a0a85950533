import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  Receipt as ReceiptIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface FinancialSummary {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  transactionsCount: number;
  averageTransactionValue: number;
  pendingInvoices: number;
  overdueInvoices: number;
  pendingPayouts: number;
}

interface RevenueData {
  period: string;
  ridesRevenue: number;
  parcelsRevenue: number;
  commissionsRevenue: number;
  totalRevenue: number;
  ridesCount: number;
  parcelsCount: number;
}

interface ExpenseData {
  category: string;
  totalAmount: number;
  count: number;
  averageAmount: number;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  currency: string;
  status: string;
  date: string;
  description: string;
  category: string;
}

const FinancialManagement: React.FC = () => {
  const [summary, setSummary] = useState<FinancialSummary | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [expenseData, setExpenseData] = useState<ExpenseData[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [dateRange, setDateRange] = useState('30');
  const [revenuePeriod, setRevenuePeriod] = useState('daily');

  useEffect(() => {
    loadFinancialData();
  }, [dateRange, revenuePeriod]);

  const loadFinancialData = async () => {
    try {
      setLoading(true);

      // Load financial summary
      const summaryResponse = await fetch(`http://localhost:8098/api/financial/summary?dateRange=${dateRange}`);
      const summaryData = await summaryResponse.json();
      
      if (summaryData.success) {
        setSummary(summaryData.data);
      }

      // Load revenue data
      const revenueResponse = await fetch(`http://localhost:8098/api/financial/revenue?period=${revenuePeriod}&days=${dateRange}`);
      const revenueResult = await revenueResponse.json();
      
      if (revenueResult.success) {
        setRevenueData(revenueResult.data);
      }

      // Load expense data
      const expenseResponse = await fetch('http://localhost:8098/api/financial/expenses');
      const expenseResult = await expenseResponse.json();
      
      if (expenseResult.success) {
        setExpenseData(expenseResult.data);
      }

      // Load recent transactions
      const transactionsResponse = await fetch('http://localhost:8098/api/financial/transactions?limit=20');
      const transactionsResult = await transactionsResponse.json();
      
      if (transactionsResult.success) {
        setTransactions(transactionsResult.data);
      }

    } catch (error) {
      console.error('Failed to load financial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getTransactionTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'ride_payment': 'دفعة رحلة',
      'parcel_payment': 'دفعة طرد',
      'driver_payout': 'مدفوعات سائق',
      'fuel_expense': 'مصروف وقود',
      'maintenance_expense': 'مصروف صيانة',
      'commission': 'عمولة'
    };
    return labels[type] || type;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'completed': 'success',
      'pending': 'warning',
      'failed': 'error',
      'refunded': 'info'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'completed': 'مكتمل',
      'pending': 'معلق',
      'failed': 'فاشل',
      'refunded': 'مسترد'
    };
    return labels[status] || status;
  };

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'fuel': 'وقود',
      'maintenance': 'صيانة',
      'insurance': 'تأمين',
      'salaries': 'رواتب',
      'marketing': 'تسويق',
      'office': 'مكتب',
      'technology': 'تقنية'
    };
    return labels[category] || category;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل البيانات المالية...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <BankIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          الإدارة المالية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إدارة شاملة للإيرادات والمصروفات والتحليلات المالية
        </Typography>
      </Box>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>فترة البيانات</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                label="فترة البيانات"
              >
                <MenuItem value="7">آخر 7 أيام</MenuItem>
                <MenuItem value="30">آخر 30 يوم</MenuItem>
                <MenuItem value="90">آخر 90 يوم</MenuItem>
                <MenuItem value="365">آخر سنة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>فترة الإيرادات</InputLabel>
              <Select
                value={revenuePeriod}
                onChange={(e) => setRevenuePeriod(e.target.value)}
                label="فترة الإيرادات"
              >
                <MenuItem value="daily">يومي</MenuItem>
                <MenuItem value="weekly">أسبوعي</MenuItem>
                <MenuItem value="monthly">شهري</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadFinancialData}
            >
              تحديث البيانات
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Financial Summary Cards */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(summary.totalRevenue)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الإيرادات
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      {formatNumber(summary.transactionsCount)} معاملة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingDownIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(summary.totalExpenses)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المصروفات
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      متوسط المعاملة: {formatCurrency(summary.averageTransactionValue)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color={summary.netProfit >= 0 ? 'success' : 'error'} sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4" color={summary.netProfit >= 0 ? 'success.main' : 'error.main'}>
                      {formatCurrency(summary.netProfit)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      صافي الربح
                    </Typography>
                    <Typography variant="caption" color={summary.profitMargin >= 0 ? 'success.main' : 'error.main'}>
                      هامش الربح: {summary.profitMargin.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{summary.pendingInvoices + summary.overdueInvoices}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      فواتير تحتاج متابعة
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      معلقة: {summary.pendingInvoices} | متأخرة: {summary.overdueInvoices}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs for Different Views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="الإيرادات" icon={<TrendingUpIcon />} />
          <Tab label="المصروفات" icon={<TrendingDownIcon />} />
          <Tab label="المعاملات" icon={<ReceiptIcon />} />
          <Tab label="التقارير" icon={<AssessmentIcon />} />
        </Tabs>
      </Paper>

      {/* Revenue Tab */}
      {selectedTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  اتجاه الإيرادات
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    رسم بياني للإيرادات (يتطلب مكتبة رسوم بيانية)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توزيع الإيرادات
                </Typography>
                {revenueData.length > 0 && (
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      آخر {revenueData.length} فترة
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={70} 
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption">
                      رحلات الركاب: 70%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={25} 
                      color="secondary"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption">
                      توصيل الطرود: 25%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={5} 
                      color="info"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption">
                      العمولات: 5%
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Expenses Tab */}
      {selectedTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  المصروفات حسب الفئة
                </Typography>
                {expenseData.map((expense) => (
                  <Box key={expense.category} sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">{getCategoryLabel(expense.category)}</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(expense.totalAmount)}
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.min((expense.totalAmount / Math.max(...expenseData.map(e => e.totalAmount))) * 100, 100)}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {expense.count} معاملة - متوسط: {formatCurrency(expense.averageAmount)}
                    </Typography>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تحليل المصروفات
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  تحليل تفصيلي للمصروفات (يتطلب تطوير إضافي)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Transactions Tab */}
      {selectedTab === 2 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              المعاملات الأخيرة
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>معرف المعاملة</TableCell>
                    <TableCell>النوع</TableCell>
                    <TableCell>المبلغ</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>التاريخ</TableCell>
                    <TableCell>الوصف</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {transaction.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {getTransactionTypeLabel(transaction.type)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography 
                          variant="body2" 
                          fontWeight="bold"
                          color={transaction.amount >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatCurrency(Math.abs(transaction.amount))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(transaction.status)}
                          color={getStatusColor(transaction.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(transaction.date).toLocaleDateString('ar-SA')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap>
                          {transaction.description}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Reports Tab */}
      {selectedTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  التقارير المالية
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  التقارير المالية التفصيلية (يتطلب تطوير إضافي)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default FinancialManagement;
