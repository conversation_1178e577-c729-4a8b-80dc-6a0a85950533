package com.tecnodrive.paymentservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Payment Service Application
 *
 * This service handles:
 * - Payment processing for rides and deliveries
 * - Payment method management
 * - Transaction history and tracking
 * - Payment status updates
 * - Refund processing
 * - Payment gateway integration
 * - Billing and invoicing
 * - Payment analytics and reporting
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
@EnableTransactionManagement
public class PaymentServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(PaymentServiceApplication.class, args);
    }
}
