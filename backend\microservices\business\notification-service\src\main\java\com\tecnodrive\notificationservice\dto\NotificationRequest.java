package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;

/**
 * Notification Request DTO
 * 
 * Used for sending notifications to users
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationRequest {

    /**
     * Template name to use
     */
    @NotBlank(message = "Template name is required")
    private String templateName;

    /**
     * User ID to send notification to
     */
    @NotBlank(message = "User ID is required")
    private String userId;

    /**
     * Recipient address (email, phone, device token, etc.)
     * If not provided, will be looked up from user profile
     */
    private String recipientAddress;

    /**
     * Template variables for substitution
     */
    private Map<String, Object> variables;

    /**
     * Override channel (optional)
     */
    private NotificationTemplate.NotificationChannel channel;

    /**
     * Override priority (optional)
     */
    private NotificationTemplate.NotificationPriority priority;

    /**
     * Schedule delivery for later (optional)
     */
    private java.time.Instant scheduledAt;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;

    /**
     * Additional metadata
     */
    private Map<String, Object> metadata;
}
