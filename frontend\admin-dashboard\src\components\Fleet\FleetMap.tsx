import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  DirectionsCar,
  LocationOn,
  Speed,
  Battery3Bar,
  Refresh,
} from '@mui/icons-material';
import { websocketService, LocationUpdate } from '../../services/websocketService';

interface VehicleLocation {
  vehicleId: string;
  plateNumber: string;
  driverName?: string;
  location: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
  status: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE';
  speed?: number;
  fuel?: number;
  battery?: number;
}

const FleetMap: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<Map<string, any>>(new Map());
  
  const [vehicles, setVehicles] = useState<VehicleLocation[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<string>('ALL');
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [loading, setLoading] = useState(true);

  // Mock vehicle data
  const mockVehicles: VehicleLocation[] = [
    {
      vehicleId: '1',
      plateNumber: 'YE-001',
      driverName: 'علي أحمد',
      location: {
        latitude: 15.3694,
        longitude: 44.1910,
        timestamp: new Date().toISOString(),
      },
      status: 'ACTIVE',
      speed: 45,
      fuel: 75,
      battery: 85,
    },
    {
      vehicleId: '2',
      plateNumber: 'YE-002',
      driverName: 'محمد سالم',
      location: {
        latitude: 15.3500,
        longitude: 44.2000,
        timestamp: new Date().toISOString(),
      },
      status: 'ACTIVE',
      speed: 30,
      fuel: 60,
      battery: 90,
    },
    {
      vehicleId: '3',
      plateNumber: 'YE-003',
      driverName: null,
      location: {
        latitude: 15.3800,
        longitude: 44.1800,
        timestamp: new Date().toISOString(),
      },
      status: 'INACTIVE',
      speed: 0,
      fuel: 40,
      battery: 70,
    },
  ];

  // Initialize Google Maps
  const initializeMap = () => {
    if (!mapRef.current || !window.google) return;

    const map = new window.google.maps.Map(mapRef.current, {
      center: { lat: 15.3694, lng: 44.1910 }, // Sana'a center
      zoom: 12,
      mapTypeControl: true,
      streetViewControl: false,
      fullscreenControl: true,
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }],
        },
      ],
    });

    mapInstanceRef.current = map;
    setIsMapLoaded(true);
    updateVehicleMarkers(mockVehicles);
  };

  // Update vehicle markers on map
  const updateVehicleMarkers = (vehicleData: VehicleLocation[]) => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current.clear();

    // Add new markers
    vehicleData.forEach(vehicle => {
      if (selectedVehicle !== 'ALL' && vehicle.vehicleId !== selectedVehicle) {
        return;
      }

      const marker = new window.google.maps.Marker({
        map: mapInstanceRef.current,
        position: {
          lat: vehicle.location.latitude,
          lng: vehicle.location.longitude,
        },
        icon: {
          url: getVehicleIcon(vehicle.status),
          scaledSize: new window.google.maps.Size(40, 40),
        },
        title: `${vehicle.plateNumber} - ${vehicle.driverName || 'بدون سائق'}`,
      });

      // Create info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: createInfoWindowContent(vehicle),
      });

      marker.addListener('click', () => {
        infoWindow.open(mapInstanceRef.current, marker);
      });

      markersRef.current.set(vehicle.vehicleId, marker);
    });

    // Fit map to show all markers
    if (vehicleData.length > 0) {
      const bounds = new window.google.maps.LatLngBounds();
      vehicleData.forEach(vehicle => {
        if (selectedVehicle === 'ALL' || vehicle.vehicleId === selectedVehicle) {
          bounds.extend({
            lat: vehicle.location.latitude,
            lng: vehicle.location.longitude,
          });
        }
      });
      mapInstanceRef.current.fitBounds(bounds);
    }
  };

  // Get vehicle icon based on status
  const getVehicleIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '/icons/vehicle-active.png';
      case 'INACTIVE':
        return '/icons/vehicle-inactive.png';
      case 'MAINTENANCE':
        return '/icons/vehicle-maintenance.png';
      default:
        return '/icons/vehicle-default.png';
    }
  };

  // Create info window content
  const createInfoWindowContent = (vehicle: VehicleLocation) => {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h3 style="margin: 0 0 10px 0; color: #1976d2;">${vehicle.plateNumber}</h3>
        <p style="margin: 5px 0;"><strong>السائق:</strong> ${vehicle.driverName || 'غير مخصص'}</p>
        <p style="margin: 5px 0;"><strong>الحالة:</strong> ${getStatusLabel(vehicle.status)}</p>
        <p style="margin: 5px 0;"><strong>السرعة:</strong> ${vehicle.speed || 0} كم/س</p>
        <p style="margin: 5px 0;"><strong>الوقود:</strong> ${vehicle.fuel || 0}%</p>
        <p style="margin: 5px 0;"><strong>البطارية:</strong> ${vehicle.battery || 0}%</p>
        <p style="margin: 5px 0; font-size: 12px; color: #666;">
          آخر تحديث: ${new Date(vehicle.location.timestamp).toLocaleTimeString('ar-SA')}
        </p>
      </div>
    `;
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'نشط';
      case 'INACTIVE': return 'غير نشط';
      case 'MAINTENANCE': return 'صيانة';
      default: return status;
    }
  };

  // Get status color
  const getStatusColor = (status: string): "success" | "default" | "warning" => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'default';
      case 'MAINTENANCE': return 'warning';
      default: return 'default';
    }
  };

  // Handle fleet location updates from WebSocket
  const handleFleetUpdate = (updates: LocationUpdate[]) => {
    console.log('Fleet location updates received:', updates);
    
    // Update vehicle locations
    setVehicles(prevVehicles => {
      const updatedVehicles = [...prevVehicles];
      
      updates.forEach(update => {
        const vehicleIndex = updatedVehicles.findIndex(v => v.vehicleId === update.vehicleId);
        if (vehicleIndex !== -1) {
          updatedVehicles[vehicleIndex] = {
            ...updatedVehicles[vehicleIndex],
            location: {
              latitude: update.lat,
              longitude: update.lng,
              timestamp: update.timestamp,
            },
            speed: update.speed,
          };
        }
      });
      
      return updatedVehicles;
    });
  };

  // Load Google Maps script
  useEffect(() => {
    const loadGoogleMaps = () => {
      if (window.google) {
        initializeMap();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&libraries=geometry,places`;
      script.async = true;
      script.defer = true;
      script.onload = initializeMap;
      document.head.appendChild(script);
    };

    loadGoogleMaps();
  }, []);

  // Initialize data and WebSocket
  useEffect(() => {
    setVehicles(mockVehicles);
    setLoading(false);

    // Subscribe to fleet updates if auto-refresh is enabled
    if (autoRefresh) {
      websocketService.subscribeToFleetUpdates(handleFleetUpdate);
    }

    return () => {
      websocketService.unsubscribeFromFleetUpdates();
    };
  }, [autoRefresh]);

  // Update markers when vehicles or selected vehicle changes
  useEffect(() => {
    if (isMapLoaded) {
      updateVehicleMarkers(vehicles);
    }
  }, [vehicles, selectedVehicle, isMapLoaded]);

  const activeVehicles = vehicles.filter(v => v.status === 'ACTIVE').length;
  const inactiveVehicles = vehicles.filter(v => v.status === 'INACTIVE').length;
  const maintenanceVehicles = vehicles.filter(v => v.status === 'MAINTENANCE').length;

  return (
    <Box sx={{ height: '100vh', display: 'flex', gap: 2, p: 2 }}>
      {/* Map */}
      <Card sx={{ flex: 1 }}>
        <CardContent sx={{ height: '100%', p: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">خريطة الأسطول المباشرة</Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                  />
                }
                label="تحديث تلقائي"
              />
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>اختر مركبة</InputLabel>
                <Select
                  value={selectedVehicle}
                  label="اختر مركبة"
                  onChange={(e) => setSelectedVehicle(e.target.value)}
                >
                  <MenuItem value="ALL">جميع المركبات</MenuItem>
                  {vehicles.map(vehicle => (
                    <MenuItem key={vehicle.vehicleId} value={vehicle.vehicleId}>
                      {vehicle.plateNumber}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Box>
          
          <Box
            ref={mapRef}
            sx={{
              width: '100%',
              height: 'calc(100% - 60px)',
              borderRadius: 1,
              position: 'relative',
            }}
          />
          
          {loading && (
            <Box sx={{ 
              position: 'absolute', 
              top: '50%', 
              left: '50%', 
              transform: 'translate(-50%, -50%)',
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}>
              <CircularProgress />
              <Typography>جاري تحميل الخريطة...</Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Vehicle List */}
      <Card sx={{ width: 350 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>قائمة المركبات</Typography>
          
          {/* Stats */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            <Chip label={`نشط: ${activeVehicles}`} color="success" size="small" />
            <Chip label={`غير نشط: ${inactiveVehicles}`} color="default" size="small" />
            <Chip label={`صيانة: ${maintenanceVehicles}`} color="warning" size="small" />
          </Box>

          {/* Vehicle List */}
          <List sx={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
            {vehicles.map(vehicle => (
              <ListItem
                key={vehicle.vehicleId}
                sx={{
                  border: selectedVehicle === vehicle.vehicleId ? '2px solid #1976d2' : '1px solid #e0e0e0',
                  borderRadius: 1,
                  mb: 1,
                  cursor: 'pointer',
                }}
                onClick={() => setSelectedVehicle(vehicle.vehicleId)}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: getStatusColor(vehicle.status) === 'success' ? 'success.main' : 'grey.500' }}>
                    <DirectionsCar />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                        {vehicle.plateNumber}
                      </Typography>
                      <Chip
                        label={getStatusLabel(vehicle.status)}
                        color={getStatusColor(vehicle.status)}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        السائق: {vehicle.driverName || 'غير مخصص'}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <Speed sx={{ fontSize: 14 }} />
                        <Typography variant="caption">{vehicle.speed || 0} كم/س</Typography>
                        <Battery3Bar sx={{ fontSize: 14, ml: 1 }} />
                        <Typography variant="caption">{vehicle.battery || 0}%</Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        آخر تحديث: {new Date(vehicle.location.timestamp).toLocaleTimeString('ar-SA')}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FleetMap;
