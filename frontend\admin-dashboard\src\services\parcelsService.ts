import { apiMethods, ApiResponse, handleApiError, SERVICE_URLS } from './api';
import { MockService } from './mockService';
import { smartApiService } from './smartApiService';

export interface ParcelFilters {
  page?: number;
  limit?: number;
  status?: 'CREATED' | 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED';
  search?: string;
}

export interface Parcel {
  id: string;
  trackingNumber: string;
  senderName: string;
  recipientName: string;
  senderAddress: string;
  recipientAddress: string;
  weight: number;
  status: 'CREATED' | 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED';
  createdAt: string;
  estimatedDelivery?: string;
  deliveredAt?: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
}

class ParcelsService {
  private baseUrl = SERVICE_URLS.PARCEL_SERVICE;

  async getParcels(filters: ParcelFilters = {}): Promise<ApiResponse<Parcel[]>> {
    return await smartApiService.getParcels({
      page: filters.page,
      limit: filters.limit,
      status: filters.status,
      search: filters.search
    });
  }

  async getParcelById(parcelId: string): Promise<ApiResponse<Parcel>> {
    try {
      const response = await apiMethods.get<ApiResponse<Parcel>>(
        `${this.baseUrl}/${parcelId}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateParcelStatus(parcelId: string, status: string): Promise<ApiResponse<Parcel>> {
    try {
      const response = await apiMethods.put<ApiResponse<Parcel>>(
        `${this.baseUrl}/${parcelId}/status`,
        { status }
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async trackParcel(trackingNumber: string): Promise<ApiResponse<Parcel>> {
    try {
      const response = await apiMethods.get<ApiResponse<Parcel>>(
        `${this.baseUrl}/track/${trackingNumber}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const parcelsService = new ParcelsService();
