# TECNO DRIVE - Complete Integration Environment Configuration
# Auto-generated for Frontend-Backend Integration

# Application Settings
REACT_APP_APP_NAME=TECNO DRIVE
REACT_APP_VERSION=1.8.3
REACT_APP_ENVIRONMENT=development
NODE_ENV=development

# Primary API Gateway
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_FRONTEND_URL=http://localhost:3000

# Core Services (Direct Access)
REACT_APP_AUTH_SERVICE_URL=http://localhost:8081
REACT_APP_USER_SERVICE_URL=http://localhost:8082
REACT_APP_RIDE_SERVICE_URL=http://localhost:8083
REACT_APP_FLEET_SERVICE_URL=http://localhost:8084
REACT_APP_PAYMENT_SERVICE_URL=http://localhost:8085

# Business Services (Direct Access)
REACT_APP_NOTIFICATION_SERVICE_URL=http://localhost:8086
REACT_APP_ANALYTICS_SERVICE_URL=http://localhost:8087
REACT_APP_PARCEL_SERVICE_URL=http://localhost:8088
REACT_APP_HR_SERVICE_URL=http://localhost:8089
REACT_APP_FINANCIAL_SERVICE_URL=http://localhost:8090
REACT_APP_WALLET_SERVICE_URL=http://localhost:8091

# Enhanced API Endpoints
REACT_APP_GRAPHQL_HTTP_URL=http://localhost:8080/graphql
REACT_APP_GRAPHQL_WS_URL=ws://localhost:8080/graphql
REACT_APP_WEBSOCKET_URL=ws://localhost:8080
REACT_APP_SIEM_API_URL=http://localhost:8080/siem
REACT_APP_AI_API_URL=http://localhost:8080/ai

# Authentication & Security
REACT_APP_JWT_SECRET=tecno-drive-secret-key-2024
REACT_APP_SESSION_TIMEOUT=3600000
REACT_APP_CORS_ORIGIN=http://localhost:3000

# Database Configuration (for reference)
DATABASE_HOST=localhost
DATABASE_PORT=5432
REDIS_HOST=localhost
REDIS_PORT=6379
MONGODB_HOST=localhost
MONGODB_PORT=27017

# SIEM Integration
REACT_APP_AZURE_WORKSPACE_ID=your-azure-workspace-id
REACT_APP_AZURE_SUBSCRIPTION_ID=your-azure-subscription-id
REACT_APP_AZURE_RESOURCE_GROUP=your-azure-resource-group
REACT_APP_AZURE_TENANT_ID=your-azure-tenant-id

# AI Services
REACT_APP_OPENAI_API_KEY=your-openai-api-key
REACT_APP_AZURE_AI_KEY=your-azure-ai-key
REACT_APP_AZURE_AI_ENDPOINT=https://your-region.api.cognitive.microsoft.com

# Maps & Geolocation
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
REACT_APP_MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# Feature Flags
REACT_APP_ENABLE_REAL_TIME=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_ENABLE_PREDICTIVE_MAINTENANCE=true
REACT_APP_ENABLE_SIEM_INTEGRATION=true
REACT_APP_ENABLE_CIRCUIT_BREAKER=true
REACT_APP_ENABLE_RATE_LIMITING=true
REACT_APP_ENABLE_MOCK_DATA=false

# Performance Configuration
REACT_APP_MAX_REQUESTS_PER_MINUTE=100
REACT_APP_CIRCUIT_BREAKER_THRESHOLD=5
REACT_APP_CIRCUIT_BREAKER_TIMEOUT=60000
REACT_APP_API_TIMEOUT=30000
REACT_APP_CACHE_TTL=300000

# Development Settings
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_DEVTOOLS=true
REACT_APP_MOCK_API=false

# Default Admin Credentials
REACT_APP_DEFAULT_ADMIN_USERNAME=Azal
REACT_APP_DEFAULT_ADMIN_PASSWORD=password tecno

# Features Flags
REACT_APP_ENABLE_EXCEL_IMPORT=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=true

# Oracle APEX Integration
REACT_APP_APEX_URL=https://your-apex-instance.com
REACT_APP_APEX_WORKSPACE=TECNO_DRIVE
REACT_APP_APEX_APP_ID=100

# External Services
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
REACT_APP_WEBSOCKET_URL=ws://localhost:8080/ws

# Logging
REACT_APP_LOG_LEVEL=info
REACT_APP_ENABLE_CONSOLE_LOGS=true

# Performance
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_CACHE_TIMEOUT=300000

# UI Settings
REACT_APP_DEFAULT_LANGUAGE=ar
REACT_APP_DEFAULT_THEME=light
REACT_APP_ITEMS_PER_PAGE=25
