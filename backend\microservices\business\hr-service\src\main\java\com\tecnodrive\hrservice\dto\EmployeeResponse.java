package com.tecnodrive.hrservice.dto;

import com.tecnodrive.hrservice.entity.Employee;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

/**
 * Employee Response DTO
 * 
 * Used for returning employee information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeResponse {

    /**
     * Employee ID
     */
    private String id;

    /**
     * Personal information
     */
    private String firstName;
    private String lastName;
    private String fullName;
    private String email;
    private String employeeNumber;
    private String phoneNumber;
    private LocalDate dateOfBirth;
    private Employee.Gender gender;
    private String address;

    /**
     * Employment information
     */
    private String position;
    private String department;
    private Employee.EmployeeStatus status;
    private Employee.EmploymentType employmentType;
    private LocalDate hireDate;
    private LocalDate terminationDate;
    private LocalDate probationEndDate;

    /**
     * Compensation information
     */
    private BigDecimal salary;
    private BigDecimal hourlyRate;
    private Employee.PayFrequency payFrequency;
    private String currency;
    private BigDecimal monthlySalary;
    private BigDecimal annualSalary;

    /**
     * Manager and reporting structure
     */
    private String managerId;
    private String managerName;

    /**
     * Leave and benefits
     */
    private Integer annualLeaveDays;
    private Integer sickLeaveDays;
    private Integer usedAnnualLeave;
    private Integer usedSickLeave;
    private Integer remainingAnnualLeave;
    private Integer remainingSickLeave;

    /**
     * Performance and development
     */
    private LocalDate lastPerformanceReview;
    private LocalDate nextPerformanceReview;
    private BigDecimal performanceRating;
    private String performanceNotes;

    /**
     * Emergency contact
     */
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String emergencyContactRelation;

    /**
     * Company/Tenant information
     */
    private String companyId;

    /**
     * Additional information
     */
    private String notes;
    private String skills;
    private String certifications;
    private String education;

    /**
     * System fields
     */
    private boolean isActive;
    private Instant createdAt;
    private Instant updatedAt;

    /**
     * Calculated fields
     */
    private boolean onProbation;
    private boolean performanceReviewDue;
    private int yearsOfService;
    private boolean eligibleForBenefits;

    /**
     * Employee Summary DTO for dashboard/list views
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EmployeeSummary {
        private String id;
        private String fullName;
        private String email;
        private String employeeNumber;
        private String position;
        private String department;
        private Employee.EmployeeStatus status;
        private Employee.EmploymentType employmentType;
        private LocalDate hireDate;
        private BigDecimal salary;
        private boolean onProbation;
        private boolean performanceReviewDue;
        private int yearsOfService;
    }

    /**
     * Employee Statistics DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EmployeeStatistics {
        private long totalEmployees;
        private long activeEmployees;
        private long inactiveEmployees;
        private long employeesOnProbation;
        private long employeesOnLeave;
        private long terminatedEmployees;
        private double averageYearsOfService;
        private BigDecimal averageSalary;
        private BigDecimal totalPayroll;
        private long employeesWithDueReviews;
        private long fullTimeEmployees;
        private long partTimeEmployees;
        private long contractEmployees;
    }

    /**
     * Department Statistics DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DepartmentStatistics {
        private String department;
        private long employeeCount;
        private BigDecimal averageSalary;
        private BigDecimal totalSalary;
        private double averageYearsOfService;
        private long activeEmployees;
        private long employeesOnProbation;
    }

    /**
     * Payroll Summary DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PayrollSummary {
        private String employeeId;
        private String fullName;
        private String employeeNumber;
        private String department;
        private BigDecimal baseSalary;
        private BigDecimal overtimePay;
        private BigDecimal bonuses;
        private BigDecimal deductions;
        private BigDecimal netPay;
        private Employee.PayFrequency payFrequency;
        private String currency;
    }
}
