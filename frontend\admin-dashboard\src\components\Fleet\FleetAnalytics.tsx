import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  DirectionsCar as CarIcon,
  Build as MaintenanceIcon,
  LocalGasStation as FuelIcon,
  Speed as SpeedIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';
import { fleetService, VehicleDto, FleetAnalyticsDto } from '../../services/fleetService';

const FleetAnalytics: React.FC = () => {
  const [vehicles, setVehicles] = useState<VehicleDto[]>([]);
  const [analytics, setAnalytics] = useState<FleetAnalyticsDto | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('last_30_days');
  const [selectedAnalyticsType, setSelectedAnalyticsType] = useState<string>('usage');
  const [loading, setLoading] = useState(false);

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load vehicles
      const vehiclesResponse = await fleetService.getVehicles();
      if (vehiclesResponse.success && vehiclesResponse.data) {
        setVehicles(vehiclesResponse.data);
      }

      // Load analytics
      const analyticsResponse = await fleetService.getFleetAnalytics({
        from: getDateRange(selectedPeriod).from,
        to: getDateRange(selectedPeriod).to,
        type: selectedAnalyticsType as 'usage' | 'maintenance' | 'fuel',
      });
      if (analyticsResponse.success && analyticsResponse.data) {
        setAnalytics(analyticsResponse.data);
      }

    } catch (error) {
      console.error('Error loading fleet analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDateRange = (period: string) => {
    const now = new Date();
    const ranges = {
      last_7_days: {
        from: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      },
      last_30_days: {
        from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      },
      last_90_days: {
        from: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      },
      last_year: {
        from: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        to: now.toISOString(),
      },
    };
    return ranges[period as keyof typeof ranges] || ranges.last_30_days;
  };

  useEffect(() => {
    loadData();
  }, [selectedPeriod, selectedAnalyticsType]);

  // Mock data for charts
  const usageOverTimeData = [
    { date: '2025-06-01', distance: 1200, trips: 45, utilization: 75 },
    { date: '2025-06-08', distance: 1350, trips: 52, utilization: 82 },
    { date: '2025-06-15', distance: 1180, trips: 48, utilization: 78 },
    { date: '2025-06-22', distance: 1420, trips: 58, utilization: 85 },
    { date: '2025-06-29', distance: 1380, trips: 55, utilization: 83 },
    { date: '2025-07-06', distance: 1450, trips: 60, utilization: 88 },
  ];

  const maintenanceCostData = [
    { month: 'يناير', cost: 15000, tasks: 12 },
    { month: 'فبراير', cost: 18000, tasks: 15 },
    { month: 'مارس', cost: 12000, tasks: 10 },
    { month: 'أبريل', cost: 22000, tasks: 18 },
    { month: 'مايو', cost: 16000, tasks: 13 },
    { month: 'يونيو', cost: 20000, tasks: 16 },
  ];

  const fuelConsumptionData = [
    { vehicle: 'YE-001', consumption: 450, efficiency: 12.5 },
    { vehicle: 'YE-002', consumption: 380, efficiency: 14.2 },
    { vehicle: 'YE-003', consumption: 420, efficiency: 13.1 },
    { vehicle: 'YE-004', consumption: 350, efficiency: 15.8 },
    { vehicle: 'YE-005', consumption: 280, efficiency: 18.5 },
  ];

  const vehicleStatusDistribution = [
    { name: 'نشط', value: analytics?.activeVehicles || 3, color: '#4caf50' },
    { name: 'صيانة', value: analytics?.maintenanceVehicles || 1, color: '#ff9800' },
    { name: 'غير متصل', value: analytics?.offlineVehicles || 1, color: '#f44336' },
  ];

  const topPerformingVehicles = vehicles
    .sort((a, b) => (b.mileage || 0) - (a.mileage || 0))
    .slice(0, 5)
    .map((vehicle, index) => ({
      rank: index + 1,
      plateNumber: vehicle.plateNumber,
      make: vehicle.make,
      model: vehicle.model,
      mileage: vehicle.mileage || 0,
      status: vehicle.status,
      utilization: Math.floor(Math.random() * 30) + 70, // Mock utilization
    }));

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          تحليلات الأسطول
        </Typography>
        <Typography variant="body1" color="text.secondary">
          تحليلات شاملة لأداء الأسطول والكفاءة التشغيلية
        </Typography>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>الفترة الزمنية</InputLabel>
              <Select
                value={selectedPeriod}
                label="الفترة الزمنية"
                onChange={(e) => setSelectedPeriod(e.target.value)}
              >
                <MenuItem value="last_7_days">آخر 7 أيام</MenuItem>
                <MenuItem value="last_30_days">آخر 30 يوم</MenuItem>
                <MenuItem value="last_90_days">آخر 90 يوم</MenuItem>
                <MenuItem value="last_year">آخر سنة</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>نوع التحليل</InputLabel>
              <Select
                value={selectedAnalyticsType}
                label="نوع التحليل"
                onChange={(e) => setSelectedAnalyticsType(e.target.value)}
              >
                <MenuItem value="usage">الاستخدام</MenuItem>
                <MenuItem value="maintenance">الصيانة</MenuItem>
                <MenuItem value="fuel">الوقود</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
              disabled={loading}
            >
              تحديث
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => console.log('Export analytics report')}
            >
              تصدير التقرير
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CarIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {analytics?.totalVehicles || vehicles.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المركبات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SpeedIcon sx={{ fontSize: 40, color: 'success.main' }} />
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {analytics?.totalMileage?.toLocaleString() || '172,000'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي المسافة (كم)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MaintenanceIcon sx={{ fontSize: 40, color: 'warning.main' }} />
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {analytics?.maintenanceCosts?.toLocaleString() || '8,000'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تكلفة الصيانة (ريال)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'info.main' }} />
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                    {analytics?.utilizationRate || 75}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل الاستخدام
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                اتجاه الاستخدام عبر الوقت
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={usageOverTimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="distance" stackId="1" stroke="#8884d8" fill="#8884d8" name="المسافة (كم)" />
                  <Area type="monotone" dataKey="trips" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="عدد الرحلات" />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزيع حالة المركبات
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={vehicleStatusDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {vehicleStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Maintenance and Fuel Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                تكلفة الصيانة الشهرية
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={maintenanceCostData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="cost" fill="#ff9800" name="التكلفة (ريال)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                استهلاك الوقود حسب المركبة
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={fuelConsumptionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="vehicle" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="consumption" fill="#4caf50" name="الاستهلاك (لتر)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Vehicles Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            أفضل المركبات أداءً
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الترتيب</TableCell>
                  <TableCell>رقم اللوحة</TableCell>
                  <TableCell>الماركة والموديل</TableCell>
                  <TableCell>المسافة المقطوعة</TableCell>
                  <TableCell>معدل الاستخدام</TableCell>
                  <TableCell>الحالة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {topPerformingVehicles.map((vehicle) => (
                  <TableRow key={vehicle.plateNumber}>
                    <TableCell>
                      <Chip
                        label={`#${vehicle.rank}`}
                        color={vehicle.rank <= 3 ? 'primary' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>
                      {vehicle.plateNumber}
                    </TableCell>
                    <TableCell>
                      {vehicle.make} {vehicle.model}
                    </TableCell>
                    <TableCell>
                      {vehicle.mileage.toLocaleString()} كم
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${vehicle.utilization}%`}
                        color={vehicle.utilization > 80 ? 'success' : vehicle.utilization > 60 ? 'warning' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          vehicle.status === 'ACTIVE' ? 'نشط' :
                          vehicle.status === 'MAINTENANCE' ? 'صيانة' :
                          vehicle.status === 'OFFLINE' ? 'غير متصل' : vehicle.status
                        }
                        color={
                          vehicle.status === 'ACTIVE' ? 'success' :
                          vehicle.status === 'MAINTENANCE' ? 'warning' :
                          vehicle.status === 'OFFLINE' ? 'error' : 'default'
                        }
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FleetAnalytics;
