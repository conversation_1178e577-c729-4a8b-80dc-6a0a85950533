-- TECNO DRIVE Platform - Database Initialization Script
-- This script creates all required databases and extensions

-- Enable PostGIS extension for the main database
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- Create all application databases
CREATE DATABASE IF NOT EXISTS auth_db;
CREATE DATABASE IF NOT EXISTS ride_db;
CREATE DATABASE IF NOT EXISTS fleet_db;
CREATE DATABASE IF NOT EXISTS parcel_db;
CREATE DATABASE IF NOT EXISTS payment_db;
CREATE DATABASE IF NOT EXISTS notification_db;
CREATE DATABASE IF NOT EXISTS financial_db;
CREATE DATABASE IF NOT EXISTS hr_db;
CREATE DATABASE IF NOT EXISTS analytics_db;
CREATE DATABASE IF NOT EXISTS saas_db;
CREATE DATABASE IF NOT EXISTS location_db;

-- Connect to each database and enable PostGIS where needed
\c auth_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c ride_db;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c fleet_db;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c parcel_db;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c payment_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c notification_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c financial_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c hr_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c analytics_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c saas_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c location_db;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create sample location data for testing
CREATE TABLE IF NOT EXISTS test_locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255),
    location GEOMETRY(POINT, 4326),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO test_locations (name, location) VALUES 
('Riyadh Center', ST_GeomFromText('POINT(46.6753 24.7136)', 4326)),
('Jeddah Port', ST_GeomFromText('POINT(39.1975 21.4858)', 4326)),
('Dammam Industrial', ST_GeomFromText('POINT(50.0647 26.4207)', 4326));

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Switch back to main database
\c postgres;

-- Create monitoring views
CREATE OR REPLACE VIEW database_status AS
SELECT 
    datname as database_name,
    pg_size_pretty(pg_database_size(datname)) as size,
    (SELECT count(*) FROM pg_stat_activity WHERE datname = pg_database.datname) as active_connections
FROM pg_database 
WHERE datistemplate = false 
ORDER BY pg_database_size(datname) DESC;

-- Create connection monitoring function
CREATE OR REPLACE FUNCTION get_connection_info()
RETURNS TABLE(
    database_name text,
    active_connections bigint,
    max_connections integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        datname::text,
        count(*)::bigint,
        (SELECT setting::integer FROM pg_settings WHERE name = 'max_connections')
    FROM pg_stat_activity 
    WHERE datname IS NOT NULL 
    GROUP BY datname;
END;
$$ LANGUAGE plpgsql;

-- Log initialization completion
DO $$
BEGIN
    RAISE NOTICE 'TECNO DRIVE Database initialization completed successfully!';
    RAISE NOTICE 'PostGIS enabled for location services';
    RAISE NOTICE 'All application databases created';
    RAISE NOTICE 'UUID extensions enabled';
END $$;
