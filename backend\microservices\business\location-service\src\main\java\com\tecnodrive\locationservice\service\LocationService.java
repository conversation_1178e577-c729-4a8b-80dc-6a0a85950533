package com.tecnodrive.locationservice.service;

import com.tecnodrive.locationservice.entity.Location;
import com.tecnodrive.locationservice.repository.LocationRepository;
// Temporarily disabled until PostGIS is properly installed
// import org.locationtech.jts.geom.Coordinate;
// import org.locationtech.jts.geom.GeometryFactory;
// import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional
public class LocationService {
    
    @Autowired
    private LocationRepository locationRepository;

    // Temporarily disabled until PostGIS is properly installed
    // private final GeometryFactory geometryFactory = new GeometryFactory();
    
    // Save location
    public Location saveLocation(Location location) {
        // Temporarily disabled until PostGIS is properly installed
        // Create PostGIS Point from latitude/longitude
        // if (location.getLatitude() != null && location.getLongitude() != null) {
        //     Point point = geometryFactory.createPoint(
        //         new Coordinate(location.getLongitude(), location.getLatitude())
        //     );
        //     point.setSRID(4326); // WGS84
        //     location.setCoordinates(point);
        // }
        
        return locationRepository.save(location);
    }
    
    // Get location by ID
    public Optional<Location> getLocationById(UUID id) {
        return locationRepository.findById(id);
    }
    
    // Get latest location for entity
    public Optional<Location> getLatestLocationForEntity(String entityId) {
        return locationRepository.findTopByEntityIdAndIsActiveTrueOrderByTimestampDesc(entityId);
    }
    
    // Get all locations for entity
    public List<Location> getLocationsByEntity(String entityId) {
        return locationRepository.findByEntityIdAndIsActiveTrueOrderByTimestampDesc(entityId);
    }
    
    // Get locations by entity type
    public List<Location> getLocationsByEntityType(String entityType) {
        return locationRepository.findByEntityTypeAndIsActiveTrueOrderByTimestampDesc(entityType);
    }
    
    // Get locations within radius
    public List<Location> getLocationsWithinRadius(double latitude, double longitude, double radiusMeters) {
        String point = String.format("POINT(%f %f)", longitude, latitude);
        return locationRepository.findLocationsWithinRadius(point, radiusMeters);
    }
    
    // Get nearest locations
    public List<Location> getNearestLocations(double latitude, double longitude, int limit) {
        String point = String.format("POINT(%f %f)", longitude, latitude);
        return locationRepository.findNearestLocations(point, limit);
    }
    
    // Calculate distance between two points
    public Double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        return locationRepository.calculateDistance(lat1, lon1, lat2, lon2);
    }
    
    // Get location history
    public List<Location> getLocationHistory(String entityId, LocalDateTime startTime, LocalDateTime endTime) {
        return locationRepository.findByEntityIdAndTimestampBetweenAndIsActiveTrueOrderByTimestampDesc(
            entityId, startTime, endTime);
    }
    
    // Get locations by city
    public List<Location> getLocationsByCity(String city) {
        return locationRepository.findByCityAndIsActiveTrueOrderByTimestampDesc(city);
    }
    
    // Get locations by country
    public List<Location> getLocationsByCountry(String country) {
        return locationRepository.findByCountryAndIsActiveTrueOrderByTimestampDesc(country);
    }
    
    // Deactivate location
    public void deactivateLocation(UUID id) {
        Optional<Location> location = locationRepository.findById(id);
        if (location.isPresent()) {
            Location loc = location.get();
            loc.setIsActive(false);
            loc.setUpdatedAt(LocalDateTime.now());
            locationRepository.save(loc);
        }
    }
    
    // Bulk save locations
    public List<Location> bulkSaveLocations(List<Location> locations) {
        List<Location> savedLocations = new ArrayList<>();
        for (Location location : locations) {
            savedLocations.add(saveLocation(location));
        }
        return savedLocations;
    }
    
    // Get locations along route
    public List<Location> getLocationsAlongRoute(String lineString, double bufferMeters) {
        return locationRepository.findLocationsAlongRoute(lineString, bufferMeters);
    }
    
    // Get locations within polygon
    public List<Location> getLocationsWithinPolygon(String polygon) {
        return locationRepository.findLocationsWithinPolygon(polygon);
    }
    
    // Get location statistics
    public Map<String, Object> getLocationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Total active locations
        long totalLocations = locationRepository.count();
        stats.put("total_locations", totalLocations);
        
        // Count by entity type
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("DRIVER", locationRepository.countActiveLocationsByEntityType("DRIVER"));
        countByType.put("VEHICLE", locationRepository.countActiveLocationsByEntityType("VEHICLE"));
        countByType.put("CUSTOMER", locationRepository.countActiveLocationsByEntityType("CUSTOMER"));
        stats.put("count_by_type", countByType);
        
        // Recent updates (last hour)
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        List<Location> recentUpdates = locationRepository.findByUpdatedAtAfterAndIsActiveTrueOrderByUpdatedAtDesc(oneHourAgo);
        stats.put("recent_updates_count", recentUpdates.size());
        
        // Stale locations (not updated in last 24 hours)
        LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
        List<Location> staleLocations = locationRepository.findStaleLocations(oneDayAgo);
        stats.put("stale_locations_count", staleLocations.size());
        
        stats.put("last_updated", LocalDateTime.now());
        
        return stats;
    }
    
    // Get detailed statistics by entity type
    public List<Map<String, Object>> getDetailedStatsByEntityType() {
        List<Object[]> rawStats = locationRepository.getLocationStatsByEntityType();
        List<Map<String, Object>> stats = new ArrayList<>();
        
        for (Object[] row : rawStats) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("entity_type", row[0]);
            stat.put("total_locations", row[1]);
            stat.put("unique_entities", row[2]);
            stat.put("avg_accuracy", row[3]);
            stat.put("latest_update", row[4]);
            stats.add(stat);
        }
        
        return stats;
    }
    
    // Track entity movement
    public Map<String, Object> trackEntityMovement(String entityId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Location> locations = getLocationHistory(entityId, startTime, endTime);
        
        if (locations.isEmpty()) {
            return Map.of("error", "No location data found for the specified time range");
        }
        
        Map<String, Object> movement = new HashMap<>();
        movement.put("entity_id", entityId);
        movement.put("start_time", startTime);
        movement.put("end_time", endTime);
        movement.put("total_points", locations.size());
        
        // Calculate total distance traveled
        double totalDistance = 0.0;
        for (int i = 1; i < locations.size(); i++) {
            Location prev = locations.get(i);
            Location curr = locations.get(i - 1);
            if (prev.getLatitude() != null && prev.getLongitude() != null &&
                curr.getLatitude() != null && curr.getLongitude() != null) {
                totalDistance += calculateDistance(
                    prev.getLatitude(), prev.getLongitude(),
                    curr.getLatitude(), curr.getLongitude()
                );
            }
        }
        movement.put("total_distance_meters", totalDistance);
        
        // Calculate average speed
        if (!locations.isEmpty() && locations.size() > 1) {
            LocalDateTime start = locations.get(locations.size() - 1).getTimestamp();
            LocalDateTime end = locations.get(0).getTimestamp();
            long durationMinutes = java.time.Duration.between(start, end).toMinutes();
            if (durationMinutes > 0) {
                double avgSpeedKmh = (totalDistance / 1000.0) / (durationMinutes / 60.0);
                movement.put("average_speed_kmh", avgSpeedKmh);
            }
        }
        
        movement.put("locations", locations);
        
        return movement;
    }
}
