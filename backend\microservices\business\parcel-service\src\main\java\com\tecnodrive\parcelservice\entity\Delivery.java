package com.tecnodrive.parcelservice.entity;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.UUID;

/**
 * Delivery Entity
 */
@Entity
@Table(name = "deliveries")
public class Delivery {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(nullable = false, unique = true)
    private UUID parcelId;

    private UUID driverId;

    private Instant assignedAt;

    private Instant pickedUpAt;

    private Instant deliveredAt;

    @Column(nullable = false)
    private String status = "ASSIGNED";

    // Constructors
    public Delivery() {}

    public Delivery(UUID parcelId, UUID driverId) {
        this.parcelId = parcelId;
        this.driverId = driverId;
        this.assignedAt = Instant.now();
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public UUID getParcelId() { return parcelId; }
    public void setParcelId(UUID parcelId) { this.parcelId = parcelId; }

    public UUID getDriverId() { return driverId; }
    public void setDriverId(UUID driverId) { this.driverId = driverId; }

    public Instant getAssignedAt() { return assignedAt; }
    public void setAssignedAt(Instant assignedAt) { this.assignedAt = assignedAt; }

    public Instant getPickedUpAt() { return pickedUpAt; }
    public void setPickedUpAt(Instant pickedUpAt) { this.pickedUpAt = pickedUpAt; }

    public Instant getDeliveredAt() { return deliveredAt; }
    public void setDeliveredAt(Instant deliveredAt) { this.deliveredAt = deliveredAt; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    @Override
    public String toString() {
        return "Delivery{" +
                "id=" + id +
                ", parcelId=" + parcelId +
                ", driverId=" + driverId +
                ", status='" + status + '\'' +
                '}';
    }
}
