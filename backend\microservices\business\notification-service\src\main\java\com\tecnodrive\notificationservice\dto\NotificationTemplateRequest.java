package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * Notification Template Request DTO
 * 
 * Used for creating and updating notification templates
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationTemplateRequest {

    /**
     * Unique template identifier
     */
    @NotBlank(message = "Template name is required")
    @Size(min = 3, max = 100, message = "Template name must be between 3 and 100 characters")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "Template name must be uppercase with underscores (e.g., WELCOME_EMAIL)")
    private String templateName;

    /**
     * Template display name
     */
    @NotBlank(message = "Display name is required")
    @Size(max = 200, message = "Display name cannot exceed 200 characters")
    private String displayName;

    /**
     * Template description
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /**
     * Notification channel
     */
    @NotNull(message = "Channel is required")
    private NotificationTemplate.NotificationChannel channel;

    /**
     * Template category for organization
     */
    @Size(max = 50, message = "Category cannot exceed 50 characters")
    private String category;

    /**
     * Subject template (for email/SMS)
     */
    @Size(max = 500, message = "Subject template cannot exceed 500 characters")
    private String subjectTemplate;

    /**
     * Message body template
     */
    @NotBlank(message = "Body template is required")
    private String bodyTemplate;

    /**
     * Template variables (JSON format)
     */
    private String templateVariables;

    /**
     * Template priority
     */
    private NotificationTemplate.NotificationPriority priority;

    /**
     * Template status
     */
    private Boolean isActive;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;
}
