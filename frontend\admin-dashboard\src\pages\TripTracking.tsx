import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Chip,

  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar
} from '@mui/material';
import {
  MyLocation as LocationIcon,
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  LocalShipping as ParcelIcon,
  Schedule as ScheduleIcon,
  Route as RouteIcon,
  Share as ShareIcon,
  Visibility as ViewIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  Navigation as NavigationIcon,
  Timeline as TimelineIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Map as MapIcon
} from '@mui/icons-material';

interface Trip {
  id: string;
  type: string;
  status: string;
  customerName: string;
  customerPhone: string;
  driverName?: string;
  driverPhone?: string;
  vehiclePlate?: string;
  origin: {
    address: string;
    coordinates: { lat: number; lng: number };
  };
  destination: {
    address: string;
    coordinates: { lat: number; lng: number };
  };
  currentLocation: {
    lat: number;
    lng: number;
    timestamp: string;
  };
  startTime: string;
  estimatedArrival: string;
  actualArrival?: string;
  distance: number;
  fare: number;
  paymentMethod: string;
  paymentStatus: string;
  rating?: number;
  feedback?: string;
  priority: string;
  isEmergency: boolean;
  parcelDetails?: {
    weight: number;
    dimensions: string;
    description: string;
    fragile: boolean;
    value: number;
    recipientName: string;
    recipientPhone: string;
  };
  shareableLink: string;
}

interface TrackingEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  actor: string;
  actorName: string;
  severity: string;
  isVisible: boolean;
}

interface ShareableLink {
  id: string;
  url: string;
  token: string;
  isActive: boolean;
  expiresAt: string;
  accessCount: number;
  permissions: {
    viewLocation: boolean;
    viewDriver: boolean;
    viewETA: boolean;
    viewRoute: boolean;
    contactDriver: boolean;
  };
}

const TripTracking: React.FC = () => {
  const [tripId, setTripId] = useState('');
  const [trip, setTrip] = useState<Trip | null>(null);
  const [events, setEvents] = useState<TrackingEvent[]>([]);
  const [shareableLink, setShareableLink] = useState<ShareableLink | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [mapDialogOpen, setMapDialogOpen] = useState(false);

  const loadTripData = async (id: string) => {
    if (!id.trim()) return;

    try {
      setLoading(true);
      setError(null);

      // Load trip details
      const tripResponse = await fetch(`http://localhost:8102/api/tracking/trip/${id}`);
      const tripData = await tripResponse.json();
      
      if (tripData.success) {
        setTrip(tripData.data);
      } else {
        setError('لم يتم العثور على الرحلة');
        return;
      }

      // Load trip events
      const eventsResponse = await fetch(`http://localhost:8102/api/tracking/trip/${id}/events`);
      const eventsData = await eventsResponse.json();
      
      if (eventsData.success) {
        setEvents(eventsData.data);
      }

      // Load shareable link
      const linkResponse = await fetch(`http://localhost:8102/api/tracking/trip/${id}/share`);
      const linkData = await linkResponse.json();
      
      if (linkData.success) {
        setShareableLink(linkData.data);
      }

    } catch (err) {
      console.error('Failed to load trip data:', err);
      setError('فشل في تحميل بيانات الرحلة');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    loadTripData(tripId);
  };

  const handleCreateShareableLink = async () => {
    if (!trip) return;

    try {
      const response = await fetch(`http://localhost:8102/api/tracking/trip/${trip.id}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          permissions: {
            viewLocation: true,
            viewDriver: true,
            viewETA: true,
            viewRoute: true,
            contactDriver: false
          }
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setShareableLink(data.data);
      }
    } catch (error) {
      console.error('Failed to create shareable link:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'pending': 'warning',
      'assigned': 'info',
      'pickup_in_progress': 'primary',
      'in_transit': 'primary',
      'delivery_in_progress': 'primary',
      'completed': 'success',
      'cancelled': 'error'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'pending': 'في الانتظار',
      'assigned': 'مُعيَّن',
      'pickup_in_progress': 'جاري الالتقاط',
      'in_transit': 'في الطريق',
      'delivery_in_progress': 'جاري التسليم',
      'completed': 'مكتمل',
      'cancelled': 'ملغي'
    };
    return labels[status] || status;
  };

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'passenger_ride': 'رحلة ركاب',
      'parcel_delivery': 'توصيل طرد',
      'mixed': 'مختلط'
    };
    return labels[type] || type;
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error': return <ErrorIcon color="error" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'info': return <InfoIcon color="info" />;
      default: return <CheckIcon color="success" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#f44336';
      case 'high': return '#ff9800';
      case 'normal': return '#4caf50';
      default: return '#9e9e9e';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          تتبع الرحلات والطرود
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          تتبع مفصل للرحلات مع إمكانية المشاركة والتتبع المباشر
        </Typography>
      </Box>

      {/* Search Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="معرف الرحلة"
              placeholder="أدخل معرف الرحلة للتتبع"
              value={tripId}
              onChange={(e) => setTripId(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleSearch}
              disabled={loading || !tripId.trim()}
              startIcon={loading ? <CircularProgress size={20} /> : <LocationIcon />}
            >
              {loading ? 'جاري البحث...' : 'تتبع الرحلة'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Trip Details */}
      {trip && (
        <Grid container spacing={3}>
          {/* Trip Info Card */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    تفاصيل الرحلة {trip.id}
                  </Typography>
                  <Box display="flex" gap={1}>
                    <Chip
                      label={getStatusLabel(trip.status)}
                      color={getStatusColor(trip.status)}
                    />
                    {trip.isEmergency && (
                      <Chip label="طوارئ" color="error" />
                    )}
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: getPriorityColor(trip.priority),
                        alignSelf: 'center'
                      }}
                    />
                  </Box>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">نوع الرحلة</Typography>
                    <Typography variant="body1">{getTypeLabel(trip.type)}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">العميل</Typography>
                    <Typography variant="body1">{trip.customerName}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">السائق</Typography>
                    <Typography variant="body1">{trip.driverName || 'لم يتم التعيين'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">رقم المركبة</Typography>
                    <Typography variant="body1">{trip.vehiclePlate || 'غير محدد'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">المنشأ</Typography>
                    <Typography variant="body1">{trip.origin.address}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">الوجهة</Typography>
                    <Typography variant="body1">{trip.destination.address}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">المسافة</Typography>
                    <Typography variant="body1">{trip.distance.toFixed(1)} كم</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">الأجرة</Typography>
                    <Typography variant="body1" color="success.main">
                      {formatCurrency(trip.fare)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">وقت البداية</Typography>
                    <Typography variant="body1">
                      {new Date(trip.startTime).toLocaleString('ar-SA')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">الوصول المتوقع</Typography>
                    <Typography variant="body1">
                      {new Date(trip.estimatedArrival).toLocaleString('ar-SA')}
                    </Typography>
                  </Grid>
                </Grid>

                {/* Parcel Details */}
                {trip.parcelDetails && (
                  <Box mt={3}>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      <ParcelIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      تفاصيل الطرد
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" color="text.secondary">الوزن</Typography>
                        <Typography variant="body1">{trip.parcelDetails.weight} كجم</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" color="text.secondary">الأبعاد</Typography>
                        <Typography variant="body1">{trip.parcelDetails.dimensions}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" color="text.secondary">المستلم</Typography>
                        <Typography variant="body1">{trip.parcelDetails.recipientName}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" color="text.secondary">القيمة</Typography>
                        <Typography variant="body1">{formatCurrency(trip.parcelDetails.value)}</Typography>
                      </Grid>
                      {trip.parcelDetails.fragile && (
                        <Grid item xs={12}>
                          <Chip label="قابل للكسر" color="warning" size="small" />
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                )}

                {/* Action Buttons */}
                <Box mt={3} display="flex" gap={2} flexWrap="wrap">
                  <Button
                    variant="outlined"
                    startIcon={<MapIcon />}
                    onClick={() => setMapDialogOpen(true)}
                  >
                    عرض على الخريطة
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<ShareIcon />}
                    onClick={() => setShareDialogOpen(true)}
                  >
                    مشاركة الرحلة
                  </Button>
                  {trip.driverPhone && (
                    <Button
                      variant="outlined"
                      startIcon={<PhoneIcon />}
                      href={`tel:${trip.driverPhone}`}
                    >
                      اتصال بالسائق
                    </Button>
                  )}
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => loadTripData(trip.id)}
                  >
                    تحديث
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Current Status Card */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <NavigationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  الموقع الحالي
                </Typography>
                
                <Box textAlign="center" py={2}>
                  <Avatar sx={{ width: 60, height: 60, mx: 'auto', mb: 2, bgcolor: 'primary.main' }}>
                    <CarIcon sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="body1" gutterBottom>
                    آخر تحديث: {new Date(trip.currentLocation.timestamp).toLocaleTimeString('ar-SA')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    خط العرض: {trip.currentLocation.lat.toFixed(6)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    خط الطول: {trip.currentLocation.lng.toFixed(6)}
                  </Typography>
                </Box>

                {/* Progress Indicator */}
                <Box mt={2}>
                  <Typography variant="body2" gutterBottom>
                    تقدم الرحلة
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={trip.status === 'completed' ? 100 : 
                           trip.status === 'in_transit' ? 70 :
                           trip.status === 'pickup_in_progress' ? 30 : 10}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                {/* Payment Status */}
                <Box mt={2}>
                  <Typography variant="subtitle2" color="text.secondary">حالة الدفع</Typography>
                  <Chip
                    label={trip.paymentStatus === 'paid' ? 'مدفوع' : 'معلق'}
                    color={trip.paymentStatus === 'paid' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>

                {/* Rating */}
                {trip.rating && (
                  <Box mt={2}>
                    <Typography variant="subtitle2" color="text.secondary">التقييم</Typography>
                    <Typography variant="body1">
                      {trip.rating.toFixed(1)} ⭐
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Trip Timeline */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <TimelineIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  سجل الأحداث
                </Typography>
                
                <List>
                  {events.filter(event => event.isVisible).map((event, index) => (
                    <ListItem key={event.id} sx={{ flexDirection: 'column', alignItems: 'flex-start', py: 2 }}>
                      <Box display="flex" alignItems="center" width="100%" mb={1}>
                        <Box sx={{ mr: 2 }}>
                          {getSeverityIcon(event.severity)}
                        </Box>
                        <Box flexGrow={1}>
                          <Typography variant="h6" component="span">
                            {event.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {new Date(event.timestamp).toLocaleString('ar-SA')}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ ml: 5, width: '100%' }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {event.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          بواسطة: {event.actorName} • {event.location.address}
                        </Typography>
                      </Box>
                      {index < events.filter(event => event.isVisible).length - 1 && (
                        <Divider sx={{ width: '100%', mt: 2 }} />
                      )}
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>مشاركة رابط التتبع</DialogTitle>
        <DialogContent>
          {shareableLink ? (
            <Box>
              <Typography variant="body2" gutterBottom>
                يمكن مشاركة هذا الرابط مع العميل لتتبع الرحلة:
              </Typography>
              <TextField
                fullWidth
                value={shareableLink.url}
                InputProps={{
                  readOnly: true,
                }}
                sx={{ mb: 2 }}
              />
              <Typography variant="caption" color="text.secondary">
                الرابط صالح حتى: {new Date(shareableLink.expiresAt).toLocaleString('ar-SA')}
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                عدد مرات الوصول: {shareableLink.accessCount}
              </Typography>
            </Box>
          ) : (
            <Box textAlign="center" py={3}>
              <Typography variant="body1" gutterBottom>
                لم يتم إنشاء رابط مشاركة لهذه الرحلة
              </Typography>
              <Button
                variant="contained"
                onClick={handleCreateShareableLink}
                startIcon={<ShareIcon />}
              >
                إنشاء رابط مشاركة
              </Button>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialogOpen(false)}>إغلاق</Button>
          {shareableLink && (
            <Button
              variant="contained"
              onClick={() => navigator.clipboard.writeText(shareableLink.url)}
            >
              نسخ الرابط
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Map Dialog */}
      <Dialog open={mapDialogOpen} onClose={() => setMapDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>خريطة الرحلة</DialogTitle>
        <DialogContent>
          <Box 
            sx={{ 
              height: 400, 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              backgroundColor: 'grey.100',
              borderRadius: 1
            }}
          >
            <Typography variant="h6" color="text.secondary">
              خريطة تفاعلية لمسار الرحلة
              <br />
              (يتطلب تكامل مع خدمة الخرائط)
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMapDialogOpen(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TripTracking;
