package com.tecnodrive.parcelservice.service.impl;

import com.tecnodrive.parcelservice.dto.ParcelDto;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import com.tecnodrive.parcelservice.repository.ParcelRepository;
import com.tecnodrive.parcelservice.service.EnhancedParcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Enhanced Parcel Service Implementation
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EnhancedParcelServiceImpl implements EnhancedParcelService {

    private final ParcelRepository parcelRepository;

    @Override
    @Transactional
    public ParcelDto createParcel(ParcelDto dto) {
        log.info("Creating new parcel for user: {}", dto.getUserId());
        
        // التحقق من عدم وجود باركود مكرر
        if (parcelRepository.existsByBarcode(dto.getBarcode())) {
            throw new RuntimeException("Barcode already exists: " + dto.getBarcode());
        }
        
        // إنشاء Entity من DTO
        ParcelEntity parcel = ParcelEntity.builder()
                .parcelId(generateParcelId())
                .userId(dto.getUserId())
                .barcode(dto.getBarcode())
                .senderName(dto.getSenderName())
                .receiverName(dto.getReceiverName())
                .senderAddress(dto.getSenderAddress())
                .receiverAddress(dto.getReceiverAddress())
                .weightKg(dto.getWeightKg())
                .dimensions(ParcelEntity.Dimensions.builder()
                        .lengthCm(dto.getDimensions().getLengthCm())
                        .widthCm(dto.getDimensions().getWidthCm())
                        .heightCm(dto.getDimensions().getHeightCm())
                        .build())
                .status(ParcelEntity.ParcelStatus.CREATED)
                .priority(dto.getPriority() != null ? 
                    ParcelEntity.ParcelPriority.valueOf(dto.getPriority()) : 
                    ParcelEntity.ParcelPriority.MEDIUM)
                .fragile(dto.getFragile() != null ? dto.getFragile() : false)
                .insuranceValue(dto.getInsuranceValue())
                .notes(dto.getNotes())
                .createdBy(dto.getUserId())
                .build();
        
        // حساب التكلفة
        parcel.calculateCost();
        
        // حفظ الطرد
        parcel = parcelRepository.save(parcel);
        
        log.info("Parcel created successfully with ID: {}", parcel.getParcelId());
        return convertToDto(parcel);
    }

    @Override
    @Transactional
    public ParcelDto updateParcel(String parcelId, ParcelDto dto) {
        log.info("Updating parcel: {}", parcelId);
        
        ParcelEntity parcel = parcelRepository.findById(parcelId)
                .orElseThrow(() -> new RuntimeException("Parcel not found with ID: " + parcelId));
        
        // التحقق من إمكانية التعديل
        if (!parcel.isEditable()) {
            throw new RuntimeException("Parcel cannot be edited in current status: " + parcel.getStatus());
        }
        
        // تحديث البيانات
        if (dto.getSenderName() != null) parcel.setSenderName(dto.getSenderName());
        if (dto.getReceiverName() != null) parcel.setReceiverName(dto.getReceiverName());
        if (dto.getSenderAddress() != null) parcel.setSenderAddress(dto.getSenderAddress());
        if (dto.getReceiverAddress() != null) parcel.setReceiverAddress(dto.getReceiverAddress());
        if (dto.getWeightKg() != null) parcel.setWeightKg(dto.getWeightKg());
        if (dto.getNotes() != null) parcel.setNotes(dto.getNotes());
        if (dto.getFragile() != null) parcel.setFragile(dto.getFragile());
        if (dto.getInsuranceValue() != null) parcel.setInsuranceValue(dto.getInsuranceValue());
        
        parcel.setUpdatedBy(dto.getUserId());
        
        // إعادة حساب التكلفة
        parcel.calculateCost();
        
        parcel = parcelRepository.save(parcel);
        
        log.info("Parcel updated successfully: {}", parcelId);
        return convertToDto(parcel);
    }

    @Override
    public ParcelDto getParcelById(String parcelId) {
        ParcelEntity parcel = parcelRepository.findById(parcelId)
                .orElseThrow(() -> new RuntimeException("Parcel not found with ID: " + parcelId));
        return convertToDto(parcel);
    }

    @Override
    public ParcelDto getParcelByBarcode(String barcode) {
        ParcelEntity parcel = parcelRepository.findByBarcode(barcode)
                .orElseThrow(() -> new RuntimeException("Parcel not found with barcode: " + barcode));
        return convertToDto(parcel);
    }

    @Override
    public Page<ParcelDto> getParcelsByUser(String userId, Pageable pageable) {
        return parcelRepository.findByUserId(userId, pageable)
                .map(this::convertToDto);
    }

    @Override
    public Page<ParcelDto> getParcelsByStatus(ParcelEntity.ParcelStatus status, Pageable pageable) {
        return parcelRepository.findByStatus(status, pageable)
                .map(this::convertToDto);
    }

    @Override
    @Transactional
    public ParcelDto updateParcelStatus(String parcelId, ParcelEntity.ParcelStatus newStatus, String updatedBy) {
        log.info("Updating parcel status: {} to {}", parcelId, newStatus);
        
        ParcelEntity parcel = parcelRepository.findById(parcelId)
                .orElseThrow(() -> new RuntimeException("Parcel not found with ID: " + parcelId));
        
        parcel.updateStatus(newStatus, updatedBy);
        parcel = parcelRepository.save(parcel);
        
        log.info("Parcel status updated successfully: {} -> {}", parcelId, newStatus);
        return convertToDto(parcel);
    }

    @Override
    public Page<ParcelDto> searchParcels(String searchTerm, Pageable pageable) {
        return parcelRepository.searchParcels(searchTerm, pageable)
                .map(this::convertToDto);
    }

    @Override
    public List<ParcelDto> getAllParcels() {
        return parcelRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void deleteParcel(String parcelId) {
        log.info("Deleting parcel: {}", parcelId);
        
        ParcelEntity parcel = parcelRepository.findById(parcelId)
                .orElseThrow(() -> new RuntimeException("Parcel not found with ID: " + parcelId));
        
        // التحقق من إمكانية الحذف
        if (parcel.getStatus() == ParcelEntity.ParcelStatus.DELIVERED) {
            throw new RuntimeException("Cannot delete delivered parcel");
        }
        
        parcelRepository.delete(parcel);
        log.info("Parcel deleted successfully: {}", parcelId);
    }

    @Override
    @Transactional
    public ParcelDto cancelParcel(String parcelId, String reason, String cancelledBy) {
        log.info("Cancelling parcel: {} with reason: {}", parcelId, reason);
        
        ParcelEntity parcel = parcelRepository.findById(parcelId)
                .orElseThrow(() -> new RuntimeException("Parcel not found with ID: " + parcelId));
        
        // التحقق من إمكانية الإلغاء
        if (!parcel.isCancellable()) {
            throw new RuntimeException("Parcel cannot be cancelled in current status: " + parcel.getStatus());
        }
        
        parcel.updateStatus(ParcelEntity.ParcelStatus.CANCELLED, cancelledBy);
        parcel.setNotes(parcel.getNotes() + "\nCancellation reason: " + reason);
        
        parcel = parcelRepository.save(parcel);
        
        log.info("Parcel cancelled successfully: {}", parcelId);
        return convertToDto(parcel);
    }

    @Override
    public Object getParcelStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // إحصائيات الحالة
        List<Object[]> statusCounts = parcelRepository.countParcelsByStatus();
        Map<String, Long> statusStats = new HashMap<>();
        for (Object[] row : statusCounts) {
            statusStats.put(row[0].toString(), (Long) row[1]);
        }
        statistics.put("statusCounts", statusStats);
        
        // إجمالي الإيرادات
        Double totalRevenue = parcelRepository.getTotalEstimatedRevenue();
        statistics.put("totalRevenue", totalRevenue != null ? totalRevenue : 0.0);
        
        // إجمالي عدد الطرود
        long totalParcels = parcelRepository.count();
        statistics.put("totalParcels", totalParcels);
        
        // الطرود الهشة
        long fragileParcels = parcelRepository.findByFragileTrue().size();
        statistics.put("fragileParcels", fragileParcels);
        
        // الطرود المؤمنة
        long insuredParcels = parcelRepository.findParcelsWithInsurance().size();
        statistics.put("insuredParcels", insuredParcels);
        
        // الطرود المتأخرة
        long delayedParcels = parcelRepository.findDelayedParcels(LocalDateTime.now()).size();
        statistics.put("delayedParcels", delayedParcels);
        
        return statistics;
    }

    /**
     * تحويل Entity إلى DTO
     */
    private ParcelDto convertToDto(ParcelEntity entity) {
        return ParcelDto.builder()
                .parcelId(entity.getParcelId())
                .barcode(entity.getBarcode())
                .senderName(entity.getSenderName())
                .receiverName(entity.getReceiverName())
                .senderAddress(entity.getSenderAddress())
                .receiverAddress(entity.getReceiverAddress())
                .weightKg(entity.getWeightKg())
                .dimensions(ParcelDto.DimensionsDto.builder()
                        .lengthCm(entity.getDimensions().getLengthCm())
                        .widthCm(entity.getDimensions().getWidthCm())
                        .heightCm(entity.getDimensions().getHeightCm())
                        .build())
                .status(entity.getStatus().name())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .estimatedDeliveryDate(entity.getEstimatedDeliveryDate())
                .actualDeliveryDate(entity.getActualDeliveryDate())
                .notes(entity.getNotes())
                .priority(entity.getPriority().name())
                .fragile(entity.getFragile())
                .insuranceValue(entity.getInsuranceValue())
                .build();
    }

    /**
     * توليد معرف طرد فريد
     */
    private String generateParcelId() {
        return "PCL-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
