#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Comprehensive Report Generator
# This script generates detailed platform reports

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("system", "performance", "security", "usage")]
    [string]$ReportType = "system"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$reportDir = "reports"
$reportFile = "$reportDir/tecnodrive_report_$ReportType_$timestamp.html"

# Create reports directory
New-Item -ItemType Directory -Path $reportDir -Force | Out-Null

Write-Host "📊 TECNO DRIVE Report Generator" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Cyan
Write-Host "Report Type: $ReportType" -ForegroundColor Yellow
Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow

function Get-SystemInfo {
    $systemInfo = @{
        Platform = "TECNO DRIVE v1.0.0"
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Host = $env:COMPUTERNAME
        OS = (Get-WmiObject Win32_OperatingSystem).Caption
        TotalMemory = [math]::Round((Get-WmiObject Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
        AvailableMemory = [math]::Round((Get-WmiObject Win32_OperatingSystem).FreePhysicalMemory / 1MB, 2)
        CPUUsage = (Get-WmiObject Win32_Processor | Measure-Object -Property LoadPercentage -Average).Average
    }
    return $systemInfo
}

function Get-ServiceStatus {
    $services = @()
    
    $containerServices = @(
        @{Name="PostgreSQL"; Container="infra-postgres-1"; Port=5432},
        @{Name="Redis"; Container="infra-redis-1"; Port=6379},
        @{Name="Eureka Server"; Container="infra-eureka-server-1"; Port=8761},
        @{Name="Auth Service"; Container="auth-service-fixed"; Port=8081},
        @{Name="API Gateway"; Container="api-gateway-fixed"; Port=8080}
    )
    
    foreach ($service in $containerServices) {
        $status = docker inspect --format='{{.State.Status}}' $service.Container 2>$null
        $uptime = docker inspect --format='{{.State.StartedAt}}' $service.Container 2>$null
        
        $services += @{
            Name = $service.Name
            Status = if ($status -eq "running") { "Running" } else { "Stopped" }
            Port = $service.Port
            Uptime = $uptime
            Container = $service.Container
        }
    }
    
    return $services
}

function Get-DatabaseInfo {
    $databases = @()
    
    $dbList = @(
        "auth_db", "ride_db", "fleet_db", "parcel_db", 
        "payment_db", "notification_db", "financial_db", 
        "hr_db", "analytics_db", "saas_db", "location_db"
    )
    
    foreach ($db in $dbList) {
        try {
            $size = docker exec infra-postgres-1 psql -U postgres -d $db -c "SELECT pg_size_pretty(pg_database_size('$db'));" -t 2>$null
            $tables = docker exec infra-postgres-1 psql -U postgres -d $db -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" -t 2>$null
            
            $databases += @{
                Name = $db
                Size = $size.Trim()
                Tables = $tables.Trim()
                Status = "Active"
            }
        }
        catch {
            $databases += @{
                Name = $db
                Size = "Unknown"
                Tables = "Unknown"
                Status = "Error"
            }
        }
    }
    
    return $databases
}

function Generate-HTMLReport {
    param($SystemInfo, $Services, $Databases)
    
    $html = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TECNO DRIVE - تقرير النظام</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .section { background: white; padding: 25px; margin-bottom: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; margin-bottom: 20px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .info-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #667eea; }
        .info-label { font-weight: bold; color: #555; }
        .info-value { color: #333; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }
        th { background: #667eea; color: white; }
        .status-running { color: #4CAF50; font-weight: bold; }
        .status-stopped { color: #f44336; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 TECNO DRIVE Platform</h1>
        <p>تقرير شامل للنظام - $($SystemInfo.Timestamp)</p>
    </div>
    
    <div class="section">
        <h2>📊 معلومات النظام</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">المنصة</div>
                <div class="info-value">$($SystemInfo.Platform)</div>
            </div>
            <div class="info-item">
                <div class="info-label">الخادم</div>
                <div class="info-value">$($SystemInfo.Host)</div>
            </div>
            <div class="info-item">
                <div class="info-label">نظام التشغيل</div>
                <div class="info-value">$($SystemInfo.OS)</div>
            </div>
            <div class="info-item">
                <div class="info-label">إجمالي الذاكرة</div>
                <div class="info-value">$($SystemInfo.TotalMemory) GB</div>
            </div>
            <div class="info-item">
                <div class="info-label">الذاكرة المتاحة</div>
                <div class="info-value">$($SystemInfo.AvailableMemory) MB</div>
            </div>
            <div class="info-item">
                <div class="info-label">استخدام المعالج</div>
                <div class="info-value">$($SystemInfo.CPUUsage)%</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>🔧 حالة الخدمات</h2>
        <table>
            <thead>
                <tr>
                    <th>اسم الخدمة</th>
                    <th>الحالة</th>
                    <th>المنفذ</th>
                    <th>الحاوية</th>
                </tr>
            </thead>
            <tbody>
"@

    foreach ($service in $Services) {
        $statusClass = if ($service.Status -eq "Running") { "status-running" } else { "status-stopped" }
        $html += @"
                <tr>
                    <td>$($service.Name)</td>
                    <td class="$statusClass">$($service.Status)</td>
                    <td>$($service.Port)</td>
                    <td>$($service.Container)</td>
                </tr>
"@
    }

    $html += @"
            </tbody>
        </table>
    </div>
    
    <div class="section">
        <h2>🗄️ قواعد البيانات</h2>
        <table>
            <thead>
                <tr>
                    <th>اسم قاعدة البيانات</th>
                    <th>الحجم</th>
                    <th>عدد الجداول</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
"@

    foreach ($db in $Databases) {
        $html += @"
                <tr>
                    <td>$($db.Name)</td>
                    <td>$($db.Size)</td>
                    <td>$($db.Tables)</td>
                    <td class="status-running">$($db.Status)</td>
                </tr>
"@
    }

    $html += @"
            </tbody>
        </table>
    </div>
    
    <div class="footer">
        <p>© 2024 TECNO DRIVE Platform - تم إنشاء التقرير تلقائياً</p>
    </div>
</body>
</html>
"@

    return $html
}

# Generate report
Write-Host "🔄 جاري إنشاء التقرير..." -ForegroundColor Blue

$systemInfo = Get-SystemInfo
$services = Get-ServiceStatus
$databases = Get-DatabaseInfo

$htmlReport = Generate-HTMLReport -SystemInfo $systemInfo -Services $services -Databases $databases

# Save report
$htmlReport | Out-File -FilePath $reportFile -Encoding UTF8

Write-Host "✅ تم إنشاء التقرير بنجاح!" -ForegroundColor Green
Write-Host "📁 مسار التقرير: $reportFile" -ForegroundColor Cyan

# Open report in browser
Start-Process $reportFile
