package com.tecnodrive.financialservice.repository;

import com.tecnodrive.financialservice.entity.Budget;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Budget Repository
 * 
 * Data access layer for Budget entities
 */
@Repository
public interface BudgetRepository extends JpaRepository<Budget, UUID> {

    /**
     * Find budgets by company
     */
    List<Budget> findByCompanyId(String companyId);

    /**
     * Find budgets by company with pagination
     */
    Page<Budget> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find budgets by category
     */
    List<Budget> findByCategory(Budget.BudgetCategory category);

    /**
     * Find budgets by company and category
     */
    List<Budget> findByCompanyIdAndCategory(String companyId, Budget.BudgetCategory category);

    /**
     * Find budgets by status
     */
    List<Budget> findByStatus(Budget.BudgetStatus status);

    /**
     * Find budgets by company and status
     */
    List<Budget> findByCompanyIdAndStatus(String companyId, Budget.BudgetStatus status);

    /**
     * Find budgets by owner
     */
    List<Budget> findByOwnerId(String ownerId);

    /**
     * Find budgets by period
     */
    List<Budget> findByPeriod(Budget.BudgetPeriod period);

    /**
     * Find active budgets
     */
    @Query("SELECT b FROM Budget b WHERE b.status = 'ACTIVE'")
    List<Budget> findActiveBudgets();

    /**
     * Find active budgets by company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE'")
    List<Budget> findActiveBudgetsByCompany(@Param("companyId") String companyId);

    /**
     * Find budgets for current period
     */
    @Query("SELECT b FROM Budget b WHERE :currentDate BETWEEN b.periodStartDate AND b.periodEndDate")
    List<Budget> findBudgetsForCurrentPeriod(@Param("currentDate") LocalDate currentDate);

    /**
     * Find budgets for current period by company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND :currentDate BETWEEN b.periodStartDate AND b.periodEndDate")
    List<Budget> findBudgetsForCurrentPeriodByCompany(@Param("companyId") String companyId, @Param("currentDate") LocalDate currentDate);

    /**
     * Find budgets by date range
     */
    @Query("SELECT b FROM Budget b WHERE b.periodStartDate <= :endDate AND b.periodEndDate >= :startDate")
    List<Budget> findBudgetsByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find budgets over threshold
     */
    @Query("SELECT b FROM Budget b WHERE b.actualSpent >= (b.allocatedAmount * b.alertThreshold) AND b.alertsEnabled = true")
    List<Budget> findBudgetsOverThreshold();

    /**
     * Find budgets over threshold by company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND b.actualSpent >= (b.allocatedAmount * b.alertThreshold) AND b.alertsEnabled = true")
    List<Budget> findBudgetsOverThresholdByCompany(@Param("companyId") String companyId);

    /**
     * Find exceeded budgets
     */
    @Query("SELECT b FROM Budget b WHERE b.actualSpent > b.allocatedAmount")
    List<Budget> findExceededBudgets();

    /**
     * Find exceeded budgets by company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND b.actualSpent > b.allocatedAmount")
    List<Budget> findExceededBudgetsByCompany(@Param("companyId") String companyId);

    /**
     * Find budgets needing alerts
     */
    @Query("SELECT b FROM Budget b WHERE b.actualSpent >= (b.allocatedAmount * b.alertThreshold) AND b.alertsEnabled = true AND (b.lastAlertSent IS NULL OR b.lastAlertSent < :alertCutoff)")
    List<Budget> findBudgetsNeedingAlerts(@Param("alertCutoff") java.time.Instant alertCutoff);

    /**
     * Find child budgets
     */
    List<Budget> findByParentBudgetId(UUID parentBudgetId);

    /**
     * Find root budgets (no parent)
     */
    @Query("SELECT b FROM Budget b WHERE b.parentBudgetId IS NULL")
    List<Budget> findRootBudgets();

    /**
     * Find root budgets by company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND b.parentBudgetId IS NULL")
    List<Budget> findRootBudgetsByCompany(@Param("companyId") String companyId);

    /**
     * Calculate total allocated amount by company
     */
    @Query("SELECT COALESCE(SUM(b.allocatedAmount), 0) FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE'")
    BigDecimal calculateTotalAllocatedAmount(@Param("companyId") String companyId);

    /**
     * Calculate total spent amount by company
     */
    @Query("SELECT COALESCE(SUM(b.actualSpent), 0) FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE'")
    BigDecimal calculateTotalSpentAmount(@Param("companyId") String companyId);

    /**
     * Calculate total available amount by company
     */
    @Query("SELECT COALESCE(SUM(b.availableAmount), 0) FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE'")
    BigDecimal calculateTotalAvailableAmount(@Param("companyId") String companyId);

    /**
     * Get budget statistics by category
     */
    @Query("SELECT b.category, COUNT(b), COALESCE(SUM(b.allocatedAmount), 0), COALESCE(SUM(b.actualSpent), 0) FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE' GROUP BY b.category")
    List<Object[]> getBudgetStatisticsByCategory(@Param("companyId") String companyId);

    /**
     * Get budget statistics by period
     */
    @Query("SELECT b.period, COUNT(b), COALESCE(SUM(b.allocatedAmount), 0), COALESCE(SUM(b.actualSpent), 0) FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE' GROUP BY b.period")
    List<Object[]> getBudgetStatisticsByPeriod(@Param("companyId") String companyId);

    /**
     * Get budget utilization summary
     */
    @Query("SELECT b.budgetName, b.allocatedAmount, b.actualSpent, b.availableAmount, (b.actualSpent / b.allocatedAmount * 100) as utilizationPercentage FROM Budget b WHERE b.companyId = :companyId AND b.status = 'ACTIVE' ORDER BY utilizationPercentage DESC")
    List<Object[]> getBudgetUtilizationSummary(@Param("companyId") String companyId);

    /**
     * Search budgets by name
     */
    @Query("SELECT b FROM Budget b WHERE LOWER(b.budgetName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Budget> searchBudgetsByName(@Param("searchTerm") String searchTerm);

    /**
     * Search budgets by name and company
     */
    @Query("SELECT b FROM Budget b WHERE b.companyId = :companyId AND LOWER(b.budgetName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Budget> searchBudgetsByNameAndCompany(@Param("companyId") String companyId, @Param("searchTerm") String searchTerm);

    /**
     * Count budgets by status
     */
    long countByStatus(Budget.BudgetStatus status);

    /**
     * Count budgets by company and status
     */
    long countByCompanyIdAndStatus(String companyId, Budget.BudgetStatus status);

    /**
     * Count budgets by category
     */
    long countByCategory(Budget.BudgetCategory category);
}
