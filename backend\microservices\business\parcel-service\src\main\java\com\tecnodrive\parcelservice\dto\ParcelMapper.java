package com.tecnodrive.parcelservice.dto;

import com.tecnodrive.parcelservice.entity.ParcelEntity;
import com.tecnodrive.parcelservice.entity.Parcel;
import com.tecnodrive.parcelservice.entity.Delivery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * Parcel Mapper
 *
 * Maps between DTOs and Entities for Parcel operations
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ParcelMapper {

    // Enhanced ParcelEntity mappings
    @Mapping(target = "parcelId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ParcelEntity toEntity(ParcelDto dto);

    ParcelDto toDto(ParcelEntity entity);

    // Legacy Parcel mappings
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "trackingNumber", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(source = "senderId", target = "senderId")
    @Mapping(source = "receiverId", target = "receiverId")
    @Mapping(source = "weight", target = "weight")
    @Mapping(source = "dimensions", target = "dimensions")
    Parcel toLegacyEntity(ParcelRequestDto dto);

    @Mapping(source = "id", target = "id")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "trackingNumber", target = "trackingNumber")
    @Mapping(source = "createdAt", target = "createdAt")
    ParcelResponseDto toLegacyDto(Parcel parcel);

    // Delivery mappings
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "assignedAt", ignore = true)
    @Mapping(target = "pickedUpAt", ignore = true)
    @Mapping(target = "deliveredAt", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(source = "parcelId", target = "parcelId")
    @Mapping(source = "driverId", target = "driverId")
    Delivery toDeliveryEntity(DeliveryDto dto);
}
