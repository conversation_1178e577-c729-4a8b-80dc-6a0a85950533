-- Data Archival Strategy for TecnoDrive Platform
-- Implements automated data archival and retention policies

-- =====================================================
-- 1. ARCHIVE SCHEMA AND TABLES
-- =====================================================

-- Create archive schema
CREATE SCHEMA IF NOT EXISTS archive;

-- Create archive tables with same structure as main tables
CREATE TABLE archive.location_data (
    LIKE location_data_partitioned INCLUDING ALL
);

CREATE TABLE archive.rides (
    LIKE rides_partitioned INCLUDING ALL
);

CREATE TABLE archive.payments (
    LIKE payments_partitioned INCLUDING ALL
);

CREATE TABLE archive.audit_log (
    LIKE audit_log_partitioned INCLUDING ALL
);

-- Add archival metadata columns
ALTER TABLE archive.location_data ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE archive.rides ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE archive.payments ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE archive.audit_log ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create indexes on archive tables
CREATE INDEX IF NOT EXISTS idx_archive_location_data_archived_at ON archive.location_data (archived_at);
CREATE INDEX IF NOT EXISTS idx_archive_location_data_driver ON archive.location_data (driver_id, created_at);

CREATE INDEX IF NOT EXISTS idx_archive_rides_archived_at ON archive.rides (archived_at);
CREATE INDEX IF NOT EXISTS idx_archive_rides_passenger ON archive.rides (passenger_id, created_at);

CREATE INDEX IF NOT EXISTS idx_archive_payments_archived_at ON archive.payments (archived_at);
CREATE INDEX IF NOT EXISTS idx_archive_payments_user ON archive.payments (user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_archive_audit_log_archived_at ON archive.audit_log (archived_at);
CREATE INDEX IF NOT EXISTS idx_archive_audit_log_user ON archive.audit_log (user_id, created_at);

-- =====================================================
-- 2. ARCHIVAL FUNCTIONS
-- =====================================================

-- Function to archive old location data
CREATE OR REPLACE FUNCTION archive_location_data(cutoff_date DATE DEFAULT CURRENT_DATE - INTERVAL '90 days')
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER := 0;
    batch_size INTEGER := 10000;
    total_archived INTEGER := 0;
BEGIN
    LOOP
        -- Move data in batches
        WITH archived_batch AS (
            DELETE FROM location_data_partitioned 
            WHERE created_at < cutoff_date
            AND id IN (
                SELECT id FROM location_data_partitioned 
                WHERE created_at < cutoff_date 
                LIMIT batch_size
            )
            RETURNING *
        )
        INSERT INTO archive.location_data 
        SELECT *, NOW() as archived_at FROM archived_batch;
        
        GET DIAGNOSTICS archived_count = ROW_COUNT;
        total_archived := total_archived + archived_count;
        
        -- Exit if no more rows to archive
        EXIT WHEN archived_count = 0;
        
        -- Log progress
        RAISE NOTICE 'Archived % location records, total: %', archived_count, total_archived;
        
        -- Commit batch and pause briefly
        COMMIT;
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Location data archival completed. Total archived: %', total_archived;
    RETURN total_archived;
END;
$$ LANGUAGE plpgsql;

-- Function to archive old rides
CREATE OR REPLACE FUNCTION archive_rides(cutoff_date DATE DEFAULT CURRENT_DATE - INTERVAL '2 years')
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER := 0;
    batch_size INTEGER := 5000;
    total_archived INTEGER := 0;
BEGIN
    LOOP
        WITH archived_batch AS (
            DELETE FROM rides_partitioned 
            WHERE created_at < cutoff_date
            AND status IN ('COMPLETED', 'CANCELLED')
            AND id IN (
                SELECT id FROM rides_partitioned 
                WHERE created_at < cutoff_date 
                AND status IN ('COMPLETED', 'CANCELLED')
                LIMIT batch_size
            )
            RETURNING *
        )
        INSERT INTO archive.rides 
        SELECT *, NOW() as archived_at FROM archived_batch;
        
        GET DIAGNOSTICS archived_count = ROW_COUNT;
        total_archived := total_archived + archived_count;
        
        EXIT WHEN archived_count = 0;
        
        RAISE NOTICE 'Archived % ride records, total: %', archived_count, total_archived;
        
        COMMIT;
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Rides archival completed. Total archived: %', total_archived;
    RETURN total_archived;
END;
$$ LANGUAGE plpgsql;

-- Function to archive old payments
CREATE OR REPLACE FUNCTION archive_payments(cutoff_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 years')
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER := 0;
    batch_size INTEGER := 5000;
    total_archived INTEGER := 0;
BEGIN
    LOOP
        WITH archived_batch AS (
            DELETE FROM payments_partitioned 
            WHERE created_at < cutoff_date
            AND status IN ('COMPLETED', 'FAILED', 'REFUNDED')
            AND id IN (
                SELECT id FROM payments_partitioned 
                WHERE created_at < cutoff_date 
                AND status IN ('COMPLETED', 'FAILED', 'REFUNDED')
                LIMIT batch_size
            )
            RETURNING *
        )
        INSERT INTO archive.payments 
        SELECT *, NOW() as archived_at FROM archived_batch;
        
        GET DIAGNOSTICS archived_count = ROW_COUNT;
        total_archived := total_archived + archived_count;
        
        EXIT WHEN archived_count = 0;
        
        RAISE NOTICE 'Archived % payment records, total: %', archived_count, total_archived;
        
        COMMIT;
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Payments archival completed. Total archived: %', total_archived;
    RETURN total_archived;
END;
$$ LANGUAGE plpgsql;

-- Function to archive old audit logs
CREATE OR REPLACE FUNCTION archive_audit_logs(cutoff_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 year')
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER := 0;
    batch_size INTEGER := 10000;
    total_archived INTEGER := 0;
BEGIN
    LOOP
        WITH archived_batch AS (
            DELETE FROM audit_log_partitioned 
            WHERE created_at < cutoff_date
            AND id IN (
                SELECT id FROM audit_log_partitioned 
                WHERE created_at < cutoff_date 
                LIMIT batch_size
            )
            RETURNING *
        )
        INSERT INTO archive.audit_log 
        SELECT *, NOW() as archived_at FROM archived_batch;
        
        GET DIAGNOSTICS archived_count = ROW_COUNT;
        total_archived := total_archived + archived_count;
        
        EXIT WHEN archived_count = 0;
        
        RAISE NOTICE 'Archived % audit log records, total: %', archived_count, total_archived;
        
        COMMIT;
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Audit logs archival completed. Total archived: %', total_archived;
    RETURN total_archived;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. MASTER ARCHIVAL FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION run_data_archival()
RETURNS TABLE(
    table_name TEXT,
    archived_count INTEGER,
    execution_time INTERVAL
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    count_result INTEGER;
BEGIN
    -- Archive location data
    start_time := clock_timestamp();
    SELECT archive_location_data() INTO count_result;
    end_time := clock_timestamp();
    
    table_name := 'location_data';
    archived_count := count_result;
    execution_time := end_time - start_time;
    RETURN NEXT;
    
    -- Archive rides
    start_time := clock_timestamp();
    SELECT archive_rides() INTO count_result;
    end_time := clock_timestamp();
    
    table_name := 'rides';
    archived_count := count_result;
    execution_time := end_time - start_time;
    RETURN NEXT;
    
    -- Archive payments
    start_time := clock_timestamp();
    SELECT archive_payments() INTO count_result;
    end_time := clock_timestamp();
    
    table_name := 'payments';
    archived_count := count_result;
    execution_time := end_time - start_time;
    RETURN NEXT;
    
    -- Archive audit logs
    start_time := clock_timestamp();
    SELECT archive_audit_logs() INTO count_result;
    end_time := clock_timestamp();
    
    table_name := 'audit_log';
    archived_count := count_result;
    execution_time := end_time - start_time;
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. ARCHIVE COMPRESSION AND OPTIMIZATION
-- =====================================================

-- Function to compress archive tables
CREATE OR REPLACE FUNCTION compress_archive_tables()
RETURNS VOID AS $$
BEGIN
    -- Vacuum and analyze archive tables
    VACUUM ANALYZE archive.location_data;
    VACUUM ANALYZE archive.rides;
    VACUUM ANALYZE archive.payments;
    VACUUM ANALYZE archive.audit_log;
    
    -- Reindex archive tables
    REINDEX TABLE archive.location_data;
    REINDEX TABLE archive.rides;
    REINDEX TABLE archive.payments;
    REINDEX TABLE archive.audit_log;
    
    RAISE NOTICE 'Archive tables compressed and optimized';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. ARCHIVE EXPORT FUNCTIONS
-- =====================================================

-- Function to export archive data to CSV
CREATE OR REPLACE FUNCTION export_archive_to_csv(
    table_name TEXT,
    export_path TEXT,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    sql_query TEXT;
    where_clause TEXT := '';
BEGIN
    -- Build where clause for date filtering
    IF date_from IS NOT NULL THEN
        where_clause := where_clause || ' AND archived_at >= ''' || date_from || '''';
    END IF;
    
    IF date_to IS NOT NULL THEN
        where_clause := where_clause || ' AND archived_at <= ''' || date_to || '''';
    END IF;
    
    -- Remove leading AND
    IF where_clause != '' THEN
        where_clause := 'WHERE ' || substring(where_clause from 5);
    END IF;
    
    -- Build and execute COPY command
    sql_query := format('COPY (SELECT * FROM archive.%I %s) TO %L WITH CSV HEADER',
                       table_name, where_clause, export_path);
    
    EXECUTE sql_query;
    
    RAISE NOTICE 'Exported archive.% to %', table_name, export_path;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. MONITORING AND REPORTING
-- =====================================================

-- View for archive statistics
CREATE OR REPLACE VIEW archive_statistics AS
SELECT 
    'location_data' as table_name,
    count(*) as record_count,
    min(created_at) as oldest_record,
    max(created_at) as newest_record,
    pg_size_pretty(pg_total_relation_size('archive.location_data')) as table_size
FROM archive.location_data
UNION ALL
SELECT 
    'rides' as table_name,
    count(*) as record_count,
    min(created_at) as oldest_record,
    max(created_at) as newest_record,
    pg_size_pretty(pg_total_relation_size('archive.rides')) as table_size
FROM archive.rides
UNION ALL
SELECT 
    'payments' as table_name,
    count(*) as record_count,
    min(created_at) as oldest_record,
    max(created_at) as newest_record,
    pg_size_pretty(pg_total_relation_size('archive.payments')) as table_size
FROM archive.payments
UNION ALL
SELECT 
    'audit_log' as table_name,
    count(*) as record_count,
    min(created_at) as oldest_record,
    max(created_at) as newest_record,
    pg_size_pretty(pg_total_relation_size('archive.audit_log')) as table_size
FROM archive.audit_log;

-- Function to generate archival report
CREATE OR REPLACE FUNCTION generate_archival_report()
RETURNS TABLE(
    summary_type TEXT,
    details JSONB
) AS $$
BEGIN
    -- Main tables summary
    summary_type := 'main_tables';
    SELECT jsonb_agg(
        jsonb_build_object(
            'table_name', t.table_name,
            'record_count', t.record_count,
            'table_size', t.table_size
        )
    ) INTO details
    FROM (
        SELECT 'location_data_partitioned' as table_name,
               (SELECT count(*) FROM location_data_partitioned) as record_count,
               pg_size_pretty(pg_total_relation_size('location_data_partitioned')) as table_size
        UNION ALL
        SELECT 'rides_partitioned' as table_name,
               (SELECT count(*) FROM rides_partitioned) as record_count,
               pg_size_pretty(pg_total_relation_size('rides_partitioned')) as table_size
        UNION ALL
        SELECT 'payments_partitioned' as table_name,
               (SELECT count(*) FROM payments_partitioned) as record_count,
               pg_size_pretty(pg_total_relation_size('payments_partitioned')) as table_size
    ) t;
    RETURN NEXT;
    
    -- Archive tables summary
    summary_type := 'archive_tables';
    SELECT jsonb_agg(
        jsonb_build_object(
            'table_name', table_name,
            'record_count', record_count,
            'oldest_record', oldest_record,
            'newest_record', newest_record,
            'table_size', table_size
        )
    ) INTO details
    FROM archive_statistics;
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. PERMISSIONS AND SECURITY
-- =====================================================

-- Create archive role
CREATE ROLE archive_manager;

-- Grant permissions
GRANT USAGE ON SCHEMA archive TO archive_manager;
GRANT SELECT, INSERT, DELETE ON ALL TABLES IN SCHEMA archive TO archive_manager;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO archive_manager;

-- Grant read-only access to archive for reporting
GRANT USAGE ON SCHEMA archive TO application_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA archive TO application_readonly;
GRANT SELECT ON archive_statistics TO application_readonly;

-- Comments for documentation
COMMENT ON SCHEMA archive IS 'Schema containing archived data from main tables';
COMMENT ON FUNCTION archive_location_data IS 'Archive location data older than specified cutoff date';
COMMENT ON FUNCTION archive_rides IS 'Archive completed/cancelled rides older than specified cutoff date';
COMMENT ON FUNCTION archive_payments IS 'Archive completed/failed payments older than specified cutoff date';
COMMENT ON FUNCTION run_data_archival IS 'Run complete data archival process for all tables';
COMMENT ON VIEW archive_statistics IS 'Statistics and summary information for archived data';
