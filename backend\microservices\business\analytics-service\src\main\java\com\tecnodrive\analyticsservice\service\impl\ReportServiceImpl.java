package com.tecnodrive.analyticsservice.service.impl;

import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.tecnodrive.analyticsservice.dto.ReportRequest;
import com.tecnodrive.analyticsservice.dto.ReportResponse;
import com.tecnodrive.analyticsservice.service.ReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

/**
 * Report Service Implementation
 *
 * Implements business logic for report generation and analytics
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    @Override
    @Cacheable(value = "reports", key = "#request.reportType + '_' + #request.companyId + '_' + #request.period")
    public ReportResponse generateReport(ReportRequest request) {
        log.info("Generating report: {} for company: {} with period: {}", 
                request.getReportType(), request.getCompanyId(), request.getPeriod());
        
        long startTime = System.currentTimeMillis();
        
        try {
            validateReportRequest(request);
            
            ReportResponse response = switch (request.getReportType()) {
                case FINANCIAL_SUMMARY -> generateFinancialReport(request);
                case REVENUE_ANALYSIS -> generateRevenueAnalysisReport(request);
                case EXPENSE_BREAKDOWN -> generateExpenseBreakdownReport(request);
                case RIDE_ANALYTICS -> generateRideAnalyticsReport(request);
                case DRIVER_PERFORMANCE -> generateDriverPerformanceReport(request);
                case CUSTOMER_ANALYTICS -> generateCustomerAnalyticsReport(request);
                case VEHICLE_UTILIZATION -> generateVehicleUtilizationReport(request);
                case GEOGRAPHIC_ANALYSIS -> generateGeographicAnalysisReport(request);
                case OPERATIONAL_METRICS -> generateOperationalMetricsReport(request);
                case USER_ENGAGEMENT -> generateUserEngagementReport(request);
                case PAYMENT_ANALYTICS -> generatePaymentAnalyticsReport(request);
                case NOTIFICATION_METRICS -> generateNotificationMetricsReport(request);
                default -> generateDefaultReport(request);
            };
            
            // Set metadata
            long executionTime = System.currentTimeMillis() - startTime;
            response.setMetadata(createReportMetadata(request, executionTime));
            
            log.info("Report generated successfully in {}ms", executionTime);
            return response;
            
        } catch (Exception e) {
            log.error("Error generating report: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate report: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] exportToCsv(ReportResponse report) {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             CSVPrinter printer = new CSVPrinter(
                     new OutputStreamWriter(out, StandardCharsets.UTF_8),
                     CSVFormat.DEFAULT.builder().setHeader("Key", "Value").build())) {
            
            // Export summary data
            if (report.getSummary() != null) {
                for (Map.Entry<String, Object> entry : report.getSummary().entrySet()) {
                    printer.printRecord(entry.getKey(), entry.getValue());
                }
            }
            
            // Export main data
            if (report.getData() != null) {
                for (Map.Entry<String, Object> entry : report.getData().entrySet()) {
                    printer.printRecord(entry.getKey(), entry.getValue());
                }
            }
            
            printer.flush();
            return out.toByteArray();
            
        } catch (Exception e) {
            log.error("Error exporting to CSV: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to export to CSV", e);
        }
    }

    @Override
    public byte[] exportToPdf(ReportResponse report) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdf = new PdfDocument(writer);
            Document doc = new Document(pdf);

            // Add title
            if (report.getMetadata() != null && report.getMetadata().getTitle() != null) {
                doc.add(new Paragraph(report.getMetadata().getTitle()).setFontSize(18).setBold());
            }
            
            // Add period
            if (report.getMetadata() != null && report.getMetadata().getPeriod() != null) {
                doc.add(new Paragraph("Period: " + report.getMetadata().getPeriod()));
            }

            // Add summary table
            if (report.getSummary() != null && !report.getSummary().isEmpty()) {
                doc.add(new Paragraph("Summary").setFontSize(14).setBold());
                Table summaryTable = new Table(2);
                summaryTable.addHeaderCell("Metric");
                summaryTable.addHeaderCell("Value");
                
                report.getSummary().forEach((key, value) -> {
                    summaryTable.addCell(key);
                    summaryTable.addCell(value != null ? value.toString() : "");
                });
                
                doc.add(summaryTable);
            }

            doc.close();
            return baos.toByteArray();
            
        } catch (Exception e) {
            log.error("Error exporting to PDF: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to export to PDF", e);
        }
    }

    @Override
    public byte[] exportToExcel(ReportResponse report) {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("Analytics Report");
            
            // Create header row
            var headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("Metric");
            headerRow.createCell(1).setCellValue("Value");

            int rowIndex = 1;
            
            // Add summary data
            if (report.getSummary() != null) {
                for (Map.Entry<String, Object> entry : report.getSummary().entrySet()) {
                    var row = sheet.createRow(rowIndex++);
                    row.createCell(0).setCellValue(entry.getKey());
                    
                    Object value = entry.getValue();
                    if (value instanceof Number) {
                        row.createCell(1).setCellValue(((Number) value).doubleValue());
                    } else {
                        row.createCell(1).setCellValue(value != null ? value.toString() : "");
                    }
                }
            }
            
            // Auto-size columns
            sheet.autoSizeColumn(0);
            sheet.autoSizeColumn(1);
            
            workbook.write(out);
            return out.toByteArray();
            
        } catch (Exception e) {
            log.error("Error exporting to Excel: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to export to Excel", e);
        }
    }

    @Override
    public String exportToHtml(ReportResponse report) {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html><html><head><title>Analytics Report</title>");
        html.append("<style>table{border-collapse:collapse;width:100%;}th,td{border:1px solid #ddd;padding:8px;text-align:left;}th{background-color:#f2f2f2;}</style>");
        html.append("</head><body>");
        
        // Add title
        if (report.getMetadata() != null && report.getMetadata().getTitle() != null) {
            html.append("<h1>").append(report.getMetadata().getTitle()).append("</h1>");
        }
        
        // Add period
        if (report.getMetadata() != null && report.getMetadata().getPeriod() != null) {
            html.append("<p><strong>Period:</strong> ").append(report.getMetadata().getPeriod()).append("</p>");
        }
        
        // Add summary table
        if (report.getSummary() != null && !report.getSummary().isEmpty()) {
            html.append("<h2>Summary</h2>");
            html.append("<table><thead><tr><th>Metric</th><th>Value</th></tr></thead><tbody>");
            
            report.getSummary().forEach((key, value) -> {
                html.append("<tr><td>").append(key).append("</td><td>")
                    .append(value != null ? value.toString() : "").append("</td></tr>");
            });
            
            html.append("</tbody></table>");
        }
        
        html.append("</body></html>");
        return html.toString();
    }

    @Override
    @Cacheable(value = "dashboard", key = "#companyId")
    public ReportResponse getDashboardMetrics(String companyId) {
        log.debug("Getting dashboard metrics for company: {}", companyId);
        
        // Mock dashboard data - replace with actual service calls
        Map<String, Object> dashboardData = new HashMap<>();
        dashboardData.put("totalRevenue", 125000.50);
        dashboardData.put("totalRides", 1250);
        dashboardData.put("activeDrivers", 45);
        dashboardData.put("customerSatisfaction", 4.7);
        
        return ReportResponse.builder()
                .data(dashboardData)
                .metadata(ReportResponse.ReportMetadata.builder()
                        .reportType(ReportRequest.ReportType.OPERATIONAL_METRICS)
                        .title("Dashboard Metrics")
                        .companyId(companyId)
                        .generatedAt(Instant.now())
                        .build())
                .build();
    }

    @Override
    public ReportResponse.FinancialSummary getFinancialSummary(String companyId, ReportRequest.Period period) {
        // Mock financial data - replace with actual service calls
        return ReportResponse.FinancialSummary.builder()
                .totalRevenue(125000.50)
                .totalExpenses(85000.25)
                .netProfit(39999.25)
                .profitMargin(32.0)
                .averageTransactionValue(45.50)
                .totalTransactions(2750)
                .revenueByCategory(Map.of("Rides", 95000.0, "Deliveries", 30000.5))
                .expensesByCategory(Map.of("Fuel", 25000.0, "Maintenance", 15000.0, "Insurance", 10000.25))
                .build();
    }

    @Override
    public ReportResponse.OperationalMetrics getOperationalMetrics(String companyId, ReportRequest.Period period) {
        // Mock operational data - replace with actual service calls
        return ReportResponse.OperationalMetrics.builder()
                .totalRides(1250)
                .totalDeliveries(450)
                .averageRideDistance(8.5)
                .averageRideDuration(18.5)
                .customerSatisfactionScore(4.7)
                .activeDrivers(45)
                .activeVehicles(38)
                .vehicleUtilizationRate(85.5)
                .ridesByHour(generateHourlyData())
                .ridesByDay(generateDailyData())
                .build();
    }

    @Override
    public ReportResponse.UserAnalytics getUserAnalytics(String companyId, ReportRequest.Period period) {
        // Mock user analytics data - replace with actual service calls
        return ReportResponse.UserAnalytics.builder()
                .totalUsers(5500)
                .activeUsers(3200)
                .newUsers(250)
                .userRetentionRate(78.5)
                .averageSessionDuration(12.5)
                .usersByRegion(Map.of("North", 2200L, "South", 1800L, "East", 900L, "West", 600L))
                .usersByAge(Map.of("18-25", 1500L, "26-35", 2200L, "36-45", 1200L, "46+", 600L))
                .engagementMetrics(Map.of("Daily Active", 65.5, "Weekly Active", 78.2, "Monthly Active", 85.8))
                .build();
    }

    @Override
    public ReportResponse generateCustomReport(String companyId, String query, ReportRequest.Format format) {
        // Implementation for custom SQL queries
        log.info("Generating custom report for company: {} with query: {}", companyId, query);
        
        // Mock implementation - replace with actual query execution
        Map<String, Object> customData = new HashMap<>();
        customData.put("query", query);
        customData.put("results", "Custom query results would go here");
        
        return ReportResponse.builder()
                .data(customData)
                .metadata(ReportResponse.ReportMetadata.builder()
                        .reportType(ReportRequest.ReportType.CUSTOM_REPORT)
                        .title("Custom Report")
                        .companyId(companyId)
                        .generatedAt(Instant.now())
                        .format(format)
                        .build())
                .build();
    }

    @Override
    public void scheduleReport(ReportRequest request, String cronExpression) {
        log.info("Scheduling report: {} with cron: {}", request.getReportType(), cronExpression);
        // Implementation for scheduled reports
    }

    @Override
    public List<ReportRequest.ReportType> getAvailableReportTypes(String companyId) {
        // Return all available report types - could be filtered based on company subscription
        return Arrays.asList(ReportRequest.ReportType.values());
    }

    @Override
    public void validateReportRequest(ReportRequest request) {
        if (request.getReportType() == null) {
            throw new IllegalArgumentException("Report type is required");
        }
        
        if (request.getCompanyId() == null || request.getCompanyId().trim().isEmpty()) {
            throw new IllegalArgumentException("Company ID is required");
        }
        
        if (request.getPeriod() == ReportRequest.Period.CUSTOM) {
            if (request.getStartDate() == null || request.getEndDate() == null) {
                throw new IllegalArgumentException("Start date and end date are required for custom period");
            }
            
            if (request.getStartDate().isAfter(request.getEndDate())) {
                throw new IllegalArgumentException("Start date cannot be after end date");
            }
        }
    }

    // Private helper methods
    private ReportResponse generateFinancialReport(ReportRequest request) {
        // Implementation for financial report generation
        Map<String, Object> data = new HashMap<>();
        data.put("revenue", 125000.50);
        data.put("expenses", 85000.25);
        data.put("profit", 39999.25);
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("Total Revenue", "$125,000.50");
        summary.put("Total Expenses", "$85,000.25");
        summary.put("Net Profit", "$39,999.25");
        summary.put("Profit Margin", "32.0%");
        
        return ReportResponse.builder()
                .data(data)
                .summary(summary)
                .build();
    }

    private ReportResponse generateRevenueAnalysisReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateExpenseBreakdownReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateRideAnalyticsReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateDriverPerformanceReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateCustomerAnalyticsReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateVehicleUtilizationReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateGeographicAnalysisReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateOperationalMetricsReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateUserEngagementReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generatePaymentAnalyticsReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateNotificationMetricsReport(ReportRequest request) {
        // Mock implementation
        return generateDefaultReport(request);
    }

    private ReportResponse generateDefaultReport(ReportRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("reportType", request.getReportType().toString());
        data.put("message", "Sample report data for " + request.getReportType());
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("Total Records", 100);
        summary.put("Generated At", Instant.now().toString());
        
        return ReportResponse.builder()
                .data(data)
                .summary(summary)
                .build();
    }

    private ReportResponse.ReportMetadata createReportMetadata(ReportRequest request, long executionTime) {
        return ReportResponse.ReportMetadata.builder()
                .reportId(UUID.randomUUID().toString())
                .reportType(request.getReportType())
                .title(request.getTitle() != null ? request.getTitle() : request.getReportType().toString())
                .description(request.getDescription())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .period(request.getPeriod() != null ? request.getPeriod().toString() : "CUSTOM")
                .companyId(request.getCompanyId())
                .requestedBy(request.getRequestedBy())
                .generatedAt(Instant.now())
                .executionTimeMs(executionTime)
                .format(request.getFormat())
                .build();
    }

    private Map<String, Long> generateHourlyData() {
        Map<String, Long> hourlyData = new HashMap<>();
        for (int i = 0; i < 24; i++) {
            hourlyData.put(String.format("%02d:00", i), (long) (Math.random() * 100));
        }
        return hourlyData;
    }

    private Map<String, Long> generateDailyData() {
        Map<String, Long> dailyData = new HashMap<>();
        String[] days = {"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};
        for (String day : days) {
            dailyData.put(day, (long) (Math.random() * 200 + 50));
        }
        return dailyData;
    }
}






