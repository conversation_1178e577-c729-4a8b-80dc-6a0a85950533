import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
  IconButton,
  <PERSON>lapse,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as HealthyIcon,
  Error as UnhealthyIcon,
  Refresh as RefreshIcon,
  Cloud as CloudIcon,
  Storage as MockIcon,
} from '@mui/icons-material';
import { smartApiService } from '../../services/smartApiService';

interface ServiceStatus {
  name: string;
  url: string;
  isHealthy: boolean;
  lastChecked: Date;
  port: number;
}

const ServiceStatusIndicator: React.FC = () => {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadServiceStatuses();
    
    // Update every 30 seconds
    const interval = setInterval(loadServiceStatuses, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const loadServiceStatuses = () => {
    setLoading(true);
    const statuses = smartApiService.getAllServiceStatuses();
    setServices(statuses);
    setLoading(false);
  };

  const handleRefresh = () => {
    loadServiceStatuses();
  };

  const getServiceIcon = (service: ServiceStatus) => {
    if (service.isHealthy) {
      return <HealthyIcon sx={{ color: 'success.main', fontSize: 16 }} />;
    } else {
      return <UnhealthyIcon sx={{ color: 'error.main', fontSize: 16 }} />;
    }
  };

  const getServiceChip = (service: ServiceStatus) => {
    if (service.isHealthy) {
      return (
        <Chip
          icon={<CloudIcon />}
          label="مباشر"
          color="success"
          size="small"
          variant="outlined"
        />
      );
    } else {
      return (
        <Chip
          icon={<MockIcon />}
          label="تجريبي"
          color="warning"
          size="small"
          variant="outlined"
        />
      );
    }
  };

  const healthyCount = services.filter(s => s.isHealthy).length;
  const totalCount = services.length;
  const healthPercentage = totalCount > 0 ? (healthyCount / totalCount) * 100 : 0;

  const getOverallStatus = () => {
    if (healthPercentage >= 80) return { color: 'success.main', text: 'ممتاز' };
    if (healthPercentage >= 60) return { color: 'warning.main', text: 'جيد' };
    if (healthPercentage >= 40) return { color: 'orange', text: 'متوسط' };
    return { color: 'error.main', text: 'ضعيف' };
  };

  const overallStatus = getOverallStatus();

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6" component="div">
              حالة الخدمات
            </Typography>
            <Chip
              label={`${healthyCount}/${totalCount}`}
              color={healthPercentage >= 80 ? 'success' : healthPercentage >= 60 ? 'warning' : 'error'}
              size="small"
            />
            <Typography variant="body2" sx={{ color: overallStatus.color }}>
              {overallStatus.text}
            </Typography>
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="تحديث">
              <IconButton onClick={handleRefresh} size="small" disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <IconButton
              onClick={() => setExpanded(!expanded)}
              size="small"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {loading && <LinearProgress sx={{ mt: 1 }} />}

        <Box sx={{ mt: 1 }}>
          <LinearProgress
            variant="determinate"
            value={healthPercentage}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                backgroundColor: overallStatus.color,
                borderRadius: 3,
              },
            }}
          />
        </Box>

        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={1}>
              {services.map((service) => (
                <Grid item xs={12} sm={6} md={4} key={service.name}>
                  <Box
                    sx={{
                      p: 1,
                      border: 1,
                      borderColor: service.isHealthy ? 'success.light' : 'error.light',
                      borderRadius: 1,
                      backgroundColor: service.isHealthy ? 'success.50' : 'error.50',
                    }}
                  >
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box display="flex" alignItems="center" gap={1}>
                        {getServiceIcon(service)}
                        <Typography variant="body2" fontWeight="medium">
                          {service.name}
                        </Typography>
                      </Box>
                      {getServiceChip(service)}
                    </Box>
                    
                    <Typography variant="caption" color="text.secondary">
                      المنفذ: {service.port}
                    </Typography>
                    
                    <Typography variant="caption" display="block" color="text.secondary">
                      آخر فحص: {service.lastChecked.toLocaleTimeString('ar-SA')}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.50', borderRadius: 1 }}>
              <Typography variant="body2" color="info.main">
                💡 <strong>نصيحة:</strong> الخدمات الصحية تستخدم البيانات المباشرة، 
                بينما الخدمات غير المتاحة تستخدم البيانات التجريبية تلقائياً.
              </Typography>
            </Box>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default ServiceStatusIndicator;
