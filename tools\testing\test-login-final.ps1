# Final Login Test
Write-Host "🎉 Final Login Test with <PERSON>ck Auth" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Check Frontend
Write-Host "`n🌐 Checking Frontend..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "✅ Frontend: Ready (Status: $($frontendResponse.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend: Not Ready" -ForegroundColor Red
    exit 1
}

# Check .env configuration
Write-Host "`n⚙️ Checking Configuration..." -ForegroundColor Cyan
$envPath = "frontend/admin-dashboard/.env"
if (Test-Path $envPath) {
    $envContent = Get-Content $envPath
    $apiBaseUrl = ($envContent | Where-Object { $_ -like "*REACT_APP_API_BASE_URL*" }) -replace ".*=", ""
    $useMockAuth = ($envContent | Where-Object { $_ -like "*REACT_APP_USE_MOCK_AUTH*" }) -replace ".*=", ""
    
    Write-Host "📝 API Base URL: $apiBaseUrl" -ForegroundColor White
    Write-Host "🔧 Use Mock Auth: $useMockAuth" -ForegroundColor White
    
    if ($useMockAuth -eq "true") {
        Write-Host "✅ Mock Auth is enabled" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Mock Auth is not enabled" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ .env file not found" -ForegroundColor Red
}

# Check Mock Auth Service file
Write-Host "`n📁 Checking Mock Auth Service..." -ForegroundColor Cyan
$mockAuthPath = "frontend/admin-dashboard/src/services/mockAuthService.ts"
if (Test-Path $mockAuthPath) {
    Write-Host "✅ Mock Auth Service file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Mock Auth Service file not found" -ForegroundColor Red
}

# Test credentials
Write-Host "`n🔐 Available Test Credentials:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "1. Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: password123" -ForegroundColor White
Write-Host "   Role: ADMIN" -ForegroundColor White
Write-Host ""
Write-Host "2. Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: admin123" -ForegroundColor White
Write-Host "   Role: ADMIN" -ForegroundColor White
Write-Host ""
Write-Host "3. Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: manager123" -ForegroundColor White
Write-Host "   Role: MANAGER" -ForegroundColor White

# Instructions
Write-Host "`n📋 Testing Instructions:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow
Write-Host "1. 🌐 Open: http://localhost:3000/login" -ForegroundColor White
Write-Host "2. 📝 Enter credentials: <EMAIL> / password123" -ForegroundColor White
Write-Host "3. 🔍 Open Developer Tools (F12)" -ForegroundColor White
Write-Host "4. 👀 Watch Console for Mock Auth messages" -ForegroundColor White
Write-Host "5. ✅ Login should work and redirect to dashboard" -ForegroundColor White

# Browser console test
Write-Host "`n🧪 Browser Console Test Code:" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow
Write-Host @"
// Copy and paste this in browser console (F12)
console.log('🧪 Testing Mock Auth');

// Test login directly
fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
})
.then(response => response.json())
.then(data => {
  console.log('✅ Login Response:', data);
  if (data.success) {
    console.log('🎉 Login Successful!');
    console.log('🔑 Token:', data.data.token);
    console.log('👤 User:', data.data.user);
  }
})
.catch(error => console.log('❌ Login Error:', error));
"@ -ForegroundColor Gray

# Summary
Write-Host "`n📊 Summary:" -ForegroundColor Green
Write-Host "===========" -ForegroundColor Green
Write-Host "✅ Frontend is running on http://localhost:3000" -ForegroundColor Green
Write-Host "✅ Mock Auth Service is configured" -ForegroundColor Green
Write-Host "✅ Login credentials are available" -ForegroundColor Green
Write-Host "🎯 Ready to test login functionality!" -ForegroundColor Green

Write-Host "`n💡 Expected Result:" -ForegroundColor Yellow
Write-Host "- Login form should accept credentials" -ForegroundColor White
Write-Host "- Mock Auth should process login" -ForegroundColor White
Write-Host "- User should be redirected to dashboard" -ForegroundColor White
Write-Host "- No more 'Network Error' messages" -ForegroundColor White

Write-Host "`n🚀 Go ahead and test login at: http://localhost:3000/login" -ForegroundColor Cyan
