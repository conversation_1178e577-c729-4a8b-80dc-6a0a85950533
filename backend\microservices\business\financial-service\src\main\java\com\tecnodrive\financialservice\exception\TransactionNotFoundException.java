package com.tecnodrive.financialservice.exception;

/**
 * Transaction Not Found Exception
 * 
 * Thrown when a requested financial transaction cannot be found
 */
public class TransactionNotFoundException extends RuntimeException {

    public TransactionNotFoundException() {
        super("Financial transaction not found");
    }

    public TransactionNotFoundException(String message) {
        super(message);
    }

    public TransactionNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
