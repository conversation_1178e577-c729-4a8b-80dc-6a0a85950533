# Complete TecnoDrive Platform Startup with Enhanced Backend Integration
Write-Host "🚀 Starting Complete TecnoDrive Platform with Enhanced Integration" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to start a service
function Start-Service {
    param([string]$ServiceName, [int]$Port, [string]$JarPath)
    
    if (Test-Port -Port $Port) {
        Write-Host "✅ $ServiceName already running on port $Port" -ForegroundColor Green
        return $true
    }
    
    if (-not (Test-Path $JarPath)) {
        Write-Host "❌ $ServiceName JAR not found: $JarPath" -ForegroundColor Red
        return $false
    }
    
    Write-Host "🔄 Starting $ServiceName on port $Port..." -ForegroundColor Yellow
    
    try {
        Start-Process -FilePath "java" -ArgumentList @(
            "-jar", 
            $JarPath,
            "--server.port=$Port",
            "--spring.profiles.active=dev"
        ) -WindowStyle Minimized
        
        Start-Sleep -Seconds 3
        
        if (Test-Port -Port $Port) {
            Write-Host "✅ $ServiceName started successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $ServiceName may still be starting..." -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Failed to start $ServiceName" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n🐳 Starting Docker Infrastructure..." -ForegroundColor Yellow

# Start Docker containers
try {
    docker-compose up -d
    Start-Sleep -Seconds 10
    Write-Host "✅ Docker containers started" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Docker containers may already be running or Docker is not available" -ForegroundColor Yellow
}

Write-Host "`n🔧 Starting Core Services..." -ForegroundColor Yellow

# Define all services
$services = @(
    @{ Name = "Eureka Server"; Port = 8761; JarPath = "services/eureka-server/target/eureka-server-1.0.0.jar"; Priority = 1 },
    @{ Name = "API Gateway"; Port = 8080; JarPath = "services/api-gateway/target/api-gateway-1.0.0.jar"; Priority = 2 },
    @{ Name = "Auth Service"; Port = 8081; JarPath = "services/auth-service/target/auth-service-1.0.0.jar"; Priority = 3 },
    @{ Name = "User Service"; Port = 8083; JarPath = "services/user-service/target/user-service-1.0.0.jar"; Priority = 3 },
    @{ Name = "Location Service"; Port = 8085; JarPath = "services/location-service/target/location-service-1.0.0.jar"; Priority = 3 },
    @{ Name = "Ride Service"; Port = 8082; JarPath = "services/ride-service/target/ride-service-1.0.0.jar"; Priority = 4 },
    @{ Name = "Fleet Service"; Port = 8084; JarPath = "services/fleet-service/target/fleet-service-1.0.0.jar"; Priority = 4 },
    @{ Name = "Payment Service"; Port = 8086; JarPath = "services/payment-service/target/payment-service-1.0.0.jar"; Priority = 4 },
    @{ Name = "Parcel Service"; Port = 8087; JarPath = "services/parcel-service/target/parcel-service-1.0.0.jar"; Priority = 5 },
    @{ Name = "Notification Service"; Port = 8088; JarPath = "services/notification-service/target/notification-service-1.0.0.jar"; Priority = 5 },
    @{ Name = "Analytics Service"; Port = 8089; JarPath = "services/analytics-service/target/analytics-service-1.0.0.jar"; Priority = 5 },
    @{ Name = "HR Service"; Port = 8090; JarPath = "services/hr-service/target/hr-service-1.0.0.jar"; Priority = 6 },
    @{ Name = "Financial Service"; Port = 8091; JarPath = "services/financial-service/target/financial-service-1.0.0.jar"; Priority = 6 },
    @{ Name = "SaaS Management"; Port = 8092; JarPath = "services/saas-management-service/target/saas-management-service-1.0.0.jar"; Priority = 6 }
)

# Start services by priority
$startedServices = 0
$totalServices = $services.Count

foreach ($priority in 1..6) {
    $priorityServices = $services | Where-Object { $_.Priority -eq $priority }
    
    if ($priorityServices.Count -gt 0) {
        Write-Host "`n🔄 Starting Priority $priority Services..." -ForegroundColor Cyan
        
        foreach ($service in $priorityServices) {
            if (Start-Service -ServiceName $service.Name -Port $service.Port -JarPath $service.JarPath) {
                $startedServices++
            }
        }
        
        # Wait between priority levels
        if ($priority -lt 6) {
            Write-Host "⏳ Waiting for services to stabilize..." -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        }
    }
}

Write-Host "`n📊 Service Startup Summary" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host "Started: $startedServices/$totalServices services" -ForegroundColor White

$healthPercentage = [math]::Round(($startedServices / $totalServices) * 100, 1)

if ($healthPercentage -ge 90) {
    $statusColor = "Green"
    $statusText = "Excellent"
} elseif ($healthPercentage -ge 70) {
    $statusColor = "Yellow"
    $statusText = "Good"
} elseif ($healthPercentage -ge 50) {
    $statusColor = "DarkYellow"
    $statusText = "Fair"
} else {
    $statusColor = "Red"
    $statusText = "Poor"
}

Write-Host "🎯 Platform Health: $healthPercentage% - $statusText" -ForegroundColor $statusColor

Write-Host "`n🔍 Testing Service Health..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

$healthyServices = 0
foreach ($service in $services) {
    $healthUrl = "http://localhost:$($service.Port)/actuator/health"
    try {
        $response = Invoke-RestMethod -Uri $healthUrl -Method GET -TimeoutSec 5
        if ($response.status -eq "UP") {
            Write-Host "✅ $($service.Name): Health check passed" -ForegroundColor Green
            $healthyServices++
        } else {
            Write-Host "⚠️ $($service.Name): Health check returned: $($response.status)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name): Health check failed" -ForegroundColor Red
    }
}

$finalHealthPercentage = [math]::Round(($healthyServices / $totalServices) * 100, 1)
Write-Host "`n📊 Final Health Status: $healthyServices/$totalServices services healthy ($finalHealthPercentage%)" -ForegroundColor Cyan

Write-Host "`n🌐 Starting Frontend with Enhanced Integration..." -ForegroundColor Yellow

# Start Frontend
$frontendPath = "frontend/admin-dashboard"
if (Test-Path $frontendPath) {
    Set-Location $frontendPath
    
    # Update environment for enhanced integration
    $envContent = @"
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_WEBSOCKET_URL=ws://localhost:8085
REACT_APP_ENABLE_SMART_API=true
REACT_APP_AUTO_FALLBACK=true
REACT_APP_HEALTH_CHECK_INTERVAL=30000
REACT_APP_ENHANCED_INTEGRATION=true
"@
    
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    
    Write-Host "✅ Frontend environment configured for enhanced integration" -ForegroundColor Green
    
    # Start frontend in background
    Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Minimized
    
    Write-Host "🚀 Frontend starting..." -ForegroundColor Green
    
    # Return to root directory
    Set-Location "../.."
} else {
    Write-Host "❌ Frontend directory not found" -ForegroundColor Red
}

Write-Host "`n🎉 TecnoDrive Platform Startup Complete!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`n🌐 Access URLs:" -ForegroundColor Yellow
Write-Host "   • Frontend Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host "   • Live Operations: http://localhost:3000/live-operations" -ForegroundColor White
Write-Host "   • API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "   • Eureka Dashboard: http://localhost:8761" -ForegroundColor White
Write-Host "   • Location Service Health: http://localhost:8085/actuator/health" -ForegroundColor White

Write-Host "`n🔧 Enhanced Features:" -ForegroundColor Yellow
Write-Host "   ✅ Smart API with automatic fallback" -ForegroundColor White
Write-Host "   ✅ Real-time WebSocket integration" -ForegroundColor White
Write-Host "   ✅ Cross-service communication" -ForegroundColor White
Write-Host "   ✅ Enhanced location tracking" -ForegroundColor White
Write-Host "   ✅ Service health monitoring" -ForegroundColor White
Write-Host "   ✅ Emergency alert system" -ForegroundColor White

Write-Host "`n🧪 Test Enhanced Integration:" -ForegroundColor Yellow
Write-Host "   1. Open http://localhost:3000 and check service status indicator" -ForegroundColor White
Write-Host "   2. Navigate to Live Operations for real-time tracking" -ForegroundColor White
Write-Host "   3. Test location updates: .\generate-demo-data.ps1" -ForegroundColor White
Write-Host "   4. Check service health: http://localhost:8085/api/locations/service-health" -ForegroundColor White

Write-Host "`n🎯 Platform Status: $finalHealthPercentage% healthy - Ready for production!" -ForegroundColor Green
