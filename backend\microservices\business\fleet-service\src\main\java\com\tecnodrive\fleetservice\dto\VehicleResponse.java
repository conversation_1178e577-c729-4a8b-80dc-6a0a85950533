package com.tecnodrive.fleetservice.dto;

import com.tecnodrive.fleetservice.entity.Vehicle;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

/**
 * Vehicle Response DTO
 * 
 * Used for returning vehicle information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VehicleResponse {

    /**
     * Vehicle ID
     */
    private String id;

    /**
     * Unique vehicle plate number
     */
    private String plateNumber;

    /**
     * Vehicle identification number (VIN)
     */
    private String vin;

    /**
     * Vehicle make/manufacturer
     */
    private String make;

    /**
     * Vehicle model
     */
    private String model;

    /**
     * Manufacturing year
     */
    private Integer year;

    /**
     * Vehicle color
     */
    private String color;

    /**
     * Passenger capacity
     */
    private Integer capacity;

    /**
     * Vehicle type
     */
    private Vehicle.VehicleType vehicleType;

    /**
     * Fuel type
     */
    private Vehicle.FuelType fuelType;

    /**
     * Current vehicle status
     */
    private Vehicle.VehicleStatus status;

    /**
     * Current odometer reading (in kilometers)
     */
    private BigDecimal odometerReading;

    /**
     * Engine capacity (in liters)
     */
    private BigDecimal engineCapacity;

    /**
     * Transmission type
     */
    private Vehicle.TransmissionType transmissionType;

    /**
     * Currently assigned driver ID
     */
    private String assignedDriverId;

    /**
     * Company/Tenant ID
     */
    private String companyId;

    /**
     * Vehicle registration date
     */
    private LocalDate registrationDate;

    /**
     * Registration expiry date
     */
    private LocalDate registrationExpiryDate;

    /**
     * Insurance policy number
     */
    private String insurancePolicyNumber;

    /**
     * Insurance expiry date
     */
    private LocalDate insuranceExpiryDate;

    /**
     * Last maintenance date
     */
    private LocalDate lastMaintenanceDate;

    /**
     * Next scheduled maintenance date
     */
    private LocalDate nextMaintenanceDate;

    /**
     * Odometer reading at last maintenance
     */
    private BigDecimal lastMaintenanceOdometer;

    /**
     * Purchase date
     */
    private LocalDate purchaseDate;

    /**
     * Purchase price
     */
    private BigDecimal purchasePrice;

    /**
     * Current market value
     */
    private BigDecimal currentValue;

    /**
     * Average fuel consumption (km per liter)
     */
    private BigDecimal fuelConsumption;

    /**
     * Vehicle notes/comments
     */
    private String notes;

    /**
     * GPS tracking device ID
     */
    private String gpsDeviceId;

    /**
     * Whether vehicle is active in the fleet
     */
    private boolean isActive;

    /**
     * Creation timestamp
     */
    private Instant createdAt;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;

    /**
     * Calculated fields
     */
    private boolean needsMaintenance;
    private boolean insuranceExpiringSoon;
    private boolean registrationExpiringSoon;
    private boolean availableForAssignment;
    private int vehicleAge;
    private BigDecimal kilometersSinceLastMaintenance;

    /**
     * Vehicle Summary DTO for dashboard/list views
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class VehicleSummary {
        private String id;
        private String plateNumber;
        private String make;
        private String model;
        private Integer year;
        private Vehicle.VehicleType vehicleType;
        private Vehicle.VehicleStatus status;
        private String assignedDriverId;
        private boolean needsMaintenance;
        private boolean insuranceExpiringSoon;
        private BigDecimal odometerReading;
    }

    /**
     * Vehicle Statistics DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class VehicleStatistics {
        private long totalVehicles;
        private long availableVehicles;
        private long inUseVehicles;
        private long maintenanceVehicles;
        private long outOfServiceVehicles;
        private double averageAge;
        private BigDecimal totalOdometerReading;
        private BigDecimal averageOdometerReading;
        private long vehiclesNeedingMaintenance;
        private long vehiclesWithExpiringInsurance;
        private long vehiclesWithExpiringRegistration;
    }
}
