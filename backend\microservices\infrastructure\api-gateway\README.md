# 🌐 TECNO DRIVE API Gateway

Central API Gateway with JWT validation and rate limiting for the TECNO DRIVE platform.

## Features

### 🔐 Security
- **JWT Authentication**: Validates JWT tokens for protected routes
- **CORS Support**: Configurable cross-origin resource sharing
- **Route Protection**: Auth endpoints are public, others require authentication

### ⚡ Rate Limiting
- **Redis-based**: Uses Redis for distributed rate limiting
- **IP-based**: Rate limits based on client IP address
- **Configurable**: 10 requests/sec replenish rate, 20 burst capacity
- **Per-route**: Different rate limits for different services

### 🚦 Routing
- **Auth Service**: `/api/v1/auth/**` → `auth-service`
- **Ride Service**: `/api/v1/rides/**` → `ride-service`
- **Parcel Service**: `/api/v1/parcels/**` → `parcel-service`

### 📊 Monitoring
- **Health Checks**: `/actuator/health`
- **Gateway Routes**: `/actuator/gateway/routes`
- **Prometheus Metrics**: `/actuator/prometheus`

## Configuration

### Environment Variables
- `JWT_SECRET`: JWT signing secret (default: auto-generated)
- `REDIS_HOST`: Redis host (default: redis)
- `REDIS_PORT`: Redis port (default: 6379)
- `EUREKA_SERVER_URL`: Eureka server URL

### Rate Limiting Configuration
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: auth
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
```

## Building and Running

### Prerequisites
- Java 17+
- Maven 3.6+
- Redis (for rate limiting)
- Eureka Server (for service discovery)

### Build
```bash
./build-and-test.sh
```

### Run Locally
```bash
java -jar target/api-gateway-1.0.0.jar
```

### Docker
```bash
docker build -t tecnodrive/api-gateway:latest .
docker run -p 8080:8080 tecnodrive/api-gateway:latest
```

## Testing

### Health Check
```bash
curl http://localhost:8080/actuator/health
```

### Gateway Routes
```bash
curl http://localhost:8080/actuator/gateway/routes
```

### Test Rate Limiting
```bash
# This should work (within rate limit)
for i in {1..10}; do
  curl -w "%{http_code}\n" http://localhost:8080/api/v1/auth/health
done

# This should return 429 (rate limited)
for i in {1..25}; do
  curl -w "%{http_code}\n" http://localhost:8080/api/v1/auth/health
done
```

### Test JWT Authentication
```bash
# This should work (public endpoint)
curl http://localhost:8080/api/v1/auth/health

# This should return 401 (no token)
curl http://localhost:8080/api/v1/rides/health

# This should work (with valid token)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/v1/rides/health
```

## Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│ API Gateway │───▶│   Services  │
└─────────────┘    └─────────────┘    └─────────────┘
                          │
                          ▼
                   ┌─────────────┐
                   │    Redis    │
                   │(Rate Limit) │
                   └─────────────┘
```

## Security Flow

1. **Request arrives** at API Gateway
2. **CORS check** if cross-origin request
3. **Rate limiting** check against Redis
4. **JWT validation** for protected routes
5. **Route to service** if all checks pass
6. **Return response** to client

## Monitoring

The gateway exposes several monitoring endpoints:

- `/actuator/health` - Health status
- `/actuator/info` - Application info
- `/actuator/prometheus` - Prometheus metrics
- `/actuator/gateway/routes` - Current routes
- `/actuator/gateway/filters` - Available filters

## Troubleshooting

### Common Issues

1. **503 Service Unavailable**
   - Check if target services are running
   - Verify Eureka registration

2. **429 Too Many Requests**
   - Rate limit exceeded
   - Check Redis connectivity

3. **401 Unauthorized**
   - Invalid or missing JWT token
   - Check JWT secret configuration

4. **Connection refused**
   - Redis not available
   - Check Redis configuration

### Logs
```bash
# View gateway logs
docker logs api-gateway

# Enable debug logging
export LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_CLOUD_GATEWAY=DEBUG
```
