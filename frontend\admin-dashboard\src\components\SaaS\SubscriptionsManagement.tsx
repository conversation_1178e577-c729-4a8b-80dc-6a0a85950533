import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Subscriptions as SubscriptionsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { saasService, SubscriptionDto, CreateSubscriptionRequest, TenantDto } from '../../services/saasService';

// Plan types and configurations
const PLAN_CONFIGS = {
  BASIC: {
    name: 'الخطة الأساسية',
    color: 'info' as const,
    icon: '📦',
    features: ['إدارة المستخدمين', 'التقارير الأساسية', 'الدعم الفني'],
    basePrice: 500,
  },
  PREMIUM: {
    name: 'الخطة المتقدمة',
    color: 'warning' as const,
    icon: '⭐',
    features: ['جميع مميزات الأساسية', 'التحليلات المتقدمة', 'API مخصص', 'دعم أولوية'],
    basePrice: 1000,
  },
  ENTERPRISE: {
    name: 'خطة المؤسسات',
    color: 'success' as const,
    icon: '🏢',
    features: ['جميع المميزات', 'دعم مخصص', 'SLA مضمون', 'تكامل مخصص'],
    basePrice: 2000,
  },
};

const SubscriptionsManagement: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<SubscriptionDto[]>([]);
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [filterPlan, setFilterPlan] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [newSubscription, setNewSubscription] = useState<CreateSubscriptionRequest>({
    tenantId: '',
    planId: 'plan-basic',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    seats: 10,
  });

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load tenants for dropdown
      const tenantsResponse = await saasService.getTenants();
      if (tenantsResponse.success && tenantsResponse.data) {
        setTenants(tenantsResponse.data);
      }

      // Load all subscriptions (in real app, this would be paginated)
      const allSubscriptions: SubscriptionDto[] = [];
      for (const tenant of tenantsResponse.data || []) {
        const subscriptionsResponse = await saasService.getSubscriptions(tenant.id);
        if (subscriptionsResponse.success && subscriptionsResponse.data) {
          allSubscriptions.push(...subscriptionsResponse.data);
        }
      }
      setSubscriptions(allSubscriptions);

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const },
      EXPIRED: { label: 'منتهي', color: 'warning' as const },
      CANCELLED: { label: 'ملغي', color: 'error' as const },
      PENDING: { label: 'في الانتظار', color: 'info' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'default' as const };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getPlanChip = (planType: string) => {
    const config = PLAN_CONFIGS[planType as keyof typeof PLAN_CONFIGS];
    if (!config) return <Chip label={planType} size="small" />;

    return (
      <Chip
        label={`${config.icon} ${config.name}`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleEditSubscription = (subscriptionId: string) => {
    console.log('Edit subscription:', subscriptionId);
    // TODO: Implement edit functionality
  };

  const handleDeleteSubscription = async (subscriptionId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
      try {
        // TODO: Implement delete API call
        console.log('Delete subscription:', subscriptionId);
        loadData();
      } catch (error) {
        console.error('Error deleting subscription:', error);
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'tenantName',
      headerName: 'العميل',
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const tenant = tenants.find(t => t.id === params.row.tenantId);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              <BusinessIcon fontSize="small" />
            </Avatar>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {tenant?.name || 'غير محدد'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'planType',
      headerName: 'الخطة',
      width: 180,
      renderCell: (params: GridRenderCellParams) => getPlanChip(params.value),
    },
    {
      field: 'seats',
      headerName: 'المقاعد',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PeopleIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'monthlyPrice',
      headerName: 'السعر الشهري',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <MoneyIcon fontSize="small" color="action" />
          <Typography variant="body2">
            {params.value.toLocaleString()} ريال
          </Typography>
        </Box>
      ),
    },
    {
      field: 'startDate',
      headerName: 'تاريخ البداية',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'endDate',
      headerName: 'تاريخ الانتهاء',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEditSubscription(params.id as string)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteSubscription(params.id as string)}
        />,
      ],
    },
  ];

  const filteredSubscriptions = subscriptions.filter(subscription => {
    const tenant = tenants.find(t => t.id === subscription.tenantId);
    const matchesSearch = tenant?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         subscription.planName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'ALL' || subscription.status === filterStatus;
    const matchesPlan = filterPlan === 'ALL' || subscription.planType === filterPlan;
    
    return matchesSearch && matchesStatus && matchesPlan;
  });

  const handleAddSubscription = async () => {
    try {
      await saasService.createSubscription(newSubscription);
      setOpenAddDialog(false);
      setNewSubscription({
        tenantId: '',
        planId: 'plan-basic',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        seats: 10,
      });
      loadData();
    } catch (error) {
      console.error('Error creating subscription:', error);
    }
  };

  // Calculate stats
  const totalSubscriptions = subscriptions.length;
  const activeSubscriptions = subscriptions.filter(s => s.status === 'ACTIVE').length;
  const totalRevenue = subscriptions.reduce((sum, s) => sum + (s.monthlyPrice || 0), 0);
  const avgSeats = subscriptions.length > 0 ? 
    Math.round(subscriptions.reduce((sum, s) => sum + s.seats, 0) / subscriptions.length) : 0;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة الاشتراكات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة اشتراكات العملاء والخطط المختلفة
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalSubscriptions}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الاشتراكات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {activeSubscriptions}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الاشتراكات النشطة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإيرادات (ريال)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {avgSeats}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                متوسط المقاعد
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Plans Overview */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            الخطط المتاحة
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(PLAN_CONFIGS).map(([key, config]) => (
              <Grid item xs={12} md={4} key={key}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Typography variant="h6">{config.icon}</Typography>
                      <Typography variant="h6">{config.name}</Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {config.basePrice.toLocaleString()} ريال/شهر
                    </Typography>
                    <List dense>
                      {config.features.map((feature, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={feature}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في الاشتراكات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterStatus}
                label="الحالة"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ACTIVE">نشط</MenuItem>
                <MenuItem value="EXPIRED">منتهي</MenuItem>
                <MenuItem value="CANCELLED">ملغي</MenuItem>
                <MenuItem value="PENDING">في الانتظار</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>الخطة</InputLabel>
              <Select
                value={filterPlan}
                label="الخطة"
                onChange={(e) => setFilterPlan(e.target.value)}
              >
                <MenuItem value="ALL">جميع الخطط</MenuItem>
                <MenuItem value="BASIC">الأساسية</MenuItem>
                <MenuItem value="PREMIUM">المتقدمة</MenuItem>
                <MenuItem value="ENTERPRISE">المؤسسات</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة اشتراك
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredSubscriptions}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Subscription Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة اشتراك جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>العميل</InputLabel>
                  <Select
                    value={newSubscription.tenantId}
                    label="العميل"
                    onChange={(e) => setNewSubscription({ ...newSubscription, tenantId: e.target.value })}
                  >
                    {tenants.map((tenant) => (
                      <MenuItem key={tenant.id} value={tenant.id}>
                        {tenant.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>الخطة</InputLabel>
                  <Select
                    value={newSubscription.planId}
                    label="الخطة"
                    onChange={(e) => setNewSubscription({ ...newSubscription, planId: e.target.value })}
                  >
                    <MenuItem value="plan-basic">الخطة الأساسية</MenuItem>
                    <MenuItem value="plan-premium">الخطة المتقدمة</MenuItem>
                    <MenuItem value="plan-enterprise">خطة المؤسسات</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="تاريخ البداية"
                  type="date"
                  value={newSubscription.startDate}
                  onChange={(e) => setNewSubscription({ ...newSubscription, startDate: e.target.value })}
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="تاريخ الانتهاء"
                  type="date"
                  value={newSubscription.endDate}
                  onChange={(e) => setNewSubscription({ ...newSubscription, endDate: e.target.value })}
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="عدد المقاعد"
                  type="number"
                  value={newSubscription.seats}
                  onChange={(e) => setNewSubscription({ ...newSubscription, seats: parseInt(e.target.value) || 0 })}
                  fullWidth
                  required
                  inputProps={{ min: 1 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddSubscription} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubscriptionsManagement;
