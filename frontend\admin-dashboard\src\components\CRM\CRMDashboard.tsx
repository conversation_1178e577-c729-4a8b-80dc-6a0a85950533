import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Tab,
  Tabs,
  Paper,
} from '@mui/material';
import {
  People,
  Support,
  TrendingUp,
  Star,
  Phone,
  Email,
  Message,
  Add,
  Search,
  FilterList,
  Download,
  Sentiment,
  Psychology,
  Timeline,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import crmService, { Customer, SupportTicket, CustomerAnalytics, CustomerJourney } from '../../services/crmService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`crm-tabpanel-${index}`}
      aria-labelledby={`crm-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CRMDashboard: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [analytics, setAnalytics] = useState<CustomerAnalytics | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerJourney, setCustomerJourney] = useState<CustomerJourney | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [newTicket, setNewTicket] = useState<Partial<SupportTicket>>({});
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);

  const tenantId = localStorage.getItem('tenantId') || 'default';

  useEffect(() => {
    loadDashboardData();
    setupRealTimeSubscriptions();

    return () => {
      crmService.disconnect();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [customersData, ticketsData, analyticsData] = await Promise.all([
        crmService.getCustomers(tenantId, { limit: 50 }),
        crmService.getSupportTickets(tenantId),
        crmService.getCustomerAnalytics(tenantId),
      ]);

      setCustomers(customersData.customers);
      setSupportTickets(ticketsData);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading CRM dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealTimeSubscriptions = () => {
    crmService.subscribeToTicketUpdates(tenantId, (ticket: SupportTicket) => {
      setSupportTickets(prev => 
        prev.map(t => t.id === ticket.id ? ticket : t)
      );
    });

    crmService.subscribeToNewCustomers(tenantId, (customer: Customer) => {
      setCustomers(prev => [customer, ...prev]);
    });
  };

  const handleCustomerClick = async (customer: Customer) => {
    setSelectedCustomer(customer);
    try {
      const journey = await crmService.getCustomerJourney(customer.id);
      setCustomerJourney(journey);
    } catch (error) {
      console.error('Error loading customer journey:', error);
    }
  };

  const handleTicketClick = async (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    if (ticket.description) {
      try {
        const suggestions = await crmService.generateAIResponse(ticket.id, ticket.description);
        setAiSuggestions(suggestions);
      } catch (error) {
        console.error('Error generating AI suggestions:', error);
      }
    }
  };

  const handleCreateTicket = async () => {
    if (!newTicket.customerId || !newTicket.title || !newTicket.description) return;

    try {
      const ticket = await crmService.createSupportTicket({
        ...newTicket,
        tenantId,
        category: newTicket.category || 'inquiry',
        priority: newTicket.priority || 'medium',
        status: 'open',
        sentimentScore: 0,
        aiSuggestions: [],
        attachments: [],
        interactions: [],
      } as Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt' | 'interactions'>);

      setSupportTickets(prev => [ticket, ...prev]);
      setNewTicket({});
    } catch (error) {
      console.error('Error creating ticket:', error);
    }
  };

  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case 'vip': return 'error';
      case 'premium': return 'warning';
      case 'enterprise': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSentimentIcon = (score: number) => {
    if (score > 0.5) return <Sentiment color="success" />;
    if (score < -0.5) return <Sentiment color="error" />;
    return <Sentiment color="warning" />;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <Typography>Loading CRM Dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Customer Relationship Management
        </Typography>
        <Box>
          <Button
            startIcon={<Add />}
            variant="contained"
            onClick={() => setTabValue(2)}
            sx={{ mr: 1 }}
          >
            New Ticket
          </Button>
          <Button
            startIcon={<Download />}
            variant="outlined"
          >
            Export
          </Button>
        </Box>
      </Box>

      {/* Analytics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <People color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Customers
                  </Typography>
                  <Typography variant="h5">
                    {analytics?.totalCustomers || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    New This Month
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {analytics?.newCustomersThisMonth || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Star color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Satisfaction Score
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {analytics?.customerSatisfactionScore?.toFixed(1) || 0}/5
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Support color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Open Tickets
                  </Typography>
                  <Typography variant="h5" color="info.main">
                    {supportTickets.filter(t => t.status === 'open').length}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Customers" />
          <Tab label="Support Tickets" />
          <Tab label="Create Ticket" />
          <Tab label="Analytics" />
        </Tabs>

        {/* Customers Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            {customers.map((customer) => (
              <Grid item xs={12} md={6} lg={4} key={customer.id}>
                <Card
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                  onClick={() => handleCustomerClick(customer)}
                >
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Avatar sx={{ mr: 2 }}>
                        {customer.firstName[0]}{customer.lastName[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {customer.firstName} {customer.lastName}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {customer.email}
                        </Typography>
                      </Box>
                    </Box>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={customer.segment}
                        size="small"
                        color={getSegmentColor(customer.segment) as any}
                      />
                      <Chip
                        label={customer.status}
                        size="small"
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary">
                      Total Trips: {customer.totalTrips} | Spent: ${customer.totalSpent}
                    </Typography>

                    <Box display="flex" alignItems="center" mt={1}>
                      <Star color="warning" sx={{ mr: 0.5, fontSize: 16 }} />
                      <Typography variant="body2">
                        {customer.averageRating.toFixed(1)}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Support Tickets Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            {supportTickets.map((ticket) => (
              <Grid item xs={12} md={6} lg={4} key={ticket.id}>
                <Card
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                  onClick={() => handleTicketClick(ticket)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="h6" noWrap>
                        {ticket.title}
                      </Typography>
                      {getSentimentIcon(ticket.sentimentScore)}
                    </Box>

                    <Typography variant="body2" color="textSecondary" mb={2}>
                      {ticket.description.substring(0, 100)}...
                    </Typography>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={ticket.category}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={ticket.priority}
                        size="small"
                        color={getPriorityColor(ticket.priority) as any}
                      />
                      <Chip
                        label={ticket.status}
                        size="small"
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="caption" color="textSecondary">
                      Created: {new Date(ticket.createdAt).toLocaleDateString()}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Create Ticket Tab */}
        <TabPanel value={tabValue} index={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Create New Support Ticket
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Customer</InputLabel>
                    <Select
                      value={newTicket.customerId || ''}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, customerId: e.target.value }))}
                    >
                      {customers.map((customer) => (
                        <MenuItem key={customer.id} value={customer.id}>
                          {customer.firstName} {customer.lastName} - {customer.email}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={newTicket.category || ''}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, category: e.target.value as any }))}
                    >
                      <MenuItem value="technical">Technical</MenuItem>
                      <MenuItem value="billing">Billing</MenuItem>
                      <MenuItem value="complaint">Complaint</MenuItem>
                      <MenuItem value="inquiry">Inquiry</MenuItem>
                      <MenuItem value="feedback">Feedback</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Priority</InputLabel>
                    <Select
                      value={newTicket.priority || ''}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, priority: e.target.value as any }))}
                    >
                      <MenuItem value="low">Low</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="high">High</MenuItem>
                      <MenuItem value="urgent">Urgent</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Title"
                    value={newTicket.title || ''}
                    onChange={(e) => setNewTicket(prev => ({ ...prev, title: e.target.value }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Description"
                    value={newTicket.description || ''}
                    onChange={(e) => setNewTicket(prev => ({ ...prev, description: e.target.value }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    onClick={handleCreateTicket}
                    disabled={!newTicket.customerId || !newTicket.title || !newTicket.description}
                  >
                    Create Ticket
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Analytics Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Customer Segments
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={Object.entries(analytics?.customersBySegment || {}).map(([key, value]) => ({
                          name: key,
                          value,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(analytics?.customersBySegment || {}).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Support Tickets by Category
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={Object.entries(analytics?.supportTicketMetrics.ticketsByCategory || {}).map(([key, value]) => ({
                      category: key,
                      count: value,
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" />
                      <YAxis />
                      <RechartsTooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* Customer Detail Dialog */}
      <Dialog
        open={!!selectedCustomer}
        onClose={() => setSelectedCustomer(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Customer Details
        </DialogTitle>
        <DialogContent>
          {selectedCustomer && (
            <Box>
              <Box display="flex" alignItems="center" mb={3}>
                <Avatar sx={{ mr: 2, width: 56, height: 56 }}>
                  {selectedCustomer.firstName[0]}{selectedCustomer.lastName[0]}
                </Avatar>
                <Box>
                  <Typography variant="h5">
                    {selectedCustomer.firstName} {selectedCustomer.lastName}
                  </Typography>
                  <Typography variant="body1" color="textSecondary">
                    {selectedCustomer.email}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {selectedCustomer.phone}
                  </Typography>
                </Box>
              </Box>

              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Segment</Typography>
                  <Chip label={selectedCustomer.segment} color={getSegmentColor(selectedCustomer.segment) as any} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Status</Typography>
                  <Chip label={selectedCustomer.status} variant="outlined" />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Total Trips</Typography>
                  <Typography variant="h6">{selectedCustomer.totalTrips}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Total Spent</Typography>
                  <Typography variant="h6">${selectedCustomer.totalSpent}</Typography>
                </Grid>
              </Grid>

              {customerJourney && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Customer Journey
                  </Typography>
                  <List>
                    {customerJourney.touchpoints.slice(0, 5).map((touchpoint, index) => (
                      <ListItem key={index}>
                        <ListItemAvatar>
                          <Avatar>
                            <Timeline />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={touchpoint.description}
                          secondary={new Date(touchpoint.timestamp).toLocaleString()}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedCustomer(null)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Ticket Detail Dialog */}
      <Dialog
        open={!!selectedTicket}
        onClose={() => setSelectedTicket(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Support Ticket Details
        </DialogTitle>
        <DialogContent>
          {selectedTicket && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedTicket.title}
              </Typography>

              <Typography variant="body1" paragraph>
                {selectedTicket.description}
              </Typography>

              <Box display="flex" gap={1} mb={2}>
                <Chip label={selectedTicket.category} color="primary" />
                <Chip label={selectedTicket.priority} color={getPriorityColor(selectedTicket.priority) as any} />
                <Chip label={selectedTicket.status} variant="outlined" />
              </Box>

              {aiSuggestions.length > 0 && (
                <Box mt={2}>
                  <Typography variant="h6" gutterBottom>
                    AI Suggestions
                  </Typography>
                  {aiSuggestions.map((suggestion, index) => (
                    <Card key={index} sx={{ mb: 1 }}>
                      <CardContent>
                        <Box display="flex" alignItems="center">
                          <Psychology color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body2">{suggestion}</Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedTicket(null)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CRMDashboard;
