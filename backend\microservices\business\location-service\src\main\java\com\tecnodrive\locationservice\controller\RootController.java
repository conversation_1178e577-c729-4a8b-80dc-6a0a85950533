package com.tecnodrive.locationservice.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/")
public class RootController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> root() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "TECNO DRIVE - Location Service");
        response.put("version", "1.0.0");
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("description", "Location tracking service with PostGIS support for TECNO DRIVE platform");
        
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("health", "/api/locations/health");
        endpoints.put("actuator", "/actuator/health");
        endpoints.put("locations", "/api/locations");
        endpoints.put("create_location", "POST /api/locations");
        endpoints.put("nearby_locations", "GET /api/locations/nearby");
        endpoints.put("calculate_distance", "GET /api/locations/distance");
        endpoints.put("location_stats", "GET /api/locations/stats");
        
        response.put("available_endpoints", endpoints);
        
        Map<String, String> features = new HashMap<>();
        features.put("PostGIS", "Enabled - Geographic data support");
        features.put("Spatial_Queries", "Distance, Nearby, Within radius");
        features.put("Geofencing", "Zone-based location tracking");
        features.put("Route_Tracking", "Path-based location analysis");
        
        response.put("features", features);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("app", "Location Service");
        info.put("description", "Handles location tracking, geospatial queries, and route management");
        info.put("version", "1.0.0");
        info.put("build_time", LocalDateTime.now());
        info.put("java_version", System.getProperty("java.version"));
        info.put("spring_profiles", "default");
        info.put("database", "PostgreSQL with PostGIS");
        info.put("spatial_support", "PostGIS 3.3");
        
        return ResponseEntity.ok(info);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("service", "location-service");
        status.put("uptime", "Running");
        status.put("timestamp", LocalDateTime.now());
        status.put("database_status", "Connected");
        status.put("postgis_status", "Available");
        
        return ResponseEntity.ok(status);
    }

    @GetMapping("/capabilities")
    public ResponseEntity<Map<String, Object>> capabilities() {
        Map<String, Object> capabilities = new HashMap<>();
        capabilities.put("service", "location-service");
        capabilities.put("timestamp", LocalDateTime.now());
        
        Map<String, Boolean> features = new HashMap<>();
        features.put("point_tracking", true);
        features.put("distance_calculation", true);
        features.put("nearby_search", true);
        features.put("route_analysis", true);
        features.put("geofencing", true);
        features.put("zone_management", true);
        features.put("real_time_tracking", true);
        
        capabilities.put("features", features);
        
        Map<String, String> supported_formats = new HashMap<>();
        supported_formats.put("input", "JSON, GeoJSON");
        supported_formats.put("output", "JSON, GeoJSON");
        supported_formats.put("coordinates", "WGS84 (EPSG:4326)");
        
        capabilities.put("supported_formats", supported_formats);
        
        return ResponseEntity.ok(capabilities);
    }
}
