-- TECNODRIVE Payment Service Database Schema
-- Database: tecnodrive_payments

\c tecnodrive_payments;

-- Payment methods
CREATE TABLE IF NOT EXISTS payment_methods (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- Reference to user service
    method_type VARCHAR(20) NOT NULL CHECK (method_type IN ('CREDIT_CARD', 'DEBIT_CARD', 'DIGITAL_WALLET', 'BANK_TRANSFER', 'CASH', 'CRYPTO')),
    provider VARCHAR(50), -- Visa, MasterCard, PayPal, etc.
    masked_number VARCHAR(20), -- Last 4 digits for cards
    cardholder_name VARCHAR(100),
    expiry_month INTEGER,
    expiry_year INTEGER,
    billing_address JSONB,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    token VARCHAR(255), -- Encrypted token from payment processor
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment transactions
CREATE TABLE IF NOT EXISTS payment_transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    ride_id BIGINT, -- Reference to ride service
    order_id BIGINT, -- Reference to order/booking
    payment_method_id BIGINT REFERENCES payment_methods(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'YER',
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('PAYMENT', 'REFUND', 'PAYOUT', 'FEE', 'ADJUSTMENT')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED')),
    gateway VARCHAR(50), -- Payment gateway used
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB,
    description TEXT,
    fee_amount DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2),
    processed_at TIMESTAMP,
    failed_reason TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment refunds
CREATE TABLE IF NOT EXISTS payment_refunds (
    id BIGSERIAL PRIMARY KEY,
    original_transaction_id BIGINT REFERENCES payment_transactions(id),
    refund_transaction_id BIGINT REFERENCES payment_transactions(id),
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_reason VARCHAR(100) NOT NULL,
    refund_type VARCHAR(20) DEFAULT 'FULL' CHECK (refund_type IN ('FULL', 'PARTIAL')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')),
    gateway_refund_id VARCHAR(255),
    processed_at TIMESTAMP,
    requested_by BIGINT, -- User ID who requested refund
    approved_by BIGINT, -- Admin who approved refund
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Driver payouts
CREATE TABLE IF NOT EXISTS driver_payouts (
    id BIGSERIAL PRIMARY KEY,
    driver_id BIGINT NOT NULL, -- Reference to user service
    payout_period_start DATE NOT NULL,
    payout_period_end DATE NOT NULL,
    total_earnings DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) DEFAULT 0.00,
    tax_deduction DECIMAL(10,2) DEFAULT 0.00,
    other_deductions DECIMAL(10,2) DEFAULT 0.00,
    net_payout DECIMAL(10,2) NOT NULL,
    payout_method VARCHAR(20) CHECK (payout_method IN ('BANK_TRANSFER', 'DIGITAL_WALLET', 'CHECK', 'CASH')),
    bank_account_id BIGINT,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED')),
    processed_at TIMESTAMP,
    reference_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bank accounts for payouts
CREATE TABLE IF NOT EXISTS bank_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- Reference to user service
    account_type VARCHAR(20) CHECK (account_type IN ('CHECKING', 'SAVINGS', 'BUSINESS')),
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    routing_number VARCHAR(20),
    account_holder_name VARCHAR(100) NOT NULL,
    swift_code VARCHAR(20),
    iban VARCHAR(50),
    is_verified BOOLEAN DEFAULT false,
    is_default BOOLEAN DEFAULT false,
    verification_deposits JSONB, -- For micro-deposit verification
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment disputes
CREATE TABLE IF NOT EXISTS payment_disputes (
    id BIGSERIAL PRIMARY KEY,
    transaction_id BIGINT REFERENCES payment_transactions(id),
    user_id BIGINT NOT NULL,
    dispute_type VARCHAR(50) NOT NULL,
    dispute_reason TEXT NOT NULL,
    amount_disputed DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'UNDER_REVIEW', 'RESOLVED', 'CLOSED')),
    resolution VARCHAR(20) CHECK (resolution IN ('REFUND', 'CHARGEBACK', 'DISMISSED', 'PARTIAL_REFUND')),
    evidence JSONB,
    admin_notes TEXT,
    resolved_at TIMESTAMP,
    resolved_by BIGINT, -- Admin user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment analytics and reporting
CREATE TABLE IF NOT EXISTS payment_analytics (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL,
    hour INTEGER CHECK (hour >= 0 AND hour <= 23),
    payment_method VARCHAR(20),
    total_transactions INTEGER DEFAULT 0,
    successful_transactions INTEGER DEFAULT 0,
    failed_transactions INTEGER DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    total_fees DECIMAL(12,2) DEFAULT 0.00,
    average_transaction_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, hour, payment_method)
);

-- Promotional codes and discounts
CREATE TABLE IF NOT EXISTS promotional_codes (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) CHECK (discount_type IN ('PERCENTAGE', 'FIXED_AMOUNT', 'FREE_RIDE')),
    discount_value DECIMAL(10,2) NOT NULL,
    minimum_amount DECIMAL(10,2),
    maximum_discount DECIMAL(10,2),
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    user_limit INTEGER DEFAULT 1, -- Per user usage limit
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NOT NULL,
    applicable_services JSONB, -- Which services this applies to
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT, -- Admin user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Promotional code usage tracking
CREATE TABLE IF NOT EXISTS promo_code_usage (
    id BIGSERIAL PRIMARY KEY,
    promo_code_id BIGINT REFERENCES promotional_codes(id),
    user_id BIGINT NOT NULL,
    transaction_id BIGINT REFERENCES payment_transactions(id),
    discount_applied DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_methods_user ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_active ON payment_methods(is_active);

CREATE INDEX IF NOT EXISTS idx_payment_transactions_user ON payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_ride ON payment_transactions(ride_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_created ON payment_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_transaction_id ON payment_transactions(transaction_id);

CREATE INDEX IF NOT EXISTS idx_driver_payouts_driver ON driver_payouts(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_payouts_period ON driver_payouts(payout_period_start, payout_period_end);
CREATE INDEX IF NOT EXISTS idx_driver_payouts_status ON driver_payouts(status);

CREATE INDEX IF NOT EXISTS idx_bank_accounts_user ON bank_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_verified ON bank_accounts(is_verified);

CREATE INDEX IF NOT EXISTS idx_payment_disputes_transaction ON payment_disputes(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_user ON payment_disputes(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_status ON payment_disputes(status);

CREATE INDEX IF NOT EXISTS idx_promotional_codes_code ON promotional_codes(code);
CREATE INDEX IF NOT EXISTS idx_promotional_codes_active ON promotional_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_promotional_codes_valid ON promotional_codes(valid_from, valid_until);

-- Triggers for updated_at
CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_driver_payouts_updated_at BEFORE UPDATE ON driver_payouts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_accounts_updated_at BEFORE UPDATE ON bank_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_disputes_updated_at BEFORE UPDATE ON payment_disputes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_promotional_codes_updated_at BEFORE UPDATE ON promotional_codes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
