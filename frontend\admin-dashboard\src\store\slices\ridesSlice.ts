import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Ride {
  id: string;
  passengerId: string;
  driverId?: string;
  pickupLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  destination: {
    latitude: number;
    longitude: number;
    address: string;
  };
  status: 'REQUESTED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  fare: number;
  distance: number;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

interface RidesState {
  rides: Ride[];
  currentRide: Ride | null;
  loading: boolean;
  error: string | null;
  totalRides: number;
  activeRides: number;
  completedRides: number;
}

const initialState: RidesState = {
  rides: [],
  currentRide: null,
  loading: false,
  error: null,
  totalRides: 0,
  activeRides: 0,
  completedRides: 0,
};

// Async thunks
export const fetchRides = createAsyncThunk(
  'rides/fetchRides',
  async (params?: { page?: number; limit?: number; status?: string }) => {
    const { ridesService } = await import('../../services/ridesService');
    const response = await ridesService.getRides(params);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب الرحلات');
    }

    return response;
  }
);

export const fetchRideById = createAsyncThunk(
  'rides/fetchRideById',
  async (rideId: string) => {
    const { ridesService } = await import('../../services/ridesService');
    const response = await ridesService.getRideById(rideId);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب تفاصيل الرحلة');
    }

    return response;
  }
);

const ridesSlice = createSlice({
  name: 'rides',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentRide: (state, action: PayloadAction<Ride | null>) => {
      state.currentRide = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRides.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRides.fulfilled, (state, action) => {
        state.loading = false;
        state.rides = action.payload.data;
        state.totalRides = action.payload.total || action.payload.data.length;
      })
      .addCase(fetchRides.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب الرحلات';
      })
      .addCase(fetchRideById.fulfilled, (state, action) => {
        state.currentRide = action.payload.data;
      });
  },
});

export const { clearError, setCurrentRide } = ridesSlice.actions;
export default ridesSlice.reducer;
