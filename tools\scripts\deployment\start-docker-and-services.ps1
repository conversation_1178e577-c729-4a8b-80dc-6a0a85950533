# Start Docker Desktop and TecnoDrive Services
Write-Host "🐳 Starting Docker Desktop and TecnoDrive Platform..." -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Gray

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to start Docker Desktop
function Start-DockerDesktop {
    Write-Host "🔄 Starting Docker Desktop..." -ForegroundColor Cyan
    
    # Try to find Docker Desktop executable
    $dockerPaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    $dockerExe = $null
    foreach ($path in $dockerPaths) {
        if (Test-Path $path) {
            $dockerExe = $path
            break
        }
    }
    
    if ($dockerExe) {
        Write-Host "  📍 Found Docker Desktop at: $dockerExe" -ForegroundColor Yellow
        Start-Process -FilePath $dockerExe -WindowStyle Hidden
        Write-Host "  🔄 Docker Desktop is starting..." -ForegroundColor Cyan
        
        # Wait for Docker to be ready
        $timeout = 120 # 2 minutes
        $elapsed = 0
        
        while (-not (Test-DockerRunning) -and $elapsed -lt $timeout) {
            Write-Host "  ⏳ Waiting for Docker to be ready... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
            Start-Sleep -Seconds 5
            $elapsed += 5
        }
        
        if (Test-DockerRunning) {
            Write-Host "  ✅ Docker Desktop is ready!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ Docker Desktop failed to start within $timeout seconds" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "  ❌ Docker Desktop executable not found" -ForegroundColor Red
        Write-Host "  💡 Please install Docker Desktop or start it manually" -ForegroundColor Cyan
        return $false
    }
}

# Check if Docker is already running
if (Test-DockerRunning) {
    Write-Host "✅ Docker is already running" -ForegroundColor Green
} else {
    Write-Host "⚠️  Docker is not running" -ForegroundColor Yellow
    
    if (-not (Start-DockerDesktop)) {
        Write-Host "❌ Failed to start Docker Desktop" -ForegroundColor Red
        Write-Host "💡 Please start Docker Desktop manually and run this script again" -ForegroundColor Cyan
        exit 1
    }
}

# Now start TecnoDrive services
Write-Host "`n🚀 Starting TecnoDrive Platform Services..." -ForegroundColor Green

# Step 1: Start Infrastructure
Write-Host "`n📦 Step 1: Starting Infrastructure Services..." -ForegroundColor Cyan
Write-Host "  🔄 Starting PostgreSQL and Redis..." -ForegroundColor Yellow

try {
    docker compose -f docker-compose.all-services.yml up -d postgres redis
    Write-Host "  ✅ Infrastructure services started" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Failed to start infrastructure services" -ForegroundColor Red
    Write-Host "  Error: $_" -ForegroundColor Red
}

Write-Host "  ⏳ Waiting for infrastructure to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Step 2: Start Service Discovery
Write-Host "`n🔧 Step 2: Starting Service Discovery..." -ForegroundColor Cyan
Write-Host "  🔄 Starting Eureka Server..." -ForegroundColor Yellow

try {
    docker compose -f docker-compose.all-services.yml up -d eureka-server
    Write-Host "  ✅ Eureka Server started" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Failed to start Eureka Server" -ForegroundColor Red
}

Write-Host "  ⏳ Waiting for Eureka to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Step 3: Start API Gateway
Write-Host "`n🌐 Step 3: Starting API Gateway..." -ForegroundColor Cyan
Write-Host "  🔄 Starting API Gateway..." -ForegroundColor Yellow

try {
    docker compose -f docker-compose.all-services.yml up -d api-gateway
    Write-Host "  ✅ API Gateway started" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Failed to start API Gateway" -ForegroundColor Red
}

Write-Host "  ⏳ Waiting for API Gateway..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Step 4: Start Core Services
Write-Host "`n🎯 Step 4: Starting Core Services..." -ForegroundColor Cyan
$coreServices = @("auth-service", "user-service", "ride-service")

foreach ($service in $coreServices) {
    Write-Host "  🔄 Starting $service..." -ForegroundColor Yellow
    try {
        docker compose -f docker-compose.all-services.yml up -d $service
        Write-Host "  ✅ $service started" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to start $service" -ForegroundColor Red
    }
    Start-Sleep -Seconds 5
}

Write-Host "  ⏳ Waiting for core services..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Step 5: Start Business Services
Write-Host "`n💼 Step 5: Starting Business Services..." -ForegroundColor Cyan
$businessServices = @(
    "fleet-service", "location-service", "payment-service", 
    "parcel-service", "notification-service"
)

foreach ($service in $businessServices) {
    Write-Host "  🔄 Starting $service..." -ForegroundColor Yellow
    try {
        docker compose -f docker-compose.all-services.yml up -d $service
        Write-Host "  ✅ $service started" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to start $service" -ForegroundColor Red
    }
    Start-Sleep -Seconds 3
}

Write-Host "  ⏳ Waiting for business services..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Step 6: Start Support Services
Write-Host "`n🔧 Step 6: Starting Support Services..." -ForegroundColor Cyan
$supportServices = @(
    "analytics-service", "hr-service", "financial-service", "saas-management-service"
)

foreach ($service in $supportServices) {
    Write-Host "  🔄 Starting $service..." -ForegroundColor Yellow
    try {
        docker compose -f docker-compose.all-services.yml up -d $service
        Write-Host "  ✅ $service started" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to start $service" -ForegroundColor Red
    }
    Start-Sleep -Seconds 3
}

# Final wait for all services
Write-Host "`n⏳ Final wait for all services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check final status
Write-Host "`n📊 Final Service Status:" -ForegroundColor Cyan
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "tecno" }
    if ($containers) {
        Write-Host "✅ Running Services:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "⚠️  No TecnoDrive services found running" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking service status" -ForegroundColor Red
}

# Service URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • Eureka Dashboard:    http://localhost:8761" -ForegroundColor White
Write-Host "  • API Gateway:         http://localhost:8080" -ForegroundColor White
Write-Host "  • Auth Service:        http://localhost:8081/actuator/health" -ForegroundColor White
Write-Host "  • Ride Service:        http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "  • User Service:        http://localhost:8083/actuator/health" -ForegroundColor White
Write-Host "  • Fleet Service:       http://localhost:8084/actuator/health" -ForegroundColor White
Write-Host "  • Location Service:    http://localhost:8085/actuator/health" -ForegroundColor White
Write-Host "  • Payment Service:     http://localhost:8086/actuator/health" -ForegroundColor White
Write-Host "  • Parcel Service:      http://localhost:8087/actuator/health" -ForegroundColor White
Write-Host "  • Notification Service: http://localhost:8088/actuator/health" -ForegroundColor White
Write-Host "  • Analytics Service:   http://localhost:8089/actuator/health" -ForegroundColor White
Write-Host "  • HR Service:          http://localhost:8090/actuator/health" -ForegroundColor White
Write-Host "  • Financial Service:   http://localhost:8091/actuator/health" -ForegroundColor White
Write-Host "  • SaaS Management:     http://localhost:8092/actuator/health" -ForegroundColor White

Write-Host "`n🎉 TecnoDrive Platform startup completed!" -ForegroundColor Green
Write-Host "💡 Use 'docker ps' to check service status" -ForegroundColor Cyan
Write-Host "💡 Use 'docker logs [service-name]' to check service logs" -ForegroundColor Cyan
Write-Host "💡 Run 'powershell -ExecutionPolicy Bypass -File final-service-status.ps1' for health check" -ForegroundColor Cyan
