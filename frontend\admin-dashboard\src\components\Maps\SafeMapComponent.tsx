import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Button
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  MyLocation as MyLocationIcon,
  Layers as LayersIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

// Import Leaflet directly
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix Leaflet default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface SafeMapComponentProps {
  height?: string;
}

// Map providers
const MAP_PROVIDERS = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors'
  },
  {
    id: 'cartodb-light',
    name: 'CartoDB Light',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  }
];

const SafeMapComponent: React.FC<SafeMapComponentProps> = ({ height = '600px' }) => {
  // Refs
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const tileLayerRef = useRef<L.TileLayer | null>(null);
  const initializationRef = useRef<boolean>(false);
  
  // State
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');
  const [mapReady, setMapReady] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  // Fixed center for Riyadh
  const center: [number, number] = [24.7136, 46.6753];
  const zoom = 12;

  // Safe map initialization
  const initializeMap = useCallback(() => {
    if (initializationRef.current || !mapContainerRef.current || mapInstanceRef.current) {
      return;
    }

    try {
      initializationRef.current = true;
      console.log('🗺️ Starting safe map initialization...');

      // Clear container
      mapContainerRef.current.innerHTML = '';
      
      // Create unique container ID
      const containerId = `safe-map-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      mapContainerRef.current.id = containerId;

      // Create map instance with error handling
      const map = L.map(mapContainerRef.current, {
        center: center,
        zoom: zoom,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        dragging: true,
        touchZoom: true,
        boxZoom: true,
        keyboard: true,
        attributionControl: true
      });

      // Add error handling
      map.on('error', (e) => {
        console.error('❌ Map error:', e);
        setInitError('خطأ في تحميل الخريطة');
      });

      // Add tile layer
      const provider = MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
      const tileLayer = L.tileLayer(provider.url, {
        attribution: provider.attribution,
        maxZoom: 19,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      });

      tileLayer.on('tileerror', (e) => {
        console.warn('⚠️ Tile loading error:', e);
      });

      tileLayer.addTo(map);
      tileLayerRef.current = tileLayer;
      mapInstanceRef.current = map;

      console.log('✅ Safe map initialization completed');
      setMapReady(true);
      setInitError(null);

    } catch (error) {
      console.error('❌ Map initialization failed:', error);
      setInitError(error instanceof Error ? error.message : 'فشل في تهيئة الخريطة');
      initializationRef.current = false;
    }
  }, [selectedProvider]);

  // Initialize map on mount
  useEffect(() => {
    const timer = setTimeout(initializeMap, 100);
    
    return () => {
      clearTimeout(timer);
      
      // Cleanup
      if (mapInstanceRef.current) {
        console.log('🧹 Cleaning up map...');
        
        // Clear markers
        markersRef.current.forEach(marker => {
          try {
            mapInstanceRef.current?.removeLayer(marker);
          } catch (e) {
            console.warn('Warning cleaning marker:', e);
          }
        });
        markersRef.current = [];
        
        // Remove tile layer
        if (tileLayerRef.current) {
          try {
            mapInstanceRef.current.removeLayer(tileLayerRef.current);
          } catch (e) {
            console.warn('Warning cleaning tile layer:', e);
          }
          tileLayerRef.current = null;
        }
        
        // Remove map
        try {
          mapInstanceRef.current.remove();
        } catch (e) {
          console.warn('Warning removing map:', e);
        }
        
        mapInstanceRef.current = null;
        initializationRef.current = false;
        setMapReady(false);
      }
    };
  }, [initializeMap]);

  // Load vehicles
  useEffect(() => {
    loadVehicles();
  }, []);

  // Update tile layer when provider changes
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    try {
      const newProvider = MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
      
      // Remove old tile layer
      if (tileLayerRef.current) {
        mapInstanceRef.current.removeLayer(tileLayerRef.current);
      }
      
      // Add new tile layer
      const newTileLayer = L.tileLayer(newProvider.url, {
        attribution: newProvider.attribution,
        maxZoom: 19,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      });
      
      newTileLayer.addTo(mapInstanceRef.current);
      tileLayerRef.current = newTileLayer;
      
      console.log('🔄 Tile layer updated to:', newProvider.name);
    } catch (error) {
      console.error('❌ Error updating tile layer:', error);
    }
  }, [selectedProvider, mapReady]);

  // Update markers when vehicles change
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    try {
      // Clear existing markers
      markersRef.current.forEach(marker => {
        mapInstanceRef.current?.removeLayer(marker);
      });
      markersRef.current = [];

      // Add new markers
      vehicles.forEach(vehicle => {
        const marker = createVehicleMarker(vehicle);
        if (marker && mapInstanceRef.current) {
          marker.addTo(mapInstanceRef.current);
          markersRef.current.push(marker);
        }
      });

      console.log(`🚗 Updated ${vehicles.length} vehicle markers`);
    } catch (error) {
      console.error('❌ Error updating markers:', error);
    }
  }, [vehicles, mapReady]);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
          setError(null);
        }
      } else {
        throw new Error('Failed to load vehicles');
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Fallback to mock data
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'Ahmed Mohamed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'Sara Ahmed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_003',
          lat: 24.7100,
          lng: 46.6700,
          speed: 0,
          heading: 0,
          status: 'idle',
          driver: 'Mohamed Ali',
          lastUpdate: new Date().toISOString()
        }
      ]);
      
      setError('Using mock vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const createVehicleMarker = (vehicle: Vehicle): L.Marker | null => {
    try {
      const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
      
      const icon = L.divIcon({
        html: `
          <div style="
            background-color: ${color};
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
          ">
            🚗
          </div>
        `,
        className: 'safe-vehicle-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      const marker = L.marker([vehicle.lat, vehicle.lng], { icon });
      
      // Add popup
      const popupContent = `
        <div style="min-width: 200px;">
          <h3>مركبة ${vehicle.id}</h3>
          <p><strong>السائق:</strong> ${vehicle.driver}</p>
          <p><strong>السرعة:</strong> ${vehicle.speed} كم/س</p>
          <p><strong>الحالة:</strong> ${vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}</p>
          <p><strong>الإحداثيات:</strong> ${vehicle.lat.toFixed(4)}, ${vehicle.lng.toFixed(4)}</p>
          <p><strong>آخر تحديث:</strong> ${new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}</p>
        </div>
      `;
      
      marker.bindPopup(popupContent);
      
      return marker;
    } catch (error) {
      console.error('Error creating vehicle marker:', error);
      return null;
    }
  };

  const handleRefresh = () => {
    loadVehicles();
  };

  const handleRetryInit = () => {
    setInitError(null);
    initializationRef.current = false;
    setMapReady(false);
    setTimeout(initializeMap, 100);
  };

  // Show initialization error
  if (initError) {
    return (
      <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: 3
          }}
        >
          <ErrorIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            فشل في تحميل الخريطة
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
            {initError}
          </Typography>
          <Button variant="contained" onClick={handleRetryInit} startIcon={<RefreshIcon />}>
            إعادة المحاولة
          </Button>
        </Box>
      </Paper>
    );
  }

  // Show loading
  if (loading && vehicles.length === 0) {
    return (
      <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            جاري تحميل الخريطة...
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: 16,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {/* Provider Selection */}
        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <FormControl fullWidth size="small">
              <InputLabel>مزود الخريطة</InputLabel>
              <Select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                label="مزود الخريطة"
              >
                {MAP_PROVIDERS.map(provider => (
                  <MenuItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Card>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Box display="flex" gap={1}>
              <Tooltip title="تحديث">
                <IconButton onClick={handleRefresh} size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="الطبقات">
                <IconButton size="small">
                  <LayersIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Vehicle Stats */}
      {vehicles.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <Typography variant="subtitle2" gutterBottom>
                إحصائيات المركبات
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`المجموع: ${vehicles.length}`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                  size="small"
                  color="success"
                />
                <Chip
                  label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                  size="small"
                  color="warning"
                />
              </Box>
              {error && (
                <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
                  {error}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Map Container */}
      <div
        ref={mapContainerRef}
        style={{
          height: '100%',
          width: '100%',
          position: 'relative'
        }}
      />
    </Paper>
  );
};

export default SafeMapComponent;
