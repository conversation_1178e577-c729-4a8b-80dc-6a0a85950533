// Smart API Service - Automatically switches between real and mock data
import { apiMethods, ApiResponse, checkServiceHealth, SERVICE_URLS } from './api';
import { MockService } from './mockService';

interface ServiceStatus {
  name: string;
  url: string;
  isHealthy: boolean;
  lastChecked: Date;
  port: number;
}

class SmartApiService {
  private serviceStatuses: Map<string, ServiceStatus> = new Map();
  private healthCheckInterval: number = 30000; // 30 seconds
  private healthCheckTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeServices();
    this.startHealthChecking();
  }

  private initializeServices() {
    const services = [
      { name: 'ride', url: 'http://localhost:8082', port: 8082 },
      { name: 'user', url: 'http://localhost:8083', port: 8083 },
      { name: 'fleet', url: 'http://localhost:8084', port: 8084 },
      { name: 'location', url: 'http://localhost:8085', port: 8085 },
      { name: 'payment', url: 'http://localhost:8086', port: 8086 },
      { name: 'parcel', url: 'http://localhost:8087', port: 8087 },
      { name: 'notification', url: 'http://localhost:8088', port: 8088 },
      { name: 'analytics', url: 'http://localhost:8089', port: 8089 },
      { name: 'hr', url: 'http://localhost:8090', port: 8090 },
      { name: 'financial', url: 'http://localhost:8091', port: 8091 },
      { name: 'auth', url: 'http://localhost:8081', port: 8081 },
      { name: 'gateway', url: 'http://localhost:8080', port: 8080 },
    ];

    services.forEach(service => {
      this.serviceStatuses.set(service.name, {
        name: service.name,
        url: service.url,
        isHealthy: false,
        lastChecked: new Date(),
        port: service.port
      });
    });
  }

  private async startHealthChecking() {
    // Initial health check
    await this.checkAllServicesHealth();

    // Set up periodic health checks
    this.healthCheckTimer = setInterval(async () => {
      await this.checkAllServicesHealth();
    }, this.healthCheckInterval);
  }

  private async checkAllServicesHealth() {
    const promises = Array.from(this.serviceStatuses.keys()).map(async (serviceName) => {
      const service = this.serviceStatuses.get(serviceName)!;
      try {
        const isHealthy = await this.checkSingleServiceHealth(service.url);
        this.serviceStatuses.set(serviceName, {
          ...service,
          isHealthy,
          lastChecked: new Date()
        });
      } catch (error) {
        this.serviceStatuses.set(serviceName, {
          ...service,
          isHealthy: false,
          lastChecked: new Date()
        });
      }
    });

    await Promise.all(promises);
    this.logServiceStatuses();
  }

  private async checkSingleServiceHealth(url: string): Promise<boolean> {
    try {
      // إزالة timeout من RequestInit واستخدام AbortController بدلاً من ذلك
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${url}/actuator/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  private logServiceStatuses() {
    const healthyServices = Array.from(this.serviceStatuses.values()).filter(s => s.isHealthy);
    const unhealthyServices = Array.from(this.serviceStatuses.values()).filter(s => !s.isHealthy);

    console.log(`🟢 Healthy Services (${healthyServices.length}):`, 
      healthyServices.map(s => `${s.name}:${s.port}`).join(', '));
    
    if (unhealthyServices.length > 0) {
      console.log(`🔴 Unhealthy Services (${unhealthyServices.length}):`, 
        unhealthyServices.map(s => `${s.name}:${s.port}`).join(', '));
    }
  }

  public isServiceHealthy(serviceName: string): boolean {
    const service = this.serviceStatuses.get(serviceName);
    return service?.isHealthy || false;
  }

  public getServiceStatus(serviceName: string): ServiceStatus | null {
    return this.serviceStatuses.get(serviceName) || null;
  }

  public getAllServiceStatuses(): ServiceStatus[] {
    return Array.from(this.serviceStatuses.values());
  }

  // Smart API methods that automatically fallback to mock data
  public async getRides(params?: any): Promise<ApiResponse<any[]>> {
    if (this.isServiceHealthy('ride')) {
      try {
        const response = await apiMethods.get(`http://localhost:8082/api/rides`, { params });
        return response.data;
      } catch (error) {
        console.warn('Ride service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for rides');
    return await MockService.getRides(params);
  }

  public async getUsers(params?: any): Promise<ApiResponse<any[]>> {
    if (this.isServiceHealthy('user')) {
      try {
        const response = await apiMethods.get(`http://localhost:8083/api/users`, { params });
        return response.data;
      } catch (error) {
        console.warn('User service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for users');
    return await MockService.getUsers(params);
  }

  public async getVehicles(params?: any): Promise<ApiResponse<any[]>> {
    if (this.isServiceHealthy('fleet')) {
      try {
        const response = await apiMethods.get(`http://localhost:8084/api/fleet/vehicles`, { params });
        return response.data;
      } catch (error) {
        console.warn('Fleet service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for vehicles');
    return await MockService.getVehicles(params);
  }

  public async getParcels(params?: any): Promise<ApiResponse<any[]>> {
    if (this.isServiceHealthy('parcel')) {
      try {
        const response = await apiMethods.get(`http://localhost:8087/api/parcels`, { params });
        return response.data;
      } catch (error) {
        console.warn('Parcel service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for parcels');
    return await MockService.getParcels(params);
  }

  public async getPayments(params?: any): Promise<ApiResponse<any[]>> {
    if (this.isServiceHealthy('payment')) {
      try {
        const response = await apiMethods.get(`http://localhost:8086/api/payments`, { params });
        return response.data;
      } catch (error) {
        console.warn('Payment service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for payments');
    return await MockService.getPayments(params);
  }

  public async getAnalytics(): Promise<ApiResponse<any>> {
    if (this.isServiceHealthy('analytics')) {
      try {
        const response = await apiMethods.get(`http://localhost:8089/api/analytics/dashboard`);
        return response.data;
      } catch (error) {
        console.warn('Analytics service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for analytics');
    return await MockService.getAnalytics();
  }

  public async getDashboardStats(): Promise<ApiResponse<any>> {
    if (this.isServiceHealthy('gateway')) {
      try {
        const response = await apiMethods.get(`http://localhost:8080/api/dashboard/stats`);
        return response.data;
      } catch (error) {
        console.warn('Gateway service failed, falling back to mock data:', error);
      }
    }
    
    console.log('🎭 Using mock data for dashboard stats');
    return await MockService.getDashboardStats();
  }

  // Real-time location updates
  public async updateVehicleLocation(vehicleId: string, location: any): Promise<ApiResponse<any>> {
    if (this.isServiceHealthy('location')) {
      try {
        const response = await apiMethods.post(`http://localhost:8085/api/locations/update`, {
          vehicleId,
          ...location
        });
        return response.data;
      } catch (error) {
        console.warn('Location service failed:', error);
        throw error;
      }
    }
    
    throw new Error('Location service is not available');
  }

  public async sendAlert(alert: any): Promise<ApiResponse<any>> {
    if (this.isServiceHealthy('location')) {
      try {
        const response = await apiMethods.post(`http://localhost:8085/api/locations/alert`, alert);
        return response.data;
      } catch (error) {
        console.warn('Location service failed for alert:', error);
        throw error;
      }
    }
    
    throw new Error('Location service is not available for alerts');
  }

  public destroy() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }
}

// Create singleton instance
export const smartApiService = new SmartApiService();
export default smartApiService;
