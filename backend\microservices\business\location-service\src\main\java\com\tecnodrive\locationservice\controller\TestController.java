package com.tecnodrive.locationservice.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Test Controller for API testing
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    @GetMapping("/hello")
    public Map<String, Object> hello() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Hello from Location Service!");
        response.put("status", "success");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    @GetMapping("/map-config")
    public Map<String, Object> getMapConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("defaultCenter", Map.of("lat", 24.7136, "lng", 46.6753));
        config.put("defaultZoom", 12);
        config.put("maxZoom", 19);
        config.put("provider", "openstreetmap");
        config.put("status", "working");
        return config;
    }

    @GetMapping("/tiles")
    public Map<String, Object> getTileServers() {
        Map<String, Object> tiles = new HashMap<>();
        
        tiles.put("openstreetmap", Map.of(
            "url", "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            "attribution", "© OpenStreetMap contributors",
            "maxZoom", 19
        ));
        
        tiles.put("cartodb-light", Map.of(
            "url", "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
            "attribution", "© CartoDB",
            "maxZoom", 19
        ));
        
        return Map.of(
            "success", true,
            "data", tiles,
            "timestamp", System.currentTimeMillis()
        );
    }
}
