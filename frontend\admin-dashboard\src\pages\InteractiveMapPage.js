import React, { useState, useEffect } from 'react';
import SimpleInteractiveMap from '../components/SimpleInteractiveMap';
// import InteractiveMap from '../components/InteractiveMap'; // Will be enabled after libraries are installed
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Dropdown,
  Menu,
  notification
} from '@mui/material';
import {
  DirectionsCar as CarOutlined,
  LocationOn as EnvironmentOutlined,
  Schedule as ClockCircleOutlined,
  Warning as WarningOutlined,
  Refresh as ReloadOutlined,
  Settings as SettingOutlined,
  Fullscreen as FullscreenOutlined,
  Download as DownloadOutlined
} from '@mui/icons-material';
import './InteractiveMapPage.css';

const InteractiveMapPage = () => {
  const [mapStats, setMapStats] = useState({
    activeVehicles: 0,
    totalRoutes: 0,
    trafficAlerts: 0,
    geofenceViolations: 0
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch map statistics
  useEffect(() => {
    const fetchMapStats = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/api/map/stats`);
        if (response.ok) {
          const data = await response.json();
          setMapStats(data);
          setLastUpdate(new Date());
        }
      } catch (error) {
        console.error('Error fetching map stats:', error);
      }
    };

    fetchMapStats();

    // Auto refresh every 30 seconds
    const interval = autoRefresh ? setInterval(fetchMapStats, 30000) : null;

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle export map data
  const exportMapData = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/api/map/export`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `map-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        notification.success({
          message: 'تم التصدير بنجاح',
          description: 'تم تصدير بيانات الخريطة بنجاح',
        });
      }
    } catch (error) {
      notification.error({
        message: 'خطأ في التصدير',
        description: 'فشل في تصدير بيانات الخريطة',
      });
    }
  };

  // Settings menu
  const settingsMenu = (
    <Menu>
      <Menu.Item key="refresh" onClick={() => setAutoRefresh(!autoRefresh)}>
        {autoRefresh ? 'إيقاف التحديث التلقائي' : 'تفعيل التحديث التلقائي'}
      </Menu.Item>
      <Menu.Item key="export" onClick={exportMapData}>
        تصدير بيانات الخريطة
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="settings">
        إعدادات الخريطة
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={`interactive-map-page ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Page Header */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <EnvironmentOutlined /> الخرائط التفاعلية
          </h1>
          <p>تتبع المركبات والطرق في الوقت الفعلي</p>
        </div>
        
        <div className="header-actions">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => window.location.reload()}
            >
              تحديث
            </Button>
            
            <Dropdown overlay={settingsMenu} placement="bottomRight">
              <Button icon={<SettingOutlined />}>
                الإعدادات
              </Button>
            </Dropdown>
            
            <Button 
              icon={<FullscreenOutlined />} 
              onClick={toggleFullscreen}
            >
              ملء الشاشة
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      {!isFullscreen && (
        <Row gutter={[16, 16]} className="stats-row">
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="المركبات النشطة"
                value={mapStats.activeVehicles}
                prefix={<CarOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="الطرق النشطة"
                value={mapStats.totalRoutes}
                prefix={<EnvironmentOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="تنبيهات المرور"
                value={mapStats.trafficAlerts}
                prefix={<WarningOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="انتهاكات المناطق"
                value={mapStats.geofenceViolations}
                prefix={<ClockCircleOutlined style={{ color: '#f5222d' }} />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Interactive Map */}
      <div className="map-section">
        <Card 
          className="map-card"
          bodyStyle={{ padding: 0 }}
          title={
            <div className="map-card-header">
              <span>الخريطة التفاعلية</span>
              <div className="map-info">
                <span className="last-update">
                  آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
                </span>
                <span className={`auto-refresh ${autoRefresh ? 'active' : 'inactive'}`}>
                  {autoRefresh ? '🟢 تحديث تلقائي' : '🔴 تحديث يدوي'}
                </span>
              </div>
            </div>
          }
        >
          <SimpleInteractiveMap />
        </Card>
      </div>

      {/* Map Legend */}
      {!isFullscreen && (
        <Card className="map-legend" title="دليل الخريطة">
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon vehicle-active"></span>
                <span>مركبة نشطة</span>
              </div>
            </Col>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon vehicle-busy"></span>
                <span>مركبة مشغولة</span>
              </div>
            </Col>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon traffic-heavy"></span>
                <span>مرور كثيف</span>
              </div>
            </Col>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon traffic-light"></span>
                <span>مرور خفيف</span>
              </div>
            </Col>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon geofence-active"></span>
                <span>منطقة محددة نشطة</span>
              </div>
            </Col>
            <Col span={12}>
              <div className="legend-item">
                <span className="legend-icon route-active"></span>
                <span>طريق نشط</span>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* Quick Actions */}
      {!isFullscreen && (
        <Card className="quick-actions" title="إجراءات سريعة">
          <Space wrap>
            <Button type="primary" icon={<CarOutlined />}>
              إضافة مركبة جديدة
            </Button>
            <Button icon={<EnvironmentOutlined />}>
              إنشاء منطقة محددة
            </Button>
            <Button icon={<WarningOutlined />}>
              عرض التنبيهات
            </Button>
            <Button icon={<DownloadOutlined />} onClick={exportMapData}>
              تصدير البيانات
            </Button>
          </Space>
        </Card>
      )}
    </div>
  );
};

export default InteractiveMapPage;
