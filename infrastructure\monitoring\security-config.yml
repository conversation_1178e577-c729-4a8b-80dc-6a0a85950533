# TECNO DRIVE Platform - Enhanced Security Configuration
# This file contains security settings and best practices

security:
  jwt:
    secret: ${JWT_SECRET:TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2024}
    expiration: 86400 # 24 hours
    refresh-expiration: 604800 # 7 days
    
  cors:
    allowed-origins:
      - "http://localhost:3000"
      - "http://localhost:3001"
      - "http://localhost:3002"
      - "https://tecnodrive.com"
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - "*"
    allow-credentials: true
    
  rate-limiting:
    enabled: true
    requests-per-minute: 100
    burst-capacity: 200
    
  ssl:
    enabled: false # Set to true in production
    keystore-path: /etc/ssl/tecnodrive.p12
    keystore-password: ${SSL_KEYSTORE_PASSWORD}
    
  oauth2:
    google:
      client-id: ${GOOGLE_CLIENT_ID}
      client-secret: ${GOOGLE_CLIENT_SECRET}
    github:
      client-id: ${GITHUB_CLIENT_ID}
      client-secret: ${GITHUB_CLIENT_SECRET}

monitoring:
  metrics:
    enabled: true
    export:
      prometheus:
        enabled: true
        endpoint: /actuator/prometheus
        
  health:
    enabled: true
    show-details: when-authorized
    
  tracing:
    enabled: true
    zipkin:
      base-url: http://zipkin:9411
      
logging:
  level:
    com.tecnodrive: INFO
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
  file:
    name: logs/tecnodrive.log
    max-size: 100MB
    max-history: 30

database:
  security:
    encrypt-connection: true
    ssl-mode: require
    
  connection-pool:
    maximum-pool-size: 20
    minimum-idle: 5
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000

redis:
  security:
    password: ${REDIS_PASSWORD}
    ssl: false # Set to true in production
    
  connection-pool:
    max-active: 20
    max-idle: 10
    min-idle: 5
    max-wait: 3000
