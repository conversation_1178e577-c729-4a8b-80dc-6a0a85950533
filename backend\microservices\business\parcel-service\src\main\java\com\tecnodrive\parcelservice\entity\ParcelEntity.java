package com.tecnodrive.parcelservice.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Entity لجدول الطرود في قاعدة البيانات
 */
@Entity
@Table(name = "parcel_info", indexes = {
    @Index(name = "idx_parcel_barcode", columnList = "barcode"),
    @Index(name = "idx_parcel_user_id", columnList = "user_id"),
    @Index(name = "idx_parcel_status", columnList = "status"),
    @Index(name = "idx_parcel_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParcelEntity {
    
    @Id
    @Column(name = "parcel_id", length = 50)
    private String parcelId;
    
    @Column(name = "user_id", nullable = false, length = 50)
    private String userId;
    
    @Column(name = "barcode", unique = true, nullable = false, length = 20)
    private String barcode;
    
    @Column(name = "sender_name", nullable = false, length = 100)
    private String senderName;
    
    @Column(name = "receiver_name", nullable = false, length = 100)
    private String receiverName;
    
    @Column(name = "sender_address", nullable = false, length = 255)
    private String senderAddress;
    
    @Column(name = "receiver_address", nullable = false, length = 255)
    private String receiverAddress;
    
    @Column(name = "sender_phone", length = 20)
    private String senderPhone;
    
    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;
    
    @Column(name = "weight_kg", nullable = false)
    private Double weightKg;
    
    @Embedded
    private Dimensions dimensions;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ParcelStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", length = 10)
    private ParcelPriority priority;
    
    @Column(name = "fragile")
    private Boolean fragile;
    
    @Column(name = "insurance_value")
    private Double insuranceValue;
    
    @Column(name = "estimated_cost")
    private Double estimatedCost;
    
    @Column(name = "actual_cost")
    private Double actualCost;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "estimated_delivery_date")
    private LocalDateTime estimatedDeliveryDate;
    
    @Column(name = "actual_delivery_date")
    private LocalDateTime actualDeliveryDate;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    // العلاقات
    @OneToMany(mappedBy = "parcel", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ParcelTrackingEntity> trackingHistory;
    
    @OneToMany(mappedBy = "parcel", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ParcelPaymentEntity> payments;
    
    @OneToMany(mappedBy = "parcel", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ParcelItemEntity> items;
    
    /**
     * الأبعاد المدمجة
     */
    @Embeddable
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Dimensions {
        
        @Column(name = "length_cm", nullable = false)
        private Integer lengthCm;
        
        @Column(name = "width_cm", nullable = false)
        private Integer widthCm;
        
        @Column(name = "height_cm", nullable = false)
        private Integer heightCm;
        
        /**
         * حساب الحجم
         */
        public Double getVolume() {
            return lengthCm * widthCm * heightCm * 1.0;
        }
        
        /**
         * حساب الوزن الحجمي
         */
        public Double getVolumetricWeight() {
            return getVolume() / 5000.0;
        }
    }
    
    /**
     * حالات الطرد
     */
    public enum ParcelStatus {
        CREATED("تم الإنشاء"),
        PICKED_UP("تم الاستلام"),
        IN_TRANSIT("في الطريق"),
        OUT_FOR_DELIVERY("خارج للتوصيل"),
        DELIVERED("تم التوصيل"),
        RETURNED("تم الإرجاع"),
        CANCELLED("ملغي"),
        LOST("مفقود"),
        DAMAGED("تالف");
        
        private final String arabicName;
        
        ParcelStatus(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    /**
     * أولويات الطرد
     */
    public enum ParcelPriority {
        LOW("منخفضة"),
        MEDIUM("متوسطة"),
        HIGH("عالية"),
        URGENT("عاجل");
        
        private final String arabicName;
        
        ParcelPriority(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    /**
     * تحديث حالة الطرد
     */
    public void updateStatus(ParcelStatus newStatus, String updatedBy) {
        this.status = newStatus;
        this.updatedBy = updatedBy;
        this.updatedAt = LocalDateTime.now();
        
        // تحديث تاريخ التوصيل الفعلي
        if (newStatus == ParcelStatus.DELIVERED) {
            this.actualDeliveryDate = LocalDateTime.now();
        }
    }
    
    /**
     * حساب التكلفة
     */
    public void calculateCost() {
        if (weightKg != null && dimensions != null) {
            double weightCost = weightKg * 10.0;
            double volumeCost = dimensions.getVolumetricWeight() * 8.0;
            double baseCost = 15.0;
            
            double totalCost = baseCost + Math.max(weightCost, volumeCost);
            
            if (Boolean.TRUE.equals(fragile)) {
                totalCost += 20.0;
            }
            
            if (insuranceValue != null && insuranceValue > 0) {
                totalCost += insuranceValue * 0.02;
            }
            
            this.estimatedCost = Math.round(totalCost * 100.0) / 100.0;
        }
    }
    
    /**
     * التحقق من إمكانية التعديل
     */
    public boolean isEditable() {
        return status == ParcelStatus.CREATED || status == ParcelStatus.PICKED_UP;
    }
    
    /**
     * التحقق من إمكانية الإلغاء
     */
    public boolean isCancellable() {
        return status != ParcelStatus.DELIVERED && 
               status != ParcelStatus.CANCELLED && 
               status != ParcelStatus.LOST;
    }
}
