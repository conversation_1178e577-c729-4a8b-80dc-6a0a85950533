import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typography,
  Divider,
} from '@mui/material';
import {
  Save as SaveIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

// Enhanced Components
import {
  TecnoCard,
  TecnoButton,
  StatsCard,
  LoadingSpinner,
  ToastNotification,
  useToast,
} from '../components/UI';

const ComponentsShowcase: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [toastOpen, setToastOpen] = useState(false);
  const { showSuccess, showError, showWarning, showInfo, ToastContainer } = useToast();

  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  const handleToastDemo = (type: string) => {
    switch (type) {
      case 'success':
        showSuccess('تم حفظ البيانات بنجاح!', 'نجح العملية');
        break;
      case 'error':
        showError('حدث خطأ أثناء حفظ البيانات', 'خطأ في النظام');
        break;
      case 'warning':
        showWarning('يرجى التحقق من البيانات المدخلة', 'تحذير');
        break;
      case 'info':
        showInfo('تم تحديث النظام إلى الإصدار الجديد', 'معلومات');
        break;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        معرض المكونات المحسنة
      </Typography>

      {/* TecnoCard Examples */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          TecnoCard - أنواع البطاقات
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6} lg={3}>
            <TecnoCard
              title="بطاقة افتراضية"
              subtitle="تصميم أساسي نظيف"
              variant="default"
            >
              <Typography variant="body2">
                هذه بطاقة بالتصميم الافتراضي مع ظلال خفيفة ومظهر نظيف.
              </Typography>
            </TecnoCard>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <TecnoCard
              title="بطاقة مرتفعة"
              subtitle="ظلال أكثر وضوحاً"
              variant="elevated"
              status="success"
            >
              <Typography variant="body2">
                بطاقة مرتفعة مع ظلال أكثر وضوحاً وشريط حالة أخضر.
              </Typography>
            </TecnoCard>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <TecnoCard
              title="بطاقة متدرجة"
              subtitle="خلفية متدرجة جميلة"
              variant="gradient"
              badge="جديد"
            >
              <Typography variant="body2">
                بطاقة بخلفية متدرجة وتأثير زجاجي مع badge تنبيه.
              </Typography>
            </TecnoCard>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <TecnoCard
              title="بطاقة محددة"
              subtitle="حدود واضحة"
              variant="outlined"
              hoverable
            >
              <Typography variant="body2">
                بطاقة بحدود واضحة مع تأثير hover للتفاعل.
              </Typography>
            </TecnoCard>
          </Grid>
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* TecnoButton Examples */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          TecnoButton - أنواع الأزرار
        </Typography>
        <Grid container spacing={2}>
          <Grid item>
            <TecnoButton variant="primary" icon={<SaveIcon />}>
              حفظ
            </TecnoButton>
          </Grid>
          <Grid item>
            <TecnoButton variant="secondary" icon={<EditIcon />}>
              تعديل
            </TecnoButton>
          </Grid>
          <Grid item>
            <TecnoButton variant="outline" icon={<ViewIcon />}>
              عرض
            </TecnoButton>
          </Grid>
          <Grid item>
            <TecnoButton variant="ghost" icon={<DownloadIcon />}>
              تحميل
            </TecnoButton>
          </Grid>
          <Grid item>
            <TecnoButton variant="gradient" icon={<UploadIcon />} shadow>
              رفع
            </TecnoButton>
          </Grid>
          <Grid item>
            <TecnoButton variant="danger" icon={<DeleteIcon />}>
              حذف
            </TecnoButton>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            أحجام مختلفة:
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TecnoButton variant="primary" size="small">
              صغير
            </TecnoButton>
            <TecnoButton variant="primary" size="medium">
              متوسط
            </TecnoButton>
            <TecnoButton variant="primary" size="large">
              كبير
            </TecnoButton>
          </Box>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            حالات خاصة:
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TecnoButton
              variant="primary"
              loading={loading}
              onClick={handleLoadingDemo}
            >
              تحميل
            </TecnoButton>
            <TecnoButton variant="primary" rounded shadow>
              مدور مع ظل
            </TecnoButton>
            <TecnoButton variant="outline" fullWidth sx={{ maxWidth: 200 }}>
              عرض كامل
            </TecnoButton>
          </Box>
        </Box>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* StatsCard Examples */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          StatsCard - بطاقات الإحصائيات
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard
              title="المبيعات اليومية"
              value="1,247"
              subtitle="عملية بيع"
              color="primary"
              trend={{
                value: 12.5,
                label: 'من أمس',
                period: 'يومياً',
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard
              title="الإيرادات"
              value="45,680 ريال"
              subtitle="إجمالي الشهر"
              color="success"
              trend={{
                value: -5.2,
                label: 'من الشهر الماضي',
                period: 'شهرياً',
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard
              title="المستخدمين النشطين"
              value="892"
              subtitle="مستخدم متصل"
              color="info"
              progress={{
                value: 78,
                label: 'معدل النشاط',
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard
              title="معدل الأداء"
              value="94.5%"
              subtitle="كفاءة النظام"
              color="warning"
              trend={{
                value: 0,
                label: 'مستقر',
                period: 'أسبوعياً',
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Loading Examples */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          LoadingSpinner - مؤشرات التحميل
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <TecnoCard title="تحميل دائري" variant="outlined">
              <LoadingSpinner variant="circular" size="medium" />
            </TecnoCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <TecnoCard title="تحميل نقطي" variant="outlined">
              <LoadingSpinner variant="dots" size="medium" />
            </TecnoCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <TecnoCard title="تحميل نبضي" variant="outlined">
              <LoadingSpinner variant="pulse" size="medium" />
            </TecnoCard>
          </Grid>
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Toast Examples */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          Toast Notifications - الإشعارات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <TecnoButton
            variant="primary"
            onClick={() => handleToastDemo('success')}
          >
            إشعار نجاح
          </TecnoButton>
          <TecnoButton
            variant="danger"
            onClick={() => handleToastDemo('error')}
          >
            إشعار خطأ
          </TecnoButton>
          <TecnoButton
            variant="outline"
            onClick={() => handleToastDemo('warning')}
          >
            إشعار تحذير
          </TecnoButton>
          <TecnoButton
            variant="ghost"
            onClick={() => handleToastDemo('info')}
          >
            إشعار معلومات
          </TecnoButton>
        </Box>
      </Box>

      {/* Toast Container */}
      <ToastContainer />
    </Box>
  );
};

export default ComponentsShowcase;
