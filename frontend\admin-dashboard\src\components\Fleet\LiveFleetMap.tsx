import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  DirectionsCar as CarIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  FilterList as FilterIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { fleetService, VehicleDto, LocationDto } from '../../services/fleetService';

// Mock Google Maps component (since we don't have the actual API key)
const MockGoogleMap: React.FC<{
  vehicles: VehicleDto[];
  selectedVehicle?: string;
  onVehicleSelect: (vehicleId: string) => void;
}> = ({ vehicles, selectedVehicle, onVehicleSelect }) => {
  return (
    <Box
      sx={{
        height: '100%',
        width: '100%',
        bgcolor: '#e8f5e8',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        border: '2px dashed #4caf50',
        borderRadius: 2,
      }}
    >
      <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
        🗺️ خريطة Google Maps التفاعلية
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
        عرض مواقع المركبات في الوقت الفعلي
        <br />
        (يتطلب مفتاح Google Maps API للتفعيل الكامل)
      </Typography>
      
      {/* Mock vehicle markers */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'center' }}>
        {vehicles.slice(0, 5).map((vehicle, index) => (
          <Paper
            key={vehicle.id}
            sx={{
              p: 2,
              cursor: 'pointer',
              border: selectedVehicle === vehicle.id ? '2px solid #1976d2' : '1px solid #ddd',
              bgcolor: selectedVehicle === vehicle.id ? '#e3f2fd' : 'white',
              '&:hover': { bgcolor: '#f5f5f5' },
            }}
            onClick={() => onVehicleSelect(vehicle.id)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: vehicle.status === 'ACTIVE' ? 'success.main' :
                          vehicle.status === 'MAINTENANCE' ? 'warning.main' :
                          vehicle.status === 'OFFLINE' ? 'error.main' : 'grey.500'
                }}
              >
                <CarIcon fontSize="small" />
              </Avatar>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                  {vehicle.plateNumber}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {vehicle.make} {vehicle.model}
                </Typography>
              </Box>
            </Box>
            {vehicle.currentLocation && (
              <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
                📍 {vehicle.currentLocation.address || 'موقع غير محدد'}
              </Typography>
            )}
          </Paper>
        ))}
      </Box>
      
      {/* Legend */}
      <Box sx={{ position: 'absolute', bottom: 16, left: 16 }}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
            دليل الألوان:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, bgcolor: 'success.main', borderRadius: '50%' }} />
              <Typography variant="caption">نشط</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, bgcolor: 'warning.main', borderRadius: '50%' }} />
              <Typography variant="caption">صيانة</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, bgcolor: 'error.main', borderRadius: '50%' }} />
              <Typography variant="caption">غير متصل</Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

const LiveFleetMap: React.FC = () => {
  const [vehicles, setVehicles] = useState<VehicleDto[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<VehicleDto[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [filterMake, setFilterMake] = useState<string>('ALL');
  const [loading, setLoading] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const wsConnections = useRef<{ [vehicleId: string]: boolean }>({});

  // Load vehicles data
  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fleetService.getVehicles();
      
      if (response.success && response.data) {
        setVehicles(response.data);
        setFilteredVehicles(response.data);
        
        // Start WebSocket connections for real-time updates
        response.data.forEach(vehicle => {
          if (vehicle.status === 'ACTIVE' && !wsConnections.current[vehicle.id]) {
            startLocationTracking(vehicle.id);
          }
        });
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
    } finally {
      setLoading(false);
    }
  };

  // Start real-time location tracking for a vehicle
  const startLocationTracking = (vehicleId: string) => {
    if (wsConnections.current[vehicleId]) return;
    
    wsConnections.current[vehicleId] = true;
    
    fleetService.connectToLocationUpdates(vehicleId, (locationData: LocationDto) => {
      // Update vehicle location in real-time
      setVehicles(prevVehicles => 
        prevVehicles.map(vehicle => 
          vehicle.id === vehicleId 
            ? { ...vehicle, currentLocation: locationData }
            : vehicle
        )
      );
    });
  };

  // Stop all WebSocket connections
  const stopAllLocationTracking = () => {
    fleetService.disconnectFromLocationUpdates();
    wsConnections.current = {};
  };

  useEffect(() => {
    loadVehicles();
    
    // Cleanup WebSocket connections on unmount
    return () => {
      stopAllLocationTracking();
    };
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...vehicles];
    
    if (filterStatus !== 'ALL') {
      filtered = filtered.filter(vehicle => vehicle.status === filterStatus);
    }
    
    if (filterMake !== 'ALL') {
      filtered = filtered.filter(vehicle => vehicle.make === filterMake);
    }
    
    setFilteredVehicles(filtered);
  }, [vehicles, filterStatus, filterMake]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: '🟢' },
      MAINTENANCE: { label: 'صيانة', color: 'warning' as const, icon: '🟠' },
      OFFLINE: { label: 'غير متصل', color: 'error' as const, icon: '🔴' },
      OUT_OF_SERVICE: { label: 'خارج الخدمة', color: 'default' as const, icon: '⚫' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: '⚪' 
    };
    
    return (
      <Chip
        label={`${config.icon} ${config.label}`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  // Get unique makes for filter
  const uniqueMakes = Array.from(new Set(vehicles.map(v => v.make))).sort();

  // Calculate stats
  const activeVehicles = vehicles.filter(v => v.status === 'ACTIVE').length;
  const maintenanceVehicles = vehicles.filter(v => v.status === 'MAINTENANCE').length;
  const offlineVehicles = vehicles.filter(v => v.status === 'OFFLINE').length;

  const selectedVehicleData = vehicles.find(v => v.id === selectedVehicle);

  return (
    <Box sx={{ height: isFullscreen ? '100vh' : 'calc(100vh - 200px)' }}>
      {/* Header */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          خريطة الأسطول الحية
        </Typography>
        <Typography variant="body1" color="text.secondary">
          تتبع مواقع المركبات في الوقت الفعلي
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', height: isFullscreen ? 'calc(100vh - 100px)' : '600px', gap: 2 }}>
        {/* Sidebar */}
        {!isFullscreen && (
          <Card sx={{ width: 350, display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              {/* Filters */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  فلاتر العرض
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControl size="small">
                    <InputLabel>الحالة</InputLabel>
                    <Select
                      value={filterStatus}
                      label="الحالة"
                      onChange={(e) => setFilterStatus(e.target.value)}
                    >
                      <MenuItem value="ALL">جميع الحالات</MenuItem>
                      <MenuItem value="ACTIVE">نشط</MenuItem>
                      <MenuItem value="MAINTENANCE">صيانة</MenuItem>
                      <MenuItem value="OFFLINE">غير متصل</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl size="small">
                    <InputLabel>الماركة</InputLabel>
                    <Select
                      value={filterMake}
                      label="الماركة"
                      onChange={(e) => setFilterMake(e.target.value)}
                    >
                      <MenuItem value="ALL">جميع الماركات</MenuItem>
                      {uniqueMakes.map((make) => (
                        <MenuItem key={make} value={make}>
                          {make}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>

              {/* Stats */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  إحصائيات سريعة
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">المركبات النشطة:</Typography>
                    <Chip label={activeVehicles} color="success" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">في الصيانة:</Typography>
                    <Chip label={maintenanceVehicles} color="warning" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">غير متصلة:</Typography>
                    <Chip label={offlineVehicles} color="error" size="small" />
                  </Box>
                </Box>
              </Box>

              {/* Vehicle List */}
              <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  قائمة المركبات ({filteredVehicles.length})
                </Typography>
                <List dense>
                  {filteredVehicles.map((vehicle) => (
                    <ListItem
                      key={vehicle.id}
                      button
                      selected={selectedVehicle === vehicle.id}
                      onClick={() => setSelectedVehicle(vehicle.id)}
                      sx={{
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        mb: 1,
                        '&.Mui-selected': {
                          bgcolor: 'primary.light',
                          color: 'primary.contrastText',
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            bgcolor: vehicle.status === 'ACTIVE' ? 'success.main' :
                                    vehicle.status === 'MAINTENANCE' ? 'warning.main' :
                                    vehicle.status === 'OFFLINE' ? 'error.main' : 'grey.500'
                          }}
                        >
                          <CarIcon fontSize="small" />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={vehicle.plateNumber}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {vehicle.make} {vehicle.model}
                            </Typography>
                            {getStatusChip(vehicle.status)}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>

              {/* Selected Vehicle Info */}
              {selectedVehicleData && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    المركبة المحددة:
                  </Typography>
                  <Typography variant="body2">
                    {selectedVehicleData.plateNumber} - {selectedVehicleData.make} {selectedVehicleData.model}
                  </Typography>
                  {selectedVehicleData.currentLocation && (
                    <Typography variant="caption" color="text.secondary">
                      📍 {selectedVehicleData.currentLocation.address || 'موقع غير محدد'}
                    </Typography>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        )}

        {/* Map Container */}
        <Card sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Map Controls */}
          <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              الخريطة التفاعلية
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="تحديث">
                <IconButton onClick={loadVehicles} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={isFullscreen ? "إغلاق ملء الشاشة" : "ملء الشاشة"}>
                <IconButton onClick={() => setIsFullscreen(!isFullscreen)}>
                  <FullscreenIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Map */}
          <Box sx={{ flexGrow: 1, position: 'relative' }}>
            <MockGoogleMap
              vehicles={filteredVehicles}
              selectedVehicle={selectedVehicle}
              onVehicleSelect={setSelectedVehicle}
            />
          </Box>
        </Card>
      </Box>

      {/* Real-time Status */}
      <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              bgcolor: 'success.main',
              borderRadius: '50%',
              animation: 'pulse 2s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 },
              },
            }}
          />
          <Typography variant="body2" color="text.secondary">
            التحديث المباشر نشط
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
        </Typography>
      </Box>
    </Box>
  );
};

export default LiveFleetMap;
