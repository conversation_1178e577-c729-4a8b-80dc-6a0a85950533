#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime

def main():
    print("🔄 اختبار تحويل البيانات اليمنية إلى SQL...")
    
    # تحقق من وجود ملف البيانات النموذجية
    sample_file = "sample_yemen_data.json"
    if not os.path.exists(sample_file):
        print(f"❌ ملف {sample_file} غير موجود")
        return
    
    print(f"✅ تم العثور على {sample_file}")
    
    # تحميل البيانات
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✅ تم تحميل البيانات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تحميل البيانات: {e}")
        return
    
    # عرض إحصائيات البيانات
    companies = data.get('companies', [])
    users = data.get('users', [])
    drivers = data.get('drivers', [])
    vehicles = data.get('vehicles', [])
    trips = data.get('trips', [])
    parcels = data.get('parcels', [])
    
    print(f"📊 إحصائيات البيانات:")
    print(f"   • الشركات: {len(companies)}")
    print(f"   • المستخدمين: {len(users)}")
    print(f"   • السائقين: {len(drivers)}")
    print(f"   • المركبات: {len(vehicles)}")
    print(f"   • الرحلات: {len(trips)}")
    print(f"   • الطرود: {len(parcels)}")
    
    # إنشاء SQL بسيط للاختبار
    sql_lines = []
    sql_lines.append("-- اختبار SQL للبيانات اليمنية")
    sql_lines.append(f"-- تم الإنشاء في: {datetime.now()}")
    sql_lines.append("")
    
    # إضافة بعض البيانات التجريبية
    if companies:
        sql_lines.append("-- الشركات")
        for company in companies[:2]:  # أول شركتين فقط للاختبار
            name = company['name'].replace("'", "''")
            sql_lines.append(f"-- شركة: {name}")
    
    if users:
        sql_lines.append("-- المستخدمين")
        for user in users[:2]:  # أول مستخدمين فقط للاختبار
            name = f"{user['first_name']} {user['last_name']}"
            sql_lines.append(f"-- مستخدم: {name}")
    
    # حفظ ملف الاختبار
    test_file = "test_output.sql"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_lines))
        print(f"✅ تم إنشاء ملف الاختبار: {test_file}")
    except Exception as e:
        print(f"❌ خطأ في حفظ الملف: {e}")
        return
    
    print("🎉 اختبار التحويل مكتمل بنجاح!")

if __name__ == "__main__":
    main()
