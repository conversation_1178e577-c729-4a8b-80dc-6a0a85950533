# 🚀 TECNO DRIVE Secure Kubernetes Cluster

## 📋 Overview

This Helm chart provides a comprehensive, secure, and resilient Kubernetes deployment for the TECNO DRIVE platform. It includes:

- **🛡️ Security**: Gatekeeper policies, Network Policies, Pod Security Standards
- **📊 Monitoring**: Prometheus, Grafana dashboards, comprehensive alerting
- **🔍 Observability**: Fluent Bit logging, SIEM integration
- **🔄 GitOps**: ArgoCD sync waves for ordered deployment
- **💪 Resilience**: PodDisruptionBudgets, Health Probes, Auto-scaling

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "Security Layer"
        GK[Gatekeeper] --> CT[ConstraintTemplates]
        GK --> C[Constraints]
        NP[NetworkPolicies] --> PS[PodSecurity]
    end
    
    subgraph "Application Layer"
        AG[API Gateway] --> BS[Business Services]
        BS --> CS[Core Services]
        AG --> CS
    end
    
    subgraph "Monitoring Layer"
        P[Prometheus] --> G[Grafana]
        FB[Fluent Bit] --> SIEM[SIEM/Elasticsearch]
        P --> AM[AlertManager]
    end
    
    subgraph "GitOps Layer"
        AC[ArgoCD] --> SW[Sync Waves]
        SW --> Security Layer
        SW --> Application Layer
        SW --> Monitoring Layer
    end
```

## 🚀 Quick Start

### Prerequisites

- Kubernetes cluster (v1.24+)
- Helm 3.8+
- kubectl configured
- ArgoCD (optional, for GitOps)

### 1. Install Dependencies

```bash
# Add Helm repositories
helm repo add gatekeeper https://open-policy-agent.github.io/gatekeeper/charts
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add grafana https://grafana.github.io/helm-charts
helm repo add fluent https://fluent.github.io/helm-charts
helm repo update
```

### 2. Create Namespace

```bash
kubectl create namespace tecno-drive-system
kubectl label namespace tecno-drive-system tecno-drive.com/managed=true
```

### 3. Install the Chart

```bash
# Basic installation
helm upgrade --install tecno-drive-secure ./secure-cluster \
  -n tecno-drive-system \
  --create-namespace

# Production installation with custom values
helm upgrade --install tecno-drive-secure ./secure-cluster \
  -n tecno-drive-system \
  --create-namespace \
  -f values-production.yaml \
  --set global.environment=production \
  --set siem.elasticsearch.endpoint=your-elasticsearch-endpoint
```

### 4. Verify Installation

```bash
# Check all resources
kubectl get all -n tecno-drive-system

# Check Gatekeeper status
kubectl get constrainttemplates
kubectl get constraints

# Check security violations
kubectl get events --field-selector reason=ConstraintViolation
```

## ⚙️ Configuration

### Core Configuration Files

| File | Purpose |
|------|---------|
| `values.yaml` | Main configuration |
| `values-production.yaml` | Production overrides |
| `values-development.yaml` | Development overrides |
| `values-staging.yaml` | Staging overrides |

### Key Configuration Sections

#### Security Settings

```yaml
security:
  policies:
    centralSecurity:
      enabled: true
      enforcement: warn  # warn, deny
      checks:
        privileged: true
        hostNetwork: true
        capabilities: true
        securityContext: true
        resources: true
        probes: true
        networkPolicies: true
        podDisruptionBudgets: true
```

#### Monitoring Settings

```yaml
monitoring:
  enabled: true
  prometheus:
    enabled: true
    retention: 15d
    storage: 50Gi
  grafana:
    enabled: true
    adminPassword: "secure-password"
    dashboards:
      security: true
      resilience: true
      performance: true
```

#### SIEM Integration

```yaml
siem:
  enabled: true
  provider: elasticsearch  # elasticsearch, splunk
  elasticsearch:
    endpoint: "elasticsearch.logging.svc.cluster.local:9200"
    index: "tecno-drive-security"
  alerting:
    slack:
      enabled: true
      webhook: "https://hooks.slack.com/..."
      channel: "#security-alerts"
```

## 🛡️ Security Features

### 1. Gatekeeper Policies

The chart includes a comprehensive `CentralSecurity` policy that checks:

- **Privileged containers**: Prevents privileged execution
- **Host access**: Blocks hostNetwork, hostPID, hostIPC
- **Security context**: Enforces non-root execution
- **Capabilities**: Requires dropping ALL capabilities
- **Resources**: Ensures resource limits and requests
- **Probes**: Validates health and readiness probes
- **Network policies**: Requires network isolation
- **PodDisruptionBudgets**: Ensures resilience configuration

### 2. Network Security

- **Default deny-all**: All traffic blocked by default
- **Selective allow**: Only necessary communication permitted
- **DNS access**: Controlled DNS resolution
- **Monitoring access**: Secure metrics collection

### 3. Pod Security Standards

- **Restricted profile**: Enforces strictest security standards
- **Non-root execution**: All containers run as non-root
- **Read-only filesystem**: Prevents runtime modifications
- **Capability dropping**: Removes unnecessary privileges

## 📊 Monitoring & Observability

### Grafana Dashboards

1. **Security Dashboard**: Violations, trends, compliance status
2. **Resilience Dashboard**: Service health, performance metrics
3. **Business Dashboard**: Application-specific metrics

### Prometheus Alerts

- **Critical Security Violations**: Immediate notification
- **High Security Violations**: Warning notifications
- **Resilience Issues**: PDB and probe violations
- **Performance Issues**: Resource utilization alerts

### SIEM Integration

- **Real-time log forwarding**: Security events to SIEM
- **Structured logging**: JSON format with metadata
- **Alert correlation**: Integration with existing SOC tools

## 🔄 GitOps with ArgoCD

### Sync Waves

| Wave | Resources | Purpose |
|------|-----------|---------|
| -1 | ConstraintTemplates, CRDs | Foundation |
| 0 | Constraints, NetworkPolicies | Security |
| 1 | PDBs, ServiceMonitors | Resilience |
| 2 | Services, ConfigMaps | Infrastructure |
| 3 | Deployments, StatefulSets | Applications |

### ArgoCD Application

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecno-drive-secure
spec:
  source:
    repoURL: https://github.com/tecnodrive/platform
    path: kubernetes/secure-cluster
    targetRevision: main
  destination:
    server: https://kubernetes.default.svc
    namespace: tecno-drive-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

## 🧪 Testing

### Security Testing

```bash
# Test privileged container (should be blocked)
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-privileged
  namespace: tecno-drive-system
spec:
  containers:
  - name: test
    image: nginx
    securityContext:
      privileged: true
EOF

# Test missing probes (should generate warning)
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-no-probes
  namespace: tecno-drive-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test
  template:
    metadata:
      labels:
        app: test
    spec:
      containers:
      - name: nginx
        image: nginx
EOF
```

### Resilience Testing

```bash
# Test PodDisruptionBudget
kubectl drain <node-name> --ignore-daemonsets --delete-emptydir-data

# Verify services remain available
kubectl get pods -n tecno-drive-system
curl http://api-gateway-service/health
```

### Monitoring Testing

```bash
# Generate test violations
kubectl apply -f test-violations/

# Check Grafana dashboards
open http://grafana.example.com/d/security-dashboard

# Check Prometheus alerts
open http://prometheus.example.com/alerts
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Gatekeeper Webhook Timeout

```bash
# Check webhook status
kubectl get validatingadmissionwebhook

# Check Gatekeeper logs
kubectl logs -n gatekeeper-system -l control-plane=controller-manager

# Increase timeout
helm upgrade tecno-drive-secure ./secure-cluster \
  --set gatekeeper.timeoutSeconds=10
```

#### 2. Policy Violations Blocking Deployments

```bash
# Check violations
kubectl get constraints -o yaml | grep -A 10 violations

# Temporarily set to warn mode
kubectl patch centralsecurity tecno-drive-security-policy \
  --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
```

#### 3. Network Policy Issues

```bash
# Check network policies
kubectl get networkpolicies -n tecno-drive-system

# Test connectivity
kubectl run test-pod --image=busybox --rm -it -- /bin/sh
# Inside pod: wget -qO- http://service-name:port
```

### Emergency Procedures

#### Disable All Security Policies

```bash
# EMERGENCY ONLY - Disable Gatekeeper
kubectl delete validatingadmissionwebhook gatekeeper-validating-webhook-configuration

# Set all constraints to warn
kubectl get constraints -o name | \
  xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
```

#### Re-enable Security

```bash
# Re-apply webhook
helm upgrade tecno-drive-secure ./secure-cluster

# Reset constraints to deny
kubectl get constraints -o name | \
  xargs -I {} kubectl patch {} --type='merge' -p='{"spec":{"enforcementAction":"deny"}}'
```

## 📚 Additional Resources

- [Gatekeeper Documentation](https://open-policy-agent.github.io/gatekeeper/)
- [Kubernetes Security Best Practices](https://kubernetes.io/docs/concepts/security/)
- [Pod Security Standards](https://kubernetes.io/docs/concepts/security/pod-security-standards/)
- [Network Policies](https://kubernetes.io/docs/concepts/services-networking/network-policies/)
- [TECNO DRIVE Security Guide](../docs/security-guide.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

---

**🚀 TECNO DRIVE Platform - Secure, Scalable, Reliable**
