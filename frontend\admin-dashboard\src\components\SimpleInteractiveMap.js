import React, { useState, useEffect, useRef } from 'react';
import './SimpleInteractiveMap.css';

const SimpleInteractiveMap = () => {
  const [vehicles, setVehicles] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [traffic, setTraffic] = useState([]);
  const [mapCenter, setMapCenter] = useState({ lat: 24.7136, lng: 46.6753 }); // Riyadh
  const [zoomLevel, setZoomLevel] = useState(12);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [showTraffic, setShowTraffic] = useState(true);
  const [showRoutes, setShowRoutes] = useState(true);
  
  const wsRef = useRef(null);
  const mapRef = useRef(null);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      const wsUrl = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8085';
      wsRef.current = new WebSocket(`${wsUrl}/ws`);

      wsRef.current.onopen = () => {
        console.log('Connected to map WebSocket');
        setIsConnected(true);
        
        // Subscribe to map updates
        wsRef.current.send(JSON.stringify({
          type: 'subscribe',
          subscriptions: [
            'map:all',
            'vehicle_tracking',
            'route_update',
            'traffic_update'
          ]
        }));
      };

      wsRef.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      wsRef.current.onclose = () => {
        console.log('Disconnected from map WebSocket');
        setIsConnected(false);
        // Reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'vehicle_tracking':
        updateVehiclePosition(message.data);
        break;
      case 'route_update':
        updateRoute(message.data);
        break;
      case 'traffic_update':
        updateTraffic(message.data);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  };

  // Update vehicle position
  const updateVehiclePosition = (vehicleData) => {
    setVehicles(prev => {
      const index = prev.findIndex(v => v.vehicleId === vehicleData.vehicleId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = { ...updated[index], ...vehicleData };
        return updated;
      } else {
        return [...prev, vehicleData];
      }
    });
  };

  // Update route
  const updateRoute = (routeData) => {
    setRoutes(prev => {
      const index = prev.findIndex(r => r.routeId === routeData.routeId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = routeData;
        return updated;
      } else {
        return [...prev, routeData];
      }
    });
  };

  // Update traffic
  const updateTraffic = (trafficData) => {
    setTraffic(prev => {
      const index = prev.findIndex(t => t.streetId === trafficData.streetId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = trafficData;
        return updated;
      } else {
        return [...prev, trafficData];
      }
    });
  };

  // Vehicle click handler
  const handleVehicleClick = (vehicle) => {
    setSelectedVehicle(vehicle);
  };

  // Simulate map interaction
  const handleMapClick = (event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Convert pixel coordinates to lat/lng (simplified)
    const lat = mapCenter.lat + (y - 300) / 10000;
    const lng = mapCenter.lng + (x - 400) / 10000;
    
    console.log('Map clicked at:', { lat, lng });
  };

  return (
    <div className="simple-interactive-map-container">
      {/* Map Controls */}
      <div className="map-controls">
        <div className="connection-status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢 متصل' : '🔴 غير متصل'}
          </span>
        </div>
        
        <div className="layer-controls">
          <label>
            <input
              type="checkbox"
              checked={showTraffic}
              onChange={(e) => setShowTraffic(e.target.checked)}
            />
            عرض حركة المرور
          </label>
          <label>
            <input
              type="checkbox"
              checked={showRoutes}
              onChange={(e) => setShowRoutes(e.target.checked)}
            />
            عرض الطرق
          </label>
        </div>

        <div className="map-stats">
          <div>المركبات النشطة: {vehicles.length}</div>
          <div>الطرق النشطة: {routes.length}</div>
          <div>مستوى التكبير: {zoomLevel}</div>
        </div>
      </div>

      {/* Simple Map Display */}
      <div 
        className="simple-map-display"
        onClick={handleMapClick}
        ref={mapRef}
      >
        <div className="map-background">
          <div className="map-grid"></div>
          
          {/* Center marker */}
          <div className="center-marker">
            <span>📍 الرياض</span>
          </div>

          {/* Vehicle Markers */}
          {vehicles.map(vehicle => (
            <div
              key={vehicle.vehicleId}
              className={`vehicle-marker ${vehicle.status}`}
              style={{
                left: `${400 + (vehicle.lng - mapCenter.lng) * 10000}px`,
                top: `${300 - (vehicle.lat - mapCenter.lat) * 10000}px`,
                transform: `rotate(${vehicle.heading || 0}deg)`
              }}
              onClick={() => handleVehicleClick(vehicle)}
              title={`مركبة ${vehicle.vehicleId} - ${vehicle.status}`}
            >
              🚗
            </div>
          ))}

          {/* Route Lines */}
          {showRoutes && routes.map(route => (
            route.waypoints && (
              <svg key={route.routeId} className="route-overlay">
                <polyline
                  points={route.waypoints.map(wp => 
                    `${400 + (wp.lng - mapCenter.lng) * 10000},${300 - (wp.lat - mapCenter.lat) * 10000}`
                  ).join(' ')}
                  stroke="blue"
                  strokeWidth="3"
                  fill="none"
                  opacity="0.7"
                />
              </svg>
            )
          ))}

          {/* Traffic Indicators */}
          {showTraffic && traffic.map(trafficItem => (
            <div
              key={trafficItem.streetId}
              className={`traffic-indicator ${trafficItem.trafficLevel?.toLowerCase()}`}
              style={{
                left: `${400 + (trafficItem.lng - mapCenter.lng) * 10000}px`,
                top: `${300 - (trafficItem.lat - mapCenter.lat) * 10000}px`
              }}
              title={`حركة المرور: ${trafficItem.trafficLevel}`}
            >
              🚦
            </div>
          ))}
        </div>
      </div>

      {/* Selected Vehicle Details */}
      {selectedVehicle && (
        <div className="vehicle-details-panel">
          <div className="panel-header">
            <h3>تفاصيل المركبة {selectedVehicle.vehicleId}</h3>
            <button onClick={() => setSelectedVehicle(null)}>×</button>
          </div>
          <div className="panel-content">
            <p><strong>الموقع:</strong> {selectedVehicle.lat?.toFixed(6)}, {selectedVehicle.lng?.toFixed(6)}</p>
            <p><strong>السرعة:</strong> {selectedVehicle.speed || 0} كم/س</p>
            <p><strong>الاتجاه:</strong> {selectedVehicle.heading || 0}°</p>
            <p><strong>الحالة:</strong> {selectedVehicle.status || 'غير محدد'}</p>
            <p><strong>آخر تحديث:</strong> {new Date(selectedVehicle.timestamp || Date.now()).toLocaleString('ar-SA')}</p>
          </div>
        </div>
      )}

      {/* Map Legend */}
      <div className="map-legend">
        <h4>دليل الخريطة</h4>
        <div className="legend-items">
          <div className="legend-item">
            <span className="legend-icon">🚗</span>
            <span>مركبة نشطة</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon">🚦</span>
            <span>حركة المرور</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon">📍</span>
            <span>موقع مركزي</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon" style={{color: 'blue'}}>━</span>
            <span>طريق نشط</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <button onClick={() => setMapCenter({ lat: 24.7136, lng: 46.6753 })}>
          📍 العودة للرياض
        </button>
        <button onClick={() => setZoomLevel(prev => Math.min(prev + 1, 18))}>
          🔍 تكبير
        </button>
        <button onClick={() => setZoomLevel(prev => Math.max(prev - 1, 1))}>
          🔍 تصغير
        </button>
        <button onClick={() => window.location.reload()}>
          🔄 تحديث
        </button>
      </div>
    </div>
  );
};

export default SimpleInteractiveMap;
