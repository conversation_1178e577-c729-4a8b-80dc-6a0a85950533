@echo off
echo ========================================
echo    مولد البيانات الافتراضية - TecnoDrive
echo ========================================
echo.

echo جاري التحقق من وجود Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت Python من https://python.org
    echo.
    pause
    exit /b 1
)

echo Python متاح! جاري التحقق من مكتبة Faker...
python -c "import faker" >nul 2>&1
if %errorlevel% neq 0 (
    echo مكتبة Faker غير مثبتة. جاري تثبيتها...
    python -m pip install faker
    if %errorlevel% neq 0 (
        echo فشل في تثبيت Faker. سيتم استخدام النسخة المبسطة...
        echo.
        echo جاري تشغيل مولد البيانات المبسط...
        python simple_yemen_data.py
        goto :end
    )
)

echo جاري تشغيل مولد البيانات الكامل...
python generate_yemen_data.py

:end
echo.
echo تم الانتهاء! تحقق من مجلد generated_data
pause
