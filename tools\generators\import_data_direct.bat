@echo off
chcp 65001 >nul
echo ===================================================================
echo TECNODRIVE Platform - استيراد البيانات اليمنية مباشرة
echo ===================================================================

echo.
echo 🚀 بدء عملية استيراد البيانات اليمنية إلى قاعدة البيانات...
echo.

:: التحقق من وجود ملف SQL
if not exist "yemen_data_import.sql" (
    echo ❌ خطأ: ملف yemen_data_import.sql غير موجود
    echo تأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف البيانات SQL

:: البحث عن PostgreSQL
echo.
echo 🔍 جاري البحث عن PostgreSQL...
set PSQL_PATH=""

if exist "C:\Program Files\PostgreSQL\16\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\16\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 16
) else if exist "C:\Program Files\PostgreSQL\15\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\15\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 15
) else if exist "C:\Program Files\PostgreSQL\14\bin\psql.exe" (
    set PSQL_PATH="C:\Program Files\PostgreSQL\14\bin\psql.exe"
    echo ✅ تم العثور على PostgreSQL 14
) else (
    echo ⚠️ لم يتم العثور على PostgreSQL في المسارات الافتراضية
    echo سيتم محاولة استخدام psql من PATH
    set PSQL_PATH=psql
)

echo.
echo 📋 معلومات الاتصال بقاعدة البيانات:
echo    المضيف: localhost
echo    المنفذ: 5432 (أو 5433)
echo    المستخدم: postgres
echo    قاعدة البيانات: tecnodrive_main
echo.

echo 📊 البيانات التي سيتم استيرادها:
echo    • 2 شركة نقل يمنية
echo    • 5 مستخدمين (ركاب وسائقين)
echo    • 3 مركبات مسجلة
echo    • 3 رحلات تجريبية
echo    • 2 طرد للتوصيل
echo.

:: السؤال عن المتابعة
set /p CONTINUE="هل تريد المتابعة مع استيراد البيانات؟ (y/n): "
if /i "%CONTINUE%" NEQ "y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

:: محاولة الاتصال بالمنفذ 5432 أولاً
echo.
echo 🚀 جاري استيراد البيانات إلى قاعدة البيانات (المنفذ 5432)...
echo سيتم طلب كلمة مرور PostgreSQL...
echo.

%PSQL_PATH% -h localhost -p 5432 -U postgres -d tecnodrive_main -f yemen_data_import.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم استيراد البيانات اليمنية بنجاح!
    goto SUCCESS
)

:: إذا فشل، جرب المنفذ 5433
echo.
echo ⚠️ فشل الاتصال بالمنفذ 5432، جاري المحاولة مع المنفذ 5433...
echo.

%PSQL_PATH% -h localhost -p 5433 -U postgres -d tecnodrive_main -f yemen_data_import.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم استيراد البيانات اليمنية بنجاح!
    goto SUCCESS
) else (
    goto FAILURE
)

:SUCCESS
echo.
echo 📊 البيانات المستوردة بنجاح:
echo    • الشركات والمؤسسات اليمنية
echo    • المستخدمين والسائقين من مختلف المحافظات
echo    • المركبات المسجلة
echo    • الرحلات التجريبية
echo    • الطرود والشحنات
echo.
echo 🎯 يمكنك الآن:
echo    1. تشغيل تطبيق TECNODRIVE
echo    2. اختبار الوظائف مع البيانات اليمنية
echo    3. عرض البيانات في لوحة التحكم
echo    4. إضافة المزيد من البيانات
echo.
echo 💡 للتحقق من البيانات المستوردة:
echo    psql -h localhost -p 5432 -U postgres -d tecnodrive_main
echo    SELECT COUNT(*) FROM users;
echo    SELECT COUNT(*) FROM vehicles;
echo    SELECT COUNT(*) FROM trips;
echo.
goto END

:FAILURE
echo.
echo ❌ فشل في استيراد البيانات
echo.
echo 🔧 تحقق من:
echo    1. تشغيل PostgreSQL على المنفذ 5432 أو 5433
echo    2. وجود قاعدة البيانات tecnodrive_main
echo    3. صحة كلمة مرور المستخدم postgres
echo    4. تطبيق schema قاعدة البيانات أولاً
echo.
echo 💡 لإنشاء قاعدة البيانات وتطبيق Schema:
echo    1. إنشاء قاعدة البيانات:
echo       psql -h localhost -p 5432 -U postgres -c "CREATE DATABASE tecnodrive_main;"
echo.
echo    2. تطبيق Schema:
echo       psql -h localhost -p 5432 -U postgres -d tecnodrive_main -f ../services/database/tecnodrive_schema.sql
echo.
echo    3. ثم إعادة تشغيل هذا الملف
echo.

:END
echo.
pause
