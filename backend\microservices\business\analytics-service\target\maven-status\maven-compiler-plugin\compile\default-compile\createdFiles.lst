com\tecnodrive\analyticsservice\AnalyticsServiceApplication.class
com\tecnodrive\analyticsservice\dto\ReportResponse$PaginationInfo.class
com\tecnodrive\analyticsservice\dto\ReportResponse$DataPoint$DataPointBuilder.class
com\tecnodrive\analyticsservice\dto\ReportResponse$PaginationInfo$PaginationInfoBuilder.class
com\tecnodrive\analyticsservice\enums\ReportType.class
com\tecnodrive\analyticsservice\dto\ReportResponse$ChartData$ChartDataBuilder.class
com\tecnodrive\analyticsservice\service\impl\ReportServiceImpl.class
com\tecnodrive\analyticsservice\dto\ReportResponse$ReportMetadata$ReportMetadataBuilder.class
com\tecnodrive\analyticsservice\service\impl\ReportServiceImpl$1.class
com\tecnodrive\analyticsservice\controller\ReportController$1.class
com\tecnodrive\analyticsservice\dto\ReportRequest$ReportType.class
com\tecnodrive\analyticsservice\dto\ReportResponse$UserAnalytics$UserAnalyticsBuilder.class
com\tecnodrive\analyticsservice\dto\ReportResponse$UserAnalytics.class
com\tecnodrive\analyticsservice\dto\ReportResponse$OperationalMetrics.class
com\tecnodrive\analyticsservice\dto\ReportRequest$Period.class
com\tecnodrive\analyticsservice\dto\ReportRequest$ReportRequestBuilder.class
com\tecnodrive\analyticsservice\dto\ReportRequest.class
com\tecnodrive\analyticsservice\dto\ReportResponse$OperationalMetrics$OperationalMetricsBuilder.class
com\tecnodrive\analyticsservice\dto\ReportResponse.class
com\tecnodrive\analyticsservice\dto\ReportRequest$Format.class
com\tecnodrive\analyticsservice\dto\ReportResponse$DataPoint.class
com\tecnodrive\analyticsservice\controller\ReportController.class
com\tecnodrive\analyticsservice\service\ReportService.class
com\tecnodrive\analyticsservice\dto\ReportResponse$ChartData.class
com\tecnodrive\analyticsservice\dto\ReportResponse$ReportResponseBuilder.class
com\tecnodrive\analyticsservice\exception\GlobalExceptionHandler.class
com\tecnodrive\analyticsservice\dto\ReportResponse$FinancialSummary.class
com\tecnodrive\analyticsservice\dto\ReportResponse$ReportMetadata.class
com\tecnodrive\analyticsservice\dto\ReportResponse$FinancialSummary$FinancialSummaryBuilder.class
