import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  LocationOn,
  Star,
  DirectionsCar,
} from '@mui/icons-material';

interface Driver {
  id: string;
  name: string;
  rating: number;
  location: string;
  status: 'AVAILABLE' | 'BUSY' | 'OFFLINE';
  ridesCompleted: number;
}

const mockDrivers: Driver[] = [
  {
    id: '1',
    name: 'علي أحمد',
    rating: 4.8,
    location: 'شارع الزبيري',
    status: 'AVAILABLE',
    ridesCompleted: 45,
  },
  {
    id: '2',
    name: 'محمد سالم',
    rating: 4.9,
    location: 'السبعين',
    status: 'BUSY',
    ridesCompleted: 38,
  },
  {
    id: '3',
    name: 'سعد محمد',
    rating: 4.7,
    location: 'الحصبة',
    status: 'AVAILABLE',
    ridesCompleted: 52,
  },
  {
    id: '4',
    name: 'عبدالله علي',
    rating: 4.6,
    location: 'شارع الستين',
    status: 'AVAILABLE',
    ridesCompleted: 41,
  },
  {
    id: '5',
    name: 'يحيى محمد',
    rating: 4.9,
    location: 'التحرير',
    status: 'OFFLINE',
    ridesCompleted: 67,
  },
];

const getStatusColor = (status: string): "success" | "warning" | "error" => {
  switch (status) {
    case 'AVAILABLE':
      return 'success';
    case 'BUSY':
      return 'warning';
    case 'OFFLINE':
      return 'error';
    default:
      return 'success';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'AVAILABLE':
      return 'متاح';
    case 'BUSY':
      return 'مشغول';
    case 'OFFLINE':
      return 'غير متصل';
    default:
      return status;
  }
};

const ActiveDriversMap: React.FC = () => {
  const availableDrivers = mockDrivers.filter(d => d.status === 'AVAILABLE').length;
  const busyDrivers = mockDrivers.filter(d => d.status === 'BUSY').length;
  const offlineDrivers = mockDrivers.filter(d => d.status === 'OFFLINE').length;

  return (
    <Box sx={{ height: 320 }}>
      {/* Summary Stats */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            متاح: {availableDrivers}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            مشغول: {busyDrivers}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            غير متصل: {offlineDrivers}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={(availableDrivers / mockDrivers.length) * 100}
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>

      {/* Drivers List */}
      <Box sx={{ height: 240, overflow: 'auto' }}>
        <List sx={{ width: '100%', p: 0 }}>
          {mockDrivers.map((driver) => (
            <ListItem key={driver.id} sx={{ px: 0, py: 1 }}>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <DirectionsCar />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {driver.name}
                    </Typography>
                    <Chip
                      label={getStatusLabel(driver.status)}
                      size="small"
                      color={getStatusColor(driver.status)}
                      variant="outlined"
                    />
                  </Box>
                }
                secondary={
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <LocationOn sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {driver.location}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Star sx={{ fontSize: 14, color: 'warning.main' }} />
                        <Typography variant="body2" color="text.secondary">
                          {driver.rating}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {driver.ridesCompleted} رحلة
                      </Typography>
                    </Box>
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
};

export default ActiveDriversMap;
