import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Divider,
  Alert,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Work as HRIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Security as SecurityIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Business as DepartmentIcon,
  AdminPanelSettings as AdminIcon,
  Engineering as TechIcon,
  AccountBalance as FinanceIcon,
  Support as SupportIcon,
  PhoneAndroid as MobileIcon,
  Web as WebIcon,
  Storage as BackendIcon,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`hr-tabpanel-${index}`}
      aria-labelledby={`hr-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  role: string;
  permissions: string[];
  manager?: string;
  hireDate: string;
  salary: number;
  status: 'active' | 'inactive' | 'suspended';
}

interface Department {
  id: string;
  name: string;
  description: string;
  manager: string;
  employeeCount: number;
  budget: number;
}

const HRManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [employeeDialogOpen, setEmployeeDialogOpen] = useState(false);
  const [departmentDialogOpen, setDepartmentDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock data for departments
  const departments: Department[] = [
    {
      id: '1',
      name: 'الإدارة العامة',
      description: 'الإدارة العليا والتخطيط الاستراتيجي',
      manager: 'أحمد محمد',
      employeeCount: 5,
      budget: 500000,
    },
    {
      id: '2',
      name: 'الإدارة المالية',
      description: 'إدارة الشؤون المالية والمحاسبة',
      manager: 'فاطمة علي',
      employeeCount: 8,
      budget: 300000,
    },
    {
      id: '3',
      name: 'الإدارة التقنية',
      description: 'تطوير وصيانة الأنظمة التقنية',
      manager: 'محمد سالم',
      employeeCount: 15,
      budget: 800000,
    },
    {
      id: '4',
      name: 'خدمة العملاء',
      description: 'دعم العملاء وحل المشاكل',
      manager: 'نورا أحمد',
      employeeCount: 12,
      budget: 400000,
    },
  ];

  // Mock data for employees
  const employees: Employee[] = [
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '+966501234567',
      department: 'الإدارة العامة',
      position: 'المدير العام',
      role: 'general_manager',
      permissions: ['all'],
      hireDate: '2023-01-15',
      salary: 25000,
      status: 'active',
    },
    {
      id: '2',
      name: 'فاطمة علي',
      email: '<EMAIL>',
      phone: '+966501234568',
      department: 'الإدارة المالية',
      position: 'مدير الشؤون المالية',
      role: 'finance_manager',
      permissions: ['finance_read', 'finance_write', 'reports_read'],
      manager: 'أحمد محمد',
      hireDate: '2023-02-01',
      salary: 18000,
      status: 'active',
    },
    {
      id: '3',
      name: 'محمد سالم',
      email: '<EMAIL>',
      phone: '+966501234569',
      department: 'الإدارة التقنية',
      position: 'مدير التقنية',
      role: 'tech_manager',
      permissions: ['tech_read', 'tech_write', 'system_admin'],
      manager: 'أحمد محمد',
      hireDate: '2023-01-20',
      salary: 20000,
      status: 'active',
    },
  ];

  // Technical department sub-departments
  const techDepartments = [
    { name: 'فريق التطوير', icon: <TechIcon />, count: 5 },
    { name: 'دعم العملاء', icon: <SupportIcon />, count: 4 },
    { name: 'موظفي الخلفية', icon: <BackendIcon />, count: 3 },
    { name: 'تطوير تطبيقات الهاتف', icon: <MobileIcon />, count: 2 },
    { name: 'تطوير الواجهة', icon: <WebIcon />, count: 3 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'suspended':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'suspended':
        return 'موقوف';
      default:
        return status;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        إدارة الموارد البشرية
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="نظرة عامة" />
        <Tab label="الموظفين" />
        <Tab label="الأقسام" />
        <Tab label="الصلاحيات" />
        <Tab label="الإدارة التقنية" />
      </Tabs>

      {/* Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {employees.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الموظفين
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <DepartmentIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {departments.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الأقسام
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <AdminIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      5
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      المدراء
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <TechIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      15
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الفريق التقني
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Departments Overview */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              الأقسام الرئيسية
            </Typography>
            <Grid container spacing={2}>
              {departments.map((dept) => (
                <Grid item xs={12} md={6} key={dept.id}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="h6">{dept.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            المدير: {dept.manager}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            عدد الموظفين: {dept.employeeCount}
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="h6" color="primary">
                            {dept.budget.toLocaleString()} ر.س
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            الميزانية
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Employees Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">إدارة الموظفين</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setEmployeeDialogOpen(true)}
          >
            إضافة موظف جديد
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>الاسم</TableCell>
                <TableCell>القسم</TableCell>
                <TableCell>المنصب</TableCell>
                <TableCell>المدير المباشر</TableCell>
                <TableCell>تاريخ التوظيف</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {employees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        {employee.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {employee.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {employee.email}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{employee.department}</TableCell>
                  <TableCell>{employee.position}</TableCell>
                  <TableCell>{employee.manager || 'لا يوجد'}</TableCell>
                  <TableCell>{employee.hireDate}</TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(employee.status)}
                      color={getStatusColor(employee.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" onClick={() => setSelectedEmployee(employee)}>
                      <ViewIcon />
                    </IconButton>
                    <IconButton size="small">
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Departments Tab */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">إدارة الأقسام</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setDepartmentDialogOpen(true)}
          >
            إضافة قسم جديد
          </Button>
        </Box>

        <Grid container spacing={3}>
          {departments.map((dept) => (
            <Grid item xs={12} md={6} lg={4} key={dept.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6">{dept.name}</Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {dept.description}
                      </Typography>
                    </Box>
                    <IconButton size="small">
                      <EditIcon />
                    </IconButton>
                  </Box>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">المدير:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {dept.manager}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">عدد الموظفين:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {dept.employeeCount}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">الميزانية:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      {dept.budget.toLocaleString()} ر.س
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Permissions Tab */}
      <TabPanel value={tabValue} index={3}>
        <Typography variant="h6" sx={{ mb: 3 }}>
          إدارة الصلاحيات والأدوار
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          يمكن للمدير العام إنشاء حسابات للمدراء مع تحديد صلاحياتهم، ويمكن للمدراء إنشاء حسابات لموظفيهم.
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  الأدوار الأساسية
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'error.main' }}>
                        <AdminIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="المدير العام"
                      secondary="صلاحيات كاملة - يمكنه إنشاء حسابات المدراء"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'warning.main' }}>
                        <FinanceIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="مدير الشؤون المالية"
                      secondary="صلاحيات مالية - يمكنه إنشاء حسابات المحاسبين"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'info.main' }}>
                        <TechIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="مدير التقنية"
                      secondary="صلاحيات تقنية - يمكنه إنشاء حسابات الفريق التقني"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  الصلاحيات المتاحة
                </Typography>
                <List dense>
                  <ListItem>
                    <Checkbox defaultChecked />
                    <ListItemText primary="قراءة البيانات المالية" />
                  </ListItem>
                  <ListItem>
                    <Checkbox />
                    <ListItemText primary="تعديل البيانات المالية" />
                  </ListItem>
                  <ListItem>
                    <Checkbox defaultChecked />
                    <ListItemText primary="عرض التقارير" />
                  </ListItem>
                  <ListItem>
                    <Checkbox />
                    <ListItemText primary="إدارة النظام" />
                  </ListItem>
                  <ListItem>
                    <Checkbox defaultChecked />
                    <ListItemText primary="إدارة المستخدمين" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Technical Department Tab */}
      <TabPanel value={tabValue} index={4}>
        <Typography variant="h6" sx={{ mb: 3 }}>
          الإدارة التقنية - الأقسام الفرعية
        </Typography>
        
        <Grid container spacing={3}>
          {techDepartments.map((techDept, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {techDept.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{techDept.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {techDept.count} موظف
                      </Typography>
                    </Box>
                  </Box>
                  <Button variant="outlined" size="small" fullWidth>
                    عرض التفاصيل
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Employee Dialog */}
      <Dialog open={employeeDialogOpen} onClose={() => setEmployeeDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة موظف جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField fullWidth label="الاسم الكامل" variant="outlined" />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField fullWidth label="البريد الإلكتروني" type="email" variant="outlined" />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField fullWidth label="رقم الهاتف" variant="outlined" />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>القسم</InputLabel>
                <Select label="القسم">
                  {departments.map((dept) => (
                    <MenuItem key={dept.id} value={dept.name}>
                      {dept.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField fullWidth label="المنصب" variant="outlined" />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField fullWidth label="الراتب" type="number" variant="outlined" />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                الصلاحيات:
              </Typography>
              <FormControlLabel control={<Checkbox />} label="قراءة البيانات" />
              <FormControlLabel control={<Checkbox />} label="تعديل البيانات" />
              <FormControlLabel control={<Checkbox />} label="إدارة المستخدمين" />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmployeeDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained" onClick={() => setEmployeeDialogOpen(false)}>
            إضافة الموظف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HRManagement;
