import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface Payment {
  id: string;
  rideId: string;
  amount: number;
  currency: string;
  method: 'CASH' | 'CARD' | 'WALLET' | 'BANK_TRANSFER';
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
}

interface PaymentsState {
  payments: Payment[];
  currentPayment: Payment | null;
  loading: boolean;
  error: string | null;
  totalRevenue: number;
  todayRevenue: number;
  pendingPayments: number;
}

const initialState: PaymentsState = {
  payments: [],
  currentPayment: null,
  loading: false,
  error: null,
  totalRevenue: 0,
  todayRevenue: 0,
  pendingPayments: 0,
};

export const fetchPayments = createAsyncThunk(
  'payments/fetchPayments',
  async (params?: { page?: number; limit?: number; status?: string }) => {
    const { paymentsService } = await import('../../services/paymentsService');
    const response = await paymentsService.getPayments(params);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب المدفوعات');
    }

    return response;
  }
);

const paymentsSlice = createSlice({
  name: 'payments',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPayments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.payments = action.payload.data;
        state.totalRevenue = action.payload.data.reduce((sum: number, p: Payment) => 
          p.status === 'COMPLETED' ? sum + p.amount : sum, 0);
        state.pendingPayments = action.payload.data.filter((p: Payment) => p.status === 'PENDING').length;
      })
      .addCase(fetchPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب المدفوعات';
      });
  },
});

export const { clearError } = paymentsSlice.actions;
export default paymentsSlice.reducer;
