package com.tecnodrive.locationservice.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Map Service for basic map operations
 */
@Service
public class MapService {

    private static final Logger log = LoggerFactory.getLogger(MapService.class);

    /**
     * Get basic map configuration
     */
    public Map<String, Object> getMapConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("defaultCenter", Map.of("lat", 24.7136, "lng", 46.6753));
        config.put("defaultZoom", 12);
        config.put("maxZoom", 19);
        config.put("minZoom", 3);
        return config;
    }

    /**
     * Update vehicle position
     */
    public Map<String, Object> updateVehiclePosition(String vehicleId, double lat, double lng, Map<String, Object> additionalData) {
        log.info("Updating vehicle position: {} at {}, {}", vehicleId, lat, lng);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("vehicleId", vehicleId);
        result.put("lat", lat);
        result.put("lng", lng);
        result.put("timestamp", System.currentTimeMillis());
        
        if (additionalData != null) {
            result.putAll(additionalData);
        }
        
        return result;
    }

    /**
     * Get vehicle positions
     */
    public List<Map<String, Object>> getVehiclePositions() {
        List<Map<String, Object>> vehicles = new ArrayList<>();
        
        // Sample data for testing
        vehicles.add(Map.of(
            "id", "vehicle_001",
            "lat", 24.7136,
            "lng", 46.6753,
            "status", "active",
            "speed", 45.5
        ));
        
        vehicles.add(Map.of(
            "id", "vehicle_002", 
            "lat", 24.7200,
            "lng", 46.6800,
            "status", "busy",
            "speed", 30.0
        ));
        
        return vehicles;
    }

    /**
     * Get map bounds for a given area
     */
    public Map<String, Object> getMapBounds(double centerLat, double centerLng, double radiusKm) {
        // Calculate approximate bounds based on radius
        double latDelta = radiusKm / 111.0; // Rough conversion: 1 degree lat ≈ 111 km
        double lngDelta = radiusKm / (111.0 * Math.cos(Math.toRadians(centerLat)));
        
        Map<String, Object> bounds = new HashMap<>();
        bounds.put("north", centerLat + latDelta);
        bounds.put("south", centerLat - latDelta);
        bounds.put("east", centerLng + lngDelta);
        bounds.put("west", centerLng - lngDelta);
        
        return bounds;
    }

    /**
     * Get nearby vehicles
     */
    public List<Map<String, Object>> getNearbyVehicles(double lat, double lng, double radiusKm) {
        List<Map<String, Object>> allVehicles = getVehiclePositions();
        List<Map<String, Object>> nearbyVehicles = new ArrayList<>();
        
        for (Map<String, Object> vehicle : allVehicles) {
            double vehicleLat = (Double) vehicle.get("lat");
            double vehicleLng = (Double) vehicle.get("lng");
            
            double distance = calculateDistance(lat, lng, vehicleLat, vehicleLng);
            if (distance <= radiusKm) {
                vehicle.put("distance", distance);
                nearbyVehicles.add(vehicle);
            }
        }
        
        return nearbyVehicles;
    }

    /**
     * Calculate distance between two points
     */
    public double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final int R = 6371; // Radius of the earth in km
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lngDistance = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lngDistance / 2) * Math.sin(lngDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c; // Distance in km
    }

    /**
     * Get map statistics
     */
    public Map<String, Object> getMapStats() {
        Map<String, Object> stats = new HashMap<>();
        
        List<Map<String, Object>> vehicles = getVehiclePositions();
        long activeVehicles = vehicles.stream()
                .filter(v -> "active".equals(v.get("status")))
                .count();
        
        stats.put("totalVehicles", vehicles.size());
        stats.put("activeVehicles", activeVehicles);
        stats.put("busyVehicles", vehicles.size() - activeVehicles);
        stats.put("lastUpdated", System.currentTimeMillis());
        
        return stats;
    }

    /**
     * Validate coordinates
     */
    public boolean isValidCoordinate(double lat, double lng) {
        return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
    }

    /**
     * Format coordinates for display
     */
    public String formatCoordinates(double lat, double lng) {
        return String.format("%.6f, %.6f", lat, lng);
    }

    /**
     * Get map center for Saudi Arabia
     */
    public Map<String, Object> getSaudiArabiaCenter() {
        return Map.of(
            "lat", 24.7136,
            "lng", 46.6753,
            "zoom", 6,
            "name", "المملكة العربية السعودية"
        );
    }

    /**
     * Get major cities in Saudi Arabia
     */
    public List<Map<String, Object>> getMajorCities() {
        List<Map<String, Object>> cities = new ArrayList<>();
        
        cities.add(Map.of(
            "name", "الرياض",
            "lat", 24.7136,
            "lng", 46.6753,
            "population", 7000000
        ));
        
        cities.add(Map.of(
            "name", "جدة",
            "lat", 21.4858,
            "lng", 39.1925,
            "population", 4000000
        ));
        
        cities.add(Map.of(
            "name", "مكة المكرمة",
            "lat", 21.3891,
            "lng", 39.8579,
            "population", 2000000
        ));
        
        cities.add(Map.of(
            "name", "المدينة المنورة",
            "lat", 24.5247,
            "lng", 39.5692,
            "population", 1500000
        ));
        
        cities.add(Map.of(
            "name", "الدمام",
            "lat", 26.4207,
            "lng", 50.0888,
            "population", 1000000
        ));
        
        return cities;
    }

    /**
     * Check if a point is within bounds
     */
    public boolean isWithinBounds(double lat, double lng, Map<String, Object> bounds) {
        double north = (Double) bounds.get("north");
        double south = (Double) bounds.get("south");
        double east = (Double) bounds.get("east");
        double west = (Double) bounds.get("west");
        
        return lat >= south && lat <= north && lng >= west && lng <= east;
    }

    /**
     * Get nearby locations (placeholder)
     */
    public List<Map<String, Object>> getNearbyLocations(double lat, double lng, double radiusKm) {
        List<Map<String, Object>> locations = new ArrayList<>();
        
        // This is a placeholder - in a real implementation, you would query a database
        // For now, return some sample locations around Riyadh
        if (calculateDistance(lat, lng, 24.7136, 46.6753) <= radiusKm) {
            locations.add(Map.of(
                "id", "riyadh_center",
                "name", "مركز الرياض",
                "lat", 24.7136,
                "lng", 46.6753,
                "type", "city_center"
            ));
        }
        
        return locations;
    }

    // Methods required by MapController

    /**
     * Update vehicle position (overloaded method for MapController)
     */
    public void updateVehiclePosition(String vehicleId, double lat, double lng, double heading, double speed, String status) {
        log.info("Updating vehicle position: {} at {}, {} with heading: {}, speed: {}, status: {}",
                vehicleId, lat, lng, heading, speed, status);

        Map<String, Object> additionalData = new HashMap<>();
        additionalData.put("heading", heading);
        additionalData.put("speed", speed);
        additionalData.put("status", status);

        updateVehiclePosition(vehicleId, lat, lng, additionalData);
    }

    /**
     * Update route
     */
    public void updateRoute(String routeId, String vehicleId, List<Map<String, Object>> waypoints, Map<String, Object> routeInfo) {
        log.info("Updating route: {} for vehicle: {} with {} waypoints", routeId, vehicleId, waypoints.size());
        // Implementation for route update
    }

    /**
     * Update traffic conditions
     */
    public void updateTrafficConditions(String streetId, String trafficLevel, double avgSpeed, String description) {
        log.info("Updating traffic conditions for street: {} - level: {}, speed: {}", streetId, trafficLevel, avgSpeed);
        // Implementation for traffic update
    }

    /**
     * Update geofence
     */
    public void updateGeofence(String geofenceId, String name, String type, List<Map<String, Double>> coordinates, Map<String, Object> rules) {
        log.info("Updating geofence: {} - name: {}, type: {}", geofenceId, name, type);
        // Implementation for geofence update
    }

    /**
     * Update location clusters
     */
    public void updateLocationClusters(double centerLat, double centerLng, double radiusKm, int zoomLevel) {
        log.info("Updating location clusters at {}, {} with radius: {} km, zoom: {}", centerLat, centerLng, radiusKm, zoomLevel);
        // Implementation for cluster update
    }

    /**
     * Update map viewport
     */
    public void updateMapViewport(String sessionId, double centerLat, double centerLng, int zoomLevel, Map<String, Object> bounds) {
        log.info("Updating map viewport for session: {} at {}, {} zoom: {}", sessionId, centerLat, centerLng, zoomLevel);
        // Implementation for viewport update
    }

    /**
     * Get map data for area
     */
    public Map<String, Object> getMapDataForArea(double centerLat, double centerLng, double radiusKm) {
        log.info("Getting map data for area at {}, {} with radius: {} km", centerLat, centerLng, radiusKm);

        Map<String, Object> mapData = new HashMap<>();
        mapData.put("vehicles", getNearbyVehicles(centerLat, centerLng, radiusKm));
        mapData.put("locations", getNearbyLocations(centerLat, centerLng, radiusKm));
        mapData.put("bounds", getMapBounds(centerLat, centerLng, radiusKm));
        mapData.put("center", Map.of("lat", centerLat, "lng", centerLng));
        mapData.put("radius", radiusKm);

        return mapData;
    }
}
