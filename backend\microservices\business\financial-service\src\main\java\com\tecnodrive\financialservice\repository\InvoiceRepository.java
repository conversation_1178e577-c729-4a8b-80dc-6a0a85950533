package com.tecnodrive.financialservice.repository;

import com.tecnodrive.financialservice.entity.Invoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Invoice Repository
 * 
 * Data access layer for Invoice entities
 */
@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, UUID> {

    /**
     * Find invoice by invoice number
     */
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);

    /**
     * Find invoices by company
     */
    List<Invoice> findByCompanyId(String companyId);

    /**
     * Find invoices by company with pagination
     */
    Page<Invoice> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find invoices by billed entity
     */
    List<Invoice> findByBilledEntityTypeAndBilledEntityId(String billedEntityType, String billedEntityId);

    /**
     * Find invoices by status
     */
    List<Invoice> findByStatus(Invoice.InvoiceStatus status);

    /**
     * Find invoices by company and status
     */
    List<Invoice> findByCompanyIdAndStatus(String companyId, Invoice.InvoiceStatus status);

    /**
     * Find invoices by type
     */
    List<Invoice> findByInvoiceType(Invoice.InvoiceType invoiceType);

    /**
     * Find overdue invoices
     */
    @Query("SELECT i FROM Invoice i WHERE i.dueDate < :currentDate AND i.outstandingAmount > 0")
    List<Invoice> findOverdueInvoices(@Param("currentDate") LocalDate currentDate);

    /**
     * Find overdue invoices by company
     */
    @Query("SELECT i FROM Invoice i WHERE i.companyId = :companyId AND i.dueDate < :currentDate AND i.outstandingAmount > 0")
    List<Invoice> findOverdueInvoicesByCompany(@Param("companyId") String companyId, @Param("currentDate") LocalDate currentDate);

    /**
     * Find invoices due soon
     */
    @Query("SELECT i FROM Invoice i WHERE i.dueDate BETWEEN :startDate AND :endDate AND i.outstandingAmount > 0")
    List<Invoice> findInvoicesDueSoon(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find unpaid invoices
     */
    @Query("SELECT i FROM Invoice i WHERE i.outstandingAmount > 0")
    List<Invoice> findUnpaidInvoices();

    /**
     * Find unpaid invoices by company
     */
    @Query("SELECT i FROM Invoice i WHERE i.companyId = :companyId AND i.outstandingAmount > 0")
    List<Invoice> findUnpaidInvoicesByCompany(@Param("companyId") String companyId);

    /**
     * Find invoices by date range
     */
    @Query("SELECT i FROM Invoice i WHERE i.issueDate BETWEEN :startDate AND :endDate")
    List<Invoice> findInvoicesByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find invoices by company and date range
     */
    @Query("SELECT i FROM Invoice i WHERE i.companyId = :companyId AND i.issueDate BETWEEN :startDate AND :endDate")
    List<Invoice> findByCompanyIdAndDateRange(
            @Param("companyId") String companyId,
            @Param("startDate") LocalDate startDate, 
            @Param("endDate") LocalDate endDate
    );

    /**
     * Calculate total outstanding amount by company
     */
    @Query("SELECT COALESCE(SUM(i.outstandingAmount), 0) FROM Invoice i WHERE i.companyId = :companyId")
    BigDecimal calculateTotalOutstandingAmount(@Param("companyId") String companyId);

    /**
     * Calculate total invoice amount by company
     */
    @Query("SELECT COALESCE(SUM(i.totalAmount), 0) FROM Invoice i WHERE i.companyId = :companyId")
    BigDecimal calculateTotalInvoiceAmount(@Param("companyId") String companyId);

    /**
     * Calculate total paid amount by company
     */
    @Query("SELECT COALESCE(SUM(i.amountPaid), 0) FROM Invoice i WHERE i.companyId = :companyId")
    BigDecimal calculateTotalPaidAmount(@Param("companyId") String companyId);

    /**
     * Calculate revenue for period
     */
    @Query("SELECT COALESCE(SUM(i.totalAmount), 0) FROM Invoice i WHERE i.companyId = :companyId AND i.issueDate BETWEEN :startDate AND :endDate AND i.status NOT IN ('CANCELLED', 'REFUNDED')")
    BigDecimal calculateRevenueForPeriod(
            @Param("companyId") String companyId,
            @Param("startDate") LocalDate startDate, 
            @Param("endDate") LocalDate endDate
    );

    /**
     * Get invoice statistics by status
     */
    @Query("SELECT i.status, COUNT(i), COALESCE(SUM(i.totalAmount), 0) FROM Invoice i WHERE i.companyId = :companyId GROUP BY i.status")
    List<Object[]> getInvoiceStatisticsByStatus(@Param("companyId") String companyId);

    /**
     * Get monthly invoice summary
     */
    @Query("SELECT YEAR(i.issueDate), MONTH(i.issueDate), COUNT(i), COALESCE(SUM(i.totalAmount), 0), COALESCE(SUM(i.amountPaid), 0) FROM Invoice i WHERE i.companyId = :companyId GROUP BY YEAR(i.issueDate), MONTH(i.issueDate) ORDER BY YEAR(i.issueDate), MONTH(i.issueDate)")
    List<Object[]> getMonthlyInvoiceSummary(@Param("companyId") String companyId);

    /**
     * Find invoices needing reminders
     */
    @Query("SELECT i FROM Invoice i WHERE i.dueDate < :currentDate AND i.outstandingAmount > 0 AND (i.lastReminderSent IS NULL OR i.lastReminderSent < :reminderCutoff)")
    List<Invoice> findInvoicesNeedingReminders(@Param("currentDate") LocalDate currentDate, @Param("reminderCutoff") java.time.Instant reminderCutoff);

    /**
     * Check if invoice number exists
     */
    boolean existsByInvoiceNumber(String invoiceNumber);

    /**
     * Count invoices by status
     */
    long countByStatus(Invoice.InvoiceStatus status);

    /**
     * Count invoices by company and status
     */
    long countByCompanyIdAndStatus(String companyId, Invoice.InvoiceStatus status);

    /**
     * Find next invoice number sequence
     */
    @Query("SELECT MAX(CAST(SUBSTRING(i.invoiceNumber, 4) AS int)) FROM Invoice i WHERE i.invoiceNumber LIKE :prefix%")
    Integer findMaxInvoiceSequence(@Param("prefix") String prefix);
}
