import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';

interface MapContainerWrapperProps {
  children: React.ReactNode;
  height?: string;
  onMapReady?: () => void;
  onMapError?: (error: string) => void;
}

const MapContainerWrapper: React.FC<MapContainerWrapperProps> = ({
  children,
  height = '600px',
  onMapReady,
  onMapError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  const initializeContainer = useCallback(() => {
    if (!containerRef.current) return;

    try {
      // Clear any existing content
      containerRef.current.innerHTML = '';
      
      // Add a unique identifier to prevent conflicts
      const uniqueId = `map-container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      containerRef.current.id = uniqueId;
      
      console.log(`🗺️ Initializing map container: ${uniqueId}`);
      
      setIsReady(true);
      setError(null);
      onMapReady?.();
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Map container initialization failed:', errorMessage);
      
      setError(errorMessage);
      onMapError?.(errorMessage);
      
      // Retry logic
      if (retryCount < maxRetries) {
        console.log(`🔄 Retrying map initialization (${retryCount + 1}/${maxRetries})...`);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          initializeContainer();
        }, 1000);
      }
    }
  }, [onMapReady, onMapError, retryCount, maxRetries]);

  useEffect(() => {
    const timer = setTimeout(initializeContainer, 100);
    
    return () => {
      clearTimeout(timer);
      
      // Cleanup
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, [initializeContainer]);

  if (error && retryCount >= maxRetries) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: 3
        }}
      >
        <Alert severity="error" sx={{ mb: 2 }}>
          فشل في تحميل الخريطة: {error}
        </Alert>
        <Typography variant="body2" color="text.secondary">
          يرجى تحديث الصفحة والمحاولة مرة أخرى
        </Typography>
      </Box>
    );
  }

  if (!isReady) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: 3
        }}
      >
        <CircularProgress sx={{ mb: 2 }} />
        <Typography variant="body1">
          جاري تحضير الخريطة...
        </Typography>
        {retryCount > 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            محاولة {retryCount + 1} من {maxRetries + 1}
          </Typography>
        )}
      </Box>
    );
  }

  return (
    <div
      ref={containerRef}
      style={{
        height,
        width: '100%',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {children}
    </div>
  );
};

export default MapContainerWrapper;
