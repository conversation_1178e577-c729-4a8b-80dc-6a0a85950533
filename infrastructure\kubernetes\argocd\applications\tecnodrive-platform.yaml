apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecnodrive-platform
  namespace: argocd
  labels:
    app.kubernetes.io/name: tecnodrive-platform
    app.kubernetes.io/part-of: tecnodrive
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: default
  
  source:
    repoURL: https://github.com/tecnodrive/platform
    targetRevision: main
    path: k8s/helm/tecnodrive-platform
    helm:
      valueFiles:
        - values.yaml
        - values-production.yaml
      parameters:
        - name: global.environment
          value: production
        - name: global.imageTag
          value: latest
  
  destination:
    server: https://kubernetes.default.svc
    namespace: tecnodrive
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  
  revisionHistoryLimit: 10
  
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas
    - group: ""
      kind: Secret
      jsonPointers:
        - /data

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecnodrive-infrastructure
  namespace: argocd
  labels:
    app.kubernetes.io/name: tecnodrive-infrastructure
    app.kubernetes.io/part-of: tecnodrive
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  
  source:
    repoURL: https://github.com/tecnodrive/platform
    targetRevision: main
    path: k8s/helm/infrastructure
    helm:
      valueFiles:
        - values.yaml
        - values-production.yaml
  
  destination:
    server: https://kubernetes.default.svc
    namespace: tecnodrive-infrastructure
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
    retry:
      limit: 3
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecnodrive-monitoring
  namespace: argocd
  labels:
    app.kubernetes.io/name: tecnodrive-monitoring
    app.kubernetes.io/part-of: tecnodrive
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  project: default
  
  source:
    repoURL: https://github.com/tecnodrive/platform
    targetRevision: main
    path: k8s/helm/monitoring
    helm:
      valueFiles:
        - values.yaml
        - values-production.yaml
  
  destination:
    server: https://kubernetes.default.svc
    namespace: tecnodrive-monitoring
  
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
    retry:
      limit: 3
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
