package com.tecnodrive.hrservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE HR Service Application
 *
 * This service handles:
 * - Employee management and profiles
 * - Department and position management
 * - Payroll and salary administration
 * - Performance evaluation and tracking
 * - Leave and attendance management
 * - Employee onboarding and offboarding
 * - Training and development tracking
 * - HR analytics and reporting
 * - Compliance and documentation
 * - Benefits and compensation management
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class HrServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(HrServiceApplication.class, args);
    }
}
