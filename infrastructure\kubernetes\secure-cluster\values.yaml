# TECNO DRIVE Secure Cluster Configuration
# Comprehensive security, monitoring, and resilience settings

global:
  # Global settings
  namespace: tecno-drive-system
  clusterName: tecno-drive-cluster
  environment: production
  region: saudi-arabia

  # Image settings
  imageRegistry: docker.io
  imagePullSecrets: []

  # Security settings
  securityContext:
    runAsUser: 1000
    runAsGroup: 3000
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault

# Gatekeeper Configuration
gatekeeper:
  enabled: true
  auditInterval: 30s
  constraintViolationsLimit: 20
  auditFromCache: false
  auditChunkSize: 500
  logLevel: INFO
  emitAdmissionEvents: true
  emitAuditEvents: true
  failurePolicy: Ignore
  timeoutSeconds: 3
  exemptNamespaces:
    - kube-system
    - kube-public
    - kube-node-lease
    - gatekeeper-system
    - argocd
    - monitoring
    - logging

  # Mutation settings
  mutation:
    enabled: true

  # Resource limits
  resources:
    limits:
      cpu: 1000m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 256Mi

# ArgoCD Configuration
argocd:
  enabled: true
  repoURL: "https://github.com/tecnodrive/platform"
  targetRevision: "HEAD"
  environment: production

  syncWaves:
    enabled: true
    waves:
      security: -1
      policies: 0
      resilience: 1
      applications: 2

  # Sync policies
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ApplyOutOfSyncOnly=true
      - RespectIgnoreDifferences=true

  # Multi-environment support
  multiEnvironment:
    enabled: false
    environments: []

# Monitoring Configuration
monitoring:
  enabled: true
  
  prometheus:
    enabled: true
    retention: 15d
    storageClass: fast-ssd
    storage: 50Gi
    
    # Alert rules
    rules:
      security:
        enabled: true
        criticalThreshold: 1
        warningThreshold: 5
      
      resilience:
        enabled: true
        pdbMissingThreshold: 1
        probeMissingThreshold: 3
      
      performance:
        enabled: true
        cpuThreshold: 80
        memoryThreshold: 85
  
  grafana:
    enabled: true
    adminPassword: "TecnoDrive2025!Secure"
    persistence:
      enabled: true
      size: 10Gi

    # Dashboards
    dashboards:
      security: true
      resilience: true
      performance: true
      business: true

    # Data sources
    datasources:
      prometheus: true
      loki: true
      elasticsearch: true

  # SLO Configuration
  slo:
    enabled: true

    # Security Policy Compliance SLO
    securityCompliance:
      target: 0.999 # 99.9%

    # Resilience Coverage SLO
    resilienceCoverage:
      target: 0.95 # 95%

    # API Gateway Performance SLO
    apiGatewayPerformance:
      target: 0.995 # 99.5%

    # Business Services SLO
    businessServices:
      target: 0.99 # 99%

    # Webhook Performance SLO
    webhookPerformance:
      maxLatency: 0.5 # 500ms
      minSuccessRate: 0.99 # 99%

    # Certificate Management SLO
    certificateManagement:
      healthTarget: 0.95 # 95%
      renewalSuccessTarget: 0.98 # 98%

  # Alertmanager Configuration
  alertmanager:
    enabled: true

# Logging Configuration
logging:
  enabled: true

  # Loki Configuration
  loki:
    enabled: true
    replicas: 1
    logLevel: info
    replicationFactor: 1
    retentionPeriod: 720h # 30 days

    image:
      tag: "2.9.0"
      pullPolicy: IfNotPresent

    persistence:
      enabled: true
      size: 10Gi
      storageClass: ""

    resources:
      requests:
        memory: 256Mi
        cpu: 100m
      limits:
        memory: 512Mi
        cpu: 500m

  # Promtail Configuration
  promtail:
    enabled: true

    image:
      tag: "2.9.0"
      pullPolicy: IfNotPresent

    resources:
      requests:
        memory: 128Mi
        cpu: 50m
      limits:
        memory: 256Mi
        cpu: 200m

  fluentBit:
    enabled: true

    # Input sources
    inputs:
      kubernetes: true
      systemd: true
      gatekeeper: true
      argocd: true

    # Filters
    filters:
      kubernetes:
        enabled: true
        mergeLog: true
        keepLog: false

      security:
        enabled: true
        regex: "violation|security|unauthorized|forbidden"

    # Outputs
    outputs:
      elasticsearch:
        enabled: true
        host: elasticsearch.logging.svc.cluster.local
        port: 9200
        index: tecno-drive-logs

      splunk:
        enabled: false
        host: ""
        port: 8088
        token: ""

      loki:
        enabled: true
        host: loki.logging.svc.cluster.local
        port: 3100

# SIEM Integration
siem:
  enabled: true
  provider: elasticsearch # elasticsearch, splunk, qradar

  elasticsearch:
    enabled: true
    endpoint: "elasticsearch.logging.svc.cluster.local:9200"
    index: "tecno-drive-security"
    username: "elastic"
    passwordSecret: "elasticsearch-credentials"

  splunk:
    enabled: false
    endpoint: ""
    port: 8088
    tokenSecret: "splunk-token"
    index: "tecno-drive"

# Advanced Alerting Configuration
alerting:
  # SMTP Configuration
  smtp:
    host: "smtp.gmail.com"
    port: 587
    from: "<EMAIL>"
    username: "<EMAIL>"
    password: "smtp-password"
    requireTLS: true

  # PagerDuty Integration
  pagerduty:
    securityRoutingKey: ""
    infrastructureRoutingKey: ""
    businessRoutingKey: ""

  # Microsoft Teams Integration
  teams:
    securityWebhookUrl: ""
    businessWebhookUrl: ""
    infrastructureWebhookUrl: ""
    sloWebhookUrl: ""
    warningsWebhookUrl: ""

  # Slack Integration
  slack:
    enabled: true
    apiUrl: ""
    webhook: ""
    channel: "#security-alerts"

  # Email Recipients
  email:
    defaultRecipients:
      - "<EMAIL>"
      - "<EMAIL>"
    securityTeam:
      - "<EMAIL>"
      - "<EMAIL>"
    platformTeam:
      - "<EMAIL>"
      - "<EMAIL>"

# Security Policies
security:
  policies:
    # Central Security Policy
    centralSecurity:
      enabled: true
      enforcement: warn # warn, deny
      
      # Security checks
      checks:
        privileged: true
        hostNetwork: true
        hostPID: true
        hostIPC: true
        capabilities: true
        securityContext: true
        resources: true
        probes: true
        networkPolicies: true
        podDisruptionBudgets: true
    
    # Network Policies
    networkPolicies:
      enabled: true
      defaultDeny: true
      allowDNS: true
      allowMetrics: true
    
    # Pod Security Standards
    podSecurity:
      enabled: true
      standard: restricted # privileged, baseline, restricted
      version: latest

# Application Configuration
application:
  # TECNO DRIVE specific settings
  tecnoDrive:
    # Core services
    apiGateway:
      enabled: true
      replicas: 3
      image: tecnodrive/api-gateway:latest
      resources:
        requests:
          cpu: 200m
          memory: 512Mi
        limits:
          cpu: 1000m
          memory: 1Gi
    
    authService:
      enabled: true
      replicas: 2
      image: tecnodrive/auth-service:latest
      resources:
        requests:
          cpu: 100m
          memory: 256Mi
        limits:
          cpu: 500m
          memory: 512Mi
    
    userService:
      enabled: true
      replicas: 2
      image: tecnodrive/user-service:latest
    
    # Business services
    rideService:
      enabled: true
      replicas: 3
      image: tecnodrive/ride-service:latest
    
    fleetService:
      enabled: true
      replicas: 2
      image: tecnodrive/fleet-service:latest
    
    paymentService:
      enabled: true
      replicas: 2
      image: tecnodrive/payment-service:latest
    
    notificationService:
      enabled: true
      replicas: 2
      image: tecnodrive/notification-service:latest
  
  # Common settings
  common:
    # Image settings
    imageTag: latest
    imagePullPolicy: Always
    
    # Security settings
    securityContext:
      runAsUser: 1000
      runAsGroup: 3000
      runAsNonRoot: true
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
    
    # Probes
    probes:
      liveness:
        enabled: true
        path: /actuator/health
        port: 8080
        initialDelaySeconds: 30
        periodSeconds: 10
      
      readiness:
        enabled: true
        path: /actuator/health/readiness
        port: 8080
        initialDelaySeconds: 5
        periodSeconds: 5
    
    # Resources
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 500m
        memory: 512Mi
    
    # Resilience
    podDisruptionBudget:
      enabled: true
      minAvailable: 1
    
    # Horizontal Pod Autoscaler
    hpa:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
      targetMemoryUtilizationPercentage: 80

# Database Configuration
database:
  postgresql:
    enabled: true
    host: postgres.database.svc.cluster.local
    port: 5432
    database: tecnodrive
    username: postgres
    passwordSecret: postgres-credentials
    
    # Connection pool
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      connectionTimeout: 10000
      leakDetectionThreshold: 30000
  
  redis:
    enabled: true
    host: redis.database.svc.cluster.local
    port: 6379
    passwordSecret: redis-credentials
    
    # Connection settings
    timeout: 5000
    pool:
      maxActive: 8
      maxIdle: 8
      minIdle: 2

# Ingress Configuration
ingress:
  enabled: true
  className: nginx
  
  # TLS settings
  tls:
    enabled: true
    secretName: tecnodrive-tls
  
  # Hosts
  hosts:
    - host: api.tecnodrive.com
      paths:
        - path: /
          pathType: Prefix
          service: api-gateway
    
    - host: app.tecnodrive.com
      paths:
        - path: /
          pathType: Prefix
          service: frontend
  
  # Annotations
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

# Certificate Manager Configuration
certManager:
  enabled: true
  duration: 8760h # 1 year
  renewBefore: 720h # 30 days
  issuerName: gatekeeper-ca-issuer
  issuerKind: ClusterIssuer

  # Backup settings
  backup:
    enabled: true
    schedule: "0 2 * * *" # Daily at 2 AM
    kubectlVersion: "latest"



# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *" # Daily at 2 AM
  retention: 30 # days

  # Velero settings
  velero:
    enabled: true
    provider: aws # aws, gcp, azure
    bucket: tecnodrive-backups
    region: us-east-1
