package com.tecnodrive.financialservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Instant;
import java.util.UUID;

/**
 * Invoice Entity
 * 
 * Represents invoices generated for various services and subscriptions.
 * Supports automated billing and payment tracking.
 */
@Entity
@Table(name = "invoices")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Invoice {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Unique invoice number
     */
    @Column(unique = true, nullable = false, length = 50)
    private String invoiceNumber;

    /**
     * Entity type being billed (CUSTOMER, DRIVER, COMPANY)
     */
    @Column(nullable = false, length = 50)
    private String billedEntityType;

    /**
     * Entity ID being billed
     */
    @Column(nullable = false)
    private String billedEntityId;

    /**
     * Invoice type
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private InvoiceType invoiceType;

    /**
     * Invoice status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private InvoiceStatus status = InvoiceStatus.DRAFT;

    /**
     * Issue date
     */
    @Column(nullable = false)
    private LocalDate issueDate;

    /**
     * Due date
     */
    @Column(nullable = false)
    private LocalDate dueDate;

    /**
     * Subtotal amount (before tax)
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal subtotalAmount;

    /**
     * Tax amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * Discount amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * Total amount (subtotal + tax - discount)
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal totalAmount;

    /**
     * Amount paid
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal amountPaid = BigDecimal.ZERO;

    /**
     * Outstanding amount
     */
    @Column(precision = 19, scale = 2)
    private BigDecimal outstandingAmount;

    /**
     * Currency code
     */
    @Column(nullable = false, length = 3)
    @Builder.Default
    private String currency = "USD";

    /**
     * Invoice description/notes
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * Terms and conditions
     */
    @Column(columnDefinition = "TEXT")
    private String terms;

    /**
     * Company/Tenant ID
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * Created by user ID
     */
    private String createdBy;

    /**
     * Payment due reminder sent count
     */
    @Builder.Default
    private Integer remindersSent = 0;

    /**
     * Last reminder sent date
     */
    private Instant lastReminderSent;

    /**
     * Additional metadata (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Invoice Type Enum
     */
    public enum InvoiceType {
        SERVICE_INVOICE,
        SUBSCRIPTION_INVOICE,
        COMMISSION_INVOICE,
        PENALTY_INVOICE,
        REFUND_INVOICE,
        ADJUSTMENT_INVOICE
    }

    /**
     * Invoice Status Enum
     */
    public enum InvoiceStatus {
        DRAFT,
        SENT,
        VIEWED,
        PARTIALLY_PAID,
        PAID,
        OVERDUE,
        CANCELLED,
        REFUNDED
    }

    /**
     * Calculate total and outstanding amounts
     */
    @PrePersist
    @PreUpdate
    private void calculateAmounts() {
        if (subtotalAmount != null) {
            BigDecimal tax = taxAmount != null ? taxAmount : BigDecimal.ZERO;
            BigDecimal discount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
            this.totalAmount = subtotalAmount.add(tax).subtract(discount);
            
            BigDecimal paid = amountPaid != null ? amountPaid : BigDecimal.ZERO;
            this.outstandingAmount = totalAmount.subtract(paid);
        }
    }

    /**
     * Check if invoice is overdue
     */
    public boolean isOverdue() {
        return LocalDate.now().isAfter(dueDate) && 
               outstandingAmount != null && 
               outstandingAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if invoice is fully paid
     */
    public boolean isFullyPaid() {
        return outstandingAmount != null && 
               outstandingAmount.compareTo(BigDecimal.ZERO) <= 0;
    }
}
