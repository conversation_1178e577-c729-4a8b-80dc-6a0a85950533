"""
Simple FastAPI server for testing TecnoDrive Platform
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime

app = FastAPI(
    title="TecnoDrive Comprehensive System",
    description="Simple API for testing TecnoDrive Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "TecnoDrive Comprehensive System is running!",
        "timestamp": datetime.now().isoformat(),
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "UP",
        "service": "TecnoDrive Comprehensive System",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/dashboard/stats")
async def get_dashboard_stats():
    return {
        "total_rides": 1250,
        "active_drivers": 45,
        "total_revenue": 125000.50,
        "pending_requests": 12,
        "fleet_status": {
            "available": 35,
            "busy": 10,
            "maintenance": 3
        }
    }

@app.get("/api/rides")
async def get_rides():
    return {
        "rides": [
            {
                "id": 1,
                "passenger": "أحمد محمد",
                "driver": "علي حسن",
                "status": "completed",
                "fare": 25.50,
                "distance": "5.2 km",
                "timestamp": "2025-01-29T10:30:00"
            },
            {
                "id": 2,
                "passenger": "فاطمة أحمد",
                "driver": "محمد علي",
                "status": "in_progress",
                "fare": 18.00,
                "distance": "3.8 km",
                "timestamp": "2025-01-29T11:15:00"
            }
        ]
    }

@app.get("/api/drivers")
async def get_drivers():
    return {
        "drivers": [
            {
                "id": 1,
                "name": "علي حسن",
                "status": "available",
                "rating": 4.8,
                "total_rides": 245,
                "location": "صنعاء - الحصبة"
            },
            {
                "id": 2,
                "name": "محمد علي",
                "status": "busy",
                "rating": 4.6,
                "total_rides": 189,
                "location": "صنعاء - الزبيري"
            }
        ]
    }

@app.get("/api/parcels")
async def get_parcels():
    return {
        "parcels": [
            {
                "id": "PCL-001",
                "barcode": "123456789012",
                "sender_name": "أحمد محمد",
                "receiver_name": "خالد علي",
                "sender_address": "شارع بغداد، صنعاء",
                "receiver_address": "شارع الزبيري، صنعاء",
                "weight_kg": 2.5,
                "status": "IN_TRANSIT",
                "priority": "MEDIUM",
                "estimated_cost": 45.50,
                "created_at": "2025-01-29T09:30:00",
                "estimated_delivery": "2025-01-29T16:00:00"
            },
            {
                "id": "PCL-002",
                "barcode": "123456789013",
                "sender_name": "فاطمة أحمد",
                "receiver_name": "سارة محمد",
                "sender_address": "شارع الستين، صنعاء",
                "receiver_address": "شارع الحصبة، صنعاء",
                "weight_kg": 1.2,
                "status": "DELIVERED",
                "priority": "HIGH",
                "estimated_cost": 32.00,
                "created_at": "2025-01-29T08:15:00",
                "delivered_at": "2025-01-29T12:30:00"
            },
            {
                "id": "PCL-003",
                "barcode": "123456789014",
                "sender_name": "محمد علي",
                "receiver_name": "عبدالله حسن",
                "sender_address": "شارع الزبيري، صنعاء",
                "receiver_address": "شارع بغداد، صنعاء",
                "weight_kg": 5.0,
                "status": "PICKED_UP",
                "priority": "URGENT",
                "estimated_cost": 75.00,
                "created_at": "2025-01-29T11:00:00",
                "estimated_delivery": "2025-01-29T14:00:00"
            }
        ],
        "total_count": 3,
        "status_summary": {
            "CREATED": 0,
            "PICKED_UP": 1,
            "IN_TRANSIT": 1,
            "OUT_FOR_DELIVERY": 0,
            "DELIVERED": 1,
            "RETURNED": 0,
            "CANCELLED": 0
        }
    }

@app.get("/api/parcels/statistics")
async def get_parcel_statistics():
    return {
        "total_parcels": 156,
        "delivered_today": 23,
        "in_transit": 45,
        "pending_pickup": 12,
        "total_revenue": 15750.50,
        "average_delivery_time": "4.2 hours",
        "delivery_success_rate": 98.5,
        "priority_breakdown": {
            "LOW": 45,
            "MEDIUM": 67,
            "HIGH": 32,
            "URGENT": 12
        },
        "status_breakdown": {
            "CREATED": 8,
            "PICKED_UP": 12,
            "IN_TRANSIT": 45,
            "OUT_FOR_DELIVERY": 18,
            "DELIVERED": 68,
            "RETURNED": 3,
            "CANCELLED": 2
        }
    }

@app.post("/api/parcels")
async def create_parcel(parcel_data: dict):
    return {
        "success": True,
        "message": "تم إنشاء الطرد بنجاح",
        "parcel": {
            "id": "PCL-004",
            "barcode": parcel_data.get("barcode", "123456789015"),
            "sender_name": parcel_data.get("sender_name", ""),
            "receiver_name": parcel_data.get("receiver_name", ""),
            "status": "CREATED",
            "estimated_cost": 35.00,
            "created_at": datetime.now().isoformat(),
            "tracking_url": f"http://localhost:8000/api/parcels/track/PCL-004"
        }
    }

@app.get("/api/parcels/track/{parcel_id}")
async def track_parcel(parcel_id: str):
    return {
        "parcel_id": parcel_id,
        "current_status": "IN_TRANSIT",
        "tracking_history": [
            {
                "status": "CREATED",
                "location": "مركز الفرز - صنعاء",
                "timestamp": "2025-01-29T09:30:00",
                "description": "تم إنشاء الطرد وتسجيله في النظام"
            },
            {
                "status": "PICKED_UP",
                "location": "شارع بغداد - صنعاء",
                "timestamp": "2025-01-29T10:15:00",
                "description": "تم استلام الطرد من المرسل"
            },
            {
                "status": "IN_TRANSIT",
                "location": "مركز التوزيع - صنعاء",
                "timestamp": "2025-01-29T11:30:00",
                "description": "الطرد في الطريق للتوصيل"
            }
        ],
        "estimated_delivery": "2025-01-29T16:00:00",
        "delivery_address": "شارع الزبيري، صنعاء"
    }

if __name__ == "__main__":
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
