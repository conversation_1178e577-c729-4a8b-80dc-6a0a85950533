package com.tecnodrive.shared.config;

import lombok.extern.slf4j.Slf4j;

/**
 * Thread-local context for storing current tenant information
 * Used across all microservices for tenant isolation
 */
@Slf4j
public class TenantContext {

    private static final ThreadLocal<String> CURRENT_TENANT = new ThreadLocal<>();
    private static final ThreadLocal<String> CURRENT_DATABASE_URL = new ThreadLocal<>();
    private static final ThreadLocal<String> CURRENT_USER = new ThreadLocal<>();

    /**
     * Set the current tenant ID for the thread
     */
    public static void setCurrentTenant(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.warn("Attempting to set null or empty tenant ID");
            return;
        }
        CURRENT_TENANT.set(tenantId.trim().toLowerCase());
        log.debug("Set current tenant to: {}", tenantId);
    }

    /**
     * Get the current tenant ID
     */
    public static String getCurrentTenant() {
        String tenant = CURRENT_TENANT.get();
        if (tenant == null) {
            log.warn("No tenant set in current context");
        }
        return tenant;
    }

    /**
     * Set the current database URL for the tenant
     */
    public static void setCurrentDatabaseUrl(String databaseUrl) {
        CURRENT_DATABASE_URL.set(databaseUrl);
        log.debug("Set current database URL for tenant: {}", getCurrentTenant());
    }

    /**
     * Get the current database URL
     */
    public static String getCurrentDatabaseUrl() {
        return CURRENT_DATABASE_URL.get();
    }

    /**
     * Set the current user for the thread
     */
    public static void setCurrentUser(String userId) {
        CURRENT_USER.set(userId);
        log.debug("Set current user to: {} for tenant: {}", userId, getCurrentTenant());
    }

    /**
     * Get the current user
     */
    public static String getCurrentUser() {
        return CURRENT_USER.get();
    }

    /**
     * Check if tenant context is properly set
     */
    public static boolean isContextSet() {
        return getCurrentTenant() != null && getCurrentDatabaseUrl() != null;
    }

    /**
     * Clear all context for the current thread
     */
    public static void clear() {
        String tenant = getCurrentTenant();
        CURRENT_TENANT.remove();
        CURRENT_DATABASE_URL.remove();
        CURRENT_USER.remove();
        log.debug("Cleared tenant context for: {}", tenant);
    }

    /**
     * Get tenant context information for logging
     */
    public static String getContextInfo() {
        return String.format("Tenant: %s, User: %s, HasDB: %s", 
                           getCurrentTenant(), 
                           getCurrentUser(), 
                           getCurrentDatabaseUrl() != null);
    }

    /**
     * Validate that required context is set
     */
    public static void validateContext() {
        if (!isContextSet()) {
            throw new IllegalStateException("Tenant context not properly initialized. " + getContextInfo());
        }
    }
}
