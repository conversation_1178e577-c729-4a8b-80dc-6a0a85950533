# 🔧 دليل استكشاف الأخطاء - TecnoDrive Platform

## 🚨 المشاكل الشائعة وحلولها

### 1. مشاكل تجميع Java (Compilation Issues)

#### المشكلة: Missing @NonNull annotations
```
Missing non-null annotation: inherited method specifies this parameter as @NonNull
```

**الحل:**
```java
// أضف @NonNull للمعاملات المطلوبة
import org.springframework.lang.NonNull;

@Override
public void handleMessage(@NonNull WebSocketSession session, @NonNull WebSocketMessage<?> message) {
    // implementation
}
```

#### المشكلة: Type safety warnings
```
Type safety: The expression of type Map needs unchecked conversion
```

**الحل:**
```java
// أضف @SuppressWarnings("unchecked")
@SuppressWarnings("unchecked")
Map<String, Object> data = objectMapper.readValue(payload, Map.class);
```

### 2. مشاكل WebSocket

#### المشكلة: WebSocket connection failed
**الأعراض:**
- الواجهة الأمامية لا تتلقى تحديثات فورية
- رسائل خطأ في console المتصفح

**الحل:**
```bash
# 1. تحقق من حالة الخدمات
curl http://localhost:8085/api/locations/health
curl http://localhost:8080/actuator/health

# 2. تحقق من إحصائيات WebSocket
curl http://localhost:8085/api/locations/websocket/stats

# 3. تحقق من logs الخدمات
docker logs tecnodrive-location-service
docker logs tecnodrive-gateway
```

### 3. مشاكل الواجهة الأمامية

#### المشكلة: الخريطة لا تظهر
**الأعراض:**
- صفحة فارغة أو رسالة خطأ
- Console errors related to Google Maps

**الحل:**
```bash
# 1. تحقق من Google Maps API Key
echo $REACT_APP_GOOGLE_MAPS_API_KEY

# 2. تحديث .env file
REACT_APP_GOOGLE_MAPS_API_KEY=your_actual_api_key_here

# 3. إعادة تشغيل الخادم
npm start
```

#### المشكلة: Dependencies issues
```
Module not found: Can't resolve '@googlemaps/react-wrapper'
```

**الحل:**
```bash
cd frontend/admin-dashboard
npm install @googlemaps/react-wrapper @types/google.maps
npm start
```

### 4. مشاكل قاعدة البيانات

#### المشكلة: Connection refused
**الأعراض:**
- خدمات لا تستطيع الاتصال بقاعدة البيانات
- Error logs تظهر connection timeouts

**الحل:**
```bash
# 1. تحقق من حالة PostgreSQL
docker ps | grep postgres

# 2. إعادة تشغيل قاعدة البيانات
docker-compose restart postgres

# 3. تحقق من connection string
# في application.yml للخدمات
```

### 5. مشاكل Docker

#### المشكلة: Port already in use
```
Error: Port 8080 is already in use
```

**الحل:**
```bash
# 1. العثور على العملية التي تستخدم المنفذ
netstat -ano | findstr :8080

# 2. إيقاف العملية
taskkill /PID <process_id> /F

# 3. أو استخدام منفذ مختلف
# تحديث docker-compose.yml
```

### 6. مشاكل الأداء

#### المشكلة: بطء في التحديثات
**الأعراض:**
- تأخير في عرض البيانات
- WebSocket disconnections متكررة

**الحل:**
```bash
# 1. تحقق من استخدام الذاكرة
docker stats

# 2. زيادة memory limits
# في docker-compose.yml
services:
  location-service:
    mem_limit: 1g

# 3. تحسين Redis configuration
# في redis.conf
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 🛠️ أدوات التشخيص

### 1. فحص صحة النظام
```bash
# تشغيل اختبار شامل
.\test-websocket-integration.ps1

# فحص خدمة محددة
curl http://localhost:8085/api/locations/health
```

### 2. مراقبة Logs
```bash
# عرض logs لجميع الخدمات
docker-compose logs -f

# عرض logs لخدمة محددة
docker-compose logs -f location-service
```

### 3. اختبار WebSocket يدوياً
```javascript
// في console المتصفح
const ws = new WebSocket('ws://localhost:8085/ws/live-tracking');
ws.onopen = () => console.log('Connected');
ws.onmessage = (event) => console.log('Message:', event.data);
ws.send(JSON.stringify({type: 'subscribe_fleet'}));
```

## 📋 Checklist للتشخيص

### قبل البدء
- [ ] Java 17+ مثبت
- [ ] Node.js 16+ مثبت
- [ ] Docker running
- [ ] PostgreSQL accessible
- [ ] Redis accessible

### عند مواجهة مشاكل
- [ ] تحقق من logs الخدمات
- [ ] تحقق من حالة قواعد البيانات
- [ ] تحقق من network connectivity
- [ ] تحقق من environment variables
- [ ] تحقق من port availability

### للواجهة الأمامية
- [ ] npm dependencies installed
- [ ] .env file configured
- [ ] Google Maps API key valid
- [ ] Browser console clear of errors

## 🆘 الحصول على المساعدة

### معلومات مفيدة للدعم
عند طلب المساعدة، قدم المعلومات التالية:

1. **نظام التشغيل**: Windows/Linux/Mac
2. **إصدارات البرامج**:
   ```bash
   java -version
   node --version
   docker --version
   ```
3. **رسائل الخطأ الكاملة**
4. **خطوات إعادة إنتاج المشكلة**
5. **Logs ذات الصلة**

### ملفات Log المهمة
- `docker-compose logs location-service`
- `docker-compose logs api-gateway`
- Browser console (F12 -> Console)
- Network tab في Developer Tools

---

**نصيحة**: احتفظ بهذا الدليل مرجعاً سريعاً لحل المشاكل الشائعة!
