package com.tecnodrive.walletservice.dto;

import com.tecnodrive.walletservice.entity.Wallet;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Wallet Data Transfer Object
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletDTO {

    private UUID id;
    private UUID userId;
    private String phoneNumber;
    private BigDecimal balance;
    private String currency;
    private Wallet.WalletStatus status;
    private BigDecimal dailyLimit;
    private BigDecimal monthlyLimit;
    private BigDecimal dailySpent;
    private BigDecimal monthlySpent;
    private LocalDateTime lastTransactionDate;
    private Boolean isPinSet;
    private Integer failedPinAttempts;
    private LocalDateTime lockedUntil;
    private Wallet.VerificationLevel verificationLevel;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

    // Additional computed fields
    private BigDecimal availableBalance;
    private BigDecimal dailyRemaining;
    private BigDecimal monthlyRemaining;
    private Boolean isLocked;
    private Boolean isUsable;
    private Integer transactionCount;
    private LocalDateTime lastActivityDate;

    /**
     * Create WalletDTO from Wallet entity
     */
    public static WalletDTO fromEntity(Wallet wallet) {
        if (wallet == null) {
            return null;
        }

        return WalletDTO.builder()
                .id(wallet.getId())
                .userId(wallet.getUserId())
                .phoneNumber(wallet.getPhoneNumber())
                .balance(wallet.getBalance())
                .currency(wallet.getCurrency())
                .status(wallet.getStatus())
                .dailyLimit(wallet.getDailyLimit())
                .monthlyLimit(wallet.getMonthlyLimit())
                .dailySpent(wallet.getDailySpent())
                .monthlySpent(wallet.getMonthlySpent())
                .lastTransactionDate(wallet.getLastTransactionDate())
                .isPinSet(wallet.getIsPinSet())
                .failedPinAttempts(wallet.getFailedPinAttempts())
                .lockedUntil(wallet.getLockedUntil())
                .verificationLevel(wallet.getVerificationLevel())
                .notes(wallet.getNotes())
                .createdAt(wallet.getCreatedAt())
                .updatedAt(wallet.getUpdatedAt())
                .createdBy(wallet.getCreatedBy())
                .updatedBy(wallet.getUpdatedBy())
                // Computed fields
                .availableBalance(wallet.getBalance())
                .dailyRemaining(wallet.getDailyLimit().subtract(wallet.getDailySpent()))
                .monthlyRemaining(wallet.getMonthlyLimit().subtract(wallet.getMonthlySpent()))
                .isLocked(wallet.getLockedUntil() != null && wallet.getLockedUntil().isAfter(LocalDateTime.now()))
                .isUsable(wallet.isUsable())
                .lastActivityDate(wallet.getLastTransactionDate())
                .build();
    }

    /**
     * Create Wallet entity from WalletDTO
     */
    public Wallet toEntity() {
        return Wallet.builder()
                .id(this.id)
                .userId(this.userId)
                .phoneNumber(this.phoneNumber)
                .balance(this.balance != null ? this.balance : BigDecimal.ZERO)
                .currency(this.currency != null ? this.currency : "SAR")
                .status(this.status != null ? this.status : Wallet.WalletStatus.ACTIVE)
                .dailyLimit(this.dailyLimit != null ? this.dailyLimit : new BigDecimal("5000.00"))
                .monthlyLimit(this.monthlyLimit != null ? this.monthlyLimit : new BigDecimal("50000.00"))
                .dailySpent(this.dailySpent != null ? this.dailySpent : BigDecimal.ZERO)
                .monthlySpent(this.monthlySpent != null ? this.monthlySpent : BigDecimal.ZERO)
                .lastTransactionDate(this.lastTransactionDate)
                .isPinSet(this.isPinSet != null ? this.isPinSet : false)
                .failedPinAttempts(this.failedPinAttempts != null ? this.failedPinAttempts : 0)
                .lockedUntil(this.lockedUntil)
                .verificationLevel(this.verificationLevel != null ? this.verificationLevel : Wallet.VerificationLevel.BASIC)
                .notes(this.notes)
                .createdBy(this.createdBy)
                .updatedBy(this.updatedBy)
                .build();
    }
}

/**
 * Wallet Creation Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CreateWalletRequest {
    private UUID userId;
    private String phoneNumber;
    private BigDecimal initialBalance;
    private String currency;
    private BigDecimal dailyLimit;
    private BigDecimal monthlyLimit;
    private String notes;
    private String createdBy;
}

/**
 * Wallet Update Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class UpdateWalletRequest {
    private Wallet.WalletStatus status;
    private BigDecimal dailyLimit;
    private BigDecimal monthlyLimit;
    private Wallet.VerificationLevel verificationLevel;
    private String notes;
    private String updatedBy;
}

/**
 * Wallet Balance Response DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WalletBalanceResponse {
    private UUID walletId;
    private BigDecimal balance;
    private String currency;
    private BigDecimal dailyRemaining;
    private BigDecimal monthlyRemaining;
    private Boolean isUsable;
    private LocalDateTime lastUpdated;
}

/**
 * Wallet Summary DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WalletSummaryDTO {
    private UUID id;
    private String phoneNumber;
    private BigDecimal balance;
    private String currency;
    private Wallet.WalletStatus status;
    private Wallet.VerificationLevel verificationLevel;
    private LocalDateTime lastTransactionDate;
    private Boolean isUsable;
    private Integer transactionCount;
    private LocalDateTime createdAt;
}

/**
 * Wallet Statistics DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WalletStatisticsDTO {
    private Long totalWallets;
    private Long activeWallets;
    private Long suspendedWallets;
    private Long blockedWallets;
    private Long closedWallets;
    private BigDecimal totalBalance;
    private BigDecimal averageBalance;
    private BigDecimal totalDailySpent;
    private BigDecimal totalMonthlySpent;
    private LocalDateTime lastUpdated;
}
