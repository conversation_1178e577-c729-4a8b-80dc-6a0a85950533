package com.tecnodrive.auth.service.impl;

import com.tecnodrive.auth.entity.User;
import com.tecnodrive.auth.service.JwtService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT Service Implementation
 */
@Service
public class JwtServiceImpl implements JwtService {

    private static final Logger log = LoggerFactory.getLogger(JwtServiceImpl.class);

    @Value("${jwt.secret:mySecretKey}")
    private String secretKey;

    @Value("${jwt.access-token.expiration:3600}")
    private long accessTokenExpiration; // 1 hour in seconds

    @Value("${jwt.refresh-token.expiration:604800}")
    private long refreshTokenExpiration; // 7 days in seconds

    @Override
    public String generateAccessToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId().toString());
        claims.put("userType", user.getUserType().name());
        claims.put("email", user.getEmail());
        if (user.getCompanyId() != null) {
            claims.put("companyId", user.getCompanyId().toString());
        }
        return generateToken(claims, user);
    }

    @Override
    public String generateRefreshToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId().toString());
        claims.put("tokenType", "refresh");
        return createToken(claims, user.getUsername(), refreshTokenExpiration * 1000);
    }

    @Override
    public String generateToken(Map<String, Object> extraClaims, User user) {
        return createToken(extraClaims, user.getUsername(), accessTokenExpiration * 1000);
    }

    private String createToken(Map<String, Object> claims, String subject, long expiration) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    @Override
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    @Override
    public String extractUserId(String token) {
        return extractClaim(token, "userId", String.class);
    }

    @Override
    public <T> T extractClaim(String token, String claimName, Class<T> type) {
        final Claims claims = extractAllClaimsInternal(token);
        return claims.get(claimName, type);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaimsInternal(token);
        return claimsResolver.apply(claims);
    }

    @Override
    public boolean isTokenValid(String token, User user) {
        try {
            final String username = extractUsername(token);
            return username.equals(user.getUsername()) && !isTokenExpired(token);
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            log.error("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }

    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    @Override
    public long getAccessTokenExpirationTime() {
        return accessTokenExpiration;
    }

    @Override
    public long getRefreshTokenExpirationTime() {
        return refreshTokenExpiration;
    }

    @Override
    public boolean validateTokenFormat(String token) {
        try {
            extractAllClaimsInternal(token);
            return true;
        } catch (Exception e) {
            log.error("Invalid token format: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Object> extractAllClaims(String token) {
        return extractAllClaimsInternal(token);
    }

    private Claims extractAllClaimsInternal(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private SecretKey getSignInKey() {
        byte[] keyBytes = secretKey.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
