{{- if .Values.monitoring.alertmanager.enabled }}
# Advanced Alertmanager Configuration with PagerDuty and Teams Integration
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-config
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: alertmanager-config
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "alerting"
type: Opaque
stringData:
  alertmanager.yml: |
    global:
      # TECNO DRIVE global configuration
      smtp_smarthost: '{{ .Values.alerting.smtp.host }}:{{ .Values.alerting.smtp.port | default 587 }}'
      smtp_from: '{{ .Values.alerting.smtp.from | default "<EMAIL>" }}'
      smtp_auth_username: '{{ .Values.alerting.smtp.username }}'
      smtp_auth_password: '{{ .Values.alerting.smtp.password }}'
      smtp_require_tls: {{ .Values.alerting.smtp.requireTLS | default true }}
      
      # PagerDuty global settings
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
      
      # Slack global settings
      slack_api_url: '{{ .Values.alerting.slack.apiUrl }}'
      
      # Resolve timeout
      resolve_timeout: 5m

    # Routing tree
    route:
      group_by: ['alertname', 'cluster', 'service', 'severity']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'default-receiver'
      
      routes:
        # Critical Security Violations - Immediate PagerDuty
        - match:
            severity: critical
            category: security
          receiver: 'pagerduty-critical-security'
          group_wait: 0s
          group_interval: 5m
          repeat_interval: 30m
          continue: true
        
        # Critical Infrastructure Issues
        - match:
            severity: critical
            category: infrastructure
          receiver: 'pagerduty-critical-infrastructure'
          group_wait: 0s
          group_interval: 5m
          repeat_interval: 1h
          continue: true
        
        # High Priority Security Issues - Teams + Email
        - match:
            severity: high
            category: security
          receiver: 'teams-security-high'
          group_wait: 30s
          group_interval: 10m
          repeat_interval: 2h
          continue: true
        
        # Business Critical Alerts
        - match_re:
            service: '(api-gateway|payment-service|ride-service)'
            severity: '(critical|high)'
          receiver: 'business-critical'
          group_wait: 0s
          group_interval: 5m
          repeat_interval: 1h
          continue: true
        
        # Gatekeeper Policy Violations
        - match:
            alertname: 'CriticalSecurityViolation'
          receiver: 'security-violations'
          group_wait: 0s
          group_interval: 1m
          repeat_interval: 30m
        
        # Certificate Expiry Warnings
        - match:
            alertname: 'CertificateExpirySoon'
          receiver: 'certificate-alerts'
          group_wait: 1m
          group_interval: 1h
          repeat_interval: 24h
        
        # SLO Burn Rate Alerts
        - match_re:
            alertname: '.*SLO.*'
          receiver: 'slo-alerts'
          group_wait: 5m
          group_interval: 30m
          repeat_interval: 4h
        
        # Warning level alerts - Teams only
        - match:
            severity: warning
          receiver: 'teams-warnings'
          group_wait: 5m
          group_interval: 1h
          repeat_interval: 12h

    # Inhibition rules
    inhibit_rules:
      # Inhibit warning alerts if critical alert is firing
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'cluster', 'service']
      
      # Inhibit high alerts if critical alert is firing
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'high'
        equal: ['alertname', 'cluster', 'service']

    # Receivers configuration
    receivers:
      # Default receiver
      - name: 'default-receiver'
        email_configs:
          - to: '{{ .Values.alerting.email.defaultRecipients | join "," }}'
            subject: '[TECNO DRIVE] {{ "{{ .GroupLabels.alertname }}" }} - {{ "{{ .GroupLabels.severity | title }}" }}'
            body: |
              {{ "{{ range .Alerts }}" }}
              Alert: {{ "{{ .Annotations.summary }}" }}
              Description: {{ "{{ .Annotations.description }}" }}
              Severity: {{ "{{ .Labels.severity }}" }}
              Service: {{ "{{ .Labels.service }}" }}
              Cluster: {{ "{{ .Labels.cluster }}" }}
              Time: {{ "{{ .StartsAt }}" }}
              {{ "{{ end }}" }}

      # PagerDuty Critical Security
      - name: 'pagerduty-critical-security'
        pagerduty_configs:
          - routing_key: '{{ .Values.alerting.pagerduty.securityRoutingKey }}'
            description: '🚨 CRITICAL SECURITY: {{ "{{ .GroupLabels.alertname }}" }}'
            severity: 'critical'
            details:
              summary: '{{ "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}" }}'
              description: '{{ "{{ range .Alerts }}{{ .Annotations.description }}{{ end }}" }}'
              cluster: '{{ "{{ .GroupLabels.cluster }}" }}'
              service: '{{ "{{ .GroupLabels.service }}" }}'
              runbook_url: '{{ "{{ range .Alerts }}{{ .Annotations.runbook_url }}{{ end }}" }}'
            links:
              - href: 'https://grafana.tecnodrive.com/d/security-dashboard'
                text: 'Security Dashboard'
              - href: 'https://prometheus.tecnodrive.com/alerts'
                text: 'Prometheus Alerts'

      # PagerDuty Critical Infrastructure
      - name: 'pagerduty-critical-infrastructure'
        pagerduty_configs:
          - routing_key: '{{ .Values.alerting.pagerduty.infrastructureRoutingKey }}'
            description: '🔥 CRITICAL INFRA: {{ "{{ .GroupLabels.alertname }}" }}'
            severity: 'critical'
            details:
              summary: '{{ "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}" }}'
              cluster: '{{ "{{ .GroupLabels.cluster }}" }}'
              service: '{{ "{{ .GroupLabels.service }}" }}'

      # Microsoft Teams Security High Priority
      - name: 'teams-security-high'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.securityWebhookUrl }}'
            send_resolved: true
            title: '⚠️ High Priority Security Alert'
            text: |
              **Alert:** {{ "{{ .GroupLabels.alertname }}" }}
              **Severity:** {{ "{{ .GroupLabels.severity | title }}" }}
              **Cluster:** {{ "{{ .GroupLabels.cluster }}" }}
              **Service:** {{ "{{ .GroupLabels.service }}" }}
              
              {{ "{{ range .Alerts }}" }}
              **Summary:** {{ "{{ .Annotations.summary }}" }}
              **Description:** {{ "{{ .Annotations.description }}" }}
              **Time:** {{ "{{ .StartsAt.Format \"2006-01-02 15:04:05\" }}" }}
              {{ "{{ end }}" }}
              
              [View in Grafana](https://grafana.tecnodrive.com/d/security-dashboard)

      # Business Critical Alerts
      - name: 'business-critical'
        pagerduty_configs:
          - routing_key: '{{ .Values.alerting.pagerduty.businessRoutingKey }}'
            description: '💼 BUSINESS CRITICAL: {{ "{{ .GroupLabels.alertname }}" }}'
            severity: 'critical'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.businessWebhookUrl }}'
            send_resolved: true
            title: '💼 Business Critical Alert'
            text: |
              **🚨 BUSINESS IMPACT DETECTED**
              
              **Service:** {{ "{{ .GroupLabels.service }}" }}
              **Alert:** {{ "{{ .GroupLabels.alertname }}" }}
              **Severity:** {{ "{{ .GroupLabels.severity | title }}" }}
              
              {{ "{{ range .Alerts }}" }}
              **Impact:** {{ "{{ .Annotations.summary }}" }}
              **Details:** {{ "{{ .Annotations.description }}" }}
              **Started:** {{ "{{ .StartsAt.Format \"2006-01-02 15:04:05\" }}" }}
              {{ "{{ end }}" }}
              
              **Immediate Actions Required:**
              1. Check service health dashboard
              2. Review recent deployments
              3. Escalate to on-call engineer if needed
              
              [Service Dashboard](https://grafana.tecnodrive.com/d/service-overview)
              [Incident Response](https://docs.tecnodrive.com/incident-response)

      # Security Violations
      - name: 'security-violations'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.securityWebhookUrl }}'
            send_resolved: true
            title: '🛡️ Security Policy Violation'
            text: |
              **🚨 SECURITY POLICY VIOLATION DETECTED**
              
              {{ "{{ range .Alerts }}" }}
              **Policy:** {{ "{{ .Labels.constraint }}" }}
              **Violation:** {{ "{{ .Annotations.summary }}" }}
              **Resource:** {{ "{{ .Labels.resource_namespace }}" }}/{{ "{{ .Labels.resource_name }}" }}
              **Type:** {{ "{{ .Labels.violation_kind }}" }}
              **Severity:** {{ "{{ .Labels.severity }}" }}
              **Time:** {{ "{{ .StartsAt.Format \"2006-01-02 15:04:05\" }}" }}
              {{ "{{ end }}" }}
              
              **Required Actions:**
              1. Review the violating resource
              2. Apply security fixes
              3. Update deployment practices
              
              [Security Dashboard](https://grafana.tecnodrive.com/d/security-dashboard)
              [Policy Documentation](https://docs.tecnodrive.com/security-policies)
        email_configs:
          - to: '{{ .Values.alerting.email.securityTeam | join "," }}'
            subject: '[SECURITY] Policy Violation - {{ "{{ .GroupLabels.alertname }}" }}'
            body: |
              Security Policy Violation Detected
              
              {{ "{{ range .Alerts }}" }}
              Alert: {{ "{{ .Annotations.summary }}" }}
              Description: {{ "{{ .Annotations.description }}" }}
              Resource: {{ "{{ .Labels.resource_namespace }}" }}/{{ "{{ .Labels.resource_name }}" }}
              Policy: {{ "{{ .Labels.constraint }}" }}
              Severity: {{ "{{ .Labels.severity }}" }}
              Time: {{ "{{ .StartsAt }}" }}
              {{ "{{ end }}" }}

      # Certificate Alerts
      - name: 'certificate-alerts'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.infrastructureWebhookUrl }}'
            send_resolved: true
            title: '🔐 Certificate Expiry Warning'
            text: |
              **🔐 CERTIFICATE EXPIRING SOON**
              
              {{ "{{ range .Alerts }}" }}
              **Certificate:** {{ "{{ .Labels.name }}" }}
              **Namespace:** {{ "{{ .Labels.namespace }}" }}
              **Expires In:** {{ "{{ .Annotations.description }}" }}
              **Time:** {{ "{{ .StartsAt.Format \"2006-01-02 15:04:05\" }}" }}
              {{ "{{ end }}" }}
              
              **Actions Required:**
              1. Check cert-manager status
              2. Verify renewal process
              3. Manual renewal if needed
              
              [Certificate Dashboard](https://grafana.tecnodrive.com/d/certificates)

      # SLO Alerts
      - name: 'slo-alerts'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.sloWebhookUrl }}'
            send_resolved: true
            title: '📊 SLO Burn Rate Alert'
            text: |
              **📊 SERVICE LEVEL OBJECTIVE ALERT**
              
              {{ "{{ range .Alerts }}" }}
              **SLO:** {{ "{{ .Labels.sloth_slo }}" }}
              **Service:** {{ "{{ .Labels.service }}" }}
              **Burn Rate:** {{ "{{ .Annotations.description }}" }}
              **Impact:** {{ "{{ .Annotations.summary }}" }}
              {{ "{{ end }}" }}
              
              **Review Required:**
              1. Check service performance
              2. Analyze error rates
              3. Consider scaling if needed
              
              [SLO Dashboard](https://grafana.tecnodrive.com/d/slo-overview)

      # Teams Warnings
      - name: 'teams-warnings'
        webhook_configs:
          - url: '{{ .Values.alerting.teams.warningsWebhookUrl }}'
            send_resolved: true
            title: '⚠️ System Warning'
            text: |
              **⚠️ SYSTEM WARNING**
              
              {{ "{{ range .Alerts }}" }}
              **Alert:** {{ "{{ .Annotations.summary }}" }}
              **Service:** {{ "{{ .Labels.service }}" }}
              **Details:** {{ "{{ .Annotations.description }}" }}
              {{ "{{ end }}" }}
              
              [System Dashboard](https://grafana.tecnodrive.com/d/system-overview)

    # Templates for reusable message formatting
    templates:
      - '/etc/alertmanager/templates/*.tmpl'

---
# Alertmanager Templates ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-templates
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: alertmanager-templates
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "alerting"
data:
  tecno-drive.tmpl: |
    {{ "{{" }} define "tecno-drive.title" {{ "}}" }}
    [TECNO DRIVE] {{ "{{" }} .GroupLabels.alertname {{ "}}" }} - {{ "{{" }} .GroupLabels.severity | title {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}
    
    {{ "{{" }} define "tecno-drive.slack.title" {{ "}}" }}
    {{ "{{" }} if eq .Status "firing" {{ "}}" }}🔥{{ "{{" }} else {{ "}}" }}✅{{ "{{" }} end {{ "}}" }} 
    {{ "{{" }} .GroupLabels.alertname {{ "}}" }} - {{ "{{" }} .GroupLabels.severity | title {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}
    
    {{ "{{" }} define "tecno-drive.slack.text" {{ "}}" }}
    {{ "{{" }} range .Alerts {{ "}}" }}
    *Alert:* {{ "{{" }} .Annotations.summary {{ "}}" }}
    *Description:* {{ "{{" }} .Annotations.description {{ "}}" }}
    *Severity:* {{ "{{" }} .Labels.severity {{ "}}" }}
    *Service:* {{ "{{" }} .Labels.service {{ "}}" }}
    *Cluster:* {{ "{{" }} .Labels.cluster {{ "}}" }}
    *Time:* {{ "{{" }} .StartsAt.Format "2006-01-02 15:04:05" {{ "}}" }}
    {{ "{{" }} if .Annotations.runbook_url {{ "}}" }}
    *Runbook:* {{ "{{" }} .Annotations.runbook_url {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}
    
    {{ "{{" }} define "tecno-drive.teams.summary" {{ "}}" }}
    {{ "{{" }} if eq .Status "firing" {{ "}}" }}
    🚨 **ALERT FIRING**
    {{ "{{" }} else {{ "}}" }}
    ✅ **ALERT RESOLVED**
    {{ "{{" }} end {{ "}}" }}
    
    **Service:** {{ "{{" }} .GroupLabels.service {{ "}}" }}
    **Severity:** {{ "{{" }} .GroupLabels.severity | title {{ "}}" }}
    **Cluster:** {{ "{{" }} .GroupLabels.cluster {{ "}}" }}
    {{ "{{" }} end {{ "}}" }}

---
# ServiceMonitor for Alertmanager
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: alertmanager-metrics
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: alertmanager-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "alerting"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: alertmanager
  endpoints:
    - port: web
      interval: 30s
      path: /metrics
      scrapeTimeout: 10s
{{- end }}
{{- end }}
