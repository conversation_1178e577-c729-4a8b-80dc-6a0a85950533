package com.tecnodrive.auth.entity;

/**
 * User Type Enumeration
 */
public enum UserType {
    CUSTOMER("Customer"),
    DRIVE<PERSON>("Driver"),
    <PERSON><PERSON><PERSON>("Administrator"),
    COMPANY_<PERSON>N("Company Administrator"),
    SCH<PERSON><PERSON>_<PERSON>N("School Administrator"),
    <PERSON><PERSON><PERSON>_<PERSON>N("Super Administrator");

    private final String displayName;

    UserType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public boolean isAdmin() {
        return this == ADMIN || this == COMPANY_ADMIN || this == SCHOOL_ADMIN || this == SUPER_ADMIN;
    }

    public boolean isSuperAdmin() {
        return this == SUPER_ADMIN;
    }

    public boolean isCompanyUser() {
        return this == COMPANY_ADMIN || this == DRIVER;
    }
}
