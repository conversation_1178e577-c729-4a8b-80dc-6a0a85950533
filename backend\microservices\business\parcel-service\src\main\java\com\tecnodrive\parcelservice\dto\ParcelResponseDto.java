package com.tecnodrive.parcelservice.dto;

import java.time.Instant;
import java.util.UUID;

/**
 * Parcel Response DTO
 */
public class ParcelResponseDto {

    private UUID id;
    private String status;
    private String trackingNumber;
    private Instant createdAt;

    // Default constructor
    public ParcelResponseDto() {}

    // All-args constructor
    public ParcelResponseDto(UUID id, String status, String trackingNumber, Instant createdAt) {
        this.id = id;
        this.status = status;
        this.trackingNumber = trackingNumber;
        this.createdAt = createdAt;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
}
