package com.tecnodrive.walletservice.repository;

import com.tecnodrive.walletservice.entity.WalletTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Wallet Transaction Repository
 */
@Repository
public interface WalletTransactionRepository extends JpaRepository<WalletTransaction, UUID> {

    /**
     * Find transaction by reference
     */
    Optional<WalletTransaction> findByTransactionReference(String transactionReference);

    /**
     * Find transactions by wallet ID
     */
    List<WalletTransaction> findByWalletIdOrderByCreatedAtDesc(UUID walletId);

    /**
     * Find transactions by wallet ID with pagination
     */
    Page<WalletTransaction> findByWalletIdOrderByCreatedAtDesc(UUID walletId, Pageable pageable);

    /**
     * Find transactions by wallet ID and status
     */
    List<WalletTransaction> findByWalletIdAndStatus(UUID walletId, WalletTransaction.TransactionStatus status);

    /**
     * Find transactions by wallet ID and type
     */
    List<WalletTransaction> findByWalletIdAndType(UUID walletId, WalletTransaction.TransactionType type);

    /**
     * Find transactions by wallet ID and source
     */
    List<WalletTransaction> findByWalletIdAndSource(UUID walletId, WalletTransaction.TransactionSource source);

    /**
     * Find transactions by status
     */
    List<WalletTransaction> findByStatus(WalletTransaction.TransactionStatus status);

    /**
     * Find transactions by type
     */
    List<WalletTransaction> findByType(WalletTransaction.TransactionType type);

    /**
     * Find transactions by source
     */
    List<WalletTransaction> findBySource(WalletTransaction.TransactionSource source);

    /**
     * Find transactions by reference ID and type
     */
    List<WalletTransaction> findByReferenceIdAndReferenceType(String referenceId, String referenceType);

    /**
     * Find transactions by agent ID
     */
    List<WalletTransaction> findByAgentId(UUID agentId);

    /**
     * Find transactions created between dates
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    List<WalletTransaction> findTransactionsBetweenDates(@Param("startDate") LocalDateTime startDate, 
                                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Find transactions by wallet and date range
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.walletId = :walletId AND t.createdAt BETWEEN :startDate AND :endDate ORDER BY t.createdAt DESC")
    List<WalletTransaction> findByWalletIdAndDateRange(@Param("walletId") UUID walletId,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Find transactions by wallet and date range with pagination
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.walletId = :walletId AND t.createdAt BETWEEN :startDate AND :endDate ORDER BY t.createdAt DESC")
    Page<WalletTransaction> findByWalletIdAndDateRange(@Param("walletId") UUID walletId,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate,
                                                       Pageable pageable);

    /**
     * Get total amount by wallet and type
     */
    @Query("SELECT SUM(t.amount) FROM WalletTransaction t WHERE t.walletId = :walletId AND t.type = :type AND t.status = 'COMPLETED'")
    BigDecimal getTotalAmountByWalletAndType(@Param("walletId") UUID walletId, 
                                             @Param("type") WalletTransaction.TransactionType type);

    /**
     * Get total amount by wallet and source
     */
    @Query("SELECT SUM(t.amount) FROM WalletTransaction t WHERE t.walletId = :walletId AND t.source = :source AND t.status = 'COMPLETED'")
    BigDecimal getTotalAmountByWalletAndSource(@Param("walletId") UUID walletId, 
                                               @Param("source") WalletTransaction.TransactionSource source);

    /**
     * Get daily spending for wallet
     */
    @Query("SELECT SUM(t.amount) FROM WalletTransaction t WHERE t.walletId = :walletId AND t.type = 'DEBIT' AND t.status = 'COMPLETED' AND DATE(t.createdAt) = CURRENT_DATE")
    BigDecimal getDailySpending(@Param("walletId") UUID walletId);

    /**
     * Get monthly spending for wallet
     */
    @Query("SELECT SUM(t.amount) FROM WalletTransaction t WHERE t.walletId = :walletId AND t.type = 'DEBIT' AND t.status = 'COMPLETED' AND EXTRACT(MONTH FROM t.createdAt) = EXTRACT(MONTH FROM CURRENT_DATE) AND EXTRACT(YEAR FROM t.createdAt) = EXTRACT(YEAR FROM CURRENT_DATE)")
    BigDecimal getMonthlySpending(@Param("walletId") UUID walletId);

    /**
     * Count transactions by wallet and status
     */
    @Query("SELECT COUNT(t) FROM WalletTransaction t WHERE t.walletId = :walletId AND t.status = :status")
    Long countByWalletIdAndStatus(@Param("walletId") UUID walletId, 
                                  @Param("status") WalletTransaction.TransactionStatus status);

    /**
     * Find pending transactions older than specified time
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.status = 'PENDING' AND t.createdAt < :cutoffTime")
    List<WalletTransaction> findPendingTransactionsOlderThan(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find failed transactions
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.status = 'FAILED' ORDER BY t.createdAt DESC")
    List<WalletTransaction> findFailedTransactions();

    /**
     * Search transactions by multiple criteria
     */
    @Query("SELECT t FROM WalletTransaction t WHERE " +
           "(:walletId IS NULL OR t.walletId = :walletId) AND " +
           "(:type IS NULL OR t.type = :type) AND " +
           "(:source IS NULL OR t.source = :source) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:minAmount IS NULL OR t.amount >= :minAmount) AND " +
           "(:maxAmount IS NULL OR t.amount <= :maxAmount) AND " +
           "(:startDate IS NULL OR t.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR t.createdAt <= :endDate) " +
           "ORDER BY t.createdAt DESC")
    Page<WalletTransaction> searchTransactions(@Param("walletId") UUID walletId,
                                               @Param("type") WalletTransaction.TransactionType type,
                                               @Param("source") WalletTransaction.TransactionSource source,
                                               @Param("status") WalletTransaction.TransactionStatus status,
                                               @Param("minAmount") BigDecimal minAmount,
                                               @Param("maxAmount") BigDecimal maxAmount,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate,
                                               Pageable pageable);

    /**
     * Get transaction statistics
     */
    @Query("SELECT " +
           "COUNT(t) as totalTransactions, " +
           "SUM(CASE WHEN t.status = 'COMPLETED' THEN 1 ELSE 0 END) as completedTransactions, " +
           "SUM(CASE WHEN t.status = 'PENDING' THEN 1 ELSE 0 END) as pendingTransactions, " +
           "SUM(CASE WHEN t.status = 'FAILED' THEN 1 ELSE 0 END) as failedTransactions, " +
           "SUM(CASE WHEN t.type = 'CREDIT' AND t.status = 'COMPLETED' THEN t.amount ELSE 0 END) as totalCredits, " +
           "SUM(CASE WHEN t.type = 'DEBIT' AND t.status = 'COMPLETED' THEN t.amount ELSE 0 END) as totalDebits " +
           "FROM WalletTransaction t")
    Object[] getTransactionStatistics();

    /**
     * Get daily transaction statistics
     */
    @Query("SELECT " +
           "DATE(t.createdAt) as transactionDate, " +
           "COUNT(t) as transactionCount, " +
           "SUM(CASE WHEN t.type = 'CREDIT' AND t.status = 'COMPLETED' THEN t.amount ELSE 0 END) as totalCredits, " +
           "SUM(CASE WHEN t.type = 'DEBIT' AND t.status = 'COMPLETED' THEN t.amount ELSE 0 END) as totalDebits " +
           "FROM WalletTransaction t " +
           "WHERE t.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(t.createdAt) " +
           "ORDER BY DATE(t.createdAt)")
    List<Object[]> getDailyTransactionStatistics(@Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Get top transactions by amount
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.status = 'COMPLETED' ORDER BY t.amount DESC")
    List<WalletTransaction> findTopTransactionsByAmount(Pageable pageable);

    /**
     * Find recent transactions for wallet
     */
    @Query("SELECT t FROM WalletTransaction t WHERE t.walletId = :walletId ORDER BY t.createdAt DESC")
    List<WalletTransaction> findRecentTransactions(@Param("walletId") UUID walletId, Pageable pageable);
}
