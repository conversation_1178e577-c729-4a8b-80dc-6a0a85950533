import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import { useTheme } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchAnalytics } from '../../store/slices/analyticsSlice';

const Analytics: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { data: analytics, loading } = useSelector((state: RootState) => state.analytics);

  useEffect(() => {
    dispatch(fetchAnalytics() as any);
  }, [dispatch]);

  // Mock data for charts
  const dailyRidesData = [
    { day: 'السبت', rides: 45 },
    { day: 'الأحد', rides: 52 },
    { day: 'الاثنين', rides: 38 },
    { day: 'الثلاثاء', rides: 61 },
    { day: 'الأربعاء', rides: 55 },
    { day: 'الخميس', rides: 67 },
    { day: 'الجمعة', rides: 43 },
  ];

  const revenueData = [
    { month: 'يناير', revenue: 25000 },
    { month: 'فبراير', revenue: 28000 },
    { month: 'مارس', revenue: 32000 },
    { month: 'أبريل', revenue: 35000 },
    { month: 'مايو', revenue: 38000 },
    { month: 'يونيو', revenue: 42000 },
  ];

  const rideStatusData = [
    { name: 'مكتملة', value: 65, color: theme.palette.success.main },
    { name: 'جارية', value: 20, color: theme.palette.warning.main },
    { name: 'ملغية', value: 10, color: theme.palette.error.main },
    { name: 'مطلوبة', value: 5, color: theme.palette.info.main },
  ];

  const topRoutesData = [
    { route: 'الزبيري - جامعة صنعاء', count: 45 },
    { route: 'السبعين - شارع هائل', count: 38 },
    { route: 'الحصبة - المطار', count: 32 },
    { route: 'الستين - التحرير', count: 28 },
    { route: 'الثورة - الزراعة', count: 25 },
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 2, border: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography key={index} variant="body2" sx={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          التحليلات والتقارير
        </Typography>
        <Typography variant="body1" color="text.secondary">
          تحليل شامل لأداء النظام والإحصائيات
        </Typography>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                1,250
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الرحلات
              </Typography>
              <Typography variant="caption" sx={{ color: 'success.main' }}>
                +12.5% من الشهر الماضي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                45,000
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإيرادات (ريال)
              </Typography>
              <Typography variant="caption" sx={{ color: 'success.main' }}>
                +8.3% من الشهر الماضي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                4.8
              </Typography>
              <Typography variant="body2" color="text.secondary">
                متوسط التقييم
              </Typography>
              <Typography variant="caption" sx={{ color: 'success.main' }}>
                +0.2 من الشهر الماضي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                85
              </Typography>
              <Typography variant="body2" color="text.secondary">
                السائقون النشطون
              </Typography>
              <Typography variant="caption" sx={{ color: 'success.main' }}>
                +5 من الشهر الماضي
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Daily Rides Chart */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                الرحلات اليومية (الأسبوع الحالي)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={dailyRidesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="rides" fill={theme.palette.primary.main} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Ride Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                توزيع حالات الرحلات
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={rideStatusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {rideStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Monthly Revenue */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                الإيرادات الشهرية
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`} />
                  <Tooltip 
                    content={<CustomTooltip />}
                    formatter={(value: any) => [`${value.toLocaleString()} ريال`, 'الإيرادات']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke={theme.palette.success.main}
                    strokeWidth={3}
                    dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Routes */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                أكثر المسارات طلباً
              </Typography>
              <Box sx={{ height: 300, overflow: 'auto' }}>
                {topRoutesData.map((route, index) => (
                  <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                      {route.route}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          width: `${(route.count / 50) * 100}%`,
                          height: 8,
                          bgcolor: theme.palette.primary.main,
                          borderRadius: 1,
                        }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {route.count} رحلة
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
