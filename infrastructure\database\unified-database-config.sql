-- TECNODRIVE Platform - Unified Database Configuration
-- This script creates all required databases with proper security settings
-- Created: 2025-01-14

-- Create a strong password for the main user
-- Password: TecnoDrive2025!Secure#Platform

-- Drop existing databases if they exist (for clean setup)
DROP DATABASE IF EXISTS tecnodrive_auth;
DROP DATABASE IF EXISTS tecnodrive_users;
DROP DATABASE IF EXISTS tecnodrive_rides;
DROP DATABASE IF EXISTS tecnodrive_fleet;
DROP DATABASE IF EXISTS tecnodrive_parcels;
DROP DATABASE IF EXISTS tecnodrive_payments;
DROP DATABASE IF EXISTS tecnodrive_notifications;
DROP DATABASE IF EXISTS tecnodrive_financial;
DROP DATABASE IF EXISTS tecnodrive_hr;
DROP DATABASE IF EXISTS tecnodrive_analytics;
DROP DATABASE IF EXISTS tecnodrive_saas;
DROP DATABASE IF EXISTS tecnodrive_location;
DROP DATABASE IF EXISTS tecnodrive_tracking;

-- Drop existing user if exists
DROP USER IF EXISTS tecnodrive_admin;

-- Create main database user with strong password
CREATE USER tecnodrive_admin WITH PASSWORD 'TecnoDrive2025!Secure#Platform';

-- Grant necessary privileges to the user
ALTER USER tecnodrive_admin CREATEDB;
ALTER USER tecnodrive_admin WITH SUPERUSER;

-- Create all service databases
CREATE DATABASE tecnodrive_auth OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_users OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_rides OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_fleet OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_parcels OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_payments OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_notifications OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_financial OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_hr OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_analytics OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_saas OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_location OWNER tecnodrive_admin;
CREATE DATABASE tecnodrive_tracking OWNER tecnodrive_admin;

-- Grant all privileges on databases to the user
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_auth TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_users TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_rides TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_fleet TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_parcels TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_payments TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_notifications TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_financial TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_hr TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_analytics TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_saas TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_location TO tecnodrive_admin;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_tracking TO tecnodrive_admin;

-- Enable PostGIS extension for location services
\c tecnodrive_location;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

\c tecnodrive_rides;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

\c tecnodrive_tracking;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- Create read-only user for reporting and analytics
\c postgres;
CREATE USER tecnodrive_readonly WITH PASSWORD 'TecnoDrive2025!ReadOnly#Access';

-- Grant read-only access to all databases
GRANT CONNECT ON DATABASE tecnodrive_auth TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_users TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_rides TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_fleet TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_parcels TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_payments TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_notifications TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_financial TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_hr TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_analytics TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_saas TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_location TO tecnodrive_readonly;
GRANT CONNECT ON DATABASE tecnodrive_tracking TO tecnodrive_readonly;

-- Grant usage on schemas and select on all tables (to be run after tables are created)
-- This will be handled by individual services

-- Create backup user
CREATE USER tecnodrive_backup WITH PASSWORD 'TecnoDrive2025!Backup#Secure';
ALTER USER tecnodrive_backup WITH REPLICATION;

-- Security settings
ALTER SYSTEM SET log_connections = 'on';
ALTER SYSTEM SET log_disconnections = 'on';
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Performance settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Reload configuration
SELECT pg_reload_conf();

-- Display created databases
\l

-- Display created users
\du
