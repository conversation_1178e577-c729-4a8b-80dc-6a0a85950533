package com.tecnodrive.userservice.repository;

import com.tecnodrive.userservice.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for User entity
 */
@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by phone number
     */
    Optional<User> findByPhoneNumber(String phoneNumber);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Check if phone number exists
     */
    boolean existsByPhoneNumber(String phoneNumber);

    /**
     * Find users by status
     */
    List<User> findByStatus(User.UserStatus status);

    /**
     * Find users by type
     */
    List<User> findByUserType(User.UserType userType);

    /**
     * Find users by company
     */
    List<User> findByCompanyId(UUID companyId);

    /**
     * Find users by status and type
     */
    Page<User> findByStatusAndUserType(User.UserStatus status, User.UserType userType, Pageable pageable);

    /**
     * Search users by name or email
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%'))")
    Page<User> searchUsers(@Param("search") String search, Pageable pageable);

    /**
     * Find users created between dates
     */
    List<User> findByCreatedAtBetween(Instant startDate, Instant endDate);

    /**
     * Find users who haven't logged in since a specific date
     */
    List<User> findByLastLoginAtBeforeOrLastLoginAtIsNull(Instant date);

    /**
     * Find users by city
     */
    List<User> findByCity(String city);

    /**
     * Find users by country
     */
    List<User> findByCountry(String country);

    /**
     * Count users by status
     */
    long countByStatus(User.UserStatus status);

    /**
     * Count users by type
     */
    long countByUserType(User.UserType userType);

    /**
     * Find users with email notifications enabled
     */
    List<User> findByEmailNotificationsTrue();

    /**
     * Find users with SMS notifications enabled
     */
    List<User> findBySmsNotificationsTrue();

    /**
     * Find verified users
     */
    List<User> findByEmailVerifiedTrueAndPhoneVerifiedTrue();

    /**
     * Find unverified users
     */
    List<User> findByEmailVerifiedFalseOrPhoneVerifiedFalse();

    /**
     * Advanced search with multiple filters
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:userType IS NULL OR u.userType = :userType) AND " +
           "(:companyId IS NULL OR u.companyId = :companyId) AND " +
           "(:city IS NULL OR LOWER(u.city) = LOWER(:city)) AND " +
           "(:country IS NULL OR LOWER(u.country) = LOWER(:country)) AND " +
           "(:search IS NULL OR " +
           " LOWER(u.firstName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           " LOWER(u.lastName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           " LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<User> findUsersWithFilters(
        @Param("status") User.UserStatus status,
        @Param("userType") User.UserType userType,
        @Param("companyId") UUID companyId,
        @Param("city") String city,
        @Param("country") String country,
        @Param("search") String search,
        Pageable pageable
    );
}
