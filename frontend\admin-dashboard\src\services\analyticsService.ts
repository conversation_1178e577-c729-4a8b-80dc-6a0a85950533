import { apiMethods, ApiResponse, handleApiError, SERVICE_URLS } from './api';
import { AnalyticsData } from '../store/slices/analyticsSlice';

export interface AnalyticsFilters {
  startDate?: string;
  endDate?: string;
  granularity?: 'daily' | 'weekly' | 'monthly';
  metrics?: string[];
}

export interface DashboardMetrics {
  totalRides: number;
  totalRevenue: number;
  activeDrivers: number;
  activePassengers: number;
  averageRating: number;
  ridesGrowth: number;
  revenueGrowth: number;
}

class AnalyticsService {
  private baseUrl = SERVICE_URLS.ANALYTICS_SERVICE;

  async getAnalytics(filters: AnalyticsFilters = {}): Promise<ApiResponse<AnalyticsData>> {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await apiMethods.get<ApiResponse<AnalyticsData>>(
        `${this.baseUrl}?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        return await mockApiResponses.getAnalytics(filters);
      }

      throw new Error(handleApiError(error));
    }
  }

  async getDashboardMetrics(): Promise<ApiResponse<DashboardMetrics>> {
    try {
      const response = await apiMethods.get<ApiResponse<DashboardMetrics>>(
        `${this.baseUrl}/dashboard`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getRidesAnalytics(startDate?: string, endDate?: string): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiMethods.get<ApiResponse<any>>(
        `${this.baseUrl}/rides?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getRevenueAnalytics(startDate?: string, endDate?: string): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiMethods.get<ApiResponse<any>>(
        `${this.baseUrl}/revenue?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getDriverPerformance(): Promise<ApiResponse<any[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<any[]>>(
        `${this.baseUrl}/drivers/performance`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getTopRoutes(): Promise<ApiResponse<any[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<any[]>>(
        `${this.baseUrl}/routes/top`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getFleetUtilization(): Promise<ApiResponse<any>> {
    try {
      const response = await apiMethods.get<ApiResponse<any>>(
        `${this.baseUrl}/fleet/utilization`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async exportReport(reportType: string, filters: AnalyticsFilters = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await apiMethods.get(
        `${this.baseUrl}/export/${reportType}?${params.toString()}`,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;
