-- TECNODRIVE Platform - Complete Database Schema Creation
-- This script creates all databases and their schemas
-- Run this script as PostgreSQL superuser

-- First, run the unified database configuration
\i database/unified-database-config.sql

-- Create the update_updated_at_column function in each database
-- This function is used by triggers to automatically update the updated_at column

\c tecnodrive_auth;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_users;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_rides;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_fleet;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_payments;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_notifications;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_financial;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_hr;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_analytics;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_saas;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_location;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_tracking;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

\c tecnodrive_parcels;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Now create all the schemas
\echo 'Creating Auth Service Schema...'
\i database/schemas/auth-service-schema.sql

\echo 'Creating User Service Schema...'
\i database/schemas/user-service-schema.sql

\echo 'Creating Ride Service Schema...'
\i database/schemas/ride-service-schema.sql

\echo 'Creating Fleet Service Schema...'
\i database/schemas/fleet-service-schema.sql

\echo 'Creating Payment Service Schema...'
\i database/schemas/payment-service-schema.sql

-- Grant read-only access to readonly user for all schemas
\echo 'Granting read-only access...'

\c tecnodrive_auth;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_users;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_rides;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_fleet;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_payments;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_notifications;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_financial;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_hr;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_analytics;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_saas;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_location;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_tracking;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\c tecnodrive_parcels;
GRANT USAGE ON SCHEMA public TO tecnodrive_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_readonly;

\echo 'All database schemas created successfully!'
\echo 'Database setup complete.'
