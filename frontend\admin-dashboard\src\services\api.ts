import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Mock mode configuration
export const MOCK_MODE = process.env.REACT_APP_ENABLE_MOCK_DATA === 'true' || process.env.NODE_ENV === 'development';

// Service URLs - Updated to match actual backend paths
export const SERVICE_URLS = {
  API_GATEWAY: API_CONFIG.BASE_URL,
  RIDE_SERVICE: process.env.REACT_APP_RIDE_SERVICE_URL
    ? `${process.env.REACT_APP_RIDE_SERVICE_URL}/api/rides`
    : `${API_CONFIG.BASE_URL}/api/rides`,
  FLEET_SERVICE: process.env.REACT_APP_FLEET_SERVICE_URL
    ? `${process.env.REACT_APP_FLEET_SERVICE_URL}/api/fleet/vehicles`
    : `${API_CONFIG.BASE_URL}/api/fleet/vehicles`,
  AUTH_SERVICE: process.env.REACT_APP_AUTH_SERVICE_URL
    ? `${process.env.REACT_APP_AUTH_SERVICE_URL}/api/auth`
    : `${API_CONFIG.BASE_URL}/api/auth`,
  PAYMENT_SERVICE: process.env.REACT_APP_PAYMENT_SERVICE_URL
    ? `${process.env.REACT_APP_PAYMENT_SERVICE_URL}/api/payments`
    : `${API_CONFIG.BASE_URL}/api/payments`,
  ANALYTICS_SERVICE: process.env.REACT_APP_ANALYTICS_SERVICE_URL
    ? `${process.env.REACT_APP_ANALYTICS_SERVICE_URL}/api/analytics/events`
    : `${API_CONFIG.BASE_URL}/api/analytics/events`,
  NOTIFICATION_SERVICE: process.env.REACT_APP_NOTIFICATION_SERVICE_URL
    ? `${process.env.REACT_APP_NOTIFICATION_SERVICE_URL}/api/notifications`
    : `${API_CONFIG.BASE_URL}/api/notifications`,
  HR_SERVICE: process.env.REACT_APP_HR_SERVICE_URL
    ? `${process.env.REACT_APP_HR_SERVICE_URL}/api/hr/employees`
    : `${API_CONFIG.BASE_URL}/api/hr/employees`,
  PARCEL_SERVICE: process.env.REACT_APP_PARCEL_SERVICE_URL
    ? `${process.env.REACT_APP_PARCEL_SERVICE_URL}/api/parcels`
    : `${API_CONFIG.BASE_URL}/api/parcels`,
  LOCATION_SERVICE: process.env.REACT_APP_LOCATION_SERVICE_URL
    ? `${process.env.REACT_APP_LOCATION_SERVICE_URL}/api/location/logs`
    : `${API_CONFIG.BASE_URL}/api/location/logs`,
  FINANCE_SERVICE: process.env.REACT_APP_FINANCE_SERVICE_URL
    ? `${process.env.REACT_APP_FINANCE_SERVICE_URL}/api/finance/invoices`
    : `${API_CONFIG.BASE_URL}/api/finance/invoices`,
  SAAS_SERVICE: process.env.REACT_APP_SAAS_SERVICE_URL
    ? `${process.env.REACT_APP_SAAS_SERVICE_URL}/api/saas/tenants`
    : `${API_CONFIG.BASE_URL}/api/saas/tenants`,
  AUDIT_SERVICE: process.env.REACT_APP_AUDIT_SERVICE_URL
    ? `${process.env.REACT_APP_AUDIT_SERVICE_URL}/api/audit/logs`
    : `${API_CONFIG.BASE_URL}/api/audit/logs`,
  USER_SERVICE: process.env.REACT_APP_USER_SERVICE_URL
    ? `${process.env.REACT_APP_USER_SERVICE_URL}/api/users`
    : `${API_CONFIG.BASE_URL}/api/users`,
};

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      // Add auth token if available
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      // Log request in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });
      }
      
      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log response in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data,
        });
      }
      
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 Unauthorized
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        // Clear token and redirect to login
        localStorage.removeItem('token');
        window.location.href = '/login';
        
        return Promise.reject(error);
      }

      // Handle network errors with retry
      if (!error.response && originalRequest._retryCount < API_CONFIG.RETRY_ATTEMPTS) {
        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
        
        await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY_DELAY));
        
        return instance(originalRequest);
      }

      // Log error in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
          status: error.response?.status,
          message: error.message,
          data: error.response?.data,
        });
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create API instance
export const api = createApiInstance();

// Generic API methods
export const apiMethods = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.get(url, config),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.post(url, data, config),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.put(url, data, config),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.patch(url, data, config),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.delete(url, config),
};

// Response wrapper type
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  total?: number;
  page?: number;
  limit?: number;
}

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'حدث خطأ غير متوقع';
};

// Health check utility
export const checkServiceHealth = async (serviceName: string, url: string): Promise<boolean> => {
  try {
    const response = await api.get(`${url}/health`);
    return response.status === 200;
  } catch (error) {
    console.warn(`⚠️ Service ${serviceName} is not available`);
    return false;
  }
};

// Mock utilities for development
export const simulateApiDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const createMockResponse = <T>(data: T, delay: number = 1000): Promise<ApiResponse<T>> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        success: true,
        data,
        message: 'Success'
      });
    }, delay);
  });
};

export const generateMockId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export default api;
