import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Code as CodeIcon,
  GraphicEq as GraphQLIcon,
  PlayArrow as PlayIcon,
  Description as SchemaIcon,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Speed as PerformanceIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';

interface GraphQLSchema {
  id: string;
  name: string;
  service: string;
  version: string;
  schema: string;
  enabled: boolean;
  lastUpdated: string;
}

interface GraphQLQuery {
  id: string;
  name: string;
  query: string;
  variables?: Record<string, any>;
  description: string;
  createdAt: string;
}

const GraphQLGateway: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [schemas, setSchemas] = useState<GraphQLSchema[]>([]);
  const [queries, setQueries] = useState<GraphQLQuery[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [openSchemaDialog, setOpenSchemaDialog] = useState(false);
  const [openQueryDialog, setOpenQueryDialog] = useState(false);
  const [queryResult, setQueryResult] = useState<any>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [currentQuery, setCurrentQuery] = useState('');
  const [currentVariables, setCurrentVariables] = useState('{}');

  // Mock GraphQL schemas
  const mockSchemas: GraphQLSchema[] = [
    {
      id: 'schema-auth',
      name: 'Authentication Service',
      service: 'auth-service',
      version: 'v1.0.0',
      schema: `
type User {
  id: ID!
  username: String!
  email: String!
  role: Role!
  createdAt: DateTime!
}

type Role {
  id: ID!
  name: String!
  permissions: [Permission!]!
}

type Query {
  user(id: ID!): User
  users(limit: Int, offset: Int): [User!]!
}

type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
}
      `,
      enabled: true,
      lastUpdated: '2025-07-09T14:30:00Z',
    },
    {
      id: 'schema-fleet',
      name: 'Fleet Management Service',
      service: 'fleet-service',
      version: 'v1.0.0',
      schema: `
type Vehicle {
  id: ID!
  plateNumber: String!
  make: String!
  model: String!
  year: Int!
  status: VehicleStatus!
  location: Location
  driver: Driver
}

type Location {
  latitude: Float!
  longitude: Float!
  address: String
  timestamp: DateTime!
}

type Query {
  vehicle(id: ID!): Vehicle
  vehicles(status: VehicleStatus): [Vehicle!]!
  nearbyVehicles(lat: Float!, lng: Float!, radius: Float!): [Vehicle!]!
}
      `,
      enabled: true,
      lastUpdated: '2025-07-09T14:30:00Z',
    },
    {
      id: 'schema-rides',
      name: 'Ride Service',
      service: 'rides-service',
      version: 'v1.0.0',
      schema: `
type Ride {
  id: ID!
  passenger: User!
  driver: Driver
  vehicle: Vehicle
  pickupLocation: Location!
  dropoffLocation: Location!
  status: RideStatus!
  fare: Float
  createdAt: DateTime!
}

type Query {
  ride(id: ID!): Ride
  rides(status: RideStatus, userId: ID): [Ride!]!
  activeRides: [Ride!]!
}

type Mutation {
  requestRide(input: RideRequestInput!): Ride!
  acceptRide(rideId: ID!, driverId: ID!): Ride!
  completeRide(rideId: ID!): Ride!
}
      `,
      enabled: true,
      lastUpdated: '2025-07-09T14:30:00Z',
    },
  ];

  // Mock saved queries
  const mockQueries: GraphQLQuery[] = [
    {
      id: 'query-1',
      name: 'Get Active Vehicles',
      query: `
query GetActiveVehicles {
  vehicles(status: ACTIVE) {
    id
    plateNumber
    make
    model
    location {
      latitude
      longitude
      address
    }
    driver {
      name
      phone
    }
  }
}
      `,
      description: 'استرداد جميع المركبات النشطة مع مواقعها',
      createdAt: '2025-07-09T10:00:00Z',
    },
    {
      id: 'query-2',
      name: 'Get User Rides',
      query: `
query GetUserRides($userId: ID!, $status: RideStatus) {
  rides(userId: $userId, status: $status) {
    id
    pickupLocation {
      address
    }
    dropoffLocation {
      address
    }
    status
    fare
    createdAt
  }
}
      `,
      variables: { userId: "user-123", status: "COMPLETED" },
      description: 'استرداد رحلات مستخدم معين',
      createdAt: '2025-07-09T11:00:00Z',
    },
  ];

  useEffect(() => {
    setSchemas(mockSchemas);
    setQueries(mockQueries);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleExecuteQuery = async () => {
    try {
      setLoading(true);
      
      // Mock GraphQL execution
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockResult = {
        data: {
          vehicles: [
            {
              id: "vehicle-1",
              plateNumber: "أ ب ج 123",
              make: "تويوتا",
              model: "كامري",
              location: {
                latitude: 15.3694,
                longitude: 44.1910,
                address: "شارع الزبيري، صنعاء"
              },
              driver: {
                name: "علي أحمد",
                phone: "+967-777-123456"
              }
            }
          ]
        }
      };
      
      setQueryResult(mockResult);
      setSnackbarMessage('تم تنفيذ الاستعلام بنجاح');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error executing query:', error);
      setSnackbarMessage('خطأ في تنفيذ الاستعلام');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveQuery = () => {
    const newQuery: GraphQLQuery = {
      id: `query-${Date.now()}`,
      name: `Query ${queries.length + 1}`,
      query: currentQuery,
      variables: currentVariables ? JSON.parse(currentVariables) : undefined,
      description: 'استعلام محفوظ',
      createdAt: new Date().toISOString(),
    };

    setQueries(prev => [...prev, newQuery]);
    setSnackbarMessage('تم حفظ الاستعلام');
    setSnackbarOpen(true);
  };

  const schemaColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم المخطط',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <SchemaIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'service',
      headerName: 'الخدمة',
      width: 150,
    },
    {
      field: 'version',
      headerName: 'الإصدار',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" color="info" variant="outlined" />
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'مفعل' : 'معطل'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'lastUpdated',
      headerName: 'آخر تحديث',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
  ];

  const queryColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'اسم الاستعلام',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'success.main' }}>
            <CodeIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'description',
      headerName: 'الوصف',
      width: 300,
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الإنشاء',
      width: 150,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Tooltip title="تحميل"><PlayIcon /></Tooltip>}
          label="تحميل"
          onClick={() => {
            setCurrentQuery(params.row.query);
            setCurrentVariables(JSON.stringify(params.row.variables || {}, null, 2));
            setTabValue(2); // Switch to playground tab
          }}
        />,
      ],
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          GraphQL Gateway
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة وتشغيل GraphQL Federation وBFF Patterns
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {schemas.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مخططات GraphQL
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {schemas.filter(s => s.enabled).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المخططات المفعلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {queries.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الاستعلامات المحفوظة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                98.5%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                معدل التوفر
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<SchemaIcon />} label="المخططات" iconPosition="start" />
            <Tab icon={<CodeIcon />} label="الاستعلامات المحفوظة" iconPosition="start" />
            <Tab icon={<PlayIcon />} label="ساحة التجريب" iconPosition="start" />
            <Tab icon={<PerformanceIcon />} label="الأداء" iconPosition="start" />
          </Tabs>
        </Box>

        {/* Schemas Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">مخططات GraphQL</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenSchemaDialog(true)}
              >
                إضافة مخطط
              </Button>
            </Box>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={schemas}
                columns={schemaColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* Saved Queries Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>الاستعلامات المحفوظة</Typography>

            <Box sx={{ height: 400, width: '100%' }}>
              <DataGrid
                rows={queries}
                columns={queryColumns}
                loading={loading}
                pageSizeOptions={[10, 25, 50]}
                disableRowSelectionOnClick
                sx={{
                  border: 0,
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                }}
              />
            </Box>
          </Box>
        )}

        {/* GraphQL Playground Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>ساحة تجريب GraphQL</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>الاستعلام</Typography>
                  <TextField
                    multiline
                    rows={12}
                    value={currentQuery}
                    onChange={(e) => setCurrentQuery(e.target.value)}
                    fullWidth
                    placeholder="اكتب استعلام GraphQL هنا..."
                    sx={{ fontFamily: 'monospace' }}
                  />
                </Paper>
                
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>المتغيرات (JSON)</Typography>
                  <TextField
                    multiline
                    rows={4}
                    value={currentVariables}
                    onChange={(e) => setCurrentVariables(e.target.value)}
                    fullWidth
                    placeholder='{"userId": "123"}'
                    sx={{ fontFamily: 'monospace' }}
                  />
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1">النتيجة</Typography>
                    <Box>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={handleSaveQuery}
                        sx={{ mr: 1 }}
                      >
                        حفظ
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={handleExecuteQuery}
                        disabled={loading}
                        startIcon={<PlayIcon />}
                      >
                        تنفيذ
                      </Button>
                    </Box>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: 'grey.50', height: 400, overflow: 'auto' }}>
                    <pre style={{ margin: 0, fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {queryResult ? JSON.stringify(queryResult, null, 2) : 'لم يتم تنفيذ أي استعلام بعد'}
                    </pre>
                  </Paper>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Performance Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>مراقبة الأداء</Typography>
            <Alert severity="info">
              قريباً - إحصائيات الأداء ومراقبة استعلامات GraphQL
            </Alert>
          </Box>
        )}
      </Card>

      {/* Schema Dialog */}
      <Dialog open={openSchemaDialog} onClose={() => setOpenSchemaDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة مخطط GraphQL جديد</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            قريباً - واجهة لإضافة وتحرير مخططات GraphQL
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenSchemaDialog(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default GraphQLGateway;
