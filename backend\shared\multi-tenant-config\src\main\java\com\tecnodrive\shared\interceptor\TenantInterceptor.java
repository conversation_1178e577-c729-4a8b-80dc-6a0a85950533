package com.tecnodrive.shared.interceptor;

import com.tecnodrive.shared.config.TenantContext;
import com.tecnodrive.shared.service.TenantResolver;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor to extract and set tenant context from HTTP requests
 * Supports multiple tenant identification methods
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TenantInterceptor implements HandlerInterceptor {

    private final TenantResolver tenantResolver;

    private static final String TENANT_HEADER = "X-Tenant-ID";
    private static final String TENANT_PARAM = "tenantId";
    private static final String USER_HEADER = "X-User-ID";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // Skip tenant resolution for health checks and public endpoints
            if (isPublicEndpoint(request.getRequestURI())) {
                log.debug("Skipping tenant resolution for public endpoint: {}", request.getRequestURI());
                return true;
            }

            // Extract tenant ID from request
            String tenantId = extractTenantId(request);
            
            if (tenantId == null || tenantId.trim().isEmpty()) {
                log.warn("No tenant ID found in request to: {}", request.getRequestURI());
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Tenant ID is required\"}");
                return false;
            }

            // Validate and resolve tenant
            if (!tenantResolver.isValidTenant(tenantId)) {
                log.warn("Invalid tenant ID: {} for request to: {}", tenantId, request.getRequestURI());
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.getWriter().write("{\"error\":\"Invalid or inactive tenant\"}");
                return false;
            }

            // Set tenant context
            TenantContext.setCurrentTenant(tenantId);
            
            // Resolve and set database URL
            String databaseUrl = tenantResolver.getTenantDatabaseUrl(tenantId);
            if (databaseUrl == null) {
                log.error("No database URL found for tenant: {}", tenantId);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\":\"Tenant database not available\"}");
                return false;
            }
            TenantContext.setCurrentDatabaseUrl(databaseUrl);

            // Extract and set user context if available
            String userId = extractUserId(request);
            if (userId != null) {
                TenantContext.setCurrentUser(userId);
            }

            log.debug("Tenant context set: {}", TenantContext.getContextInfo());
            return true;

        } catch (Exception e) {
            log.error("Error in tenant interceptor: {}", e.getMessage(), e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\":\"Internal server error\"}");
            } catch (Exception ex) {
                log.error("Error writing error response: {}", ex.getMessage());
            }
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        // Clear tenant context after request completion
        TenantContext.clear();
        log.debug("Cleared tenant context for request: {}", request.getRequestURI());
    }

    /**
     * Extract tenant ID from various sources in order of priority:
     * 1. HTTP Header (X-Tenant-ID)
     * 2. Request Parameter (tenantId)
     * 3. Subdomain (tenant.domain.com)
     * 4. JWT Token (if present)
     */
    private String extractTenantId(HttpServletRequest request) {
        // 1. Check HTTP header
        String tenantId = request.getHeader(TENANT_HEADER);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            log.debug("Tenant ID found in header: {}", tenantId);
            return tenantId.trim();
        }

        // 2. Check request parameter
        tenantId = request.getParameter(TENANT_PARAM);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            log.debug("Tenant ID found in parameter: {}", tenantId);
            return tenantId.trim();
        }

        // 3. Check subdomain
        tenantId = extractTenantFromSubdomain(request);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            log.debug("Tenant ID found in subdomain: {}", tenantId);
            return tenantId.trim();
        }

        // 4. Check JWT token (if Authorization header is present)
        tenantId = extractTenantFromJWT(request);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            log.debug("Tenant ID found in JWT: {}", tenantId);
            return tenantId.trim();
        }

        log.debug("No tenant ID found in request");
        return null;
    }

    /**
     * Extract user ID from request headers
     */
    private String extractUserId(HttpServletRequest request) {
        String userId = request.getHeader(USER_HEADER);
        if (userId != null && !userId.trim().isEmpty()) {
            return userId.trim();
        }
        return null;
    }

    /**
     * Extract tenant from subdomain (e.g., tenant1.tecnodrive.com)
     */
    private String extractTenantFromSubdomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (serverName != null && serverName.contains(".")) {
            String[] parts = serverName.split("\\.");
            if (parts.length >= 3) { // tenant.domain.com
                String potentialTenant = parts[0];
                // Validate it's not a common subdomain
                if (!isCommonSubdomain(potentialTenant)) {
                    return potentialTenant;
                }
            }
        }
        return null;
    }

    /**
     * Extract tenant from JWT token
     */
    private String extractTenantFromJWT(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // TODO: Implement JWT parsing to extract tenant claim
            // This would require JWT library and proper token validation
            log.debug("JWT token present but tenant extraction not implemented");
        }
        return null;
    }

    /**
     * Check if the endpoint is public and doesn't require tenant context
     */
    private boolean isPublicEndpoint(String uri) {
        return uri.startsWith("/actuator/") ||
               uri.startsWith("/health") ||
               uri.startsWith("/info") ||
               uri.startsWith("/metrics") ||
               uri.startsWith("/swagger") ||
               uri.startsWith("/api-docs") ||
               uri.equals("/") ||
               uri.startsWith("/public/") ||
               uri.startsWith("/auth/login") ||
               uri.startsWith("/auth/register");
    }

    /**
     * Check if subdomain is a common one (not a tenant)
     */
    private boolean isCommonSubdomain(String subdomain) {
        return "www".equals(subdomain) ||
               "api".equals(subdomain) ||
               "admin".equals(subdomain) ||
               "app".equals(subdomain) ||
               "portal".equals(subdomain) ||
               "dashboard".equals(subdomain);
    }
}
