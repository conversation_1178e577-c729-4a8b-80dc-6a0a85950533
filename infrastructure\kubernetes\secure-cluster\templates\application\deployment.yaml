{{- if .Values.application.tecnoDrive.apiGateway.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tecno-drive-api-gateway
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "3"
    deployment.kubernetes.io/revision: "1"
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/component: gateway
    tecno-drive.com/service: api-gateway
    tecno-drive.com/tier: infrastructure
spec:
  replicas: {{ .Values.application.tecnoDrive.apiGateway.replicas | default 3 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: api-gateway
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/version: {{ .Chart.AppVersion }}
        tecno-drive.com/service: api-gateway
        tecno-drive.com/tier: infrastructure
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
        config.linkerd.io/proxy-cpu-limit: "0.5"
        config.linkerd.io/proxy-memory-limit: "512Mi"
    spec:
      serviceAccountName: tecno-drive-api-gateway
      securityContext:
        runAsUser: {{ .Values.global.securityContext.runAsUser | default 1000 }}
        runAsGroup: {{ .Values.global.securityContext.runAsGroup | default 3000 }}
        runAsNonRoot: {{ .Values.global.securityContext.runAsNonRoot | default true }}
        fsGroup: 3000
        seccompProfile:
          type: {{ .Values.global.securityContext.seccompProfile.type | default "RuntimeDefault" }}
      containers:
        - name: api-gateway
          image: "{{ .Values.global.imageRegistry }}/{{ .Values.application.tecnoDrive.apiGateway.image }}:{{ .Values.application.common.imageTag }}"
          imagePullPolicy: {{ .Values.application.common.imagePullPolicy | default "Always" }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: management
              containerPort: 8081
              protocol: TCP
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "kubernetes,{{ .Values.global.environment }}"
            - name: EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE
              value: "http://eureka-service:8761/eureka/"
            - name: MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE
              value: "health,info,prometheus,metrics"
            - name: MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS
              value: "always"
            - name: JAVA_OPTS
              value: >-
                -Xms{{ .Values.application.tecnoDrive.apiGateway.resources.requests.memory | default "256Mi" }}
                -Xmx{{ .Values.application.tecnoDrive.apiGateway.resources.limits.memory | default "1Gi" }}
                -XX:+UseG1GC
                -XX:+UseContainerSupport
                -XX:MaxRAMPercentage=75.0
                -Djava.security.egd=file:/dev/./urandom
                -Dspring.backgroundpreinitializer.ignore=true
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 3000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop: ["ALL"]
          resources:
            requests:
              memory: {{ .Values.application.tecnoDrive.apiGateway.resources.requests.memory | default "256Mi" }}
              cpu: {{ .Values.application.tecnoDrive.apiGateway.resources.requests.cpu | default "200m" }}
            limits:
              memory: {{ .Values.application.tecnoDrive.apiGateway.resources.limits.memory | default "1Gi" }}
              cpu: {{ .Values.application.tecnoDrive.apiGateway.resources.limits.cpu | default "1000m" }}
          livenessProbe:
            httpGet:
              path: {{ .Values.application.common.probes.liveness.path | default "/actuator/health" }}
              port: {{ .Values.application.common.probes.liveness.port | default 8080 }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.application.common.probes.liveness.initialDelaySeconds | default 30 }}
            periodSeconds: {{ .Values.application.common.probes.liveness.periodSeconds | default 10 }}
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: {{ .Values.application.common.probes.readiness.path | default "/actuator/health/readiness" }}
              port: {{ .Values.application.common.probes.readiness.port | default 8080 }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.application.common.probes.readiness.initialDelaySeconds | default 5 }}
            periodSeconds: {{ .Values.application.common.probes.readiness.periodSeconds | default 5 }}
            timeoutSeconds: 3
            failureThreshold: 3
            successThreshold: 1
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
            successThreshold: 1
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: cache
              mountPath: /app/cache
            - name: logs
              mountPath: /app/logs
      volumes:
        - name: tmp
          emptyDir: {}
        - name: cache
          emptyDir: {}
        - name: logs
          emptyDir: {}
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: tecno-drive-api-gateway
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
  labels:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: gateway
    tecno-drive.com/service: api-gateway
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 80
      targetPort: 8080
      protocol: TCP
    - name: management
      port: 8081
      targetPort: 8081
      protocol: TCP
  selector:
    app.kubernetes.io/name: api-gateway
    app.kubernetes.io/instance: {{ .Release.Name }}

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tecno-drive-api-gateway
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: api-gateway-serviceaccount
    app.kubernetes.io/instance: {{ .Release.Name }}
automountServiceAccountToken: true

---
{{- if .Values.application.common.hpa.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tecno-drive-api-gateway-hpa
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "3"
  labels:
    app.kubernetes.io/name: api-gateway-hpa
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tecno-drive-api-gateway
  minReplicas: {{ .Values.application.common.hpa.minReplicas | default 2 }}
  maxReplicas: {{ .Values.application.common.hpa.maxReplicas | default 10 }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.application.common.hpa.targetCPUUtilizationPercentage | default 70 }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.application.common.hpa.targetMemoryUtilizationPercentage | default 80 }}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60
        - type: Pods
          value: 2
          periodSeconds: 60
      selectPolicy: Max
{{- end }}

---
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tecno-drive-api-gateway
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "3"
  labels:
    app.kubernetes.io/name: api-gateway-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: api-gateway
      app.kubernetes.io/instance: {{ .Release.Name }}
  endpoints:
    - port: management
      path: /actuator/prometheus
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
{{- end }}
