package com.tecnodrive.saasservice.service;

import com.tecnodrive.saasservice.dto.TenantRequest;
import com.tecnodrive.saasservice.dto.TenantResponse;
import com.tecnodrive.saasservice.dto.TenantUpdateRequest;
import com.tecnodrive.saasservice.entity.Tenant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Tenant Service Interface
 * 
 * Defines business logic operations for tenant management
 */
public interface TenantService {

    /**
     * Create a new tenant
     */
    TenantResponse createTenant(TenantRequest request);

    /**
     * Get all tenants
     */
    List<TenantResponse> getAllTenants();

    /**
     * Get tenants with pagination
     */
    Page<TenantResponse> getTenants(Pageable pageable);

    /**
     * Get tenant by ID
     */
    TenantResponse getTenant(String id);

    /**
     * Get tenant by name
     */
    TenantResponse getTenantByName(String name);

    /**
     * Update tenant
     */
    TenantResponse updateTenant(String id, TenantUpdateRequest request);

    /**
     * Delete tenant
     */
    void deleteTenant(String id);

    /**
     * Get tenants by type
     */
    List<TenantResponse> getTenantsByType(Tenant.TenantType type);

    /**
     * Get tenants by status
     */
    List<TenantResponse> getTenantsByStatus(Tenant.TenantStatus status);

    /**
     * Get active tenants
     */
    List<TenantResponse> getActiveTenants();

    /**
     * Search tenants
     */
    Page<TenantResponse> searchTenants(String searchTerm, Pageable pageable);

    /**
     * Activate tenant
     */
    TenantResponse activateTenant(String id);

    /**
     * Deactivate tenant
     */
    TenantResponse deactivateTenant(String id);

    /**
     * Suspend tenant
     */
    TenantResponse suspendTenant(String id);

    /**
     * Extend subscription
     */
    TenantResponse extendSubscription(String id, Instant newEndDate);

    /**
     * Get tenants with expiring subscriptions
     */
    List<TenantResponse> getTenantsWithExpiringSubscriptions(int daysAhead);

    /**
     * Get expired tenants
     */
    List<TenantResponse> getExpiredTenants();

    /**
     * Update tenant branding
     */
    TenantResponse updateTenantBranding(String id, String brandingConfig);

    /**
     * Update tenant feature flags
     */
    TenantResponse updateTenantFeatures(String id, String featureFlags);

    /**
     * Get tenant analytics
     */
    TenantAnalytics getTenantAnalytics();

    /**
     * Check tenant name availability
     */
    boolean isTenantNameAvailable(String name);

    /**
     * Tenant Analytics DTO
     */
    record TenantAnalytics(
            long totalTenants,
            long activeTenants,
            long inactiveTenants,
            long suspendedTenants,
            long trialTenants,
            long expiredTenants,
            Map<String, Long> tenantsByType,
            Map<String, Long> tenantsByServiceType,
            long tenantsCreatedThisMonth,
            long tenantsExpiringThisMonth
    ) {}
}
