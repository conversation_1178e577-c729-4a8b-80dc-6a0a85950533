import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  color: string;
  status: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE';
  driverId?: string;
  driverName?: string;
  lastLocation?: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface FleetState {
  vehicles: Vehicle[];
  currentVehicle: Vehicle | null;
  loading: boolean;
  error: string | null;
  totalVehicles: number;
  activeVehicles: number;
  maintenanceVehicles: number;
}

const initialState: FleetState = {
  vehicles: [],
  currentVehicle: null,
  loading: false,
  error: null,
  totalVehicles: 0,
  activeVehicles: 0,
  maintenanceVehicles: 0,
};

// Async thunks
export const fetchVehicles = createAsyncThunk(
  'fleet/fetchVehicles',
  async (params?: { page?: number; limit?: number; status?: string }) => {
    const { fleetService } = await import('../../services/fleetService');
    const response = await fleetService.getVehicles(params);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب المركبات');
    }

    return response;
  }
);

export const createVehicle = createAsyncThunk(
  'fleet/createVehicle',
  async (vehicleData: Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'>) => {
    const { fleetService } = await import('../../services/fleetService');
    const response = await fleetService.createVehicle(vehicleData);

    if (!response.success) {
      throw new Error(response.message || 'فشل في إضافة المركبة');
    }

    return response;
  }
);

const fleetSlice = createSlice({
  name: 'fleet',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchVehicles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicles.fulfilled, (state, action) => {
        state.loading = false;
        state.vehicles = action.payload.data;
        state.totalVehicles = action.payload.total || action.payload.data.length;
        state.activeVehicles = action.payload.data.filter((v: Vehicle) => v.status === 'ACTIVE').length;
        state.maintenanceVehicles = action.payload.data.filter((v: Vehicle) => v.status === 'MAINTENANCE').length;
      })
      .addCase(fetchVehicles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب المركبات';
      })
      .addCase(createVehicle.fulfilled, (state, action) => {
        state.vehicles.push(action.payload.data);
        state.totalVehicles += 1;
      });
  },
});

export const { clearError } = fleetSlice.actions;
export default fleetSlice.reducer;
