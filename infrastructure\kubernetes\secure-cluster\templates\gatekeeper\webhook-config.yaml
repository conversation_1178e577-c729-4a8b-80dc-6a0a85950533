{{- if .Values.gatekeeper.enabled }}
# Advanced Gatekeeper Webhook Configuration with Auto CA Injection
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhookConfiguration
metadata:
  name: gatekeeper-validating-webhook-configuration
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
    cert-manager.io/inject-ca-from: "{{ .Release.Namespace }}/gatekeeper-webhook-cert"
    description: "TECNO DRIVE Advanced Gatekeeper Webhook with Real-time Policy Validation"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    gatekeeper.sh/system: "yes"
    tecno-drive.com/component: "security-webhook"
webhooks:
  - name: policy-validation.gatekeeper.sh
    clientConfig:
      service:
        name: gatekeeper-webhook-service
        namespace: {{ .Release.Namespace }}
        path: "/v1/admit"
        port: 443
      # caBundle will be injected automatically by cert-manager
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: ["*"]
        apiVersions: ["*"]
        resources: ["*"]
    namespaceSelector:
      matchExpressions:
        - key: admission.gatekeeper.sh/ignore
          operator: DoesNotExist
        - key: name
          operator: NotIn
          values: 
            - "gatekeeper-system"
            - "kube-system"
            - "kube-public"
            - "kube-node-lease"
            {{- if .Values.gatekeeper.exemptNamespaces }}
            {{- range .Values.gatekeeper.exemptNamespaces }}
            - {{ . | quote }}
            {{- end }}
            {{- end }}
    failurePolicy: {{ .Values.gatekeeper.failurePolicy | default "Ignore" }}
    sideEffects: None
    admissionReviewVersions: ["v1", "v1beta1"]
    timeoutSeconds: {{ .Values.gatekeeper.timeoutSeconds | default 10 }}
    
  - name: mutation.gatekeeper.sh
    clientConfig:
      service:
        name: gatekeeper-webhook-service
        namespace: {{ .Release.Namespace }}
        path: "/v1/mutate"
        port: 443
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: ["*"]
        apiVersions: ["*"]
        resources: ["*"]
    namespaceSelector:
      matchExpressions:
        - key: admission.gatekeeper.sh/ignore
          operator: DoesNotExist
        - key: name
          operator: NotIn
          values: 
            - "gatekeeper-system"
            - "kube-system"
            - "kube-public"
            - "kube-node-lease"
    failurePolicy: {{ .Values.gatekeeper.mutation.failurePolicy | default "Ignore" }}
    sideEffects: None
    admissionReviewVersions: ["v1", "v1beta1"]
    timeoutSeconds: {{ .Values.gatekeeper.mutation.timeoutSeconds | default 5 }}

---
# Gatekeeper Webhook Service
apiVersion: v1
kind: Service
metadata:
  name: gatekeeper-webhook-service
  namespace: {{ .Release.Namespace }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8888"
    prometheus.io/path: "/metrics"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook-service
    app.kubernetes.io/instance: {{ .Release.Name }}
    control-plane: controller-manager
    gatekeeper.sh/system: "yes"
    tecno-drive.com/component: "security-webhook"
spec:
  type: ClusterIP
  ports:
    - name: webhook-server
      port: 443
      targetPort: 8443
      protocol: TCP
    - name: metrics
      port: 8888
      targetPort: 8888
      protocol: TCP
    - name: healthz
      port: 9440
      targetPort: 9440
      protocol: TCP
  selector:
    control-plane: controller-manager
    gatekeeper.sh/operation: webhook
    gatekeeper.sh/system: "yes"

---
# Certificate for Webhook TLS
{{- if .Values.certManager.enabled }}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: gatekeeper-webhook-cert
  namespace: {{ .Release.Namespace }}
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook-cert
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "security-certificate"
spec:
  secretName: gatekeeper-webhook-tls
  duration: {{ .Values.certManager.duration | default "8760h" }} # 1 year
  renewBefore: {{ .Values.certManager.renewBefore | default "720h" }} # 30 days
  subject:
    organizations:
      - "TECNO DRIVE Security"
    organizationalUnits:
      - "Platform Security"
    countries:
      - "SA"
    localities:
      - "Riyadh"
  issuerRef:
    name: {{ .Values.certManager.issuerName | default "gatekeeper-ca-issuer" }}
    kind: {{ .Values.certManager.issuerKind | default "ClusterIssuer" }}
  dnsNames:
    - gatekeeper-webhook-service
    - gatekeeper-webhook-service.{{ .Release.Namespace }}
    - gatekeeper-webhook-service.{{ .Release.Namespace }}.svc
    - gatekeeper-webhook-service.{{ .Release.Namespace }}.svc.cluster.local
  ipAddresses:
    - 127.0.0.1
  usages:
    - digital signature
    - key encipherment
    - server auth
  privateKey:
    algorithm: RSA
    size: 2048
    rotationPolicy: Always

---
# ClusterIssuer for Self-Signed Certificates
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: gatekeeper-ca-issuer
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
  labels:
    app.kubernetes.io/name: gatekeeper-ca-issuer
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "security-ca"
spec:
  selfSigned: {}
{{- end }}

---
# ServiceMonitor for Webhook Metrics
{{- if .Values.monitoring.prometheus.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gatekeeper-webhook-metrics
  namespace: {{ .Release.Namespace }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook-servicemonitor
    app.kubernetes.io/instance: {{ .Release.Name }}
    control-plane: controller-manager
    gatekeeper.sh/system: "yes"
    tecno-drive.com/component: "security-monitoring"
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
      gatekeeper.sh/system: "yes"
  endpoints:
    - port: metrics
      interval: 30s
      path: /metrics
      scheme: http
      scrapeTimeout: 10s
      metricRelabelings:
        - sourceLabels: [__name__]
          regex: "gatekeeper_.*"
          action: keep
        - sourceLabels: [__name__]
          targetLabel: tecno_drive_component
          replacement: "gatekeeper"
    - port: healthz
      interval: 30s
      path: /healthz
      scheme: http
      scrapeTimeout: 5s
{{- end }}

---
# Network Policy for Webhook Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gatekeeper-webhook-network-policy
  namespace: {{ .Release.Namespace }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: gatekeeper-webhook-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "security-network"
spec:
  podSelector:
    matchLabels:
      control-plane: controller-manager
      gatekeeper.sh/system: "yes"
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow webhook traffic from API Server
    - from: []
      ports:
        - protocol: TCP
          port: 8443
    
    # Allow metrics collection from Prometheus
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8888
        - protocol: TCP
          port: 9440
    
    # Allow cert-manager access for certificate management
    - from:
        - namespaceSelector:
            matchLabels:
              name: cert-manager
      ports:
        - protocol: TCP
          port: 8443
  
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication with Kubernetes API
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 6443

---
# Emergency Break Glass ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: gatekeeper-emergency-procedures
  namespace: {{ .Release.Namespace }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: gatekeeper-emergency
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "security-emergency"
data:
  emergency-disable.sh: |
    #!/bin/bash
    # EMERGENCY ONLY - Disable Gatekeeper Webhook
    echo "🚨 EMERGENCY: Disabling Gatekeeper Webhook"
    
    # Backup current webhook configuration
    kubectl get validatingadmissionwebhook gatekeeper-validating-webhook-configuration -o yaml > webhook-backup-$(date +%Y%m%d-%H%M%S).yaml
    
    # Delete webhook configuration
    kubectl delete validatingadmissionwebhook gatekeeper-validating-webhook-configuration
    
    echo "✅ Webhook disabled. To re-enable, run: kubectl apply -f webhook-backup-*.yaml"
  
  emergency-warn-mode.sh: |
    #!/bin/bash
    # Set all constraints to warn mode
    echo "⚠️ Setting all constraints to WARN mode"
    
    kubectl get constraints -o name | while read constraint; do
      kubectl patch $constraint --type='merge' -p='{"spec":{"enforcementAction":"warn"}}'
      echo "✅ $constraint set to warn mode"
    done
  
  emergency-restore.sh: |
    #!/bin/bash
    # Restore normal operation
    echo "🔄 Restoring normal security operation"
    
    # Re-apply webhook if backup exists
    if ls webhook-backup-*.yaml 1> /dev/null 2>&1; then
      kubectl apply -f $(ls -t webhook-backup-*.yaml | head -1)
      echo "✅ Webhook restored"
    fi
    
    # Set constraints back to deny mode
    kubectl get constraints -o name | while read constraint; do
      kubectl patch $constraint --type='merge' -p='{"spec":{"enforcementAction":"deny"}}'
      echo "✅ $constraint set to deny mode"
    done

  contact-info.yaml: |
    emergency_contacts:
      - name: "TECNO DRIVE Security Team"
        email: "<EMAIL>"
        phone: "+966-11-XXX-XXXX"
        role: "Primary Security Response"
        escalation_time: "15 minutes"
      
      - name: "Platform Engineering"
        email: "<EMAIL>"
        phone: "+966-11-XXX-XXXX"
        role: "Infrastructure Support"
        escalation_time: "30 minutes"
      
      - name: "DevOps On-Call"
        email: "<EMAIL>"
        phone: "+966-11-XXX-XXXX"
        role: "24/7 Operations"
        escalation_time: "Immediate"
    
    escalation_matrix:
      severity_critical:
        - "Immediate notification to all contacts"
        - "Activate incident response team"
        - "Consider emergency disable procedures"
      
      severity_high:
        - "Notify security team within 15 minutes"
        - "Platform team within 30 minutes"
      
      severity_medium:
        - "Create ticket and notify during business hours"
        - "Review in next security meeting"
{{- end }}
