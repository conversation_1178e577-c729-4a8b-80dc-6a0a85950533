package com.tecnodrive.financialservice.repository;

import com.tecnodrive.financialservice.entity.FinancialTransactionLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Financial Transaction Repository
 * 
 * Data access layer for FinancialTransactionLog entities
 */
@Repository
public interface FinancialTransactionRepository extends JpaRepository<FinancialTransactionLog, UUID> {

    /**
     * Find transactions by company
     */
    List<FinancialTransactionLog> findByCompanyId(String companyId);

    /**
     * Find transactions by company with pagination
     */
    Page<FinancialTransactionLog> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find transactions by type
     */
    List<FinancialTransactionLog> findByTransactionType(FinancialTransactionLog.TransactionType transactionType);

    /**
     * Find transactions by status
     */
    List<FinancialTransactionLog> findByStatus(FinancialTransactionLog.TransactionStatus status);

    /**
     * Find transactions by source entity
     */
    List<FinancialTransactionLog> findBySourceEntityTypeAndSourceEntityId(String sourceEntityType, String sourceEntityId);

    /**
     * Find transactions by user
     */
    List<FinancialTransactionLog> findByUserId(String userId);

    /**
     * Find transactions by date range
     */
    @Query("SELECT t FROM FinancialTransactionLog t WHERE t.transactionDate BETWEEN :startDate AND :endDate")
    List<FinancialTransactionLog> findTransactionsBetweenDates(
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find transactions by company and date range
     */
    @Query("SELECT t FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.transactionDate BETWEEN :startDate AND :endDate")
    List<FinancialTransactionLog> findByCompanyIdAndDateRange(
            @Param("companyId") String companyId,
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Calculate total revenue by company
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.transactionType IN ('PAYMENT_RECEIVED', 'REVENUE_RECOGNIZED') AND t.status = 'COMPLETED'")
    BigDecimal calculateTotalRevenue(@Param("companyId") String companyId);

    /**
     * Calculate total expenses by company
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.transactionType IN ('PAYMENT_SENT', 'EXPENSE_RECORDED') AND t.status = 'COMPLETED'")
    BigDecimal calculateTotalExpenses(@Param("companyId") String companyId);

    /**
     * Calculate revenue for date range
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.transactionType IN ('PAYMENT_RECEIVED', 'REVENUE_RECOGNIZED') AND t.status = 'COMPLETED' AND t.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateRevenueForPeriod(
            @Param("companyId") String companyId,
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Calculate expenses for date range
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.transactionType IN ('PAYMENT_SENT', 'EXPENSE_RECORDED') AND t.status = 'COMPLETED' AND t.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateExpensesForPeriod(
            @Param("companyId") String companyId,
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find pending transactions
     */
    @Query("SELECT t FROM FinancialTransactionLog t WHERE t.status IN ('PENDING', 'PROCESSING')")
    List<FinancialTransactionLog> findPendingTransactions();

    /**
     * Find failed transactions for retry
     */
    @Query("SELECT t FROM FinancialTransactionLog t WHERE t.status = 'FAILED' AND t.transactionDate > :cutoffDate")
    List<FinancialTransactionLog> findFailedTransactionsForRetry(@Param("cutoffDate") Instant cutoffDate);

    /**
     * Get transaction statistics by type
     */
    @Query("SELECT t.transactionType, COUNT(t), COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId GROUP BY t.transactionType")
    List<Object[]> getTransactionStatisticsByType(@Param("companyId") String companyId);

    /**
     * Get monthly transaction summary
     */
    @Query("SELECT YEAR(t.transactionDate), MONTH(t.transactionDate), COUNT(t), COALESCE(SUM(t.amount), 0) FROM FinancialTransactionLog t WHERE t.companyId = :companyId AND t.status = 'COMPLETED' GROUP BY YEAR(t.transactionDate), MONTH(t.transactionDate) ORDER BY YEAR(t.transactionDate), MONTH(t.transactionDate)")
    List<Object[]> getMonthlyTransactionSummary(@Param("companyId") String companyId);

    /**
     * Find transactions by reference number
     */
    List<FinancialTransactionLog> findByReferenceNumber(String referenceNumber);

    /**
     * Count transactions by status
     */
    long countByStatus(FinancialTransactionLog.TransactionStatus status);

    /**
     * Count transactions by company and status
     */
    long countByCompanyIdAndStatus(String companyId, FinancialTransactionLog.TransactionStatus status);
}
