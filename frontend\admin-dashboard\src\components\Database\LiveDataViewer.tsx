import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  LinearProgress,
  Badge,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Visibility as ViewIcon,
  Download as ExportIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import databaseService from '../../services/databaseService';
import { QueryResult } from '../../types/api';

interface LiveDataViewerProps {
  database: string;
  tableName: string;
}

const LiveDataViewer: React.FC<LiveDataViewerProps> = ({ database, tableName }) => {
  const [data, setData] = useState<QueryResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'ASC' | 'DESC'>('ASC');

  useEffect(() => {
    if (database && tableName) {
      loadData();
    }
  }, [database, tableName]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh && refreshInterval > 0) {
      interval = setInterval(() => {
        loadData();
      }, refreshInterval);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh, refreshInterval, database, tableName]);

  const loadData = async () => {
    if (!database || !tableName) return;

    try {
      setLoading(true);
      let query = `SELECT * FROM ${tableName}`;
      
      // إضافة فلاتر
      const whereConditions = [];
      if (searchTerm) {
        whereConditions.push(`CAST(* AS TEXT) ILIKE '%${searchTerm}%'`);
      }
      
      Object.entries(filters).forEach(([column, value]) => {
        if (value) {
          whereConditions.push(`${column} ILIKE '%${value}%'`);
        }
      });

      if (whereConditions.length > 0) {
        query += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      // إضافة ترتيب
      if (sortColumn) {
        query += ` ORDER BY ${sortColumn} ${sortDirection}`;
      }

      query += ' LIMIT 100';

      const result = await databaseService.executeQuery(database, query);
      setData(result);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleColumnFilter = (column: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [column]: value
    }));
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortColumn(column);
      setSortDirection('ASC');
    }
  };

  const handleExport = async () => {
    if (!database || !tableName) return;
    
    try {
      const blob = await databaseService.exportTable(database, tableName, 'csv');
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${tableName}_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'success' | 'warning' | 'error' | 'info' | 'default'> = {
      'ACTIVE': 'success',
      'INACTIVE': 'warning',
      'PENDING': 'warning',
      'COMPLETED': 'success',
      'CANCELLED': 'error',
      'FAILED': 'error',
      'IN_PROGRESS': 'info',
      'DELIVERED': 'success',
      'IN_TRANSIT': 'info',
    };
    return statusColors[status?.toUpperCase()] || 'default';
  };

  const formatCellValue = (value: any, column: string) => {
    if (value === null || value === undefined) {
      return <Chip label="NULL" size="small" variant="outlined" />;
    }

    if (typeof value === 'boolean') {
      return <Chip label={value ? 'True' : 'False'} color={value ? 'success' : 'error'} size="small" />;
    }

    if (column.toLowerCase().includes('status')) {
      return <Chip label={value} color={getStatusColor(value)} size="small" />;
    }

    if (column.toLowerCase().includes('date') || column.toLowerCase().includes('time')) {
      try {
        return new Date(value).toLocaleString('ar-SA');
      } catch {
        return value;
      }
    }

    if (typeof value === 'number' && column.toLowerCase().includes('amount')) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(value);
    }

    if (typeof value === 'string' && value.length > 50) {
      return (
        <Tooltip title={value}>
          <span>{value.substring(0, 50)}...</span>
        </Tooltip>
      );
    }

    return value;
  };

  return (
    <Box>
      {/* Controls */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="البحث في البيانات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <IconButton size="small" onClick={loadData}>
                      <SearchIcon />
                    </IconButton>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>فترة التحديث</InputLabel>
                <Select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  label="فترة التحديث"
                >
                  <MenuItem value={1000}>1 ثانية</MenuItem>
                  <MenuItem value={5000}>5 ثواني</MenuItem>
                  <MenuItem value={10000}>10 ثواني</MenuItem>
                  <MenuItem value={30000}>30 ثانية</MenuItem>
                  <MenuItem value={60000}>دقيقة</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box display="flex" gap={1}>
                <Button
                  variant={autoRefresh ? "contained" : "outlined"}
                  startIcon={autoRefresh ? <PauseIcon /> : <PlayIcon />}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  color={autoRefresh ? "warning" : "primary"}
                >
                  {autoRefresh ? 'إيقاف' : 'تشغيل'} التحديث التلقائي
                </Button>
                
                <IconButton onClick={loadData} color="primary">
                  <RefreshIcon />
                </IconButton>
                
                <IconButton onClick={handleExport} color="success">
                  <ExportIcon />
                </IconButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="textSecondary">
                  آخر تحديث:
                </Typography>
                <Typography variant="body2">
                  {new Date().toLocaleTimeString('ar-SA')}
                </Typography>
                {loading && <CircularProgress size={16} />}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              بيانات الجدول: {tableName}
            </Typography>
            
            {data && (
              <Box display="flex" alignItems="center" gap={2}>
                <Chip 
                  label={`${data.totalRows.toLocaleString()} صف`} 
                  color="primary" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${data.executionTime}ms`} 
                  color="info" 
                  variant="outlined" 
                />
              </Box>
            )}
          </Box>

          {loading && <LinearProgress sx={{ mb: 2 }} />}

          {data ? (
            <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    {data.columns.map((column) => (
                      <TableCell 
                        key={column}
                        sx={{ 
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          '&:hover': { backgroundColor: 'action.hover' }
                        }}
                        onClick={() => handleSort(column)}
                      >
                        <Box display="flex" alignItems="center" gap={1}>
                          {column}
                          {sortColumn === column && (
                            <Typography variant="caption">
                              {sortDirection === 'ASC' ? '↑' : '↓'}
                            </Typography>
                          )}
                        </Box>
                        
                        {/* Column Filter */}
                        <TextField
                          size="small"
                          placeholder="فلتر..."
                          value={filters[column] || ''}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleColumnFilter(column, e.target.value);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          sx={{ mt: 1, width: '100%' }}
                        />
                      </TableCell>
                    ))}
                    <TableCell sx={{ fontWeight: 'bold' }}>إجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.rows.map((row, index) => (
                    <TableRow key={index} hover>
                      {row.map((cell, cellIndex) => (
                        <TableCell key={cellIndex}>
                          {formatCellValue(cell, data.columns[cellIndex])}
                        </TableCell>
                      ))}
                      <TableCell>
                        <IconButton size="small" color="primary">
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              لا توجد بيانات متاحة. اختر قاعدة بيانات وجدول لعرض البيانات.
            </Alert>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default LiveDataViewer;
