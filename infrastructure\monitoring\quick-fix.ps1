#!/usr/bin/env pwsh

# TECNO DRIVE Platform - Quick Fix Script
# This script quickly fixes and starts all missing services

Write-Host "🔧 TECNO DRIVE Quick Fix - Starting Missing Services" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Cyan

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to start service if not running
function Start-ServiceIfNeeded {
    param(
        [string]$ServiceName,
        [int]$Port,
        [string]$DockerCommand
    )
    
    if (Test-Port -Port $Port) {
        Write-Host "✅ $ServiceName already running on port $Port" -ForegroundColor Green
    } else {
        Write-Host "🔄 Starting $ServiceName on port $Port..." -ForegroundColor Yellow
        try {
            Invoke-Expression $DockerCommand
            Start-Sleep -Seconds 10
            if (Test-Port -Port $Port) {
                Write-Host "✅ $ServiceName started successfully!" -ForegroundColor Green
            } else {
                Write-Host "⚠️ $ServiceName may still be starting..." -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "❌ Failed to start $ServiceName" -ForegroundColor Red
        }
    }
}

# Start missing services
Write-Host "`n🚀 Checking and starting services..." -ForegroundColor Blue

# Prometheus
Start-ServiceIfNeeded -ServiceName "Prometheus" -Port 9090 -DockerCommand "docker run -d --name prometheus-fix -p 9090:9090 --restart unless-stopped prom/prometheus:latest"

# Grafana
Start-ServiceIfNeeded -ServiceName "Grafana" -Port 3001 -DockerCommand "docker run -d --name grafana-fix -p 3001:3000 -e GF_SECURITY_ADMIN_PASSWORD=admin123 --restart unless-stopped grafana/grafana:latest"

# API Gateway (check if it needs restart)
if (-not (Test-Port -Port 8080)) {
    Write-Host "🔄 Restarting API Gateway..." -ForegroundColor Yellow
    docker restart api-gateway-fixed 2>$null
    Start-Sleep -Seconds 15
}

# Check all services
Write-Host "`n📊 Final Service Status:" -ForegroundColor Blue

$services = @(
    @{Name="Admin Dashboard"; Port=3000; Url="http://localhost:3000"},
    @{Name="HR Frontend"; Port=3002; Url="http://localhost:3002"},
    @{Name="API Gateway"; Port=8080; Url="http://localhost:8080"},
    @{Name="Eureka Server"; Port=8761; Url="http://localhost:8761"},
    @{Name="Auth Service"; Port=8081; Url="http://localhost:8081"},
    @{Name="Prometheus"; Port=9090; Url="http://localhost:9090"},
    @{Name="Grafana"; Port=3001; Url="http://localhost:3001"}
)

foreach ($service in $services) {
    if (Test-Port -Port $service.Port) {
        Write-Host "✅ $($service.Name) - Running ($($service.Url))" -ForegroundColor Green
    } else {
        Write-Host "❌ $($service.Name) - Not responding" -ForegroundColor Red
    }
}

Write-Host "`n🔗 Quick Access Links:" -ForegroundColor Cyan
Write-Host "• Admin Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host "• API Gateway: http://localhost:8080" -ForegroundColor White
Write-Host "• Prometheus: http://localhost:9090" -ForegroundColor White
Write-Host "• Grafana: http://localhost:3001 (admin/admin123)" -ForegroundColor White
Write-Host "• Eureka: http://localhost:8761" -ForegroundColor White

Write-Host "`n✅ Quick fix completed!" -ForegroundColor Green
