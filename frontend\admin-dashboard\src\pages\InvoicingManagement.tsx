import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  CircularProgress,
  Tabs,
  Tab,
  Alert,
  Divider
} from '@mui/material';
import {
  Receipt as InvoiceIcon,
  Payment as PaymentIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  GetApp as DownloadIcon,
  Email as EmailIcon,
  Print as PrintIcon
} from '@mui/icons-material';

interface Invoice {
  id: string;
  invoiceNumber: string;
  type: string;
  status: string;
  customerName: string;
  customerEmail: string;
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  subtotal: number;
  taxAmount: number;
  total: number;
  amountPaid: number;
  amountDue: number;
  paymentMethod: string;
  description: string;
}

interface BillingMetrics {
  totalInvoices: number;
  totalRevenue: number;
  totalPaid: number;
  totalOutstanding: number;
  averageInvoiceValue: number;
  paymentRate: number;
  overdueInvoices: number;
  overdueAmount: number;
  averagePaymentTime: number;
  collectionEfficiency: number;
  monthlyGrowth: number;
}

interface PaymentRecord {
  id: string;
  invoiceId: string;
  amount: number;
  fee: number;
  netAmount: number;
  method: string;
  status: string;
  processedAt: string;
  transactionId: string;
}

const InvoicingManagement: React.FC = () => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [metrics, setMetrics] = useState<BillingMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    search: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    loadInvoicingData();
  }, [filters]);

  const loadInvoicingData = async () => {
    try {
      setLoading(true);

      // Load invoices
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const invoicesResponse = await fetch(`http://localhost:8105/api/invoicing/invoices?${queryParams}`);
      const invoicesData = await invoicesResponse.json();
      
      if (invoicesData.success) {
        setInvoices(invoicesData.data);
      }

      // Load payments
      const paymentsResponse = await fetch('http://localhost:8105/api/invoicing/payments');
      const paymentsData = await paymentsResponse.json();
      
      if (paymentsData.success) {
        setPayments(paymentsData.data);
      }

      // Load metrics
      const metricsResponse = await fetch('http://localhost:8105/api/invoicing/metrics');
      const metricsResult = await metricsResponse.json();
      
      if (metricsResult.success) {
        setMetrics(metricsResult.data);
      }

    } catch (error) {
      console.error('Failed to load invoicing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleViewDetails = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setDetailsDialogOpen(true);
  };

  const handleSendInvoice = async (invoiceId: string) => {
    try {
      const response = await fetch(`http://localhost:8105/api/invoicing/invoices/${invoiceId}/send`, {
        method: 'POST'
      });
      
      if (response.ok) {
        loadInvoicingData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to send invoice:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(Math.round(num));
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'draft': 'default',
      'sent': 'info',
      'paid': 'success',
      'overdue': 'error',
      'cancelled': 'error'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'draft': 'مسودة',
      'sent': 'مرسلة',
      'paid': 'مدفوعة',
      'overdue': 'متأخرة',
      'cancelled': 'ملغية'
    };
    return labels[status] || status;
  };

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'trip': 'رحلة',
      'parcel': 'طرد',
      'subscription': 'اشتراك',
      'penalty': 'غرامة',
      'bonus': 'مكافأة'
    };
    return labels[type] || type;
  };

  const getPaymentMethodLabel = (method: string) => {
    const labels: Record<string, string> = {
      'cash': 'نقدي',
      'card': 'بطاقة',
      'bank_transfer': 'تحويل بنكي',
      'wallet': 'محفظة إلكترونية',
      'check': 'شيك'
    };
    return labels[method] || method;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            جاري تحميل بيانات الفواتير...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <InvoiceIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          إدارة الفواتير المؤتمتة
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          نظام شامل لإدارة الفواتير والمدفوعات مع تحليلات مالية متقدمة
        </Typography>
      </Box>

      {/* Metrics Cards */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <InvoiceIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.totalInvoices}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الفواتير
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      متأخرة: {metrics.overdueInvoices}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(metrics.totalRevenue)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الإيرادات
                    </Typography>
                    <Typography variant="caption" color="success.main">
                      مدفوع: {formatCurrency(metrics.totalPaid)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatCurrency(metrics.totalOutstanding)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      المبالغ المستحقة
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      متأخرة: {formatCurrency(metrics.overdueAmount)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="info" sx={{ fontSize: 40, mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{metrics.paymentRate.toFixed(1)}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل الدفع
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      متوسط الفاتورة: {formatCurrency(metrics.averageInvoiceValue)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Performance Indicators */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>كفاءة التحصيل</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.collectionEfficiency}
                  sx={{ height: 10, borderRadius: 5, mb: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {metrics.collectionEfficiency.toFixed(1)}% من المبالغ المستحقة
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>متوسط وقت الدفع</Typography>
                <Box display="flex" alignItems="center">
                  <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="h4">{metrics.averagePaymentTime.toFixed(0)}</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    يوم
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>النمو الشهري</Typography>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon sx={{ mr: 1, color: 'success.main' }} />
                  <Typography variant="h4" color="success.main">
                    +{metrics.monthlyGrowth.toFixed(1)}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              placeholder="البحث..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="الحالة"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="draft">مسودة</MenuItem>
                <MenuItem value="sent">مرسلة</MenuItem>
                <MenuItem value="paid">مدفوعة</MenuItem>
                <MenuItem value="overdue">متأخرة</MenuItem>
                <MenuItem value="cancelled">ملغية</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>النوع</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="النوع"
              >
                <MenuItem value="">الكل</MenuItem>
                <MenuItem value="trip">رحلة</MenuItem>
                <MenuItem value="parcel">طرد</MenuItem>
                <MenuItem value="subscription">اشتراك</MenuItem>
                <MenuItem value="penalty">غرامة</MenuItem>
                <MenuItem value="bonus">مكافأة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="من تاريخ"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="إلى تاريخ"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
            >
              فاتورة جديدة
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
          <Tab label="الفواتير" icon={<InvoiceIcon />} />
          <Tab label="المدفوعات" icon={<PaymentIcon />} />
          <Tab label="التقارير" icon={<TrendingUpIcon />} />
        </Tabs>
      </Paper>

      {/* Invoices Tab */}
      {selectedTab === 0 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>رقم الفاتورة</TableCell>
                  <TableCell>العميل</TableCell>
                  <TableCell>النوع</TableCell>
                  <TableCell>تاريخ الإصدار</TableCell>
                  <TableCell>تاريخ الاستحقاق</TableCell>
                  <TableCell>المبلغ الإجمالي</TableCell>
                  <TableCell>المبلغ المدفوع</TableCell>
                  <TableCell>المبلغ المستحق</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {invoice.invoiceNumber}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getTypeLabel(invoice.type)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {invoice.customerName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {invoice.customerEmail}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getTypeLabel(invoice.type)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color={new Date(invoice.dueDate) < new Date() && invoice.status !== 'paid' ? 'error.main' : 'text.primary'}>
                        {new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(invoice.total)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(invoice.amountPaid)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color={invoice.amountDue > 0 ? 'warning.main' : 'success.main'}>
                        {formatCurrency(invoice.amountDue)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(invoice.status)}
                        color={getStatusColor(invoice.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton size="small" onClick={() => handleViewDetails(invoice)}>
                          <ViewIcon />
                        </IconButton>
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                        {invoice.status === 'draft' && (
                          <IconButton size="small" onClick={() => handleSendInvoice(invoice.id)}>
                            <SendIcon />
                          </IconButton>
                        )}
                        <IconButton size="small">
                          <DownloadIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={invoices.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
          />
        </Paper>
      )}

      {/* Payments Tab */}
      {selectedTab === 1 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>معرف الدفعة</TableCell>
                  <TableCell>رقم الفاتورة</TableCell>
                  <TableCell>المبلغ</TableCell>
                  <TableCell>الرسوم</TableCell>
                  <TableCell>المبلغ الصافي</TableCell>
                  <TableCell>طريقة الدفع</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ المعالجة</TableCell>
                  <TableCell>معرف المعاملة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {payments.slice(0, 15).map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {payment.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {payment.invoiceId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(payment.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="error.main">
                        {formatCurrency(payment.fee)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(payment.netAmount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getPaymentMethodLabel(payment.method)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={payment.status}
                        color={payment.status === 'completed' ? 'success' : payment.status === 'pending' ? 'warning' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(payment.processedAt).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {payment.transactionId}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Reports Tab */}
      {selectedTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  اتجاهات الإيرادات الشهرية
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    رسم بياني لاتجاهات الإيرادات (يتطلب مكتبة رسوم بيانية)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تقارير سريعة
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Button variant="outlined" startIcon={<DownloadIcon />}>
                    تقرير الفواتير الشهري
                  </Button>
                  <Button variant="outlined" startIcon={<DownloadIcon />}>
                    تقرير المدفوعات
                  </Button>
                  <Button variant="outlined" startIcon={<DownloadIcon />}>
                    تقرير الضرائب
                  </Button>
                  <Button variant="outlined" startIcon={<DownloadIcon />}>
                    تقرير المتأخرات
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Invoice Details Dialog */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل الفاتورة</DialogTitle>
        <DialogContent>
          {selectedInvoice && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">رقم الفاتورة</Typography>
                <Typography variant="body1">{selectedInvoice.invoiceNumber}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">العميل</Typography>
                <Typography variant="body1">{selectedInvoice.customerName}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">تاريخ الإصدار</Typography>
                <Typography variant="body1">
                  {new Date(selectedInvoice.issueDate).toLocaleDateString('ar-SA')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">تاريخ الاستحقاق</Typography>
                <Typography variant="body1">
                  {new Date(selectedInvoice.dueDate).toLocaleDateString('ar-SA')}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">المبلغ الفرعي</Typography>
                <Typography variant="body1">{formatCurrency(selectedInvoice.subtotal)}</Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">الضريبة</Typography>
                <Typography variant="body1">{formatCurrency(selectedInvoice.taxAmount)}</Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">المبلغ الإجمالي</Typography>
                <Typography variant="body1" fontWeight="bold">
                  {formatCurrency(selectedInvoice.total)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">المبلغ المدفوع</Typography>
                <Typography variant="body1" color="success.main">
                  {formatCurrency(selectedInvoice.amountPaid)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">المبلغ المستحق</Typography>
                <Typography variant="body1" color="warning.main">
                  {formatCurrency(selectedInvoice.amountDue)}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">الوصف</Typography>
                <Typography variant="body1">{selectedInvoice.description}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>إغلاق</Button>
          <Button variant="outlined" startIcon={<PrintIcon />}>
            طباعة
          </Button>
          <Button variant="outlined" startIcon={<EmailIcon />}>
            إرسال بالبريد
          </Button>
          <Button variant="contained" startIcon={<DownloadIcon />}>
            تحميل PDF
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Invoice Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>إنشاء فاتورة جديدة</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="اسم العميل"
                placeholder="أدخل اسم العميل"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="بريد العميل الإلكتروني"
                placeholder="أدخل البريد الإلكتروني"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>نوع الفاتورة</InputLabel>
                <Select label="نوع الفاتورة">
                  <MenuItem value="trip">رحلة</MenuItem>
                  <MenuItem value="parcel">طرد</MenuItem>
                  <MenuItem value="subscription">اشتراك</MenuItem>
                  <MenuItem value="penalty">غرامة</MenuItem>
                  <MenuItem value="bonus">مكافأة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="تاريخ الاستحقاق"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="وصف الفاتورة"
                placeholder="أدخل وصف الفاتورة"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained">إنشاء الفاتورة</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default InvoicingManagement;
