-- =====================================================
-- TecnoDrive Platform - Authentication Schema
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'USER' CHECK (role IN ('ADMIN', 'OPERATOR', 'DISPATCHER', 'USER')),
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ROLES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- USER_ROLES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- =====================================================
-- REFRESH_TOKENS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS refresh_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(500) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- INDEXES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user ON refresh_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires ON refresh_tokens(expires_at);

-- =====================================================
-- INSERT DEFAULT ROLES
-- =====================================================
INSERT INTO roles (name, description, permissions) VALUES
('ADMIN', 'System Administrator', '["*"]'),
('OPERATOR', 'Operations Manager', '["dashboard:read", "trips:*", "parcels:*", "vehicles:read", "drivers:read"]'),
('DISPATCHER', 'Dispatcher', '["dashboard:read", "trips:read", "trips:assign", "parcels:read", "parcels:assign"]'),
('USER', 'Regular User', '["dashboard:read", "profile:*"]')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- INSERT DEFAULT USERS
-- =====================================================
INSERT INTO users (id, username, email, password, first_name, last_name, role, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', 'Admin', 'User', 'ADMIN', true),
('550e8400-e29b-41d4-a716-446655440002', 'operator', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', 'Operator', 'User', 'OPERATOR', true),
('550e8400-e29b-41d4-a716-446655440003', 'dispatcher', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', 'Dispatcher', 'User', 'DISPATCHER', true),
('550e8400-e29b-41d4-a716-446655440004', 'demo', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGdMLeIcnQRjjda7AoUzJe', 'Demo', 'User', 'USER', true)
ON CONFLICT (username) DO NOTHING;

-- =====================================================
-- ASSIGN ROLES TO USERS
-- =====================================================
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE (u.username = 'admin' AND r.name = 'ADMIN')
   OR (u.username = 'operator' AND r.name = 'OPERATOR')
   OR (u.username = 'dispatcher' AND r.name = 'DISPATCHER')
   OR (u.username = 'demo' AND r.name = 'USER')
ON CONFLICT DO NOTHING;

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DISPLAY CREATED CREDENTIALS
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '=== TecnoDrive Authentication Setup Complete ===';
    RAISE NOTICE 'Available Users:';
    RAISE NOTICE '1. Username: admin     | Password: admin123 | Role: ADMIN';
    RAISE NOTICE '2. Username: operator  | Password: admin123 | Role: OPERATOR';
    RAISE NOTICE '3. Username: dispatcher| Password: admin123 | Role: DISPATCHER';
    RAISE NOTICE '4. Username: demo      | Password: admin123 | Role: USER';
    RAISE NOTICE '';
    RAISE NOTICE 'Login URL: http://localhost:8081/login';
    RAISE NOTICE '================================================';
END $$;
