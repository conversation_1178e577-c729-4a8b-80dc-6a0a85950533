# Test Docker Setup
Write-Host "🐳 Testing Docker Setup" -ForegroundColor Green

# Clean up any existing containers
Write-Host "🧹 Cleaning up..." -ForegroundColor Yellow
docker stop $(docker ps -aq) 2>$null
docker rm $(docker ps -aq) 2>$null

# Create network
Write-Host "🌐 Creating network..." -ForegroundColor Cyan
docker network create tecnodrive-test 2>$null

# Start a simple nginx container
Write-Host "🚀 Starting test container..." -ForegroundColor Cyan
$containerId = docker run -d --name test-container --network tecnodrive-test -p 8888:80 nginx:alpine

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Container started: $containerId" -ForegroundColor Green
    
    # Wait a moment
    Start-Sleep -Seconds 3
    
    # Check if it's running
    Write-Host "📊 Checking container status..." -ForegroundColor Cyan
    docker ps --filter "name=test-container"
    
    # Test connection
    Write-Host "🔍 Testing connection..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8888" -TimeoutSec 5
        Write-Host "✅ Connection successful! Status: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ Failed to start container" -ForegroundColor Red
}

Write-Host "🎯 Test completed!" -ForegroundColor Green
