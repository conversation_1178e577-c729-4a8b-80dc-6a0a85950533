import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface AppUser {
  id: string;
  name: string;
  email: string;
  phone: string;
  userType: 'PASSENGER' | 'DRIVER' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  rating?: number;
  totalRides?: number;
  createdAt: string;
  updatedAt: string;
}

interface UsersState {
  users: AppUser[];
  currentUser: AppUser | null;
  loading: boolean;
  error: string | null;
  totalUsers: number;
  activeUsers: number;
  drivers: number;
  passengers: number;
}

const initialState: UsersState = {
  users: [],
  currentUser: null,
  loading: false,
  error: null,
  totalUsers: 0,
  activeUsers: 0,
  drivers: 0,
  passengers: 0,
};

export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params?: { page?: number; limit?: number; userType?: string }) => {
    const { usersService } = await import('../../services/usersService');
    const response = await usersService.getUsers(params);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب المستخدمين');
    }

    return response;
  }
);

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload.data;
        state.totalUsers = action.payload.total || action.payload.data.length;
        state.drivers = action.payload.data.filter((u: AppUser) => u.userType === 'DRIVER').length;
        state.passengers = action.payload.data.filter((u: AppUser) => u.userType === 'PASSENGER').length;
        state.activeUsers = action.payload.data.filter((u: AppUser) => u.status === 'ACTIVE').length;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في جلب المستخدمين';
      });
  },
});

export const { clearError } = usersSlice.actions;
export default usersSlice.reducer;
