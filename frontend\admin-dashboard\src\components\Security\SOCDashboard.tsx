import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Badge,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Tab,
  Tabs,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  Security,
  Warning,
  Shield,
  BugReport,
  Visibility,
  Block,
  CheckCircle,
  Error,
  Info,
  Refresh,
  Download,
  PlayArrow,
  Stop,
  Assignment,
  Timeline,
  TrendingUp,
  NetworkCheck,
  VpnLock,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, AreaChart, Area } from 'recharts';
import siemIntegrationService, { 
  SIEMAlert, 
  SecurityEvent, 
  ThreatIntelligence, 
  IncidentResponse, 
  SIEMMetrics 
} from '../../services/siemIntegrationService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`soc-tabpanel-${index}`}
      aria-labelledby={`soc-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SOCDashboard: React.FC = () => {
  const [siemAlerts, setSiemAlerts] = useState<SIEMAlert[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [threatIntelligence, setThreatIntelligence] = useState<ThreatIntelligence[]>([]);
  const [incidents, setIncidents] = useState<IncidentResponse[]>([]);
  const [siemMetrics, setSiemMetrics] = useState<SIEMMetrics | null>(null);
  const [selectedAlert, setSelectedAlert] = useState<SIEMAlert | null>(null);
  const [selectedIncident, setSelectedIncident] = useState<IncidentResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [newIncident, setNewIncident] = useState<Partial<IncidentResponse>>({});

  const tenantId = localStorage.getItem('tenantId') || 'default';

  useEffect(() => {
    loadSOCData();
    setupRealTimeSubscriptions();

    return () => {
      siemIntegrationService.disconnect();
    };
  }, []);

  const loadSOCData = async () => {
    try {
      setLoading(true);
      const [alerts, events, threats, incidentsData, metrics] = await Promise.all([
        siemIntegrationService.getAzureSentinelAlerts(tenantId),
        siemIntegrationService.getSecurityEvents(tenantId),
        siemIntegrationService.getThreatIntelligence(),
        siemIntegrationService.getIncidents(tenantId),
        siemIntegrationService.getSIEMMetrics(tenantId),
      ]);

      setSiemAlerts(alerts);
      setSecurityEvents(events);
      setThreatIntelligence(threats);
      setIncidents(incidentsData);
      setSiemMetrics(metrics);
    } catch (error) {
      console.error('Error loading SOC data:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealTimeSubscriptions = () => {
    siemIntegrationService.subscribeToSIEMAlerts(tenantId, (alert: SIEMAlert) => {
      setSiemAlerts(prev => [alert, ...prev]);
      
      // Auto-create incident for critical alerts
      if (alert.severity === 'critical') {
        handleCreateIncidentFromAlert(alert);
      }
    });

    siemIntegrationService.subscribeToSecurityEvents(tenantId, (event: SecurityEvent) => {
      setSecurityEvents(prev => [event, ...prev.slice(0, 49)]);
    });

    siemIntegrationService.subscribeToThreatIntelligence((threat: ThreatIntelligence) => {
      setThreatIntelligence(prev => [threat, ...prev.slice(0, 99)]);
    });
  };

  const handleCreateIncidentFromAlert = async (alert: SIEMAlert) => {
    try {
      const incident = await siemIntegrationService.createIncident({
        alertId: alert.id,
        title: `Security Incident: ${alert.title}`,
        description: alert.description,
        severity: alert.severity,
        status: 'open',
        assignedTo: 'auto-assigned',
      });
      setIncidents(prev => [incident, ...prev]);
    } catch (error) {
      console.error('Error creating incident from alert:', error);
    }
  };

  const handleAlertClick = (alert: SIEMAlert) => {
    setSelectedAlert(alert);
  };

  const handleIncidentClick = (incident: IncidentResponse) => {
    setSelectedIncident(incident);
  };

  const handleBlockIP = async (ip: string) => {
    try {
      await siemIntegrationService.blockIPAddress(ip, 'Suspicious activity detected');
      // Refresh data
      loadSOCData();
    } catch (error) {
      console.error('Error blocking IP:', error);
    }
  };

  const handleExecutePlaybook = async (incidentId: string, playbookId: string) => {
    try {
      await siemIntegrationService.executePlaybook(incidentId, playbookId);
      // Refresh incidents
      const updatedIncidents = await siemIntegrationService.getIncidents(tenantId);
      setIncidents(updatedIncidents);
    } catch (error) {
      console.error('Error executing playbook:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <Error color="error" />;
      case 'high': return <Warning color="warning" />;
      case 'medium': return <Info color="info" />;
      case 'low': return <CheckCircle color="success" />;
      default: return <Info />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <Error color="error" />;
      case 'investigating': return <Visibility color="warning" />;
      case 'contained': return <Shield color="info" />;
      case 'resolved': return <CheckCircle color="success" />;
      case 'closed': return <CheckCircle color="success" />;
      default: return <Info />;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <Typography>Loading Security Operations Center...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Security Operations Center (SOC)
        </Typography>
        <Box>
          <IconButton onClick={loadSOCData} color="primary">
            <Refresh />
          </IconButton>
          <Button
            startIcon={<Download />}
            variant="outlined"
            sx={{ ml: 1 }}
          >
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Security Metrics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Security color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Alerts
                  </Typography>
                  <Typography variant="h5">
                    {siemMetrics?.totalAlerts || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Error color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Critical Alerts
                  </Typography>
                  <Typography variant="h5" color="error">
                    {siemMetrics?.alertsBySeverity?.critical || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Timeline color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    MTTD (Minutes)
                  </Typography>
                  <Typography variant="h5" color="info.main">
                    {siemMetrics?.meanTimeToDetection || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    MTTR (Minutes)
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {siemMetrics?.meanTimeToResponse || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Threat Trends Chart */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Threat Trends (Last 30 Days)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={siemMetrics?.threatTrends || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Area type="monotone" dataKey="count" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alerts by Severity
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={Object.entries(siemMetrics?.alertsBySeverity || {}).map(([key, value]) => ({
                      name: key,
                      value,
                    }))}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {Object.entries(siemMetrics?.alertsBySeverity || {}).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="SIEM Alerts" />
          <Tab label="Security Events" />
          <Tab label="Incidents" />
          <Tab label="Threat Intelligence" />
        </Tabs>

        {/* SIEM Alerts Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            {siemAlerts.map((alert) => (
              <Grid item xs={12} md={6} lg={4} key={alert.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { boxShadow: 3 },
                    border: alert.severity === 'critical' ? '2px solid #f44336' : 'none',
                  }}
                  onClick={() => handleAlertClick(alert)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="h6" noWrap>
                        {alert.title}
                      </Typography>
                      {getSeverityIcon(alert.severity)}
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" mb={1}>
                      {alert.description.substring(0, 100)}...
                    </Typography>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={alert.source}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={alert.severity}
                        size="small"
                        color={getSeverityColor(alert.severity) as any}
                      />
                    </Box>

                    <Typography variant="caption" color="textSecondary">
                      {new Date(alert.timestamp).toLocaleString()}
                    </Typography>

                    {alert.sourceIP && (
                      <Box mt={1}>
                        <Button
                          size="small"
                          startIcon={<Block />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleBlockIP(alert.sourceIP!);
                          }}
                          color="error"
                        >
                          Block IP
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Security Events Tab */}
        <TabPanel value={tabValue} index={1}>
          <List>
            {securityEvents.map((event) => (
              <React.Fragment key={event.id}>
                <ListItem>
                  <ListItemIcon>
                    <VpnLock color={event.riskScore > 70 ? 'error' : event.riskScore > 40 ? 'warning' : 'success'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="body1">
                          {event.eventType.replace('_', ' ').toUpperCase()}
                        </Typography>
                        <Chip
                          label={`Risk: ${event.riskScore}`}
                          size="small"
                          color={event.riskScore > 70 ? 'error' : event.riskScore > 40 ? 'warning' : 'success'}
                        />
                        <Chip
                          label={event.status}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          Source IP: {event.sourceIP} | User: {event.userId || 'Unknown'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {new Date(event.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        </TabPanel>

        {/* Incidents Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={2}>
            {incidents.map((incident) => (
              <Grid item xs={12} md={6} lg={4} key={incident.id}>
                <Card
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                  onClick={() => handleIncidentClick(incident)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="h6" noWrap>
                        {incident.title}
                      </Typography>
                      {getStatusIcon(incident.status)}
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" mb={2}>
                      {incident.description.substring(0, 100)}...
                    </Typography>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={incident.severity}
                        size="small"
                        color={getSeverityColor(incident.severity) as any}
                      />
                      <Chip
                        label={incident.status}
                        size="small"
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary">
                      Assigned to: {incident.assignedTo}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Created: {new Date(incident.createdAt).toLocaleDateString()}
                    </Typography>

                    {incident.playbook && (
                      <Box mt={1}>
                        <Button
                          size="small"
                          startIcon={<PlayArrow />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExecutePlaybook(incident.id, incident.playbook!);
                          }}
                          color="primary"
                        >
                          Execute Playbook
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Threat Intelligence Tab */}
        <TabPanel value={tabValue} index={3}>
          <List>
            {threatIntelligence.map((threat) => (
              <React.Fragment key={threat.id}>
                <ListItem>
                  <ListItemIcon>
                    <BugReport color={threat.confidence > 0.8 ? 'error' : threat.confidence > 0.5 ? 'warning' : 'info'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="body1">
                          {threat.indicatorValue}
                        </Typography>
                        <Chip
                          label={threat.indicatorType}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                        <Chip
                          label={threat.threatType}
                          size="small"
                          color={threat.confidence > 0.8 ? 'error' : 'warning'}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {threat.description}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Confidence: {(threat.confidence * 100).toFixed(0)}% | 
                          First seen: {new Date(threat.firstSeen).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        </TabPanel>
      </Paper>

      {/* Alert Detail Dialog */}
      <Dialog
        open={!!selectedAlert}
        onClose={() => setSelectedAlert(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          SIEM Alert Details
        </DialogTitle>
        <DialogContent>
          {selectedAlert && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedAlert.title}
              </Typography>
              
              <Typography variant="body1" paragraph>
                {selectedAlert.description}
              </Typography>

              <Grid container spacing={2} mb={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Source</Typography>
                  <Chip label={selectedAlert.source} color="primary" />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Severity</Typography>
                  <Chip label={selectedAlert.severity} color={getSeverityColor(selectedAlert.severity) as any} />
                </Grid>
              </Grid>

              {selectedAlert.affectedAssets.length > 0 && (
                <Box mb={2}>
                  <Typography variant="h6" gutterBottom>
                    Affected Assets
                  </Typography>
                  {selectedAlert.affectedAssets.map((asset, index) => (
                    <Chip key={index} label={asset} sx={{ mr: 1, mb: 1 }} />
                  ))}
                </Box>
              )}

              {selectedAlert.recommendations.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Recommendations
                  </Typography>
                  <List>
                    {selectedAlert.recommendations.map((rec, index) => (
                      <ListItem key={index}>
                        <ListItemText primary={rec} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedAlert(null)}>
            Close
          </Button>
          <Button variant="contained" color="primary">
            Create Incident
          </Button>
        </DialogActions>
      </Dialog>

      {/* Incident Detail Dialog */}
      <Dialog
        open={!!selectedIncident}
        onClose={() => setSelectedIncident(null)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Incident Details
        </DialogTitle>
        <DialogContent>
          {selectedIncident && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedIncident.title}
              </Typography>
              
              <Typography variant="body1" paragraph>
                {selectedIncident.description}
              </Typography>

              <Grid container spacing={2} mb={3}>
                <Grid item xs={4}>
                  <Typography variant="body2" color="textSecondary">Severity</Typography>
                  <Chip label={selectedIncident.severity} color={getSeverityColor(selectedIncident.severity) as any} />
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="body2" color="textSecondary">Status</Typography>
                  <Chip label={selectedIncident.status} variant="outlined" />
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="body2" color="textSecondary">Assigned To</Typography>
                  <Typography variant="body1">{selectedIncident.assignedTo}</Typography>
                </Grid>
              </Grid>

              {selectedIncident.timeline.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Timeline
                  </Typography>
                  <List>
                    {selectedIncident.timeline.map((entry, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <Assignment />
                        </ListItemIcon>
                        <ListItemText
                          primary={entry.action}
                          secondary={`${entry.performedBy} - ${new Date(entry.timestamp).toLocaleString()}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedIncident(null)}>
            Close
          </Button>
          <Button variant="contained" color="primary">
            Update Status
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SOCDashboard;
