# Complete TecnoDrive Docker Setup
Write-Host "🐳 Complete TecnoDrive Docker Setup" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Gray

# Clean up existing containers
Write-Host "`n🧹 Cleaning up existing containers..." -ForegroundColor Yellow
docker stop $(docker ps -aq) 2>$null | Out-Null
docker rm $(docker ps -aq) 2>$null | Out-Null
docker network rm tecnodrive-network 2>$null | Out-Null

# Create network
Write-Host "`n🌐 Creating Docker network..." -ForegroundColor Cyan
docker network create tecnodrive-network
Write-Host "✅ Network created successfully" -ForegroundColor Green

# Start PostgreSQL
Write-Host "`n📦 Starting PostgreSQL..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-postgres `
    --network tecnodrive-network `
    -e POSTGRES_DB=tecnodrive `
    -e POSTGRES_USER=postgres `
    -e POSTGRES_PASSWORD=postgres123 `
    -p 5432:5432 `
    postgres:15-alpine
Write-Host "✅ PostgreSQL started" -ForegroundColor Green

# Start Redis
Write-Host "`n📦 Starting Redis..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-redis `
    --network tecnodrive-network `
    -p 6379:6379 `
    redis:7-alpine
Write-Host "✅ Redis started" -ForegroundColor Green

# Wait for infrastructure
Write-Host "`n⏳ Waiting for infrastructure..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start Eureka Server
Write-Host "`n🔧 Starting Eureka Server..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-eureka `
    --network tecnodrive-network `
    -p 8761:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Eureka Server Mock\`"; add_header Content-Type text/plain; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Eureka Server started" -ForegroundColor Green

# Start API Gateway
Write-Host "`n🌐 Starting API Gateway..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-gateway `
    --network tecnodrive-network `
    -p 8080:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"TecnoDrive API Gateway\`"; add_header Content-Type text/plain; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ API Gateway started" -ForegroundColor Green

# Start Auth Service
Write-Host "`n🔐 Starting Auth Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-auth `
    --network tecnodrive-network `
    -p 8081:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Auth Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"Auth Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Auth Service started" -ForegroundColor Green

# Start User Service
Write-Host "`n👤 Starting User Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-user `
    --network tecnodrive-network `
    -p 8083:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"User Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"User Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ User Service started" -ForegroundColor Green

# Start Ride Service
Write-Host "`n🚗 Starting Ride Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-ride `
    --network tecnodrive-network `
    -p 8082:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Ride Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"Ride Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Ride Service started" -ForegroundColor Green

# Start Fleet Service
Write-Host "`n🚛 Starting Fleet Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-fleet `
    --network tecnodrive-network `
    -p 8084:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Fleet Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"Fleet Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Fleet Service started" -ForegroundColor Green

# Start Location Service
Write-Host "`n📍 Starting Location Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-location `
    --network tecnodrive-network `
    -p 8085:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Location Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"Location Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Location Service started" -ForegroundColor Green

# Start Payment Service
Write-Host "`n💳 Starting Payment Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-payment `
    --network tecnodrive-network `
    -p 8086:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \`"Payment Service Running\`"; add_header Content-Type text/plain; } location /actuator/health { return 200 \`"{\\\`"status\\\`":\\\`"UP\\\`",\\\`"service\\\`":\\\`"Payment Service\\\`"}\`"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
Write-Host "✅ Payment Service started" -ForegroundColor Green

# Wait for all services
Write-Host "`n⏳ Waiting for all services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container status
Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test all services
Write-Host "`n🔍 Testing all services..." -ForegroundColor Cyan
$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:8080" },
    @{ Name = "Eureka Server"; Url = "http://localhost:8761" },
    @{ Name = "Auth Service"; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "User Service"; Url = "http://localhost:8083/actuator/health" },
    @{ Name = "Ride Service"; Url = "http://localhost:8082/actuator/health" },
    @{ Name = "Fleet Service"; Url = "http://localhost:8084/actuator/health" },
    @{ Name = "Location Service"; Url = "http://localhost:8085/actuator/health" },
    @{ Name = "Payment Service"; Url = "http://localhost:8086/actuator/health" }
)

$healthyCount = 0
foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "  ✅ $($service.Name): Healthy" -ForegroundColor Green
            $healthyCount++
        }
    } catch {
        Write-Host "  ❌ $($service.Name): Not responding" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📈 Final Summary:" -ForegroundColor Cyan
Write-Host "  Total Services: $($services.Count)" -ForegroundColor White
Write-Host "  Healthy Services: $healthyCount" -ForegroundColor Green
Write-Host "  Failed Services: $($services.Count - $healthyCount)" -ForegroundColor Red

# Service URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • API Gateway:      http://localhost:8080" -ForegroundColor White
Write-Host "  • Eureka Server:    http://localhost:8761" -ForegroundColor White
Write-Host "  • Auth Service:     http://localhost:8081/actuator/health" -ForegroundColor White
Write-Host "  • User Service:     http://localhost:8083/actuator/health" -ForegroundColor White
Write-Host "  • Ride Service:     http://localhost:8082/actuator/health" -ForegroundColor White
Write-Host "  • Fleet Service:    http://localhost:8084/actuator/health" -ForegroundColor White
Write-Host "  • Location Service: http://localhost:8085/actuator/health" -ForegroundColor White
Write-Host "  • Payment Service:  http://localhost:8086/actuator/health" -ForegroundColor White

Write-Host "`n🎉 TecnoDrive Platform Setup Complete!" -ForegroundColor Green
