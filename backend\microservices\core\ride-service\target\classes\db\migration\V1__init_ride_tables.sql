-- TECNO DRIVE Ride Service Database Schema
-- Version 1.0 - Initial schema creation

-- Enable UUID and PostGIS extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS postgis;

-- Vehicle types table
CREATE TABLE vehicle_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    description TEXT,
    base_fare DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    per_km_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    per_minute_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    capacity INTEGER NOT NULL DEFAULT 4,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fare structures table for dynamic pricing
CREATE TABLE fare_structures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_type_id UUID REFERENCES vehicle_types(id),
    time_slot_start TIME,
    time_slot_end TIME,
    day_of_week INTEGER, -- 1=Monday, 7=Sunday
    surge_multiplier DECIMAL(3,2) DEFAULT 1.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Rides table
CREATE TABLE rides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    passenger_id UUID NOT NULL,
    driver_id UUID,
    vehicle_type_id UUID REFERENCES vehicle_types(id),
    
    -- Location data using PostGIS
    pickup_location GEOMETRY(POINT, 4326) NOT NULL,
    pickup_address TEXT NOT NULL,
    destination_location GEOMETRY(POINT, 4326) NOT NULL,
    destination_address TEXT NOT NULL,
    
    -- Ride details
    status VARCHAR(50) NOT NULL DEFAULT 'requested' CHECK (status IN (
        'requested', 'driver_assigned', 'driver_arrived', 'in_progress', 
        'completed', 'cancelled_by_passenger', 'cancelled_by_driver', 'cancelled_by_system'
    )),
    ride_type VARCHAR(20) DEFAULT 'standard' CHECK (ride_type IN ('standard', 'scheduled', 'shared')),
    
    -- Pricing
    estimated_fare DECIMAL(10,2),
    final_fare DECIMAL(10,2),
    surge_multiplier DECIMAL(3,2) DEFAULT 1.00,
    
    -- Timing
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scheduled_at TIMESTAMP,
    driver_assigned_at TIMESTAMP,
    driver_arrived_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    
    -- Distance and duration
    estimated_distance_km DECIMAL(8,2),
    actual_distance_km DECIMAL(8,2),
    estimated_duration_minutes INTEGER,
    actual_duration_minutes INTEGER,
    
    -- Additional info
    passenger_notes TEXT,
    cancellation_reason TEXT,
    rating_by_passenger INTEGER CHECK (rating_by_passenger >= 1 AND rating_by_passenger <= 5),
    rating_by_driver INTEGER CHECK (rating_by_driver >= 1 AND rating_by_driver <= 5),
    
    -- Multi-tenant support
    company_id UUID,
    school_id UUID,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride status history for tracking
CREATE TABLE ride_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ride_id UUID NOT NULL REFERENCES rides(id) ON DELETE CASCADE,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by UUID, -- user_id who made the change
    change_reason TEXT,
    location GEOMETRY(POINT, 4326),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Scheduled rides table
CREATE TABLE scheduled_rides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ride_id UUID NOT NULL REFERENCES rides(id) ON DELETE CASCADE,
    recurrence_type VARCHAR(20) CHECK (recurrence_type IN ('once', 'daily', 'weekly', 'monthly')),
    recurrence_days INTEGER[], -- Array of days for weekly recurrence
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Driver ride requests (for matching algorithm)
CREATE TABLE driver_ride_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ride_id UUID NOT NULL REFERENCES rides(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL,
    request_sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_received_at TIMESTAMP,
    response VARCHAR(20) CHECK (response IN ('accepted', 'declined', 'timeout')),
    decline_reason TEXT,
    distance_to_pickup_km DECIMAL(8,2),
    estimated_arrival_minutes INTEGER
);

-- Ride sharing groups
CREATE TABLE ride_sharing_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL,
    vehicle_type_id UUID REFERENCES vehicle_types(id),
    max_passengers INTEGER DEFAULT 4,
    current_passengers INTEGER DEFAULT 0,
    route_start_location GEOMETRY(POINT, 4326),
    route_end_location GEOMETRY(POINT, 4326),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'full', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride sharing participants
CREATE TABLE ride_sharing_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES ride_sharing_groups(id) ON DELETE CASCADE,
    ride_id UUID NOT NULL REFERENCES rides(id) ON DELETE CASCADE,
    pickup_order INTEGER,
    dropoff_order INTEGER,
    fare_share DECIMAL(10,2),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_rides_passenger_id ON rides(passenger_id);
CREATE INDEX idx_rides_driver_id ON rides(driver_id);
CREATE INDEX idx_rides_status ON rides(status);
CREATE INDEX idx_rides_requested_at ON rides(requested_at);
CREATE INDEX idx_rides_company_id ON rides(company_id);
CREATE INDEX idx_rides_school_id ON rides(school_id);
CREATE INDEX idx_rides_pickup_location ON rides USING GIST(pickup_location);
CREATE INDEX idx_rides_destination_location ON rides USING GIST(destination_location);

CREATE INDEX idx_ride_status_history_ride_id ON ride_status_history(ride_id);
CREATE INDEX idx_ride_status_history_created_at ON ride_status_history(created_at);

CREATE INDEX idx_driver_ride_requests_ride_id ON driver_ride_requests(ride_id);
CREATE INDEX idx_driver_ride_requests_driver_id ON driver_ride_requests(driver_id);
CREATE INDEX idx_driver_ride_requests_request_sent_at ON driver_ride_requests(request_sent_at);

CREATE INDEX idx_scheduled_rides_ride_id ON scheduled_rides(ride_id);
CREATE INDEX idx_scheduled_rides_recurrence_type ON scheduled_rides(recurrence_type);

CREATE INDEX idx_vehicle_types_is_active ON vehicle_types(is_active);
CREATE INDEX idx_fare_structures_vehicle_type_id ON fare_structures(vehicle_type_id);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rides_updated_at BEFORE UPDATE ON rides FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicle_types_updated_at BEFORE UPDATE ON vehicle_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
