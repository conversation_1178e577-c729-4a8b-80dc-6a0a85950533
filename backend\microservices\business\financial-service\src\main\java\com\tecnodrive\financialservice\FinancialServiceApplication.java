package com.tecnodrive.financialservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Financial Service Application
 *
 * This service handles:
 * - Financial transaction logging and tracking
 * - Invoice generation and management
 * - Expense tracking and categorization
 * - Budget planning and monitoring
 * - Financial reporting and analytics
 * - Revenue and profit calculations
 * - Tax calculation and compliance
 * - Financial audit trails
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class FinancialServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinancialServiceApplication.class, args);
    }
}
