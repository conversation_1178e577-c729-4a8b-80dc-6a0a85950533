# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
gunicorn>=21.2.0

# Database
asyncpg>=0.30.0
sqlalchemy>=2.0.25
alembic>=1.13.0
psycopg2-binary>=2.9.9

# Redis
redis>=5.0.0
aioredis>=2.0.1

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
cryptography>=42.0.0

# Data Processing
pandas>=2.1.0
numpy>=1.26.0
scipy>=1.11.0

# Machine Learning
tensorflow>=2.15.0
scikit-learn>=1.4.0
joblib>=1.3.0

# HTTP Client
httpx>=0.26.0
aiohttp>=3.9.0

# Validation
pydantic>=2.5.0
email-validator>=2.1.0

# Configuration
python-dotenv>=1.0.0
pydantic-settings>=2.1.0

# Logging
structlog>=23.2.0
rich>=13.7.0

# Monitoring
prometheus-client>=0.19.0
opentelemetry-api>=1.22.0
opentelemetry-sdk>=1.22.0

# WebSocket
websockets>=12.0

# Background Tasks
celery>=5.3.0
flower>=2.0.1

# Time Series
influxdb-client>=1.40.0

# Geospatial
geopy>=2.4.0
shapely>=2.0.2

# Testing
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0

# Development
black>=23.12.0
isort>=5.13.0
flake8>=7.0.0
mypy>=1.8.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.5.0
