package com.tecnodrive.locationservice.controller;

import com.tecnodrive.locationservice.service.AdvancedMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Advanced Interactive Street Map Controller
 * Provides sophisticated mapping and analytics capabilities
 */
@RestController
@RequestMapping("/api/advanced-map")
@CrossOrigin(origins = "*")
public class AdvancedMapController {

    private static final Logger log = LoggerFactory.getLogger(AdvancedMapController.class);

    @Autowired
    private AdvancedMapService advancedMapService;

    /**
     * Update street segment with advanced traffic data
     */
    @PostMapping("/street-segment/update")
    public ResponseEntity<Map<String, Object>> updateStreetSegment(@RequestBody Map<String, Object> request) {
        try {
            String segmentId = (String) request.get("segmentId");
            String streetName = (String) request.get("streetName");
            double startLat = ((Number) request.get("startLat")).doubleValue();
            double startLng = ((Number) request.get("startLng")).doubleValue();
            double endLat = ((Number) request.get("endLat")).doubleValue();
            double endLng = ((Number) request.get("endLng")).doubleValue();
            @SuppressWarnings("unchecked")
            Map<String, Object> trafficData = (Map<String, Object>) request.getOrDefault("trafficData", Map.of());
            @SuppressWarnings("unchecked")
            Map<String, Object> roadConditions = (Map<String, Object>) request.getOrDefault("roadConditions", Map.of());

            advancedMapService.updateStreetSegment(segmentId, streetName, startLat, startLng, 
                                                 endLat, endLng, trafficData, roadConditions);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Street segment updated successfully",
                "segmentId", segmentId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error updating street segment: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to update street segment: " + e.getMessage()
            ));
        }
    }

    /**
     * Generate real-time demand heatmap
     */
    @PostMapping("/heatmap/demand")
    public ResponseEntity<Map<String, Object>> generateDemandHeatmap(@RequestBody Map<String, Object> request) {
        try {
            double centerLat = ((Number) request.get("centerLat")).doubleValue();
            double centerLng = ((Number) request.get("centerLng")).doubleValue();
            double radiusKm = ((Number) request.getOrDefault("radiusKm", 10)).doubleValue();
            String timeWindow = (String) request.getOrDefault("timeWindow", "1h");
            String demandType = (String) request.getOrDefault("demandType", "rides");

            advancedMapService.generateDemandHeatmap(centerLat, centerLng, radiusKm, timeWindow, demandType);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Demand heatmap generated successfully",
                "center", Map.of("lat", centerLat, "lng", centerLng),
                "demandType", demandType,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error generating demand heatmap: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to generate heatmap: " + e.getMessage()
            ));
        }
    }

    /**
     * Advanced route optimization
     */
    @PostMapping("/route/optimize")
    public ResponseEntity<Map<String, Object>> optimizeRoute(@RequestBody Map<String, Object> request) {
        try {
            String routeId = (String) request.get("routeId");
            String vehicleId = (String) request.get("vehicleId");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> waypoints = (List<Map<String, Object>>) request.get("waypoints");
            @SuppressWarnings("unchecked")
            Map<String, Object> constraints = (Map<String, Object>) request.getOrDefault("constraints", Map.of());
            String optimizationType = (String) request.getOrDefault("optimizationType", "time");

            advancedMapService.optimizeRoute(routeId, vehicleId, waypoints, constraints, optimizationType);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Route optimization completed",
                "routeId", routeId,
                "optimizationType", optimizationType,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error optimizing route: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to optimize route: " + e.getMessage()
            ));
        }
    }

    /**
     * Analyze traffic patterns
     */
    @PostMapping("/traffic/analyze")
    public ResponseEntity<Map<String, Object>> analyzeTrafficPatterns(@RequestBody Map<String, Object> request) {
        try {
            String zoneId = (String) request.get("zoneId");
            String startTimeStr = (String) request.get("startTime");
            String endTimeStr = (String) request.get("endTime");
            String analysisType = (String) request.getOrDefault("analysisType", "congestion");

            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            advancedMapService.analyzeTrafficPatterns(zoneId, startTime, endTime, analysisType);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Traffic pattern analysis completed",
                "zoneId", zoneId,
                "analysisType", analysisType,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error analyzing traffic patterns: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to analyze traffic patterns: " + e.getMessage()
            ));
        }
    }

    /**
     * Update driver performance on map
     */
    @PostMapping("/driver/performance")
    public ResponseEntity<Map<String, Object>> updateDriverPerformance(@RequestBody Map<String, Object> request) {
        try {
            String driverId = (String) request.get("driverId");
            String vehicleId = (String) request.get("vehicleId");
            @SuppressWarnings("unchecked")
            Map<String, Object> performanceMetrics = (Map<String, Object>) request.get("performanceMetrics");
            @SuppressWarnings("unchecked")
            Map<String, Object> locationData = (Map<String, Object>) request.get("locationData");

            advancedMapService.updateDriverPerformance(driverId, vehicleId, performanceMetrics, locationData);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Driver performance updated successfully",
                "driverId", driverId,
                "vehicleId", vehicleId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error updating driver performance: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to update driver performance: " + e.getMessage()
            ));
        }
    }

    /**
     * Process zone-based alerts
     */
    @PostMapping("/zone/alert")
    public ResponseEntity<Map<String, Object>> processZoneAlert(@RequestBody Map<String, Object> request) {
        try {
            String zoneId = (String) request.get("zoneId");
            String vehicleId = (String) request.get("vehicleId");
            String alertType = (String) request.get("alertType");
            @SuppressWarnings("unchecked")
            Map<String, Object> alertData = (Map<String, Object>) request.get("alertData");
            String priority = (String) request.getOrDefault("priority", "medium");

            advancedMapService.processZoneAlert(zoneId, vehicleId, alertType, alertData, priority);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Zone alert processed successfully",
                "zoneId", zoneId,
                "alertType", alertType,
                "priority", priority,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error processing zone alert: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to process zone alert: " + e.getMessage()
            ));
        }
    }

    /**
     * Get advanced multi-layer map data
     */
    @GetMapping("/data/advanced")
    public ResponseEntity<Map<String, Object>> getAdvancedMapData(
            @RequestParam double centerLat,
            @RequestParam double centerLng,
            @RequestParam(defaultValue = "10") double radiusKm,
            @RequestParam List<String> layers,
            @RequestParam(defaultValue = "1h") String timeWindow) {
        try {
            Map<String, Object> mapData = advancedMapService.getAdvancedMapData(
                centerLat, centerLng, radiusKm, layers, timeWindow);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", mapData,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting advanced map data: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get advanced map data: " + e.getMessage()
            ));
        }
    }

    /**
     * Synchronize map view across clients
     */
    @PostMapping("/sync/view")
    public ResponseEntity<Map<String, Object>> synchronizeMapView(@RequestBody Map<String, Object> request) {
        try {
            String sessionId = (String) request.get("sessionId");
            @SuppressWarnings("unchecked")
            Map<String, Object> viewState = (Map<String, Object>) request.get("viewState");
            @SuppressWarnings("unchecked")
            List<String> subscribedLayers = (List<String>) request.getOrDefault("subscribedLayers", List.of());

            advancedMapService.synchronizeMapView(sessionId, viewState, subscribedLayers);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Map view synchronized successfully",
                "sessionId", sessionId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error synchronizing map view: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to synchronize map view: " + e.getMessage()
            ));
        }
    }

    /**
     * Get available map layers and their capabilities
     */
    @GetMapping("/layers/capabilities")
    public ResponseEntity<Map<String, Object>> getMapLayerCapabilities() {
        try {
            Map<String, Object> capabilities = Map.of(
                "availableLayers", List.of(
                    "street_segments",
                    "traffic_patterns", 
                    "demand_heatmap",
                    "driver_performance",
                    "route_optimizations",
                    "historical_data"
                ),
                "features", Map.of(
                    "realTimeUpdates", true,
                    "historicalAnalysis", true,
                    "predictiveAnalytics", true,
                    "multiLayerSupport", true,
                    "collaborativeViewing", true,
                    "advancedFiltering", true
                ),
                "supportedAnalytics", List.of(
                    "traffic_congestion",
                    "demand_forecasting",
                    "route_optimization",
                    "driver_behavior",
                    "zone_analysis",
                    "performance_metrics"
                ),
                "updateFrequency", Map.of(
                    "realTime", "5 seconds",
                    "analytics", "1 minute",
                    "historical", "1 hour"
                )
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "capabilities", capabilities,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting map layer capabilities: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get capabilities: " + e.getMessage()
            ));
        }
    }

    /**
     * Get real-time map statistics
     */
    @GetMapping("/stats/realtime")
    public ResponseEntity<Map<String, Object>> getRealtimeMapStats() {
        try {
            Map<String, Object> stats = Map.of(
                "activeVehicles", 125,
                "activeRoutes", 45,
                "trafficIncidents", 3,
                "averageSpeed", 52.5,
                "congestionLevel", "moderate",
                "demandHotspots", 8,
                "optimizedRoutes", 23,
                "performanceAlerts", 5,
                "lastUpdate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "stats", stats,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("Error getting realtime map stats: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get stats: " + e.getMessage()
            ));
        }
    }
}
