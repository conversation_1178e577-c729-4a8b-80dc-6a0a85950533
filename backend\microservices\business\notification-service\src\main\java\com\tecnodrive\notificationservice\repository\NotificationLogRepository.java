package com.tecnodrive.notificationservice.repository;

import com.tecnodrive.notificationservice.entity.NotificationLog;
import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Notification Log Repository
 * 
 * Data access layer for NotificationLog entities
 */
@Repository
public interface NotificationLogRepository extends JpaRepository<NotificationLog, UUID> {

    /**
     * Find logs by user ID
     */
    Page<NotificationLog> findByUserId(String userId, Pageable pageable);

    /**
     * Find logs by user ID (simple list)
     */
    List<NotificationLog> findByUserId(String userId);

    /**
     * Find logs by template name
     */
    List<NotificationLog> findByTemplateName(String templateName);

    /**
     * Find logs by channel
     */
    List<NotificationLog> findByChannel(NotificationTemplate.NotificationChannel channel);

    /**
     * Find logs by status
     */
    List<NotificationLog> findByStatus(NotificationLog.DeliveryStatus status);

    /**
     * Find logs by tenant
     */
    List<NotificationLog> findByTenantId(String tenantId);

    /**
     * Find logs by user and status
     */
    List<NotificationLog> findByUserIdAndStatus(String userId, NotificationLog.DeliveryStatus status);

    /**
     * Find logs by date range
     */
    @Query("SELECT l FROM NotificationLog l WHERE l.sentAt BETWEEN :startDate AND :endDate")
    List<NotificationLog> findLogsBetweenDates(
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find failed logs for retry
     */
    @Query("SELECT l FROM NotificationLog l WHERE l.status = 'FAILED' AND l.attemptCount < :maxAttempts")
    List<NotificationLog> findFailedLogsForRetry(@Param("maxAttempts") int maxAttempts);

    /**
     * Find logs by external message ID
     */
    List<NotificationLog> findByExternalMessageId(String externalMessageId);

    /**
     * Count logs by status
     */
    long countByStatus(NotificationLog.DeliveryStatus status);

    /**
     * Count logs by channel
     */
    long countByChannel(NotificationTemplate.NotificationChannel channel);

    /**
     * Count logs by user and date range
     */
    @Query("SELECT COUNT(l) FROM NotificationLog l WHERE l.userId = :userId AND l.sentAt BETWEEN :startDate AND :endDate")
    long countByUserIdAndDateRange(
            @Param("userId") String userId,
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find recent logs by user
     */
    @Query("SELECT l FROM NotificationLog l WHERE l.userId = :userId ORDER BY l.sentAt DESC")
    Page<NotificationLog> findRecentLogsByUser(@Param("userId") String userId, Pageable pageable);

    /**
     * Get delivery statistics
     */
    @Query("SELECT l.status, COUNT(l) FROM NotificationLog l GROUP BY l.status")
    List<Object[]> getDeliveryStatistics();

    /**
     * Get channel statistics
     */
    @Query("SELECT l.channel, COUNT(l) FROM NotificationLog l GROUP BY l.channel")
    List<Object[]> getChannelStatistics();

    /**
     * Delete old logs (for cleanup)
     */
    @Query("DELETE FROM NotificationLog l WHERE l.sentAt < :cutoffDate")
    void deleteOldLogs(@Param("cutoffDate") Instant cutoffDate);
}
