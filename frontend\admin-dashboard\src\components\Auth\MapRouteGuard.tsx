import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Box, CircularProgress, Typography } from '@mui/material';
import { RootState } from '../../store/store';
import { setCredentials } from '../../store/slices/authSlice';
import { 
  isAuthenticated, 
  getCurrentUser, 
  getAuthToken,
  extendSession 
} from '../../services/persistentAuthService';

interface MapRouteGuardProps {
  children: React.ReactNode;
}

const MapRouteGuard: React.FC<MapRouteGuardProps> = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { user, token } = useSelector((state: RootState) => state.auth);
  
  const [loading, setLoading] = useState(true);
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      console.log('🗺️ MapRouteGuard: Checking authentication for map routes...');
      
      try {
        // Check if user is already authenticated in Redux
        if (user && token) {
          console.log('✅ MapRouteGuard: User already authenticated in Redux');
          setLoading(false);
          setAuthChecked(true);
          return;
        }

        // Check persistent storage without auto-redirect
        if (isAuthenticated()) {
          const currentUser = getCurrentUser();
          const authToken = getAuthToken();
          
          if (currentUser && authToken) {
            console.log('✅ MapRouteGuard: Found valid auth data in storage');
            
            // Update Redux state
            dispatch(setCredentials({
              user: currentUser,
              token: authToken
            }));
            
            // Extend session if needed
            await extendSession();
            
            setLoading(false);
            setAuthChecked(true);
            return;
          }
        }

        // No valid authentication found - redirect to login
        console.log('❌ MapRouteGuard: No valid authentication found');
        
      } catch (error) {
        console.error('❌ MapRouteGuard: Authentication check failed:', error);
      } finally {
        setLoading(false);
        setAuthChecked(true);
      }
    };

    if (!authChecked) {
      checkAuthentication();
    }
  }, [dispatch, user, token, authChecked]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="50vh"
        sx={{ p: 3 }}
      >
        <CircularProgress size={40} sx={{ mb: 2 }} />
        <Typography variant="body1" sx={{ textAlign: 'center' }}>
          جاري التحقق من صلاحية الوصول للخرائط...
        </Typography>
      </Box>
    );
  }

  // Check if user is authenticated
  const userAuthenticated = user && token && isAuthenticated();
  
  if (!userAuthenticated) {
    console.log('❌ MapRouteGuard: User not authenticated, redirecting to login');
    
    // Redirect to login with return URL
    return (
      <Navigate 
        to="/login" 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If we reach here, user is authenticated
  console.log('✅ MapRouteGuard: Access granted to map routes');
  return <>{children}</>;
};

export default MapRouteGuard;
