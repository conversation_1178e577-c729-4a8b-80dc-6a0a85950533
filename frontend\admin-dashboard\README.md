# TECNO DRIVE - Admin Dashboard

لوحة التحكم الإدارية لنظام تكنو درايف للنقل الذكي.

## المميزات الجديدة ✨

- **إدارة الموارد البشرية الشاملة** - نظام HR متكامل مع إدارة الموظفين والأقسام والصلاحيات
- **استيراد وتصدير البيانات** - دعم ملفات Excel للاستيراد والتصدير
- **الإدارة التقنية** - قسم خاص للفرق التقنية والمشاريع
- **نظام الإعدادات المتقدم** - إعدادات شاملة للنظام والأمان
- **تسجيل الدخول المحسن** - نظام مصادقة مع المستخدم الرئيسي

## بيانات تسجيل الدخول 🔐

**المستخدم الرئيسي (المدير العام):**
- اسم المستخدم: `Azal`
- كلمة المرور: `password tecno`

## المميزات الأساسية

- إدارة الرحلات والحجوزات مع استيراد/تصدير Excel
- إدارة الأسطول والمركبات
- إدارة المستخدمين والسائقين
- إدارة المدفوعات والفواتير
- تحليلات وتقارير شاملة
- إدارة الإشعارات
- إدارة الطرود والتوصيل
- نظام SaaS متعدد المستأجرين
- بوابة API موحدة
- دمج Oracle APEX
- نظام مصادقة متعدد

## التقنيات المستخدمة

- React 18 مع TypeScript
- Material-UI (MUI) للواجهات
- Redux Toolkit لإدارة الحالة
- React Router للتنقل
- Recharts للمخططات البيانية
- Axios للاتصال بـ APIs
- XLSX لمعالجة ملفات Excel
- File-saver لتحميل الملفات

## تعديل عناوين الخدمات 🔧

### الطريقة الأولى: ملف .env
قم بتعديل الملف `.env` في جذر المشروع:

```bash
# تعديل عناوين الخدمات حسب إعداد الخوادم
REACT_APP_RIDE_SERVICE_URL=http://your-server:8081
REACT_APP_FLEET_SERVICE_URL=http://your-server:8082
REACT_APP_AUTH_SERVICE_URL=http://your-server:8083
# ... باقي الخدمات
```

### الطريقة الثانية: واجهة الإعدادات
1. اذهب إلى "حالة الخدمات" في لوحة التحكم
2. اضغط "إعدادات الخدمات"
3. عدّل عناوين الخدمات حسب الحاجة
4. اضغط "حفظ وفحص الخدمات"

### وضع المحاكاة للتطوير
- فعّل "وضع المحاكاة" لاستخدام بيانات تجريبية
- مفيد للتطوير والاختبار بدون خوادم حقيقية
- يمكن تفعيله من واجهة حالة الخدمات

## حالة الخدمات 🟢

النظام يدعم 9 خدمات رئيسية:
- 🔧 خدمة الرحلات (localhost:8081)
- 🔧 خدمة الأسطول (localhost:8082)
- 🔧 خدمة المصادقة (localhost:8083)
- 🔧 خدمة المدفوعات (localhost:8084)
- 🔧 خدمة التحليلات (localhost:8085)
- 🔧 خدمة الإشعارات (localhost:8086)
- 🔧 خدمة الموارد البشرية (localhost:8087)
- 🔧 خدمة الطرود (localhost:8088)
- 🔧 خدمة المستخدمين (localhost:8089)

**ملاحظة:** يمكن تعديل عناوين الخدمات من الإعدادات أو استخدام وضع المحاكاة للتطوير.

## Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
