package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Instant;

/**
 * Notification Template Response DTO
 * 
 * Used for returning notification template information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationTemplateResponse {

    /**
     * Template ID
     */
    private String id;

    /**
     * Unique template identifier
     */
    private String templateName;

    /**
     * Template display name
     */
    private String displayName;

    /**
     * Template description
     */
    private String description;

    /**
     * Notification channel
     */
    private NotificationTemplate.NotificationChannel channel;

    /**
     * Template category for organization
     */
    private String category;

    /**
     * Subject template (for email/SMS)
     */
    private String subjectTemplate;

    /**
     * Message body template
     */
    private String bodyTemplate;

    /**
     * Template variables (JSON format)
     */
    private String templateVariables;

    /**
     * Template priority
     */
    private NotificationTemplate.NotificationPriority priority;

    /**
     * Template status
     */
    private boolean isActive;

    /**
     * Template version
     */
    private Integer version;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;

    /**
     * Creation timestamp
     */
    private Instant createdAt;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;
}
