package com.tecnodrive.notificationservice.dto;

import com.tecnodrive.notificationservice.entity.UserNotificationPreference;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * Notification Preference Request DTO
 * 
 * Used for updating user notification preferences
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationPreferenceRequest {

    /**
     * Global notification settings
     */
    private Boolean enableNotifications;

    /**
     * Channel-specific preferences
     */
    private Boolean enableEmail;
    private Boolean enableSms;
    private Boolean enablePush;
    private Boolean enableInApp;

    /**
     * Category-specific preferences
     */
    private Boolean enableRideNotifications;
    private Boolean enableDeliveryNotifications;
    private Boolean enablePaymentNotifications;
    private Boolean enablePromotionalNotifications;
    private Boolean enableSecurityNotifications;

    /**
     * Time-based preferences
     */
    private Boolean enableNightTimeNotifications;

    /**
     * Quiet hours start (24-hour format, e.g., 22 for 10 PM)
     */
    @Min(value = 0, message = "Quiet hours start must be between 0 and 23")
    @Max(value = 23, message = "Quiet hours start must be between 0 and 23")
    private Integer quietHoursStart;

    /**
     * Quiet hours end (24-hour format, e.g., 8 for 8 AM)
     */
    @Min(value = 0, message = "Quiet hours end must be between 0 and 23")
    @Max(value = 23, message = "Quiet hours end must be between 0 and 23")
    private Integer quietHoursEnd;

    /**
     * Timezone for quiet hours
     */
    private String timezone;

    /**
     * Frequency preferences
     */
    private UserNotificationPreference.NotificationFrequency emailFrequency;
    private UserNotificationPreference.NotificationFrequency smsFrequency;

    /**
     * Language preference for notifications
     */
    @Size(min = 2, max = 5, message = "Language code must be 2-5 characters")
    private String language;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;
}
