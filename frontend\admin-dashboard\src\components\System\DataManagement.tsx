import React, { useState } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  Card,
  Card<PERSON>ontent,
  Button,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  Description as FileIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import ExcelService from '../../services/excelService';

interface ImportResult {
  success: boolean;
  message: string;
  data?: any[];
  errors?: string[];
}

const DataManagement: React.FC = () => {
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Mock data for demonstration
  const mockEmployees = [
    { id: '1', name: 'أحمد محمد', email: '<EMAIL>', department: 'الإدارة العامة' },
    { id: '2', name: 'فاطمة علي', email: '<EMAIL>', department: 'الإدارة المالية' },
  ];

  const mockRides = [
    { id: 'R001', passengerName: 'محمد أحمد', driverName: 'علي سالم', fare: 25.50 },
    { id: 'R002', passengerName: 'سارة محمد', driverName: 'أحمد علي', fare: 18.75 },
  ];

  const mockInvoices = [
    { id: 'INV-001', customerName: 'شركة النقل', amount: 25000, status: 'مدفوعة' },
    { id: 'INV-002', customerName: 'مؤسسة التوصيل', amount: 18500, status: 'معلقة' },
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (ExcelService.validateExcelFile(file)) {
        setSelectedFile(file);
        setImportResult(null);
      } else {
        setImportResult({
          success: false,
          message: 'يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)',
        });
      }
    }
  };

  const handleImportEmployees = async () => {
    if (!selectedFile) return;

    setLoading(true);
    try {
      const employees = await ExcelService.importEmployees(selectedFile);
      setImportResult({
        success: true,
        message: `تم استيراد ${employees.length} موظف بنجاح`,
        data: employees,
      });
    } catch (error) {
      setImportResult({
        success: false,
        message: error instanceof Error ? error.message : 'فشل في استيراد البيانات',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportEmployees = () => {
    try {
      ExcelService.exportEmployees(mockEmployees);
      setExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleExportRides = () => {
    try {
      ExcelService.exportRides(mockRides);
      setExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleExportFinancial = () => {
    try {
      ExcelService.exportFinancialData(mockInvoices, []);
      setExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleDownloadTemplate = () => {
    ExcelService.downloadEmployeeTemplate();
  };

  const exportOptions = [
    {
      title: 'بيانات الموظفين',
      description: 'تصدير جميع بيانات الموظفين والأقسام',
      icon: <FileIcon />,
      action: handleExportEmployees,
      count: mockEmployees.length,
    },
    {
      title: 'بيانات الرحلات',
      description: 'تصدير جميع بيانات الرحلات والسائقين',
      icon: <FileIcon />,
      action: handleExportRides,
      count: mockRides.length,
    },
    {
      title: 'البيانات المالية',
      description: 'تصدير الفواتير والمدفوعات',
      icon: <FileIcon />,
      action: handleExportFinancial,
      count: mockInvoices.length,
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        إدارة البيانات
      </Typography>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                استيراد البيانات
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                استيراد البيانات من ملفات Excel
              </Typography>
              <Button
                variant="contained"
                onClick={() => setImportDialogOpen(true)}
                fullWidth
              >
                استيراد البيانات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DownloadIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                تصدير البيانات
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                تصدير البيانات إلى ملفات Excel
              </Typography>
              <Button
                variant="contained"
                color="success"
                onClick={() => setExportDialogOpen(true)}
                fullWidth
              >
                تصدير البيانات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <BackupIcon sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                النسخ الاحتياطي
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                إنشاء واستعادة النسخ الاحتياطية
              </Typography>
              <Button
                variant="contained"
                color="warning"
                fullWidth
              >
                إدارة النسخ الاحتياطية
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Data Statistics */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            إحصائيات البيانات
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {mockEmployees.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الموظفين
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {mockRides.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الرحلات
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {mockInvoices.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الفواتير
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  4
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الأقسام
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            العمليات الأخيرة
          </Typography>
          <List>
            <ListItem>
              <ListItemIcon>
                <SuccessIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="تم تصدير بيانات الموظفين"
                secondary="منذ ساعتين"
              />
              <Chip label="مكتمل" color="success" size="small" />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="info" />
              </ListItemIcon>
              <ListItemText
                primary="تم إنشاء نسخة احتياطية"
                secondary="منذ 6 ساعات"
              />
              <Chip label="مكتمل" color="info" size="small" />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <SuccessIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary="تم استيراد بيانات الرحلات"
                secondary="أمس"
              />
              <Chip label="مكتمل" color="success" size="small" />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onClose={() => setImportDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>استيراد البيانات من Excel</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              يمكنك استيراد بيانات الموظفين من ملف Excel. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
            </Alert>
            
            <Button
              variant="outlined"
              onClick={handleDownloadTemplate}
              sx={{ mb: 2 }}
              startIcon={<DownloadIcon />}
            >
              تحميل قالب Excel
            </Button>
          </Box>

          <Box sx={{ mb: 3 }}>
            <input
              accept=".xlsx,.xls"
              style={{ display: 'none' }}
              id="excel-file-input"
              type="file"
              onChange={handleFileSelect}
            />
            <label htmlFor="excel-file-input">
              <Button
                variant="contained"
                component="span"
                startIcon={<UploadIcon />}
                fullWidth
              >
                اختيار ملف Excel
              </Button>
            </label>
          </Box>

          {selectedFile && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">
                الملف المحدد: {selectedFile.name}
              </Typography>
            </Box>
          )}

          {loading && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress />
              <Typography variant="body2" sx={{ mt: 1 }}>
                جاري استيراد البيانات...
              </Typography>
            </Box>
          )}

          {importResult && (
            <Alert severity={importResult.success ? 'success' : 'error'} sx={{ mb: 2 }}>
              {importResult.message}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImportDialogOpen(false)}>إلغاء</Button>
          <Button
            variant="contained"
            onClick={handleImportEmployees}
            disabled={!selectedFile || loading}
          >
            استيراد البيانات
          </Button>
        </DialogActions>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تصدير البيانات إلى Excel</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 3 }}>
            اختر نوع البيانات التي تريد تصديرها:
          </Typography>
          
          <List>
            {exportOptions.map((option, index) => (
              <React.Fragment key={index}>
                <ListItem>
                  <ListItemIcon>
                    {option.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={option.title}
                    secondary={`${option.description} (${option.count} عنصر)`}
                  />
                  <Button
                    variant="outlined"
                    onClick={option.action}
                    startIcon={<DownloadIcon />}
                  >
                    تصدير
                  </Button>
                </ListItem>
                {index < exportOptions.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DataManagement;
