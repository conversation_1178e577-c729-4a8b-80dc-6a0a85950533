// SaaS Management Types and Interfaces

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  phone: string;
  email: string;
  address?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  createdAt: string;
  updatedAt: string;
  subscriptionsCount: number;
  totalUsers: number;
  billingBalance: number;
}

export interface CreateTenantRequest {
  name: string;
  domain: string;
  phone: string;
  email: string;
  address?: string;
  status?: 'ACTIVE' | 'INACTIVE';
}

export interface UpdateTenantRequest {
  name?: string;
  domain?: string;
  phone?: string;
  email?: string;
  address?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
}

export interface Subscription {
  id: string;
  tenantId: string;
  planId: string;
  planName: string;
  planType: 'BASIC' | 'PREMIUM' | 'ENTERPRISE' | 'CUSTOM';
  startDate: string;
  endDate: string;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'PENDING';
  seats: number;
  usedSeats: number;
  monthlyPrice: number;
  yearlyPrice: number;
  billingCycle: 'MONTHLY' | 'YEARLY';
  features: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubscriptionRequest {
  tenantId: string;
  planId: string;
  startDate: string;
  endDate: string;
  seats: number;
  billingCycle: 'MONTHLY' | 'YEARLY';
  autoRenew?: boolean;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  endDate?: string;
  seats?: number;
  status?: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
  autoRenew?: boolean;
}

export interface Plan {
  id: string;
  name: string;
  type: 'BASIC' | 'PREMIUM' | 'ENTERPRISE' | 'CUSTOM';
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  maxSeats: number;
  features: PlanFeature[];
  isActive: boolean;
}

export interface PlanFeature {
  id: string;
  name: string;
  description: string;
  included: boolean;
  limit?: number;
}

export interface Invoice {
  id: string;
  tenantId: string;
  subscriptionId: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  items: InvoiceItem[];
  taxAmount: number;
  totalAmount: number;
  paymentMethod?: string;
  notes?: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreateInvoiceRequest {
  tenantId: string;
  subscriptionId: string;
  amount: number;
  dueDate: string;
  items: Omit<InvoiceItem, 'id'>[];
  notes?: string;
}

export interface Payment {
  id: string;
  invoiceId: string;
  tenantId: string;
  amount: number;
  currency: string;
  method: 'CREDIT_CARD' | 'BANK_TRANSFER' | 'PAYPAL' | 'STRIPE' | 'CASH';
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  transactionId?: string;
  processedAt?: string;
  failureReason?: string;
}

export interface UsageMetrics {
  tenantId: string;
  period: string; // YYYY-MM format
  activeUsers: number;
  totalApiCalls: number;
  dataStorage: number; // in GB
  bandwidth: number; // in GB
  features: FeatureUsage[];
  peakUsers: number;
  peakDate: string;
}

export interface FeatureUsage {
  featureName: string;
  usageCount: number;
  limit: number;
  percentage: number;
}

export interface TenantDashboardData {
  tenant: Tenant;
  subscriptions: Subscription[];
  activeSubscriptions: number;
  expiringSoon: Subscription[];
  recentInvoices: Invoice[];
  unpaidInvoices: Invoice[];
  currentUsage: UsageMetrics;
  billingBalance: number;
  nextBillingDate: string;
}

export interface SeatManagement {
  subscriptionId: string;
  totalSeats: number;
  usedSeats: number;
  availableSeats: number;
  users: TenantUser[];
  pendingInvitations: TenantInvitation[];
}

export interface TenantUser {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'USER' | 'VIEWER';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING';
  lastLogin?: string;
  createdAt: string;
}

export interface TenantInvitation {
  id: string;
  email: string;
  role: 'ADMIN' | 'USER' | 'VIEWER';
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED';
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
}

export interface CreateInvitationRequest {
  email: string;
  role: 'ADMIN' | 'USER' | 'VIEWER';
  subscriptionId: string;
}

export interface BillingSettings {
  tenantId: string;
  autoRenew: boolean;
  paymentMethod: string;
  billingEmail: string;
  taxId?: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface NotificationSettings {
  tenantId: string;
  emailNotifications: {
    invoiceGenerated: boolean;
    paymentReceived: boolean;
    subscriptionExpiring: boolean;
    usageLimitReached: boolean;
  };
  smsNotifications: {
    paymentFailed: boolean;
    subscriptionExpired: boolean;
  };
}

// API Response Types
export interface TenantsResponse {
  tenants: Tenant[];
  total: number;
  page: number;
  limit: number;
}

export interface SubscriptionsResponse {
  subscriptions: Subscription[];
  total: number;
  page: number;
  limit: number;
}

export interface InvoicesResponse {
  invoices: Invoice[];
  total: number;
  page: number;
  limit: number;
}

export interface UsageAnalyticsResponse {
  metrics: UsageMetrics[];
  summary: {
    totalUsers: number;
    totalApiCalls: number;
    totalStorage: number;
    averageUsage: number;
  };
}

// Filter and Search Types
export interface TenantFilters {
  search?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'ALL';
  createdFrom?: string;
  createdTo?: string;
  subscriptionType?: string;
}

export interface SubscriptionFilters {
  tenantId?: string;
  status?: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'PENDING' | 'ALL';
  planType?: 'BASIC' | 'PREMIUM' | 'ENTERPRISE' | 'CUSTOM' | 'ALL';
  expiringIn?: number; // days
}

export interface InvoiceFilters {
  tenantId?: string;
  status?: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED' | 'ALL';
  dateFrom?: string;
  dateTo?: string;
  amountFrom?: number;
  amountTo?: number;
}
