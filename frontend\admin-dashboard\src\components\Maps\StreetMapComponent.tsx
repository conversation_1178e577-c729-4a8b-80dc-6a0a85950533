import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Button,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  MyLocation as MyLocationIcon,
  Layers as LayersIcon,
  Error as ErrorIcon,
  Traffic as TrafficIcon,
  Satellite as SatelliteIcon
} from '@mui/icons-material';

// Import Leaflet directly
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix Leaflet default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface StreetMapComponentProps {
  height?: string;
}

// Enhanced map providers for street view
const STREET_MAP_PROVIDERS = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors'
  },
  {
    id: 'openstreetmap-hot',
    name: 'OpenStreetMap HOT',
    url: 'https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors, Tiles style by Humanitarian OpenStreetMap Team'
  },
  {
    id: 'cartodb-positron',
    name: 'CartoDB Positron',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  },
  {
    id: 'cartodb-dark',
    name: 'CartoDB Dark Matter',
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  }
];

const StreetMapComponent: React.FC<StreetMapComponentProps> = ({ height = '600px' }) => {
  // Refs
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const tileLayerRef = useRef<L.TileLayer | null>(null);
  const initializationRef = useRef<boolean>(false);
  
  // State
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');
  const [mapReady, setMapReady] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  const [showTraffic, setShowTraffic] = useState(false);
  const [showSatellite, setShowSatellite] = useState(false);

  // Fixed center for Riyadh
  const center: [number, number] = [24.7136, 46.6753];
  const zoom = 13; // Higher zoom for street level

  // Safe map initialization
  const initializeMap = useCallback(() => {
    if (initializationRef.current || !mapContainerRef.current || mapInstanceRef.current) {
      return;
    }

    try {
      initializationRef.current = true;
      console.log('🗺️ Initializing Street Map...');

      // Clear container
      mapContainerRef.current.innerHTML = '';
      
      // Create unique container ID
      const containerId = `street-map-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      mapContainerRef.current.id = containerId;

      // Create map instance with street-focused settings
      const map = L.map(mapContainerRef.current, {
        center: center,
        zoom: zoom,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        dragging: true,
        touchZoom: true,
        boxZoom: true,
        keyboard: true,
        attributionControl: true,
        maxZoom: 19,
        minZoom: 10
      });

      // Add error handling
      map.on('error', (e) => {
        console.error('❌ Street map error:', e);
        setInitError('خطأ في تحميل خريطة الشوارع');
      });

      // Add tile layer
      const provider = STREET_MAP_PROVIDERS.find(p => p.id === selectedProvider) || STREET_MAP_PROVIDERS[0];
      const tileLayer = L.tileLayer(provider.url, {
        attribution: provider.attribution,
        maxZoom: 19,
        minZoom: 10,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      });

      tileLayer.on('tileerror', (e) => {
        console.warn('⚠️ Street map tile loading error:', e);
      });

      tileLayer.addTo(map);
      tileLayerRef.current = tileLayer;
      mapInstanceRef.current = map;

      console.log('✅ Street map initialization completed');
      setMapReady(true);
      setInitError(null);

    } catch (error) {
      console.error('❌ Street map initialization failed:', error);
      setInitError(error instanceof Error ? error.message : 'فشل في تهيئة خريطة الشوارع');
      initializationRef.current = false;
    }
  }, [selectedProvider]);

  // Initialize map on mount
  useEffect(() => {
    const timer = setTimeout(initializeMap, 100);
    
    return () => {
      clearTimeout(timer);
      
      // Cleanup
      if (mapInstanceRef.current) {
        console.log('🧹 Cleaning up street map...');
        
        // Clear markers
        markersRef.current.forEach(marker => {
          try {
            mapInstanceRef.current?.removeLayer(marker);
          } catch (e) {
            console.warn('Warning cleaning marker:', e);
          }
        });
        markersRef.current = [];
        
        // Remove tile layer
        if (tileLayerRef.current) {
          try {
            mapInstanceRef.current.removeLayer(tileLayerRef.current);
          } catch (e) {
            console.warn('Warning cleaning tile layer:', e);
          }
          tileLayerRef.current = null;
        }
        
        // Remove map
        try {
          mapInstanceRef.current.remove();
        } catch (e) {
          console.warn('Warning removing map:', e);
        }
        
        mapInstanceRef.current = null;
        initializationRef.current = false;
        setMapReady(false);
      }
    };
  }, [initializeMap]);

  // Load vehicles
  useEffect(() => {
    loadVehicles();
  }, []);

  // Update tile layer when provider changes
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    try {
      const newProvider = STREET_MAP_PROVIDERS.find(p => p.id === selectedProvider) || STREET_MAP_PROVIDERS[0];
      
      // Remove old tile layer
      if (tileLayerRef.current) {
        mapInstanceRef.current.removeLayer(tileLayerRef.current);
      }
      
      // Add new tile layer
      const newTileLayer = L.tileLayer(newProvider.url, {
        attribution: newProvider.attribution,
        maxZoom: 19,
        minZoom: 10,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      });
      
      newTileLayer.addTo(mapInstanceRef.current);
      tileLayerRef.current = newTileLayer;
      
      console.log('🔄 Street map tile layer updated to:', newProvider.name);
    } catch (error) {
      console.error('❌ Error updating street map tile layer:', error);
    }
  }, [selectedProvider, mapReady]);

  // Update markers when vehicles change
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    try {
      // Clear existing markers
      markersRef.current.forEach(marker => {
        mapInstanceRef.current?.removeLayer(marker);
      });
      markersRef.current = [];

      // Add new markers
      vehicles.forEach(vehicle => {
        const marker = createStreetVehicleMarker(vehicle);
        if (marker && mapInstanceRef.current) {
          marker.addTo(mapInstanceRef.current);
          markersRef.current.push(marker);
        }
      });

      console.log(`🚗 Updated ${vehicles.length} street vehicle markers`);
    } catch (error) {
      console.error('❌ Error updating street markers:', error);
    }
  }, [vehicles, mapReady]);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
          setError(null);
        }
      } else {
        throw new Error('Failed to load vehicles');
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Fallback to mock data with street-level detail
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'Ahmed Mohamed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'Sara Ahmed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_003',
          lat: 24.7100,
          lng: 46.6700,
          speed: 0,
          heading: 0,
          status: 'idle',
          driver: 'Mohamed Ali',
          lastUpdate: new Date().toISOString()
        }
      ]);
      
      setError('Using mock vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const createStreetVehicleMarker = (vehicle: Vehicle): L.Marker | null => {
    try {
      const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
      const size = 28; // Larger for street view
      
      const icon = L.divIcon({
        html: `
          <div style="
            background-color: ${color};
            width: ${size}px;
            height: ${size}px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 3px 6px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            font-weight: bold;
            transform: rotate(${vehicle.heading}deg);
          ">
            🚗
          </div>
        `,
        className: 'street-vehicle-marker',
        iconSize: [size, size],
        iconAnchor: [size/2, size/2]
      });

      const marker = L.marker([vehicle.lat, vehicle.lng], { icon });
      
      // Enhanced popup for street view
      const popupContent = `
        <div style="min-width: 250px;">
          <h3 style="margin: 0 0 10px 0; color: #1976d2;">🚗 مركبة ${vehicle.id}</h3>
          <div style="margin-bottom: 8px;">
            <strong>👤 السائق:</strong> ${vehicle.driver}
          </div>
          <div style="margin-bottom: 8px;">
            <strong>⚡ السرعة:</strong> ${vehicle.speed} كم/س
          </div>
          <div style="margin-bottom: 8px;">
            <strong>🧭 الاتجاه:</strong> ${vehicle.heading}°
          </div>
          <div style="margin-bottom: 8px;">
            <strong>📍 الإحداثيات:</strong> ${vehicle.lat.toFixed(6)}, ${vehicle.lng.toFixed(6)}
          </div>
          <div style="margin-bottom: 8px;">
            <strong>🕒 آخر تحديث:</strong> ${new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}
          </div>
          <div style="margin-top: 10px;">
            <span style="
              background-color: ${color};
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: bold;
            ">
              ${vehicle.status === 'active' ? '🟢 نشطة' : '🟡 متوقفة'}
            </span>
          </div>
        </div>
      `;
      
      marker.bindPopup(popupContent);
      
      return marker;
    } catch (error) {
      console.error('Error creating street vehicle marker:', error);
      return null;
    }
  };

  const handleRefresh = () => {
    loadVehicles();
  };

  const handleRetryInit = () => {
    setInitError(null);
    initializationRef.current = false;
    setMapReady(false);
    setTimeout(initializeMap, 100);
  };

  // Show initialization error
  if (initError) {
    return (
      <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: 3
          }}
        >
          <ErrorIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            فشل في تحميل خريطة الشوارع
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
            {initError}
          </Typography>
          <Button variant="contained" onClick={handleRetryInit} startIcon={<RefreshIcon />}>
            إعادة المحاولة
          </Button>
        </Box>
      </Paper>
    );
  }

  // Show loading
  if (loading && vehicles.length === 0) {
    return (
      <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            جاري تحميل خريطة الشوارع...
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Enhanced Controls for Street View */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: 16,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {/* Provider Selection */}
        <Card sx={{ minWidth: 220 }}>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <FormControl fullWidth size="small">
              <InputLabel>نوع خريطة الشوارع</InputLabel>
              <Select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                label="نوع خريطة الشوارع"
              >
                {STREET_MAP_PROVIDERS.map(provider => (
                  <MenuItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </CardContent>
        </Card>

        {/* Street View Options */}
        <Card>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Typography variant="subtitle2" gutterBottom>
              خيارات العرض
            </Typography>
            <Box display="flex" flexDirection="column" gap={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showTraffic}
                    onChange={(e) => setShowTraffic(e.target.checked)}
                    size="small"
                  />
                }
                label="حركة المرور"
                sx={{ margin: 0 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={showSatellite}
                    onChange={(e) => setShowSatellite(e.target.checked)}
                    size="small"
                  />
                }
                label="عرض الأقمار الصناعية"
                sx={{ margin: 0 }}
              />
            </Box>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Card>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Box display="flex" gap={1}>
              <Tooltip title="تحديث">
                <IconButton onClick={handleRefresh} size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="موقعي">
                <IconButton size="small">
                  <MyLocationIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="الطبقات">
                <IconButton size="small">
                  <LayersIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Enhanced Vehicle Stats for Street View */}
      {vehicles.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <Typography variant="subtitle2" gutterBottom>
                🗺️ خريطة الشوارع - إحصائيات المركبات
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`المجموع: ${vehicles.length}`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                  size="small"
                  color="success"
                />
                <Chip
                  label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                  size="small"
                  color="warning"
                />
              </Box>
              {error && (
                <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
                  {error}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Map Container */}
      <div
        ref={mapContainerRef}
        style={{
          height: '100%',
          width: '100%',
          position: 'relative'
        }}
      />
    </Paper>
  );
};

export default StreetMapComponent;
