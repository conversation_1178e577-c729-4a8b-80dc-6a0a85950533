import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  <PERSON>ton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  Snackbar,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  Storage as DatabaseIcon,
  TableChart as ExcelIcon,
  Code as SqlIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Visibility as PreviewIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

interface ImportJob {
  id: string;
  fileName: string;
  fileType: 'EXCEL' | 'CSV' | 'SQL';
  targetTable: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  totalRows: number;
  processedRows: number;
  errorRows: number;
  createdAt: string;
  completedAt?: string;
  errors?: string[];
}

interface ExportJob {
  id: string;
  tableName: string;
  format: 'EXCEL' | 'CSV' | 'JSON' | 'SQL';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  totalRows: number;
  fileSize?: string;
  downloadUrl?: string;
  createdAt: string;
  completedAt?: string;
}

interface TableInfo {
  name: string;
  displayName: string;
  rowCount: number;
  columns: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
  description: string;
}

const DataImportExport: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [importJobs, setImportJobs] = useState<ImportJob[]>([]);
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [openImportDialog, setOpenImportDialog] = useState(false);
  const [openExportDialog, setOpenExportDialog] = useState(false);
  const [openSqlDialog, setOpenSqlDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedTable, setSelectedTable] = useState('');
  const [sqlQuery, setSqlQuery] = useState('');
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [importStep, setImportStep] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Available tables for import/export
  const availableTables: TableInfo[] = [
    {
      name: 'vehicles',
      displayName: 'المركبات',
      rowCount: 25,
      columns: [
        { name: 'plate_number', type: 'VARCHAR', required: true },
        { name: 'make', type: 'VARCHAR', required: true },
        { name: 'model', type: 'VARCHAR', required: true },
        { name: 'year', type: 'INTEGER', required: true },
        { name: 'color', type: 'VARCHAR', required: false },
        { name: 'status', type: 'VARCHAR', required: true },
      ],
      description: 'بيانات المركبات في الأسطول',
    },
    {
      name: 'drivers',
      displayName: 'السائقين',
      rowCount: 18,
      columns: [
        { name: 'name', type: 'VARCHAR', required: true },
        { name: 'phone', type: 'VARCHAR', required: true },
        { name: 'license_number', type: 'VARCHAR', required: true },
        { name: 'license_expiry', type: 'DATE', required: true },
        { name: 'status', type: 'VARCHAR', required: true },
      ],
      description: 'بيانات السائقين المسجلين',
    },
    {
      name: 'rides',
      displayName: 'الرحلات',
      rowCount: 1250,
      columns: [
        { name: 'pickup_location', type: 'VARCHAR', required: true },
        { name: 'dropoff_location', type: 'VARCHAR', required: true },
        { name: 'passenger_id', type: 'VARCHAR', required: true },
        { name: 'driver_id', type: 'VARCHAR', required: true },
        { name: 'fare', type: 'DECIMAL', required: true },
        { name: 'status', type: 'VARCHAR', required: true },
      ],
      description: 'سجل الرحلات المكتملة والجارية',
    },
    {
      name: 'users',
      displayName: 'المستخدمين',
      rowCount: 890,
      columns: [
        { name: 'name', type: 'VARCHAR', required: true },
        { name: 'email', type: 'VARCHAR', required: true },
        { name: 'phone', type: 'VARCHAR', required: true },
        { name: 'role', type: 'VARCHAR', required: true },
        { name: 'status', type: 'VARCHAR', required: true },
      ],
      description: 'بيانات المستخدمين والركاب',
    },
  ];

  // Mock import jobs
  const mockImportJobs: ImportJob[] = [
    {
      id: 'import-1',
      fileName: 'vehicles_data.xlsx',
      fileType: 'EXCEL',
      targetTable: 'vehicles',
      status: 'COMPLETED',
      totalRows: 50,
      processedRows: 48,
      errorRows: 2,
      createdAt: '2025-07-09T10:30:00Z',
      completedAt: '2025-07-09T10:32:15Z',
      errors: ['Row 15: Invalid plate number format', 'Row 32: Missing required field: make'],
    },
    {
      id: 'import-2',
      fileName: 'drivers_update.csv',
      fileType: 'CSV',
      targetTable: 'drivers',
      status: 'PROCESSING',
      totalRows: 25,
      processedRows: 18,
      errorRows: 0,
      createdAt: '2025-07-09T11:00:00Z',
    },
  ];

  // Mock export jobs
  const mockExportJobs: ExportJob[] = [
    {
      id: 'export-1',
      tableName: 'rides',
      format: 'EXCEL',
      status: 'COMPLETED',
      totalRows: 1250,
      fileSize: '2.5 MB',
      downloadUrl: '/downloads/rides_export_20250709.xlsx',
      createdAt: '2025-07-09T09:00:00Z',
      completedAt: '2025-07-09T09:03:45Z',
    },
    {
      id: 'export-2',
      tableName: 'users',
      format: 'CSV',
      status: 'PENDING',
      totalRows: 890,
      createdAt: '2025-07-09T11:15:00Z',
    },
  ];

  React.useEffect(() => {
    setImportJobs(mockImportJobs);
    setExportJobs(mockExportJobs);
  }, []);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'في الانتظار', color: 'default' as const, icon: <WarningIcon fontSize="small" /> },
      PROCESSING: { label: 'جاري المعالجة', color: 'info' as const, icon: <RefreshIcon fontSize="small" /> },
      COMPLETED: { label: 'مكتمل', color: 'success' as const, icon: <CheckIcon fontSize="small" /> },
      FAILED: { label: 'فشل', color: 'error' as const, icon: <ErrorIcon fontSize="small" /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
        icon={config.icon}
      />
    );
  };

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'EXCEL': return <ExcelIcon sx={{ color: '#1f7244' }} />;
      case 'CSV': return <ExcelIcon sx={{ color: '#0066cc' }} />;
      case 'SQL': return <SqlIcon sx={{ color: '#f29111' }} />;
      default: return <DatabaseIcon />;
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setImportStep(1);
      // Mock preview data
      setPreviewData([
        { plate_number: 'أ ب ج 123', make: 'تويوتا', model: 'كامري', year: 2022 },
        { plate_number: 'د هـ و 456', make: 'نيسان', model: 'التيما', year: 2021 },
        { plate_number: 'ز ح ط 789', make: 'هيونداي', model: 'إلنترا', year: 2023 },
      ]);
    }
  };

  const handleImport = () => {
    if (!selectedFile || !selectedTable) return;

    const newJob: ImportJob = {
      id: `import-${Date.now()}`,
      fileName: selectedFile.name,
      fileType: selectedFile.name.endsWith('.xlsx') ? 'EXCEL' : 'CSV',
      targetTable: selectedTable,
      status: 'PENDING',
      totalRows: previewData.length,
      processedRows: 0,
      errorRows: 0,
      createdAt: new Date().toISOString(),
    };

    setImportJobs(prev => [newJob, ...prev]);
    setOpenImportDialog(false);
    setSelectedFile(null);
    setSelectedTable('');
    setPreviewData([]);
    setImportStep(0);
    setSnackbarMessage('تم بدء عملية الاستيراد بنجاح');
    setSnackbarOpen(true);
  };

  const handleExport = (tableName: string, format: string) => {
    const table = availableTables.find(t => t.name === tableName);
    if (!table) return;

    const newJob: ExportJob = {
      id: `export-${Date.now()}`,
      tableName,
      format: format as any,
      status: 'PENDING',
      totalRows: table.rowCount,
      createdAt: new Date().toISOString(),
    };

    setExportJobs(prev => [newJob, ...prev]);
    setSnackbarMessage('تم بدء عملية التصدير بنجاح');
    setSnackbarOpen(true);
  };

  const handleSqlExecution = () => {
    if (!sqlQuery.trim()) return;

    // Mock SQL execution
    setSnackbarMessage('تم تنفيذ الاستعلام بنجاح');
    setSnackbarOpen(true);
    setOpenSqlDialog(false);
    setSqlQuery('');
  };

  const importColumns: GridColDef[] = [
    {
      field: 'fileName',
      headerName: 'اسم الملف',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getFileTypeIcon(params.row.fileType)}
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      ),
    },
    {
      field: 'targetTable',
      headerName: 'الجدول المستهدف',
      width: 150,
      renderCell: (params: GridRenderCellParams) => {
        const table = availableTables.find(t => t.name === params.value);
        return <Typography variant="body2">{table?.displayName || params.value}</Typography>;
      },
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'progress',
      headerName: 'التقدم',
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const progress = params.row.totalRows > 0 ? (params.row.processedRows / params.row.totalRows) * 100 : 0;
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress variant="determinate" value={progress} />
            <Typography variant="caption" color="text.secondary">
              {params.row.processedRows}/{params.row.totalRows} ({progress.toFixed(0)}%)
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'errorRows',
      headerName: 'الأخطاء',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          color={params.value > 0 ? 'error' : 'success'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ البدء',
      width: 130,
      valueGetter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
  ];

  const exportColumns: GridColDef[] = [
    {
      field: 'tableName',
      headerName: 'الجدول',
      width: 150,
      renderCell: (params: GridRenderCellParams) => {
        const table = availableTables.find(t => t.name === params.value);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DatabaseIcon />
            <Typography variant="body2">{table?.displayName || params.value}</Typography>
          </Box>
        );
      },
    },
    {
      field: 'format',
      headerName: 'التنسيق',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" variant="outlined" />
      ),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 130,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'totalRows',
      headerName: 'عدد السجلات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">{params.value.toLocaleString()}</Typography>
      ),
    },
    {
      field: 'fileSize',
      headerName: 'حجم الملف',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">{params.value || '-'}</Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          {params.row.status === 'COMPLETED' && params.row.downloadUrl && (
            <Tooltip title="تحميل">
              <IconButton size="small" onClick={() => window.open(params.row.downloadUrl)}>
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      ),
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة البيانات
        </Typography>
        <Typography variant="body1" color="text.secondary">
          استيراد وتصدير البيانات من وإلى النظام
        </Typography>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => setOpenImportDialog(true)}>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <UploadIcon fontSize="large" />
              </Avatar>
              <Typography variant="h6" sx={{ mb: 1 }}>
                استيراد البيانات
              </Typography>
              <Typography variant="body2" color="text.secondary">
                رفع ملفات Excel أو CSV لاستيراد البيانات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => setOpenExportDialog(true)}>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <DownloadIcon fontSize="large" />
              </Avatar>
              <Typography variant="h6" sx={{ mb: 1 }}>
                تصدير البيانات
              </Typography>
              <Typography variant="body2" color="text.secondary">
                تصدير البيانات بصيغ مختلفة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => setOpenSqlDialog(true)}>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <SqlIcon fontSize="large" />
              </Avatar>
              <Typography variant="h6" sx={{ mb: 1 }}>
                تنفيذ SQL
              </Typography>
              <Typography variant="body2" color="text.secondary">
                تنفيذ استعلامات SQL مخصصة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tables Overview */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            الجداول المتاحة
          </Typography>
          <Grid container spacing={2}>
            {availableTables.map((table) => (
              <Grid item xs={12} sm={6} md={3} key={table.name}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <DatabaseIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                    {table.displayName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {table.rowCount.toLocaleString()} سجل
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {table.description}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Import Jobs */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            عمليات الاستيراد
          </Typography>
          <Box sx={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={importJobs}
              columns={importColumns}
              pageSizeOptions={[5, 10]}
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Export Jobs */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            عمليات التصدير
          </Typography>
          <Box sx={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={exportJobs}
              columns={exportColumns}
              pageSizeOptions={[5, 10]}
              disableRowSelectionOnClick
              sx={{ border: 0 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Import Dialog */}
      <Dialog open={openImportDialog} onClose={() => setOpenImportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>استيراد البيانات</DialogTitle>
        <DialogContent>
          <Stepper activeStep={importStep} orientation="vertical">
            <Step>
              <StepLabel>اختيار الملف</StepLabel>
              <StepContent>
                <Box sx={{ mt: 2, mb: 2 }}>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    accept=".xlsx,.xls,.csv"
                    style={{ display: 'none' }}
                  />
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => fileInputRef.current?.click()}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    اختيار ملف Excel أو CSV
                  </Button>
                  {selectedFile && (
                    <Alert severity="success">
                      تم اختيار الملف: {selectedFile.name}
                    </Alert>
                  )}
                </Box>
              </StepContent>
            </Step>
            <Step>
              <StepLabel>اختيار الجدول المستهدف</StepLabel>
              <StepContent>
                <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
                  <InputLabel>الجدول المستهدف</InputLabel>
                  <Select
                    value={selectedTable}
                    label="الجدول المستهدف"
                    onChange={(e) => setSelectedTable(e.target.value)}
                  >
                    {availableTables.map((table) => (
                      <MenuItem key={table.name} value={table.name}>
                        {table.displayName} ({table.columns.length} عمود)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                {selectedTable && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    الأعمدة المطلوبة: {availableTables.find(t => t.name === selectedTable)?.columns
                      .filter(c => c.required).map(c => c.name).join(', ')}
                  </Alert>
                )}
              </StepContent>
            </Step>
            <Step>
              <StepLabel>معاينة البيانات</StepLabel>
              <StepContent>
                {previewData.length > 0 && (
                  <TableContainer component={Paper} sx={{ mt: 2, mb: 2 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          {Object.keys(previewData[0]).map((key) => (
                            <TableCell key={key}>{key}</TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {previewData.slice(0, 3).map((row, index) => (
                          <TableRow key={index}>
                            {Object.values(row).map((value, cellIndex) => (
                              <TableCell key={cellIndex}>{value as string}</TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </StepContent>
            </Step>
          </Stepper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenImportDialog(false)}>إلغاء</Button>
          <Button 
            onClick={handleImport} 
            variant="contained"
            disabled={!selectedFile || !selectedTable}
          >
            بدء الاستيراد
          </Button>
        </DialogActions>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={openExportDialog} onClose={() => setOpenExportDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>تصدير البيانات</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {availableTables.map((table) => (
              <Paper key={table.name} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {table.displayName}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {table.rowCount.toLocaleString()} سجل - {table.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['EXCEL', 'CSV', 'JSON', 'SQL'].map((format) => (
                    <Button
                      key={format}
                      variant="outlined"
                      size="small"
                      onClick={() => handleExport(table.name, format)}
                    >
                      {format}
                    </Button>
                  ))}
                </Box>
              </Paper>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenExportDialog(false)}>إغلاق</Button>
        </DialogActions>
      </Dialog>

      {/* SQL Dialog */}
      <Dialog open={openSqlDialog} onClose={() => setOpenSqlDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>تنفيذ استعلام SQL</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mt: 2, mb: 2 }}>
            تحذير: تأكد من صحة الاستعلام قبل التنفيذ. العمليات غير القابلة للتراجع قد تؤثر على البيانات.
          </Alert>
          <TextField
            label="استعلام SQL"
            value={sqlQuery}
            onChange={(e) => setSqlQuery(e.target.value)}
            fullWidth
            multiline
            rows={8}
            placeholder="SELECT * FROM vehicles WHERE status = 'ACTIVE';"
            sx={{ fontFamily: 'monospace' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenSqlDialog(false)}>إلغاء</Button>
          <Button 
            onClick={handleSqlExecution} 
            variant="contained"
            disabled={!sqlQuery.trim()}
          >
            تنفيذ
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default DataImportExport;
