import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  AccountBalance as FinanceIcon,
  TrendingUp as RevenueIcon,
  TrendingDown as ExpenseIcon,
  Receipt as InvoiceIcon,
  Payment as PaymentIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
} from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`finance-tabpanel-${index}`}
      aria-labelledby={`finance-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FinanceManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock financial data
  const financialOverview = {
    totalRevenue: 2450000,
    totalExpenses: 1850000,
    netProfit: 600000,
    pendingInvoices: 15,
    paidInvoices: 142,
    overdueInvoices: 3,
  };

  const revenueData = [
    { month: 'يناير', revenue: 180000, expenses: 140000, profit: 40000 },
    { month: 'فبراير', revenue: 220000, expenses: 160000, profit: 60000 },
    { month: 'مارس', revenue: 280000, expenses: 180000, profit: 100000 },
    { month: 'أبريل', revenue: 320000, expenses: 200000, profit: 120000 },
    { month: 'مايو', revenue: 380000, expenses: 220000, profit: 160000 },
    { month: 'يونيو', revenue: 420000, expenses: 250000, profit: 170000 },
  ];

  const expenseCategories = [
    { name: 'الرواتب', value: 45, color: '#8884d8' },
    { name: 'الوقود', value: 25, color: '#82ca9d' },
    { name: 'الصيانة', value: 15, color: '#ffc658' },
    { name: 'التأمين', value: 10, color: '#ff7300' },
    { name: 'أخرى', value: 5, color: '#00ff00' },
  ];

  const invoices = [
    {
      id: 'INV-001',
      customerName: 'شركة النقل المتقدم',
      amount: 25000,
      status: 'مدفوعة',
      dueDate: '2025-07-15',
      issueDate: '2025-06-15',
    },
    {
      id: 'INV-002',
      customerName: 'مؤسسة التوصيل السريع',
      amount: 18500,
      status: 'معلقة',
      dueDate: '2025-07-20',
      issueDate: '2025-06-20',
    },
    {
      id: 'INV-003',
      customerName: 'شركة الخدمات اللوجستية',
      amount: 32000,
      status: 'متأخرة',
      dueDate: '2025-07-05',
      issueDate: '2025-06-05',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مدفوعة':
        return 'success';
      case 'معلقة':
        return 'warning';
      case 'متأخرة':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        الإدارة المالية
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="نظرة عامة" />
        <Tab label="الفواتير" />
        <Tab label="التقارير المالية" />
        <Tab label="المدفوعات" />
      </Tabs>

      {/* Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        {/* Financial Overview Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <RevenueIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {financialOverview.totalRevenue.toLocaleString()} ر.س
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الإيرادات
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'error.main' }}>
                    <ExpenseIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {financialOverview.totalExpenses.toLocaleString()} ر.س
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي المصروفات
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <FinanceIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {financialOverview.netProfit.toLocaleString()} ر.س
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      صافي الربح
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <InvoiceIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {financialOverview.pendingInvoices}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      فواتير معلقة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Revenue Chart */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  الإيرادات والمصروفات الشهرية
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${Number(value).toLocaleString()} ر.س`} />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" name="الإيرادات" />
                    <Line type="monotone" dataKey="expenses" stroke="#82ca9d" name="المصروفات" />
                    <Line type="monotone" dataKey="profit" stroke="#ffc658" name="الربح" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  توزيع المصروفات
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={expenseCategories}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                    >
                      {expenseCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Invoices Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">إدارة الفواتير</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setInvoiceDialogOpen(true)}
          >
            إنشاء فاتورة جديدة
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>رقم الفاتورة</TableCell>
                <TableCell>اسم العميل</TableCell>
                <TableCell>المبلغ</TableCell>
                <TableCell>تاريخ الإصدار</TableCell>
                <TableCell>تاريخ الاستحقاق</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell>الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell>{invoice.id}</TableCell>
                  <TableCell>{invoice.customerName}</TableCell>
                  <TableCell>{invoice.amount.toLocaleString()} ر.س</TableCell>
                  <TableCell>{invoice.issueDate}</TableCell>
                  <TableCell>{invoice.dueDate}</TableCell>
                  <TableCell>
                    <Chip
                      label={invoice.status}
                      color={getStatusColor(invoice.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" onClick={() => setSelectedInvoice(invoice)}>
                      <ViewIcon />
                    </IconButton>
                    <IconButton size="small">
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small">
                      <DownloadIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Reports Tab */}
      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" sx={{ mb: 3 }}>
          التقارير المالية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  تقرير الأرباح والخسائر
                </Typography>
                <Button variant="outlined" startIcon={<DownloadIcon />} sx={{ mb: 2 }}>
                  تحميل التقرير
                </Button>
                <List>
                  <ListItem>
                    <ListItemText
                      primary="إجمالي الإيرادات"
                      secondary={`${financialOverview.totalRevenue.toLocaleString()} ر.س`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="إجمالي المصروفات"
                      secondary={`${financialOverview.totalExpenses.toLocaleString()} ر.س`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="صافي الربح"
                      secondary={`${financialOverview.netProfit.toLocaleString()} ر.س`}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  تقرير التدفق النقدي
                </Typography>
                <Button variant="outlined" startIcon={<DownloadIcon />} sx={{ mb: 2 }}>
                  تحميل التقرير
                </Button>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={revenueData.slice(-3)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${Number(value).toLocaleString()} ر.س`} />
                    <Bar dataKey="profit" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Payments Tab */}
      <TabPanel value={tabValue} index={3}>
        <Typography variant="h6" sx={{ mb: 3 }}>
          إدارة المدفوعات
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1">
              صفحة إدارة المدفوعات قيد التطوير...
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Invoice Dialog */}
      <Dialog open={invoiceDialogOpen} onClose={() => setInvoiceDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>إنشاء فاتورة جديدة</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم العميل"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="المبلغ"
                type="number"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="تاريخ الاستحقاق"
                type="date"
                variant="outlined"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>نوع الخدمة</InputLabel>
                <Select label="نوع الخدمة">
                  <MenuItem value="ride">خدمة النقل</MenuItem>
                  <MenuItem value="delivery">خدمة التوصيل</MenuItem>
                  <MenuItem value="subscription">اشتراك شهري</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="وصف الخدمة"
                multiline
                rows={3}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInvoiceDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained" onClick={() => setInvoiceDialogOpen(false)}>
            إنشاء الفاتورة
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FinanceManagement;
