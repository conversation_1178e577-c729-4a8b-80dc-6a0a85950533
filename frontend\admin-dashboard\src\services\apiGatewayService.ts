import { api<PERSON>eth<PERSON>, handleApiError, createMockResponse, simulateApiDelay } from './api';
import { SERVICE_URLS } from './api';

// Types for API Gateway Management
export interface RouteDefinitionDto {
  id: string;
  uri: string;
  predicates: string[];
  filters: string[];
  authMethod: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC' | 'NONE';
  enabled: boolean;
  order: number;
  metadata?: { [key: string]: any };
  createdAt: string;
  updatedAt: string;
}

export interface AuthMethodDto {
  id: string;
  name: string;
  type: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC' | 'LDAP';
  config: { [key: string]: any };
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ConsumerDto {
  id: string;
  appName: string;
  clientId: string;
  clientSecret?: string;
  apiKey?: string;
  callbackUrl?: string;
  authType: 'JWT' | 'OAUTH2' | 'API_KEY';
  scopes: string[];
  rateLimitTier: 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  status: 'ACTIVE' | 'SUSPENDED' | 'PENDING';
  tenantId?: string;
  metadata: Record<string, any>;
  lastUsed?: string;
  totalRequests: number;
  createdAt: string;
  updatedAt: string;
}

export interface RateLimitConfigDto {
  id: string;
  name: string;
  tier: 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstCapacity: number;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GatewayMetricsDto {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  requestsPerMinute: number;
  topEndpoints: Array<{
    path: string;
    requests: number;
    avgResponseTime: number;
  }>;
  errorRates: Array<{
    service: string;
    errorRate: number;
  }>;
  period: string;
}

export interface RateLimitConfigDto {
  id: string;
  consumerIdOrIp: string;
  requestsPerMinute: number;
  burstCapacity: number;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateRouteRequest {
  id: string;
  uri: string;
  predicates: string[];
  filters?: string[];
  authMethod: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC' | 'NONE';
  order?: number;
  metadata?: { [key: string]: any };
}

export interface CreateAuthMethodRequest {
  name: string;
  type: 'JWT' | 'API_KEY' | 'OAUTH2' | 'BASIC' | 'LDAP';
  config: { [key: string]: any };
  description?: string;
}

export interface CreateConsumerRequest {
  appName: string;
  callbackUrl?: string;
  authType: 'JWT' | 'API_KEY' | 'OAUTH2';
  scopes: string[];
  rateLimitRpm?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ApiGatewayService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = SERVICE_URLS.API_GATEWAY || '/api/gateway';
  }

  // Route Management
  async getRoutes(): Promise<ApiResponse<RouteDefinitionDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<RouteDefinitionDto[]>>(
        `${this.baseUrl}/admin/routes`
      );
      
      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getRoutes();
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async getRouteById(routeId: string): Promise<ApiResponse<RouteDefinitionDto>> {
    try {
      const response = await apiMethods.get<ApiResponse<RouteDefinitionDto>>(
        `${this.baseUrl}/admin/routes/${routeId}`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getRouteById(routeId);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async createRoute(routeData: CreateRouteRequest): Promise<ApiResponse<RouteDefinitionDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<RouteDefinitionDto>>(
        `${this.baseUrl}/admin/routes`,
        routeData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.createRoute(routeData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async updateRoute(routeId: string, routeData: Partial<CreateRouteRequest>): Promise<ApiResponse<RouteDefinitionDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<RouteDefinitionDto>>(
        `${this.baseUrl}/admin/routes/${routeId}`,
        routeData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.updateRoute(routeId, routeData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async deleteRoute(routeId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.delete<ApiResponse<void>>(
        `${this.baseUrl}/admin/routes/${routeId}`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.deleteRoute(routeId);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async refreshRoutes(): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/admin/routes/refresh`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        return createMockResponse(undefined);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  // Auth Methods Management
  async getAuthMethods(): Promise<ApiResponse<AuthMethodDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<AuthMethodDto[]>>(
        `${this.baseUrl}/admin/auth-methods`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getAuthMethods();
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async createAuthMethod(authMethodData: CreateAuthMethodRequest): Promise<ApiResponse<AuthMethodDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<AuthMethodDto>>(
        `${this.baseUrl}/admin/auth-methods`,
        authMethodData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.createAuthMethod(authMethodData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async updateAuthMethod(authMethodId: string, authMethodData: Partial<CreateAuthMethodRequest>): Promise<ApiResponse<AuthMethodDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<AuthMethodDto>>(
        `${this.baseUrl}/admin/auth-methods/${authMethodId}`,
        authMethodData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.updateAuthMethod(authMethodId, authMethodData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  // API Consumers Management
  async getConsumers(): Promise<ApiResponse<ApiConsumerDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<ApiConsumerDto[]>>(
        `${this.baseUrl}/admin/consumers`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getConsumers();
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async createConsumer(consumerData: CreateConsumerRequest): Promise<ApiResponse<ApiConsumerDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<ApiConsumerDto>>(
        `${this.baseUrl}/admin/consumers`,
        consumerData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.createConsumer(consumerData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async updateConsumer(consumerId: string, consumerData: Partial<CreateConsumerRequest>): Promise<ApiResponse<ApiConsumerDto>> {
    try {
      const response = await apiMethods.put<ApiResponse<ApiConsumerDto>>(
        `${this.baseUrl}/admin/consumers/${consumerId}`,
        consumerData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.updateConsumer(consumerId, consumerData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async regenerateConsumerCredentials(consumerId: string): Promise<ApiResponse<{ clientId: string; clientSecret: string }>> {
    try {
      const response = await apiMethods.post<ApiResponse<{ clientId: string; clientSecret: string }>>(
        `${this.baseUrl}/admin/consumers/${consumerId}/regenerate-credentials`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.regenerateConsumerCredentials(consumerId);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  // Gateway Metrics and Monitoring
  async getGatewayMetrics(period: string = 'last_24_hours'): Promise<ApiResponse<GatewayMetricsDto>> {
    try {
      const response = await apiMethods.get<ApiResponse<GatewayMetricsDto>>(
        `${this.baseUrl}/admin/metrics?period=${period}`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getGatewayMetrics(period);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  // Rate Limiting Management
  async getRateLimits(): Promise<ApiResponse<RateLimitConfigDto[]>> {
    try {
      const response = await apiMethods.get<ApiResponse<RateLimitConfigDto[]>>(
        `${this.baseUrl}/admin/rate-limits`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getRateLimits();
      }
      
      throw new Error(handleApiError(error));
    }
  }

  async createRateLimit(rateLimitData: Omit<RateLimitConfigDto, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<RateLimitConfigDto>> {
    try {
      const response = await apiMethods.post<ApiResponse<RateLimitConfigDto>>(
        `${this.baseUrl}/admin/rate-limits`,
        rateLimitData
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.createRateLimit(rateLimitData);
      }
      
      throw new Error(handleApiError(error));
    }
  }

  // Health and Status
  async getGatewayHealth(): Promise<ApiResponse<{ status: string; services: Array<{ name: string; status: string; responseTime: number }> }>> {
    try {
      const response = await apiMethods.get<ApiResponse<{ status: string; services: Array<{ name: string; status: string; responseTime: number }> }>>(
        `${this.baseUrl}/actuator/health`
      );
      
      return response.data;
    } catch (error) {
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiGatewayData } = await import('../utils/mockApiGatewayData');
        return await mockApiGatewayData.getGatewayHealth();
      }
      
      throw new Error(handleApiError(error));
    }
  }
}

// Create singleton instance
export const apiGatewayService = new ApiGatewayService();

export default apiGatewayService;
