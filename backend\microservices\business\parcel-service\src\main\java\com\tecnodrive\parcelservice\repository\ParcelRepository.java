package com.tecnodrive.parcelservice.repository;

import com.tecnodrive.parcelservice.entity.ParcelEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository للطرود
 */
@Repository
public interface ParcelRepository extends JpaRepository<ParcelEntity, String> {

    /**
     * البحث بالباركود
     */
    Optional<ParcelEntity> findByBarcode(String barcode);

    /**
     * التحقق من وجود باركود
     */
    boolean existsByBarcode(String barcode);

    /**
     * البحث بمعرف المستخدم
     */
    Page<ParcelEntity> findByUserId(String userId, Pageable pageable);

    /**
     * البحث بالحالة
     */
    Page<ParcelEntity> findByStatus(ParcelEntity.ParcelStatus status, Pageable pageable);

    /**
     * البحث بمعرف المستخدم والحالة
     */
    Page<ParcelEntity> findByUserIdAndStatus(String userId, ParcelEntity.ParcelStatus status, Pageable pageable);

    /**
     * البحث بالأولوية
     */
    List<ParcelEntity> findByPriorityOrderByCreatedAtAsc(ParcelEntity.ParcelPriority priority);

    /**
     * البحث بالطرود الهشة
     */
    List<ParcelEntity> findByFragileTrue();

    /**
     * البحث بالطرود التي تحتاج تأمين
     */
    @Query("SELECT p FROM ParcelEntity p WHERE p.insuranceValue > 0")
    List<ParcelEntity> findParcelsWithInsurance();

    /**
     * البحث بالطرود المتأخرة
     */
    @Query("SELECT p FROM ParcelEntity p WHERE p.estimatedDeliveryDate < :currentDate AND p.status NOT IN ('DELIVERED', 'CANCELLED', 'RETURNED')")
    List<ParcelEntity> findDelayedParcels(@Param("currentDate") LocalDateTime currentDate);

    /**
     * البحث بالطرود المنشأة في فترة معينة
     */
    List<ParcelEntity> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * البحث النصي الشامل
     */
    @Query("SELECT p FROM ParcelEntity p WHERE " +
           "LOWER(p.senderName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.receiverName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.barcode) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<ParcelEntity> searchParcels(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * عدد الطرود حسب الحالة
     */
    @Query("SELECT p.status, COUNT(p) FROM ParcelEntity p GROUP BY p.status")
    List<Object[]> countParcelsByStatus();

    /**
     * إجمالي الإيرادات المقدرة
     */
    @Query("SELECT SUM(p.estimatedCost) FROM ParcelEntity p WHERE p.status = 'DELIVERED'")
    Double getTotalEstimatedRevenue();
}
