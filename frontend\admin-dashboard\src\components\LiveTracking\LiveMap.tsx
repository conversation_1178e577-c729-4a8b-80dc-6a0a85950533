import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Wrapper, Status } from '@googlemaps/react-wrapper';
import { Box, Paper, Typography, Chip, IconButton, Tooltip } from '@mui/material';
import { 
  DirectionsCar, 
  LocalShipping, 
  Person, 
  Refresh, 
  ZoomIn, 
  ZoomOut,
  MyLocation 
} from '@mui/icons-material';

// Types for our tracking data
interface VehicleLocation {
  id: string;
  type: 'passenger' | 'delivery';
  lat: number;
  lng: number;
  heading: number;
  speed: number;
  status: 'available' | 'busy' | 'offline';
  driverName: string;
  lastUpdate: string;
}

interface ParcelLocation {
  id: string;
  lat: number;
  lng: number;
  status: 'pickup_pending' | 'in_transit' | 'delivered';
  estimatedDelivery: string;
  recipientName: string;
}

interface LiveMapProps {
  vehicles: VehicleLocation[];
  parcels: ParcelLocation[];
  center?: { lat: number; lng: number };
  zoom?: number;
  onVehicleClick?: (vehicle: VehicleLocation) => void;
  onParcelClick?: (parcel: ParcelLocation) => void;
}

// Google Maps component
const MapComponent: React.FC<{
  center: google.maps.LatLngLiteral;
  zoom: number;
  vehicles: VehicleLocation[];
  parcels: ParcelLocation[];
  onVehicleClick?: (vehicle: VehicleLocation) => void;
  onParcelClick?: (parcel: ParcelLocation) => void;
}> = ({ center, zoom, vehicles, parcels, onVehicleClick, onParcelClick }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map>();
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  // Initialize map
  useEffect(() => {
    if (ref.current && !map) {
      const newMap = new window.google.maps.Map(ref.current, {
        center,
        zoom,
        mapTypeId: 'roadmap',
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      });
      setMap(newMap);
    }
  }, [ref, map, center, zoom]);

  // Clear existing markers
  const clearMarkers = useCallback(() => {
    markers.forEach(marker => marker.setMap(null));
    setMarkers([]);
  }, [markers]);

  // Create vehicle markers
  useEffect(() => {
    if (!map) return;

    clearMarkers();
    const newMarkers: google.maps.Marker[] = [];

    // Add vehicle markers
    vehicles.forEach(vehicle => {
      const icon = {
        path: vehicle.type === 'passenger' ? 
          'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' :
          'M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z',
        fillColor: vehicle.status === 'available' ? '#4CAF50' : 
                   vehicle.status === 'busy' ? '#FF9800' : '#F44336',
        fillOpacity: 1,
        strokeColor: '#FFFFFF',
        strokeWeight: 2,
        scale: 1.5,
        rotation: vehicle.heading
      };

      const marker = new google.maps.Marker({
        position: { lat: vehicle.lat, lng: vehicle.lng },
        map,
        icon,
        title: `${vehicle.driverName} - ${vehicle.status}`
      });

      // Add click listener
      marker.addListener('click', () => {
        onVehicleClick?.(vehicle);
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px;">
            <h4>${vehicle.driverName}</h4>
            <p>Status: ${vehicle.status}</p>
            <p>Speed: ${vehicle.speed} km/h</p>
            <p>Last Update: ${new Date(vehicle.lastUpdate).toLocaleTimeString()}</p>
          </div>
        `
      });

      marker.addListener('mouseover', () => {
        infoWindow.open(map, marker);
      });

      marker.addListener('mouseout', () => {
        infoWindow.close();
      });

      newMarkers.push(marker);
    });

    // Add parcel markers
    parcels.forEach(parcel => {
      const icon = {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="24" height="24" viewBox="0 0 24 24" fill="${
            parcel.status === 'pickup_pending' ? '#2196F3' :
            parcel.status === 'in_transit' ? '#FF9800' : '#4CAF50'
          }" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2l3 3h4v14H5V5h4l3-3zm0 2.83L10.83 6H7v12h10V6h-3.83L12 4.83z"/>
          </svg>
        `),
        scaledSize: new google.maps.Size(24, 24)
      };

      const marker = new google.maps.Marker({
        position: { lat: parcel.lat, lng: parcel.lng },
        map,
        icon,
        title: `Parcel for ${parcel.recipientName}`
      });

      marker.addListener('click', () => {
        onParcelClick?.(parcel);
      });

      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px;">
            <h4>Parcel ID: ${parcel.id}</h4>
            <p>Recipient: ${parcel.recipientName}</p>
            <p>Status: ${parcel.status}</p>
            <p>ETA: ${new Date(parcel.estimatedDelivery).toLocaleString()}</p>
          </div>
        `
      });

      marker.addListener('mouseover', () => {
        infoWindow.open(map, marker);
      });

      marker.addListener('mouseout', () => {
        infoWindow.close();
      });

      newMarkers.push(marker);
    });

    setMarkers(newMarkers);
  }, [map, vehicles, parcels, onVehicleClick, onParcelClick, clearMarkers]);

  return <div ref={ref} style={{ width: '100%', height: '100%' }} />;
};

// Render function for Google Maps Wrapper
const render = (status: Status) => {
  switch (status) {
    case Status.LOADING:
      return <Typography>Loading map...</Typography>;
    case Status.FAILURE:
      return <Typography color="error">Error loading map</Typography>;
    case Status.SUCCESS:
      return null;
  }
};

// Main LiveMap component
const LiveMap: React.FC<LiveMapProps> = ({
  vehicles = [],
  parcels = [],
  center = { lat: 24.7136, lng: 46.6753 }, // Riyadh coordinates
  zoom = 12,
  onVehicleClick,
  onParcelClick
}) => {
  const [currentCenter, setCurrentCenter] = useState(center);
  const [currentZoom, setCurrentZoom] = useState(zoom);

  // Stats for the legend
  const vehicleStats = {
    available: vehicles.filter(v => v.status === 'available').length,
    busy: vehicles.filter(v => v.status === 'busy').length,
    offline: vehicles.filter(v => v.status === 'offline').length
  };

  const parcelStats = {
    pending: parcels.filter(p => p.status === 'pickup_pending').length,
    inTransit: parcels.filter(p => p.status === 'in_transit').length,
    delivered: parcels.filter(p => p.status === 'delivered').length
  };

  const handleRefresh = () => {
    // This would trigger a refresh of the data
    console.log('Refreshing map data...');
  };

  const handleZoomIn = () => {
    setCurrentZoom(prev => Math.min(prev + 1, 20));
  };

  const handleZoomOut = () => {
    setCurrentZoom(prev => Math.max(prev - 1, 1));
  };

  const handleRecenter = () => {
    setCurrentCenter(center);
    setCurrentZoom(zoom);
  };

  return (
    <Paper elevation={3} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header with controls and legend */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Live Tracking Map</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh">
              <IconButton onClick={handleRefresh} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom In">
              <IconButton onClick={handleZoomIn} size="small">
                <ZoomIn />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton onClick={handleZoomOut} size="small">
                <ZoomOut />
              </IconButton>
            </Tooltip>
            <Tooltip title="Recenter">
              <IconButton onClick={handleRecenter} size="small">
                <MyLocation />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Legend */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DirectionsCar sx={{ color: '#4CAF50' }} />
            <Chip label={`Available: ${vehicleStats.available}`} size="small" color="success" />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DirectionsCar sx={{ color: '#FF9800' }} />
            <Chip label={`Busy: ${vehicleStats.busy}`} size="small" color="warning" />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DirectionsCar sx={{ color: '#F44336' }} />
            <Chip label={`Offline: ${vehicleStats.offline}`} size="small" color="error" />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LocalShipping sx={{ color: '#2196F3' }} />
            <Chip label={`Parcels: ${parcels.length}`} size="small" color="primary" />
          </Box>
        </Box>
      </Box>

      {/* Map container */}
      <Box sx={{ flex: 1, position: 'relative' }}>
        <Wrapper
          apiKey={process.env.REACT_APP_GOOGLE_MAPS_API_KEY || 'YOUR_API_KEY_HERE'}
          render={render}
        >
          <MapComponent
            center={currentCenter}
            zoom={currentZoom}
            vehicles={vehicles}
            parcels={parcels}
            onVehicleClick={onVehicleClick}
            onParcelClick={onParcelClick}
          />
        </Wrapper>
      </Box>
    </Paper>
  );
};

export default LiveMap;
export type { VehicleLocation, ParcelLocation };
