package com.tecnodrive.parcelservice.dto;

import java.util.UUID;

/**
 * Parcel Request DTO
 */
public class ParcelRequestDto {

    private UUID senderId;
    private UUID receiverId;
    private double weight;
    private String dimensions;

    // Default constructor
    public ParcelRequestDto() {}

    // All-args constructor
    public ParcelRequestDto(UUID senderId, UUID receiverId, double weight, String dimensions) {
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.weight = weight;
        this.dimensions = dimensions;
    }

    // Getters and Setters
    public UUID getSenderId() {
        return senderId;
    }

    public void setSenderId(UUID senderId) {
        this.senderId = senderId;
    }

    public UUID getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(UUID receiverId) {
        this.receiverId = receiverId;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public String getDimensions() {
        return dimensions;
    }

    public void setDimensions(String dimensions) {
        this.dimensions = dimensions;
    }
}
