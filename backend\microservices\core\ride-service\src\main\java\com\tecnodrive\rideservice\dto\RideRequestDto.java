package com.tecnodrive.rideservice.dto;

import com.tecnodrive.rideservice.entity.RideType;

import java.time.Instant;
import java.util.UUID;

/**
 * Ride Request DTO
 */
public record RideRequestDto(
    UUID passengerId,
    LocationDto pickupLocation,
    String pickupAddress,
    LocationDto destinationLocation,
    String destinationAddress,
    String vehicleTypeName,
    RideType rideType,
    Instant scheduledAt,
    String passengerNotes,
    UUID companyId,
    UUID schoolId
) {
    
    public static record LocationDto(
        double latitude,
        double longitude
    ) {}
}
