package com.tecnodrive.rideservice.controller;

import com.tecnodrive.rideservice.dto.FareEstimateDto;
import com.tecnodrive.rideservice.dto.RideRequestDto;
import com.tecnodrive.rideservice.dto.RideResponseDto;
import com.tecnodrive.rideservice.entity.RideStatus;
import com.tecnodrive.rideservice.service.RideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Ride Controller
 */
@RestController
@RequestMapping("/api/v1/rides")
public class RideController {

    private final RideService rideService;

    @Autowired
    public RideController(RideService rideService) {
        this.rideService = rideService;
    }

    @PostMapping("/request")
    public ResponseEntity<Map<String, Object>> requestRide(@RequestBody RideRequestDto request) {
        try {
            RideResponseDto ride = rideService.requestRide(request);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Ride requested successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to request ride: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/estimate-fare")
    public ResponseEntity<Map<String, Object>> estimateFare(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Double> pickup = (Map<String, Double>) request.get("pickup");
            @SuppressWarnings("unchecked")
            Map<String, Double> destination = (Map<String, Double>) request.get("destination");

            RideRequestDto.LocationDto pickupLocation = new RideRequestDto.LocationDto(
                pickup.get("latitude"), pickup.get("longitude")
            );
            RideRequestDto.LocationDto destinationLocation = new RideRequestDto.LocationDto(
                destination.get("latitude"), destination.get("longitude")
            );

            FareEstimateDto estimate = rideService.getFareEstimate(pickupLocation, destinationLocation);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", estimate
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to estimate fare: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{rideId}/assign-driver")
    public ResponseEntity<Map<String, Object>> assignDriver(@PathVariable UUID rideId, 
                                                           @RequestBody Map<String, String> request) {
        try {
            UUID driverId = UUID.fromString(request.get("driverId"));
            RideResponseDto ride = rideService.assignDriver(rideId, driverId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Driver assigned successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to assign driver: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/{rideId}/status")
    public ResponseEntity<Map<String, Object>> updateRideStatus(@PathVariable UUID rideId,
                                                               @RequestBody Map<String, String> request) {
        try {
            RideStatus newStatus = RideStatus.valueOf(request.get("status"));
            String reason = request.get("reason");
            RideResponseDto ride = rideService.updateRideStatus(rideId, newStatus, reason);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Ride status updated successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to update ride status: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{rideId}/complete")
    public ResponseEntity<Map<String, Object>> completeRide(@PathVariable UUID rideId,
                                                           @RequestBody Map<String, Object> request) {
        try {
            BigDecimal finalFare = new BigDecimal(request.get("finalFare").toString());
            BigDecimal actualDistance = new BigDecimal(request.get("actualDistance").toString());
            Integer actualDuration = Integer.valueOf(request.get("actualDuration").toString());

            RideResponseDto ride = rideService.completeRide(rideId, finalFare, actualDistance, actualDuration);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Ride completed successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to complete ride: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{rideId}/cancel")
    public ResponseEntity<Map<String, Object>> cancelRide(@PathVariable UUID rideId,
                                                         @RequestBody Map<String, String> request) {
        try {
            RideStatus cancelStatus = RideStatus.valueOf(request.get("cancelStatus"));
            String reason = request.get("reason");
            RideResponseDto ride = rideService.cancelRide(rideId, cancelStatus, reason);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Ride cancelled successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to cancel ride: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{rideId}/rate")
    public ResponseEntity<Map<String, Object>> rateRide(@PathVariable UUID rideId,
                                                       @RequestBody Map<String, Object> request) {
        try {
            UUID raterId = UUID.fromString(request.get("raterId").toString());
            Integer rating = Integer.valueOf(request.get("rating").toString());

            RideResponseDto ride = rideService.rateRide(rideId, raterId, rating);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Ride rated successfully",
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to rate ride: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{rideId}")
    public ResponseEntity<Map<String, Object>> getRide(@PathVariable UUID rideId) {
        try {
            RideResponseDto ride = rideService.getRideById(rideId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get ride: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/passenger/{passengerId}")
    public ResponseEntity<Map<String, Object>> getRidesByPassenger(@PathVariable UUID passengerId) {
        try {
            List<RideResponseDto> rides = rideService.getRidesByPassenger(passengerId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", rides
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get rides: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/driver/{driverId}")
    public ResponseEntity<Map<String, Object>> getRidesByDriver(@PathVariable UUID driverId) {
        try {
            List<RideResponseDto> rides = rideService.getRidesByDriver(driverId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", rides
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get rides: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/passenger/{passengerId}/active")
    public ResponseEntity<Map<String, Object>> getActiveRideByPassenger(@PathVariable UUID passengerId) {
        try {
            RideResponseDto ride = rideService.getActiveRideByPassenger(passengerId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get active ride: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/driver/{driverId}/active")
    public ResponseEntity<Map<String, Object>> getActiveRideByDriver(@PathVariable UUID driverId) {
        try {
            RideResponseDto ride = rideService.getActiveRideByDriver(driverId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", ride
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get active ride: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/driver/{driverId}/available")
    public ResponseEntity<Map<String, Object>> getAvailableRides(@PathVariable UUID driverId,
                                                                @RequestParam double latitude,
                                                                @RequestParam double longitude,
                                                                @RequestParam(defaultValue = "5.0") double radiusKm) {
        try {
            List<RideResponseDto> rides = rideService.findAvailableRides(driverId, latitude, longitude, radiusKm);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", rides
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get available rides: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getRideMetrics() {
        try {
            RideService.RideMetricsDto metrics = rideService.getRideMetrics();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", metrics
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to get metrics: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of("status", "UP", "service", "ride-service"));
    }
}


