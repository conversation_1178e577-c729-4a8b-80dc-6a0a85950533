-- TECNODRIVE Ride Service Database Schema
-- Database: tecnodrive_rides

\c tecnodrive_rides;

-- Enable PostGIS extension for spatial data
CREATE EXTENSION IF NOT EXISTS postgis;

-- Ride requests table
CREATE TABLE IF NOT EXISTS ride_requests (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL, -- Reference to user service
    pickup_location GEOMETRY(POINT, 4326) NOT NULL,
    pickup_address TEXT NOT NULL,
    destination_location GEOMETRY(POINT, 4326) NOT NULL,
    destination_address TEXT NOT NULL,
    ride_type VARCHAR(20) NOT NULL CHECK (ride_type IN ('STANDARD', 'PREMIUM', 'SHARED', 'DELIVERY')),
    estimated_distance DECIMAL(8,2),
    estimated_duration INTEGER, -- in minutes
    estimated_fare DECIMAL(10,2),
    special_requirements JSONB,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'CANCELLED', 'EXPIRED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Active rides table
CREATE TABLE IF NOT EXISTS rides (
    id BIGSERIAL PRIMARY KEY,
    ride_request_id BIGINT REFERENCES ride_requests(id),
    customer_id BIGINT NOT NULL,
    driver_id BIGINT NOT NULL, -- Reference to user service
    vehicle_id BIGINT, -- Reference to fleet service
    pickup_location GEOMETRY(POINT, 4326) NOT NULL,
    pickup_address TEXT NOT NULL,
    destination_location GEOMETRY(POINT, 4326) NOT NULL,
    destination_address TEXT NOT NULL,
    ride_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'ASSIGNED' CHECK (status IN ('ASSIGNED', 'DRIVER_ARRIVED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED')),
    scheduled_time TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    cancellation_reason TEXT,
    actual_distance DECIMAL(8,2),
    actual_duration INTEGER, -- in minutes
    fare_amount DECIMAL(10,2),
    tip_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2),
    payment_status VARCHAR(20) DEFAULT 'PENDING' CHECK (payment_status IN ('PENDING', 'PAID', 'FAILED', 'REFUNDED')),
    payment_method VARCHAR(20),
    route_data JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride tracking for real-time location updates
CREATE TABLE IF NOT EXISTS ride_tracking (
    id BIGSERIAL PRIMARY KEY,
    ride_id BIGINT REFERENCES rides(id) ON DELETE CASCADE,
    driver_location GEOMETRY(POINT, 4326) NOT NULL,
    speed DECIMAL(5,2), -- km/h
    heading DECIMAL(5,2), -- degrees
    accuracy DECIMAL(8,2), -- meters
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride ratings and feedback
CREATE TABLE IF NOT EXISTS ride_ratings (
    id BIGSERIAL PRIMARY KEY,
    ride_id BIGINT REFERENCES rides(id) ON DELETE CASCADE,
    rater_id BIGINT NOT NULL, -- customer or driver
    rater_type VARCHAR(10) NOT NULL CHECK (rater_type IN ('CUSTOMER', 'DRIVER')),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    tags JSONB, -- predefined feedback tags
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride fare calculations
CREATE TABLE IF NOT EXISTS fare_calculations (
    id BIGSERIAL PRIMARY KEY,
    ride_id BIGINT REFERENCES rides(id) ON DELETE CASCADE,
    base_fare DECIMAL(10,2) NOT NULL,
    distance_fare DECIMAL(10,2) DEFAULT 0.00,
    time_fare DECIMAL(10,2) DEFAULT 0.00,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.00,
    surge_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    service_fee DECIMAL(10,2) DEFAULT 0.00,
    total_fare DECIMAL(10,2) NOT NULL,
    calculation_details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride cancellations
CREATE TABLE IF NOT EXISTS ride_cancellations (
    id BIGSERIAL PRIMARY KEY,
    ride_id BIGINT REFERENCES rides(id) ON DELETE CASCADE,
    cancelled_by VARCHAR(10) NOT NULL CHECK (cancelled_by IN ('CUSTOMER', 'DRIVER', 'SYSTEM')),
    cancellation_reason VARCHAR(50) NOT NULL,
    cancellation_fee DECIMAL(10,2) DEFAULT 0.00,
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    details TEXT,
    cancelled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Surge pricing zones
CREATE TABLE IF NOT EXISTS surge_zones (
    id BIGSERIAL PRIMARY KEY,
    zone_name VARCHAR(100) NOT NULL,
    zone_geometry GEOMETRY(POLYGON, 4326) NOT NULL,
    surge_multiplier DECIMAL(3,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ride statistics for analytics
CREATE TABLE IF NOT EXISTS ride_statistics (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL,
    hour INTEGER NOT NULL CHECK (hour >= 0 AND hour <= 23),
    zone_id BIGINT,
    total_requests INTEGER DEFAULT 0,
    completed_rides INTEGER DEFAULT 0,
    cancelled_rides INTEGER DEFAULT 0,
    average_wait_time INTEGER, -- minutes
    average_ride_duration INTEGER, -- minutes
    average_fare DECIMAL(10,2),
    total_revenue DECIMAL(12,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, hour, zone_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_ride_requests_customer ON ride_requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_created ON ride_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_ride_requests_pickup_location ON ride_requests USING GIST(pickup_location);
CREATE INDEX IF NOT EXISTS idx_ride_requests_destination_location ON ride_requests USING GIST(destination_location);

CREATE INDEX IF NOT EXISTS idx_rides_customer ON rides(customer_id);
CREATE INDEX IF NOT EXISTS idx_rides_driver ON rides(driver_id);
CREATE INDEX IF NOT EXISTS idx_rides_status ON rides(status);
CREATE INDEX IF NOT EXISTS idx_rides_created ON rides(created_at);
CREATE INDEX IF NOT EXISTS idx_rides_pickup_location ON rides USING GIST(pickup_location);
CREATE INDEX IF NOT EXISTS idx_rides_destination_location ON rides USING GIST(destination_location);

CREATE INDEX IF NOT EXISTS idx_ride_tracking_ride ON ride_tracking(ride_id);
CREATE INDEX IF NOT EXISTS idx_ride_tracking_timestamp ON ride_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_ride_tracking_location ON ride_tracking USING GIST(driver_location);

CREATE INDEX IF NOT EXISTS idx_ride_ratings_ride ON ride_ratings(ride_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_rater ON ride_ratings(rater_id, rater_type);

CREATE INDEX IF NOT EXISTS idx_surge_zones_geometry ON surge_zones USING GIST(zone_geometry);
CREATE INDEX IF NOT EXISTS idx_surge_zones_active ON surge_zones(is_active);

-- Triggers for updated_at
CREATE TRIGGER update_ride_requests_updated_at BEFORE UPDATE ON ride_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rides_updated_at BEFORE UPDATE ON rides FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_surge_zones_updated_at BEFORE UPDATE ON surge_zones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
