package com.tecnodrive.locationservice.config;

import com.tecnodrive.locationservice.websocket.LocationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket Configuration for Location Service
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private LocationWebSocketHandler locationWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(@NonNull WebSocketHandlerRegistry registry) {
        registry.addHandler(locationWebSocketHandler, "/ws/live-tracking")
                .setAllowedOrigins("*") // Configure based on your security requirements
                .withSockJS(); // Enable SockJS fallback

        registry.addHandler(locationWebSocketHandler, "/ws/fleet")
                .setAllowedOrigins("*")
                .withSockJS();
    }
}
