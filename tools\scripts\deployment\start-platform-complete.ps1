# Complete TecnoDrive Platform Startup Script
param(
    [Parameter(Mandatory=$false)]
    [switch]$Infrastructure = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Services = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$All = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Status = $false
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"
$White = "White"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Show-ServiceStatus {
    Write-ColorOutput "`n📊 Current Service Status:" $Cyan
    
    $services = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "tecno|infra" }
    
    if ($services) {
        Write-ColorOutput "✅ Running Services:" $Green
        $services | ForEach-Object { Write-ColorOutput "  $_" $White }
    } else {
        Write-ColorOutput "⚠️  No TecnoDrive services found running" $Yellow
    }
    
    Write-ColorOutput "`n🌐 Service Access URLs:" $Green
    Write-ColorOutput "  • API Gateway:      http://localhost:8080" $White
    Write-ColorOutput "  • Eureka Dashboard: http://localhost:8761" $White
    Write-ColorOutput "  • Auth Service:     http://localhost:8081" $White
    Write-ColorOutput "  • User Service:     http://localhost:8082" $White
    Write-ColorOutput "  • Location Service: http://localhost:8086" $White
    Write-ColorOutput "  • Parcel Service:   http://localhost:8084" $White
    Write-ColorOutput "  • PostgreSQL:       localhost:5432" $White
    Write-ColorOutput "  • Redis:            localhost:6379" $White
}

# Check if just status is requested
if ($Status) {
    Show-ServiceStatus
    exit 0
}

# Check Docker
if (-not (Test-DockerRunning)) {
    Write-ColorOutput "❌ Docker is not running. Please start Docker Desktop first." $Red
    exit 1
}

Write-ColorOutput "🚀 Starting TecnoDrive Platform..." $Green
Write-ColorOutput "✅ Docker is running" $Green

# Determine what to start
$startInfra = $Infrastructure -or $All
$startServices = $Services -or $All

if (-not $startInfra -and -not $startServices) {
    Write-ColorOutput "⚠️  Please specify what to start:" $Yellow
    Write-ColorOutput "  -Infrastructure : Start only infrastructure (DB, Redis, Eureka)" $White
    Write-ColorOutput "  -Services      : Start only application services" $White
    Write-ColorOutput "  -All           : Start everything" $White
    Write-ColorOutput "  -Status        : Show current status" $White
    exit 1
}

if ($startInfra) {
    Write-ColorOutput "`n📦 Starting Infrastructure Services..." $Cyan
    
    # Start infrastructure services
    docker compose -f docker-compose.complete.yml up -d postgres redis eureka-server
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Infrastructure services started" $Green
        
        # Wait for services to be ready
        Write-ColorOutput "⏳ Waiting for infrastructure to be ready..." $Yellow
        Start-Sleep -Seconds 20
    } else {
        Write-ColorOutput "❌ Failed to start infrastructure services" $Red
        exit 1
    }
}

if ($startServices) {
    Write-ColorOutput "`n🔧 Starting Application Services..." $Cyan
    
    # Start application services
    docker compose -f docker-compose.complete.yml up -d api-gateway auth-service user-service location-service parcel-service
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Application services started" $Green
        
        # Wait for services to register
        Write-ColorOutput "⏳ Waiting for services to register with Eureka..." $Yellow
        Start-Sleep -Seconds 30
    } else {
        Write-ColorOutput "❌ Failed to start application services" $Red
        exit 1
    }
}

# Show final status
Show-ServiceStatus

# Health check
Write-ColorOutput "`n🔍 Performing Health Checks..." $Cyan

$healthChecks = @(
    @{ Name = "Eureka Server"; Url = "http://localhost:8761/actuator/health" },
    @{ Name = "API Gateway"; Url = "http://localhost:8080/actuator/health" },
    @{ Name = "Auth Service"; Url = "http://localhost:8081/actuator/health" }
)

foreach ($check in $healthChecks) {
    try {
        $response = Invoke-WebRequest -Uri $check.Url -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-ColorOutput "  ✅ $($check.Name): Healthy" $Green
        } else {
            Write-ColorOutput "  ⚠️  $($check.Name): Responding but not healthy" $Yellow
        }
    }
    catch {
        Write-ColorOutput "  ❌ $($check.Name): Not responding" $Red
    }
}

Write-ColorOutput "`n🎉 TecnoDrive Platform startup completed!" $Green
Write-ColorOutput "`n💡 Useful Commands:" $Cyan
Write-ColorOutput "  • Check status:    .\start-platform-complete.ps1 -Status" $White
Write-ColorOutput "  • View logs:       docker compose -f docker-compose.complete.yml logs -f [service-name]" $White
Write-ColorOutput "  • Stop all:        docker compose -f docker-compose.complete.yml down" $White
Write-ColorOutput "  • Restart service: docker compose -f docker-compose.complete.yml restart [service-name]" $White
