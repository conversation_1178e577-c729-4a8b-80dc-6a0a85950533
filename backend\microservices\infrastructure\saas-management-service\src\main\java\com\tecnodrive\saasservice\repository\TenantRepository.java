package com.tecnodrive.saasservice.repository;

import com.tecnodrive.saasservice.entity.Tenant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Tenant Repository
 * 
 * Data access layer for Tenant entities
 */
@Repository
public interface TenantRepository extends JpaRepository<Tenant, UUID> {

    /**
     * Find tenant by name (case-insensitive)
     */
    Optional<Tenant> findByNameIgnoreCase(String name);

    /**
     * Find tenants by type
     */
    List<Tenant> findByType(Tenant.TenantType type);

    /**
     * Find tenants by status
     */
    List<Tenant> findByStatus(Tenant.TenantStatus status);

    /**
     * Find active tenants
     */
    @Query("SELECT t FROM Tenant t WHERE t.status = 'ACTIVE'")
    List<Tenant> findActiveTenants();

    /**
     * Find tenants by service type
     */
    List<Tenant> findByServiceType(Tenant.ServiceType serviceType);

    /**
     * Find tenants by pricing plan
     */
    List<Tenant> findByPricingPlanId(String pricingPlanId);

    /**
     * Find tenants by contact person
     */
    List<Tenant> findByContactPersonId(String contactPersonId);

    /**
     * Find tenants with expiring subscriptions
     */
    @Query("SELECT t FROM Tenant t WHERE t.subscriptionEndDate BETWEEN :startDate AND :endDate")
    List<Tenant> findTenantsWithExpiringSubscriptions(
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Find expired tenants
     */
    @Query("SELECT t FROM Tenant t WHERE t.subscriptionEndDate < :currentDate AND t.status != 'EXPIRED'")
    List<Tenant> findExpiredTenants(@Param("currentDate") Instant currentDate);

    /**
     * Find tenants created between dates
     */
    @Query("SELECT t FROM Tenant t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    List<Tenant> findTenantsCreatedBetween(
            @Param("startDate") Instant startDate, 
            @Param("endDate") Instant endDate
    );

    /**
     * Search tenants by name or display name
     */
    @Query("SELECT t FROM Tenant t WHERE LOWER(t.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(t.displayName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Tenant> searchTenants(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Count tenants by status
     */
    long countByStatus(Tenant.TenantStatus status);

    /**
     * Count tenants by type
     */
    long countByType(Tenant.TenantType type);

    /**
     * Check if tenant name exists (case-insensitive)
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Find tenants with custom query for analytics
     */
    @Query("SELECT t.type, COUNT(t) FROM Tenant t GROUP BY t.type")
    List<Object[]> getTenantCountByType();

    /**
     * Find tenants with custom query for status analytics
     */
    @Query("SELECT t.status, COUNT(t) FROM Tenant t GROUP BY t.status")
    List<Object[]> getTenantCountByStatus();
}
