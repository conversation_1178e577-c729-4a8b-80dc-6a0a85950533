package com.tecnodrive.parcelservice.service;

import com.tecnodrive.parcelservice.dto.ParcelDto;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import com.tecnodrive.parcelservice.repository.ParcelRepository;
import com.tecnodrive.parcelservice.service.impl.EnhancedParcelServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for Enhanced Parcel Service
 */
@ExtendWith(MockitoExtension.class)
class ParcelServiceTest {

    @Mock
    private ParcelRepository parcelRepository;

    private EnhancedParcelService parcelService;

    @BeforeEach
    void setUp() {
        parcelService = new EnhancedParcelServiceImpl(parcelRepository);
    }

    @Test
    void createParcel_ShouldCreateParcel_WhenValidRequest() {
        // Given
        ParcelDto request = createSampleParcelDto();
        ParcelEntity savedParcel = createSampleParcelEntity();

        when(parcelRepository.existsByBarcode(anyString())).thenReturn(false);
        when(parcelRepository.save(any(ParcelEntity.class))).thenReturn(savedParcel);

        // When
        ParcelDto result = parcelService.createParcel(request);

        // Then
        assertNotNull(result);
        assertEquals("CREATED", result.getStatus());
        assertEquals(request.getBarcode(), result.getBarcode());
        assertEquals(request.getSenderName(), result.getSenderName());
        verify(parcelRepository).save(any(ParcelEntity.class));
    }

    @Test
    void createParcel_ShouldThrowException_WhenBarcodeExists() {
        // Given
        ParcelDto request = createSampleParcelDto();
        when(parcelRepository.existsByBarcode(request.getBarcode())).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> parcelService.createParcel(request));
        assertEquals("Barcode already exists: " + request.getBarcode(), exception.getMessage());
    }

    @Test
    void getParcelById_ShouldReturnParcel_WhenParcelExists() {
        // Given
        String parcelId = "PCL-123";
        ParcelEntity parcel = createSampleParcelEntity();
        when(parcelRepository.findById(parcelId)).thenReturn(Optional.of(parcel));

        // When
        ParcelDto result = parcelService.getParcelById(parcelId);

        // Then
        assertNotNull(result);
        assertEquals(parcel.getParcelId(), result.getParcelId());
        assertEquals(parcel.getBarcode(), result.getBarcode());
    }

    @Test
    void getParcelById_ShouldThrowException_WhenParcelNotFound() {
        // Given
        String parcelId = "PCL-123";
        when(parcelRepository.findById(parcelId)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> parcelService.getParcelById(parcelId));
        assertEquals("Parcel not found with ID: " + parcelId, exception.getMessage());
    }

    @Test
    void getParcelByBarcode_ShouldReturnParcel_WhenBarcodeExists() {
        // Given
        String barcode = "123456789012";
        ParcelEntity parcel = createSampleParcelEntity();
        when(parcelRepository.findByBarcode(barcode)).thenReturn(Optional.of(parcel));

        // When
        ParcelDto result = parcelService.getParcelByBarcode(barcode);

        // Then
        assertNotNull(result);
        assertEquals(parcel.getBarcode(), result.getBarcode());
    }

    @Test
    void updateParcelStatus_ShouldUpdateStatus_WhenValidRequest() {
        // Given
        String parcelId = "PCL-123";
        ParcelEntity parcel = createSampleParcelEntity();
        parcel.setStatus(ParcelEntity.ParcelStatus.CREATED);

        when(parcelRepository.findById(parcelId)).thenReturn(Optional.of(parcel));
        when(parcelRepository.save(any(ParcelEntity.class))).thenReturn(parcel);

        // When
        ParcelDto result = parcelService.updateParcelStatus(parcelId,
            ParcelEntity.ParcelStatus.IN_TRANSIT, "user123");

        // Then
        assertNotNull(result);
        verify(parcelRepository).save(any(ParcelEntity.class));
    }

    @Test
    void getParcelsByUser_ShouldReturnPagedResults() {
        // Given
        String userId = "user123";
        Pageable pageable = PageRequest.of(0, 10);
        List<ParcelEntity> parcels = Arrays.asList(createSampleParcelEntity());
        Page<ParcelEntity> page = new PageImpl<>(parcels, pageable, 1);

        when(parcelRepository.findByUserId(userId, pageable)).thenReturn(page);

        // When
        Page<ParcelDto> result = parcelService.getParcelsByUser(userId, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }

    @Test
    void cancelParcel_ShouldCancelParcel_WhenCancellable() {
        // Given
        String parcelId = "PCL-123";
        ParcelEntity parcel = createSampleParcelEntity();
        parcel.setStatus(ParcelEntity.ParcelStatus.CREATED);

        when(parcelRepository.findById(parcelId)).thenReturn(Optional.of(parcel));
        when(parcelRepository.save(any(ParcelEntity.class))).thenReturn(parcel);

        // When
        ParcelDto result = parcelService.cancelParcel(parcelId, "Customer request", "user123");

        // Then
        assertNotNull(result);
        verify(parcelRepository).save(any(ParcelEntity.class));
    }

    // Helper methods
    private ParcelDto createSampleParcelDto() {
        return ParcelDto.builder()
                .userId("user123")
                .barcode("123456789012")
                .senderName("أحمد محمد")
                .receiverName("خالد علي")
                .senderAddress("شارع بغداد، صنعاء")
                .receiverAddress("شارع الزبيري، صنعاء")
                .weightKg(2.5)
                .dimensions(ParcelDto.DimensionsDto.builder()
                        .lengthCm(30)
                        .widthCm(20)
                        .heightCm(10)
                        .build())
                .priority("MEDIUM")
                .fragile(false)
                .build();
    }

    private ParcelEntity createSampleParcelEntity() {
        return ParcelEntity.builder()
                .parcelId("PCL-123")
                .userId("user123")
                .barcode("123456789012")
                .senderName("أحمد محمد")
                .receiverName("خالد علي")
                .senderAddress("شارع بغداد، صنعاء")
                .receiverAddress("شارع الزبيري، صنعاء")
                .weightKg(2.5)
                .dimensions(ParcelEntity.Dimensions.builder()
                        .lengthCm(30)
                        .widthCm(20)
                        .heightCm(10)
                        .build())
                .status(ParcelEntity.ParcelStatus.CREATED)
                .priority(ParcelEntity.ParcelPriority.MEDIUM)
                .fragile(false)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
