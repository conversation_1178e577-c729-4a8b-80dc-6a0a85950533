{{- if .Values.gatekeeper.enabled }}
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: CentralSecurity
metadata:
  name: tecno-drive-security-policy
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
    description: "TECNO DRIVE Central Security Constraint - Enforces security and resilience policies"
  labels:
    app.kubernetes.io/name: central-security-constraint
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tecno-drive.com/policy-type: "security"
spec:
  # Enforcement mode
  enforcementAction: {{ .Values.security.policies.centralSecurity.enforcement | default "warn" }}
  
  # Match criteria
  match:
    - excludedNamespaces: 
        {{- if .Values.security.policies.centralSecurity.exemptNamespaces }}
        {{- toYaml .Values.security.policies.centralSecurity.exemptNamespaces | nindent 8 }}
        {{- else }}
        - "kube-system"
        - "kube-public"
        - "kube-node-lease"
        - "gatekeeper-system"
        - "argocd"
        - "monitoring"
        - "logging"
        {{- end }}
      kinds:
        - apiGroups: [""]
          kinds: ["Pod", "Namespace"]
        - apiGroups: ["apps"]
          kinds: ["Deployment", "StatefulSet", "DaemonSet"]
        - apiGroups: ["batch"]
          kinds: ["Job", "CronJob"]
  
  # Parameters for the constraint
  parameters:
    enforcement: {{ .Values.security.policies.centralSecurity.enforcement | default "warn" }}
    
    exemptNamespaces:
      {{- if .Values.security.policies.centralSecurity.exemptNamespaces }}
      {{- toYaml .Values.security.policies.centralSecurity.exemptNamespaces | nindent 6 }}
      {{- else }}
      - "kube-system"
      - "kube-public"
      - "kube-node-lease"
      - "gatekeeper-system"
      - "argocd"
      - "monitoring"
      - "logging"
      {{- end }}
    
    securityChecks:
      privileged: {{ .Values.security.policies.centralSecurity.checks.privileged | default true }}
      hostNetwork: {{ .Values.security.policies.centralSecurity.checks.hostNetwork | default true }}
      hostPID: {{ .Values.security.policies.centralSecurity.checks.hostPID | default true }}
      hostIPC: {{ .Values.security.policies.centralSecurity.checks.hostIPC | default true }}
      capabilities: {{ .Values.security.policies.centralSecurity.checks.capabilities | default true }}
      securityContext: {{ .Values.security.policies.centralSecurity.checks.securityContext | default true }}
      resources: {{ .Values.security.policies.centralSecurity.checks.resources | default true }}
      probes: {{ .Values.security.policies.centralSecurity.checks.probes | default true }}
      networkPolicies: {{ .Values.security.policies.centralSecurity.checks.networkPolicies | default true }}
      podDisruptionBudgets: {{ .Values.security.policies.centralSecurity.checks.podDisruptionBudgets | default true }}

---
# Additional Security Constraints for TECNO DRIVE
{{- if .Values.security.policies.networkPolicies.enabled }}
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: requirednetworkpolicy
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: required-network-policy
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  crd:
    spec:
      names:
        kind: RequiredNetworkPolicy
      validation:
        type: object
        properties:
          exemptNamespaces:
            type: array
            items:
              type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package requirednetworkpolicy
        
        violation[{"msg": msg}] {
          input.review.object.kind == "Namespace"
          namespace := input.review.object.metadata.name
          not is_exempt_namespace(namespace)
          not has_network_policy(namespace)
          msg := sprintf("Namespace %s requires a NetworkPolicy", [namespace])
        }
        
        is_exempt_namespace(namespace) {
          exempt_namespaces := input.parameters.exemptNamespaces
          namespace == exempt_namespaces[_]
        }
        
        is_exempt_namespace(namespace) {
          namespace == "kube-system"
        }
        
        is_exempt_namespace(namespace) {
          namespace == "kube-public"
        }
        
        has_network_policy(namespace) {
          # This would check data.inventory for existing NetworkPolicies
          # For now, we'll implement a basic check
          data.inventory.namespace[namespace]["networking.k8s.io/v1"]["NetworkPolicy"]
        }

---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: RequiredNetworkPolicy
metadata:
  name: tecno-drive-network-policy-requirement
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: network-policy-requirement
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  enforcementAction: {{ .Values.security.policies.networkPolicies.enforcement | default "warn" }}
  match:
    - excludedNamespaces:
        - "kube-system"
        - "kube-public"
        - "kube-node-lease"
        - "gatekeeper-system"
      kinds:
        - apiGroups: [""]
          kinds: ["Namespace"]
  parameters:
    exemptNamespaces:
      - "kube-system"
      - "kube-public"
      - "kube-node-lease"
      - "gatekeeper-system"
      - "argocd"
      - "monitoring"
      - "logging"
{{- end }}

---
# Pod Security Standards Constraint
{{- if .Values.security.policies.podSecurity.enabled }}
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: podsecuritystandards
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  labels:
    app.kubernetes.io/name: pod-security-standards
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  crd:
    spec:
      names:
        kind: PodSecurityStandards
      validation:
        type: object
        properties:
          standard:
            type: string
            enum: ["privileged", "baseline", "restricted"]
          exemptNamespaces:
            type: array
            items:
              type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package podsecuritystandards
        
        violation[{"msg": msg}] {
          input.parameters.standard == "restricted"
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          not is_exempt_namespace
          spec := get_pod_spec(input.review.object)
          not spec.securityContext.runAsNonRoot
          msg := sprintf("%s %s: Pod Security Standard 'restricted' requires runAsNonRoot", [input.review.object.kind, input.review.object.metadata.name])
        }
        
        violation[{"msg": msg}] {
          input.parameters.standard in ["baseline", "restricted"]
          input.review.object.kind in ["Pod", "Deployment", "StatefulSet", "DaemonSet"]
          not is_exempt_namespace
          spec := get_pod_spec(input.review.object)
          spec.hostNetwork == true
          msg := sprintf("%s %s: Pod Security Standard '%s' prohibits hostNetwork", [input.review.object.kind, input.review.object.metadata.name, input.parameters.standard])
        }
        
        is_exempt_namespace {
          input.review.object.metadata.namespace
          exempt_namespaces := input.parameters.exemptNamespaces
          input.review.object.metadata.namespace == exempt_namespaces[_]
        }
        
        get_pod_spec(obj) = spec {
          obj.kind == "Pod"
          spec := obj.spec
        }
        
        get_pod_spec(obj) = spec {
          obj.kind in ["Deployment", "StatefulSet", "DaemonSet"]
          spec := obj.spec.template.spec
        }

---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: PodSecurityStandards
metadata:
  name: tecno-drive-pod-security-standards
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: pod-security-standards-constraint
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  enforcementAction: {{ .Values.security.policies.podSecurity.enforcement | default "warn" }}
  match:
    - excludedNamespaces:
        - "kube-system"
        - "kube-public"
        - "kube-node-lease"
        - "gatekeeper-system"
      kinds:
        - apiGroups: [""]
          kinds: ["Pod"]
        - apiGroups: ["apps"]
          kinds: ["Deployment", "StatefulSet", "DaemonSet"]
  parameters:
    standard: {{ .Values.security.policies.podSecurity.standard | default "restricted" }}
    exemptNamespaces:
      - "kube-system"
      - "kube-public"
      - "kube-node-lease"
      - "gatekeeper-system"
      - "argocd"
      - "monitoring"
      - "logging"
{{- end }}
{{- end }}
