# TECNO DRIVE Comprehensive Platform - Docker Compose
# Complete microservices architecture with all components

services:
  # =====================================================
  # DATABASE SERVICES
  # =====================================================
  
  # PostgreSQL Database for development
  postgres:
    image: postgres:15-alpine
    container_name: tecno-postgres
    environment:
      POSTGRES_DB: tecnodrive
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: tecnodrive_secure_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./database/sample_data.sql:/docker-entrypoint-initdb.d/02-sample.sql:ro
    networks:
      - tecno-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d tecnodrive"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: tecno-redis
    command: redis-server --requirepass tecnodrive_redis_2024 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - tecno-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # =====================================================
  # MESSAGE QUEUE SERVICES
  # =====================================================

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: tecno-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - tecno-network
    restart: unless-stopped

  # Kafka for event streaming
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: tecno-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - tecno-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # =====================================================
  # APPLICATION SERVICES
  # =====================================================

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tecno-backend
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************************/tecnodrive
      - REDIS_URL=redis://:tecnodrive_redis_2024@redis:6379/0
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - JWT_SECRET_KEY=tecnodrive_jwt_secret_key_2024_very_secure
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    volumes:
      - ./backend/logs:/app/logs
      - ./ai-models:/app/ai-models:ro
    networks:
      - tecno-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tecno-frontend
    depends_on:
      - backend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
      - REACT_APP_ENVIRONMENT=production
    volumes:
      - ./frontend/public/icons:/app/public/icons:ro
    networks:
      - tecno-network
    restart: unless-stopped

  # Stream Processing Service (Flink)
  flink-jobmanager:
    image: flink:1.17.1-scala_2.12-java11
    container_name: tecno-flink-jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
    volumes:
      - flink_data:/opt/flink/data
      - ./stream-processing/target:/opt/flink/usrlib
    networks:
      - tecno-network
    restart: unless-stopped

  flink-taskmanager:
    image: flink:1.17.1-scala_2.12-java11
    depends_on:
      - flink-jobmanager
    command: taskmanager
    deploy:
      replicas: 2
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      - TASK_MANAGER_NUMBER_OF_TASK_SLOTS=2
    volumes:
      - "flink_data:/opt/flink/data"
      - "./stream-processing/target:/opt/flink/usrlib"
    networks:
      - tecno-network
    restart: unless-stopped

  # AI Model Serving (TensorFlow Serving)
  ai-model-server:
    image: tensorflow/serving:2.13.0
    container_name: tecno-ai-models
    ports:
      - "8500:8500"  # gRPC
      - "8501:8501"  # REST API
    environment:
      - MODEL_NAME=demand_prediction
      - MODEL_BASE_PATH=/models
    volumes:
      - ./ai-models/saved_models:/models
    networks:
      - tecno-network
    restart: unless-stopped

  # =====================================================
  # MONITORING SERVICES
  # =====================================================

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: tecno-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - tecno-network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:10.0.0
    container_name: tecno-grafana
    depends_on:
      - prometheus
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=tecnodrive_grafana_2024
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - tecno-network
    restart: unless-stopped

  # Zipkin for distributed tracing
  zipkin:
    image: openzipkin/zipkin:2.24
    container_name: tecno-zipkin
    ports:
      - "9411:9411"
    environment:
      - STORAGE_TYPE=mem
    networks:
      - tecno-network
    restart: unless-stopped

  # =====================================================
  # UTILITY SERVICES
  # =====================================================

  # Nginx reverse proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: tecno-nginx
    depends_on:
      - frontend
      - backend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - tecno-network
    restart: unless-stopped

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: tecno-redis-commander
    depends_on:
      - redis
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:tecnodrive_redis_2024
    networks:
      - tecno-network
    restart: unless-stopped

  # Kafka UI for Kafka management
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: tecno-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8083:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:29092
      - KAFKA_CLUSTERS_0_ZOOKEEPER=zookeeper:2181
    networks:
      - tecno-network
    restart: unless-stopped

# =====================================================
# NETWORKS
# =====================================================
networks:
  tecno-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =====================================================
# VOLUMES
# =====================================================
volumes:
  timescale_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  flink_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
