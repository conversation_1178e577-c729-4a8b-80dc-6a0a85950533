server:
  port: 8089

spring:
  application:
    name: hr-service
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_hr
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false

  cache:
    type: simple
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics

# Logging
logging:
  level:
    com.tecnodrive.hrservice: DEBUG

# HR Configuration
hr:
  employee:
    default-status: ACTIVE
    probation-period-months: 3
    annual-leave-days: 21
    sick-leave-days: 10
  payroll:
    pay-frequency: MONTHLY
    overtime-rate: 1.5
    currency: "RY"
  performance:
    review-frequency: ANNUAL
    rating-scale: 5
    goals-per-employee: 5
  leave:
    max-consecutive-days: 30
    advance-notice-days: 7
    approval-required: true
  training:
    budget-per-employee: 5000
    mandatory-hours-per-year: 40

# Feign Configuration
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000

# Async Configuration
async:
  core-pool-size: 5
  max-pool-size: 10
  queue-capacity: 100

# Scheduling Configuration
scheduling:
  payroll-processing-cron: "0 0 1 * * *"
  performance-review-reminder-cron: "0 0 9 * * MON"
  leave-balance-update-cron: "0 0 2 1 * *"
