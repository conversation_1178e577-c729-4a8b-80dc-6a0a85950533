# TecnoDrive Live Operations Dashboard - Quick Start Script
# This script starts the dashboard with enhanced mock data

Write-Host "🚀 Starting TecnoDrive Live Operations Dashboard" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "frontend/admin-dashboard/package.json")) {
    Write-Host "❌ Error: Please run this script from the project root directory" -ForegroundColor Red
    exit 1
}

Write-Host "📦 Checking dependencies..." -ForegroundColor Yellow

# Navigate to frontend directory
Set-Location "frontend/admin-dashboard"

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Check if Google Maps dependencies are installed
$packageJson = Get-Content "package.json" | ConvertFrom-Json
if (-not $packageJson.dependencies."@googlemaps/react-wrapper") {
    Write-Host "📦 Installing Google Maps dependencies..." -ForegroundColor Yellow
    npm install @googlemaps/react-wrapper @types/google.maps --legacy-peer-deps
}

Write-Host "🔧 Checking environment configuration..." -ForegroundColor Yellow

# Check if .env file exists
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Write-Host "📝 Creating .env file from .env.example..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
    } else {
        Write-Host "⚠️  Creating basic .env file..." -ForegroundColor Yellow
        @"
# TecnoDrive Admin Dashboard Environment Variables
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_WEBSOCKET_URL=ws://localhost:8080
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
REACT_APP_ENABLE_MOCK_DATA=true
"@ | Out-File -FilePath ".env" -Encoding UTF8
    }
}

# Check if Google Maps API key is set
$envContent = Get-Content ".env" -Raw
if ($envContent -match "REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here" -or 
    $envContent -notmatch "REACT_APP_GOOGLE_MAPS_API_KEY=") {
    Write-Host "⚠️  Google Maps API key not configured!" -ForegroundColor Yellow
    Write-Host "   The map will show a development warning." -ForegroundColor Yellow
    Write-Host "   To fix this, update REACT_APP_GOOGLE_MAPS_API_KEY in .env file" -ForegroundColor Yellow
}

Write-Host "✅ Environment configured successfully!" -ForegroundColor Green

Write-Host "`n🎯 Available Features in Phase 1:" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host "✅ Live Operations Dashboard (/live-operations)" -ForegroundColor Green
Write-Host "   • Interactive map with 25 mock vehicles" -ForegroundColor White
Write-Host "   • Real-time alerts panel with 8 sample alerts" -ForegroundColor White
Write-Host "   • Live statistics and status indicators" -ForegroundColor White
Write-Host "   • WebSocket simulation for real-time updates" -ForegroundColor White

Write-Host "`n✅ Enhanced Rides Management (/rides/enhanced)" -ForegroundColor Green
Write-Host "   • 50 sample rides with realistic data" -ForegroundColor White
Write-Host "   • Advanced search and filtering" -ForegroundColor White
Write-Host "   • Real-time statistics dashboard" -ForegroundColor White
Write-Host "   • Export capabilities" -ForegroundColor White

Write-Host "`n✅ Mock Data Features:" -ForegroundColor Green
Write-Host "   • Saudi driver and customer names" -ForegroundColor White
Write-Host "   • Riyadh locations and coordinates" -ForegroundColor White
Write-Host "   • Realistic vehicle movements" -ForegroundColor White
Write-Host "   • Arabic alert messages" -ForegroundColor White

Write-Host "`n🚀 Starting development server..." -ForegroundColor Green

# Start the development server
Write-Host "📱 Opening browser to http://localhost:3000" -ForegroundColor Yellow
Write-Host "🗺️  Navigate to /live-operations to see the live dashboard" -ForegroundColor Yellow

# Start npm in a new process so we can continue with instructions
Start-Process -FilePath "npm" -ArgumentList "start" -NoNewWindow

Write-Host "`n⏳ Waiting for server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "`n🎉 Dashboard is starting!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

Write-Host "`n📍 Quick Navigation:" -ForegroundColor Cyan
Write-Host "• http://localhost:3000/live-operations - Live Operations Dashboard" -ForegroundColor White
Write-Host "• http://localhost:3000/rides/enhanced - Enhanced Rides Management" -ForegroundColor White
Write-Host "• http://localhost:3000/dashboard - Main Dashboard" -ForegroundColor White

Write-Host "`n🔧 Testing Features:" -ForegroundColor Cyan
Write-Host "1. Live Map:" -ForegroundColor Yellow
Write-Host "   • Click on vehicle markers to see details" -ForegroundColor White
Write-Host "   • Hover over markers for quick info" -ForegroundColor White
Write-Host "   • Use map controls (zoom, refresh, recenter)" -ForegroundColor White

Write-Host "`n2. Alerts Panel:" -ForegroundColor Yellow
Write-Host "   • Filter alerts by severity (Critical, Warning, All)" -ForegroundColor White
Write-Host "   • Acknowledge alerts by clicking the check icon" -ForegroundColor White
Write-Host "   • Expand alerts for detailed information" -ForegroundColor White

Write-Host "`n3. Enhanced Rides:" -ForegroundColor Yellow
Write-Host "   • Use the search box to find specific rides" -ForegroundColor White
Write-Host "   • Filter by status, vehicle type, payment status" -ForegroundColor White
Write-Host "   • Use date range filters" -ForegroundColor White
Write-Host "   • Export data using the toolbar" -ForegroundColor White

Write-Host "`n🔄 Real-time Simulation:" -ForegroundColor Cyan
Write-Host "• Vehicle positions update every 5 seconds" -ForegroundColor White
Write-Host "• New alerts appear randomly" -ForegroundColor White
Write-Host "• Statistics refresh automatically" -ForegroundColor White
Write-Host "• Toggle 'Live Mode' to pause/resume updates" -ForegroundColor White

Write-Host "`n📊 Sample Data:" -ForegroundColor Cyan
Write-Host "• 25 vehicles (mix of passenger and delivery)" -ForegroundColor White
Write-Host "• 15 parcels in various states" -ForegroundColor White
Write-Host "• 8 alerts with different severities" -ForegroundColor White
Write-Host "• 50 rides with complete details" -ForegroundColor White

Write-Host "`n🛠️  Development Notes:" -ForegroundColor Cyan
Write-Host "• All data is simulated for demonstration" -ForegroundColor White
Write-Host "• WebSocket connections fall back to mock data" -ForegroundColor White
Write-Host "• Google Maps requires API key for full functionality" -ForegroundColor White
Write-Host "• Components are fully responsive" -ForegroundColor White

Write-Host "`n🎯 Next Steps (Phase 2):" -ForegroundColor Cyan
Write-Host "• Wallet management integration" -ForegroundColor White
Write-Host "• Real backend API connections" -ForegroundColor White
Write-Host "• Advanced analytics and AI features" -ForegroundColor White
Write-Host "• Corporate client management" -ForegroundColor White

Write-Host "`n✨ Enjoy testing the Live Operations Dashboard!" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server when done." -ForegroundColor Yellow

# Return to original directory
Set-Location "../.."
