import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Map as MapIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  LocationOn as LocationIcon,
  DirectionsCar as CarIcon,
  Speed as SpeedIcon,
  Update as UpdateIcon,
  Info as InfoIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import RealInteractiveMap from '../components/Maps/RealInteractiveMap';

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface MapStats {
  totalVehicles: number;
  activeVehicles: number;
  idleVehicles: number;
  averageSpeed: number;
  lastUpdate: string;
  mapCenter: { lat: number; lng: number };
  coverage: {
    lat: { min: number; max: number };
    lng: { min: number; max: number };
  };
}

const RealMapPage: React.FC = () => {
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [mapStats, setMapStats] = useState<MapStats | null>(null);
  const [apiStatus, setApiStatus] = useState<{
    mapService: boolean;
    vehicleData: boolean;
    tileServers: boolean;
  }>({
    mapService: false,
    vehicleData: false,
    tileServers: false
  });
  const [clickedLocation, setClickedLocation] = useState<{lat: number; lng: number} | null>(null);
  const [showVehicleDialog, setShowVehicleDialog] = useState(false);
  const [showLocationDialog, setShowLocationDialog] = useState(false);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);

  useEffect(() => {
    testMapAPIs();
    loadMapStats();
    
    // Set up real-time updates
    if (realTimeUpdates) {
      const interval = setInterval(() => {
        testMapAPIs();
        loadMapStats();
      }, 30000); // Update every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [realTimeUpdates]);

  const testMapAPIs = async () => {
    const endpoints = [
      { name: 'mapService', url: 'http://localhost:8085/health' },
      { name: 'vehicleData', url: 'http://localhost:8085/api/map/vehicles' },
      { name: 'tileServers', url: 'http://localhost:8085/api/map/tiles' }
    ];

    const newStatus = { mapService: false, vehicleData: false, tileServers: false };

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.url);
        newStatus[endpoint.name as keyof typeof newStatus] = response.ok;
      } catch (error) {
        console.error(`Failed to test ${endpoint.name}:`, error);
      }
    }

    setApiStatus(newStatus);
  };

  const loadMapStats = async () => {
    try {
      const response = await fetch('http://localhost:8085/api/locations/stats');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setMapStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to load map stats:', error);
    }
  };

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setShowVehicleDialog(true);
  };

  const handleMapClick = (lat: number, lng: number) => {
    setClickedLocation({ lat, lng });
    setShowLocationDialog(true);
  };

  const handleRefreshAll = () => {
    testMapAPIs();
    loadMapStats();
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <MapIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          خرائط OpenStreetMap حقيقية وتفاعلية
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          خرائط تفاعلية مع بيانات حقيقية ومتابعة المركبات في الوقت الفعلي
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Control Panel */}
        <Grid item xs={12} md={4}>
          {/* API Status */}
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6">
                  حالة الخدمات
                </Typography>
                <IconButton onClick={handleRefreshAll} size="small">
                  <RefreshIcon />
                </IconButton>
              </Box>
              
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">خدمة الخرائط</Typography>
                  <Chip
                    icon={apiStatus.mapService ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.mapService ? 'متصل' : 'غير متصل'}
                    color={apiStatus.mapService ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">بيانات المركبات</Typography>
                  <Chip
                    icon={apiStatus.vehicleData ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.vehicleData ? 'متصل' : 'غير متصل'}
                    color={apiStatus.vehicleData ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body2">خوادم الخرائط</Typography>
                  <Chip
                    icon={apiStatus.tileServers ? <CheckCircleIcon /> : <ErrorIcon />}
                    label={apiStatus.tileServers ? 'متصل' : 'غير متصل'}
                    color={apiStatus.tileServers ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Box>

              <FormControlLabel
                control={
                  <Switch
                    checked={realTimeUpdates}
                    onChange={(e) => setRealTimeUpdates(e.target.checked)}
                  />
                }
                label="التحديث في الوقت الفعلي"
                sx={{ mt: 2 }}
              />
            </CardContent>
          </Card>

          {/* Map Statistics */}
          {mapStats && (
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  إحصائيات الخريطة
                </Typography>
                
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <CarIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="إجمالي المركبات"
                      secondary={mapStats.totalVehicles}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="المركبات النشطة"
                      secondary={mapStats.activeVehicles}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <ErrorIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="المركبات المتوقفة"
                      secondary={mapStats.idleVehicles}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <SpeedIcon color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="متوسط السرعة"
                      secondary={`${mapStats.averageSpeed.toFixed(1)} كم/س`}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <UpdateIcon color="secondary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="آخر تحديث"
                      secondary={new Date(mapStats.lastUpdate).toLocaleString('ar-SA')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          )}

          {/* Map Features */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                ميزات الخريطة
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="خرائط OpenStreetMap حقيقية" />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="تفاعل كامل (تكبير، تصغير، سحب)" />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="مزودي خرائط متعددين" />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="تتبع المركبات في الوقت الفعلي" />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="تحديد الموقع الجغرافي" />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText primary="عرض الأقمار الصناعية" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Map Panel */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              <Typography variant="h6">
                خريطة تفاعلية - الرياض، المملكة العربية السعودية
              </Typography>
              <Button
                variant="outlined"
                startIcon={<InfoIcon />}
                size="small"
                onClick={() => alert('اضغط على المركبات لرؤية التفاصيل\nاضغط على الخريطة لرؤية الإحداثيات')}
              >
                مساعدة
              </Button>
            </Box>
            
            {!apiStatus.mapService ? (
              <Alert severity="warning" sx={{ mb: 2 }}>
                تحذير: خدمة الخرائط غير متصلة. قد تعمل الخريطة بوضع محدود.
              </Alert>
            ) : (
              <Alert severity="success" sx={{ mb: 2 }}>
                خدمة الخرائط متصلة وتعمل بشكل مثالي.
              </Alert>
            )}

            <RealInteractiveMap
              height="600px"
              showControls={true}
              showVehicles={true}
              onVehicleClick={handleVehicleClick}
              onMapClick={handleMapClick}
            />
          </Paper>
        </Grid>
      </Grid>

      {/* Vehicle Details Dialog */}
      <Dialog
        open={showVehicleDialog}
        onClose={() => setShowVehicleDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          تفاصيل المركبة {selectedVehicle?.id}
        </DialogTitle>
        <DialogContent>
          {selectedVehicle && (
            <Box>
              <TextField
                label="معرف المركبة"
                value={selectedVehicle.id}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="السائق"
                value={selectedVehicle.driver}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="السرعة (كم/س)"
                value={selectedVehicle.speed}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="الاتجاه (درجة)"
                value={selectedVehicle.heading}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="خط العرض"
                value={selectedVehicle.lat.toFixed(6)}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="خط الطول"
                value={selectedVehicle.lng.toFixed(6)}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="الحالة"
                value={selectedVehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="آخر تحديث"
                value={new Date(selectedVehicle.lastUpdate).toLocaleString('ar-SA')}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowVehicleDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Location Details Dialog */}
      <Dialog
        open={showLocationDialog}
        onClose={() => setShowLocationDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          تفاصيل الموقع المحدد
        </DialogTitle>
        <DialogContent>
          {clickedLocation && (
            <Box>
              <TextField
                label="خط العرض"
                value={clickedLocation.lat.toFixed(6)}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="خط الطول"
                value={clickedLocation.lng.toFixed(6)}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <Alert severity="info" sx={{ mt: 2 }}>
                يمكنك استخدام هذه الإحداثيات لإضافة نقاط اهتمام جديدة أو تحديد مواقع المركبات.
              </Alert>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLocationDialog(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default RealMapPage;
