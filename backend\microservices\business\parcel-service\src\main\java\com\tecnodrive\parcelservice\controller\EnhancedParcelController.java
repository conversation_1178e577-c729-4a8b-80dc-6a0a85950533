package com.tecnodrive.parcelservice.controller;

import com.tecnodrive.parcelservice.dto.ParcelDto;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import com.tecnodrive.parcelservice.service.EnhancedParcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Enhanced Parcel Controller
 */
@RestController
@RequestMapping("/parcels")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class EnhancedParcelController {

    private final EnhancedParcelService parcelService;

    /**
     * إنشاء طرد جديد
     */
    @PostMapping
    public ResponseEntity<ParcelDto> createParcel(@Valid @RequestBody ParcelDto parcelDto) {
        try {
            log.info("Creating new parcel for user: {}", parcelDto.getUserId());
            ParcelDto createdParcel = parcelService.createParcel(parcelDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdParcel);
        } catch (Exception e) {
            log.error("Error creating parcel: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * تحديث طرد موجود
     */
    @PutMapping("/{parcelId}")
    public ResponseEntity<ParcelDto> updateParcel(
            @PathVariable String parcelId,
            @Valid @RequestBody ParcelDto parcelDto) {
        try {
            log.info("Updating parcel: {}", parcelId);
            ParcelDto updatedParcel = parcelService.updateParcel(parcelId, parcelDto);
            return ResponseEntity.ok(updatedParcel);
        } catch (RuntimeException e) {
            log.error("Error updating parcel {}: {}", parcelId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            log.error("Unexpected error updating parcel {}: {}", parcelId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * الحصول على طرد بالمعرف
     */
    @GetMapping("/{parcelId}")
    public ResponseEntity<ParcelDto> getParcelById(@PathVariable String parcelId) {
        try {
            ParcelDto parcel = parcelService.getParcelById(parcelId);
            return ResponseEntity.ok(parcel);
        } catch (RuntimeException e) {
            log.error("Parcel not found: {}", parcelId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * الحصول على طرد بالباركود
     */
    @GetMapping("/barcode/{barcode}")
    public ResponseEntity<ParcelDto> getParcelByBarcode(@PathVariable String barcode) {
        try {
            ParcelDto parcel = parcelService.getParcelByBarcode(barcode);
            return ResponseEntity.ok(parcel);
        } catch (RuntimeException e) {
            log.error("Parcel not found with barcode: {}", barcode);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * الحصول على طرود المستخدم
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<ParcelDto>> getParcelsByUser(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<ParcelDto> parcels = parcelService.getParcelsByUser(userId, pageable);
        return ResponseEntity.ok(parcels);
    }

    /**
     * الحصول على طرود بحالة معينة
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<Page<ParcelDto>> getParcelsByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        try {
            ParcelEntity.ParcelStatus parcelStatus = ParcelEntity.ParcelStatus.valueOf(status.toUpperCase());
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            
            Page<ParcelDto> parcels = parcelService.getParcelsByStatus(parcelStatus, pageable);
            return ResponseEntity.ok(parcels);
        } catch (IllegalArgumentException e) {
            log.error("Invalid status: {}", status);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * تحديث حالة الطرد
     */
    @PatchMapping("/{parcelId}/status")
    public ResponseEntity<ParcelDto> updateParcelStatus(
            @PathVariable String parcelId,
            @RequestBody Map<String, String> statusUpdate) {
        
        try {
            String newStatus = statusUpdate.get("status");
            String updatedBy = statusUpdate.get("updatedBy");
            
            if (newStatus == null || updatedBy == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
            ParcelEntity.ParcelStatus status = ParcelEntity.ParcelStatus.valueOf(newStatus.toUpperCase());
            ParcelDto updatedParcel = parcelService.updateParcelStatus(parcelId, status, updatedBy);
            
            return ResponseEntity.ok(updatedParcel);
        } catch (IllegalArgumentException e) {
            log.error("Invalid status in update request");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (RuntimeException e) {
            log.error("Error updating parcel status: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * البحث في الطرود
     */
    @GetMapping("/search")
    public ResponseEntity<Page<ParcelDto>> searchParcels(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<ParcelDto> parcels = parcelService.searchParcels(query, pageable);
        
        return ResponseEntity.ok(parcels);
    }

    /**
     * الحصول على جميع الطرود
     */
    @GetMapping
    public ResponseEntity<List<ParcelDto>> getAllParcels() {
        List<ParcelDto> parcels = parcelService.getAllParcels();
        return ResponseEntity.ok(parcels);
    }

    /**
     * حذف طرد
     */
    @DeleteMapping("/{parcelId}")
    public ResponseEntity<Void> deleteParcel(@PathVariable String parcelId) {
        try {
            parcelService.deleteParcel(parcelId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            log.error("Error deleting parcel {}: {}", parcelId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * إلغاء طرد
     */
    @PostMapping("/{parcelId}/cancel")
    public ResponseEntity<ParcelDto> cancelParcel(
            @PathVariable String parcelId,
            @RequestBody Map<String, String> cancellationRequest) {
        
        try {
            String reason = cancellationRequest.get("reason");
            String cancelledBy = cancellationRequest.get("cancelledBy");
            
            if (reason == null || cancelledBy == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
            ParcelDto cancelledParcel = parcelService.cancelParcel(parcelId, reason, cancelledBy);
            return ResponseEntity.ok(cancelledParcel);
        } catch (RuntimeException e) {
            log.error("Error cancelling parcel {}: {}", parcelId, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * الحصول على إحصائيات الطرود
     */
    @GetMapping("/statistics")
    public ResponseEntity<Object> getParcelStatistics() {
        Object statistics = parcelService.getParcelStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * الحصول على حالات الطرود المتاحة
     */
    @GetMapping("/statuses")
    public ResponseEntity<ParcelEntity.ParcelStatus[]> getParcelStatuses() {
        return ResponseEntity.ok(ParcelEntity.ParcelStatus.values());
    }

    /**
     * الحصول على أولويات الطرود المتاحة
     */
    @GetMapping("/priorities")
    public ResponseEntity<ParcelEntity.ParcelPriority[]> getParcelPriorities() {
        return ResponseEntity.ok(ParcelEntity.ParcelPriority.values());
    }

    /**
     * Health Check
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "parcel-service",
            "timestamp", String.valueOf(System.currentTimeMillis())
        ));
    }
}
