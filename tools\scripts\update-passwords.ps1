# TECNODRIVE Platform - Password Update Script
# This script updates database passwords securely

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateNew = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$UpdateEnvFile = $false
)

# Function to generate secure password
function Generate-SecurePassword {
    param([int]$Length = 32)
    
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    $password = ""
    
    for ($i = 0; $i -lt $Length; $i++) {
        $password += $chars[(Get-Random -Maximum $chars.Length)]
    }
    
    return $password
}

# Function to update PostgreSQL user password
function Update-PostgreSQLPassword {
    param(
        [string]$Username,
        [string]$NewPassword,
        [string]$Host = "localhost",
        [int]$Port = 5432
    )
    
    try {
        $env:PGPASSWORD = "postgres"  # Current admin password
        $query = "ALTER USER $Username WITH PASSWORD '$NewPassword';"
        
        psql -h $Host -p $Port -U postgres -d postgres -c $query
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Password updated successfully for user: $Username" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to update password for user: $Username" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error updating password for $Username`: $_" -ForegroundColor Red
        return $false
    }
}

# Function to update Redis password
function Update-RedisPassword {
    param(
        [string]$NewPassword,
        [string]$Host = "localhost",
        [int]$Port = 6379
    )
    
    try {
        # Update Redis configuration
        $redisConfigPath = "redis-config/redis.conf"
        
        if (Test-Path $redisConfigPath) {
            $content = Get-Content $redisConfigPath
            $content = $content -replace "requirepass .*", "requirepass $NewPassword"
            Set-Content -Path $redisConfigPath -Value $content
            
            Write-Host "✅ Redis configuration updated successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Redis configuration file not found" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error updating Redis password: $_" -ForegroundColor Red
        return $false
    }
}

# Function to update environment file
function Update-EnvironmentFile {
    param(
        [hashtable]$Passwords
    )
    
    try {
        $envFile = ".env"
        
        if (-not (Test-Path $envFile)) {
            Copy-Item ".env.example" $envFile
        }
        
        $content = Get-Content $envFile
        
        foreach ($key in $Passwords.Keys) {
            $pattern = "^$key=.*"
            $replacement = "$key=$($Passwords[$key])"
            
            if ($content -match $pattern) {
                $content = $content -replace $pattern, $replacement
            } else {
                $content += $replacement
            }
        }
        
        Set-Content -Path $envFile -Value $content
        Write-Host "✅ Environment file updated successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Error updating environment file: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "🔐 TECNODRIVE Password Update Script" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host ""

# Generate new passwords if requested
if ($GenerateNew) {
    Write-Host "🔄 Generating new passwords..." -ForegroundColor Blue
    
    $newPasswords = @{
        "DB_PASSWORD" = "TecnoDrive2025!Secure#Platform$(Get-Random -Maximum 9999)"
        "REDIS_PASSWORD" = "TecnoDrive2025!Redis#Cache$(Get-Random -Maximum 9999)"
        "JWT_SECRET" = "TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2025!$(Get-Random -Maximum 9999)"
    }
    
    Write-Host "Generated new passwords:" -ForegroundColor Green
    foreach ($key in $newPasswords.Keys) {
        Write-Host "  $key`: [HIDDEN]" -ForegroundColor Gray
    }
    Write-Host ""
} else {
    # Use existing passwords from environment or defaults
    $newPasswords = @{
        "DB_PASSWORD" = $env:DB_PASSWORD ?? "TecnoDrive2025!Secure#Platform"
        "REDIS_PASSWORD" = $env:REDIS_PASSWORD ?? "TecnoDrive2025!Redis#Cache"
        "JWT_SECRET" = $env:JWT_SECRET ?? "TecnoDriveSecretKeyForJWTTokenGenerationAndValidation2025!"
    }
}

# Update PostgreSQL passwords
Write-Host "🗄️ Updating PostgreSQL passwords..." -ForegroundColor Blue

$dbUsers = @("tecnodrive_admin", "tecnodrive_readonly", "tecnodrive_backup")
$success = $true

foreach ($user in $dbUsers) {
    $password = $newPasswords["DB_PASSWORD"]
    if ($user -eq "tecnodrive_readonly") {
        $password = $newPasswords["DB_PASSWORD"] -replace "Platform", "ReadOnly"
    } elseif ($user -eq "tecnodrive_backup") {
        $password = $newPasswords["DB_PASSWORD"] -replace "Platform", "Backup"
    }
    
    $result = Update-PostgreSQLPassword -Username $user -NewPassword $password
    if (-not $result) {
        $success = $false
    }
}

# Update Redis password
Write-Host ""
Write-Host "🔴 Updating Redis password..." -ForegroundColor Blue
$redisResult = Update-RedisPassword -NewPassword $newPasswords["REDIS_PASSWORD"]
if (-not $redisResult) {
    $success = $false
}

# Update environment file if requested
if ($UpdateEnvFile) {
    Write-Host ""
    Write-Host "📝 Updating environment file..." -ForegroundColor Blue
    $envResult = Update-EnvironmentFile -Passwords $newPasswords
    if (-not $envResult) {
        $success = $false
    }
}

# Summary
Write-Host ""
if ($success) {
    Write-Host "✅ All passwords updated successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔄 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Restart all services to apply new passwords"
    Write-Host "2. Update application configuration files"
    Write-Host "3. Test all service connections"
    Write-Host "4. Update monitoring and backup scripts"
    Write-Host ""
    Write-Host "⚠️  Security reminders:" -ForegroundColor Red
    Write-Host "- Store passwords securely"
    Write-Host "- Update passwords regularly (every 90 days)"
    Write-Host "- Monitor for unauthorized access"
    Write-Host "- Keep audit logs"
} else {
    Write-Host "❌ Some password updates failed. Please check the errors above." -ForegroundColor Red
    exit 1
}

# Create password rotation reminder
$reminderDate = (Get-Date).AddDays(90).ToString("yyyy-MM-dd")
$reminderFile = "password-rotation-reminder.txt"
Set-Content -Path $reminderFile -Value "Next password rotation due: $reminderDate"

Write-Host "📅 Password rotation reminder created: $reminderFile" -ForegroundColor Cyan
