package com.tecnodrive.analyticsservice.service;

import com.tecnodrive.analyticsservice.dto.ReportRequest;
import com.tecnodrive.analyticsservice.dto.ReportResponse;

/**
 * Report Service Interface
 * 
 * Defines business logic operations for report generation and analytics
 */
public interface ReportService {

    /**
     * Generate a report based on the request parameters
     */
    ReportResponse generateReport(ReportRequest request);

    /**
     * Export report to CSV format
     */
    byte[] exportToCsv(ReportResponse report);

    /**
     * Export report to PDF format
     */
    byte[] exportToPdf(ReportResponse report);

    /**
     * Export report to Excel format
     */
    byte[] exportToExcel(ReportResponse report);

    /**
     * Export report to HTML format
     */
    String exportToHtml(ReportResponse report);

    /**
     * Get dashboard metrics for real-time display
     */
    ReportResponse getDashboardMetrics(String companyId);

    /**
     * Get financial summary for a specific period
     */
    ReportResponse.FinancialSummary getFinancialSummary(String companyId, ReportRequest.Period period);

    /**
     * Get operational metrics for a specific period
     */
    ReportResponse.OperationalMetrics getOperationalMetrics(String companyId, ReportRequest.Period period);

    /**
     * Get user analytics for a specific period
     */
    ReportResponse.UserAnalytics getUserAnalytics(String companyId, ReportRequest.Period period);

    /**
     * Generate custom report with SQL query
     */
    ReportResponse generateCustomReport(String companyId, String query, ReportRequest.Format format);

    /**
     * Schedule a report for automatic generation
     */
    void scheduleReport(ReportRequest request, String cronExpression);

    /**
     * Get available report types for a company
     */
    java.util.List<ReportRequest.ReportType> getAvailableReportTypes(String companyId);

    /**
     * Validate report request parameters
     */
    void validateReportRequest(ReportRequest request);
}
