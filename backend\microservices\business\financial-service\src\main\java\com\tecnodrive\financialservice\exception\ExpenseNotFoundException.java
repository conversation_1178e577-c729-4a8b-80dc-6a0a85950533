package com.tecnodrive.financialservice.exception;

/**
 * Expense Not Found Exception
 * 
 * Thrown when a requested expense cannot be found
 */
public class ExpenseNotFoundException extends RuntimeException {

    public ExpenseNotFoundException() {
        super("Expense not found");
    }

    public ExpenseNotFoundException(String message) {
        super(message);
    }

    public ExpenseNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
