package com.tecnodrive.walletservice.service;

import com.tecnodrive.walletservice.dto.WalletTransactionDTO;
import com.tecnodrive.walletservice.entity.WalletTransaction;
import com.tecnodrive.walletservice.repository.WalletTransactionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Wallet Transaction Service
 * Business logic for wallet transaction management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class WalletTransactionService {

    private final WalletTransactionRepository transactionRepository;

    /**
     * Get transaction by ID
     */
    @Cacheable(value = "transactions", key = "#transactionId")
    public Optional<WalletTransactionDTO> getTransactionById(UUID transactionId) {
        return transactionRepository.findById(transactionId)
                .map(WalletTransactionDTO::fromEntity);
    }

    /**
     * Get transaction by reference
     */
    @Cacheable(value = "transactions", key = "'ref:' + #transactionReference")
    public Optional<WalletTransactionDTO> getTransactionByReference(String transactionReference) {
        return transactionRepository.findByTransactionReference(transactionReference)
                .map(WalletTransactionDTO::fromEntity);
    }

    /**
     * Get transactions for wallet
     */
    public List<WalletTransactionDTO> getWalletTransactions(UUID walletId) {
        return transactionRepository.findByWalletIdOrderByCreatedAtDesc(walletId)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions for wallet with pagination
     */
    public Page<WalletTransactionDTO> getWalletTransactions(UUID walletId, Pageable pageable) {
        return transactionRepository.findByWalletIdOrderByCreatedAtDesc(walletId, pageable)
                .map(WalletTransactionDTO::fromEntity);
    }

    /**
     * Get recent transactions for wallet
     */
    public List<WalletTransactionDTO> getRecentTransactions(UUID walletId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return transactionRepository.findRecentTransactions(walletId, pageable)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by wallet and date range
     */
    public List<WalletTransactionDTO> getTransactionsByDateRange(UUID walletId, 
                                                                LocalDateTime startDate, 
                                                                LocalDateTime endDate) {
        return transactionRepository.findByWalletIdAndDateRange(walletId, startDate, endDate)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by wallet and date range with pagination
     */
    public Page<WalletTransactionDTO> getTransactionsByDateRange(UUID walletId, 
                                                                LocalDateTime startDate, 
                                                                LocalDateTime endDate,
                                                                Pageable pageable) {
        return transactionRepository.findByWalletIdAndDateRange(walletId, startDate, endDate, pageable)
                .map(WalletTransactionDTO::fromEntity);
    }

    /**
     * Get transactions by status
     */
    public List<WalletTransactionDTO> getTransactionsByStatus(WalletTransaction.TransactionStatus status) {
        return transactionRepository.findByStatus(status)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by type
     */
    public List<WalletTransactionDTO> getTransactionsByType(WalletTransaction.TransactionType type) {
        return transactionRepository.findByType(type)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by source
     */
    public List<WalletTransactionDTO> getTransactionsBySource(WalletTransaction.TransactionSource source) {
        return transactionRepository.findBySource(source)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by reference ID and type
     */
    public List<WalletTransactionDTO> getTransactionsByReference(String referenceId, String referenceType) {
        return transactionRepository.findByReferenceIdAndReferenceType(referenceId, referenceType)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions by agent
     */
    public List<WalletTransactionDTO> getTransactionsByAgent(UUID agentId) {
        return transactionRepository.findByAgentId(agentId)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Search transactions
     */
    public Page<WalletTransactionDTO> searchTransactions(UUID walletId,
                                                         WalletTransaction.TransactionType type,
                                                         WalletTransaction.TransactionSource source,
                                                         WalletTransaction.TransactionStatus status,
                                                         BigDecimal minAmount,
                                                         BigDecimal maxAmount,
                                                         LocalDateTime startDate,
                                                         LocalDateTime endDate,
                                                         Pageable pageable) {
        return transactionRepository.searchTransactions(walletId, type, source, status, 
                                                       minAmount, maxAmount, startDate, endDate, pageable)
                .map(WalletTransactionDTO::fromEntity);
    }

    /**
     * Get total amount by wallet and type
     */
    public BigDecimal getTotalAmountByWalletAndType(UUID walletId, WalletTransaction.TransactionType type) {
        BigDecimal total = transactionRepository.getTotalAmountByWalletAndType(walletId, type);
        return total != null ? total : BigDecimal.ZERO;
    }

    /**
     * Get total amount by wallet and source
     */
    public BigDecimal getTotalAmountByWalletAndSource(UUID walletId, WalletTransaction.TransactionSource source) {
        BigDecimal total = transactionRepository.getTotalAmountByWalletAndSource(walletId, source);
        return total != null ? total : BigDecimal.ZERO;
    }

    /**
     * Get daily spending for wallet
     */
    public BigDecimal getDailySpending(UUID walletId) {
        BigDecimal spending = transactionRepository.getDailySpending(walletId);
        return spending != null ? spending : BigDecimal.ZERO;
    }

    /**
     * Get monthly spending for wallet
     */
    public BigDecimal getMonthlySpending(UUID walletId) {
        BigDecimal spending = transactionRepository.getMonthlySpending(walletId);
        return spending != null ? spending : BigDecimal.ZERO;
    }

    /**
     * Count transactions by wallet and status
     */
    public Long countTransactionsByWalletAndStatus(UUID walletId, WalletTransaction.TransactionStatus status) {
        return transactionRepository.countByWalletIdAndStatus(walletId, status);
    }

    /**
     * Get pending transactions older than specified time
     */
    public List<WalletTransactionDTO> getPendingTransactionsOlderThan(LocalDateTime cutoffTime) {
        return transactionRepository.findPendingTransactionsOlderThan(cutoffTime)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get failed transactions
     */
    public List<WalletTransactionDTO> getFailedTransactions() {
        return transactionRepository.findFailedTransactions()
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get top transactions by amount
     */
    public List<WalletTransactionDTO> getTopTransactionsByAmount(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return transactionRepository.findTopTransactionsByAmount(pageable)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transactions between dates
     */
    public List<WalletTransactionDTO> getTransactionsBetweenDates(LocalDateTime startDate, LocalDateTime endDate) {
        return transactionRepository.findTransactionsBetweenDates(startDate, endDate)
                .stream()
                .map(WalletTransactionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Get transaction statistics
     */
    public TransactionStatistics getTransactionStatistics() {
        Object[] stats = transactionRepository.getTransactionStatistics();
        
        if (stats != null && stats.length >= 6) {
            return TransactionStatistics.builder()
                    .totalTransactions((Long) stats[0])
                    .completedTransactions((Long) stats[1])
                    .pendingTransactions((Long) stats[2])
                    .failedTransactions((Long) stats[3])
                    .totalCredits((BigDecimal) stats[4])
                    .totalDebits((BigDecimal) stats[5])
                    .lastUpdated(LocalDateTime.now())
                    .build();
        }
        
        return TransactionStatistics.builder()
                .totalTransactions(0L)
                .completedTransactions(0L)
                .pendingTransactions(0L)
                .failedTransactions(0L)
                .totalCredits(BigDecimal.ZERO)
                .totalDebits(BigDecimal.ZERO)
                .lastUpdated(LocalDateTime.now())
                .build();
    }

    /**
     * Get daily transaction statistics
     */
    public List<DailyTransactionStats> getDailyTransactionStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> results = transactionRepository.getDailyTransactionStatistics(startDate, endDate);
        
        return results.stream()
                .map(result -> DailyTransactionStats.builder()
                        .date((LocalDateTime) result[0])
                        .transactionCount((Long) result[1])
                        .totalCredits((BigDecimal) result[2])
                        .totalDebits((BigDecimal) result[3])
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * Transaction Statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TransactionStatistics {
        private Long totalTransactions;
        private Long completedTransactions;
        private Long pendingTransactions;
        private Long failedTransactions;
        private BigDecimal totalCredits;
        private BigDecimal totalDebits;
        private LocalDateTime lastUpdated;
    }

    /**
     * Daily Transaction Statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class DailyTransactionStats {
        private LocalDateTime date;
        private Long transactionCount;
        private BigDecimal totalCredits;
        private BigDecimal totalDebits;
    }
}
