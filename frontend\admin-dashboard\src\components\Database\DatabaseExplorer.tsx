import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TablePagination,
} from '@mui/material';
import {
  Storage as DatabaseIcon,
  TableChart as TableIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as ConnectedIcon,
  Error as ErrorIcon,
  Code as QueryIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';
import databaseService from '../../services/databaseService';
import {
  DatabaseConnection,
  TableInfo,
  QueryResult,
  DatabaseStats,
  ColumnInfo
} from '../../types/api';
import LiveDataViewer from './LiveDataViewer';
import DataVisualization from './DataVisualization';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`database-tabpanel-${index}`}
      aria-labelledby={`database-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const DatabaseExplorer: React.FC = () => {
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [selectedDatabase, setSelectedDatabase] = useState<string>('');
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<QueryResult | null>(null);
  const [tableSchema, setTableSchema] = useState<ColumnInfo[]>([]);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [customQuery, setCustomQuery] = useState('');
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [showQueryDialog, setShowQueryDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newRecord, setNewRecord] = useState<Record<string, any>>({});

  useEffect(() => {
    loadConnections();
    loadStats();
  }, []);

  useEffect(() => {
    if (selectedDatabase) {
      loadTables();
    }
  }, [selectedDatabase]);

  useEffect(() => {
    if (selectedDatabase && selectedTable) {
      loadTableData();
      loadTableSchema();
    }
  }, [selectedDatabase, selectedTable, page, rowsPerPage]);

  const loadConnections = async () => {
    try {
      setLoading(true);
      const data = await databaseService.getDatabaseConnections();
      setConnections(data);
      if (data.length > 0 && !selectedDatabase) {
        setSelectedDatabase(data[0].database);
      }
    } catch (error) {
      console.error('Error loading connections:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTables = async () => {
    if (!selectedDatabase) return;
    
    try {
      setLoading(true);
      const data = await databaseService.getTables(selectedDatabase);
      setTables(data);
      if (data.length > 0 && !selectedTable) {
        setSelectedTable(data[0].tableName);
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTableData = async () => {
    if (!selectedDatabase || !selectedTable) return;
    
    try {
      setLoading(true);
      const data = await databaseService.getTableData(selectedDatabase, selectedTable, page + 1, rowsPerPage);
      setTableData(data);
    } catch (error) {
      console.error('Error loading table data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTableSchema = async () => {
    if (!selectedDatabase || !selectedTable) return;
    
    try {
      const schema = await databaseService.getTableSchema(selectedDatabase, selectedTable);
      setTableSchema(schema);
    } catch (error) {
      console.error('Error loading table schema:', error);
    }
  };

  const loadStats = async () => {
    try {
      const data = await databaseService.getDatabaseStats();
      setStats(data);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleSearch = async () => {
    if (!selectedDatabase || !selectedTable || !searchTerm) return;
    
    try {
      setLoading(true);
      const data = await databaseService.searchTableData(selectedDatabase, selectedTable, searchTerm);
      setTableData(data);
    } catch (error) {
      console.error('Error searching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomQuery = async () => {
    if (!selectedDatabase || !customQuery) return;
    
    try {
      setLoading(true);
      const result = await databaseService.executeQuery(selectedDatabase, customQuery);
      setQueryResult(result);
    } catch (error) {
      console.error('Error executing query:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'csv' | 'json' | 'excel') => {
    if (!selectedDatabase || !selectedTable) return;
    
    try {
      const blob = await databaseService.exportTable(selectedDatabase, selectedTable, format);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedTable}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const handleAddRecord = async () => {
    if (!selectedDatabase || !selectedTable) return;
    
    try {
      const success = await databaseService.insertRecord(selectedDatabase, selectedTable, newRecord);
      if (success) {
        setShowAddDialog(false);
        setNewRecord({});
        loadTableData();
      }
    } catch (error) {
      console.error('Error adding record:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <ConnectedIcon color="success" />;
      case 'disconnected':
        return <ErrorIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <ErrorIcon color="disabled" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'postgresql':
        return 'primary';
      case 'mongodb':
        return 'success';
      case 'redis':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Database Explorer
        </Typography>
        <Box>
          <IconButton onClick={loadConnections} color="primary">
            <RefreshIcon />
          </IconButton>
          <Button
            startIcon={<QueryIcon />}
            variant="outlined"
            onClick={() => setShowQueryDialog(true)}
            sx={{ ml: 1 }}
          >
            Custom Query
          </Button>
        </Box>
      </Box>

      {/* Database Statistics */}
      {stats && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <DatabaseIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Databases
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalDatabases}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TableIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Tables
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalTables}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <StatsIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Rows
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalRows.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <DatabaseIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Size
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalSize}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Left Sidebar - Connections and Tables */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Database Connections
              </Typography>
              
              {connections.map((conn) => (
                <Accordion key={conn.id} expanded={selectedDatabase === conn.database}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    onClick={() => setSelectedDatabase(conn.database)}
                  >
                    <Box display="flex" alignItems="center" width="100%">
                      {getStatusIcon(conn.status)}
                      <Box ml={1} flexGrow={1}>
                        <Typography variant="body2" fontWeight="bold">
                          {conn.name}
                        </Typography>
                        <Chip
                          label={conn.type}
                          size="small"
                          color={getTypeColor(conn.type) as any}
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {tables.map((table) => (
                        <ListItem
                          key={table.tableName}
                          button
                          selected={selectedTable === table.tableName}
                          onClick={() => setSelectedTable(table.tableName)}
                        >
                          <ListItemIcon>
                            <TableIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={table.tableName}
                            secondary={`${table.rowCount.toLocaleString()} rows`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content Area */}
        <Grid item xs={12} md={9}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  {selectedTable ? `Table: ${selectedTable}` : 'Select a table'}
                </Typography>
                
                {selectedTable && (
                  <Box>
                    <Button
                      startIcon={<AddIcon />}
                      variant="outlined"
                      onClick={() => setShowAddDialog(true)}
                      sx={{ mr: 1 }}
                    >
                      Add Record
                    </Button>
                    <Button
                      startIcon={<ExportIcon />}
                      variant="outlined"
                      onClick={() => handleExport('csv')}
                    >
                      Export CSV
                    </Button>
                  </Box>
                )}
              </Box>

              {selectedTable && (
                <>
                  {/* Search Bar */}
                  <Box display="flex" gap={2} mb={3}>
                    <TextField
                      fullWidth
                      placeholder="Search in table..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    />
                    <Button
                      variant="contained"
                      startIcon={<SearchIcon />}
                      onClick={handleSearch}
                    >
                      Search
                    </Button>
                  </Box>

                  {/* Tabs */}
                  <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                    <Tab label="Live Data" />
                    <Tab label="Data Visualization" />
                    <Tab label="Data" />
                    <Tab label="Schema" />
                    <Tab label="Indexes" />
                  </Tabs>

                  {/* Live Data Tab */}
                  <TabPanel value={tabValue} index={0}>
                    <LiveDataViewer
                      database={selectedDatabase}
                      tableName={selectedTable}
                    />
                  </TabPanel>

                  {/* Data Visualization Tab */}
                  <TabPanel value={tabValue} index={1}>
                    <DataVisualization
                      database={selectedDatabase}
                      tableName={selectedTable}
                    />
                  </TabPanel>

                  {/* Data Tab */}
                  <TabPanel value={tabValue} index={2}>
                    {loading ? (
                      <Box display="flex" justifyContent="center" p={3}>
                        <CircularProgress />
                      </Box>
                    ) : tableData ? (
                      <>
                        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
                          <Table stickyHeader>
                            <TableHead>
                              <TableRow>
                                {tableData.columns.map((column) => (
                                  <TableCell key={column} sx={{ fontWeight: 'bold' }}>
                                    {column}
                                  </TableCell>
                                ))}
                                <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {tableData.rows.map((row, index) => (
                                <TableRow key={index} hover>
                                  {row.map((cell, cellIndex) => (
                                    <TableCell key={cellIndex}>
                                      {typeof cell === 'string' && cell.length > 50
                                        ? `${cell.substring(0, 50)}...`
                                        : String(cell)
                                      }
                                    </TableCell>
                                  ))}
                                  <TableCell>
                                    <IconButton size="small" color="primary">
                                      <ViewIcon />
                                    </IconButton>
                                    <IconButton size="small" color="warning">
                                      <EditIcon />
                                    </IconButton>
                                    <IconButton size="small" color="error">
                                      <DeleteIcon />
                                    </IconButton>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                        
                        <TablePagination
                          component="div"
                          count={tableData.totalRows}
                          page={page}
                          onPageChange={(e, newPage) => setPage(newPage)}
                          rowsPerPage={rowsPerPage}
                          onRowsPerPageChange={(e) => {
                            setRowsPerPage(parseInt(e.target.value, 10));
                            setPage(0);
                          }}
                          rowsPerPageOptions={[10, 25, 50, 100]}
                        />
                      </>
                    ) : (
                      <Alert severity="info">No data available</Alert>
                    )}
                  </TabPanel>

                  {/* Schema Tab */}
                  <TabPanel value={tabValue} index={3}>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Column Name</TableCell>
                            <TableCell>Data Type</TableCell>
                            <TableCell>Nullable</TableCell>
                            <TableCell>Primary Key</TableCell>
                            <TableCell>Foreign Key</TableCell>
                            <TableCell>Default Value</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {tableSchema.map((column) => (
                            <TableRow key={column.name}>
                              <TableCell>
                                <Typography fontWeight={column.primaryKey ? 'bold' : 'normal'}>
                                  {column.name}
                                </Typography>
                              </TableCell>
                              <TableCell>{column.type}</TableCell>
                              <TableCell>
                                <Chip
                                  label={column.nullable ? 'Yes' : 'No'}
                                  color={column.nullable ? 'default' : 'primary'}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>
                                {column.primaryKey && (
                                  <Chip label="PK" color="primary" size="small" />
                                )}
                              </TableCell>
                              <TableCell>
                                {column.foreignKey && (
                                  <Chip label={column.foreignKey} color="secondary" size="small" />
                                )}
                              </TableCell>
                              <TableCell>{column.defaultValue || '-'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </TabPanel>

                  {/* Indexes Tab */}
                  <TabPanel value={tabValue} index={4}>
                    {tables.find(t => t.tableName === selectedTable)?.indexes.map((index) => (
                      <Chip key={index} label={index} sx={{ mr: 1, mb: 1 }} />
                    ))}
                  </TabPanel>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Custom Query Dialog */}
      <Dialog open={showQueryDialog} onClose={() => setShowQueryDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Execute Custom Query</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={6}
            placeholder="Enter your SQL query here..."
            value={customQuery}
            onChange={(e) => setCustomQuery(e.target.value)}
            sx={{ mt: 2 }}
          />
          
          {queryResult && (
            <Box mt={3}>
              <Typography variant="h6" gutterBottom>
                Query Result ({queryResult.totalRows} rows, {queryResult.executionTime}ms)
              </Typography>
              <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {queryResult.columns.map((column) => (
                        <TableCell key={column}>{column}</TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {queryResult.rows.slice(0, 10).map((row, index) => (
                      <TableRow key={index}>
                        {row.map((cell, cellIndex) => (
                          <TableCell key={cellIndex}>{String(cell)}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQueryDialog(false)}>Cancel</Button>
          <Button onClick={handleCustomQuery} variant="contained">
            Execute Query
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Record Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Record to {selectedTable}</DialogTitle>
        <DialogContent>
          {tableSchema.filter(col => !col.primaryKey).map((column) => (
            <TextField
              key={column.name}
              fullWidth
              label={column.name}
              type={column.type.includes('INT') ? 'number' : 'text'}
              required={!column.nullable}
              value={newRecord[column.name] || ''}
              onChange={(e) => setNewRecord(prev => ({
                ...prev,
                [column.name]: e.target.value
              }))}
              sx={{ mt: 2 }}
            />
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button onClick={handleAddRecord} variant="contained">
            Add Record
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseExplorer;
