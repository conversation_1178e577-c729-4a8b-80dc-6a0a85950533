-- TECNODRIVE Platform - Security Configuration
-- This script configures security settings, users, and permissions

-- Connect as superuser to configure security
\c postgres;

-- Create security roles with specific permissions
CREATE ROLE tecnodrive_app_role;
CREATE ROLE tecnodrive_read_role;
CREATE ROLE tecnodrive_backup_role;

-- Grant basic connection privileges
GRANT CONNECT ON DATABASE tecnodrive_auth TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_users TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_rides TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_fleet TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_payments TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_notifications TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_financial TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_hr TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_analytics TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_saas TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_location TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_tracking TO tecnodrive_app_role;
GRANT CONNECT ON DATABASE tecnodrive_parcels TO tecnodrive_app_role;

-- Grant read-only access to read role
GRANT CONNECT ON DATABASE tecnodrive_auth TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_users TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_rides TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_fleet TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_payments TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_notifications TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_financial TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_hr TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_analytics TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_saas TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_location TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_tracking TO tecnodrive_read_role;
GRANT CONNECT ON DATABASE tecnodrive_parcels TO tecnodrive_read_role;

-- Assign roles to users
GRANT tecnodrive_app_role TO tecnodrive_admin;
GRANT tecnodrive_read_role TO tecnodrive_readonly;
GRANT tecnodrive_backup_role TO tecnodrive_backup;

-- Configure password policies
ALTER SYSTEM SET password_encryption = 'scram-sha-256';
ALTER SYSTEM SET log_connections = 'on';
ALTER SYSTEM SET log_disconnections = 'on';
ALTER SYSTEM SET log_statement = 'ddl';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Configure connection limits
ALTER USER tecnodrive_admin CONNECTION LIMIT 50;
ALTER USER tecnodrive_readonly CONNECTION LIMIT 20;
ALTER USER tecnodrive_backup CONNECTION LIMIT 5;

-- Set session timeouts
ALTER USER tecnodrive_admin SET statement_timeout = '30min';
ALTER USER tecnodrive_readonly SET statement_timeout = '10min';
ALTER USER tecnodrive_backup SET statement_timeout = '60min';

-- Configure each database with proper permissions
\c tecnodrive_auth;
GRANT USAGE ON SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tecnodrive_app_role;

GRANT USAGE ON SCHEMA public TO tecnodrive_read_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_read_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_read_role;

\c tecnodrive_users;
GRANT USAGE ON SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tecnodrive_app_role;

GRANT USAGE ON SCHEMA public TO tecnodrive_read_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_read_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_read_role;

\c tecnodrive_rides;
GRANT USAGE ON SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tecnodrive_app_role;

GRANT USAGE ON SCHEMA public TO tecnodrive_read_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_read_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_read_role;

\c tecnodrive_fleet;
GRANT USAGE ON SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tecnodrive_app_role;

GRANT USAGE ON SCHEMA public TO tecnodrive_read_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_read_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_read_role;

\c tecnodrive_payments;
GRANT USAGE ON SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tecnodrive_app_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tecnodrive_app_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tecnodrive_app_role;

GRANT USAGE ON SCHEMA public TO tecnodrive_read_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tecnodrive_read_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO tecnodrive_read_role;

-- Configure row-level security for sensitive tables
\c tecnodrive_payments;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_accounts ENABLE ROW LEVEL SECURITY;

-- Create policies for payment data
CREATE POLICY payment_user_policy ON payment_transactions
    FOR ALL TO tecnodrive_app_role
    USING (user_id = current_setting('app.current_user_id')::bigint);

CREATE POLICY payment_method_user_policy ON payment_methods
    FOR ALL TO tecnodrive_app_role
    USING (user_id = current_setting('app.current_user_id')::bigint);

CREATE POLICY bank_account_user_policy ON bank_accounts
    FOR ALL TO tecnodrive_app_role
    USING (user_id = current_setting('app.current_user_id')::bigint);

-- Configure audit logging
\c postgres;
CREATE EXTENSION IF NOT EXISTS pgaudit;
ALTER SYSTEM SET pgaudit.log = 'ddl,write';
ALTER SYSTEM SET pgaudit.log_catalog = 'off';
ALTER SYSTEM SET pgaudit.log_parameter = 'on';
ALTER SYSTEM SET pgaudit.log_relation = 'on';

-- Configure SSL and encryption
ALTER SYSTEM SET ssl = 'on';
ALTER SYSTEM SET ssl_cert_file = 'server.crt';
ALTER SYSTEM SET ssl_key_file = 'server.key';
ALTER SYSTEM SET ssl_ca_file = 'ca.crt';
ALTER SYSTEM SET ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL';
ALTER SYSTEM SET ssl_prefer_server_ciphers = 'on';

-- Configure connection security
ALTER SYSTEM SET listen_addresses = 'localhost';
ALTER SYSTEM SET max_connections = '200';
ALTER SYSTEM SET superuser_reserved_connections = '3';

-- Configure authentication
-- Note: This requires manual editing of pg_hba.conf
-- Add these lines to pg_hba.conf:
-- hostssl all tecnodrive_admin 127.0.0.1/32 scram-sha-256
-- hostssl all tecnodrive_readonly 127.0.0.1/32 scram-sha-256
-- hostssl all tecnodrive_backup 127.0.0.1/32 scram-sha-256

-- Reload configuration
SELECT pg_reload_conf();

-- Create security monitoring views
CREATE OR REPLACE VIEW security_audit AS
SELECT 
    schemaname,
    tablename,
    usename,
    application_name,
    client_addr,
    backend_start,
    query_start,
    state,
    query
FROM pg_stat_activity 
WHERE state = 'active' AND query NOT LIKE '%pg_stat_activity%';

-- Create function to rotate passwords
CREATE OR REPLACE FUNCTION rotate_user_password(username TEXT, new_password TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    EXECUTE format('ALTER USER %I WITH PASSWORD %L', username, new_password);
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Revoke public access from functions
REVOKE ALL ON FUNCTION rotate_user_password(TEXT, TEXT) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION rotate_user_password(TEXT, TEXT) TO postgres;

\echo 'Security configuration completed successfully!';
\echo 'Remember to:';
\echo '1. Update pg_hba.conf with SSL authentication settings';
\echo '2. Generate SSL certificates';
\echo '3. Set up regular password rotation';
\echo '4. Configure firewall rules';
\echo '5. Set up monitoring and alerting';
