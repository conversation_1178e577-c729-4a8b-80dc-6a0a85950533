package com.tecnodrive.parcelservice.service;

import com.tecnodrive.parcelservice.dto.ParcelDto;
import com.tecnodrive.parcelservice.entity.ParcelEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Enhanced Parcel Service Interface
 */
public interface EnhancedParcelService {
    
    /**
     * إنشاء طرد جديد
     */
    ParcelDto createParcel(ParcelDto dto);
    
    /**
     * تحديث طرد موجود
     */
    ParcelDto updateParcel(String parcelId, ParcelDto dto);
    
    /**
     * الحصول على طرد بالمعرف
     */
    ParcelDto getParcelById(String parcelId);
    
    /**
     * الحصول على طرد بالباركود
     */
    ParcelDto getParcelByBarcode(String barcode);
    
    /**
     * الحصول على طرود المستخدم
     */
    Page<ParcelDto> getParcelsByUser(String userId, Pageable pageable);
    
    /**
     * الحصول على طرود بحالة معينة
     */
    Page<ParcelDto> getParcelsByStatus(ParcelEntity.ParcelStatus status, Pageable pageable);
    
    /**
     * تحديث حالة الطرد
     */
    ParcelDto updateParcelStatus(String parcelId, ParcelEntity.ParcelStatus newStatus, String updatedBy);
    
    /**
     * البحث في الطرود
     */
    Page<ParcelDto> searchParcels(String searchTerm, Pageable pageable);
    
    /**
     * الحصول على جميع الطرود
     */
    List<ParcelDto> getAllParcels();
    
    /**
     * حذف طرد
     */
    void deleteParcel(String parcelId);
    
    /**
     * إلغاء طرد
     */
    ParcelDto cancelParcel(String parcelId, String reason, String cancelledBy);
    
    /**
     * الحصول على إحصائيات الطرود
     */
    Object getParcelStatistics();
}
