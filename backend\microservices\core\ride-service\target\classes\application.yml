server:
  port: 8082

spring:
  application:
    name: ride-service

  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/tecnodrive_rides
    username: ${DB_USERNAME:tecnodrive_admin}
    password: ${DB_PASSWORD:TecnoDrive2025!Secure#Platform}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.spatial.dialect.postgis.PostgisPG95Dialect
        format_sql: true

  flyway:
    enabled: false
    locations: classpath:db/migration
    baseline-on-migrate: true

  kafka:
    bootstrap-servers: kafka:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: ride-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "com.tecnodrive"

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus

# Logging
logging:
  level:
    com.tecnodrive.rideservice: DEBUG
    org.springframework.kafka: INFO

# Custom application properties
app:
  ride:
    matching:
      radius-km: 5
      max-wait-time-minutes: 10
    pricing:
      base-fare: 500  # YER
      per-km-rate: 100  # YER
      per-minute-rate: 50  # YER
