import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'MANAGER' | 'OPERATOR' | 'DRIVER' | 'CUSTOMER'; // توسيع الأدوار
  avatar: string; // جعلها إلزامية
  permissions?: string[];
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  loading: false,
  error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }) => {
    const { authService } = await import('../../services/authService');
    const response = await authService.login(credentials);

    if (!response.success || !response.data) {
      throw new Error(response.message || 'فشل في تسجيل الدخول');
    }

    return response.data;
  }
);

export const logoutUser = createAsyncThunk('auth/logout', async () => {
  const { authService } = await import('../../services/authService');
  await authService.logout();
  return null;
});

export const getProfile = createAsyncThunk('auth/getProfile', async () => {
  const { authService } = await import('../../services/authService');
  const response = await authService.getProfile();

  if (!response.success || !response.data) {
    throw new Error(response.message || 'فشل في جلب بيانات المستخدم');
  }

  return response.data;
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCredentials: (state, action: PayloadAction<{ user: User; token: string }>) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
      localStorage.setItem('token', action.payload.token);
    },
    clearCredentials: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      localStorage.removeItem('token');
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        // تأكد من أن البيانات تتطابق مع نوع User
        state.user = {
          ...action.payload.user,
          avatar: action.payload.user.avatar || '/default-avatar.png'
        } as User;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        localStorage.setItem('token', action.payload.token);
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'حدث خطأ في تسجيل الدخول';
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      });
  },
});

export const { clearError, setCredentials, clearCredentials } = authSlice.actions;
export default authSlice.reducer;
