package com.tecnodrive.fleetservice.service.impl;

import com.tecnodrive.fleetservice.dto.VehicleRequest;
import com.tecnodrive.fleetservice.dto.VehicleResponse;
import com.tecnodrive.fleetservice.entity.Vehicle;
import com.tecnodrive.fleetservice.exception.VehicleNotFoundException;
import com.tecnodrive.fleetservice.repository.VehicleRepository;
import com.tecnodrive.fleetservice.service.VehicleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Vehicle Service Implementation
 * 
 * Implements business logic for vehicle fleet management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class VehicleServiceImpl implements VehicleService {

    private final VehicleRepository vehicleRepository;

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse createVehicle(VehicleRequest request) {
        log.info("Creating vehicle with plate number: {}", request.getPlateNumber());
        
        validateVehicleRequest(request);
        
        // Check if plate number already exists
        if (!isPlateNumberAvailable(request.getPlateNumber())) {
            throw new IllegalArgumentException("Plate number already exists: " + request.getPlateNumber());
        }
        
        // Check if VIN already exists (if provided)
        if (request.getVin() != null && !isVinAvailable(request.getVin())) {
            throw new IllegalArgumentException("VIN already exists: " + request.getVin());
        }
        
        Vehicle vehicle = mapToEntity(request);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Vehicle created successfully with ID: {}", vehicle.getId());
        return mapToResponse(vehicle);
    }

    @Override
    @Cacheable(value = "vehicles", key = "#id")
    public VehicleResponse getVehicle(String id) {
        log.debug("Getting vehicle by ID: {}", id);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(id))
                .orElseThrow(() -> new VehicleNotFoundException(id));
        
        return mapToResponse(vehicle);
    }

    @Override
    @Cacheable(value = "vehicles", key = "#plateNumber")
    public VehicleResponse getVehicleByPlateNumber(String plateNumber) {
        log.debug("Getting vehicle by plate number: {}", plateNumber);
        
        Vehicle vehicle = vehicleRepository.findByPlateNumber(plateNumber)
                .orElseThrow(() -> new VehicleNotFoundException("Vehicle not found with plate number: " + plateNumber));
        
        return mapToResponse(vehicle);
    }

    @Override
    @Cacheable(value = "vehicles", key = "'all'")
    public List<VehicleResponse> getAllVehicles() {
        log.debug("Getting all vehicles");
        
        return vehicleRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<VehicleResponse> getVehicles(Pageable pageable) {
        log.debug("Getting vehicles with pagination: {}", pageable);
        
        return vehicleRepository.findAll(pageable)
                .map(this::mapToResponse);
    }

    @Override
    @Cacheable(value = "vehicles", key = "'company_' + #companyId")
    public List<VehicleResponse> getVehiclesByCompany(String companyId) {
        log.debug("Getting vehicles by company: {}", companyId);
        
        return vehicleRepository.findByCompanyId(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<VehicleResponse> getVehiclesByCompany(String companyId, Pageable pageable) {
        log.debug("Getting vehicles by company: {} with pagination: {}", companyId, pageable);
        
        return vehicleRepository.findByCompanyId(companyId, pageable)
                .map(this::mapToResponse);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse updateVehicle(String id, VehicleRequest request) {
        log.info("Updating vehicle with ID: {}", id);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(id))
                .orElseThrow(() -> new VehicleNotFoundException(id));
        
        validateVehicleRequest(request);
        
        // Check if plate number is being changed and if it's available
        if (!vehicle.getPlateNumber().equals(request.getPlateNumber()) && 
            !isPlateNumberAvailable(request.getPlateNumber())) {
            throw new IllegalArgumentException("Plate number already exists: " + request.getPlateNumber());
        }
        
        // Check if VIN is being changed and if it's available
        if (request.getVin() != null && 
            !request.getVin().equals(vehicle.getVin()) && 
            !isVinAvailable(request.getVin())) {
            throw new IllegalArgumentException("VIN already exists: " + request.getVin());
        }
        
        updateEntityFromRequest(vehicle, request);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Vehicle updated successfully with ID: {}", vehicle.getId());
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public void deleteVehicle(String id) {
        log.info("Deleting vehicle with ID: {}", id);
        
        UUID vehicleId = UUID.fromString(id);
        if (!vehicleRepository.existsById(vehicleId)) {
            throw new VehicleNotFoundException(id);
        }
        
        vehicleRepository.deleteById(vehicleId);
        log.info("Vehicle deleted successfully with ID: {}", id);
    }

    @Override
    public List<VehicleResponse> getVehiclesByStatus(Vehicle.VehicleStatus status) {
        log.debug("Getting vehicles by status: {}", status);
        
        return vehicleRepository.findByStatus(status).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesByCompanyAndStatus(String companyId, Vehicle.VehicleStatus status) {
        log.debug("Getting vehicles by company: {} and status: {}", companyId, status);
        
        return vehicleRepository.findByCompanyIdAndStatus(companyId, status).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "vehicles", key = "'available'")
    public List<VehicleResponse> getAvailableVehicles() {
        log.debug("Getting available vehicles");
        
        return vehicleRepository.findAvailableVehicles().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "vehicles", key = "'available_' + #companyId")
    public List<VehicleResponse> getAvailableVehiclesByCompany(String companyId) {
        log.debug("Getting available vehicles by company: {}", companyId);
        
        return vehicleRepository.findAvailableVehiclesByCompany(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse assignDriver(String vehicleId, String driverId) {
        log.info("Assigning driver {} to vehicle {}", driverId, vehicleId);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        if (vehicle.getAssignedDriverId() != null) {
            throw new IllegalStateException("Vehicle is already assigned to driver: " + vehicle.getAssignedDriverId());
        }
        
        if (vehicle.getStatus() != Vehicle.VehicleStatus.AVAILABLE) {
            throw new IllegalStateException("Vehicle is not available for assignment. Current status: " + vehicle.getStatus());
        }
        
        vehicle.setAssignedDriverId(driverId);
        vehicle.setStatus(Vehicle.VehicleStatus.IN_USE);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Driver assigned successfully to vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse unassignDriver(String vehicleId) {
        log.info("Unassigning driver from vehicle {}", vehicleId);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        vehicle.setAssignedDriverId(null);
        vehicle.setStatus(Vehicle.VehicleStatus.AVAILABLE);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Driver unassigned successfully from vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse updateVehicleStatus(String vehicleId, Vehicle.VehicleStatus status) {
        log.info("Updating vehicle {} status to {}", vehicleId, status);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        vehicle.setStatus(status);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Vehicle status updated successfully for vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse updateOdometerReading(String vehicleId, BigDecimal odometerReading) {
        log.info("Updating odometer reading for vehicle {} to {}", vehicleId, odometerReading);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        if (vehicle.getOdometerReading() != null && 
            odometerReading.compareTo(vehicle.getOdometerReading()) < 0) {
            throw new IllegalArgumentException("New odometer reading cannot be less than current reading");
        }
        
        vehicle.setOdometerReading(odometerReading);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Odometer reading updated successfully for vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse scheduleMaintenance(String vehicleId, LocalDate maintenanceDate) {
        log.info("Scheduling maintenance for vehicle {} on {}", vehicleId, maintenanceDate);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        vehicle.setNextMaintenanceDate(maintenanceDate);
        vehicle.setStatus(Vehicle.VehicleStatus.MAINTENANCE);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Maintenance scheduled successfully for vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    @CacheEvict(value = "vehicles", allEntries = true)
    public VehicleResponse completeMaintenance(String vehicleId, BigDecimal odometerReading, LocalDate nextMaintenanceDate) {
        log.info("Completing maintenance for vehicle {}", vehicleId);
        
        Vehicle vehicle = vehicleRepository.findById(UUID.fromString(vehicleId))
                .orElseThrow(() -> new VehicleNotFoundException(vehicleId));
        
        vehicle.setLastMaintenanceDate(LocalDate.now());
        vehicle.setLastMaintenanceOdometer(odometerReading);
        vehicle.setOdometerReading(odometerReading);
        vehicle.setNextMaintenanceDate(nextMaintenanceDate);
        vehicle.setStatus(Vehicle.VehicleStatus.AVAILABLE);
        vehicle = vehicleRepository.save(vehicle);
        
        log.info("Maintenance completed successfully for vehicle {}", vehicleId);
        return mapToResponse(vehicle);
    }

    @Override
    public List<VehicleResponse> getVehiclesNeedingMaintenance() {
        log.debug("Getting vehicles needing maintenance");
        
        return vehicleRepository.findVehiclesNeedingMaintenance(LocalDate.now()).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesNeedingMaintenanceByCompany(String companyId) {
        log.debug("Getting vehicles needing maintenance by company: {}", companyId);
        
        return vehicleRepository.findVehiclesNeedingMaintenance(LocalDate.now()).stream()
                .filter(vehicle -> companyId.equals(vehicle.getCompanyId()))
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesWithExpiringInsurance(int warningDays) {
        log.debug("Getting vehicles with expiring insurance within {} days", warningDays);
        
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(warningDays);
        
        return vehicleRepository.findVehiclesWithExpiringInsurance(startDate, endDate).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesWithExpiringRegistration(int warningDays) {
        log.debug("Getting vehicles with expiring registration within {} days", warningDays);
        
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(warningDays);
        
        return vehicleRepository.findVehiclesWithExpiringRegistration(startDate, endDate).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Page<VehicleResponse> searchVehicles(String searchTerm, Pageable pageable) {
        log.debug("Searching vehicles with term: {}", searchTerm);
        
        return vehicleRepository.searchVehicles(searchTerm, pageable)
                .map(this::mapToResponse);
    }

    @Override
    public Page<VehicleResponse> searchVehiclesByCompany(String companyId, String searchTerm, Pageable pageable) {
        log.debug("Searching vehicles by company: {} with term: {}", companyId, searchTerm);
        
        return vehicleRepository.searchVehiclesByCompany(companyId, searchTerm, pageable)
                .map(this::mapToResponse);
    }

    @Override
    @Cacheable(value = "vehicleStats", key = "#companyId")
    public VehicleResponse.VehicleStatistics getVehicleStatistics(String companyId) {
        log.debug("Getting vehicle statistics for company: {}", companyId);
        
        long totalVehicles = vehicleRepository.countByCompanyId(companyId);
        long availableVehicles = vehicleRepository.countByCompanyIdAndStatus(companyId, Vehicle.VehicleStatus.AVAILABLE);
        long inUseVehicles = vehicleRepository.countByCompanyIdAndStatus(companyId, Vehicle.VehicleStatus.IN_USE);
        long maintenanceVehicles = vehicleRepository.countByCompanyIdAndStatus(companyId, Vehicle.VehicleStatus.MAINTENANCE);
        long outOfServiceVehicles = vehicleRepository.countByCompanyIdAndStatus(companyId, Vehicle.VehicleStatus.OUT_OF_SERVICE);
        
        Double averageAge = vehicleRepository.calculateAverageAgeByCompany(companyId);
        BigDecimal totalOdometer = vehicleRepository.calculateTotalOdometerByCompany(companyId);
        BigDecimal averageOdometer = vehicleRepository.calculateAverageOdometerByCompany(companyId);
        
        long vehiclesNeedingMaintenance = getVehiclesNeedingMaintenanceByCompany(companyId).size();
        long vehiclesWithExpiringInsurance = getVehiclesWithExpiringInsurance(30).stream()
                .filter(v -> companyId.equals(v.getCompanyId()))
                .count();
        long vehiclesWithExpiringRegistration = getVehiclesWithExpiringRegistration(30).stream()
                .filter(v -> companyId.equals(v.getCompanyId()))
                .count();
        
        return VehicleResponse.VehicleStatistics.builder()
                .totalVehicles(totalVehicles)
                .availableVehicles(availableVehicles)
                .inUseVehicles(inUseVehicles)
                .maintenanceVehicles(maintenanceVehicles)
                .outOfServiceVehicles(outOfServiceVehicles)
                .averageAge(averageAge != null ? averageAge : 0.0)
                .totalOdometerReading(totalOdometer)
                .averageOdometerReading(averageOdometer)
                .vehiclesNeedingMaintenance(vehiclesNeedingMaintenance)
                .vehiclesWithExpiringInsurance(vehiclesWithExpiringInsurance)
                .vehiclesWithExpiringRegistration(vehiclesWithExpiringRegistration)
                .build();
    }

    @Override
    public List<VehicleResponse.VehicleSummary> getVehicleSummaries(String companyId) {
        log.debug("Getting vehicle summaries for company: {}", companyId);
        
        return vehicleRepository.findByCompanyId(companyId).stream()
                .map(this::mapToSummary)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesByType(String companyId, Vehicle.VehicleType vehicleType) {
        log.debug("Getting vehicles by company: {} and type: {}", companyId, vehicleType);
        
        return vehicleRepository.findByCompanyIdAndVehicleType(companyId, vehicleType).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesByFuelType(String companyId, Vehicle.FuelType fuelType) {
        log.debug("Getting vehicles by company: {} and fuel type: {}", companyId, fuelType);
        
        return vehicleRepository.findByFuelType(fuelType).stream()
                .filter(vehicle -> companyId.equals(vehicle.getCompanyId()))
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getUnassignedVehicles(String companyId) {
        log.debug("Getting unassigned vehicles for company: {}", companyId);
        
        return vehicleRepository.findUnassignedVehiclesByCompany(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleResponse> getVehiclesByDriver(String driverId) {
        log.debug("Getting vehicles by driver: {}", driverId);
        
        return vehicleRepository.findByAssignedDriverId(driverId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public void validateVehicleRequest(VehicleRequest request) {
        if (request.getPlateNumber() == null || request.getPlateNumber().trim().isEmpty()) {
            throw new IllegalArgumentException("Plate number is required");
        }
        
        if (request.getMake() == null || request.getMake().trim().isEmpty()) {
            throw new IllegalArgumentException("Make is required");
        }
        
        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("Model is required");
        }
        
        if (request.getYear() == null || request.getYear() < 1900 || request.getYear() > LocalDate.now().getYear() + 1) {
            throw new IllegalArgumentException("Invalid year");
        }
        
        if (request.getCapacity() == null || request.getCapacity() < 1) {
            throw new IllegalArgumentException("Capacity must be at least 1");
        }
        
        if (request.getCompanyId() == null || request.getCompanyId().trim().isEmpty()) {
            throw new IllegalArgumentException("Company ID is required");
        }
    }

    @Override
    public boolean isPlateNumberAvailable(String plateNumber) {
        return !vehicleRepository.existsByPlateNumber(plateNumber);
    }

    @Override
    public boolean isVinAvailable(String vin) {
        return vin == null || !vehicleRepository.existsByVin(vin);
    }

    // Private helper methods
    private Vehicle mapToEntity(VehicleRequest request) {
        return Vehicle.builder()
                .plateNumber(request.getPlateNumber())
                .vin(request.getVin())
                .make(request.getMake())
                .model(request.getModel())
                .year(request.getYear())
                .color(request.getColor())
                .capacity(request.getCapacity())
                .vehicleType(request.getVehicleType())
                .fuelType(request.getFuelType())
                .status(request.getStatus() != null ? request.getStatus() : Vehicle.VehicleStatus.AVAILABLE)
                .odometerReading(request.getOdometerReading() != null ? request.getOdometerReading() : BigDecimal.ZERO)
                .engineCapacity(request.getEngineCapacity())
                .transmissionType(request.getTransmissionType())
                .assignedDriverId(request.getAssignedDriverId())
                .companyId(request.getCompanyId())
                .registrationDate(request.getRegistrationDate())
                .registrationExpiryDate(request.getRegistrationExpiryDate())
                .insurancePolicyNumber(request.getInsurancePolicyNumber())
                .insuranceExpiryDate(request.getInsuranceExpiryDate())
                .lastMaintenanceDate(request.getLastMaintenanceDate())
                .nextMaintenanceDate(request.getNextMaintenanceDate())
                .lastMaintenanceOdometer(request.getLastMaintenanceOdometer())
                .purchaseDate(request.getPurchaseDate())
                .purchasePrice(request.getPurchasePrice())
                .currentValue(request.getCurrentValue())
                .fuelConsumption(request.getFuelConsumption())
                .notes(request.getNotes())
                .gpsDeviceId(request.getGpsDeviceId())
                .isActive(request.getIsActive() != null ? request.getIsActive() : true)
                .build();
    }

    private void updateEntityFromRequest(Vehicle vehicle, VehicleRequest request) {
        vehicle.setPlateNumber(request.getPlateNumber());
        vehicle.setVin(request.getVin());
        vehicle.setMake(request.getMake());
        vehicle.setModel(request.getModel());
        vehicle.setYear(request.getYear());
        vehicle.setColor(request.getColor());
        vehicle.setCapacity(request.getCapacity());
        vehicle.setVehicleType(request.getVehicleType());
        vehicle.setFuelType(request.getFuelType());
        
        if (request.getStatus() != null) {
            vehicle.setStatus(request.getStatus());
        }
        
        if (request.getOdometerReading() != null) {
            vehicle.setOdometerReading(request.getOdometerReading());
        }
        
        vehicle.setEngineCapacity(request.getEngineCapacity());
        vehicle.setTransmissionType(request.getTransmissionType());
        vehicle.setAssignedDriverId(request.getAssignedDriverId());
        vehicle.setRegistrationDate(request.getRegistrationDate());
        vehicle.setRegistrationExpiryDate(request.getRegistrationExpiryDate());
        vehicle.setInsurancePolicyNumber(request.getInsurancePolicyNumber());
        vehicle.setInsuranceExpiryDate(request.getInsuranceExpiryDate());
        vehicle.setLastMaintenanceDate(request.getLastMaintenanceDate());
        vehicle.setNextMaintenanceDate(request.getNextMaintenanceDate());
        vehicle.setLastMaintenanceOdometer(request.getLastMaintenanceOdometer());
        vehicle.setPurchaseDate(request.getPurchaseDate());
        vehicle.setPurchasePrice(request.getPurchasePrice());
        vehicle.setCurrentValue(request.getCurrentValue());
        vehicle.setFuelConsumption(request.getFuelConsumption());
        vehicle.setNotes(request.getNotes());
        vehicle.setGpsDeviceId(request.getGpsDeviceId());
        
        if (request.getIsActive() != null) {
            vehicle.setActive(request.getIsActive());
        }
    }

    private VehicleResponse mapToResponse(Vehicle vehicle) {
        return VehicleResponse.builder()
                .id(vehicle.getId().toString())
                .plateNumber(vehicle.getPlateNumber())
                .vin(vehicle.getVin())
                .make(vehicle.getMake())
                .model(vehicle.getModel())
                .year(vehicle.getYear())
                .color(vehicle.getColor())
                .capacity(vehicle.getCapacity())
                .vehicleType(vehicle.getVehicleType())
                .fuelType(vehicle.getFuelType())
                .status(vehicle.getStatus())
                .odometerReading(vehicle.getOdometerReading())
                .engineCapacity(vehicle.getEngineCapacity())
                .transmissionType(vehicle.getTransmissionType())
                .assignedDriverId(vehicle.getAssignedDriverId())
                .companyId(vehicle.getCompanyId())
                .registrationDate(vehicle.getRegistrationDate())
                .registrationExpiryDate(vehicle.getRegistrationExpiryDate())
                .insurancePolicyNumber(vehicle.getInsurancePolicyNumber())
                .insuranceExpiryDate(vehicle.getInsuranceExpiryDate())
                .lastMaintenanceDate(vehicle.getLastMaintenanceDate())
                .nextMaintenanceDate(vehicle.getNextMaintenanceDate())
                .lastMaintenanceOdometer(vehicle.getLastMaintenanceOdometer())
                .purchaseDate(vehicle.getPurchaseDate())
                .purchasePrice(vehicle.getPurchasePrice())
                .currentValue(vehicle.getCurrentValue())
                .fuelConsumption(vehicle.getFuelConsumption())
                .notes(vehicle.getNotes())
                .gpsDeviceId(vehicle.getGpsDeviceId())
                .isActive(vehicle.isActive())
                .createdAt(vehicle.getCreatedAt())
                .updatedAt(vehicle.getUpdatedAt())
                .needsMaintenance(vehicle.needsMaintenance())
                .insuranceExpiringSoon(vehicle.isInsuranceExpiringSoon(30))
                .registrationExpiringSoon(vehicle.isRegistrationExpiringSoon(30))
                .availableForAssignment(vehicle.isAvailableForAssignment())
                .vehicleAge(vehicle.getVehicleAge())
                .kilometersSinceLastMaintenance(vehicle.getKilometersSinceLastMaintenance())
                .build();
    }

    private VehicleResponse.VehicleSummary mapToSummary(Vehicle vehicle) {
        return VehicleResponse.VehicleSummary.builder()
                .id(vehicle.getId().toString())
                .plateNumber(vehicle.getPlateNumber())
                .make(vehicle.getMake())
                .model(vehicle.getModel())
                .year(vehicle.getYear())
                .vehicleType(vehicle.getVehicleType())
                .status(vehicle.getStatus())
                .assignedDriverId(vehicle.getAssignedDriverId())
                .needsMaintenance(vehicle.needsMaintenance())
                .insuranceExpiringSoon(vehicle.isInsuranceExpiringSoon(30))
                .odometerReading(vehicle.getOdometerReading())
                .build();
    }
}
