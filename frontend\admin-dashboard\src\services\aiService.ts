import axios from 'axios';

// AI Service Types
export interface SentimentAnalysisResult {
  score: number; // -1 to 1 (negative to positive)
  label: 'negative' | 'neutral' | 'positive';
  confidence: number; // 0 to 1
  emotions?: {
    anger: number;
    fear: number;
    joy: number;
    sadness: number;
    surprise: number;
  };
  keywords: string[];
}

export interface PredictiveMaintenanceResult {
  vehicleId: string;
  component: string;
  failureProbability: number;
  predictedFailureDate: string;
  confidence: number;
  recommendations: string[];
  factors: Array<{
    factor: string;
    impact: number;
    description: string;
  }>;
  maintenanceActions: Array<{
    action: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimatedCost: number;
    estimatedTime: number;
  }>;
}

export interface RiskAssessmentResult {
  riskScore: number; // 0 to 100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    factor: string;
    weight: number;
    value: number;
    description: string;
  }>;
  mitigationStrategies: Array<{
    strategy: string;
    effectiveness: number;
    implementationCost: number;
    timeToImplement: number;
  }>;
  historicalComparison: {
    similarEvents: number;
    averageResolutionTime: number;
    successRate: number;
  };
}

export interface CustomerSegmentationResult {
  customerId: string;
  segment: 'regular' | 'premium' | 'vip' | 'enterprise';
  confidence: number;
  characteristics: string[];
  lifetimeValue: number;
  churnProbability: number;
  nextBestActions: Array<{
    action: string;
    expectedImpact: number;
    priority: number;
  }>;
  similarCustomers: string[];
}

export interface DemandPredictionResult {
  location: {
    lat: number;
    lng: number;
    area: string;
  };
  timeframe: string;
  predictedDemand: number;
  confidence: number;
  factors: Array<{
    factor: string;
    impact: number;
    description: string;
  }>;
  recommendations: Array<{
    action: string;
    expectedBenefit: string;
    priority: number;
  }>;
  historicalData: Array<{
    date: string;
    actualDemand: number;
    predictedDemand: number;
  }>;
}

export interface RouteOptimizationResult {
  optimizedRoute: Array<{
    lat: number;
    lng: number;
    address: string;
    estimatedArrival: string;
    serviceTime: number;
  }>;
  totalDistance: number;
  totalTime: number;
  fuelConsumption: number;
  estimatedCost: number;
  savings: {
    distanceSaved: number;
    timeSaved: number;
    fuelSaved: number;
    costSaved: number;
  };
  alternativeRoutes: Array<{
    route: any[];
    distance: number;
    time: number;
    cost: number;
    pros: string[];
    cons: string[];
  }>;
}

export interface AnomalyDetectionResult {
  anomalies: Array<{
    id: string;
    type: 'performance' | 'behavior' | 'security' | 'operational';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    detectedAt: string;
    affectedEntities: string[];
    confidence: number;
    possibleCauses: string[];
    recommendedActions: string[];
  }>;
  overallScore: number;
  trend: 'improving' | 'stable' | 'deteriorating';
}

export interface ChatbotResponse {
  response: string;
  intent: string;
  confidence: number;
  entities: Array<{
    entity: string;
    value: string;
    confidence: number;
  }>;
  suggestedActions: Array<{
    action: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
  followUpQuestions: string[];
  escalateToHuman: boolean;
}

class AIService {
  private baseURL = process.env.REACT_APP_AI_API_URL || 'http://localhost:8080';
  private openAIKey = process.env.REACT_APP_OPENAI_API_KEY;
  private azureAIKey = process.env.REACT_APP_AZURE_AI_KEY;

  // Sentiment Analysis
  async analyzeSentiment(text: string, language: string = 'en'): Promise<SentimentAnalysisResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/sentiment`, {
        text,
        language,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error analyzing sentiment:', error);
      throw error;
    }
  }

  async batchSentimentAnalysis(texts: string[], language: string = 'en'): Promise<SentimentAnalysisResult[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/sentiment/batch`, {
        texts,
        language,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error in batch sentiment analysis:', error);
      throw error;
    }
  }

  // Predictive Maintenance
  async predictMaintenance(vehicleId: string, sensorData?: any[]): Promise<PredictiveMaintenanceResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/predictive-maintenance`, {
        vehicleId,
        sensorData,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error predicting maintenance:', error);
      throw error;
    }
  }

  async updateMaintenanceModel(vehicleId: string, maintenanceRecord: any): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/ai/predictive-maintenance/update-model`, {
        vehicleId,
        maintenanceRecord,
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error updating maintenance model:', error);
      throw error;
    }
  }

  // Risk Assessment
  async assessRisk(riskData: {
    entityType: string;
    entityId: string;
    context: Record<string, any>;
    historicalData?: any[];
  }): Promise<RiskAssessmentResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/risk-assessment`, riskData, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error assessing risk:', error);
      throw error;
    }
  }

  async calculateRiskScore(factors: Array<{ factor: string; value: number; weight: number }>): Promise<number> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/risk-score`, { factors }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.score;
    } catch (error) {
      console.error('Error calculating risk score:', error);
      throw error;
    }
  }

  // Customer Segmentation
  async segmentCustomer(customerId: string): Promise<CustomerSegmentationResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/customer-segmentation`, {
        customerId,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error segmenting customer:', error);
      throw error;
    }
  }

  async batchCustomerSegmentation(customerIds: string[]): Promise<CustomerSegmentationResult[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/customer-segmentation/batch`, {
        customerIds,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error in batch customer segmentation:', error);
      throw error;
    }
  }

  // Demand Prediction
  async predictDemand(location: { lat: number; lng: number }, timeframe: string): Promise<DemandPredictionResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/demand-prediction`, {
        location,
        timeframe,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error predicting demand:', error);
      throw error;
    }
  }

  async getDemandHeatmap(bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  }, timeframe: string): Promise<Array<{
    lat: number;
    lng: number;
    demand: number;
    confidence: number;
  }>> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/demand-heatmap`, {
        bounds,
        timeframe,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error getting demand heatmap:', error);
      throw error;
    }
  }

  // Route Optimization
  async optimizeRoute(waypoints: Array<{ lat: number; lng: number; address: string }>, constraints?: {
    vehicleType?: string;
    maxDistance?: number;
    maxTime?: number;
    avoidTolls?: boolean;
    avoidHighways?: boolean;
  }): Promise<RouteOptimizationResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/route-optimization`, {
        waypoints,
        constraints,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error optimizing route:', error);
      throw error;
    }
  }

  async optimizeMultipleRoutes(routes: Array<{
    vehicleId: string;
    waypoints: Array<{ lat: number; lng: number; address: string }>;
  }>): Promise<RouteOptimizationResult[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/route-optimization/batch`, {
        routes,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error optimizing multiple routes:', error);
      throw error;
    }
  }

  // Anomaly Detection
  async detectAnomalies(data: {
    entityType: string;
    entityId: string;
    metrics: Array<{
      timestamp: string;
      values: Record<string, number>;
    }>;
    timeframe: string;
  }): Promise<AnomalyDetectionResult> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/anomaly-detection`, data, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error detecting anomalies:', error);
      throw error;
    }
  }

  // Chatbot and NLP
  async processChatMessage(message: string, context?: {
    userId: string;
    tenantId: string;
    conversationId?: string;
    previousMessages?: Array<{ role: 'user' | 'assistant'; content: string }>;
  }): Promise<ChatbotResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/chatbot`, {
        message,
        context,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error processing chat message:', error);
      throw error;
    }
  }

  async generateSmartResponse(ticketId: string, context: string): Promise<string[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/smart-response`, {
        ticketId,
        context,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.suggestions;
    } catch (error) {
      console.error('Error generating smart response:', error);
      throw error;
    }
  }

  // Text Generation and Summarization
  async summarizeText(text: string, maxLength?: number): Promise<string> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/summarize`, {
        text,
        maxLength,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.summary;
    } catch (error) {
      console.error('Error summarizing text:', error);
      throw error;
    }
  }

  async generateReport(data: any, reportType: string, template?: string): Promise<string> {
    try {
      const response = await axios.post(`${this.baseURL}/api/ai/generate-report`, {
        data,
        reportType,
        template,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.report;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  // Model Management
  async getModelStatus(modelType: string): Promise<{
    status: 'training' | 'ready' | 'updating' | 'error';
    accuracy: number;
    lastTrained: string;
    version: string;
    metrics: Record<string, number>;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/api/ai/models/${modelType}/status`, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error getting model status:', error);
      throw error;
    }
  }

  async retrainModel(modelType: string, trainingData?: any[]): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/api/ai/models/${modelType}/retrain`, {
        trainingData,
      }, {
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.error('Error retraining model:', error);
      throw error;
    }
  }

  // Helper methods
  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      'X-API-Key': this.openAIKey || this.azureAIKey || '',
    };
  }
}

export const aiService = new AIService();
export default aiService;
