// Mock data for TecnoDrive Platform
export const mockRides = [
  {
    id: 'ride_001',
    passengerId: 'user_001',
    driverId: 'driver_001',
    passengerName: 'أحمد محمد',
    driverName: 'سعد العتيبي',
    pickupLocation: {
      latitude: 24.7136,
      longitude: 46.6753,
      address: 'الرياض، حي الملز'
    },
    destination: {
      latitude: 24.7236,
      longitude: 46.6853,
      address: 'الرياض، حي العليا'
    },
    status: 'IN_PROGRESS',
    fare: 25.50,
    distance: 8.5,
    duration: 15,
    createdAt: '2025-01-20T10:30:00Z',
    updatedAt: '2025-01-20T10:35:00Z'
  },
  {
    id: 'ride_002',
    passengerId: 'user_002',
    driverId: 'driver_002',
    passengerName: 'فاطمة أحمد',
    driverName: 'محمد الشمري',
    pickupLocation: {
      latitude: 24.7036,
      longitude: 46.6653,
      address: 'الرياض، حي النخيل'
    },
    destination: {
      latitude: 24.7336,
      longitude: 46.6953,
      address: 'الرياض، حي الورود'
    },
    status: 'COMPLETED',
    fare: 18.75,
    distance: 6.2,
    duration: 12,
    createdAt: '2025-01-20T09:15:00Z',
    updatedAt: '2025-01-20T09:30:00Z'
  },
  {
    id: 'ride_003',
    passengerId: 'user_003',
    driverId: null,
    passengerName: 'عبدالله سالم',
    driverName: null,
    pickupLocation: {
      latitude: 24.6936,
      longitude: 46.6553,
      address: 'الرياض، حي الصحافة'
    },
    destination: {
      latitude: 24.7436,
      longitude: 46.7053,
      address: 'الرياض، حي الياسمين'
    },
    status: 'REQUESTED',
    fare: 32.00,
    distance: 12.3,
    duration: 20,
    createdAt: '2025-01-20T11:00:00Z',
    updatedAt: '2025-01-20T11:00:00Z'
  }
];

export const mockUsers = [
  {
    id: 'user_001',
    name: 'أحمد محمد العلي',
    email: '<EMAIL>',
    phone: '+966501234567',
    userType: 'PASSENGER',
    status: 'ACTIVE',
    createdAt: '2024-12-01T00:00:00Z',
    totalRides: 45,
    rating: 4.8
  },
  {
    id: 'driver_001',
    name: 'سعد العتيبي',
    email: '<EMAIL>',
    phone: '+966502345678',
    userType: 'DRIVER',
    status: 'ACTIVE',
    createdAt: '2024-11-15T00:00:00Z',
    totalRides: 234,
    rating: 4.9,
    vehicleId: 'vehicle_001',
    licenseNumber: 'DL123456789'
  },
  {
    id: 'user_002',
    name: 'فاطمة أحمد',
    email: '<EMAIL>',
    phone: '+966503456789',
    userType: 'PASSENGER',
    status: 'ACTIVE',
    createdAt: '2024-12-10T00:00:00Z',
    totalRides: 23,
    rating: 4.7
  },
  {
    id: 'admin_001',
    name: 'محمد الإداري',
    email: '<EMAIL>',
    phone: '+966504567890',
    userType: 'ADMIN',
    status: 'ACTIVE',
    createdAt: '2024-10-01T00:00:00Z',
    totalRides: 0,
    rating: 0
  }
];

export const mockVehicles = [
  {
    id: 'vehicle_001',
    driverId: 'driver_001',
    driverName: 'سعد العتيبي',
    make: 'Toyota',
    model: 'Camry',
    year: 2022,
    plateNumber: 'ABC-1234',
    color: 'أبيض',
    status: 'ACTIVE',
    location: {
      latitude: 24.7136,
      longitude: 46.6753
    },
    lastUpdate: '2025-01-20T11:30:00Z'
  },
  {
    id: 'vehicle_002',
    driverId: 'driver_002',
    driverName: 'محمد الشمري',
    make: 'Hyundai',
    model: 'Elantra',
    year: 2021,
    plateNumber: 'XYZ-5678',
    color: 'أسود',
    status: 'ACTIVE',
    location: {
      latitude: 24.7236,
      longitude: 46.6853
    },
    lastUpdate: '2025-01-20T11:25:00Z'
  },
  {
    id: 'vehicle_003',
    driverId: 'driver_003',
    driverName: 'عبدالرحمن القحطاني',
    make: 'Nissan',
    model: 'Altima',
    year: 2023,
    plateNumber: 'DEF-9012',
    color: 'فضي',
    status: 'MAINTENANCE',
    location: {
      latitude: 24.6936,
      longitude: 46.6553
    },
    lastUpdate: '2025-01-20T08:00:00Z'
  }
];

export const mockParcels = [
  {
    id: 'parcel_001',
    trackingNumber: 'TN001234567',
    senderName: 'شركة التجارة الحديثة',
    recipientName: 'خالد أحمد',
    senderAddress: 'الرياض، حي العليا، شارع الملك فهد',
    recipientAddress: 'الرياض، حي الملز، شارع الأمير محمد بن عبدالعزيز',
    weight: 2.5,
    status: 'IN_TRANSIT',
    createdAt: '2025-01-20T09:00:00Z',
    estimatedDelivery: '2025-01-20T15:00:00Z',
    currentLocation: {
      latitude: 24.7136,
      longitude: 46.6753
    }
  },
  {
    id: 'parcel_002',
    trackingNumber: 'TN001234568',
    senderName: 'مؤسسة الإلكترونيات',
    recipientName: 'نورا سالم',
    senderAddress: 'الرياض، حي النخيل، طريق الملك عبدالعزيز',
    recipientAddress: 'الرياض، حي الورود، شارع التحلية',
    weight: 1.2,
    status: 'DELIVERED',
    createdAt: '2025-01-19T14:30:00Z',
    estimatedDelivery: '2025-01-20T10:00:00Z',
    deliveredAt: '2025-01-20T09:45:00Z'
  },
  {
    id: 'parcel_003',
    trackingNumber: 'TN001234569',
    senderName: 'متجر الأزياء العصرية',
    recipientName: 'عبدالله محمد',
    senderAddress: 'الرياض، حي الصحافة، مجمع الراشد',
    recipientAddress: 'الرياض، حي الياسمين، فيلا رقم 123',
    weight: 0.8,
    status: 'PICKED_UP',
    createdAt: '2025-01-20T11:15:00Z',
    estimatedDelivery: '2025-01-20T17:00:00Z'
  }
];

export const mockPayments = [
  {
    id: 'payment_001',
    rideId: 'ride_002',
    amount: 18.75,
    method: 'CASH',
    status: 'COMPLETED',
    createdAt: '2025-01-20T09:30:00Z',
    description: 'دفع رحلة من النخيل إلى الورود'
  },
  {
    id: 'payment_002',
    rideId: 'ride_001',
    amount: 25.50,
    method: 'CARD',
    status: 'PENDING',
    createdAt: '2025-01-20T10:35:00Z',
    description: 'دفع رحلة من الملز إلى العليا'
  },
  {
    id: 'payment_003',
    parcelId: 'parcel_002',
    amount: 15.00,
    method: 'WALLET',
    status: 'COMPLETED',
    createdAt: '2025-01-20T09:45:00Z',
    description: 'دفع توصيل طرد'
  }
];

export const mockAnalytics = {
  totalRides: 1247,
  totalRevenue: 45678.50,
  activeDrivers: 89,
  totalUsers: 2341,
  ridesThisMonth: 234,
  revenueThisMonth: 8765.25,
  averageRating: 4.7,
  completionRate: 94.2
};
