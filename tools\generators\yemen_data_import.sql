-- ===================================================================
-- TECNODRIVE Platform - البيانات اليمنية المولدة
-- تم إنشاؤها يدوياً من البيانات النموذجية
-- تاريخ الإنشاء: 2025-01-26
-- ===================================================================

-- تنظيف البيانات الموجودة (قم بإلغاء التعليق إذا كنت تريد حذف البيانات الموجودة)
-- TRUNCATE TABLE parcels, trips, vehicles, users, tenants CASCADE;

-- ===================================================================
-- إدراج الشركات والمؤسسات اليمنية
-- ===================================================================

INSERT INTO tenants (
    id, name, type, contact_email, phone_number, address, 
    status, created_at, updated_at
) VALUES 
('comp-001', 'شركة النقل اليمنية - صنعاء', 'company', '<EMAIL>', '+967777123456', 'صنعاء، شارع الستين', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('comp-002', 'مؤسسة التوصيل السريع - عدن', 'company', '<EMAIL>', '+************', 'عدن، شارع الثورة', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- إدراج المستخدمين والسائقين
-- ===================================================================

-- المستخدمين العاديين (الركاب)
INSERT INTO users (
    id, email, phone_number, password_hash, first_name, last_name, 
    user_type, status, account_tier, nationality, preferred_language, 
    country, is_email_verified, is_phone_verified, rating, 
    total_trips, total_spent, wallet_balance, loyalty_points, 
    created_at, updated_at
) VALUES 
('user-001', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'أحمد', 'الحوثي', 'passenger', 'active', 'basic', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.0, 0, 0.00, 1000.00, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('user-002', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'فاطمة', 'الزبيدي', 'passenger', 'active', 'basic', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.0, 0, 0.00, 1000.00, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- السائقين
INSERT INTO users (
    id, email, phone_number, password_hash, first_name, last_name, 
    user_type, status, account_tier, nationality, preferred_language, 
    country, is_email_verified, is_phone_verified, rating, 
    total_trips, total_spent, wallet_balance, loyalty_points, 
    created_at, updated_at
) VALUES 
('driver-001', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'محمد', 'الأحمر', 'driver', 'active', 'silver', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.8, 50, 0.00, 2500.00, 250, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('driver-002', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'علي', 'صالح', 'driver', 'active', 'silver', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.6, 35, 0.00, 1800.00, 175, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- إدراج المركبات
-- ===================================================================

INSERT INTO vehicles (
    id, driver_id, make, model, year, license_plate, color, 
    vehicle_type, capacity, status, created_at, updated_at
) VALUES 
('vehicle-001', 'driver-001', 'Toyota', 'Corolla', 2020, 'YEM-001-ABC', 'أبيض', 'economy', 4, 'available', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vehicle-002', 'driver-002', 'Hyundai', 'Elantra', 2019, 'YEM-002-DEF', 'أزرق', 'comfort', 4, 'available', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- إدراج الرحلات التجريبية
-- ===================================================================

INSERT INTO trips (
    id, user_id, driver_id, vehicle_id, trip_type, status,
    pickup_location, dropoff_location, pickup_lat, pickup_lon,
    dropoff_lat, dropoff_lon, distance_km, fare_amount,
    payment_status, start_time, end_time, requested_at,
    driver_rating, user_rating, created_at, updated_at
) VALUES 
('trip-001', 'user-001', 'driver-001', 'vehicle-001', 'ride', 'completed', 'صنعاء - شارع الزبيري', 'صنعاء - جامعة صنعاء', 15.3520, 44.2065, 15.3720, 44.2165, 5.2, 1500.00, 'paid', '2025-01-25 08:00:00', '2025-01-25 08:25:00', '2025-01-25 07:55:00', 4.8, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('trip-002', 'user-002', 'driver-002', 'vehicle-002', 'ride', 'completed', 'عدن - شارع الثورة', 'عدن - مطار عدن', 12.7797, 45.0365, 12.8297, 45.0265, 8.7, 2200.00, 'paid', '2025-01-25 14:30:00', '2025-01-25 15:05:00', '2025-01-25 14:25:00', 4.6, 4.7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- إدراج الطرود والشحنات
-- ===================================================================

INSERT INTO parcels (
    id, sender_id, driver_id, vehicle_id, status,
    pickup_address, pickup_lat, pickup_lon,
    delivery_address, delivery_lat, delivery_lon,
    recipient_name, recipient_phone, item_description,
    weight_kg, dimensions_cm, delivery_fee, payment_status,
    pickup_time, delivery_time, requested_at, created_at, updated_at
) VALUES 
('parcel-001', 'user-001', 'driver-001', 'vehicle-001', 'delivered', 'صنعاء - السبعين', 15.3420, 44.2165, 'صنعاء - شارع الستين', 15.3620, 44.2065, 'محمد أحمد', '+967777333444', 'وثائق مهمة', 0.5, '30x20x5', 800.00, 'paid', '2025-01-25 10:00:00', '2025-01-25 10:45:00', '2025-01-25 09:50:00', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('parcel-002', 'user-002', 'driver-002', 'vehicle-002', 'in_transit', 'عدن - كريتر', 12.7897, 45.0465, 'عدن - المعلا', 12.7697, 45.0265, 'سارة علي', '+967733555666', 'هدايا', 2.3, '40x30x15', 1200.00, 'pending', '2025-01-26 09:15:00', NULL, '2025-01-26 09:00:00', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- إضافة بيانات إضافية للاختبار
-- ===================================================================

-- مستخدمين إضافيين من محافظات مختلفة
INSERT INTO users (
    id, email, phone_number, password_hash, first_name, last_name, 
    user_type, status, account_tier, nationality, preferred_language, 
    country, is_email_verified, is_phone_verified, rating, 
    total_trips, total_spent, wallet_balance, loyalty_points, 
    created_at, updated_at
) VALUES 
('user-003', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'عمر', 'التعزي', 'passenger', 'active', 'basic', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.2, 3, 850.00, 1200.00, 15, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('user-004', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'نادية', 'الحديدة', 'passenger', 'active', 'basic', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.1, 2, 600.00, 800.00, 10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('driver-003', '<EMAIL>', '+************', '$2a$10$defaultpasswordhash', 'حسن', 'الإبي', 'driver', 'active', 'silver', 'Yemen', 'ar', 'Yemen', TRUE, TRUE, 4.7, 42, 0.00, 2100.00, 210, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- مركبات إضافية
INSERT INTO vehicles (
    id, driver_id, make, model, year, license_plate, color, 
    vehicle_type, capacity, status, created_at, updated_at
) VALUES 
('vehicle-003', 'driver-003', 'Nissan', 'Sunny', 2021, 'YEM-003-GHI', 'أحمر', 'economy', 4, 'available', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- رحلات إضافية
INSERT INTO trips (
    id, user_id, driver_id, vehicle_id, trip_type, status,
    pickup_location, dropoff_location, pickup_lat, pickup_lon,
    dropoff_lat, dropoff_lon, distance_km, fare_amount,
    payment_status, requested_at, created_at, updated_at
) VALUES 
('trip-003', 'user-003', 'driver-003', 'vehicle-003', 'ride', 'in_progress', 'تعز - شارع جمال', 'تعز - المستشفى الجمهوري', 13.5795, 44.0205, 13.5895, 44.0305, 3.2, 1000.00, 'pending', '2025-01-26 11:30:00', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ===================================================================
-- تحديث الإحصائيات
-- ===================================================================

-- تحديث إحصائيات السائقين
UPDATE users SET 
    total_trips = total_trips + 1,
    wallet_balance = wallet_balance + 500.00
WHERE user_type = 'driver' AND id IN ('driver-001', 'driver-002');

-- ===================================================================
-- انتهاء ملف البيانات اليمنية
-- ===================================================================
