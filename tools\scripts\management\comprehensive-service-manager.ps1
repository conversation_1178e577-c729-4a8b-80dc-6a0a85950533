# Comprehensive TecnoDrive Service Manager
param(
    [Parameter(Mandatory=$false)]
    [switch]$Check = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Start = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Stop = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Restart = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Health = $false
)

# Service definitions with correct ports
$services = @{
    "eureka-server" = @{ Port = 8761; Container = "eureka-tecno"; HealthPath = "/actuator/health" }
    "api-gateway" = @{ Port = 8080; Container = "api-gateway-tecno"; HealthPath = "/actuator/health" }
    "auth-service" = @{ Port = 8081; Container = "auth-service-tecno"; HealthPath = "/actuator/health" }
    "ride-service" = @{ Port = 8082; Container = "ride-service-tecno"; HealthPath = "/actuator/health" }
    "user-service" = @{ Port = 8083; Container = "user-service-tecno"; HealthPath = "/actuator/health" }
    "fleet-service" = @{ Port = 8084; Container = "fleet-service-tecno"; HealthPath = "/actuator/health" }
    "location-service" = @{ Port = 8085; Container = "location-service-tecno"; HealthPath = "/actuator/health" }
    "payment-service" = @{ Port = 8086; Container = "payment-service-tecno"; HealthPath = "/actuator/health" }
    "parcel-service" = @{ Port = 8087; Container = "parcel-service-tecno"; HealthPath = "/actuator/health" }
    "notification-service" = @{ Port = 8088; Container = "notification-service-tecno"; HealthPath = "/actuator/health" }
    "analytics-service" = @{ Port = 8089; Container = "analytics-service-tecno"; HealthPath = "/actuator/health" }
    "hr-service" = @{ Port = 8090; Container = "hr-service-tecno"; HealthPath = "/actuator/health" }
    "financial-service" = @{ Port = 8091; Container = "financial-service-tecno"; HealthPath = "/actuator/health" }
    "saas-management-service" = @{ Port = 8092; Container = "saas-management-service-tecno"; HealthPath = "/actuator/health" }
}

$infrastructure = @{
    "postgres" = @{ Port = 5432; Container = "postgres-tecno" }
    "redis" = @{ Port = 6379; Container = "redis-tecno" }
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-ServiceHealth {
    param([string]$ServiceName, [hashtable]$ServiceInfo)
    
    $url = "http://localhost:$($ServiceInfo.Port)$($ServiceInfo.HealthPath)"
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-ColorOutput "  ✅ $ServiceName (Port $($ServiceInfo.Port)): Healthy" "Green"
            return $true
        } else {
            Write-ColorOutput "  ⚠️  $ServiceName (Port $($ServiceInfo.Port)): Responding but not healthy" "Yellow"
            return $false
        }
    }
    catch {
        Write-ColorOutput "  ❌ $ServiceName (Port $($ServiceInfo.Port)): Not responding" "Red"
        return $false
    }
}

function Get-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker ps -a --filter "name=$ContainerName" --format "{{.Status}}" 2>$null
        if ($status) {
            if ($status -like "*Up*") {
                return "Running"
            } elseif ($status -like "*Exited*") {
                return "Stopped"
            } else {
                return "Unknown"
            }
        } else {
            return "Not Found"
        }
    }
    catch {
        return "Error"
    }
}

function Start-ServiceContainer {
    param([string]$ServiceName, [string]$ContainerName)
    
    $status = Get-ContainerStatus -ContainerName $ContainerName
    
    if ($status -eq "Not Found") {
        Write-ColorOutput "  ⚠️  Container $ContainerName not found. Creating..." "Yellow"
        # Try to create from docker-compose
        docker compose -f docker-compose.complete.yml up -d $ServiceName 2>$null
    } elseif ($status -eq "Stopped") {
        Write-ColorOutput "  🔄 Starting $ContainerName..." "Cyan"
        docker start $ContainerName 2>$null
    } elseif ($status -eq "Running") {
        Write-ColorOutput "  ✅ $ContainerName already running" "Green"
    }
    
    Start-Sleep -Seconds 2
}

function Show-ServiceStatus {
    Write-ColorOutput "`n📊 TecnoDrive Platform Service Status" "Cyan"
    Write-ColorOutput "============================================================" "Gray"
    
    # Infrastructure Services
    Write-ColorOutput "`n🏗️  Infrastructure Services:" "Yellow"
    foreach ($service in $infrastructure.GetEnumerator()) {
        $status = Get-ContainerStatus -ContainerName $service.Value.Container
        $statusColor = if ($status -eq "Running") { "Green" } elseif ($status -eq "Stopped") { "Red" } else { "Yellow" }
        Write-ColorOutput "  • $($service.Key.PadRight(20)) Port: $($service.Value.Port.ToString().PadRight(5)) Status: $status" $statusColor
    }
    
    # Application Services
    Write-ColorOutput "`n🔧 Application Services:" "Yellow"
    foreach ($service in $services.GetEnumerator()) {
        $status = Get-ContainerStatus -ContainerName $service.Value.Container
        $statusColor = if ($status -eq "Running") { "Green" } elseif ($status -eq "Stopped") { "Red" } else { "Yellow" }
        Write-ColorOutput "  • $($service.Key.PadRight(25)) Port: $($service.Value.Port.ToString().PadRight(5)) Status: $status" $statusColor
    }
}

function Start-AllServices {
    Write-ColorOutput "`n🚀 Starting TecnoDrive Platform Services..." "Green"
    
    # Start infrastructure first
    Write-ColorOutput "`n📦 Starting Infrastructure Services..." "Cyan"
    foreach ($service in $infrastructure.GetEnumerator()) {
        Start-ServiceContainer -ServiceName $service.Key -ContainerName $service.Value.Container
    }
    
    Write-ColorOutput "⏳ Waiting for infrastructure to be ready..." "Yellow"
    Start-Sleep -Seconds 15
    
    # Start core services
    Write-ColorOutput "`n🔧 Starting Core Services..." "Cyan"
    $coreServices = @("eureka-server", "api-gateway")
    foreach ($serviceName in $coreServices) {
        if ($services.ContainsKey($serviceName)) {
            Start-ServiceContainer -ServiceName $serviceName -ContainerName $services[$serviceName].Container
        }
    }
    
    Write-ColorOutput "⏳ Waiting for core services..." "Yellow"
    Start-Sleep -Seconds 20
    
    # Start application services
    Write-ColorOutput "`n🎯 Starting Application Services..." "Cyan"
    $appServices = $services.Keys | Where-Object { $_ -notin $coreServices }
    foreach ($serviceName in $appServices) {
        Start-ServiceContainer -ServiceName $serviceName -ContainerName $services[$serviceName].Container
        Start-Sleep -Seconds 3
    }
}

function Test-AllHealth {
    Write-ColorOutput "`n🔍 Health Check Results:" "Cyan"
    Write-ColorOutput "==================================================" "Gray"
    
    $healthyCount = 0
    $totalCount = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        if (Test-ServiceHealth -ServiceName $service.Key -ServiceInfo $service.Value) {
            $healthyCount++
        }
    }
    
    Write-ColorOutput "`n📈 Health Summary: $healthyCount/$totalCount services healthy" "Cyan"
    
    if ($healthyCount -eq $totalCount) {
        Write-ColorOutput "🎉 All services are healthy!" "Green"
    } elseif ($healthyCount -gt ($totalCount * 0.7)) {
        Write-ColorOutput "⚠️  Most services are healthy, some need attention" "Yellow"
    } else {
        Write-ColorOutput "❌ Many services need attention" "Red"
    }
}

function Stop-AllServices {
    Write-ColorOutput "`n🛑 Stopping TecnoDrive Platform Services..." "Red"
    
    # Stop application services first
    foreach ($service in $services.GetEnumerator()) {
        Write-ColorOutput "  🔄 Stopping $($service.Value.Container)..." "Yellow"
        docker stop $service.Value.Container 2>$null
    }
    
    # Stop infrastructure last
    foreach ($service in $infrastructure.GetEnumerator()) {
        Write-ColorOutput "  🔄 Stopping $($service.Value.Container)..." "Yellow"
        docker stop $service.Value.Container 2>$null
    }
    
    Write-ColorOutput "✅ All services stopped" "Green"
}

function Restart-AllServices {
    Write-ColorOutput "`n🔄 Restarting TecnoDrive Platform..." "Cyan"
    Stop-AllServices
    Start-Sleep -Seconds 10
    Start-AllServices
}

# Main execution
if (-not $Check -and -not $Start -and -not $Stop -and -not $Restart -and -not $Health) {
    Write-ColorOutput "🎯 TecnoDrive Service Manager" "Green"
    Write-ColorOutput "Usage:" "White"
    Write-ColorOutput "  -Check    : Show current service status" "Cyan"
    Write-ColorOutput "  -Start    : Start all services" "Green"
    Write-ColorOutput "  -Stop     : Stop all services" "Red"
    Write-ColorOutput "  -Restart  : Restart all services" "Yellow"
    Write-ColorOutput "  -Health   : Perform health checks" "Cyan"
    exit 0
}

if ($Check) { Show-ServiceStatus }
if ($Start) { Start-AllServices }
if ($Stop) { Stop-AllServices }
if ($Restart) { Restart-AllServices }
if ($Health) { Test-AllHealth }

# Always show final status
Show-ServiceStatus

Write-ColorOutput "`n🌐 Service Access URLs:" "Green"
Write-ColorOutput "  • Eureka Dashboard:    http://localhost:8761" "White"
Write-ColorOutput "  • API Gateway:         http://localhost:8080" "White"
Write-ColorOutput "  • Auth Service:        http://localhost:8081/actuator/health" "White"
Write-ColorOutput "  • Ride Service:        http://localhost:8082/actuator/health" "White"
Write-ColorOutput "  • User Service:        http://localhost:8083/actuator/health" "White"
Write-ColorOutput "  • Fleet Service:       http://localhost:8084/actuator/health" "White"
Write-ColorOutput "  • Location Service:    http://localhost:8085/actuator/health" "White"
Write-ColorOutput "  • Payment Service:     http://localhost:8086/actuator/health" "White"
Write-ColorOutput "  • Parcel Service:      http://localhost:8087/actuator/health" "White"
Write-ColorOutput "  • Notification Service: http://localhost:8088/actuator/health" "White"
Write-ColorOutput "  • Analytics Service:   http://localhost:8089/actuator/health" "White"
Write-ColorOutput "  • HR Service:          http://localhost:8090/actuator/health" "White"
Write-ColorOutput "  • Financial Service:   http://localhost:8091/actuator/health" "White"
Write-ColorOutput "  • SaaS Management:     http://localhost:8092/actuator/health" "White"
