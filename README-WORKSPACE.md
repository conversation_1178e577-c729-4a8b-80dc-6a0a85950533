# 🚀 TecnoDrive Platform - Multi-Root Workspace Guide

## 📋 نظرة عامة

تم إعداد **Multi-Root Workspace** في VSCode لإدارة جميع مكونات منصة TecnoDrive في نافذة واحدة، مما يسهل التطوير والتنقل بين المشاريع المختلفة.

## 🏗️ بنية Workspace

### 📁 المجلدات الرئيسية

| المجلد | الوصف | التقنية |
|--------|--------|---------|
| 🏠 Root | المجلد الجذر للمنصة | - |
| 📚 Documentation | جميع الوثائق | Markdown |
| ⚙️ Configuration | ملفات التكوين | YAML/JSON |
| 🔧 Tools & Scripts | أدوات التطوير | PowerShell/Bash |
| 🏗️ Infrastructure | البنية التحتية | Docker/K8s/Terraform |

### 🐍 Python Backend
- **Comprehensive System** - النظام الشامل بـ FastAPI

### ☕ Java Microservices

#### Core Services (الخدمات الأساسية)
- 🔐 **Auth Service** - المصادقة والتفويض
- 👤 **User Service** - إدارة المستخدمين  
- 💳 **Payment Service** - معالجة المدفوعات
- 🚗 **Ride Service** - إدارة الرحلات

#### Business Services (خدمات الأعمال)
- 📊 **Analytics Service** - التحليلات والذكاء الاصطناعي
- 💰 **Financial Service** - الخدمات المالية
- 🚛 **Fleet Service** - إدارة الأسطول
- 👥 **HR Service** - الموارد البشرية
- 📍 **Location Service** - المواقع والخرائط
- 🔔 **Notification Service** - الإشعارات
- 📦 **Parcel Service** - إدارة الطرود
- 💼 **Wallet Service** - المحافظ الرقمية

#### Infrastructure Services (خدمات البنية التحتية)
- 🌐 **API Gateway** - بوابة API الموحدة
- 🔍 **Eureka Server** - اكتشاف الخدمات
- 🏢 **SaaS Management** - إدارة SaaS
- 🏠 **Tenant Management** - إدارة المستأجرين

### ⚛️ Frontend Applications
- 🎛️ **Admin Dashboard** - لوحة تحكم الإدارة (React + TypeScript)
- 🚗 **Driver App** - تطبيق السائقين
- 👤 **Passenger App** - تطبيق الركاب
- 🧩 **Shared Components** - المكونات المشتركة

## 🚀 كيفية الاستخدام

### 1. فتح Workspace
```bash
# فتح VSCode مع الـ workspace
code tecno-drive-workspace.code-workspace
```

### 2. المهام المتاحة (Tasks)

#### 🏗️ مهام البناء والتشغيل
- **🚀 Start All Core Services** - تشغيل جميع الخدمات الأساسية
- **🛑 Stop All Services** - إيقاف جميع الخدمات
- **🏗️ Build All Java Services** - بناء جميع خدمات Java
- **🧪 Test All Java Services** - اختبار جميع خدمات Java
- **📦 Package All Java Services** - تحزيم جميع خدمات Java

#### 🎛️ مهام Frontend
- **🎛️ Start Admin Dashboard** - تشغيل لوحة التحكم
- **🏗️ Build Admin Dashboard** - بناء لوحة التحكم

#### 🐍 مهام Python
- **🐍 Start Python Backend** - تشغيل النظام الشامل

#### 🐳 مهام Docker
- **🐳 Docker Compose Up** - تشغيل جميع الخدمات بـ Docker
- **🐳 Docker Compose Down** - إيقاف Docker Compose

#### 📊 مهام المراقبة
- **📊 Monitor Services** - مراقبة حالة الخدمات

### 3. تكوينات التصحيح (Debug Configurations)

#### ☕ Java Services
- **🔐 Debug Auth Service** - تصحيح خدمة المصادقة
- **🌐 Debug API Gateway** - تصحيح بوابة API

#### 🐍 Python Backend
- **🐍 Debug Python Backend** - تصحيح النظام الشامل

#### ⚛️ Frontend
- **🎛️ Debug Admin Dashboard** - تصحيح لوحة التحكم

## ⚙️ الإعدادات المتقدمة

### 🔧 إعدادات Java
- Maven integration مفعل
- Spring Boot support
- Auto-import للمكتبات
- Code formatting تلقائي

### 🐍 إعدادات Python
- Virtual environment تلقائي
- Black formatter
- Pylint linting
- Auto-import للمكتبات

### ⚛️ إعدادات Frontend
- TypeScript support كامل
- ESLint integration
- Prettier formatting
- Auto-import للمكونات

### 🐳 إعدادات DevOps
- Docker integration
- Kubernetes support
- Terraform syntax highlighting
- YAML validation

## 📊 ميزات الإنتاجية

### 🗂️ File Nesting
- تجميع الملفات ذات الصلة
- إخفاء ملفات البناء
- تنظيم أفضل للمشروع

### 🔍 Search & Exclude
- استبعاد مجلدات البناء من البحث
- تحسين أداء البحث
- فلترة الملفات غير المهمة

### 🎨 UI Customization
- ألوان مخصصة للمشروع
- تمييز بصري للمجلدات
- تحسين تجربة المطور

## 🛠️ الإضافات المطلوبة

### ☕ Java Development
- Java Extension Pack
- Spring Boot Tools
- Maven for Java

### 🐍 Python Development
- Python Extension
- Black Formatter
- Pylint

### ⚛️ Frontend Development
- TypeScript and JavaScript
- ESLint
- Prettier

### 🐳 DevOps
- Docker
- Kubernetes Tools
- Terraform

## 📝 نصائح للاستخدام

### 1. التنقل السريع
- استخدم `Ctrl+P` للبحث عن الملفات
- استخدم `Ctrl+Shift+P` للوصول للمهام
- استخدم `Ctrl+`` لفتح Terminal

### 2. إدارة المشاريع
- كل مشروع فرعي له إعداداته الخاصة
- يمكن تشغيل عدة خدمات في نفس الوقت
- استخدم المهام المعرفة مسبقاً

### 3. التصحيح
- كل خدمة لها تكوين تصحيح منفصل
- يمكن تصحيح عدة خدمات في نفس الوقت
- استخدم Breakpoints للتحكم في التنفيذ

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### Java Services
```bash
# إذا فشل Maven في التحميل
mvn clean install -f config/pom.xml

# إذا فشل Spring Boot في البدء
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Python Backend
```bash
# إذا فشل Python في البدء
cd backend/comprehensive-system
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### Frontend
```bash
# إذا فشل npm في التحميل
cd frontend/admin-dashboard
npm install --force
npm start
```

## 📞 الدعم

للحصول على المساعدة:
1. راجع الوثائق في مجلد `docs/`
2. تحقق من ملفات README في كل مشروع فرعي
3. استخدم مهام المراقبة لفحص حالة الخدمات

---

**تم إعداد هذا Workspace لتحسين تجربة التطوير وزيادة الإنتاجية في مشروع TecnoDrive Platform.**
