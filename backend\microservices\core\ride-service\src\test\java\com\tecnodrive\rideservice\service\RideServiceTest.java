package com.tecnodrive.rideservice.service;

import com.tecnodrive.rideservice.dto.RideRequestDto;
import com.tecnodrive.rideservice.dto.RideResponseDto;
import com.tecnodrive.rideservice.entity.Ride;
import com.tecnodrive.rideservice.entity.RideStatus;
import com.tecnodrive.rideservice.entity.RideType;
import com.tecnodrive.rideservice.entity.VehicleType;
import com.tecnodrive.rideservice.repository.RideRepository;
import com.tecnodrive.rideservice.repository.VehicleTypeRepository;
import com.tecnodrive.rideservice.service.impl.RideServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for RideService
 */
@ExtendWith(MockitoExtension.class)
class RideServiceTest {

    @Mock
    private RideRepository rideRepository;

    @Mock
    private VehicleTypeRepository vehicleTypeRepository;

    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    private RideService rideService;

    @BeforeEach
    void setUp() {
        rideService = new RideServiceImpl(rideRepository, vehicleTypeRepository, kafkaTemplate);
    }

    @Test
    void requestRide_ShouldCreateRide_WhenValidRequest() {
        // Given
        UUID passengerId = UUID.randomUUID();
        RideRequestDto.LocationDto pickup = new RideRequestDto.LocationDto(15.3694, 44.1910); // Sana'a
        RideRequestDto.LocationDto destination = new RideRequestDto.LocationDto(15.3729, 44.2065);
        
        RideRequestDto request = new RideRequestDto(
            passengerId,
            pickup,
            "شارع الزبيري، صنعاء",
            destination,
            "جامعة صنعاء، صنعاء",
            "economy",
            RideType.STANDARD,
            null,
            "في انتظاركم",
            null,
            null
        );

        VehicleType vehicleType = new VehicleType("economy", "اقتصادي", 
            BigDecimal.valueOf(500), BigDecimal.valueOf(100), BigDecimal.valueOf(50), 4);
        vehicleType.setId(UUID.randomUUID());

        Ride savedRide = new Ride();
        savedRide.setId(UUID.randomUUID());
        savedRide.setPassengerId(passengerId);
        savedRide.setStatus(RideStatus.REQUESTED);
        savedRide.setVehicleType(vehicleType);

        when(rideRepository.findActiveRideByPassenger(passengerId)).thenReturn(Optional.empty());
        when(vehicleTypeRepository.findByNameAndIsActiveTrue("economy")).thenReturn(Optional.of(vehicleType));
        when(rideRepository.save(any(Ride.class))).thenReturn(savedRide);

        // When
        RideResponseDto result = rideService.requestRide(request);

        // Then
        assertNotNull(result);
        assertEquals(passengerId, result.passengerId());
        assertEquals(RideStatus.REQUESTED, result.status());
        verify(rideRepository).save(any(Ride.class));
        verify(kafkaTemplate).send(eq("ride-requested"), any());
    }

    @Test
    void requestRide_ShouldThrowException_WhenPassengerHasActiveRide() {
        // Given
        UUID passengerId = UUID.randomUUID();
        RideRequestDto request = new RideRequestDto(
            passengerId,
            new RideRequestDto.LocationDto(15.3694, 44.1910),
            "شارع الزبيري، صنعاء",
            new RideRequestDto.LocationDto(15.3729, 44.2065),
            "جامعة صنعاء، صنعاء",
            "economy",
            RideType.STANDARD,
            null,
            null,
            null,
            null
        );

        Ride activeRide = new Ride();
        activeRide.setStatus(RideStatus.IN_PROGRESS);
        when(rideRepository.findActiveRideByPassenger(passengerId)).thenReturn(Optional.of(activeRide));

        // When & Then
        assertThrows(IllegalStateException.class, () -> rideService.requestRide(request));
        verify(rideRepository, never()).save(any(Ride.class));
    }

    @Test
    void assignDriver_ShouldAssignDriver_WhenValidRequest() {
        // Given
        UUID rideId = UUID.randomUUID();
        UUID driverId = UUID.randomUUID();
        
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setStatus(RideStatus.REQUESTED);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));
        when(rideRepository.findActiveRideByDriver(driverId)).thenReturn(Optional.empty());
        when(rideRepository.save(any(Ride.class))).thenReturn(ride);

        // When
        RideResponseDto result = rideService.assignDriver(rideId, driverId);

        // Then
        assertNotNull(result);
        assertEquals(driverId, result.driverId());
        assertEquals(RideStatus.DRIVER_ASSIGNED, result.status());
        verify(rideRepository).save(any(Ride.class));
        verify(kafkaTemplate).send(eq("driver-assigned"), any());
    }

    @Test
    void assignDriver_ShouldThrowException_WhenDriverHasActiveRide() {
        // Given
        UUID rideId = UUID.randomUUID();
        UUID driverId = UUID.randomUUID();
        
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setStatus(RideStatus.REQUESTED);

        Ride activeDriverRide = new Ride();
        activeDriverRide.setStatus(RideStatus.IN_PROGRESS);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));
        when(rideRepository.findActiveRideByDriver(driverId)).thenReturn(Optional.of(activeDriverRide));

        // When & Then
        assertThrows(IllegalStateException.class, () -> rideService.assignDriver(rideId, driverId));
        verify(rideRepository, never()).save(any(Ride.class));
    }

    @Test
    void updateRideStatus_ShouldUpdateStatus_WhenValidTransition() {
        // Given
        UUID rideId = UUID.randomUUID();
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setStatus(RideStatus.DRIVER_ASSIGNED);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));
        when(rideRepository.save(any(Ride.class))).thenReturn(ride);

        // When
        RideResponseDto result = rideService.updateRideStatus(rideId, RideStatus.DRIVER_ARRIVED, null);

        // Then
        assertNotNull(result);
        assertEquals(RideStatus.DRIVER_ARRIVED, result.status());
        verify(rideRepository).save(any(Ride.class));
        verify(kafkaTemplate).send(eq("ride-status-updated"), any());
    }

    @Test
    void completeRide_ShouldCompleteRide_WhenInProgress() {
        // Given
        UUID rideId = UUID.randomUUID();
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setStatus(RideStatus.IN_PROGRESS);

        BigDecimal finalFare = BigDecimal.valueOf(1500);
        BigDecimal actualDistance = BigDecimal.valueOf(8.5);
        Integer actualDuration = 25;

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));
        when(rideRepository.save(any(Ride.class))).thenReturn(ride);

        // When
        RideResponseDto result = rideService.completeRide(rideId, finalFare, actualDistance, actualDuration);

        // Then
        assertNotNull(result);
        assertEquals(RideStatus.COMPLETED, result.status());
        assertEquals(finalFare, result.finalFare());
        assertEquals(actualDistance, result.actualDistanceKm());
        assertEquals(actualDuration, result.actualDurationMinutes());
        verify(rideRepository).save(any(Ride.class));
        verify(kafkaTemplate).send(eq("ride-completed"), any());
    }

    @Test
    void rateRide_ShouldRateRide_WhenValidRating() {
        // Given
        UUID rideId = UUID.randomUUID();
        UUID passengerId = UUID.randomUUID();
        Integer rating = 5;
        
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setPassengerId(passengerId);
        ride.setStatus(RideStatus.COMPLETED);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));
        when(rideRepository.save(any(Ride.class))).thenReturn(ride);

        // When
        RideResponseDto result = rideService.rateRide(rideId, passengerId, rating);

        // Then
        assertNotNull(result);
        assertEquals(rating, result.ratingByPassenger());
        verify(rideRepository).save(any(Ride.class));
        verify(kafkaTemplate).send(eq("ride-rated"), any());
    }

    @Test
    void rateRide_ShouldThrowException_WhenInvalidRating() {
        // Given
        UUID rideId = UUID.randomUUID();
        UUID passengerId = UUID.randomUUID();
        Integer invalidRating = 6; // Rating should be 1-5
        
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setPassengerId(passengerId);
        ride.setStatus(RideStatus.COMPLETED);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            rideService.rateRide(rideId, passengerId, invalidRating));
        verify(rideRepository, never()).save(any(Ride.class));
    }

    @Test
    void getRideById_ShouldReturnRide_WhenExists() {
        // Given
        UUID rideId = UUID.randomUUID();
        Ride ride = new Ride();
        ride.setId(rideId);
        ride.setStatus(RideStatus.COMPLETED);

        when(rideRepository.findById(rideId)).thenReturn(Optional.of(ride));

        // When
        RideResponseDto result = rideService.getRideById(rideId);

        // Then
        assertNotNull(result);
        assertEquals(rideId, result.id());
        assertEquals(RideStatus.COMPLETED, result.status());
    }

    @Test
    void getRideById_ShouldThrowException_WhenNotExists() {
        // Given
        UUID rideId = UUID.randomUUID();
        when(rideRepository.findById(rideId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> rideService.getRideById(rideId));
    }
}



