package com.tecnodrive.rideservice.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * Fare Estimate DTO
 */
public record FareEstimateDto(
    BigDecimal estimatedDistance,
    Integer estimatedDuration,
    List<VehicleTypeFareDto> vehicleTypeFares
) {
    
    public static record VehicleTypeFareDto(
        String vehicleTypeName,
        String vehicleTypeNameAr,
        BigDecimal baseFare,
        BigDecimal distanceFare,
        BigDecimal timeFare,
        BigDecimal surgeFare,
        BigDecimal totalFare,
        BigDecimal surgeMultiplier,
        String description
    ) {}
}
