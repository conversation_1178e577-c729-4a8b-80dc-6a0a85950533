package com.tecnodrive.notificationservice.repository;

import com.tecnodrive.notificationservice.entity.NotificationTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Notification Template Repository
 * 
 * Data access layer for NotificationTemplate entities
 */
@Repository
public interface NotificationTemplateRepository extends JpaRepository<NotificationTemplate, UUID> {

    /**
     * Find template by name
     */
    Optional<NotificationTemplate> findByTemplateName(String templateName);

    /**
     * Find template by name and tenant
     */
    Optional<NotificationTemplate> findByTemplateNameAndTenantId(String templateName, String tenantId);

    /**
     * Check if template name exists
     */
    boolean existsByTemplateName(String templateName);

    /**
     * Check if template name exists for tenant
     */
    boolean existsByTemplateNameAndTenantId(String templateName, String tenantId);

    /**
     * Find templates by channel
     */
    List<NotificationTemplate> findByChannel(NotificationTemplate.NotificationChannel channel);

    /**
     * Find templates by category
     */
    List<NotificationTemplate> findByCategory(String category);

    /**
     * Find active templates
     */
    List<NotificationTemplate> findByIsActiveTrue();

    /**
     * Find templates by tenant
     */
    List<NotificationTemplate> findByTenantId(String tenantId);

    /**
     * Find active templates by tenant
     */
    List<NotificationTemplate> findByTenantIdAndIsActiveTrue(String tenantId);

    /**
     * Search templates by name or display name
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE " +
           "LOWER(t.templateName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(t.displayName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<NotificationTemplate> searchTemplates(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find templates by priority
     */
    List<NotificationTemplate> findByPriority(NotificationTemplate.NotificationPriority priority);

    /**
     * Count templates by channel
     */
    long countByChannel(NotificationTemplate.NotificationChannel channel);

    /**
     * Count active templates
     */
    long countByIsActiveTrue();

    /**
     * Find templates by channel and tenant
     */
    List<NotificationTemplate> findByChannelAndTenantId(
            NotificationTemplate.NotificationChannel channel, 
            String tenantId
    );
}
