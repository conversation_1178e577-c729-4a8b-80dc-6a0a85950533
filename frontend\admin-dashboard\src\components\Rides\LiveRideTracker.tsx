import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Avatar,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBack,
  Phone,
  Message,
  MyLocation,
  DirectionsCar,
  Person,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { websocketService, RideUpdate, LocationUpdate } from '../../services/websocketService';
import { ridesService } from '../../services/ridesService';

// Google Maps types
declare global {
  interface Window {
    google: any;
    initMap: () => void;
  }
}

interface RideData {
  id: string;
  passengerId: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  driverAvatar?: string;
  vehicleNumber?: string;
  pickupLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  destination: {
    latitude: number;
    longitude: number;
    address: string;
  };
  status: string;
  eta?: number;
}

const LiveRideTracker: React.FC = () => {
  const { rideId } = useParams<{ rideId: string }>();
  const navigate = useNavigate();
  
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const driverMarkerRef = useRef<any>(null);
  const passengerMarkerRef = useRef<any>(null);
  const routePolylineRef = useRef<any>(null);
  
  const [rideData, setRideData] = useState<RideData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [eta, setEta] = useState<number | null>(null);

  // Initialize Google Maps
  const initializeMap = () => {
    if (!mapRef.current || !window.google || !rideData) return;

    const map = new window.google.maps.Map(mapRef.current, {
      center: {
        lat: rideData.pickupLocation.latitude,
        lng: rideData.pickupLocation.longitude,
      },
      zoom: 14,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    });

    mapInstanceRef.current = map;

    // Create markers
    driverMarkerRef.current = new window.google.maps.Marker({
      map,
      position: {
        lat: rideData.pickupLocation.latitude,
        lng: rideData.pickupLocation.longitude,
      },
      icon: {
        url: '/icons/driver-marker.png',
        scaledSize: new window.google.maps.Size(40, 40),
      },
      title: 'السائق',
    });

    passengerMarkerRef.current = new window.google.maps.Marker({
      map,
      position: {
        lat: rideData.pickupLocation.latitude,
        lng: rideData.pickupLocation.longitude,
      },
      icon: {
        url: '/icons/passenger-marker.png',
        scaledSize: new window.google.maps.Size(40, 40),
      },
      title: 'الراكب',
    });

    // Create route polyline
    routePolylineRef.current = new window.google.maps.Polyline({
      map,
      strokeColor: '#FF6600',
      strokeOpacity: 1.0,
      strokeWeight: 4,
    });

    // Draw initial route
    drawRoute();
    setIsMapLoaded(true);
  };

  // Draw route between pickup and destination
  const drawRoute = () => {
    if (!window.google || !mapInstanceRef.current || !rideData) return;

    const directionsService = new window.google.maps.DirectionsService();
    const directionsRenderer = new window.google.maps.DirectionsRenderer({
      map: mapInstanceRef.current,
      suppressMarkers: true,
      polylineOptions: {
        strokeColor: '#FF6600',
        strokeWeight: 4,
      },
    });

    directionsService.route(
      {
        origin: {
          lat: rideData.pickupLocation.latitude,
          lng: rideData.pickupLocation.longitude,
        },
        destination: {
          lat: rideData.destination.latitude,
          lng: rideData.destination.longitude,
        },
        travelMode: window.google.maps.TravelMode.DRIVING,
      },
      (result: any, status: any) => {
        if (status === 'OK') {
          directionsRenderer.setDirections(result);
        }
      }
    );
  };

  // Update driver location on map
  const updateDriverLocation = (location: LocationUpdate) => {
    if (!driverMarkerRef.current) return;

    const newPosition = {
      lat: location.lat,
      lng: location.lng,
    };

    driverMarkerRef.current.setPosition(newPosition);

    // Center map on driver if needed
    if (mapInstanceRef.current) {
      mapInstanceRef.current.panTo(newPosition);
    }
  };

  // Load ride data
  const loadRideData = async () => {
    if (!rideId) return;

    try {
      setLoading(true);
      const response = await ridesService.getRideById(rideId);
      
      if (response.success && response.data) {
        setRideData(response.data as any);
      } else {
        setError('فشل في جلب بيانات الرحلة');
      }
    } catch (err) {
      setError('حدث خطأ في جلب بيانات الرحلة');
      console.error('Error loading ride data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle ride updates from WebSocket
  const handleRideUpdate = (update: RideUpdate) => {
    console.log('Ride update received:', update);
    
    if (update.driverLocation) {
      updateDriverLocation(update.driverLocation);
    }
    
    if (update.eta !== undefined) {
      setEta(update.eta);
    }

    // Update ride status if changed
    if (update.status && rideData) {
      setRideData(prev => prev ? { ...prev, status: update.status } : null);
    }
  };

  // Load Google Maps script
  useEffect(() => {
    const loadGoogleMaps = () => {
      if (window.google) {
        initializeMap();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&libraries=geometry,places`;
      script.async = true;
      script.defer = true;
      script.onload = initializeMap;
      document.head.appendChild(script);
    };

    if (rideData) {
      loadGoogleMaps();
    }
  }, [rideData]);

  // Initialize WebSocket and load data
  useEffect(() => {
    loadRideData();

    return () => {
      if (rideId) {
        websocketService.unsubscribeFromRideUpdates(rideId);
      }
    };
  }, [rideId]);

  // Subscribe to WebSocket updates
  useEffect(() => {
    if (rideId && rideData) {
      websocketService.subscribeToRideUpdates(rideId, handleRideUpdate);
    }

    return () => {
      if (rideId) {
        websocketService.unsubscribeFromRideUpdates(rideId);
      }
    };
  }, [rideId, rideData]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'REQUESTED': return 'info';
      case 'ACCEPTED': return 'primary';
      case 'IN_PROGRESS': return 'warning';
      case 'COMPLETED': return 'success';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'REQUESTED': return 'مطلوبة';
      case 'ACCEPTED': return 'مقبولة';
      case 'IN_PROGRESS': return 'جارية';
      case 'COMPLETED': return 'مكتملة';
      case 'CANCELLED': return 'ملغية';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>جاري تحميل بيانات الرحلة...</Typography>
      </Box>
    );
  }

  if (error || !rideData) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'لم يتم العثور على الرحلة'}</Alert>
        <Button onClick={() => navigate('/rides')} sx={{ mt: 2 }}>
          العودة إلى قائمة الرحلات
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={() => navigate('/rides')}>
                <ArrowBack />
              </IconButton>
              <Typography variant="h6">
                تتبع الرحلة #{rideId?.slice(-6)}
              </Typography>
              <Chip
                label={getStatusLabel(rideData.status)}
                color={getStatusColor(rideData.status) as any}
                variant="outlined"
              />
            </Box>
            {eta && (
              <Typography variant="h6" color="primary">
                الوصول خلال {eta} دقيقة
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Map */}
      <Card sx={{ flexGrow: 1, mb: 2 }}>
        <Box
          ref={mapRef}
          sx={{
            width: '100%',
            height: '100%',
            minHeight: 400,
            borderRadius: 1,
          }}
        />
        {!isMapLoaded && (
          <Box sx={{ 
            position: 'absolute', 
            top: '50%', 
            left: '50%', 
            transform: 'translate(-50%, -50%)',
            display: 'flex',
            alignItems: 'center',
            gap: 2
          }}>
            <CircularProgress />
            <Typography>جاري تحميل الخريطة...</Typography>
          </Box>
        )}
      </Card>

      {/* Driver Info */}
      {rideData.driverId && (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar src={rideData.driverAvatar}>
                  <DirectionsCar />
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {rideData.driverName || 'السائق'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    مركبة: {rideData.vehicleNumber || 'غير محدد'}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {rideData.driverPhone && (
                  <IconButton color="primary" href={`tel:${rideData.driverPhone}`}>
                    <Phone />
                  </IconButton>
                )}
                <IconButton color="primary">
                  <Message />
                </IconButton>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default LiveRideTracker;
