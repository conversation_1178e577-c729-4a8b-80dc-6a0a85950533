import axios from 'axios';
import { io, Socket } from 'socket.io-client';

// Types for CRM
export interface Customer {
  id: string;
  tenantId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  segment: 'regular' | 'premium' | 'vip' | 'enterprise';
  status: 'active' | 'inactive' | 'suspended';
  registrationDate: string;
  lastActivity: string;
  totalTrips: number;
  totalSpent: number;
  averageRating: number;
  preferences: CustomerPreferences;
  tags: string[];
  notes: string;
}

export interface CustomerPreferences {
  preferredVehicleType: string[];
  preferredPaymentMethod: string;
  communicationChannel: 'email' | 'sms' | 'push' | 'all';
  language: string;
  timezone: string;
  notifications: {
    tripUpdates: boolean;
    promotions: boolean;
    newsletters: boolean;
  };
}

export interface SupportTicket {
  id: string;
  tenantId: string;
  customerId: string;
  title: string;
  description: string;
  category: 'technical' | 'billing' | 'complaint' | 'inquiry' | 'feedback';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_customer' | 'resolved' | 'closed';
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  sentimentScore: number;
  aiSuggestions: string[];
  attachments: string[];
  interactions: TicketInteraction[];
}

export interface TicketInteraction {
  id: string;
  type: 'comment' | 'status_change' | 'assignment' | 'escalation';
  content: string;
  author: string;
  timestamp: string;
  isInternal: boolean;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomersThisMonth: number;
  customersBySegment: Record<string, number>;
  customerSatisfactionScore: number;
  churnRate: number;
  lifetimeValue: number;
  supportTicketMetrics: {
    totalTickets: number;
    averageResolutionTime: number;
    satisfactionScore: number;
    ticketsByCategory: Record<string, number>;
  };
}

export interface CustomerJourney {
  customerId: string;
  touchpoints: Array<{
    timestamp: string;
    type: 'registration' | 'trip' | 'support' | 'payment' | 'feedback';
    description: string;
    sentiment?: number;
  }>;
  milestones: Array<{
    name: string;
    achievedAt: string;
    value?: number;
  }>;
}

class CRMService {
  private socket: Socket | null = null;
  private baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    this.socket = io(`${this.baseURL}/crm`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to CRM WebSocket');
    });
  }

  // Real-time subscriptions
  subscribeToTicketUpdates(tenantId: string, callback: (ticket: SupportTicket) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-tickets', { tenantId });
      this.socket.on('ticket-updated', callback);
    }
  }

  subscribeToNewCustomers(tenantId: string, callback: (customer: Customer) => void) {
    if (this.socket) {
      this.socket.emit('subscribe-customers', { tenantId });
      this.socket.on('new-customer', callback);
    }
  }

  // Customer Management
  async getCustomers(tenantId: string, filters?: {
    segment?: string;
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ customers: Customer[]; total: number }> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/customers`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  async getCustomer(customerId: string): Promise<Customer> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/customers/${customerId}`, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw error;
    }
  }

  async updateCustomer(customerId: string, updates: Partial<Customer>): Promise<Customer> {
    try {
      const response = await axios.put(`${this.baseURL}/api/crm/customers/${customerId}`, updates, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  async getCustomerJourney(customerId: string): Promise<CustomerJourney> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/customers/${customerId}/journey`, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer journey:', error);
      throw error;
    }
  }

  // Support Ticket Management
  async getSupportTickets(tenantId: string, filters?: {
    status?: string;
    priority?: string;
    category?: string;
    assignedTo?: string;
    customerId?: string;
  }): Promise<SupportTicket[]> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/support-tickets`, {
        params: { tenantId, ...filters },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching support tickets:', error);
      throw error;
    }
  }

  async createSupportTicket(ticket: Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt' | 'interactions'>): Promise<SupportTicket> {
    try {
      const response = await axios.post(`${this.baseURL}/api/crm/support-tickets`, ticket, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error creating support ticket:', error);
      throw error;
    }
  }

  async updateSupportTicket(ticketId: string, updates: Partial<SupportTicket>): Promise<SupportTicket> {
    try {
      const response = await axios.put(`${this.baseURL}/api/crm/support-tickets/${ticketId}`, updates, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error updating support ticket:', error);
      throw error;
    }
  }

  async addTicketInteraction(ticketId: string, interaction: Omit<TicketInteraction, 'id' | 'timestamp'>): Promise<TicketInteraction> {
    try {
      const response = await axios.post(
        `${this.baseURL}/api/crm/support-tickets/${ticketId}/interactions`,
        interaction,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error adding ticket interaction:', error);
      throw error;
    }
  }

  // AI-powered features
  async analyzeSentiment(text: string): Promise<{ score: number; label: string; confidence: number }> {
    try {
      const response = await axios.post(`${this.baseURL}/api/crm/analyze-sentiment`, { text }, {
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error analyzing sentiment:', error);
      throw error;
    }
  }

  async generateAIResponse(ticketId: string, context: string): Promise<string[]> {
    try {
      const response = await axios.post(`${this.baseURL}/api/crm/generate-response`, {
        ticketId,
        context,
      }, {
        headers: this.getAuthHeaders(),
      });
      return response.data.suggestions;
    } catch (error) {
      console.error('Error generating AI response:', error);
      throw error;
    }
  }

  // Analytics
  async getCustomerAnalytics(tenantId: string, timeframe: string = '30d'): Promise<CustomerAnalytics> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/analytics`, {
        params: { tenantId, timeframe },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer analytics:', error);
      throw error;
    }
  }

  async getCustomerSegmentation(tenantId: string): Promise<Array<{ segment: string; count: number; characteristics: string[] }>> {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/segmentation`, {
        params: { tenantId },
        headers: this.getAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer segmentation:', error);
      throw error;
    }
  }

  private getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

export const crmService = new CRMService();
export default crmService;
