import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Alert
} from '@mui/material';
import {
  Map as MapIcon
} from '@mui/icons-material';
import SafeMapComponent from '../components/Maps/SafeMapComponent';

const SimpleMapPage: React.FC = () => {
  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          <MapIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          خريطة OpenStreetMap بسيطة
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          خريطة تفاعلية بسيطة مع تتبع المركبات
        </Typography>
      </Box>

      {/* Instructions */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>تعليمات الاستخدام:</strong><br />
          • استخدم عجلة الماوس للتكبير والتصغير<br />
          • اسحب الخريطة للتنقل<br />
          • اضغط على المركبات لرؤية التفاصيل<br />
          • غير مزود الخريطة من القائمة المنسدلة
        </Typography>
      </Alert>

      {/* Map */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          خريطة الرياض - المملكة العربية السعودية
        </Typography>
        
        <SafeMapComponent height="600px" />
      </Paper>
    </Container>
  );
};

export default SimpleMapPage;
