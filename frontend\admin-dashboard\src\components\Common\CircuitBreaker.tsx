import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Box, Button, Typography, LinearProgress } from '@mui/material';
import { Error, Refresh, Warning } from '@mui/icons-material';

interface CircuitBreakerProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
  maxFailures?: number;
  resetTimeout?: number;
  retryDelay?: number;
}

type CircuitState = 'closed' | 'open' | 'half-open';

const CircuitBreaker: React.FC<CircuitBreakerProps> = ({
  children,
  fallback,
  onError,
  maxFailures = 5,
  resetTimeout = 60000, // 1 minute
  retryDelay = 5000, // 5 seconds
}) => {
  const [state, setState] = useState<CircuitState>('closed');
  const [failureCount, setFailureCount] = useState(0);
  const [lastFailureTime, setLastFailureTime] = useState<number>(0);
  const [nextRetryTime, setNextRetryTime] = useState<number>(0);
  const [error, setError] = useState<Error | null>(null);

  const recordSuccess = useCallback(() => {
    setFailureCount(0);
    setState('closed');
    setError(null);
  }, []);

  const recordFailure = useCallback((err: Error) => {
    const newFailureCount = failureCount + 1;
    setFailureCount(newFailureCount);
    setLastFailureTime(Date.now());
    setError(err);
    
    if (onError) {
      onError(err);
    }

    if (newFailureCount >= maxFailures) {
      setState('open');
      setNextRetryTime(Date.now() + resetTimeout);
    }
  }, [failureCount, maxFailures, resetTimeout, onError]);

  const attemptReset = useCallback(() => {
    if (state === 'open' && Date.now() >= nextRetryTime) {
      setState('half-open');
      setNextRetryTime(Date.now() + retryDelay);
    }
  }, [state, nextRetryTime, retryDelay]);

  const manualReset = useCallback(() => {
    setState('closed');
    setFailureCount(0);
    setError(null);
    setNextRetryTime(0);
  }, []);

  useEffect(() => {
    if (state === 'open') {
      const timer = setInterval(() => {
        if (Date.now() >= nextRetryTime) {
          attemptReset();
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [state, nextRetryTime, attemptReset]);

  // Error boundary functionality
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      recordFailure(new Error(event.message));
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      recordFailure(new Error(event.reason));
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [recordFailure]);

  const getTimeUntilRetry = () => {
    const timeLeft = nextRetryTime - Date.now();
    return Math.max(0, Math.ceil(timeLeft / 1000));
  };

  const getStateColor = () => {
    switch (state) {
      case 'closed': return 'success';
      case 'half-open': return 'warning';
      case 'open': return 'error';
      default: return 'info';
    }
  };

  const getStateIcon = () => {
    switch (state) {
      case 'closed': return null;
      case 'half-open': return <Warning />;
      case 'open': return <Error />;
      default: return null;
    }
  };

  if (state === 'open') {
    const timeUntilRetry = getTimeUntilRetry();
    
    return (
      <Box>
        <Alert 
          severity="error" 
          icon={<Error />}
          action={
            <Button 
              color="inherit" 
              size="small" 
              onClick={manualReset}
              startIcon={<Refresh />}
            >
              Reset
            </Button>
          }
        >
          <Typography variant="h6" gutterBottom>
            Service Temporarily Unavailable
          </Typography>
          <Typography variant="body2" gutterBottom>
            The circuit breaker is open due to repeated failures. 
            {timeUntilRetry > 0 && ` Automatic retry in ${timeUntilRetry} seconds.`}
          </Typography>
          {error && (
            <Typography variant="caption" color="textSecondary">
              Last error: {error.message}
            </Typography>
          )}
          {timeUntilRetry > 0 && (
            <Box mt={2}>
              <LinearProgress 
                variant="determinate" 
                value={((resetTimeout - (nextRetryTime - Date.now())) / resetTimeout) * 100}
                color="error"
              />
            </Box>
          )}
        </Alert>
        
        {fallback && (
          <Box mt={2}>
            {fallback}
          </Box>
        )}
      </Box>
    );
  }

  if (state === 'half-open') {
    return (
      <Box>
        <Alert severity="warning" icon={<Warning />}>
          <Typography variant="body2">
            Service is recovering. Monitoring for stability...
          </Typography>
          <Typography variant="caption" color="textSecondary">
            Failure count: {failureCount}/{maxFailures}
          </Typography>
        </Alert>
        <Box mt={2}>
          {children}
        </Box>
      </Box>
    );
  }

  // State is 'closed' - normal operation
  return (
    <Box>
      {children}
    </Box>
  );
};

export default CircuitBreaker;
