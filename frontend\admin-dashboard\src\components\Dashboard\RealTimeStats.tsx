import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Refresh,
  Timeline,
  Speed,
  People,
  DirectionsCar,
  AttachMoney,
  LocalShipping,
  Star,
} from '@mui/icons-material';

interface RealTimeStat {
  id: string;
  label: string;
  value: number;
  previousValue: number;
  unit: string;
  icon: React.ReactNode;
  color: string;
  target?: number;
}

const RealTimeStats: React.FC = () => {
  const [stats, setStats] = useState<RealTimeStat[]>([
    {
      id: 'active_rides',
      label: 'الرحلات النشطة',
      value: 45,
      previousValue: 42,
      unit: 'رحلة',
      icon: <DirectionsCar />,
      color: '#667eea',
      target: 60,
    },
    {
      id: 'online_drivers',
      label: 'السائقون المتصلون',
      value: 128,
      previousValue: 125,
      unit: 'سائق',
      icon: <People />,
      color: '#48bb78',
      target: 150,
    },
    {
      id: 'hourly_revenue',
      label: 'الإيرادات/الساعة',
      value: 2450,
      previousValue: 2380,
      unit: 'ريال',
      icon: <AttachMoney />,
      color: '#4299e1',
      target: 3000,
    },
    {
      id: 'delivery_rate',
      label: 'معدل التسليم',
      value: 94.5,
      previousValue: 93.2,
      unit: '%',
      icon: <LocalShipping />,
      color: '#ed8936',
      target: 95,
    },
    {
      id: 'avg_rating',
      label: 'متوسط التقييم',
      value: 4.7,
      previousValue: 4.6,
      unit: '⭐',
      icon: <Star />,
      color: '#9f7aea',
      target: 5,
    },
    {
      id: 'response_time',
      label: 'زمن الاستجابة',
      value: 2.3,
      previousValue: 2.8,
      unit: 'دقيقة',
      icon: <Speed />,
      color: '#f56565',
      target: 2,
    },
  ]);

  const [lastUpdate, setLastUpdate] = useState(new Date());

  const updateStats = () => {
    setStats(prevStats => 
      prevStats.map(stat => ({
        ...stat,
        previousValue: stat.value,
        value: stat.value + (Math.random() - 0.5) * (stat.value * 0.1),
      }))
    );
    setLastUpdate(new Date());
  };

  useEffect(() => {
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const getChangePercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const getProgressPercentage = (value: number, target?: number) => {
    if (!target) return 0;
    return Math.min((value / target) * 100, 100);
  };

  return (
    <Card className="enhanced-card" sx={{ mb: 3 }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                mr: 2,
              }}
            >
              <Timeline />
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>
                إحصائيات الوقت الفعلي
              </Typography>
              <Typography variant="body2" color="text.secondary">
                آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
          <Tooltip title="تحديث البيانات">
            <IconButton 
              onClick={updateStats}
              className="interactive-hover"
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8, #6a4190)',
                },
              }}
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        <Grid container spacing={3}>
          {stats.map((stat, index) => {
            const changePercentage = getChangePercentage(stat.value, stat.previousValue);
            const isPositive = changePercentage > 0;
            const progressPercentage = getProgressPercentage(stat.value, stat.target);

            return (
              <Grid item xs={12} sm={6} md={4} key={stat.id}>
                <Box
                  className="scale-in interactive-hover"
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    overflow: 'hidden',
                    animationDelay: `${index * 0.1}s`,
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      background: `linear-gradient(90deg, ${stat.color}, ${stat.color}88)`,
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Avatar
                      sx={{
                        width: 48,
                        height: 48,
                        background: `${stat.color}20`,
                        color: stat.color,
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                    <Chip
                      icon={isPositive ? <TrendingUp /> : <TrendingDown />}
                      label={`${isPositive ? '+' : ''}${changePercentage.toFixed(1)}%`}
                      size="small"
                      sx={{
                        background: isPositive 
                          ? 'linear-gradient(135deg, #48bb78, #38a169)' 
                          : 'linear-gradient(135deg, #f56565, #e53e3e)',
                        color: 'white',
                        fontWeight: 600,
                        '& .MuiChip-icon': {
                          color: 'white',
                        },
                      }}
                    />
                  </Box>

                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      mb: 1,
                      background: `linear-gradient(135deg, ${stat.color}, ${stat.color}cc)`,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    {stat.value.toFixed(stat.unit === '%' || stat.unit === '⭐' ? 1 : 0)}
                    <Typography component="span" variant="h6" sx={{ color: 'text.secondary', mr: 1 }}>
                      {stat.unit}
                    </Typography>
                  </Typography>

                  <Typography variant="body1" sx={{ fontWeight: 600, mb: 2 }}>
                    {stat.label}
                  </Typography>

                  {stat.target && (
                    <>
                      <Divider sx={{ my: 1.5 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          الهدف
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {stat.target} {stat.unit}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={progressPercentage}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: `${stat.color}20`,
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 3,
                            background: `linear-gradient(90deg, ${stat.color}, ${stat.color}cc)`,
                          },
                        }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                        {progressPercentage.toFixed(1)}% من الهدف
                      </Typography>
                    </>
                  )}
                </Box>
              </Grid>
            );
          })}
        </Grid>

        {/* Overall Performance Indicator */}
        <Box sx={{ mt: 3, p: 2, borderRadius: 2, background: 'rgba(72, 187, 120, 0.1)' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ background: '#48bb78', mr: 2 }}>
                📈
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 700, color: '#48bb78' }}>
                  الأداء العام ممتاز
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  جميع المؤشرات تظهر نمواً إيجابياً
                </Typography>
              </Box>
            </Box>
            <Chip
              label="نشط"
              sx={{
                background: '#48bb78',
                color: 'white',
                fontWeight: 600,
                animation: 'pulse 2s infinite',
              }}
            />
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RealTimeStats;
