import {
  VehicleDto,
  LocationDto,
  MaintenanceTaskDto,
  FleetAnalyticsDto,
  CreateVehicleRequest,
  UpdateVehicleRequest,
  DriverDto,
  MaintenanceRecordDto,
  ApiResponse
} from '../types/api';
import { createMockResponse, simulateApiDelay, generateMockId } from '../services/api';

// Mock Vehicles Data
const mockVehicles: VehicleDto[] = [
  {
    id: '1',
    plateNumber: 'YE-001',
    make: 'Toyota',
    model: 'Corolla',
    year: 2020,
    color: 'أبيض',
    capacity: 4,
    status: 'ACTIVE',
    vin: '1HGCM82633A004352',
    fuelType: 'GASOLINE',
    mileage: 45000,
    lastServiceDate: '2025-06-15T00:00:00Z',
    nextServiceDate: '2025-09-15T00:00:00Z',
    currentLocation: {
      id: 'loc-1',
      vehicleId: '1',
      latitude: 15.3694,
      longitude: 44.1910,
      address: 'صنعاء، اليمن',
      speed: 35,
      heading: 90,
      timestamp: '2025-07-09T14:30:00Z',
    },
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2025-07-09T14:30:00Z',
  },
  {
    id: '2',
    plateNumber: 'YE-002',
    make: 'Hyundai',
    model: 'Accent',
    year: 2019,
    color: 'أزرق',
    capacity: 4,
    status: 'MAINTENANCE',
    vin: '2HGCM82633A004353',
    fuelType: 'GASOLINE',
    mileage: 52000,
    lastServiceDate: '2025-07-01T00:00:00Z',
    nextServiceDate: '2025-10-01T00:00:00Z',
    currentLocation: {
      id: 'loc-2',
      vehicleId: '2',
      latitude: 15.3704,
      longitude: 44.1920,
      address: 'ورشة الصيانة، صنعاء',
      speed: 0,
      heading: 0,
      timestamp: '2025-07-09T10:00:00Z',
    },
    createdAt: '2024-02-20T09:15:00Z',
    updatedAt: '2025-07-09T10:00:00Z',
  },
  {
    id: '3',
    plateNumber: 'YE-003',
    make: 'Nissan',
    model: 'Sunny',
    year: 2021,
    color: 'أحمر',
    capacity: 4,
    status: 'ACTIVE',
    vin: '3HGCM82633A004354',
    fuelType: 'GASOLINE',
    mileage: 28000,
    lastServiceDate: '2025-05-20T00:00:00Z',
    nextServiceDate: '2025-08-20T00:00:00Z',
    currentLocation: {
      id: 'loc-3',
      vehicleId: '3',
      latitude: 15.3714,
      longitude: 44.1930,
      address: 'شارع الزبيري، صنعاء',
      speed: 45,
      heading: 180,
      timestamp: '2025-07-09T15:00:00Z',
    },
    createdAt: '2024-03-10T11:30:00Z',
    updatedAt: '2025-07-09T15:00:00Z',
  },
  {
    id: '4',
    plateNumber: 'YE-004',
    make: 'Kia',
    model: 'Cerato',
    year: 2022,
    color: 'أسود',
    capacity: 4,
    status: 'OFFLINE',
    vin: '4HGCM82633A004355',
    fuelType: 'GASOLINE',
    mileage: 15000,
    lastServiceDate: '2025-04-10T00:00:00Z',
    nextServiceDate: '2025-07-10T00:00:00Z',
    currentLocation: {
      id: 'loc-4',
      vehicleId: '4',
      latitude: 15.3724,
      longitude: 44.1940,
      address: 'موقف السيارات، صنعاء',
      speed: 0,
      heading: 0,
      timestamp: '2025-07-08T18:00:00Z',
    },
    createdAt: '2024-04-05T14:20:00Z',
    updatedAt: '2025-07-08T18:00:00Z',
  },
  {
    id: '5',
    plateNumber: 'YE-005',
    make: 'Toyota',
    model: 'Camry',
    year: 2021,
    color: 'فضي',
    capacity: 4,
    status: 'ACTIVE',
    vin: '5HGCM82633A004356',
    fuelType: 'HYBRID',
    mileage: 32000,
    lastServiceDate: '2025-06-01T00:00:00Z',
    nextServiceDate: '2025-09-01T00:00:00Z',
    currentLocation: {
      id: 'loc-5',
      vehicleId: '5',
      latitude: 15.3734,
      longitude: 44.1950,
      address: 'شارع الستين، صنعاء',
      speed: 25,
      heading: 270,
      timestamp: '2025-07-09T16:00:00Z',
    },
    createdAt: '2024-05-15T10:45:00Z',
    updatedAt: '2025-07-09T16:00:00Z',
  },
];

// Mock Maintenance Tasks Data
const mockMaintenanceTasks: MaintenanceTaskDto[] = [
  {
    id: 'maint-1',
    vehicleId: '1',
    taskType: 'OIL_CHANGE',
    description: 'تغيير زيت المحرك والفلتر',
    status: 'COMPLETED',
    priority: 'MEDIUM',
    scheduledDate: '2025-06-15T09:00:00Z',
    completedDate: '2025-06-15T10:30:00Z',
    cost: 3000,
    mileageAtService: 44500,
    notes: 'تم تغيير الزيت بنجاح',
    assignedTo: 'أحمد الميكانيكي',
    createdAt: '2025-06-10T08:00:00Z',
    updatedAt: '2025-06-15T10:30:00Z',
  },
  {
    id: 'maint-2',
    vehicleId: '2',
    taskType: 'BRAKE_CHECK',
    description: 'فحص وصيانة نظام الفرامل',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    scheduledDate: '2025-07-09T08:00:00Z',
    cost: 5000,
    mileageAtService: 52000,
    notes: 'يحتاج تغيير أقراص الفرامل',
    assignedTo: 'محمد الفني',
    createdAt: '2025-07-05T10:00:00Z',
    updatedAt: '2025-07-09T08:00:00Z',
  },
  {
    id: 'maint-3',
    vehicleId: '3',
    taskType: 'GENERAL_INSPECTION',
    description: 'فحص دوري شامل',
    status: 'SCHEDULED',
    priority: 'LOW',
    scheduledDate: '2025-08-20T10:00:00Z',
    assignedTo: 'علي الفني',
    createdAt: '2025-07-01T12:00:00Z',
    updatedAt: '2025-07-01T12:00:00Z',
  },
  {
    id: 'maint-4',
    vehicleId: '4',
    taskType: 'TIRE_ROTATION',
    description: 'تدوير الإطارات وفحص الضغط',
    status: 'SCHEDULED',
    priority: 'MEDIUM',
    scheduledDate: '2025-07-15T14:00:00Z',
    assignedTo: 'سالم الفني',
    createdAt: '2025-07-08T09:00:00Z',
    updatedAt: '2025-07-08T09:00:00Z',
  },
];

// Mock Location History Data
const mockLocationHistory: { [vehicleId: string]: LocationDto[] } = {
  '1': [
    {
      id: 'loc-1-1',
      vehicleId: '1',
      latitude: 15.3694,
      longitude: 44.1910,
      address: 'صنعاء، اليمن',
      speed: 35,
      heading: 90,
      timestamp: '2025-07-09T14:30:00Z',
    },
    {
      id: 'loc-1-2',
      vehicleId: '1',
      latitude: 15.3684,
      longitude: 44.1900,
      address: 'شارع الجمهورية، صنعاء',
      speed: 40,
      heading: 85,
      timestamp: '2025-07-09T14:25:00Z',
    },
    {
      id: 'loc-1-3',
      vehicleId: '1',
      latitude: 15.3674,
      longitude: 44.1890,
      address: 'ميدان التحرير، صنعاء',
      speed: 30,
      heading: 80,
      timestamp: '2025-07-09T14:20:00Z',
    },
  ],
};

// Mock Fleet Analytics Data
const mockFleetAnalytics: FleetAnalyticsDto = {
  totalVehicles: 5,
  activeVehicles: 3,
  maintenanceVehicles: 1,
  offlineVehicles: 1,
  totalMileage: 172000,
  averageMileage: 34400,
  maintenanceCosts: 8000,
  fuelConsumption: 2500,
  utilizationRate: 75,
  period: 'last_30_days',
};

// Mock Fleet Data Service
export const mockFleetData = {
  // Vehicle Management
  async getVehicles(params?: {
    search?: string;
    status?: string;
    make?: string;
    model?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<VehicleDto[]>> {
    await simulateApiDelay();
    
    let filteredVehicles = [...mockVehicles];
    
    // Apply search filter
    if (params?.search) {
      const searchTerm = params.search.toLowerCase();
      filteredVehicles = filteredVehicles.filter(vehicle =>
        vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
        vehicle.make.toLowerCase().includes(searchTerm) ||
        vehicle.model.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply status filter
    if (params?.status && params.status !== 'ALL') {
      filteredVehicles = filteredVehicles.filter(vehicle => vehicle.status === params.status);
    }
    
    // Apply make filter
    if (params?.make && params.make !== 'ALL') {
      filteredVehicles = filteredVehicles.filter(vehicle => vehicle.make === params.make);
    }
    
    // Apply model filter
    if (params?.model && params.model !== 'ALL') {
      filteredVehicles = filteredVehicles.filter(vehicle => vehicle.model === params.model);
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVehicles = filteredVehicles.slice(startIndex, endIndex);
    
    return createMockResponse(paginatedVehicles);
  },

  async getVehicleById(vehicleId: string): Promise<ApiResponse<VehicleDto>> {
    await simulateApiDelay();
    const vehicle = mockVehicles.find(v => v.id === vehicleId);
    
    if (!vehicle) {
      throw new Error('المركبة غير موجودة');
    }
    
    return createMockResponse(vehicle);
  },

  async createVehicle(vehicleData: CreateVehicleRequest): Promise<ApiResponse<VehicleDto>> {
    await simulateApiDelay();
    
    const newVehicle: VehicleDto = {
      id: generateMockId(),
      ...vehicleData,
      status: vehicleData.status || 'ACTIVE',
      mileage: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockVehicles.push(newVehicle);
    return createMockResponse(newVehicle);
  },

  async updateVehicle(vehicleId: string, vehicleData: UpdateVehicleRequest): Promise<ApiResponse<VehicleDto>> {
    await simulateApiDelay();
    
    const vehicleIndex = mockVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) {
      throw new Error('المركبة غير موجودة');
    }
    
    const updatedVehicle = {
      ...mockVehicles[vehicleIndex],
      ...vehicleData,
      updatedAt: new Date().toISOString(),
    };
    
    mockVehicles[vehicleIndex] = updatedVehicle;
    return createMockResponse(updatedVehicle);
  },

  async deleteVehicle(vehicleId: string): Promise<ApiResponse<void>> {
    await simulateApiDelay();
    
    const vehicleIndex = mockVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) {
      throw new Error('المركبة غير موجودة');
    }
    
    mockVehicles.splice(vehicleIndex, 1);
    return createMockResponse(undefined);
  },

  // Location Tracking
  async getVehicleLocation(vehicleId: string, params?: {
    from?: string;
    to?: string;
  }): Promise<ApiResponse<LocationDto[]>> {
    await simulateApiDelay();
    
    const locationHistory = mockLocationHistory[vehicleId] || [];
    let filteredLocations = [...locationHistory];
    
    // Apply date filters if provided
    if (params?.from) {
      const fromDate = new Date(params.from);
      filteredLocations = filteredLocations.filter(loc => new Date(loc.timestamp) >= fromDate);
    }
    
    if (params?.to) {
      const toDate = new Date(params.to);
      filteredLocations = filteredLocations.filter(loc => new Date(loc.timestamp) <= toDate);
    }
    
    return createMockResponse(filteredLocations);
  },

  // Maintenance Management
  async getMaintenanceTasks(vehicleId: string): Promise<ApiResponse<MaintenanceTaskDto[]>> {
    await simulateApiDelay();
    
    const vehicleTasks = mockMaintenanceTasks.filter(task => task.vehicleId === vehicleId);
    return createMockResponse(vehicleTasks);
  },

  async createMaintenanceTask(taskData: CreateMaintenanceTaskRequest): Promise<ApiResponse<MaintenanceTaskDto>> {
    await simulateApiDelay();
    
    const newTask: MaintenanceTaskDto = {
      id: generateMockId(),
      ...taskData,
      status: 'SCHEDULED',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockMaintenanceTasks.push(newTask);
    return createMockResponse(newTask);
  },

  async updateMaintenanceTask(taskId: string, taskData: Partial<MaintenanceTaskDto>): Promise<ApiResponse<MaintenanceTaskDto>> {
    await simulateApiDelay();
    
    const taskIndex = mockMaintenanceTasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) {
      throw new Error('مهمة الصيانة غير موجودة');
    }
    
    const updatedTask = {
      ...mockMaintenanceTasks[taskIndex],
      ...taskData,
      updatedAt: new Date().toISOString(),
    };
    
    mockMaintenanceTasks[taskIndex] = updatedTask;
    return createMockResponse(updatedTask);
  },

  // Fleet Analytics
  async getFleetAnalytics(params?: {
    from?: string;
    to?: string;
    type?: 'usage' | 'maintenance' | 'fuel';
  }): Promise<ApiResponse<FleetAnalyticsDto>> {
    await simulateApiDelay();
    
    // In a real implementation, this would calculate analytics based on the date range and type
    const analytics = {
      ...mockFleetAnalytics,
      period: params?.from && params?.to ? `${params.from}_to_${params.to}` : 'last_30_days',
    };
    
    return createMockResponse(analytics);
  },
};
