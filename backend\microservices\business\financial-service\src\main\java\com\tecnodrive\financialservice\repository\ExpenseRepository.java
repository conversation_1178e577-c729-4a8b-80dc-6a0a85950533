package com.tecnodrive.financialservice.repository;

import com.tecnodrive.financialservice.entity.Expense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Expense Repository
 * 
 * Data access layer for Expense entities
 */
@Repository
public interface ExpenseRepository extends JpaRepository<Expense, UUID> {

    /**
     * Find expenses by company
     */
    List<Expense> findByCompanyId(String companyId);

    /**
     * Find expenses by company with pagination
     */
    Page<Expense> findByCompanyId(String companyId, Pageable pageable);

    /**
     * Find expenses by category
     */
    List<Expense> findByExpenseCategory(Expense.ExpenseCategory expenseCategory);

    /**
     * Find expenses by company and category
     */
    List<Expense> findByCompanyIdAndExpenseCategory(String companyId, Expense.ExpenseCategory expenseCategory);

    /**
     * Find expenses by status
     */
    List<Expense> findByStatus(Expense.ExpenseStatus status);

    /**
     * Find expenses by company and status
     */
    List<Expense> findByCompanyIdAndStatus(String companyId, Expense.ExpenseStatus status);

    /**
     * Find expenses by employee
     */
    List<Expense> findByIncurredByEmployeeId(String employeeId);

    /**
     * Find expenses by employee with pagination
     */
    Page<Expense> findByIncurredByEmployeeId(String employeeId, Pageable pageable);

    /**
     * Find expenses by budget
     */
    List<Expense> findByBudgetId(UUID budgetId);

    /**
     * Find expenses by date range
     */
    @Query("SELECT e FROM Expense e WHERE e.expenseDate BETWEEN :startDate AND :endDate")
    List<Expense> findExpensesByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find expenses by company and date range
     */
    @Query("SELECT e FROM Expense e WHERE e.companyId = :companyId AND e.expenseDate BETWEEN :startDate AND :endDate")
    List<Expense> findByCompanyIdAndDateRange(
            @Param("companyId") String companyId,
            @Param("startDate") LocalDate startDate, 
            @Param("endDate") LocalDate endDate
    );

    /**
     * Find pending approval expenses
     */
    @Query("SELECT e FROM Expense e WHERE e.status = 'PENDING'")
    List<Expense> findPendingApprovalExpenses();

    /**
     * Find pending approval expenses by company
     */
    @Query("SELECT e FROM Expense e WHERE e.companyId = :companyId AND e.status = 'PENDING'")
    List<Expense> findPendingApprovalExpensesByCompany(@Param("companyId") String companyId);

    /**
     * Find tax deductible expenses
     */
    @Query("SELECT e FROM Expense e WHERE e.taxDeductible = true AND e.status = 'APPROVED'")
    List<Expense> findTaxDeductibleExpenses();

    /**
     * Find tax deductible expenses by company
     */
    @Query("SELECT e FROM Expense e WHERE e.companyId = :companyId AND e.taxDeductible = true AND e.status = 'APPROVED'")
    List<Expense> findTaxDeductibleExpensesByCompany(@Param("companyId") String companyId);

    /**
     * Find reimbursable expenses
     */
    @Query("SELECT e FROM Expense e WHERE e.reimbursementStatus IN ('PENDING', 'APPROVED')")
    List<Expense> findReimbursableExpenses();

    /**
     * Find reimbursable expenses by employee
     */
    @Query("SELECT e FROM Expense e WHERE e.incurredByEmployeeId = :employeeId AND e.reimbursementStatus IN ('PENDING', 'APPROVED')")
    List<Expense> findReimbursableExpensesByEmployee(@Param("employeeId") String employeeId);

    /**
     * Calculate total expenses by company
     */
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.status = 'APPROVED'")
    BigDecimal calculateTotalExpenses(@Param("companyId") String companyId);

    /**
     * Calculate total expenses by category
     */
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.expenseCategory = :category AND e.status = 'APPROVED'")
    BigDecimal calculateTotalExpensesByCategory(@Param("companyId") String companyId, @Param("category") Expense.ExpenseCategory category);

    /**
     * Calculate expenses for period
     */
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.expenseDate BETWEEN :startDate AND :endDate AND e.status = 'APPROVED'")
    BigDecimal calculateExpensesForPeriod(
            @Param("companyId") String companyId,
            @Param("startDate") LocalDate startDate, 
            @Param("endDate") LocalDate endDate
    );

    /**
     * Calculate expenses by budget
     */
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.budgetId = :budgetId AND e.status = 'APPROVED'")
    BigDecimal calculateExpensesByBudget(@Param("budgetId") UUID budgetId);

    /**
     * Calculate total reimbursement amount
     */
    @Query("SELECT COALESCE(SUM(e.reimbursementAmount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.reimbursementStatus = 'APPROVED'")
    BigDecimal calculateTotalReimbursementAmount(@Param("companyId") String companyId);

    /**
     * Get expense statistics by category
     */
    @Query("SELECT e.expenseCategory, COUNT(e), COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.status = 'APPROVED' GROUP BY e.expenseCategory")
    List<Object[]> getExpenseStatisticsByCategory(@Param("companyId") String companyId);

    /**
     * Get monthly expense summary
     */
    @Query("SELECT YEAR(e.expenseDate), MONTH(e.expenseDate), COUNT(e), COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.status = 'APPROVED' GROUP BY YEAR(e.expenseDate), MONTH(e.expenseDate) ORDER BY YEAR(e.expenseDate), MONTH(e.expenseDate)")
    List<Object[]> getMonthlyExpenseSummary(@Param("companyId") String companyId);

    /**
     * Get expense statistics by employee
     */
    @Query("SELECT e.incurredByEmployeeId, COUNT(e), COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.companyId = :companyId AND e.status = 'APPROVED' GROUP BY e.incurredByEmployeeId")
    List<Object[]> getExpenseStatisticsByEmployee(@Param("companyId") String companyId);

    /**
     * Find expenses by vendor
     */
    List<Expense> findByVendorContainingIgnoreCase(String vendor);

    /**
     * Find expenses by receipt number
     */
    List<Expense> findByReceiptNumber(String receiptNumber);

    /**
     * Count expenses by status
     */
    long countByStatus(Expense.ExpenseStatus status);

    /**
     * Count expenses by company and status
     */
    long countByCompanyIdAndStatus(String companyId, Expense.ExpenseStatus status);

    /**
     * Count expenses by category
     */
    long countByExpenseCategory(Expense.ExpenseCategory expenseCategory);
}
