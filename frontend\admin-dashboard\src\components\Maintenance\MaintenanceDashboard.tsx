import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Tab,
  Tabs,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  Build,
  DirectionsCar,
  Warning,
  Schedule,
  TrendingUp,
  Add,
  Search,
  FilterList,
  Download,
  PhotoCamera,
  Assignment,
  Psychology,
  Sensors,
  Battery,
  Speed,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, AreaChart, Area } from 'recharts';
import maintenanceService, { Vehicle, MaintenanceSchedule, MaintenanceRecord, PredictiveMaintenanceAlert, MaintenanceAnalytics } from '../../services/maintenanceService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`maintenance-tabpanel-${index}`}
      aria-labelledby={`maintenance-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const MaintenanceDashboard: React.FC = () => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [maintenanceSchedules, setMaintenanceSchedules] = useState<MaintenanceSchedule[]>([]);
  const [predictiveAlerts, setPredictiveAlerts] = useState<PredictiveMaintenanceAlert[]>([]);
  const [analytics, setAnalytics] = useState<MaintenanceAnalytics | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [selectedSchedule, setSelectedSchedule] = useState<MaintenanceSchedule | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [newSchedule, setNewSchedule] = useState<Partial<MaintenanceSchedule>>({});
  const [completionData, setCompletionData] = useState<Partial<MaintenanceRecord>>({});

  const tenantId = localStorage.getItem('tenantId') || 'default';

  useEffect(() => {
    loadDashboardData();
    setupRealTimeSubscriptions();

    return () => {
      maintenanceService.disconnect();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [vehiclesData, schedulesData, alertsData, analyticsData] = await Promise.all([
        maintenanceService.getVehicles(tenantId),
        maintenanceService.getMaintenanceSchedules(tenantId),
        maintenanceService.getPredictiveAlerts(tenantId),
        maintenanceService.getMaintenanceAnalytics(tenantId),
      ]);

      setVehicles(vehiclesData);
      setMaintenanceSchedules(schedulesData);
      setPredictiveAlerts(alertsData);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading maintenance dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealTimeSubscriptions = () => {
    maintenanceService.subscribeToMaintenanceUpdates(tenantId, (schedule: MaintenanceSchedule) => {
      setMaintenanceSchedules(prev => 
        prev.map(s => s.id === schedule.id ? schedule : s)
      );
    });

    maintenanceService.subscribeToPredictiveAlerts(tenantId, (alert: PredictiveMaintenanceAlert) => {
      setPredictiveAlerts(prev => [alert, ...prev]);
    });
  };

  const handleVehicleClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
  };

  const handleScheduleClick = (schedule: MaintenanceSchedule) => {
    setSelectedSchedule(schedule);
  };

  const handleCreateSchedule = async () => {
    if (!newSchedule.vehicleId || !newSchedule.title || !newSchedule.dueDate) return;

    try {
      const schedule = await maintenanceService.createMaintenanceSchedule({
        ...newSchedule,
        tenantId,
        type: newSchedule.type || 'preventive',
        priority: newSchedule.priority || 'medium',
        status: 'scheduled',
        requiredParts: newSchedule.requiredParts || [],
        requiredCertifications: newSchedule.requiredCertifications || [],
        estimatedCost: newSchedule.estimatedCost || 0,
        scheduledBy: 'current-user', // Replace with actual user ID
      } as Omit<MaintenanceSchedule, 'id' | 'createdAt'>);

      setMaintenanceSchedules(prev => [schedule, ...prev]);
      setNewSchedule({});
    } catch (error) {
      console.error('Error creating maintenance schedule:', error);
    }
  };

  const handleCompleteMaintenance = async () => {
    if (!selectedSchedule || !completionData.workPerformed) return;

    try {
      const record = await maintenanceService.completeMaintenance(selectedSchedule.id, {
        ...completionData,
        tenantId,
        vehicleId: selectedSchedule.vehicleId,
        performedBy: 'current-user', // Replace with actual user ID
        actualDuration: completionData.actualDuration || 0,
        actualCost: completionData.actualCost || 0,
        partsUsed: completionData.partsUsed || [],
        beforePhotos: completionData.beforePhotos || [],
        afterPhotos: completionData.afterPhotos || [],
        mechanicNotes: completionData.mechanicNotes || '',
        qualityRating: completionData.qualityRating || 5,
        issuesFound: completionData.issuesFound || [],
        status: 'completed',
      } as Omit<MaintenanceRecord, 'id' | 'scheduleId' | 'performedAt'>);

      setMaintenanceSchedules(prev => 
        prev.map(s => s.id === selectedSchedule.id ? { ...s, status: 'completed' } : s)
      );
      setSelectedSchedule(null);
      setCompletionData({});
    } catch (error) {
      console.error('Error completing maintenance:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'info';
      case 'overdue': return 'error';
      case 'scheduled': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <Warning color="error" />;
      case 'high': return <Warning color="warning" />;
      case 'medium': return <Warning color="info" />;
      case 'low': return <Warning color="success" />;
      default: return <Warning />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <Typography>Loading Maintenance Dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Maintenance Management
        </Typography>
        <Box>
          <Button
            startIcon={<Add />}
            variant="contained"
            onClick={() => setTabValue(2)}
            sx={{ mr: 1 }}
          >
            Schedule Maintenance
          </Button>
          <Button
            startIcon={<Download />}
            variant="outlined"
          >
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Analytics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <DirectionsCar color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Vehicles
                  </Typography>
                  <Typography variant="h5">
                    {analytics?.totalVehicles || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Build color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    In Maintenance
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {analytics?.vehiclesInMaintenance || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Warning color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Overdue
                  </Typography>
                  <Typography variant="h5" color="error.main">
                    {analytics?.overdueMaintenances || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUp color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Efficiency
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {analytics?.maintenanceEfficiency?.toFixed(1) || 0}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Predictive Alerts */}
      {predictiveAlerts.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Predictive Maintenance Alerts
            </Typography>
            <Grid container spacing={2}>
              {predictiveAlerts.slice(0, 3).map((alert) => (
                <Grid item xs={12} md={4} key={alert.id}>
                  <Alert
                    severity={alert.severity as any}
                    icon={getSeverityIcon(alert.severity)}
                    action={
                      <Chip
                        label={`${(alert.confidence * 100).toFixed(0)}%`}
                        size="small"
                        variant="outlined"
                      />
                    }
                  >
                    <Typography variant="body2">
                      <strong>{alert.component}</strong> - {alert.alertType}
                    </Typography>
                    <Typography variant="caption" display="block">
                      Predicted failure: {new Date(alert.predictedFailureDate).toLocaleDateString()}
                    </Typography>
                  </Alert>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Vehicles" />
          <Tab label="Maintenance Schedules" />
          <Tab label="Schedule Maintenance" />
          <Tab label="Analytics" />
        </Tabs>

        {/* Vehicles Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            {vehicles.map((vehicle) => (
              <Grid item xs={12} md={6} lg={4} key={vehicle.id}>
                <Card
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                  onClick={() => handleVehicleClick(vehicle)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Box>
                        <Typography variant="h6">
                          {vehicle.plateNumber}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {vehicle.make} {vehicle.model} ({vehicle.year})
                        </Typography>
                      </Box>
                      <Chip
                        label={vehicle.status}
                        size="small"
                        color={getStatusColor(vehicle.status) as any}
                      />
                    </Box>

                    <Box mb={2}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Health Score
                      </Typography>
                      <Box display="flex" alignItems="center">
                        <LinearProgress
                          variant="determinate"
                          value={vehicle.healthScore}
                          color={getHealthScoreColor(vehicle.healthScore) as any}
                          sx={{ flexGrow: 1, mr: 1 }}
                        />
                        <Typography variant="body2">
                          {vehicle.healthScore}%
                        </Typography>
                      </Box>
                    </Box>

                    <Typography variant="body2" color="textSecondary">
                      Mileage: {vehicle.mileage.toLocaleString()} km
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Next Maintenance: {new Date(vehicle.nextMaintenanceDue).toLocaleDateString()}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Maintenance Schedules Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            {maintenanceSchedules.map((schedule) => (
              <Grid item xs={12} md={6} lg={4} key={schedule.id}>
                <Card
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                  onClick={() => handleScheduleClick(schedule)}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="h6" noWrap>
                        {schedule.title}
                      </Typography>
                      <Chip
                        label={schedule.status}
                        size="small"
                        color={getStatusColor(schedule.status) as any}
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary" mb={2}>
                      {schedule.description}
                    </Typography>

                    <Box display="flex" gap={1} mb={1}>
                      <Chip
                        label={schedule.type}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={schedule.priority}
                        size="small"
                        color={getPriorityColor(schedule.priority) as any}
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary">
                      Due: {new Date(schedule.dueDate).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Estimated Cost: ${schedule.estimatedCost}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Schedule Maintenance Tab */}
        <TabPanel value={tabValue} index={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Schedule New Maintenance
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Vehicle</InputLabel>
                    <Select
                      value={newSchedule.vehicleId || ''}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, vehicleId: e.target.value }))}
                    >
                      {vehicles.map((vehicle) => (
                        <MenuItem key={vehicle.id} value={vehicle.id}>
                          {vehicle.plateNumber} - {vehicle.make} {vehicle.model}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Type</InputLabel>
                    <Select
                      value={newSchedule.type || ''}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, type: e.target.value as any }))}
                    >
                      <MenuItem value="preventive">Preventive</MenuItem>
                      <MenuItem value="corrective">Corrective</MenuItem>
                      <MenuItem value="predictive">Predictive</MenuItem>
                      <MenuItem value="emergency">Emergency</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Priority</InputLabel>
                    <Select
                      value={newSchedule.priority || ''}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, priority: e.target.value as any }))}
                    >
                      <MenuItem value="low">Low</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="high">High</MenuItem>
                      <MenuItem value="critical">Critical</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="date"
                    label="Due Date"
                    value={newSchedule.dueDate?.split('T')[0] || ''}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, dueDate: e.target.value }))}
                    margin="normal"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Title"
                    value={newSchedule.title || ''}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, title: e.target.value }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Description"
                    value={newSchedule.description || ''}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, description: e.target.value }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Estimated Duration (hours)"
                    value={newSchedule.estimatedDuration || ''}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, estimatedDuration: Number(e.target.value) }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Estimated Cost"
                    value={newSchedule.estimatedCost || ''}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, estimatedCost: Number(e.target.value) }))}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    onClick={handleCreateSchedule}
                    disabled={!newSchedule.vehicleId || !newSchedule.title || !newSchedule.dueDate}
                  >
                    Schedule Maintenance
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Analytics Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Maintenance by Type
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={Object.entries(analytics?.maintenanceByType || {}).map(([key, value]) => ({
                          name: key,
                          value,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(analytics?.maintenanceByType || {}).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Top Maintenance Issues
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={analytics?.topMaintenanceIssues || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="issue" />
                      <YAxis />
                      <RechartsTooltip />
                      <Bar dataKey="frequency" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* Vehicle Detail Dialog */}
      <Dialog
        open={!!selectedVehicle}
        onClose={() => setSelectedVehicle(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Vehicle Details - {selectedVehicle?.plateNumber}
        </DialogTitle>
        <DialogContent>
          {selectedVehicle && (
            <Box>
              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Make & Model</Typography>
                  <Typography variant="h6">{selectedVehicle.make} {selectedVehicle.model}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Year</Typography>
                  <Typography variant="h6">{selectedVehicle.year}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Status</Typography>
                  <Chip label={selectedVehicle.status} color={getStatusColor(selectedVehicle.status) as any} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Health Score</Typography>
                  <Box display="flex" alignItems="center">
                    <LinearProgress
                      variant="determinate"
                      value={selectedVehicle.healthScore}
                      color={getHealthScoreColor(selectedVehicle.healthScore) as any}
                      sx={{ flexGrow: 1, mr: 1 }}
                    />
                    <Typography variant="body2">{selectedVehicle.healthScore}%</Typography>
                  </Box>
                </Grid>
              </Grid>

              <Typography variant="h6" gutterBottom>
                Sensor Status
              </Typography>
              <Grid container spacing={1} mb={3}>
                {selectedVehicle.sensorConfiguration.map((sensor) => (
                  <Grid item xs={12} sm={6} md={4} key={sensor.id}>
                    <Card variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Box display="flex" alignItems="center" mb={1}>
                          <Sensors sx={{ mr: 1, fontSize: 16 }} />
                          <Typography variant="body2">{sensor.type}</Typography>
                        </Box>
                        <Typography variant="caption" color="textSecondary">
                          {sensor.location}
                        </Typography>
                        <Chip
                          label={sensor.calibrationStatus}
                          size="small"
                          color={sensor.calibrationStatus === 'calibrated' ? 'success' : 'warning'}
                          sx={{ mt: 1 }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              <Typography variant="h6" gutterBottom>
                Recent Maintenance History
              </Typography>
              <List>
                {selectedVehicle.maintenanceHistory.slice(0, 3).map((record, index) => (
                  <ListItem key={index}>
                    <ListItemAvatar>
                      <Avatar>
                        <Build />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={record.workPerformed}
                      secondary={`${new Date(record.performedAt).toLocaleDateString()} - $${record.actualCost}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedVehicle(null)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Maintenance Schedule Detail Dialog */}
      <Dialog
        open={!!selectedSchedule}
        onClose={() => setSelectedSchedule(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Maintenance Schedule Details
        </DialogTitle>
        <DialogContent>
          {selectedSchedule && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedSchedule.title}
              </Typography>

              <Typography variant="body1" paragraph>
                {selectedSchedule.description}
              </Typography>

              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Type</Typography>
                  <Chip label={selectedSchedule.type} color="primary" />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Priority</Typography>
                  <Chip label={selectedSchedule.priority} color={getPriorityColor(selectedSchedule.priority) as any} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Due Date</Typography>
                  <Typography variant="body1">{new Date(selectedSchedule.dueDate).toLocaleDateString()}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Estimated Cost</Typography>
                  <Typography variant="body1">${selectedSchedule.estimatedCost}</Typography>
                </Grid>
              </Grid>

              {selectedSchedule.status === 'scheduled' && (
                <Box mt={3}>
                  <Typography variant="h6" gutterBottom>
                    Complete Maintenance
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Work Performed"
                    value={completionData.workPerformed || ''}
                    onChange={(e) => setCompletionData(prev => ({ ...prev, workPerformed: e.target.value }))}
                    margin="normal"
                  />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Actual Duration (hours)"
                        value={completionData.actualDuration || ''}
                        onChange={(e) => setCompletionData(prev => ({ ...prev, actualDuration: Number(e.target.value) }))}
                        margin="normal"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Actual Cost"
                        value={completionData.actualCost || ''}
                        onChange={(e) => setCompletionData(prev => ({ ...prev, actualCost: Number(e.target.value) }))}
                        margin="normal"
                      />
                    </Grid>
                  </Grid>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="Mechanic Notes"
                    value={completionData.mechanicNotes || ''}
                    onChange={(e) => setCompletionData(prev => ({ ...prev, mechanicNotes: e.target.value }))}
                    margin="normal"
                  />
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedSchedule(null)}>
            Close
          </Button>
          {selectedSchedule?.status === 'scheduled' && (
            <Button
              onClick={handleCompleteMaintenance}
              variant="contained"
              disabled={!completionData.workPerformed}
            >
              Complete Maintenance
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaintenanceDashboard;
