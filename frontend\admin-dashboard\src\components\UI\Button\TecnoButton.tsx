import React from 'react';
import {
  Button,
  ButtonProps,
  CircularProgress,
  useTheme,
  alpha,
  Box,
} from '@mui/material';

interface TecnoButtonProps extends Omit<ButtonProps, 'variant'> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
  fullWidth?: boolean;
  rounded?: boolean;
  shadow?: boolean;
}

const TecnoButton: React.FC<TecnoButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  icon,
  iconPosition = 'start',
  fullWidth = false,
  rounded = false,
  shadow = false,
  disabled,
  onClick,
  ...props
}) => {
  const theme = useTheme();

  const getButtonStyles = () => {
    const baseStyles = {
      fontWeight: 500,
      textTransform: 'none' as const,
      borderRadius: rounded ? theme.shape.borderRadius * 3 : theme.shape.borderRadius,
      transition: theme.transitions.create(['all'], {
        duration: theme.transitions.duration.short,
      }),
      position: 'relative' as const,
      overflow: 'hidden' as const,
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
        transition: theme.transitions.create(['left'], {
          duration: theme.transitions.duration.complex,
        }),
      },
      '&:hover::before': {
        left: '100%',
      },
    };

    const sizeStyles = {
      small: {
        padding: `${theme.spacing(0.75)} ${theme.spacing(2)}`,
        fontSize: theme.typography.caption.fontSize,
        minHeight: 32,
      },
      medium: {
        padding: `${theme.spacing(1)} ${theme.spacing(3)}`,
        fontSize: theme.typography.body2.fontSize,
        minHeight: 40,
      },
      large: {
        padding: `${theme.spacing(1.5)} ${theme.spacing(4)}`,
        fontSize: theme.typography.body1.fontSize,
        minHeight: 48,
      },
    };

    const variantStyles = {
      primary: {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
        boxShadow: shadow ? theme.shadows[3] : 'none',
        '&:hover': {
          backgroundColor: theme.palette.primary.dark,
          boxShadow: shadow ? theme.shadows[6] : theme.shadows[2],
          transform: 'translateY(-1px)',
        },
        '&:active': {
          transform: 'translateY(0)',
        },
      },
      secondary: {
        backgroundColor: theme.palette.secondary.main,
        color: theme.palette.secondary.contrastText,
        boxShadow: shadow ? theme.shadows[3] : 'none',
        '&:hover': {
          backgroundColor: theme.palette.secondary.dark,
          boxShadow: shadow ? theme.shadows[6] : theme.shadows[2],
          transform: 'translateY(-1px)',
        },
      },
      outline: {
        backgroundColor: 'transparent',
        color: theme.palette.primary.main,
        border: `2px solid ${theme.palette.primary.main}`,
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary.main, 0.1),
          borderColor: theme.palette.primary.dark,
          transform: 'translateY(-1px)',
        },
      },
      ghost: {
        backgroundColor: 'transparent',
        color: theme.palette.primary.main,
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary.main, 0.1),
          transform: 'translateY(-1px)',
        },
      },
      gradient: {
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        color: '#ffffff',
        boxShadow: shadow ? theme.shadows[3] : 'none',
        '&:hover': {
          background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
          boxShadow: shadow ? theme.shadows[6] : theme.shadows[2],
          transform: 'translateY(-1px)',
        },
      },
      danger: {
        backgroundColor: theme.palette.error.main,
        color: theme.palette.error.contrastText,
        boxShadow: shadow ? theme.shadows[3] : 'none',
        '&:hover': {
          backgroundColor: theme.palette.error.dark,
          boxShadow: shadow ? theme.shadows[6] : theme.shadows[2],
          transform: 'translateY(-1px)',
        },
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      width: fullWidth ? '100%' : 'auto',
    };
  };

  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CircularProgress
            size={size === 'small' ? 16 : size === 'large' ? 24 : 20}
            color="inherit"
          />
          {children && <span>جاري التحميل...</span>}
        </Box>
      );
    }

    if (icon) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {iconPosition === 'start' && icon}
          {children}
          {iconPosition === 'end' && icon}
        </Box>
      );
    }

    return children;
  };

  return (
    <Button
      {...props}
      disabled={disabled || loading}
      onClick={loading ? undefined : onClick}
      sx={getButtonStyles()}
    >
      {renderContent()}
    </Button>
  );
};

export default TecnoButton;
