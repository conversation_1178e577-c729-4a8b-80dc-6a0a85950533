import { api<PERSON>eth<PERSON>, handleApiError, SERVICE_URLS } from './api';
import { ApiResponse, LoginResponse, UserRole } from '../types/api';
import { User } from '../store/slices/authSlice';
import { mockLogin, mockLogout, mockGetCurrentUser } from './mockAuthService';
import { generateMockLoginResponse, toUserRole } from '../utils/typeFixUtils';

// Types for Auth Service
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  phone: string;
  userType: 'ADMIN' | 'MANAGER' | 'OPERATOR';
  role?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface UpdateProfileRequest {
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

class AuthService {
  private baseUrl = SERVICE_URLS.AUTH_SERVICE;

  // Login user
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    // Force use Mock Auth for now (debugging)
    const useMockAuth = true; // process.env.REACT_APP_USE_MOCK_AUTH === 'true';

    console.log('🔧 Login attempt with credentials:', credentials.email);
    console.log('🔧 Using Mock Auth:', useMockAuth);

    if (useMockAuth) {
      console.log('🔧 Using Mock Auth Service');

      try {
        const mockResponse = await mockLogin({
          email: credentials.email,
          password: credentials.password
        });

        console.log('🔧 Mock Response:', mockResponse);

        if (mockResponse.success && mockResponse.data) {
          // Convert mock response to expected format
          const response: ApiResponse<LoginResponse> = {
            success: true,
            message: mockResponse.message,
            data: {
              user: {
                ...mockResponse.data.user,
                role: toUserRole(mockResponse.data.user.role),
                avatar: mockResponse.data.user.avatar || '/default-avatar.png'
              },
              token: mockResponse.data.token,
              expiresIn: mockResponse.data.expiresIn || 86400,
              refreshToken: mockResponse.data.refreshToken
            }
          };

          console.log('🔧 Converted Response:', response);
          return response;
        } else {
          console.log('❌ Mock Auth failed:', mockResponse.message);
          throw new Error(mockResponse.message);
        }
      } catch (error) {
        console.log('❌ Mock Auth error:', error);
        throw new Error(handleApiError(error));
      }
    }

    try {
      const response = await apiMethods.post<ApiResponse<LoginResponse>>(
        `${this.baseUrl}/login`,
        credentials
      );

      // Store token in localStorage
      if (response.data.data?.token) {
        localStorage.setItem('token', response.data.data.token);

        if (response.data.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.data.refreshToken);
        }
      }

      return response.data;
    } catch (error) {
      // Fallback to mock data if service is not available
      if (process.env.REACT_APP_ENABLE_MOCK_DATA === 'true') {
        const { mockApiResponses } = await import('../utils/mockDataManager');
        const mockResponse = await mockApiResponses.login(credentials);

        // Store mock token
        if (mockResponse.data?.token) {
          localStorage.setItem('token', mockResponse.data.token);
        }

        return mockResponse;
      }

      throw new Error(handleApiError(error));
    }
  }

  // Register new user
  async register(userData: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await apiMethods.post<ApiResponse<LoginResponse>>(
        `${this.baseUrl}/register`,
        userData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        await apiMethods.post(`${this.baseUrl}/logout`, { refreshToken });
      }
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    }
  }

  // Refresh token
  async refreshToken(): Promise<ApiResponse<{ token: string; refreshToken?: string }>> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiMethods.post<ApiResponse<{ token: string; refreshToken?: string }>>(
        `${this.baseUrl}/refresh`,
        { refreshToken }
      );
      
      // Update stored tokens
      if (response.data.data?.token) {
        localStorage.setItem('token', response.data.data.token);
        
        if (response.data.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.data.refreshToken);
        }
      }
      
      return response.data;
    } catch (error) {
      // Clear tokens on refresh failure
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      throw new Error(handleApiError(error));
    }
  }

  // Get current user profile
  async getProfile(): Promise<ApiResponse<User>> {
    try {
      const response = await apiMethods.get<ApiResponse<User>>(
        `${this.baseUrl}/profile`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update user profile
  async updateProfile(profileData: UpdateProfileRequest): Promise<ApiResponse<User>> {
    try {
      const response = await apiMethods.put<ApiResponse<User>>(
        `${this.baseUrl}/profile`,
        profileData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Change password
  async changePassword(passwordData: ChangePasswordRequest): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/change-password`,
        passwordData
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/reset-password`,
        { email }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Reset password with token
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/reset-password/confirm`,
        { token, newPassword }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Verify email
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/verify-email`,
        { token }
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Resend verification email
  async resendVerificationEmail(): Promise<ApiResponse<void>> {
    try {
      const response = await apiMethods.post<ApiResponse<void>>(
        `${this.baseUrl}/resend-verification`
      );
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token;
  }

  // Get stored token
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  // Get stored refresh token
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  // Validate token (check if expired)
  async validateToken(): Promise<boolean> {
    try {
      const response = await apiMethods.get(`${this.baseUrl}/validate`);
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

export const authService = new AuthService();
export default authService;
