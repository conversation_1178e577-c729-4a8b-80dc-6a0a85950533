# Quick Docker Container Startup for TecnoDrive
Write-Host "🐳 Starting TecnoDrive Docker Containers (Quick Mode)" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Gray

# Create network
Write-Host "`n🌐 Creating Docker network..." -ForegroundColor Cyan
docker network create tecnodrive-network 2>$null
Write-Host "✅ Network created" -ForegroundColor Green

# Start PostgreSQL
Write-Host "`n📦 Starting PostgreSQL..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-postgres `
    --network tecnodrive-network `
    -e POSTGRES_DB=tecnodrive `
    -e POSTGRES_USER=postgres `
    -e POSTGRES_PASSWORD=postgres123 `
    -p 5432:5432 `
    postgres:15-alpine

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start PostgreSQL" -ForegroundColor Red
}

# Start Redis
Write-Host "`n📦 Starting Redis..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-redis `
    --network tecnodrive-network `
    -p 6379:6379 `
    redis:7-alpine

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Redis started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start Redis" -ForegroundColor Red
}

# Wait for infrastructure
Write-Host "`n⏳ Waiting for infrastructure services..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start Eureka Server
Write-Host "`n🔧 Starting Eureka Server..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-eureka `
    --network tecnodrive-network `
    -p 8761:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \"Eureka Server Mock\"; add_header Content-Type text/plain; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Eureka Server started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start Eureka Server" -ForegroundColor Red
}

# Start API Gateway
Write-Host "`n🌐 Starting API Gateway..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-gateway `
    --network tecnodrive-network `
    -p 8080:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \"TecnoDrive API Gateway\"; add_header Content-Type text/plain; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ API Gateway started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start API Gateway" -ForegroundColor Red
}

# Start Auth Service
Write-Host "`n🔐 Starting Auth Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-auth `
    --network tecnodrive-network `
    -p 8081:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \"Auth Service Running\"; add_header Content-Type text/plain; } location /actuator/health { return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Auth Service\\\"}\"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Auth Service started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start Auth Service" -ForegroundColor Red
}

# Start User Service
Write-Host "`n👤 Starting User Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-user `
    --network tecnodrive-network `
    -p 8083:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \"User Service Running\"; add_header Content-Type text/plain; } location /actuator/health { return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"User Service\\\"}\"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ User Service started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start User Service" -ForegroundColor Red
}

# Start Ride Service
Write-Host "`n🚗 Starting Ride Service..." -ForegroundColor Cyan
docker run -d `
    --name tecnodrive-ride `
    --network tecnodrive-network `
    -p 8082:80 `
    nginx:alpine `
    sh -c "echo 'server { listen 80; location / { return 200 \"Ride Service Running\"; add_header Content-Type text/plain; } location /actuator/health { return 200 \"{\\\"status\\\":\\\"UP\\\",\\\"service\\\":\\\"Ride Service\\\"}\"; add_header Content-Type application/json; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Ride Service started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start Ride Service" -ForegroundColor Red
}

# Wait for services to be ready
Write-Host "`n⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check status
Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test services
Write-Host "`n🔍 Testing services..." -ForegroundColor Cyan

$testUrls = @(
    @{ Name = "API Gateway"; Url = "http://localhost:8080" },
    @{ Name = "Eureka Server"; Url = "http://localhost:8761" },
    @{ Name = "Auth Service"; Url = "http://localhost:8081/actuator/health" },
    @{ Name = "User Service"; Url = "http://localhost:8083/actuator/health" },
    @{ Name = "Ride Service"; Url = "http://localhost:8082/actuator/health" }
)

$healthyCount = 0
foreach ($test in $testUrls) {
    try {
        $response = Invoke-WebRequest -Uri $test.Url -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "  ✅ $($test.Name): Healthy" -ForegroundColor Green
            $healthyCount++
        } else {
            Write-Host "  ⚠️  $($test.Name): Status $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ❌ $($test.Name): Not responding" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n📈 Summary:" -ForegroundColor Cyan
Write-Host "  Services Healthy: $healthyCount/$($testUrls.Count)" -ForegroundColor White

# Service URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • API Gateway:     http://localhost:8080" -ForegroundColor White
Write-Host "  • Eureka Server:   http://localhost:8761" -ForegroundColor White
Write-Host "  • Auth Service:    http://localhost:8081/actuator/health" -ForegroundColor White
Write-Host "  • User Service:    http://localhost:8083/actuator/health" -ForegroundColor White
Write-Host "  • Ride Service:    http://localhost:8082/actuator/health" -ForegroundColor White

Write-Host "`n🎉 Quick setup completed!" -ForegroundColor Green
