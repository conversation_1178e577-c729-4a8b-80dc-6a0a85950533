-- TECNO DRIVE Parcel & Delivery Service Database Schema
-- Version 1.0 - Initial schema creation (Simplified)

-- Enable UUID and PostGIS extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS postgis;

-- Parcels table (simplified)
CREATE TABLE parcels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID NOT NULL,
  receiver_id UUID NOT NULL,
  weight DECIMAL(8,2) NOT NULL,
  dimensions VARCHAR(50),
  status VARCHAR(50) NOT NULL DEFAULT 'CREATED',
  tracking_number VARCHAR(100) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Deliveries table
CREATE TABLE deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parcel_id UUID REFERENCES parcels(id) UNIQUE,
  driver_id UUID,
  assigned_at TIMESTAMP,
  picked_up_at TIMESTAMP,
  delivered_at TIMESTAMP,
  status VARCHAR(50) NOT NULL DEFAULT 'ASSIGNED'
);

-- Delivery proof table
CREATE TABLE delivery_proof (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  delivery_id UUID REFERENCES deliveries(id),
  proof_type VARCHAR(50),
  proof_url TEXT,
  captured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Parcel categories table
CREATE TABLE parcel_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) UNIQUE NOT NULL,
  name_ar VARCHAR(100) NOT NULL,
  description TEXT,
  max_weight_kg DECIMAL(8,2) NOT NULL DEFAULT 50.00,
  max_dimensions_cm VARCHAR(50),
  base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  price_per_kg DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  price_per_km DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  is_fragile BOOLEAN DEFAULT false,
  requires_signature BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Delivery zones table
CREATE TABLE delivery_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  name_ar VARCHAR(100) NOT NULL,
  city VARCHAR(100) NOT NULL,
  area_polygon TEXT, -- Will be changed to GEOMETRY in next migration
  base_delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  per_km_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  estimated_delivery_hours INTEGER DEFAULT 24,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


