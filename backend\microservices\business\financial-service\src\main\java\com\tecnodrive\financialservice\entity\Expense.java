package com.tecnodrive.financialservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Instant;
import java.util.UUID;

/**
 * Expense Entity
 * 
 * Tracks business expenses for financial reporting and tax purposes.
 * Supports expense categorization and approval workflows.
 */
@Entity
@Table(name = "expenses")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Expense {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Expense category
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ExpenseCategory expenseCategory;

    /**
     * Expense subcategory for detailed tracking
     */
    @Column(length = 100)
    private String subcategory;

    /**
     * Expense amount
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;

    /**
     * Currency code
     */
    @Column(nullable = false, length = 3)
    @Builder.Default
    private String currency = "USD";

    /**
     * Expense date
     */
    @Column(nullable = false)
    private LocalDate expenseDate;

    /**
     * Expense description
     */
    @Column(nullable = false, length = 500)
    private String description;

    /**
     * Expense status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ExpenseStatus status = ExpenseStatus.PENDING;

    /**
     * Employee who incurred the expense
     */
    @Column(nullable = false)
    private String incurredByEmployeeId;

    /**
     * Vendor/Supplier information
     */
    @Column(length = 200)
    private String vendor;

    /**
     * Receipt number or reference
     */
    @Column(length = 100)
    private String receiptNumber;

    /**
     * Tax amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * Whether expense is tax deductible
     */
    @Builder.Default
    private boolean taxDeductible = true;

    /**
     * Reimbursement status
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private ReimbursementStatus reimbursementStatus = ReimbursementStatus.NOT_APPLICABLE;

    /**
     * Reimbursement amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal reimbursementAmount = BigDecimal.ZERO;

    /**
     * Approved by user ID
     */
    private String approvedBy;

    /**
     * Approval date
     */
    private Instant approvedAt;

    /**
     * Approval notes
     */
    @Column(length = 500)
    private String approvalNotes;

    /**
     * Company/Tenant ID
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * Budget ID if linked to a budget
     */
    private UUID budgetId;

    /**
     * Additional metadata (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Expense Category Enum
     */
    public enum ExpenseCategory {
        FUEL,
        VEHICLE_MAINTENANCE,
        VEHICLE_INSURANCE,
        OFFICE_SUPPLIES,
        MARKETING,
        TECHNOLOGY,
        UTILITIES,
        RENT,
        SALARIES,
        BENEFITS,
        TRAINING,
        TRAVEL,
        MEALS,
        LEGAL,
        ACCOUNTING,
        CONSULTING,
        EQUIPMENT,
        SOFTWARE_LICENSES,
        TELECOMMUNICATIONS,
        OTHER
    }

    /**
     * Expense Status Enum
     */
    public enum ExpenseStatus {
        PENDING,
        APPROVED,
        REJECTED,
        PAID,
        CANCELLED
    }

    /**
     * Reimbursement Status Enum
     */
    public enum ReimbursementStatus {
        NOT_APPLICABLE,
        PENDING,
        APPROVED,
        PAID,
        REJECTED
    }

    /**
     * Check if expense needs approval
     */
    public boolean needsApproval() {
        return status == ExpenseStatus.PENDING;
    }

    /**
     * Check if expense is approved
     */
    public boolean isApproved() {
        return status == ExpenseStatus.APPROVED || status == ExpenseStatus.PAID;
    }

    /**
     * Get net expense amount (amount - reimbursement)
     */
    public BigDecimal getNetExpenseAmount() {
        BigDecimal reimbursement = reimbursementAmount != null ? reimbursementAmount : BigDecimal.ZERO;
        return amount.subtract(reimbursement);
    }
}
