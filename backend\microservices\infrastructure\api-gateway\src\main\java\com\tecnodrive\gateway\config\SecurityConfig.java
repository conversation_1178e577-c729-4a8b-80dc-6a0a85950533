package com.tecnodrive.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Enhanced Security Configuration for API Gateway
 * Implements comprehensive security measures including CORS, CSRF protection,
 * security headers, and authentication/authorization
 */
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        return http
                // CSRF Configuration - Disable for now, can be enabled selectively
                .csrf(csrf -> csrf.disable())

                // CORS Configuration
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))

                // Authorization Rules
                .authorizeExchange(exchanges -> exchanges
                        // Public endpoints
                        .pathMatchers("/actuator/health", "/actuator/info").permitAll()
                        .pathMatchers("/api/v1/auth/login", "/api/v1/auth/register").permitAll()
                        .pathMatchers("/fallback/**").permitAll()
                        .pathMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()

                        // Admin endpoints - require ADMIN role
                        .pathMatchers("/api/v1/admin/**").hasRole("ADMIN")
                        .pathMatchers("/api/v1/financial/**").hasAnyRole("ADMIN", "FINANCE_MANAGER")
                        .pathMatchers("/api/v1/hr/**").hasAnyRole("ADMIN", "HR_MANAGER")
                        .pathMatchers("/api/v1/fleet/**").hasAnyRole("ADMIN", "FLEET_MANAGER")

                        // Payment endpoints - require authentication and specific roles
                        .pathMatchers("/api/v1/payments/process").hasAnyRole("USER", "DRIVER", "ADMIN")
                        .pathMatchers("/api/v1/payments/**").hasAnyRole("USER", "DRIVER", "ADMIN", "FINANCE_MANAGER")

                        // Driver-specific endpoints
                        .pathMatchers("/api/v1/rides/accept", "/api/v1/rides/complete").hasRole("DRIVER")
                        .pathMatchers("/api/v1/locations/track").hasAnyRole("DRIVER", "ADMIN")

                        // User endpoints
                        .pathMatchers("/api/v1/rides/**").hasAnyRole("USER", "DRIVER", "ADMIN")
                        .pathMatchers("/api/v1/parcels/**").hasAnyRole("USER", "ADMIN")
                        .pathMatchers("/api/v1/notifications/**").authenticated()

                        // Analytics - require specific permissions
                        .pathMatchers("/api/v1/analytics/**").hasAnyRole("ADMIN", "ANALYST")

                        // SaaS management - admin only
                        .pathMatchers("/api/v1/saas/**").hasRole("ADMIN")

                        // All other requests require authentication
                        .anyExchange().authenticated()
                )

                .build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // Allowed origins - be specific in production
        configuration.setAllowedOriginPatterns(Arrays.asList(
                "https://*.tecnodrive.com",
                "https://localhost:*",
                "http://localhost:*"
        ));

        // Allowed methods
        configuration.setAllowedMethods(Arrays.asList(
                "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"
        ));

        // Allowed headers
        configuration.setAllowedHeaders(Arrays.asList(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "X-Request-ID",
                "X-CSRF-TOKEN",
                "Accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
        ));

        // Exposed headers
        configuration.setExposedHeaders(Arrays.asList(
                "X-Request-ID",
                "X-Response-Time",
                "X-Rate-Limit-Remaining",
                "X-Rate-Limit-Reset"
        ));

        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
