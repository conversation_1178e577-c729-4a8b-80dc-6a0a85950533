# Generate Demo Data for TecnoDrive Platform
Write-Host "🎭 Generating Demo Data for TecnoDrive Platform..." -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

$API_BASE = "http://localhost:8085"

# Demo vehicles data
$vehicles = @(
    @{
        vehicleId = "vehicle_001"
        lat = 24.7136
        lng = 46.6753
        speed = 45.5
        heading = 180
        status = "busy"
        driverName = "أحمد الراشد"
        vehicleType = "sedan"
    },
    @{
        vehicleId = "vehicle_002"
        lat = 24.7236
        lng = 46.6853
        speed = 0
        heading = 90
        status = "available"
        driverName = "محمد العتيبي"
        vehicleType = "suv"
    },
    @{
        vehicleId = "vehicle_003"
        lat = 24.7036
        lng = 46.6653
        speed = 30.2
        heading = 270
        status = "busy"
        driverName = "سعد النهدي"
        vehicleType = "van"
    },
    @{
        vehicleId = "vehicle_004"
        lat = 24.7336
        lng = 46.6953
        speed = 55.8
        heading = 45
        status = "busy"
        driverName = "عبدالله الشمري"
        vehicleType = "truck"
    },
    @{
        vehicleId = "vehicle_005"
        lat = 24.6936
        lng = 46.6553
        speed = 0
        heading = 0
        status = "maintenance"
        driverName = "فهد القحطاني"
        vehicleType = "sedan"
    }
)

# Demo alerts data
$alerts = @(
    @{
        type = "delay"
        severity = "warning"
        vehicleId = "vehicle_001"
        message = "تأخير في التوصيل - 10 دقائق"
        location = @{
            lat = 24.7136
            lng = 46.6753
        }
    },
    @{
        type = "traffic"
        severity = "info"
        vehicleId = "vehicle_003"
        message = "ازدحام مروري على الطريق الدائري"
        location = @{
            lat = 24.7036
            lng = 46.6653
        }
    },
    @{
        type = "breakdown"
        severity = "critical"
        vehicleId = "vehicle_005"
        message = "عطل في المركبة - تحتاج صيانة فورية"
        location = @{
            lat = 24.6936
            lng = 46.6553
        }
    }
)

Write-Host "`n🚗 Sending Vehicle Location Updates..." -ForegroundColor Yellow

foreach ($vehicle in $vehicles) {
    try {
        $json = $vehicle | ConvertTo-Json -Depth 3
        $response = Invoke-RestMethod -Uri "$API_BASE/api/locations/update" -Method POST -Body $json -ContentType "application/json"
        Write-Host "✅ Vehicle $($vehicle.vehicleId): $($vehicle.driverName) - $($vehicle.status)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to update vehicle $($vehicle.vehicleId): $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Milliseconds 500
}

Write-Host "`n⚠️  Sending Demo Alerts..." -ForegroundColor Yellow

foreach ($alert in $alerts) {
    try {
        $json = $alert | ConvertTo-Json -Depth 3
        $response = Invoke-RestMethod -Uri "$API_BASE/api/locations/alert" -Method POST -Body $json -ContentType "application/json"
        Write-Host "✅ Alert sent: $($alert.message)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to send alert: $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Milliseconds 300
}

Write-Host "`n📊 Generating Random Movement..." -ForegroundColor Yellow

# Simulate vehicle movement
for ($i = 1; $i -le 10; $i++) {
    Write-Host "Movement update $i/10..." -ForegroundColor Cyan
    
    foreach ($vehicle in $vehicles) {
        if ($vehicle.status -eq "busy") {
            # Add small random movement
            $newLat = $vehicle.lat + (Get-Random -Minimum -0.001 -Maximum 0.001)
            $newLng = $vehicle.lng + (Get-Random -Minimum -0.001 -Maximum 0.001)
            $newSpeed = Get-Random -Minimum 20 -Maximum 60
            $newHeading = Get-Random -Minimum 0 -Maximum 360
            
            $updateData = @{
                vehicleId = $vehicle.vehicleId
                lat = $newLat
                lng = $newLng
                speed = $newSpeed
                heading = $newHeading
                status = $vehicle.status
                driverName = $vehicle.driverName
                vehicleType = $vehicle.vehicleType
                timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
            }
            
            try {
                $json = $updateData | ConvertTo-Json -Depth 3
                $response = Invoke-RestMethod -Uri "$API_BASE/api/locations/update" -Method POST -Body $json -ContentType "application/json"
                
                # Update vehicle position for next iteration
                $vehicle.lat = $newLat
                $vehicle.lng = $newLng
                $vehicle.speed = $newSpeed
                $vehicle.heading = $newHeading
                
            } catch {
                Write-Host "❌ Movement update failed for $($vehicle.vehicleId)" -ForegroundColor Red
            }
        }
    }
    
    Start-Sleep -Seconds 2
}

Write-Host "`n🎉 Demo Data Generation Complete!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Now open your browser and navigate to:" -ForegroundColor Yellow
Write-Host "   http://localhost:3000/live-operations" -ForegroundColor White
Write-Host ""
Write-Host "🗺️  You should see:" -ForegroundColor Yellow
Write-Host "   • 5 vehicles on the map with different statuses" -ForegroundColor White
Write-Host "   • Real-time location updates" -ForegroundColor White
Write-Host "   • Alert notifications in the sidebar" -ForegroundColor White
Write-Host "   • Live statistics updating" -ForegroundColor White
Write-Host ""
Write-Host "🔄 The vehicles will continue moving automatically!" -ForegroundColor Cyan
