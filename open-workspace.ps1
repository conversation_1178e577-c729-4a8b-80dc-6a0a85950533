# 🚀 TecnoDrive Platform - Multi-Root Workspace Launcher
# This script opens the TecnoDrive workspace in VSCode with all necessary configurations

param(
    [switch]$InstallExtensions,
    [switch]$SetupEnvironment,
    [switch]$CheckHealth,
    [switch]$Help
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "🚀 TecnoDrive Platform - Multi-Root Workspace Launcher" $Blue
    Write-ColorOutput "=" * 60 $Blue
    Write-ColorOutput ""
    Write-ColorOutput "Usage:" $Yellow
    Write-ColorOutput "  .\open-workspace.ps1 [OPTIONS]" $Cyan
    Write-ColorOutput ""
    Write-ColorOutput "Options:" $Yellow
    Write-ColorOutput "  -InstallExtensions    Install recommended VSCode extensions" $Cyan
    Write-ColorOutput "  -SetupEnvironment     Initialize development environment" $Cyan
    Write-ColorOutput "  -CheckHealth          Check system health and requirements" $Cyan
    Write-ColorOutput "  -Help                 Show this help message" $Cyan
    Write-ColorOutput ""
    Write-ColorOutput "Examples:" $Yellow
    Write-ColorOutput "  .\open-workspace.ps1                    # Open workspace" $Cyan
    Write-ColorOutput "  .\open-workspace.ps1 -InstallExtensions # Install extensions and open" $Cyan
    Write-ColorOutput "  .\open-workspace.ps1 -SetupEnvironment  # Initialize environment and open" $Cyan
    Write-ColorOutput "  .\open-workspace.ps1 -CheckHealth       # Check health before opening" $Cyan
}

function Test-VSCodeInstalled {
    try {
        $null = Get-Command code -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

function Test-RequiredTools {
    Write-ColorOutput "🔍 Checking required tools..." $Yellow
    
    $tools = @{
        "VSCode"  = { Test-VSCodeInstalled }
        "Java"    = { try { $null = Get-Command java -ErrorAction Stop; return $true } catch { return $false } }
        "Maven"   = { try { $null = Get-Command mvn -ErrorAction Stop; return $true } catch { return $false } }
        "Node.js" = { try { $null = Get-Command node -ErrorAction Stop; return $true } catch { return $false } }
        "npm"     = { try { $null = Get-Command npm -ErrorAction Stop; return $true } catch { return $false } }
        "Python"  = { try { $null = Get-Command python -ErrorAction Stop; return $true } catch { return $false } }
        "Docker"  = { try { $null = Get-Command docker -ErrorAction Stop; return $true } catch { return $false } }
        "Git"     = { try { $null = Get-Command git -ErrorAction Stop; return $true } catch { return $false } }
    }
    
    $allGood = $true
    foreach ($tool in $tools.Keys) {
        $isInstalled = & $tools[$tool]
        if ($isInstalled) {
            Write-ColorOutput "  ✅ $tool is installed" $Green
        }
        else {
            Write-ColorOutput "  ❌ $tool is NOT installed" $Red
            $allGood = $false
        }
    }
    
    return $allGood
}

function Install-VSCodeExtensions {
    Write-ColorOutput "📦 Installing recommended VSCode extensions..." $Yellow
    
    $extensions = @(
        "vscjava.vscode-java-pack",
        "vscjava.vscode-spring-initializr",
        "vscjava.vscode-spring-boot-dashboard",
        "pivotal.vscode-spring-boot",
        "ms-python.python",
        "ms-python.black-formatter",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "ms-kubernetes-tools.vscode-kubernetes-tools",
        "ms-azuretools.vscode-docker",
        "hashicorp.terraform",
        "ms-vscode.powershell",
        "yzhang.markdown-all-in-one"
    )
    
    foreach ($extension in $extensions) {
        Write-ColorOutput "  Installing $extension..." $Cyan
        try {
            code --install-extension $extension --force
            Write-ColorOutput "  ✅ Installed $extension" $Green
        }
        catch {
            Write-ColorOutput "  ❌ Failed to install $extension" $Red
        }
    }
}

function Initialize-Environment {
    Write-ColorOutput "🛠️ Setting up development environment..." $Yellow
    
    # Create .vscode directory if it doesn't exist
    if (-not (Test-Path ".vscode")) {
        New-Item -ItemType Directory -Path ".vscode" -Force
        Write-ColorOutput "  ✅ Created .vscode directory" $Green
    }
    
    # Check if Python virtual environment exists
    if (-not (Test-Path "backend/comprehensive-system/venv")) {
        Write-ColorOutput "  🐍 Creating Python virtual environment..." $Cyan
        try {
            Set-Location "backend/comprehensive-system"
            python -m venv venv
            Write-ColorOutput "  ✅ Created Python virtual environment" $Green
            Set-Location "../.."
        }
        catch {
            Write-ColorOutput "  ❌ Failed to create Python virtual environment" $Red
            Set-Location "../.."
        }
    }
    
    # Check if Node.js dependencies are installed
    if (-not (Test-Path "frontend/admin-dashboard/node_modules")) {
        Write-ColorOutput "  📦 Installing Node.js dependencies..." $Cyan
        try {
            Set-Location "frontend/admin-dashboard"
            npm install
            Write-ColorOutput "  ✅ Installed Node.js dependencies" $Green
            Set-Location "../.."
        }
        catch {
            Write-ColorOutput "  ❌ Failed to install Node.js dependencies" $Red
            Set-Location "../.."
        }
    }
}

function Open-Workspace {
    Write-ColorOutput "🚀 Opening TecnoDrive Multi-Root Workspace..." $Blue
    
    if (Test-Path "tecno-drive-workspace.code-workspace") {
        try {
            code tecno-drive-workspace.code-workspace
            Write-ColorOutput "✅ Workspace opened successfully!" $Green
            Write-ColorOutput ""
            Write-ColorOutput "📋 Quick Tips:" $Yellow
            Write-ColorOutput "  • Use Ctrl+Shift+P to access tasks" $Cyan
            Write-ColorOutput "  • Use Ctrl+P to quickly find files" $Cyan
            Write-ColorOutput "  • Check the Tasks menu for build/run options" $Cyan
            Write-ColorOutput "  • Each service has its own debug configuration" $Cyan
        }
        catch {
            Write-ColorOutput "❌ Failed to open workspace" $Red
            Write-ColorOutput "Error: $_" $Red
        }
    }
    else {
        Write-ColorOutput "❌ Workspace file not found: tecno-drive-workspace.code-workspace" $Red
        Write-ColorOutput "Please make sure you're in the correct directory." $Yellow
    }
}

# Main execution
Write-ColorOutput "🚀 TecnoDrive Platform - Multi-Root Workspace Launcher" $Blue
Write-ColorOutput "=" * 60 $Blue

if ($Help) {
    Show-Help
    exit 0
}

if ($CheckHealth) {
    $healthCheck = Test-RequiredTools
    if (-not $healthCheck) {
        Write-ColorOutput ""
        Write-ColorOutput "⚠️ Some required tools are missing. Please install them before proceeding." $Yellow
        exit 1
    }
    Write-ColorOutput "✅ All required tools are installed!" $Green
}

if ($SetupEnvironment) {
    Initialize-Environment
}

if ($InstallExtensions) {
    if (-not (Test-VSCodeInstalled)) {
        Write-ColorOutput "❌ VSCode is not installed. Please install VSCode first." $Red
        exit 1
    }
    Install-VSCodeExtensions
}

Open-Workspace

Write-ColorOutput ""
Write-ColorOutput "🎉 Happy coding with TecnoDrive Platform!" $Green
