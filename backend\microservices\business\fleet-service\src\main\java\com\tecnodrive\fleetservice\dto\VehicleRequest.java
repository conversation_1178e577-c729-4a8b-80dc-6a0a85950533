package com.tecnodrive.fleetservice.dto;

import com.tecnodrive.fleetservice.entity.Vehicle;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Vehicle Request DTO
 * 
 * Used for creating and updating vehicles
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VehicleRequest {

    /**
     * Unique vehicle plate number
     */
    @NotBlank(message = "Plate number is required")
    @Size(max = 20, message = "Plate number cannot exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9\\-\\s]+$", message = "Plate number must contain only letters, numbers, hyphens, and spaces")
    private String plateNumber;

    /**
     * Vehicle identification number (VIN)
     */
    @Size(max = 50, message = "VIN cannot exceed 50 characters")
    private String vin;

    /**
     * Vehicle make/manufacturer
     */
    @NotBlank(message = "Make is required")
    @Size(max = 50, message = "Make cannot exceed 50 characters")
    private String make;

    /**
     * Vehicle model
     */
    @NotBlank(message = "Model is required")
    @Size(max = 50, message = "Model cannot exceed 50 characters")
    private String model;

    /**
     * Manufacturing year
     */
    @NotNull(message = "Year is required")
    @Min(value = 1900, message = "Year must be 1900 or later")
    @Max(value = 2030, message = "Year cannot be in the future beyond 2030")
    private Integer year;

    /**
     * Vehicle color
     */
    @Size(max = 30, message = "Color cannot exceed 30 characters")
    private String color;

    /**
     * Passenger capacity
     */
    @NotNull(message = "Capacity is required")
    @Min(value = 1, message = "Capacity must be at least 1")
    @Max(value = 100, message = "Capacity cannot exceed 100")
    private Integer capacity;

    /**
     * Vehicle type
     */
    @NotNull(message = "Vehicle type is required")
    private Vehicle.VehicleType vehicleType;

    /**
     * Fuel type
     */
    @NotNull(message = "Fuel type is required")
    private Vehicle.FuelType fuelType;

    /**
     * Current vehicle status
     */
    private Vehicle.VehicleStatus status;

    /**
     * Current odometer reading (in kilometers)
     */
    @DecimalMin(value = "0.0", message = "Odometer reading cannot be negative")
    @Digits(integer = 8, fraction = 2, message = "Odometer reading must have at most 8 integer digits and 2 decimal places")
    private BigDecimal odometerReading;

    /**
     * Engine capacity (in liters)
     */
    @DecimalMin(value = "0.1", message = "Engine capacity must be at least 0.1 liters")
    @DecimalMax(value = "20.0", message = "Engine capacity cannot exceed 20.0 liters")
    @Digits(integer = 2, fraction = 2, message = "Engine capacity must have at most 2 integer digits and 2 decimal places")
    private BigDecimal engineCapacity;

    /**
     * Transmission type
     */
    private Vehicle.TransmissionType transmissionType;

    /**
     * Currently assigned driver ID
     */
    private String assignedDriverId;

    /**
     * Company/Tenant ID
     */
    @NotBlank(message = "Company ID is required")
    private String companyId;

    /**
     * Vehicle registration date
     */
    private LocalDate registrationDate;

    /**
     * Registration expiry date
     */
    private LocalDate registrationExpiryDate;

    /**
     * Insurance policy number
     */
    @Size(max = 100, message = "Insurance policy number cannot exceed 100 characters")
    private String insurancePolicyNumber;

    /**
     * Insurance expiry date
     */
    private LocalDate insuranceExpiryDate;

    /**
     * Last maintenance date
     */
    private LocalDate lastMaintenanceDate;

    /**
     * Next scheduled maintenance date
     */
    private LocalDate nextMaintenanceDate;

    /**
     * Odometer reading at last maintenance
     */
    @DecimalMin(value = "0.0", message = "Last maintenance odometer cannot be negative")
    @Digits(integer = 8, fraction = 2, message = "Last maintenance odometer must have at most 8 integer digits and 2 decimal places")
    private BigDecimal lastMaintenanceOdometer;

    /**
     * Purchase date
     */
    private LocalDate purchaseDate;

    /**
     * Purchase price
     */
    @DecimalMin(value = "0.0", message = "Purchase price cannot be negative")
    @Digits(integer = 10, fraction = 2, message = "Purchase price must have at most 10 integer digits and 2 decimal places")
    private BigDecimal purchasePrice;

    /**
     * Current market value
     */
    @DecimalMin(value = "0.0", message = "Current value cannot be negative")
    @Digits(integer = 10, fraction = 2, message = "Current value must have at most 10 integer digits and 2 decimal places")
    private BigDecimal currentValue;

    /**
     * Average fuel consumption (km per liter)
     */
    @DecimalMin(value = "0.1", message = "Fuel consumption must be at least 0.1 km/l")
    @DecimalMax(value = "100.0", message = "Fuel consumption cannot exceed 100.0 km/l")
    @Digits(integer = 3, fraction = 2, message = "Fuel consumption must have at most 3 integer digits and 2 decimal places")
    private BigDecimal fuelConsumption;

    /**
     * Vehicle notes/comments
     */
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    private String notes;

    /**
     * GPS tracking device ID
     */
    @Size(max = 50, message = "GPS device ID cannot exceed 50 characters")
    private String gpsDeviceId;

    /**
     * Whether vehicle is active in the fleet
     */
    private Boolean isActive;
}
