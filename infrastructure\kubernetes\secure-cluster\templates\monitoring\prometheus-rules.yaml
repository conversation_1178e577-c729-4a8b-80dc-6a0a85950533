{{- if .Values.monitoring.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tecno-drive-security-alerts
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: security-alerts
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
    - name: tecno-drive-security-alerts
      interval: 30s
      rules:
        # Critical Security Violations
        - alert: CriticalSecurityViolation
          expr: |
            increase(gatekeeper_violations_total{
              constraint="centralsecurity",
              violation_kind="security",
              severity="critical"
            }[5m]) > {{ .Values.monitoring.rules.security.criticalThreshold | default 0 }}
          for: 1m
          labels:
            severity: critical
            team: security
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "🚨 مخالفة أمنية حرجة في TECNO DRIVE"
            description: |
              تم اكتشاف {{ "{{ $value }}" }} مخالفات أمنية حرجة في النظام.
              
              التفاصيل:
              - النوع: {{ "{{ $labels.violation_kind }}" }}
              - المصدر: {{ "{{ $labels.resource_namespace }}" }}/{{ "{{ $labels.resource_name }}" }}
              - السياسة: {{ "{{ $labels.constraint }}" }}
              
              الإجراءات المطلوبة:
              1. مراجعة فورية للمورد المخالف
              2. تطبيق الإصلاحات الأمنية
              3. التحقق من عدم وجود مخالفات مماثلة
            runbook_url: "https://docs.tecnodrive.com/security/critical-violations"
        
        # High Security Violations
        - alert: HighSecurityViolation
          expr: |
            increase(gatekeeper_violations_total{
              constraint="centralsecurity",
              violation_kind="security",
              severity="high"
            }[15m]) > {{ .Values.monitoring.rules.security.highThreshold | default 2 }}
          for: 5m
          labels:
            severity: warning
            team: security
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "⚠️ مخالفات أمنية عالية الخطورة"
            description: |
              تم اكتشاف {{ "{{ $value }}" }} مخالفات أمنية عالية الخطورة.
              
              يرجى مراجعة وإصلاح هذه المخالفات في أقرب وقت ممكن.
        
        # Resilience Issues
        - alert: ResilienceViolation
          expr: |
            increase(gatekeeper_violations_total{
              constraint="centralsecurity",
              violation_kind="resilience"
            }[30m]) > {{ .Values.monitoring.rules.resilience.threshold | default 5 }}
          for: 10m
          labels:
            severity: warning
            team: platform
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "🔧 مشاكل في مرونة النظام"
            description: |
              تم اكتشاف {{ "{{ $value }}" }} مخالفات في مرونة النظام.
              
              المشاكل الشائعة:
              - نقص في PodDisruptionBudgets
              - عدم وجود Health Probes
              - تكوين غير صحيح للموارد
        
        # Network Security Issues
        - alert: NetworkSecurityViolation
          expr: |
            increase(gatekeeper_violations_total{
              constraint="centralsecurity",
              violation_kind="network"
            }[30m]) > {{ .Values.monitoring.rules.network.threshold | default 3 }}
          for: 15m
          labels:
            severity: warning
            team: network
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "🌐 مخالفات أمان الشبكة"
            description: |
              تم اكتشاف {{ "{{ $value }}" }} مخالفات في أمان الشبكة.
              
              يرجى التحقق من:
              - وجود NetworkPolicies
              - تكوين Ingress الآمن
              - عزل الشبكات بين الخدمات

    - name: tecno-drive-gatekeeper-health
      interval: 30s
      rules:
        # Gatekeeper Down
        - alert: GatekeeperDown
          expr: |
            up{job="gatekeeper-controller-manager-metrics-service"} == 0
          for: 2m
          labels:
            severity: critical
            team: platform
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "🚨 Gatekeeper غير متاح"
            description: |
              Gatekeeper Controller Manager غير متاح.
              
              هذا يعني أن السياسات الأمنية لا يتم تطبيقها!
              
              الإجراءات الفورية:
              1. فحص حالة pods في gatekeeper-system
              2. مراجعة logs للأخطاء
              3. إعادة تشغيل إذا لزم الأمر
            runbook_url: "https://docs.tecnodrive.com/gatekeeper/troubleshooting"
        
        # Gatekeeper Webhook Latency
        - alert: GatekeeperHighLatency
          expr: |
            histogram_quantile(0.99, 
              rate(gatekeeper_webhook_request_duration_seconds_bucket[5m])
            ) > 1
          for: 5m
          labels:
            severity: warning
            team: platform
            component: gatekeeper
            service: tecno-drive
          annotations:
            summary: "⏱️ بطء في استجابة Gatekeeper"
            description: |
              Gatekeeper Webhook يستغرق وقتاً طويلاً للاستجابة.
              
              الزمن الحالي: {{ "{{ $value }}" }}s
              
              قد يؤثر هذا على أداء نشر التطبيقات.

    - name: tecno-drive-application-health
      interval: 30s
      rules:
        # Application Pod Restarts
        - alert: HighPodRestartRate
          expr: |
            increase(kube_pod_container_status_restarts_total{
              namespace=~"tecno-drive.*"
            }[15m]) > 3
          for: 5m
          labels:
            severity: warning
            team: application
            component: "{{ "{{ $labels.container }}" }}"
            service: tecno-drive
          annotations:
            summary: "🔄 معدل إعادة تشغيل عالي للحاويات"
            description: |
              الحاوية {{ "{{ $labels.container }}" }} في Pod {{ "{{ $labels.pod }}" }}
              تم إعادة تشغيلها {{ "{{ $value }}" }} مرات في آخر 15 دقيقة.
              
              يرجى التحقق من:
              - صحة التطبيق
              - توفر الموارد
              - تكوين Health Probes
        
        # Missing Probes
        - alert: MissingHealthProbes
          expr: |
            kube_deployment_status_replicas{namespace=~"tecno-drive.*"} > 0
            unless on(deployment, namespace) 
            kube_deployment_spec_template_spec_containers_liveness_probe{namespace=~"tecno-drive.*"}
          for: 10m
          labels:
            severity: warning
            team: application
            component: "{{ "{{ $labels.deployment }}" }}"
            service: tecno-drive
          annotations:
            summary: "🏥 نقص في Health Probes"
            description: |
              Deployment {{ "{{ $labels.deployment }}" }} في namespace {{ "{{ $labels.namespace }}" }}
              لا يحتوي على Liveness Probe.
              
              هذا قد يؤثر على:
              - اكتشاف الأعطال
              - الاستجابة للمشاكل
              - جودة الخدمة

    - name: tecno-drive-resource-usage
      interval: 30s
      rules:
        # High CPU Usage
        - alert: HighCPUUsage
          expr: |
            (
              rate(container_cpu_usage_seconds_total{
                namespace=~"tecno-drive.*",
                container!="POD",
                container!=""
              }[5m]) * 100
            ) > {{ .Values.monitoring.rules.performance.cpuThreshold | default 80 }}
          for: 10m
          labels:
            severity: warning
            team: platform
            component: "{{ "{{ $labels.container }}" }}"
            service: tecno-drive
          annotations:
            summary: "📊 استخدام عالي لوحدة المعالجة المركزية"
            description: |
              الحاوية {{ "{{ $labels.container }}" }} في Pod {{ "{{ $labels.pod }}" }}
              تستخدم {{ "{{ $value }}" }}% من وحدة المعالجة المركزية.
              
              يرجى التحقق من:
              - كفاءة التطبيق
              - تكوين Resource Limits
              - الحاجة لتوسيع النطاق
        
        # High Memory Usage
        - alert: HighMemoryUsage
          expr: |
            (
              container_memory_working_set_bytes{
                namespace=~"tecno-drive.*",
                container!="POD",
                container!=""
              } / 
              container_spec_memory_limit_bytes{
                namespace=~"tecno-drive.*",
                container!="POD",
                container!=""
              } * 100
            ) > {{ .Values.monitoring.rules.performance.memoryThreshold | default 85 }}
          for: 10m
          labels:
            severity: warning
            team: platform
            component: "{{ "{{ $labels.container }}" }}"
            service: tecno-drive
          annotations:
            summary: "💾 استخدام عالي للذاكرة"
            description: |
              الحاوية {{ "{{ $labels.container }}" }} في Pod {{ "{{ $labels.pod }}" }}
              تستخدم {{ "{{ $value }}" }}% من الذاكرة المحددة.
              
              قد تحتاج إلى:
              - زيادة Memory Limits
              - تحسين استخدام الذاكرة
              - مراجعة تسريبات الذاكرة

    - name: tecno-drive-business-metrics
      interval: 60s
      rules:
        # API Gateway Error Rate
        - alert: HighAPIErrorRate
          expr: |
            (
              rate(http_requests_total{
                service="api-gateway",
                status=~"5.."
              }[5m]) /
              rate(http_requests_total{
                service="api-gateway"
              }[5m]) * 100
            ) > 5
          for: 5m
          labels:
            severity: critical
            team: backend
            component: api-gateway
            service: tecno-drive
          annotations:
            summary: "🚪 معدل أخطاء عالي في API Gateway"
            description: |
              API Gateway يواجه معدل أخطاء {{ "{{ $value }}" }}%.
              
              هذا قد يؤثر على جميع خدمات TECNO DRIVE.
        
        # Database Connection Issues
        - alert: DatabaseConnectionIssues
          expr: |
            increase(database_connection_errors_total{
              namespace=~"tecno-drive.*"
            }[10m]) > 10
          for: 5m
          labels:
            severity: critical
            team: database
            component: "{{ "{{ $labels.service }}" }}"
            service: tecno-drive
          annotations:
            summary: "🗄️ مشاكل في الاتصال بقاعدة البيانات"
            description: |
              الخدمة {{ "{{ $labels.service }}" }} تواجه مشاكل في الاتصال بقاعدة البيانات.
              
              عدد الأخطاء: {{ "{{ $value }}" }} في آخر 10 دقائق.
{{- end }}
