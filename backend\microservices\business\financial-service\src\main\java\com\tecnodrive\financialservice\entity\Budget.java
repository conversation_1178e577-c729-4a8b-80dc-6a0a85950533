package com.tecnodrive.financialservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Instant;
import java.util.UUID;

/**
 * Budget Entity
 * 
 * Manages budget planning and tracking for different categories and periods.
 * Supports budget monitoring and alert generation.
 */
@Entity
@Table(name = "budgets")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class Budget {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * Budget name/title
     */
    @Column(nullable = false, length = 200)
    private String budgetName;

    /**
     * Budget category
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BudgetCategory category;

    /**
     * Budget subcategory for detailed tracking
     */
    @Column(length = 100)
    private String subcategory;

    /**
     * Budget period type
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BudgetPeriod period;

    /**
     * Period start date
     */
    @Column(nullable = false)
    private LocalDate periodStartDate;

    /**
     * Period end date
     */
    @Column(nullable = false)
    private LocalDate periodEndDate;

    /**
     * Allocated/Planned amount
     */
    @Column(nullable = false, precision = 19, scale = 2)
    private BigDecimal allocatedAmount;

    /**
     * Actually spent amount
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal actualSpent = BigDecimal.ZERO;

    /**
     * Committed amount (pending expenses)
     */
    @Column(precision = 19, scale = 2)
    @Builder.Default
    private BigDecimal committedAmount = BigDecimal.ZERO;

    /**
     * Available amount (allocated - actual - committed)
     */
    @Column(precision = 19, scale = 2)
    private BigDecimal availableAmount;

    /**
     * Currency code
     */
    @Column(nullable = false, length = 3)
    @Builder.Default
    private String currency = "USD";

    /**
     * Budget status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private BudgetStatus status = BudgetStatus.ACTIVE;

    /**
     * Alert threshold percentage (0.0 to 1.0)
     */
    @Column(precision = 3, scale = 2)
    @Builder.Default
    private BigDecimal alertThreshold = new BigDecimal("0.80");

    /**
     * Whether alerts are enabled
     */
    @Builder.Default
    private boolean alertsEnabled = true;

    /**
     * Last alert sent date
     */
    private Instant lastAlertSent;

    /**
     * Budget description/notes
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * Budget owner/manager
     */
    @Column(nullable = false)
    private String ownerId;

    /**
     * Company/Tenant ID
     */
    @Column(nullable = false)
    private String companyId;

    /**
     * Parent budget ID for hierarchical budgets
     */
    private UUID parentBudgetId;

    /**
     * Additional metadata (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @LastModifiedDate
    @Column(nullable = false)
    private Instant updatedAt;

    /**
     * Budget Category Enum
     */
    public enum BudgetCategory {
        OPERATIONAL,
        MARKETING,
        TECHNOLOGY,
        HUMAN_RESOURCES,
        FACILITIES,
        VEHICLE_FLEET,
        MAINTENANCE,
        INSURANCE,
        LEGAL_COMPLIANCE,
        RESEARCH_DEVELOPMENT,
        CAPITAL_EXPENDITURE,
        CONTINGENCY,
        OTHER
    }

    /**
     * Budget Period Enum
     */
    public enum BudgetPeriod {
        MONTHLY,
        QUARTERLY,
        SEMI_ANNUAL,
        ANNUAL,
        CUSTOM
    }

    /**
     * Budget Status Enum
     */
    public enum BudgetStatus {
        DRAFT,
        ACTIVE,
        SUSPENDED,
        COMPLETED,
        CANCELLED
    }

    /**
     * Calculate available amount
     */
    @PrePersist
    @PreUpdate
    private void calculateAvailableAmount() {
        if (allocatedAmount != null) {
            BigDecimal actual = actualSpent != null ? actualSpent : BigDecimal.ZERO;
            BigDecimal committed = committedAmount != null ? committedAmount : BigDecimal.ZERO;
            this.availableAmount = allocatedAmount.subtract(actual).subtract(committed);
        }
    }

    /**
     * Get budget utilization percentage
     */
    public BigDecimal getUtilizationPercentage() {
        if (allocatedAmount == null || allocatedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal actual = actualSpent != null ? actualSpent : BigDecimal.ZERO;
        return actual.divide(allocatedAmount, 4, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
    }

    /**
     * Check if budget is over threshold
     */
    public boolean isOverThreshold() {
        if (alertThreshold == null || allocatedAmount == null || 
            allocatedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        
        BigDecimal utilization = getUtilizationPercentage().divide(new BigDecimal("100"));
        return utilization.compareTo(alertThreshold) >= 0;
    }

    /**
     * Check if budget is exceeded
     */
    public boolean isExceeded() {
        if (allocatedAmount == null) {
            return false;
        }
        
        BigDecimal actual = actualSpent != null ? actualSpent : BigDecimal.ZERO;
        return actual.compareTo(allocatedAmount) > 0;
    }

    /**
     * Get remaining amount
     */
    public BigDecimal getRemainingAmount() {
        if (allocatedAmount == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal actual = actualSpent != null ? actualSpent : BigDecimal.ZERO;
        BigDecimal remaining = allocatedAmount.subtract(actual);
        return remaining.max(BigDecimal.ZERO);
    }

    /**
     * Check if budget period is active
     */
    public boolean isPeriodActive() {
        LocalDate now = LocalDate.now();
        return !now.isBefore(periodStartDate) && !now.isAfter(periodEndDate);
    }
}
