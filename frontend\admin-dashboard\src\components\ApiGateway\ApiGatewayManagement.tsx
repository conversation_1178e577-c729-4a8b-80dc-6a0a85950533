import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Stack,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Route as RouteIcon,
  Security as SecurityIcon,
  Apps as AppsIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  CheckCircle as CheckCircleIcon,
  Api as ApiIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import RoutesManagement from './RoutesManagement';
import AuthMethodsManagement from './AuthMethodsManagement';
import ConsumersManagement from './ConsumersManagement';
import GatewayMetrics from './GatewayMetrics';
import RateLimitingConfig from './RateLimitingConfig';
import GatewayDashboard from './GatewayDashboard';
import ExternalAppsManagement from './ExternalAppsManagement';
import AdvancedMonitoring from './AdvancedMonitoring';
import GraphQLGateway from './GraphQLGateway';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`gateway-tabpanel-${index}`}
      aria-labelledby={`gateway-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ApiGatewayManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine active tab based on current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/routes')) return 1;
    if (path.includes('/auth-methods')) return 2;
    if (path.includes('/consumers')) return 3;
    if (path.includes('/metrics')) return 4;
    if (path.includes('/settings')) return 5;
    return 0; // Dashboard
  };

  const [tabValue, setTabValue] = React.useState(getActiveTab());

  React.useEffect(() => {
    setTabValue(getActiveTab());
  }, [location.pathname]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    const routes = ['', 'routes', 'auth-methods', 'consumers', 'metrics', 'settings'];
    navigate(`/api-gateway/${routes[newValue]}`);
  };

  // Mock data for dashboard
  const gatewayOverviewData = [
    { hour: '00:00', requests: 1200, responseTime: 245 },
    { hour: '04:00', requests: 800, responseTime: 220 },
    { hour: '08:00', requests: 2500, responseTime: 280 },
    { hour: '12:00', requests: 3200, responseTime: 310 },
    { hour: '16:00', requests: 2800, responseTime: 290 },
    { hour: '20:00', requests: 1800, responseTime: 250 },
  ];

  const routeDistribution = [
    { name: 'Auth Service', value: 25, color: '#4caf50' },
    { name: 'Fleet Service', value: 30, color: '#2196f3' },
    { name: 'Rides Service', value: 35, color: '#ff9800' },
    { name: 'Others', value: 10, color: '#9c27b0' },
  ];

  const recentActivities = [
    { id: 1, type: 'route', message: 'تم إضافة مسار جديد: /api/payments/**', time: '5 دقائق' },
    { id: 2, type: 'consumer', message: 'تم تسجيل مستهلك جديد: Mobile App v2.0', time: '15 دقيقة' },
    { id: 3, type: 'auth', message: 'تم تحديث طريقة مصادقة JWT', time: '30 دقيقة' },
    { id: 4, type: 'alert', message: 'تحذير: معدل الأخطاء مرتفع في /api/rides/**', time: '45 دقيقة' },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'route': return <RouteIcon />;
      case 'consumer': return <AppsIcon />;
      case 'auth': return <SecurityIcon />;
      case 'alert': return <TrendingUpIcon />;
      default: return <DashboardIcon />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'route': return 'primary';
      case 'consumer': return 'success';
      case 'auth': return 'info';
      case 'alert': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Routes>
      <Route path="/routes/*" element={<RoutesManagement />} />
      <Route path="/auth-methods" element={<AuthMethodsManagement />} />
      <Route path="/consumers" element={<ConsumersManagement />} />
      <Route path="/metrics" element={<GatewayMetrics />} />
      <Route path="/rate-limiting" element={<RateLimitingConfig />} />
      <Route path="/*" element={
        <Box>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              إدارة API Gateway
            </Typography>
            <Typography variant="body1" color="text.secondary">
              لوحة تحكم شاملة لإدارة وتكوين ومراقبة API Gateway
            </Typography>
          </Box>

          {/* Navigation Tabs */}
          <Card sx={{ mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab 
                  icon={<DashboardIcon />} 
                  label="لوحة التحكم" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<RouteIcon />} 
                  label="المسارات" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<SecurityIcon />} 
                  label="طرق المصادقة" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<AppsIcon />} 
                  label="المستهلكين" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<AnalyticsIcon />} 
                  label="المراقبة" 
                  iconPosition="start"
                />
                <Tab 
                  icon={<SettingsIcon />} 
                  label="الإعدادات" 
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            {/* Dashboard Tab Content */}
            <TabPanel value={tabValue} index={0}>
              {/* Quick Stats */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <ApiIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            326,000
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            إجمالي الطلبات
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <CheckCircleIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            97.8%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            معدل النجاح
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'warning.main' }}>
                          <SpeedIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            245ms
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            متوسط زمن الاستجابة
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'info.main' }}>
                          <TrendingUpIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            125.5
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            طلبات/ثانية
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Charts and Analytics */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, md: 8 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        حركة المرور خلال 24 ساعة
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={gatewayOverviewData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="hour" />
                          <YAxis />
                          <Tooltip />
                          <Line 
                            type="monotone" 
                            dataKey="requests" 
                            stroke="#1976d2" 
                            strokeWidth={3}
                            name="عدد الطلبات"
                          />
                          <Line 
                            type="monotone" 
                            dataKey="responseTime" 
                            stroke="#ff9800" 
                            strokeWidth={3}
                            name="زمن الاستجابة (ms)"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        توزيع الطلبات حسب الخدمة
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={routeDistribution}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                          >
                            {routeDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Recent Activities and Quick Actions */}
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 8 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        الأنشطة الأخيرة
                      </Typography>
                      <List>
                        {recentActivities.map((activity) => (
                          <ListItem key={activity.id}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: `${getActivityColor(activity.type)}.main` }}>
                                {getActivityIcon(activity.type)}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={activity.message}
                              secondary={`منذ ${activity.time}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        إجراءات سريعة
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button
                          variant="contained"
                          startIcon={<RouteIcon />}
                          onClick={() => navigate('/api-gateway/routes')}
                          fullWidth
                        >
                          إضافة مسار جديد
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<AppsIcon />}
                          onClick={() => navigate('/api-gateway/consumers')}
                          fullWidth
                        >
                          تسجيل مستهلك جديد
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<SecurityIcon />}
                          onClick={() => navigate('/api-gateway/auth-methods')}
                          fullWidth
                        >
                          إضافة طريقة مصادقة
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<AnalyticsIcon />}
                          onClick={() => navigate('/api-gateway/metrics')}
                          fullWidth
                        >
                          عرض المراقبة
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* System Health */}
              <Grid container spacing={3} sx={{ mt: 3 }}>
                <Grid size={{ xs: 12 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        حالة النظام
                      </Typography>
                      <Grid container spacing={2}>
                        {[
                          { name: 'API Gateway', status: 'UP', load: 45 },
                          { name: 'Auth Service', status: 'UP', load: 67 },
                          { name: 'Fleet Service', status: 'UP', load: 52 },
                          { name: 'Rides Service', status: 'UP', load: 78 },
                          { name: 'Notifications', status: 'UP', load: 38 },
                          { name: 'SaaS Service', status: 'UP', load: 71 },
                        ].map((service, index) => (
                          <Grid size={{ xs: 12, sm: 6, md: 2 }} key={index}>
                            <Card variant="outlined">
                              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                                <Avatar 
                                  sx={{ 
                                    bgcolor: service.status === 'UP' ? 'success.main' : 'error.main',
                                    mx: 'auto',
                                    mb: 1,
                                    width: 32,
                                    height: 32
                                  }}
                                >
                                  <CheckCircleIcon fontSize="small" />
                                </Avatar>
                                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                                  {service.name}
                                </Typography>
                                <Chip 
                                  label={service.status} 
                                  size="small" 
                                  color="success"
                                  sx={{ mb: 1 }}
                                />
                                <Typography variant="caption" display="block" color="text.secondary">
                                  Load: {service.load}%
                                </Typography>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Other tabs will show their respective components */}
            <TabPanel value={tabValue} index={1}>
              <RoutesManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
              <AuthMethodsManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={3}>
              <ConsumersManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={4}>
              <GatewayMetrics />
            </TabPanel>
            <TabPanel value={tabValue} index={5}>
              <Box>
                <Typography variant="h5" sx={{ mb: 2 }}>
                  إعدادات API Gateway
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  قريباً - إعدادات متقدمة لـ API Gateway
                </Typography>
              </Box>
            </TabPanel>
          </Card>
        </Box>
      } />
    </Routes>
  );
};

export default ApiGatewayManagement;
