-- =====================================================
-- TECNO DRIVE - Database Migration V2024.01.15
-- تحديث قاعدة البيانات للهيكل المحسن
-- =====================================================

-- تفعيل الامتدادات المطلوبة (إذا لم تكن مفعلة)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- 1. إنشاء ENUMs الجديدة
-- =====================================================

-- إنشاء ENUMs إذا لم تكن موجودة
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'trip_status_enum') THEN
        CREATE TYPE trip_status_enum AS ENUM (
            'pending', 'accepted', 'in_progress', 'completed', 'cancelled', 'delayed'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'parcel_status_enum') THEN
        CREATE TYPE parcel_status_enum AS ENUM (
            'Created', 'PickedUp', 'InTransit', 'OutForDelivery', 
            'Delivered', 'Returned', 'Cancelled', 'Lost', 'Damaged'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ticket_status_enum') THEN
        CREATE TYPE ticket_status_enum AS ENUM (
            'open', 'in_progress', 'resolved', 'closed', 'escalated'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'parcel_priority_enum') THEN
        CREATE TYPE parcel_priority_enum AS ENUM (
            'LOW', 'MEDIUM', 'HIGH', 'URGENT'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method_enum') THEN
        CREATE TYPE payment_method_enum AS ENUM (
            'cash', 'card', 'wallet', 'bank_transfer', 'mobile_payment'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_status_enum') THEN
        CREATE TYPE payment_status_enum AS ENUM (
            'pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'vehicle_type_enum') THEN
        CREATE TYPE vehicle_type_enum AS ENUM (
            'sedan', 'suv', 'van', 'bus', 'truck', 'motorcycle'
        );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'vehicle_status_enum') THEN
        CREATE TYPE vehicle_status_enum AS ENUM (
            'active', 'inactive', 'maintenance', 'retired'
        );
    END IF;
END $$;

-- =====================================================
-- 2. تحديث الجداول الموجودة
-- =====================================================

-- تحديث جدول المستخدمين
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS date_of_birth DATE,
ADD COLUMN IF NOT EXISTS gender TEXT CHECK (gender IN ('male', 'female')),
ADD COLUMN IF NOT EXISTS profile_image TEXT,
ADD COLUMN IF NOT EXISTS language TEXT DEFAULT 'ar',
ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'Asia/Aden',
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS deleted_by UUID REFERENCES users(id);

-- تحديث جدول الحجوزات
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS from_latitude DECIMAL(10,8),
ADD COLUMN IF NOT EXISTS from_longitude DECIMAL(11,8),
ADD COLUMN IF NOT EXISTS to_latitude DECIMAL(10,8),
ADD COLUMN IF NOT EXISTS to_longitude DECIMAL(11,8),
ADD COLUMN IF NOT EXISTS passenger_names JSONB,
ADD COLUMN IF NOT EXISTS actual_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS booking_type TEXT DEFAULT 'regular',
ADD COLUMN IF NOT EXISTS special_requests TEXT,
ADD COLUMN IF NOT EXISTS cancellation_reason TEXT,
ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS cancelled_by UUID REFERENCES users(id);

-- تحديث جدول الطرود
ALTER TABLE parcels 
ADD COLUMN IF NOT EXISTS sender_phone TEXT,
ADD COLUMN IF NOT EXISTS receiver_phone TEXT,
ADD COLUMN IF NOT EXISTS pickup_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS delivery_attempts INTEGER DEFAULT 0;

-- تحديث نوع البيانات للحقول الموجودة
DO $$
BEGIN
    -- تحديث حالة الطرود إلى ENUM
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'parcels' AND column_name = 'status' 
               AND data_type = 'text') THEN
        ALTER TABLE parcels ALTER COLUMN status TYPE parcel_status_enum 
        USING status::parcel_status_enum;
    END IF;
    
    -- تحديث أولوية الطرود إلى ENUM
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'parcels' AND column_name = 'priority' 
               AND data_type = 'text') THEN
        ALTER TABLE parcels ALTER COLUMN priority TYPE parcel_priority_enum 
        USING priority::parcel_priority_enum;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- تجاهل الأخطاء إذا كانت الأعمدة غير موجودة
        NULL;
END $$;

-- =====================================================
-- 3. إنشاء الجداول الجديدة
-- =====================================================

-- جدول جلسات المستخدمين
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    access_token    TEXT      NOT NULL,
    refresh_token   TEXT      NOT NULL,
    device_info     JSONB,
    ip_address      INET,
    user_agent      TEXT,
    is_active       BOOLEAN   DEFAULT TRUE,
    expires_at      TIMESTAMP NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحجوزات الموسعة
CREATE TABLE IF NOT EXISTS bookings_extended (
    id              UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id      TEXT      NOT NULL REFERENCES bookings(booking_id),
    promo_code_id   UUID,
    extra_fee       DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount      DECIMAL(10,2) DEFAULT 0,
    service_fee     DECIMAL(10,2) DEFAULT 0,
    total_amount    DECIMAL(10,2),
    loyalty_points_earned INTEGER DEFAULT 0,
    loyalty_points_used INTEGER DEFAULT 0,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العروض الترويجية
CREATE TABLE IF NOT EXISTS promotions (
    promo_code_id   UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    code            TEXT      UNIQUE NOT NULL,
    title           TEXT      NOT NULL,
    description     TEXT,
    discount_type   TEXT      NOT NULL CHECK (discount_type IN ('percentage', 'fixed', 'free_shipping')),
    value           DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_discount    DECIMAL(10,2),
    usage_limit     INTEGER,
    used_count      INTEGER   DEFAULT 0,
    user_limit      INTEGER   DEFAULT 1,
    is_active       BOOLEAN   DEFAULT TRUE,
    starts_at       TIMESTAMP NOT NULL,
    expires_at      TIMESTAMP NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة المرجع للعروض الترويجية
ALTER TABLE bookings_extended 
ADD CONSTRAINT fk_bookings_extended_promo 
FOREIGN KEY (promo_code_id) REFERENCES promotions(promo_code_id);

-- جدول الطرق
CREATE TABLE IF NOT EXISTS routes (
    route_id        UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    route_name      TEXT      NOT NULL,
    from_location   TEXT      NOT NULL,
    to_location     TEXT      NOT NULL,
    from_latitude   DECIMAL(10,8),
    from_longitude  DECIMAL(11,8),
    to_latitude     DECIMAL(10,8),
    to_longitude    DECIMAL(11,8),
    distance_km     DECIMAL(8,2),
    estimated_duration INTERVAL,
    base_price      DECIMAL(10,2),
    price_per_km    DECIMAL(10,2),
    is_active       BOOLEAN   DEFAULT TRUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الكباتن
CREATE TABLE IF NOT EXISTS captains (
    captain_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id),
    license_number  TEXT      UNIQUE NOT NULL,
    license_expiry  DATE,
    rating          DECIMAL(3,2) DEFAULT 0.00,
    total_trips     INTEGER   DEFAULT 0,
    city            TEXT,
    verification_status TEXT DEFAULT 'pending',
    verification_date TIMESTAMP,
    verified_by     UUID      REFERENCES users(id),
    is_active       BOOLEAN   DEFAULT TRUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المركبات
CREATE TABLE IF NOT EXISTS vehicles (
    vehicle_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id        UUID      REFERENCES users(id),
    license_plate   TEXT      UNIQUE NOT NULL,
    make            TEXT      NOT NULL,
    model           TEXT      NOT NULL,
    year            INTEGER,
    color           TEXT,
    capacity        INTEGER   NOT NULL,
    vehicle_type    vehicle_type_enum NOT NULL,
    status          vehicle_status_enum DEFAULT 'active',
    registration_expiry DATE,
    insurance_expiry DATE,
    last_maintenance DATE,
    next_maintenance DATE,
    fuel_type       TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول السائقين
CREATE TABLE IF NOT EXISTS drivers (
    driver_id       UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    captain_id      UUID      NOT NULL REFERENCES captains(captain_id),
    vehicle_id      UUID      REFERENCES vehicles(vehicle_id),
    shift_start     TIME,
    shift_end       TIME,
    is_available    BOOLEAN   DEFAULT FALSE,
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    last_location_update TIMESTAMP,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- تحديث جدول الرحلات
CREATE TABLE IF NOT EXISTS trips (
    trip_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id        UUID      REFERENCES routes(route_id),
    driver_id       UUID      REFERENCES drivers(driver_id),
    vehicle_id      UUID      REFERENCES vehicles(vehicle_id),
    departure_time  TIMESTAMP NOT NULL,
    arrival_time    TIMESTAMP,
    actual_departure_time TIMESTAMP,
    actual_arrival_time TIMESTAMP,
    available_seats INTEGER   NOT NULL,
    booked_seats    INTEGER   DEFAULT 0,
    base_price      DECIMAL(10,2),
    status          trip_status_enum DEFAULT 'pending',
    cancellation_reason TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID      REFERENCES users(id),
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by      UUID      REFERENCES users(id)
);

-- =====================================================
-- 4. إنشاء الفهارس الجديدة
-- =====================================================

-- فهارس الجلسات
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- فهارس الحجوزات الموسعة
CREATE INDEX IF NOT EXISTS idx_bookings_extended_booking_id ON bookings_extended(booking_id);
CREATE INDEX IF NOT EXISTS idx_bookings_extended_promo ON bookings_extended(promo_code_id);

-- فهارس العروض الترويجية
CREATE INDEX IF NOT EXISTS idx_promotions_code ON promotions(code);
CREATE INDEX IF NOT EXISTS idx_promotions_active ON promotions(is_active);
CREATE INDEX IF NOT EXISTS idx_promotions_dates ON promotions(starts_at, expires_at);

-- فهارس الطرق
CREATE INDEX IF NOT EXISTS idx_routes_locations ON routes(from_location, to_location);
CREATE INDEX IF NOT EXISTS idx_routes_active ON routes(is_active);

-- فهارس الكباتن
CREATE INDEX IF NOT EXISTS idx_captains_user_id ON captains(user_id);
CREATE INDEX IF NOT EXISTS idx_captains_license ON captains(license_number);
CREATE INDEX IF NOT EXISTS idx_captains_verification ON captains(verification_status);

-- فهارس المركبات
CREATE INDEX IF NOT EXISTS idx_vehicles_owner_id ON vehicles(owner_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_plate ON vehicles(license_plate);
CREATE INDEX IF NOT EXISTS idx_vehicles_type ON vehicles(vehicle_type);
CREATE INDEX IF NOT EXISTS idx_vehicles_status ON vehicles(status);

-- فهارس السائقين
CREATE INDEX IF NOT EXISTS idx_drivers_captain_id ON drivers(captain_id);
CREATE INDEX IF NOT EXISTS idx_drivers_vehicle_id ON drivers(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_drivers_available ON drivers(is_available);
CREATE INDEX IF NOT EXISTS idx_drivers_location ON drivers(current_latitude, current_longitude);

-- فهارس الرحلات
CREATE INDEX IF NOT EXISTS idx_trips_route_id ON trips(route_id);
CREATE INDEX IF NOT EXISTS idx_trips_driver_id ON trips(driver_id);
CREATE INDEX IF NOT EXISTS idx_trips_vehicle_id ON trips(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_trips_departure_time ON trips(departure_time);
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);

-- =====================================================
-- 5. إنشاء TRIGGERS للتحديث التلقائي
-- =====================================================

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق TRIGGER على الجداول المطلوبة
DO $$
DECLARE
    table_name TEXT;
    tables_to_update TEXT[] := ARRAY['users', 'bookings', 'parcels', 'promotions', 'routes', 'captains', 'vehicles', 'drivers', 'trips'];
BEGIN
    FOREACH table_name IN ARRAY tables_to_update
    LOOP
        -- التحقق من وجود عمود updated_at
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = table_name AND column_name = 'updated_at'
        ) THEN
            -- حذف TRIGGER إذا كان موجوداً
            EXECUTE format('DROP TRIGGER IF EXISTS update_%s_updated_at ON %s', table_name, table_name);
            
            -- إنشاء TRIGGER جديد
            EXECUTE format('CREATE TRIGGER update_%s_updated_at 
                           BEFORE UPDATE ON %s 
                           FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', 
                           table_name, table_name);
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- 6. تنظيف البيانات القديمة (اختياري)
-- =====================================================

-- حذف الجلسات المنتهية الصلاحية
DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;

-- تحديث الحقول الفارغة
UPDATE users SET language = 'ar' WHERE language IS NULL;
UPDATE users SET timezone = 'Asia/Aden' WHERE timezone IS NULL;

-- =====================================================
-- 7. إضافة قيود جديدة
-- =====================================================

-- قيود التحقق من صحة البيانات
ALTER TABLE users 
ADD CONSTRAINT IF NOT EXISTS chk_users_email_format 
CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE users 
ADD CONSTRAINT IF NOT EXISTS chk_users_phone_format 
CHECK (phone ~* '^\+?[1-9]\d{1,14}$');

-- قيود المركبات
ALTER TABLE vehicles 
ADD CONSTRAINT IF NOT EXISTS chk_vehicles_year 
CHECK (year >= 1990 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1);

ALTER TABLE vehicles 
ADD CONSTRAINT IF NOT EXISTS chk_vehicles_capacity 
CHECK (capacity > 0 AND capacity <= 100);

-- قيود الكباتن
ALTER TABLE captains 
ADD CONSTRAINT IF NOT EXISTS chk_captains_rating 
CHECK (rating >= 0.00 AND rating <= 5.00);

-- =====================================================
-- 8. إنشاء VIEWS مفيدة
-- =====================================================

-- عرض معلومات المستخدمين النشطين
CREATE OR REPLACE VIEW active_users AS
SELECT 
    u.id,
    u.full_name,
    u.email,
    u.phone,
    u.is_active,
    u.is_verified,
    u.created_at,
    u.last_login_at,
    array_agg(r.role_name) as roles
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.role_id
WHERE u.is_active = true AND u.deleted_at IS NULL
GROUP BY u.id, u.full_name, u.email, u.phone, u.is_active, u.is_verified, u.created_at, u.last_login_at;

-- عرض إحصائيات الطرود
CREATE OR REPLACE VIEW parcel_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    SUM(estimated_cost) as total_value,
    AVG(weight_kg) as avg_weight
FROM parcels 
GROUP BY status;

-- =====================================================
-- 9. رسالة إتمام التحديث
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Database migration V2024.01.15 completed successfully!';
    RAISE NOTICE 'Enhanced schema with improved RBAC, parcel system, and analytics ready.';
END $$;
