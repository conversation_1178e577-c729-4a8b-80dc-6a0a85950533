package com.tecnodrive.rideservice.service.impl;

import com.tecnodrive.rideservice.dto.FareEstimateDto;
import com.tecnodrive.rideservice.dto.RideRequestDto;
import com.tecnodrive.rideservice.dto.RideResponseDto;
import com.tecnodrive.rideservice.entity.Ride;
import com.tecnodrive.rideservice.entity.RideStatus;
import com.tecnodrive.rideservice.entity.VehicleType;
import com.tecnodrive.rideservice.repository.RideRepository;
import com.tecnodrive.rideservice.repository.VehicleTypeRepository;
import com.tecnodrive.rideservice.service.RideService;
import com.tecnodrive.rideservice.util.LocationUtil;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Ride Service Implementation
 */
@Service
@Transactional
public class RideServiceImpl implements RideService {

    private final RideRepository rideRepository;
    private final VehicleTypeRepository vehicleTypeRepository;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final GeometryFactory geometryFactory;

    @Autowired
    public RideServiceImpl(RideRepository rideRepository,
                          VehicleTypeRepository vehicleTypeRepository,
                          KafkaTemplate<String, Object> kafkaTemplate) {
        this.rideRepository = rideRepository;
        this.vehicleTypeRepository = vehicleTypeRepository;
        this.kafkaTemplate = kafkaTemplate;
        this.geometryFactory = new GeometryFactory();
    }

    @Override
    public RideResponseDto requestRide(RideRequestDto request) {
        // Validate request
        validateRideRequest(request);

        // Check if passenger has active ride
        if (rideRepository.findActiveRideByPassenger(request.passengerId()).isPresent()) {
            throw new IllegalStateException("Passenger already has an active ride");
        }

        // Get vehicle type
        VehicleType vehicleType = vehicleTypeRepository.findByNameAndIsActiveTrue(request.vehicleTypeName())
                .orElseThrow(() -> new IllegalArgumentException("Invalid vehicle type: " + request.vehicleTypeName()));

        // Create ride entity
        Ride ride = createRideFromRequest(request, vehicleType);

        // Calculate fare estimate
        BigDecimal estimatedFare = calculateFareEstimate(ride, vehicleType);
        ride.setEstimatedFare(estimatedFare);

        // Save ride
        ride = rideRepository.save(ride);

        // Publish ride requested event
        publishRideRequestedEvent(ride);

        return mapToResponseDto(ride);
    }

    @Override
    public FareEstimateDto getFareEstimate(RideRequestDto.LocationDto pickup, 
                                          RideRequestDto.LocationDto destination) {
        // Calculate distance and duration
        double distanceKm = LocationUtil.calculateDistance(
            pickup.latitude(), pickup.longitude(),
            destination.latitude(), destination.longitude()
        );
        int estimatedDuration = LocationUtil.estimateDuration(distanceKm);

        // Get all active vehicle types
        List<VehicleType> vehicleTypes = vehicleTypeRepository.findByIsActiveTrue();

        // Calculate fare for each vehicle type
        List<FareEstimateDto.VehicleTypeFareDto> vehicleTypeFares = vehicleTypes.stream()
                .map(vt -> calculateVehicleTypeFare(vt, distanceKm, estimatedDuration))
                .collect(Collectors.toList());

        return new FareEstimateDto(
            BigDecimal.valueOf(distanceKm).setScale(2, RoundingMode.HALF_UP),
            estimatedDuration,
            vehicleTypeFares
        );
    }

    @Override
    public RideResponseDto assignDriver(UUID rideId, UUID driverId) {
        Ride ride = getRideEntity(rideId);

        if (ride.getStatus() != RideStatus.REQUESTED) {
            throw new IllegalStateException("Ride is not in requested status");
        }

        // Check if driver has active ride
        if (rideRepository.findActiveRideByDriver(driverId).isPresent()) {
            throw new IllegalStateException("Driver already has an active ride");
        }

        ride.assignDriver(driverId);
        ride = rideRepository.save(ride);

        // Publish driver assigned event
        publishDriverAssignedEvent(ride);

        return mapToResponseDto(ride);
    }

    @Override
    public RideResponseDto updateRideStatus(UUID rideId, RideStatus newStatus, String reason) {
        Ride ride = getRideEntity(rideId);

        RideStatus oldStatus = ride.getStatus();
        
        switch (newStatus) {
            case DRIVER_ARRIVED -> ride.markDriverArrived();
            case IN_PROGRESS -> ride.startRide();
            case CANCELLED_BY_PASSENGER, CANCELLED_BY_DRIVER, CANCELLED_BY_SYSTEM -> 
                ride.cancelRide(newStatus, reason);
            default -> ride.setStatus(newStatus);
        }

        ride = rideRepository.save(ride);

        // Publish status update event
        publishRideStatusUpdateEvent(ride, oldStatus, newStatus);

        return mapToResponseDto(ride);
    }

    @Override
    public RideResponseDto completeRide(UUID rideId, BigDecimal finalFare, 
                                       BigDecimal actualDistance, Integer actualDuration) {
        Ride ride = getRideEntity(rideId);

        if (ride.getStatus() != RideStatus.IN_PROGRESS) {
            throw new IllegalStateException("Ride is not in progress");
        }

        ride.completeRide(finalFare, actualDistance, actualDuration);
        ride = rideRepository.save(ride);

        // Publish ride completed event
        publishRideCompletedEvent(ride);

        return mapToResponseDto(ride);
    }

    @Override
    public RideResponseDto cancelRide(UUID rideId, RideStatus cancelStatus, String reason) {
        return updateRideStatus(rideId, cancelStatus, reason);
    }

    @Override
    public RideResponseDto rateRide(UUID rideId, UUID raterId, Integer rating) {
        Ride ride = getRideEntity(rideId);

        if (ride.getStatus() != RideStatus.COMPLETED) {
            throw new IllegalStateException("Can only rate completed rides");
        }

        if (rating < 1 || rating > 5) {
            throw new IllegalArgumentException("Rating must be between 1 and 5");
        }

        if (ride.getPassengerId().equals(raterId)) {
            ride.setRatingByPassenger(rating);
        } else if (ride.getDriverId() != null && ride.getDriverId().equals(raterId)) {
            ride.setRatingByDriver(rating);
        } else {
            throw new IllegalArgumentException("Only passenger or driver can rate the ride");
        }

        ride = rideRepository.save(ride);

        // Publish rating event
        publishRideRatedEvent(ride, raterId, rating);

        return mapToResponseDto(ride);
    }

    @Override
    public RideResponseDto getRideById(UUID rideId) {
        Ride ride = getRideEntity(rideId);
        return mapToResponseDto(ride);
    }

    @Override
    public List<RideResponseDto> getRidesByPassenger(UUID passengerId) {
        List<Ride> rides = rideRepository.findByPassengerIdOrderByRequestedAtDesc(passengerId);
        return rides.stream().map(this::mapToResponseDto).collect(Collectors.toList());
    }

    @Override
    public List<RideResponseDto> getRidesByDriver(UUID driverId) {
        List<Ride> rides = rideRepository.findByDriverIdOrderByRequestedAtDesc(driverId);
        return rides.stream().map(this::mapToResponseDto).collect(Collectors.toList());
    }

    @Override
    public RideResponseDto getActiveRideByPassenger(UUID passengerId) {
        return rideRepository.findActiveRideByPassenger(passengerId)
                .map(this::mapToResponseDto)
                .orElse(null);
    }

    @Override
    public RideResponseDto getActiveRideByDriver(UUID driverId) {
        return rideRepository.findActiveRideByDriver(driverId)
                .map(this::mapToResponseDto)
                .orElse(null);
    }

    @Override
    public List<RideResponseDto> findAvailableRides(UUID driverId, double latitude, double longitude, double radiusKm) {
        // Check if driver has active ride
        if (rideRepository.findActiveRideByDriver(driverId).isPresent()) {
            return List.of(); // Driver already has active ride
        }

        // Find rides near driver location
        List<Ride> nearbyRides = rideRepository.findRidesNearLocation(latitude, longitude, radiusKm * 1000); // Convert to meters
        
        return nearbyRides.stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    public RideMetricsDto getRideMetrics() {
        long totalRides = rideRepository.count();
        long activeRides = rideRepository.countByStatus(RideStatus.REQUESTED) +
                          rideRepository.countByStatus(RideStatus.DRIVER_ASSIGNED) +
                          rideRepository.countByStatus(RideStatus.DRIVER_ARRIVED) +
                          rideRepository.countByStatus(RideStatus.IN_PROGRESS);
        long completedRides = rideRepository.countByStatus(RideStatus.COMPLETED);
        long cancelledRides = rideRepository.countByStatus(RideStatus.CANCELLED_BY_PASSENGER) +
                             rideRepository.countByStatus(RideStatus.CANCELLED_BY_DRIVER) +
                             rideRepository.countByStatus(RideStatus.CANCELLED_BY_SYSTEM);

        // Calculate average rating and total revenue
        // This would typically be done with more complex queries
        double averageRating = 4.2; // Placeholder
        BigDecimal totalRevenue = BigDecimal.valueOf(50000); // Placeholder

        return new RideMetricsDto(totalRides, activeRides, completedRides, cancelledRides, averageRating, totalRevenue);
    }

    // Helper methods
    private void validateRideRequest(RideRequestDto request) {
        if (request.passengerId() == null) {
            throw new IllegalArgumentException("Passenger ID is required");
        }
        if (request.pickupLocation() == null || request.destinationLocation() == null) {
            throw new IllegalArgumentException("Pickup and destination locations are required");
        }
        if (request.pickupAddress() == null || request.pickupAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("Pickup address is required");
        }
        if (request.destinationAddress() == null || request.destinationAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("Destination address is required");
        }
    }

    private Ride createRideFromRequest(RideRequestDto request, VehicleType vehicleType) {
        Point pickupPoint = geometryFactory.createPoint(
            new Coordinate(request.pickupLocation().longitude(), request.pickupLocation().latitude())
        );
        pickupPoint.setSRID(4326);

        Point destinationPoint = geometryFactory.createPoint(
            new Coordinate(request.destinationLocation().longitude(), request.destinationLocation().latitude())
        );
        destinationPoint.setSRID(4326);

        Ride ride = new Ride(request.passengerId(), pickupPoint, request.pickupAddress(),
                           destinationPoint, request.destinationAddress());
        
        ride.setVehicleType(vehicleType);
        ride.setRideType(request.rideType() != null ? request.rideType() : com.tecnodrive.rideservice.entity.RideType.STANDARD);
        ride.setScheduledAt(request.scheduledAt());
        ride.setPassengerNotes(request.passengerNotes());
        ride.setCompanyId(request.companyId());
        ride.setSchoolId(request.schoolId());

        return ride;
    }

    private BigDecimal calculateFareEstimate(Ride ride, VehicleType vehicleType) {
        // Calculate distance
        double distanceKm = LocationUtil.calculateDistance(
            ride.getPickupLocation().getY(), ride.getPickupLocation().getX(),
            ride.getDestinationLocation().getY(), ride.getDestinationLocation().getX()
        );

        // Estimate duration
        int durationMinutes = LocationUtil.estimateDuration(distanceKm);

        // Calculate base fare
        BigDecimal baseFare = vehicleType.getBaseFare();
        BigDecimal distanceFare = vehicleType.getPerKmRate().multiply(BigDecimal.valueOf(distanceKm));
        BigDecimal timeFare = vehicleType.getPerMinuteRate().multiply(BigDecimal.valueOf(durationMinutes));

        BigDecimal totalFare = baseFare.add(distanceFare).add(timeFare);

        // Apply surge pricing (simplified)
        BigDecimal surgeMultiplier = calculateSurgeMultiplier();
        ride.setSurgeMultiplier(surgeMultiplier);

        return totalFare.multiply(surgeMultiplier).setScale(2, RoundingMode.HALF_UP);
    }

    private FareEstimateDto.VehicleTypeFareDto calculateVehicleTypeFare(VehicleType vehicleType, 
                                                                       double distanceKm, int durationMinutes) {
        BigDecimal baseFare = vehicleType.getBaseFare();
        BigDecimal distanceFare = vehicleType.getPerKmRate().multiply(BigDecimal.valueOf(distanceKm));
        BigDecimal timeFare = vehicleType.getPerMinuteRate().multiply(BigDecimal.valueOf(durationMinutes));
        
        BigDecimal surgeMultiplier = calculateSurgeMultiplier();
        BigDecimal subtotal = baseFare.add(distanceFare).add(timeFare);
        BigDecimal surgeFare = subtotal.multiply(surgeMultiplier.subtract(BigDecimal.ONE));
        BigDecimal totalFare = subtotal.multiply(surgeMultiplier);

        return new FareEstimateDto.VehicleTypeFareDto(
            vehicleType.getName(),
            vehicleType.getNameAr(),
            baseFare.setScale(2, RoundingMode.HALF_UP),
            distanceFare.setScale(2, RoundingMode.HALF_UP),
            timeFare.setScale(2, RoundingMode.HALF_UP),
            surgeFare.setScale(2, RoundingMode.HALF_UP),
            totalFare.setScale(2, RoundingMode.HALF_UP),
            surgeMultiplier,
            vehicleType.getDescription()
        );
    }

    private BigDecimal calculateSurgeMultiplier() {
        // Simplified surge calculation - in real implementation, this would consider:
        // - Current time of day
        // - Day of week
        // - Demand vs supply
        // - Special events
        return BigDecimal.valueOf(1.2); // 20% surge
    }

    private Ride getRideEntity(UUID rideId) {
        return rideRepository.findById(rideId)
                .orElseThrow(() -> new IllegalArgumentException("Ride not found: " + rideId));
    }

    private RideResponseDto mapToResponseDto(Ride ride) {
        RideResponseDto.VehicleTypeDto vehicleTypeDto = null;
        if (ride.getVehicleType() != null) {
            VehicleType vt = ride.getVehicleType();
            vehicleTypeDto = new RideResponseDto.VehicleTypeDto(
                vt.getId(), vt.getName(), vt.getNameAr(), vt.getDescription(),
                vt.getBaseFare(), vt.getPerKmRate(), vt.getPerMinuteRate(), vt.getCapacity()
            );
        }

        RideResponseDto.LocationDto pickupLocationDto = null;
        if (ride.getPickupLocation() != null) {
            pickupLocationDto = new RideResponseDto.LocationDto(
                ride.getPickupLocation().getY(), ride.getPickupLocation().getX()
            );
        }

        RideResponseDto.LocationDto destinationLocationDto = null;
        if (ride.getDestinationLocation() != null) {
            destinationLocationDto = new RideResponseDto.LocationDto(
                ride.getDestinationLocation().getY(), ride.getDestinationLocation().getX()
            );
        }

        return new RideResponseDto(
            ride.getId(), ride.getPassengerId(), ride.getDriverId(), vehicleTypeDto,
            pickupLocationDto, ride.getPickupAddress(), destinationLocationDto, ride.getDestinationAddress(),
            ride.getStatus(), ride.getRideType(), ride.getEstimatedFare(), ride.getFinalFare(),
            ride.getSurgeMultiplier(), ride.getRequestedAt(), ride.getScheduledAt(),
            ride.getDriverAssignedAt(), ride.getDriverArrivedAt(), ride.getStartedAt(),
            ride.getCompletedAt(), ride.getCancelledAt(), ride.getEstimatedDistanceKm(),
            ride.getActualDistanceKm(), ride.getEstimatedDurationMinutes(), ride.getActualDurationMinutes(),
            ride.getPassengerNotes(), ride.getCancellationReason(), ride.getRatingByPassenger(),
            ride.getRatingByDriver(), ride.getCompanyId(), ride.getSchoolId(),
            ride.getCreatedAt(), ride.getUpdatedAt()
        );
    }

    // Kafka event publishing methods
    private void publishRideRequestedEvent(Ride ride) {
        RideRequestedEvent event = new RideRequestedEvent(
            ride.getId().toString(),
            ride.getPassengerId().toString(),
            ride.getPickupAddress(),
            ride.getDestinationAddress(),
            ride.getEstimatedFare(),
            ride.getRequestedAt()
        );
        kafkaTemplate.send("ride-requested", event);
    }

    private void publishDriverAssignedEvent(Ride ride) {
        DriverAssignedEvent event = new DriverAssignedEvent(
            ride.getId().toString(),
            ride.getDriverId().toString(),
            ride.getPassengerId().toString(),
            ride.getDriverAssignedAt()
        );
        kafkaTemplate.send("driver-assigned", event);
    }

    private void publishRideStatusUpdateEvent(Ride ride, RideStatus oldStatus, RideStatus newStatus) {
        RideStatusUpdateEvent event = new RideStatusUpdateEvent(
            ride.getId().toString(),
            oldStatus.toString(),
            newStatus.toString(),
            Instant.now()
        );
        kafkaTemplate.send("ride-status-updated", event);
    }

    private void publishRideCompletedEvent(Ride ride) {
        RideCompletedEvent event = new RideCompletedEvent(
            ride.getId().toString(),
            ride.getPassengerId().toString(),
            ride.getDriverId().toString(),
            ride.getFinalFare(),
            ride.getActualDistanceKm(),
            ride.getActualDurationMinutes(),
            ride.getCompletedAt()
        );
        kafkaTemplate.send("ride-completed", event);
    }

    private void publishRideRatedEvent(Ride ride, UUID raterId, Integer rating) {
        RideRatedEvent event = new RideRatedEvent(
            ride.getId().toString(),
            raterId.toString(),
            rating,
            Instant.now()
        );
        kafkaTemplate.send("ride-rated", event);
    }

    // Event classes (would typically be in separate files)
    public record RideRequestedEvent(String rideId, String passengerId, String pickupAddress, 
                                   String destinationAddress, BigDecimal estimatedFare, Instant requestedAt) {}
    
    public record DriverAssignedEvent(String rideId, String driverId, String passengerId, Instant assignedAt) {}
    
    public record RideStatusUpdateEvent(String rideId, String oldStatus, String newStatus, Instant updatedAt) {}
    
    public record RideCompletedEvent(String rideId, String passengerId, String driverId, 
                                   BigDecimal finalFare, BigDecimal actualDistance, Integer actualDuration, Instant completedAt) {}
    
    public record RideRatedEvent(String rideId, String raterId, Integer rating, Instant ratedAt) {}
}
