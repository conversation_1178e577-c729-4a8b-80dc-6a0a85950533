import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  MyLocation as MyLocationIcon,
  Layers as LayersIcon
} from '@mui/icons-material';

// Import Leaflet directly
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix Leaflet default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface LeafletMapFixedProps {
  height?: string;
}

// Map providers
const MAP_PROVIDERS = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors'
  },
  {
    id: 'cartodb-light',
    name: 'CartoDB Light',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  },
  {
    id: 'cartodb-dark',
    name: 'CartoDB Dark',
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO'
  }
];

const LeafletMapFixed: React.FC<LeafletMapFixedProps> = ({ height = '600px' }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const tileLayerRef = useRef<L.TileLayer | null>(null);
  
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');
  const [mapReady, setMapReady] = useState(false);

  // Fixed center for Riyadh
  const center: [number, number] = [24.7136, 46.6753];
  const zoom = 12;

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    try {
      console.log('🗺️ Initializing Leaflet map...');
      
      // Create map instance
      const map = L.map(mapRef.current, {
        center: center,
        zoom: zoom,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        dragging: true,
        touchZoom: true,
        boxZoom: true,
        keyboard: true
      });

      // Add initial tile layer
      const initialProvider = MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
      const tileLayer = L.tileLayer(initialProvider.url, {
        attribution: initialProvider.attribution,
        maxZoom: 19
      });
      
      tileLayer.addTo(map);
      tileLayerRef.current = tileLayer;
      mapInstanceRef.current = map;
      
      console.log('✅ Leaflet map initialized successfully');
      setMapReady(true);

    } catch (error) {
      console.error('❌ Failed to initialize map:', error);
      setError('فشل في تهيئة الخريطة');
    }

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        console.log('🧹 Cleaning up map instance...');
        
        // Clear markers
        markersRef.current.forEach(marker => {
          mapInstanceRef.current?.removeLayer(marker);
        });
        markersRef.current = [];
        
        // Remove tile layer
        if (tileLayerRef.current) {
          mapInstanceRef.current.removeLayer(tileLayerRef.current);
          tileLayerRef.current = null;
        }
        
        // Remove map
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
        setMapReady(false);
      }
    };
  }, []); // Empty dependency array - only run once

  // Load vehicles
  useEffect(() => {
    loadVehicles();
  }, []);

  // Update tile layer when provider changes
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    const newProvider = MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
    
    // Remove old tile layer
    if (tileLayerRef.current) {
      mapInstanceRef.current.removeLayer(tileLayerRef.current);
    }
    
    // Add new tile layer
    const newTileLayer = L.tileLayer(newProvider.url, {
      attribution: newProvider.attribution,
      maxZoom: 19
    });
    
    newTileLayer.addTo(mapInstanceRef.current);
    tileLayerRef.current = newTileLayer;
    
    console.log('🔄 Tile layer updated to:', newProvider.name);
  }, [selectedProvider, mapReady]);

  // Update markers when vehicles change
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current?.removeLayer(marker);
    });
    markersRef.current = [];

    // Add new markers
    vehicles.forEach(vehicle => {
      const marker = createVehicleMarker(vehicle);
      if (marker && mapInstanceRef.current) {
        marker.addTo(mapInstanceRef.current);
        markersRef.current.push(marker);
      }
    });

    console.log(`🚗 Updated ${vehicles.length} vehicle markers`);
  }, [vehicles, mapReady]);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
          setError(null);
        }
      } else {
        throw new Error('Failed to load vehicles');
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Fallback to mock data
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'Ahmed Mohamed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'Sara Ahmed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_003',
          lat: 24.7100,
          lng: 46.6700,
          speed: 0,
          heading: 0,
          status: 'idle',
          driver: 'Mohamed Ali',
          lastUpdate: new Date().toISOString()
        }
      ]);
      
      setError('Using mock vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const createVehicleMarker = (vehicle: Vehicle): L.Marker | null => {
    try {
      const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
      
      const icon = L.divIcon({
        html: `
          <div style="
            background-color: ${color};
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
          ">
            🚗
          </div>
        `,
        className: 'leaflet-vehicle-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      const marker = L.marker([vehicle.lat, vehicle.lng], { icon });
      
      // Add popup
      const popupContent = `
        <div style="min-width: 200px;">
          <h3>مركبة ${vehicle.id}</h3>
          <p><strong>السائق:</strong> ${vehicle.driver}</p>
          <p><strong>السرعة:</strong> ${vehicle.speed} كم/س</p>
          <p><strong>الحالة:</strong> ${vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}</p>
          <p><strong>الإحداثيات:</strong> ${vehicle.lat.toFixed(4)}, ${vehicle.lng.toFixed(4)}</p>
          <p><strong>آخر تحديث:</strong> ${new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}</p>
        </div>
      `;
      
      marker.bindPopup(popupContent);
      
      return marker;
    } catch (error) {
      console.error('Error creating vehicle marker:', error);
      return null;
    }
  };

  const handleRefresh = () => {
    loadVehicles();
  };

  const handleLocateUser = () => {
    if (!mapInstanceRef.current) return;
    
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userPos: [number, number] = [
            position.coords.latitude,
            position.coords.longitude
          ];
          mapInstanceRef.current?.setView(userPos, 15);
        },
        (error) => {
          console.error('Geolocation error:', error);
          alert('تعذر الحصول على موقعك الحالي');
        }
      );
    } else {
      alert('المتصفح لا يدعم تحديد الموقع');
    }
  };

  if (loading && vehicles.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={height}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          جاري تحميل الخريطة...
        </Typography>
      </Box>
    );
  }

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: 16,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {/* Provider Selection */}
        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <FormControl fullWidth size="small">
              <InputLabel>مزود الخريطة</InputLabel>
              <Select
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                label="مزود الخريطة"
              >
                {MAP_PROVIDERS.map(provider => (
                  <MenuItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Card>
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Box display="flex" gap={1}>
              <Tooltip title="تحديث">
                <IconButton onClick={handleRefresh} size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="موقعي">
                <IconButton onClick={handleLocateUser} size="small">
                  <MyLocationIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="الطبقات">
                <IconButton size="small">
                  <LayersIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Vehicle Stats */}
      {vehicles.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Card>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <Typography variant="subtitle2" gutterBottom>
                إحصائيات المركبات
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`المجموع: ${vehicles.length}`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                  size="small"
                  color="success"
                />
                <Chip
                  label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                  size="small"
                  color="warning"
                />
              </Box>
              {error && (
                <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
                  {error}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Error Alert */}
      {error && error !== 'Using mock vehicle data' && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <Alert severity="warning">
            {error}
          </Alert>
        </Box>
      )}

      {/* Map Container */}
      <div
        ref={mapRef}
        style={{
          height: '100%',
          width: '100%',
          position: 'relative'
        }}
      />
    </Paper>
  );
};

export default LeafletMapFixed;
