package com.tecnodrive.parcelservice.entity;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.UUID;

/**
 * Delivery Proof Entity
 */
@Entity
@Table(name = "delivery_proof")
public class DeliveryProof {

    @Id
    private UUID id = UUID.randomUUID();

    @Column(nullable = false)
    private UUID deliveryId;

    private String proofType;

    private String proofUrl;

    private Instant capturedAt = Instant.now();

    // Constructors
    public DeliveryProof() {}

    public DeliveryProof(UUID deliveryId, String proofType, String proofUrl) {
        this.deliveryId = deliveryId;
        this.proofType = proofType;
        this.proofUrl = proofUrl;
    }

    // Getters and Setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public UUID getDeliveryId() { return deliveryId; }
    public void setDeliveryId(UUID deliveryId) { this.deliveryId = deliveryId; }

    public String getProofType() { return proofType; }
    public void setProofType(String proofType) { this.proofType = proofType; }

    public String getProofUrl() { return proofUrl; }
    public void setProofUrl(String proofUrl) { this.proofUrl = proofUrl; }

    public Instant getCapturedAt() { return capturedAt; }
    public void setCapturedAt(Instant capturedAt) { this.capturedAt = capturedAt; }

    @Override
    public String toString() {
        return "DeliveryProof{" +
                "id=" + id +
                ", deliveryId=" + deliveryId +
                ", proofType='" + proofType + '\'' +
                ", proofUrl='" + proofUrl + '\'' +
                '}';
    }
}
