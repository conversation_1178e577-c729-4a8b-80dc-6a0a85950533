package com.tecnodrive.authservice.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Enhanced JWT Token Manager with Revocation Support
 * Provides secure JWT token generation, validation, and revocation capabilities
 */
@Slf4j
@Component
public class JwtTokenManager {

    private final SecretKey secretKey;
    private final long accessTokenExpiration;
    private final long refreshTokenExpiration;
    private final String issuer;
    private final String audience;
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final String REVOKED_TOKEN_PREFIX = "jwt:revoked:";
    private static final String REFRESH_TOKEN_PREFIX = "jwt:refresh:";
    private static final String USER_SESSIONS_PREFIX = "user:sessions:";

    public JwtTokenManager(
            @Value("${security.jwt.secret}") String secret,
            @Value("${security.jwt.access-token-expiration}") long accessTokenExpiration,
            @Value("${security.jwt.refresh-token-expiration}") long refreshTokenExpiration,
            @Value("${security.jwt.issuer}") String issuer,
            @Value("${security.jwt.audience}") String audience,
            RedisTemplate<String, String> redisTemplate) {
        
        this.secretKey = Keys.hmacShaKeyFor(secret.getBytes());
        this.accessTokenExpiration = accessTokenExpiration;
        this.refreshTokenExpiration = refreshTokenExpiration;
        this.issuer = issuer;
        this.audience = audience;
        this.redisTemplate = redisTemplate;
    }

    /**
     * Generate access token with enhanced security
     */
    public String generateAccessToken(String userId, String username, String role, String sessionId) {
        Instant now = Instant.now();
        Instant expiration = now.plus(accessTokenExpiration, ChronoUnit.MILLIS);
        String jti = UUID.randomUUID().toString(); // JWT ID for revocation

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        claims.put("sessionId", sessionId);
        claims.put("tokenType", "access");
        claims.put("jti", jti);

        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(userId)
                .setIssuer(issuer)
                .setAudience(audience)
                .setIssuedAt(Date.from(now))
                .setExpiration(Date.from(expiration))
                .setId(jti)
                .signWith(secretKey, SignatureAlgorithm.HS512)
                .compact();

        // Store session information
        storeUserSession(userId, sessionId, jti, expiration);
        
        log.debug("Generated access token for user: {} with JTI: {}", userId, jti);
        return token;
    }

    /**
     * Generate refresh token
     */
    public String generateRefreshToken(String userId, String sessionId) {
        Instant now = Instant.now();
        Instant expiration = now.plus(refreshTokenExpiration, ChronoUnit.MILLIS);
        String jti = UUID.randomUUID().toString();

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("sessionId", sessionId);
        claims.put("tokenType", "refresh");
        claims.put("jti", jti);

        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(userId)
                .setIssuer(issuer)
                .setAudience(audience)
                .setIssuedAt(Date.from(now))
                .setExpiration(Date.from(expiration))
                .setId(jti)
                .signWith(secretKey, SignatureAlgorithm.HS512)
                .compact();

        // Store refresh token in Redis
        redisTemplate.opsForValue().set(
                REFRESH_TOKEN_PREFIX + jti, 
                userId, 
                refreshTokenExpiration, 
                TimeUnit.MILLISECONDS
        );

        log.debug("Generated refresh token for user: {} with JTI: {}", userId, jti);
        return token;
    }

    /**
     * Validate token and check if it's revoked
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = parseToken(token);
            String jti = claims.getId();
            
            // Check if token is revoked
            if (isTokenRevoked(jti)) {
                log.warn("Attempted to use revoked token with JTI: {}", jti);
                return false;
            }
            
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("Invalid token: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Parse token and extract claims
     */
    public Claims parseToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .requireIssuer(issuer)
                .requireAudience(audience)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * Revoke a specific token
     */
    public void revokeToken(String token) {
        try {
            Claims claims = parseToken(token);
            String jti = claims.getId();
            Date expiration = claims.getExpiration();
            
            if (jti != null && expiration != null) {
                long ttl = expiration.getTime() - System.currentTimeMillis();
                if (ttl > 0) {
                    redisTemplate.opsForValue().set(
                            REVOKED_TOKEN_PREFIX + jti, 
                            "revoked", 
                            ttl, 
                            TimeUnit.MILLISECONDS
                    );
                    log.info("Revoked token with JTI: {}", jti);
                }
            }
        } catch (JwtException e) {
            log.warn("Failed to revoke invalid token: {}", e.getMessage());
        }
    }

    /**
     * Revoke all tokens for a user
     */
    public void revokeAllUserTokens(String userId) {
        String sessionKey = USER_SESSIONS_PREFIX + userId;
        Map<Object, Object> sessions = redisTemplate.opsForHash().entries(sessionKey);
        
        for (Object sessionId : sessions.keySet()) {
            String sessionData = (String) sessions.get(sessionId);
            if (sessionData != null) {
                String[] parts = sessionData.split(":");
                if (parts.length >= 2) {
                    String jti = parts[0];
                    long expiration = Long.parseLong(parts[1]);
                    
                    long ttl = expiration - System.currentTimeMillis();
                    if (ttl > 0) {
                        redisTemplate.opsForValue().set(
                                REVOKED_TOKEN_PREFIX + jti, 
                                "revoked", 
                                ttl, 
                                TimeUnit.MILLISECONDS
                        );
                    }
                }
            }
        }
        
        // Clear user sessions
        redisTemplate.delete(sessionKey);
        log.info("Revoked all tokens for user: {}", userId);
    }

    /**
     * Check if token is revoked
     */
    public boolean isTokenRevoked(String jti) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(REVOKED_TOKEN_PREFIX + jti));
    }

    /**
     * Refresh access token using refresh token
     */
    public String refreshAccessToken(String refreshToken) {
        try {
            Claims claims = parseToken(refreshToken);
            String userId = claims.getSubject();
            String jti = claims.getId();
            String sessionId = claims.get("sessionId", String.class);
            
            // Verify refresh token exists in Redis
            String storedUserId = redisTemplate.opsForValue().get(REFRESH_TOKEN_PREFIX + jti);
            if (!userId.equals(storedUserId)) {
                throw new JwtException("Invalid refresh token");
            }
            
            // Generate new access token (would need to fetch user details)
            // This is a simplified version - in practice, you'd fetch user role from database
            return generateAccessToken(userId, "username", "USER", sessionId);
            
        } catch (JwtException e) {
            log.warn("Failed to refresh token: {}", e.getMessage());
            throw new JwtException("Invalid refresh token");
        }
    }

    /**
     * Store user session information
     */
    private void storeUserSession(String userId, String sessionId, String jti, Instant expiration) {
        String sessionKey = USER_SESSIONS_PREFIX + userId;
        String sessionData = jti + ":" + expiration.toEpochMilli();
        
        redisTemplate.opsForHash().put(sessionKey, sessionId, sessionData);
        redisTemplate.expire(sessionKey, refreshTokenExpiration, TimeUnit.MILLISECONDS);
    }

    /**
     * Get user ID from token
     */
    public String getUserIdFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getSubject();
        } catch (JwtException e) {
            return null;
        }
    }

    /**
     * Get session ID from token
     */
    public String getSessionIdFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.get("sessionId", String.class);
        } catch (JwtException e) {
            return null;
        }
    }

    /**
     * Check if token is expired
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration().before(new Date());
        } catch (JwtException e) {
            return true;
        }
    }

    /**
     * Get token expiration time
     */
    public Date getTokenExpiration(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration();
        } catch (JwtException e) {
            return null;
        }
    }
}
