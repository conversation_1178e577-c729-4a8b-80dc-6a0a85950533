# =============================================================================
# TECNO DRIVE API Gateway - Ultra-Secure Distroless Dockerfile
# =============================================================================

# Build stage with Maven cache optimization
FROM maven:3.9.4-openjdk-17-slim AS dependencies

WORKDIR /app

# Copy only pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies offline for better caching
RUN mvn dependency:go-offline -B --no-transfer-progress

# =============================================================================
# Build stage
FROM maven:3.9.4-openjdk-17-slim AS builder

WORKDIR /app

# Copy dependencies from previous stage
COPY --from=dependencies /root/.m2 /root/.m2
COPY pom.xml .

# Copy source code
COPY src ./src

# Build application with optimizations
RUN mvn clean package -DskipTests --no-transfer-progress \
    -Dmaven.compile.fork=true \
    -Dmaven.compiler.maxmem=1024m

# Extract JAR layers for better caching
RUN java -Djarmode=layertools -jar target/*.jar extract

# =============================================================================
# Distroless runtime stage (Ultra-secure, minimal attack surface)
FROM gcr.io/distroless/java17-debian11:nonroot

# Metadata labels
LABEL maintainer="<EMAIL>" \
      org.opencontainers.image.title="TECNO DRIVE API Gateway (Distroless)" \
      org.opencontainers.image.description="Ultra-secure API Gateway with minimal attack surface" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.source="https://github.com/tecno-drive/api-gateway" \
      org.opencontainers.image.vendor="TECNO DRIVE" \
      org.opencontainers.image.licenses="MIT"

WORKDIR /app

# Copy JAR layers for better caching
COPY --from=builder /app/dependencies/ ./
COPY --from=builder /app/spring-boot-loader/ ./
COPY --from=builder /app/snapshot-dependencies/ ./
COPY --from=builder /app/application/ ./

# Expose port
EXPOSE 8080

# Environment variables for JVM optimization
ENV JAVA_TOOL_OPTIONS="-server \
    -XX:+UseG1GC \
    -XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:InitialRAMPercentage=50.0 \
    -XX:+OptimizeStringConcat \
    -XX:+UseStringDeduplication \
    -XX:+ExitOnOutOfMemoryError \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.main.allow-bean-definition-overriding=true \
    -Dspring.jmx.enabled=false"

# Application environment
ENV SPRING_PROFILES_ACTIVE=docker
ENV SERVER_PORT=8080

# Use Spring Boot's layered JAR approach
ENTRYPOINT ["java", "org.springframework.boot.loader.JarLauncher"]

# =============================================================================
# Health check alternative (for Kubernetes)
# Since Distroless doesn't have curl, use Kubernetes probes instead:
# 
# livenessProbe:
#   httpGet:
#     path: /actuator/health/liveness
#     port: 8080
#   initialDelaySeconds: 90
#   periodSeconds: 30
# 
# readinessProbe:
#   httpGet:
#     path: /actuator/health/readiness
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10
# =============================================================================
