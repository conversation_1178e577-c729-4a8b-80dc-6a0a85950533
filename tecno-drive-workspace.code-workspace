{"folders": [{"name": "🏠 Root - TecnoDrive Platform", "path": "."}, {"name": "📚 Documentation", "path": "./docs"}, {"name": "⚙️ Configuration", "path": "./config"}, {"name": "🔧 Tools & Scripts", "path": "./tools"}, {"name": "🏗️ Infrastructure", "path": "./infrastructure"}, {"name": "🐍 Python Backend - Comprehensive System", "path": "./backend/comprehensive-system"}, {"name": "📖 API Documentation", "path": "./backend/api-docs"}, {"name": "🔗 Shared Backend Libraries", "path": "./backend/shared"}, {"name": "🔐 Auth Service (Core)", "path": "./backend/microservices/core/auth-service"}, {"name": "👤 User Service (Core)", "path": "./backend/microservices/core/user-service"}, {"name": "💳 Payment Service (Core)", "path": "./backend/microservices/core/payment-service"}, {"name": "🚗 Ride Service (Core)", "path": "./backend/microservices/core/ride-service"}, {"name": "📊 Analytics Service (Business)", "path": "./backend/microservices/business/analytics-service"}, {"name": "💰 Financial Service (Business)", "path": "./backend/microservices/business/financial-service"}, {"name": "🚛 Fleet Service (Business)", "path": "./backend/microservices/business/fleet-service"}, {"name": "👥 HR Service (Business)", "path": "./backend/microservices/business/hr-service"}, {"name": "📍 Location Service (Business)", "path": "./backend/microservices/business/location-service"}, {"name": "🔔 Notification Service (Business)", "path": "./backend/microservices/business/notification-service"}, {"name": "📦 Parcel Service (Business)", "path": "./backend/microservices/business/parcel-service"}, {"name": "💼 Wallet Service (Business)", "path": "./backend/microservices/business/wallet-service"}, {"name": "🌐 API Gateway (Infrastructure)", "path": "./backend/microservices/infrastructure/api-gateway"}, {"name": "🔍 Eureka Server (Infrastructure)", "path": "./backend/microservices/infrastructure/eureka-server"}, {"name": "🏢 SaaS Management Service (Infrastructure)", "path": "./backend/microservices/infrastructure/saas-management-service"}, {"name": "🏠 Tenant Management Service (Infrastructure)", "path": "./backend/microservices/infrastructure/tenant-management-service"}, {"name": "🎛️ Admin Dashboard (Frontend)", "path": "./frontend/admin-dashboard"}, {"name": "🚗 Driver App (Frontend)", "path": "./frontend/user-apps/driver-app"}, {"name": "👤 Passenger App (Frontend)", "path": "./frontend/user-apps/passenger-app"}, {"name": "🧩 Shared Components (Frontend)", "path": "./frontend/shared-components"}], "settings": {"java.configuration.workspaceCacheLimit": 90, "java.import.gradle.enabled": false, "java.import.maven.enabled": true, "java.compile.nullAnalysis.mode": "automatic", "java.configuration.maven.userSettings": "", "maven.executable.path": "mvn", "spring-boot.ls.checkjvm": false, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["./frontend/admin-dashboard", "./frontend/user-apps/driver-app", "./frontend/user-apps/passenger-app", "./frontend/shared-components"], "python.defaultInterpreterPath": "./backend/comprehensive-system/venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "files.exclude": {"**/node_modules": true, "**/target": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/__pycache__": true, "**/*.pyc": true, "**/venv": true, "**/.env": true}, "search.exclude": {"**/node_modules": true, "**/target": true, "**/dist": true, "**/build": true, "**/.git": true, "**/logs": true, "**/__pycache__": true, "**/venv": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/target/**": true, "**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/__pycache__/**": true, "**/venv/**": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "git.ignoreLimitWarning": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts", "*.jsx": "${capture}.js", "*.tsx": "${capture}.ts", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml", "pom.xml": "target/", "Dockerfile": ".dockerignore", "docker-compose.yml": "docker-compose.*.yml"}}, "extensions": {"recommendations": ["vscjava.vscode-java-pack", "vscjava.vscode-spring-initializr", "vscjava.vscode-spring-boot-dashboard", "pivotal.vscode-spring-boot", "ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-kubernetes-tools.vscode-kubernetes-tools", "ms-vscode-remote.remote-containers", "ms-azuretools.vscode-docker", "hashicorp.terraform", "ms-vscode.powershell", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "yzhang.markdown-all-in-one", "shd101wyy.markdown-preview-enhanced", "ms-vscode.hexeditor", "ms-vscode-remote.remote-ssh"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 Start All Core Services", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "./start-platform.ps1", "-Mode", "core"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🛑 Stop All Services", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "./stop-platform.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🏗️ Build All Java Services", "type": "shell", "command": "mvn", "args": ["clean", "compile", "-f", "config/pom.xml"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🧪 Test All Java Services", "type": "shell", "command": "mvn", "args": ["test", "-f", "config/pom.xml"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📦 Package All Java Services", "type": "shell", "command": "mvn", "args": ["clean", "package", "-f", "config/pom.xml"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🎛️ Start Admin Dashboard", "type": "shell", "command": "npm", "args": ["start"], "options": {"cwd": "./frontend/admin-dashboard"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": "$tsc"}, {"label": "🏗️ Build Admin Dashboard", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "./frontend/admin-dashboard"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$tsc"}, {"label": "🐍 Start Python Backend", "type": "shell", "command": "python", "args": ["-m", "u<PERSON><PERSON>", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "options": {"cwd": "./backend/comprehensive-system"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🐳 Docker Compose Up", "type": "shell", "command": "docker-compose", "args": ["-f", "config/docker-compose.yml", "up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🐳 Docker Compose Down", "type": "shell", "command": "docker-compose", "args": ["-f", "config/docker-compose.yml", "down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📊 Monitor Services", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "./tools/scripts/monitor-all-services.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"type": "java", "name": "🔐 Debug Auth Service", "request": "launch", "mainClass": "com.tecnodrive.auth.AuthServiceApplication", "projectName": "auth-service", "cwd": "${workspaceFolder}/backend/microservices/core/auth-service", "env": {"SPRING_PROFILES_ACTIVE": "dev"}}, {"type": "java", "name": "🌐 Debug API Gateway", "request": "launch", "mainClass": "com.tecnodrive.gateway.ApiGatewayApplication", "projectName": "api-gateway", "cwd": "${workspaceFolder}/backend/microservices/infrastructure/api-gateway", "env": {"SPRING_PROFILES_ACTIVE": "dev"}}, {"type": "debugpy", "name": "🐍 Debug Python Backend", "request": "launch", "program": "${workspaceFolder}/backend/comprehensive-system/main.py", "cwd": "${workspaceFolder}/backend/comprehensive-system", "env": {"PYTHONPATH": "${workspaceFolder}/backend/comprehensive-system"}, "console": "integratedTerminal"}, {"type": "node", "name": "🎛️ Debug Admin Dashboard", "request": "launch", "program": "${workspaceFolder}/frontend/admin-dashboard/node_modules/.bin/react-scripts", "args": ["start"], "cwd": "${workspaceFolder}/frontend/admin-dashboard", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}]}}