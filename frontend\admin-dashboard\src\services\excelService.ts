import * as XLSX from 'xlsx';
// إصلاح استيراد file-saver
import * as FileSaver from 'file-saver';

export interface ExportData {
  sheetName: string;
  data: any[];
  headers?: string[];
}

export class ExcelService {
  /**
   * Export data to Excel file
   */
  static exportToExcel(exportData: ExportData[], filename: string = 'data.xlsx'): void {
    try {
      const workbook = XLSX.utils.book_new();

      exportData.forEach(({ sheetName, data, headers }) => {
        let worksheet: XLSX.WorkSheet;

        if (headers && headers.length > 0) {
          // Create worksheet with custom headers
          worksheet = XLSX.utils.json_to_sheet(data, { header: headers });
        } else {
          // Create worksheet with automatic headers
          worksheet = XLSX.utils.json_to_sheet(data);
        }

        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
      });

      // Generate Excel file buffer
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      
      // Create blob and save file
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      FileSaver.saveAs(blob, filename);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      throw new Error('فشل في تصدير البيانات إلى Excel');
    }
  }

  /**
   * Import data from Excel file
   */
  static importFromExcel(file: File): Promise<{ [sheetName: string]: any[] }> {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader();
        
        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target?.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });
            
            const result: { [sheetName: string]: any[] } = {};
            
            // Process each sheet
            workbook.SheetNames.forEach(sheetName => {
              const worksheet = workbook.Sheets[sheetName];
              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
              
              // Convert to objects with proper headers
              if (jsonData.length > 0) {
                const headers = jsonData[0] as string[];
                const rows = jsonData.slice(1) as any[][];
                
                result[sheetName] = rows.map(row => {
                  const obj: any = {};
                  headers.forEach((header, index) => {
                    obj[header] = row[index] || '';
                  });
                  return obj;
                });
              }
            });
            
            resolve(result);
          } catch (error) {
            reject(new Error('فشل في قراءة ملف Excel'));
          }
        };
        
        reader.onerror = () => {
          reject(new Error('فشل في قراءة الملف'));
        };
        
        reader.readAsArrayBuffer(file);
      } catch (error) {
        reject(new Error('فشل في معالجة الملف'));
      }
    });
  }

  /**
   * Export employees data
   */
  static exportEmployees(employees: any[]): void {
    const employeeData = employees.map(emp => ({
      'الرقم التعريفي': emp.id,
      'الاسم': emp.name,
      'البريد الإلكتروني': emp.email,
      'رقم الهاتف': emp.phone,
      'القسم': emp.department,
      'المنصب': emp.position,
      'الدور': emp.role,
      'المدير المباشر': emp.manager || '',
      'تاريخ التوظيف': emp.hireDate,
      'الراتب': emp.salary,
      'الحالة': emp.status,
    }));

    this.exportToExcel([{
      sheetName: 'الموظفين',
      data: employeeData
    }], 'employees.xlsx');
  }

  /**
   * Export rides data
   */
  static exportRides(rides: any[]): void {
    const ridesData = rides.map(ride => ({
      'رقم الرحلة': ride.id,
      'اسم الراكب': ride.passengerName,
      'رقم هاتف الراكب': ride.passengerPhone,
      'اسم السائق': ride.driverName,
      'رقم هاتف السائق': ride.driverPhone,
      'نقطة الانطلاق': ride.pickupLocation?.address || '',
      'الوجهة': ride.destination?.address || '',
      'التكلفة': ride.fare,
      'المسافة': ride.distance,
      'وقت الطلب': ride.requestTime,
      'وقت البدء': ride.startTime,
      'وقت الانتهاء': ride.endTime,
      'الحالة': ride.status,
      'طريقة الدفع': ride.paymentMethod,
    }));

    this.exportToExcel([{
      sheetName: 'الرحلات',
      data: ridesData
    }], 'rides.xlsx');
  }

  /**
   * Export financial data
   */
  static exportFinancialData(invoices: any[], payments: any[]): void {
    const invoicesData = invoices.map(invoice => ({
      'رقم الفاتورة': invoice.id,
      'اسم العميل': invoice.customerName,
      'المبلغ': invoice.amount,
      'تاريخ الإصدار': invoice.issueDate,
      'تاريخ الاستحقاق': invoice.dueDate,
      'الحالة': invoice.status,
    }));

    const paymentsData = payments.map(payment => ({
      'رقم المعاملة': payment.id,
      'رقم الفاتورة': payment.invoiceId,
      'المبلغ': payment.amount,
      'تاريخ الدفع': payment.paymentDate,
      'طريقة الدفع': payment.method,
      'الحالة': payment.status,
    }));

    this.exportToExcel([
      {
        sheetName: 'الفواتير',
        data: invoicesData
      },
      {
        sheetName: 'المدفوعات',
        data: paymentsData
      }
    ], 'financial_data.xlsx');
  }

  /**
   * Export fleet data
   */
  static exportFleetData(vehicles: any[]): void {
    const vehiclesData = vehicles.map(vehicle => ({
      'رقم المركبة': vehicle.id,
      'رقم اللوحة': vehicle.plateNumber,
      'النوع': vehicle.type,
      'الموديل': vehicle.model,
      'السنة': vehicle.year,
      'اسم السائق': vehicle.driverName,
      'الحالة': vehicle.status,
      'الموقع الحالي': vehicle.currentLocation,
      'المسافة المقطوعة': vehicle.totalDistance,
      'عدد الرحلات': vehicle.totalTrips,
      'تاريخ آخر صيانة': vehicle.lastMaintenance,
    }));

    this.exportToExcel([{
      sheetName: 'الأسطول',
      data: vehiclesData
    }], 'fleet_data.xlsx');
  }

  /**
   * Import employees from Excel
   */
  static async importEmployees(file: File): Promise<any[]> {
    try {
      const data = await this.importFromExcel(file);
      const employeesSheet = data['الموظفين'] || data['Employees'] || Object.values(data)[0];
      
      if (!employeesSheet || employeesSheet.length === 0) {
        throw new Error('لم يتم العثور على بيانات الموظفين في الملف');
      }

      // Validate and transform data
      return employeesSheet.map((row: any, index: number) => {
        if (!row['الاسم'] && !row['Name']) {
          throw new Error(`الصف ${index + 1}: اسم الموظف مطلوب`);
        }
        
        if (!row['البريد الإلكتروني'] && !row['Email']) {
          throw new Error(`الصف ${index + 1}: البريد الإلكتروني مطلوب`);
        }

        return {
          name: row['الاسم'] || row['Name'],
          email: row['البريد الإلكتروني'] || row['Email'],
          phone: row['رقم الهاتف'] || row['Phone'],
          department: row['القسم'] || row['Department'],
          position: row['المنصب'] || row['Position'],
          role: row['الدور'] || row['Role'],
          manager: row['المدير المباشر'] || row['Manager'],
          hireDate: row['تاريخ التوظيف'] || row['Hire Date'],
          salary: parseFloat(row['الراتب'] || row['Salary']) || 0,
          status: row['الحالة'] || row['Status'] || 'active',
        };
      });
    } catch (error) {
      console.error('Error importing employees:', error);
      throw error;
    }
  }

  /**
   * Generate template for employees import
   */
  static downloadEmployeeTemplate(): void {
    const templateData = [{
      'الاسم': 'مثال: أحمد محمد',
      'البريد الإلكتروني': '<EMAIL>',
      'رقم الهاتف': '+966501234567',
      'القسم': 'الإدارة العامة',
      'المنصب': 'موظف',
      'الدور': 'employee',
      'المدير المباشر': 'المدير العام',
      'تاريخ التوظيف': '2024-01-01',
      'الراتب': '5000',
      'الحالة': 'active',
    }];

    this.exportToExcel([{
      sheetName: 'قالب الموظفين',
      data: templateData
    }], 'employee_template.xlsx');
  }

  /**
   * Validate Excel file
   */
  static validateExcelFile(file: File): boolean {
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    return validTypes.includes(file.type) && file.size > 0;
  }
}

export default ExcelService;
