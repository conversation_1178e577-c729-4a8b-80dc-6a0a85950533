import React, { useState, useEffect, useRef, use<PERSON>allback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  ButtonGroup,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Fab,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Map as MapIcon,
  Layers as LayersIcon,
  MyLocation as MyLocationIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Traffic as TrafficIcon,
  Satellite as SatelliteIcon,
  DirectionsCar as CarIcon,
  LocationOn as LocationIcon,
  Settings as SettingsIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon
} from '@mui/icons-material';

// Leaflet imports
import { MapContainer, TileLayer, Marker, Popup, useMap, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface Vehicle {
  id: string;
  lat: number;
  lng: number;
  speed: number;
  heading: number;
  status: string;
  driver: string;
  lastUpdate: string;
}

interface MapProvider {
  id: string;
  name: string;
  url: string;
  attribution: string;
  maxZoom: number;
  subdomains?: string[];
}

interface RealInteractiveMapProps {
  height?: string;
  showControls?: boolean;
  showVehicles?: boolean;
  center?: [number, number];
  zoom?: number;
  onVehicleClick?: (vehicle: Vehicle) => void;
  onMapClick?: (lat: number, lng: number) => void;
}

// Map providers configuration
const MAP_PROVIDERS: MapProvider[] = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    maxZoom: 19
  },
  {
    id: 'cartodb-light',
    name: 'CartoDB Light',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
    maxZoom: 19,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'cartodb-dark',
    name: 'CartoDB Dark',
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
    maxZoom: 19,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'esri-satellite',
    name: 'Satellite (Esri)',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© <a href="https://www.esri.com/">Esri</a> — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
    maxZoom: 18
  }
];

// Default center (Riyadh, Saudi Arabia)
const DEFAULT_CENTER: [number, number] = [24.7136, 46.6753];
const DEFAULT_ZOOM = 12;

// Map event handler component
const MapEventHandler: React.FC<{
  onMapClick?: (lat: number, lng: number) => void;
}> = ({ onMapClick }) => {
  useMapEvents({
    click: (e) => {
      if (onMapClick) {
        onMapClick(e.latlng.lat, e.latlng.lng);
      }
    }
  });
  return null;
};

// Map controls component
const MapControls: React.FC<{
  onZoomIn: () => void;
  onZoomOut: () => void;
  onLocate: () => void;
}> = ({ onZoomIn, onZoomOut, onLocate }) => {
  const map = useMap();

  useEffect(() => {
    // Add custom controls
    const customControl = L.control({ position: 'topright' });
    
    customControl.onAdd = () => {
      const div = L.DomUtil.create('div', 'custom-map-controls');
      div.style.background = 'white';
      div.style.padding = '5px';
      div.style.borderRadius = '5px';
      div.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
      
      return div;
    };
    
    customControl.addTo(map);
    
    return () => {
      map.removeControl(customControl);
    };
  }, [map]);

  return null;
};

const RealInteractiveMap: React.FC<RealInteractiveMapProps> = ({
  height = '600px',
  showControls = true,
  showVehicles = true,
  center = DEFAULT_CENTER,
  zoom = DEFAULT_ZOOM,
  onVehicleClick,
  onMapClick
}) => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState('openstreetmap');
  const [showTraffic, setShowTraffic] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>(center);
  const [mapZoom, setMapZoom] = useState(zoom);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [controlsOpen, setControlsOpen] = useState(false);
  const [mapKey, setMapKey] = useState(0);
  const mapRef = useRef<L.Map | null>(null);

  // Load vehicles data
  useEffect(() => {
    loadVehicles();
    const interval = setInterval(loadVehicles, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8085/api/map/vehicles');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setVehicles(data.data);
          setError(null);
        }
      } else {
        throw new Error('Failed to load vehicles');
      }
    } catch (err) {
      console.error('Failed to load vehicles:', err);
      
      // Fallback to mock data
      setVehicles([
        {
          id: 'vehicle_001',
          lat: 24.7136,
          lng: 46.6753,
          speed: 45,
          heading: 90,
          status: 'active',
          driver: 'Ahmed Mohamed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_002',
          lat: 24.7200,
          lng: 46.6800,
          speed: 30,
          heading: 180,
          status: 'active',
          driver: 'Sara Ahmed',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'vehicle_003',
          lat: 24.7100,
          lng: 46.6700,
          speed: 0,
          heading: 0,
          status: 'idle',
          driver: 'Mohamed Ali',
          lastUpdate: new Date().toISOString()
        }
      ]);
      
      setError('Using mock vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentProvider = (): MapProvider => {
    return MAP_PROVIDERS.find(p => p.id === selectedProvider) || MAP_PROVIDERS[0];
  };

  const handleProviderChange = (providerId: string) => {
    setSelectedProvider(providerId);
  };

  const handleRefresh = () => {
    loadVehicles();
  };

  const handleLocateUser = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userPos: [number, number] = [
            position.coords.latitude,
            position.coords.longitude
          ];
          setUserLocation(userPos);
          setMapCenter(userPos);
          setMapZoom(15);
        },
        (error) => {
          console.error('Geolocation error:', error);
          alert('تعذر الحصول على موقعك الحالي');
        }
      );
    } else {
      alert('المتصفح لا يدعم تحديد الموقع');
    }
  };

  const handleZoomIn = () => {
    setMapZoom(prev => Math.min(prev + 1, 19));
  };

  const handleZoomOut = () => {
    setMapZoom(prev => Math.max(prev - 1, 1));
  };

  const createVehicleIcon = (vehicle: Vehicle) => {
    const color = vehicle.status === 'active' ? '#4CAF50' : '#FFC107';
    const rotation = vehicle.heading;
    
    return L.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: white;
          font-weight: bold;
          transform: rotate(${rotation}deg);
          cursor: pointer;
        ">
          🚗
        </div>
      `,
      className: 'vehicle-marker',
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    });
  };

  const createUserLocationIcon = () => {
    return L.divIcon({
      html: `
        <div style="
          background-color: #2196F3;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
          animation: pulse 2s infinite;
        "></div>
        <style>
          @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
          }
        </style>
      `,
      className: 'user-location-marker',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });
  };

  if (loading && vehicles.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={height}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          جاري تحميل الخريطة التفاعلية...
        </Typography>
      </Box>
    );
  }

  const currentProvider = getCurrentProvider();

  return (
    <Paper sx={{ height, position: 'relative', overflow: 'hidden' }}>
      {/* Floating Controls */}
      {showControls && (
        <>
          {/* Settings FAB */}
          <Fab
            color="primary"
            size="medium"
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              zIndex: 1000
            }}
            onClick={() => setControlsOpen(true)}
          >
            <SettingsIcon />
          </Fab>

          {/* Map Controls */}
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              zIndex: 1000,
              display: 'flex',
              flexDirection: 'column',
              gap: 1
            }}
          >
            <ButtonGroup orientation="vertical" variant="contained" size="small">
              <Tooltip title="تكبير">
                <IconButton onClick={handleZoomIn} sx={{ bgcolor: 'white', color: 'primary.main' }}>
                  <ZoomInIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="تصغير">
                <IconButton onClick={handleZoomOut} sx={{ bgcolor: 'white', color: 'primary.main' }}>
                  <ZoomOutIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="موقعي">
                <IconButton onClick={handleLocateUser} sx={{ bgcolor: 'white', color: 'primary.main' }}>
                  <MyLocationIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="تحديث">
                <IconButton onClick={handleRefresh} sx={{ bgcolor: 'white', color: 'primary.main' }}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </ButtonGroup>
          </Box>

          {/* Vehicle Stats */}
          {showVehicles && vehicles.length > 0 && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 16,
                left: 16,
                zIndex: 1000
              }}
            >
              <Card sx={{ minWidth: 200 }}>
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Typography variant="subtitle2" gutterBottom>
                    إحصائيات المركبات
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Chip
                      icon={<CarIcon />}
                      label={`المجموع: ${vehicles.length}`}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={`نشطة: ${vehicles.filter(v => v.status === 'active').length}`}
                      size="small"
                      color="success"
                    />
                    <Chip
                      label={`متوقفة: ${vehicles.filter(v => v.status === 'idle').length}`}
                      size="small"
                      color="warning"
                    />
                  </Box>
                  {error && (
                    <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
                      {error}
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Box>
          )}

          {/* Controls Drawer */}
          <Drawer
            anchor="left"
            open={controlsOpen}
            onClose={() => setControlsOpen(false)}
            sx={{ zIndex: 1300 }}
          >
            <Box sx={{ width: 300, p: 2 }}>
              <Typography variant="h6" gutterBottom>
                إعدادات الخريطة
              </Typography>
              
              <Divider sx={{ mb: 2 }} />

              {/* Provider Selection */}
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>مزود الخريطة</InputLabel>
                <Select
                  value={selectedProvider}
                  onChange={(e) => handleProviderChange(e.target.value)}
                  label="مزود الخريطة"
                >
                  {MAP_PROVIDERS.map(provider => (
                    <MenuItem key={provider.id} value={provider.id}>
                      {provider.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Map Options */}
              <FormControlLabel
                control={
                  <Switch
                    checked={showTraffic}
                    onChange={(e) => setShowTraffic(e.target.checked)}
                  />
                }
                label="عرض حركة المرور"
                sx={{ mb: 1, display: 'block' }}
              />

              <Divider sx={{ my: 2 }} />

              {/* Map Info */}
              <Typography variant="subtitle2" gutterBottom>
                معلومات الخريطة
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                المزود الحالي: {currentProvider.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                المركز: {mapCenter[0].toFixed(4)}, {mapCenter[1].toFixed(4)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مستوى التكبير: {mapZoom}
              </Typography>
            </Box>
          </Drawer>
        </>
      )}

      {/* Map Container */}
      <MapContainer
        key={`map-${mapKey}-${selectedProvider}`}
        center={mapCenter}
        zoom={mapZoom}
        style={{ height: '100%', width: '100%' }}
        zoomControl={false}
        ref={mapRef}
      >
        {/* Base Tile Layer */}
        <TileLayer
          url={currentProvider.url}
          attribution={currentProvider.attribution}
          maxZoom={currentProvider.maxZoom}
          subdomains={currentProvider.subdomains}
        />

        {/* Map Event Handler */}
        <MapEventHandler onMapClick={onMapClick} />

        {/* Map Controls */}
        <MapControls
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onLocate={handleLocateUser}
        />

        {/* User Location Marker */}
        {userLocation && (
          <Marker
            position={userLocation}
            icon={createUserLocationIcon()}
          >
            <Popup>
              <Card sx={{ minWidth: 150 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    موقعك الحالي
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>الإحداثيات:</strong><br />
                    {userLocation[0].toFixed(6)}, {userLocation[1].toFixed(6)}
                  </Typography>
                </CardContent>
              </Card>
            </Popup>
          </Marker>
        )}

        {/* Vehicle Markers */}
        {showVehicles && vehicles.map(vehicle => (
          <Marker
            key={vehicle.id}
            position={[vehicle.lat, vehicle.lng]}
            icon={createVehicleIcon(vehicle)}
            eventHandlers={{
              click: () => onVehicleClick?.(vehicle)
            }}
          >
            <Popup>
              <Card sx={{ minWidth: 250 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    مركبة {vehicle.id}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>السائق:</strong> {vehicle.driver}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>السرعة:</strong> {vehicle.speed} كم/س
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>الاتجاه:</strong> {vehicle.heading}°
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>الإحداثيات:</strong> {vehicle.lat.toFixed(4)}, {vehicle.lng.toFixed(4)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>آخر تحديث:</strong> {new Date(vehicle.lastUpdate).toLocaleString('ar-SA')}
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={vehicle.status === 'active' ? 'نشطة' : 'متوقفة'}
                      color={vehicle.status === 'active' ? 'success' : 'warning'}
                      size="small"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </Paper>
  );
};

export default RealInteractiveMap;
