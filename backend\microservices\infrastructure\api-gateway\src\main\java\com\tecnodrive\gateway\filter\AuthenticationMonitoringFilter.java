package com.tecnodrive.gateway.filter;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Authentication Monitoring Filter for API Gateway
 * Monitors authentication attempts, failures, and suspicious activities
 */
@Slf4j
@Component
public class AuthenticationMonitoringFilter implements GlobalFilter, Ordered {

    private final MeterRegistry meterRegistry;
    private final Counter authenticationAttempts;
    private final Counter authenticationFailures;
    private final Counter authenticationSuccesses;
    private final Counter suspiciousActivities;
    private final Timer authenticationTimer;
    
    // Rate limiting for failed attempts per IP
    private final ConcurrentHashMap<String, AtomicInteger> failedAttemptsPerIp = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Instant> lastAttemptPerIp = new ConcurrentHashMap<>();
    
    // Suspicious activity tracking
    private final ConcurrentHashMap<String, AtomicInteger> suspiciousActivityPerIp = new ConcurrentHashMap<>();
    
    private static final int MAX_FAILED_ATTEMPTS_PER_IP = 10;
    private static final Duration RATE_LIMIT_WINDOW = Duration.ofMinutes(15);
    private static final int MAX_SUSPICIOUS_ACTIVITIES = 5;

    public AuthenticationMonitoringFilter(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.authenticationAttempts = Counter.builder("gateway.auth.attempts")
                .description("Total authentication attempts")
                .register(meterRegistry);
        this.authenticationFailures = Counter.builder("gateway.auth.failures")
                .description("Failed authentication attempts")
                .register(meterRegistry);
        this.authenticationSuccesses = Counter.builder("gateway.auth.successes")
                .description("Successful authentication attempts")
                .register(meterRegistry);
        this.suspiciousActivities = Counter.builder("gateway.auth.suspicious")
                .description("Suspicious authentication activities")
                .register(meterRegistry);
        this.authenticationTimer = Timer.builder("gateway.auth.duration")
                .description("Authentication request duration")
                .register(meterRegistry);
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().value();
        String clientIp = getClientIp(request);
        
        // Only monitor authentication-related endpoints
        if (!isAuthenticationEndpoint(path)) {
            return chain.filter(exchange);
        }

        Timer.Sample sample = Timer.start(meterRegistry);
        authenticationAttempts.increment();

        // Check if IP is rate limited
        if (isRateLimited(clientIp)) {
            log.warn("Rate limited authentication attempt from IP: {}", clientIp);
            suspiciousActivities.increment();
            recordSuspiciousActivity(clientIp);
            return blockRequest(exchange, "Rate Limited");
        }

        return chain.filter(exchange)
                .doOnSuccess(aVoid -> {
                    sample.stop(authenticationTimer);
                    ServerHttpResponse response = exchange.getResponse();
                    HttpStatus statusCode = (HttpStatus) response.getStatusCode();
                    
                    if (statusCode != null) {
                        if (statusCode.is2xxSuccessful()) {
                            handleSuccessfulAuthentication(clientIp, path);
                        } else if (statusCode.is4xxClientError()) {
                            handleFailedAuthentication(clientIp, path, statusCode);
                        }
                    }
                })
                .doOnError(throwable -> {
                    sample.stop(authenticationTimer);
                    log.error("Authentication error for IP {}: {}", clientIp, throwable.getMessage());
                    handleFailedAuthentication(clientIp, path, HttpStatus.INTERNAL_SERVER_ERROR);
                });
    }

    private boolean isAuthenticationEndpoint(String path) {
        return path.contains("/auth/") || 
               path.contains("/login") || 
               path.contains("/register") ||
               path.contains("/oauth") ||
               path.contains("/token");
    }

    private void handleSuccessfulAuthentication(String clientIp, String path) {
        authenticationSuccesses.increment();
        
        // Reset failed attempts for this IP
        failedAttemptsPerIp.remove(clientIp);
        lastAttemptPerIp.remove(clientIp);
        
        log.info("Successful authentication from IP: {} on path: {}", clientIp, path);
        
        // Log to security audit
        logSecurityEvent("AUTH_SUCCESS", clientIp, path, null);
    }

    private void handleFailedAuthentication(String clientIp, String path, HttpStatus statusCode) {
        authenticationFailures.increment();
        
        // Increment failed attempts for this IP
        AtomicInteger attempts = failedAttemptsPerIp.computeIfAbsent(clientIp, k -> new AtomicInteger(0));
        int currentAttempts = attempts.incrementAndGet();
        lastAttemptPerIp.put(clientIp, Instant.now());
        
        log.warn("Failed authentication from IP: {} on path: {} (attempt #{}, status: {})", 
                clientIp, path, currentAttempts, statusCode);
        
        // Check for suspicious activity
        if (currentAttempts >= MAX_FAILED_ATTEMPTS_PER_IP / 2) {
            recordSuspiciousActivity(clientIp);
        }
        
        // Log to security audit
        logSecurityEvent("AUTH_FAILURE", clientIp, path, statusCode.toString());
        
        // Alert if too many failures
        if (currentAttempts >= MAX_FAILED_ATTEMPTS_PER_IP) {
            log.error("SECURITY ALERT: Too many failed authentication attempts from IP: {} ({})", 
                    clientIp, currentAttempts);
            alertSecurityTeam(clientIp, currentAttempts);
        }
    }

    private boolean isRateLimited(String clientIp) {
        AtomicInteger attempts = failedAttemptsPerIp.get(clientIp);
        Instant lastAttempt = lastAttemptPerIp.get(clientIp);
        
        if (attempts == null || lastAttempt == null) {
            return false;
        }
        
        // Check if rate limit window has expired
        if (Duration.between(lastAttempt, Instant.now()).compareTo(RATE_LIMIT_WINDOW) > 0) {
            failedAttemptsPerIp.remove(clientIp);
            lastAttemptPerIp.remove(clientIp);
            return false;
        }
        
        return attempts.get() >= MAX_FAILED_ATTEMPTS_PER_IP;
    }

    private void recordSuspiciousActivity(String clientIp) {
        AtomicInteger activities = suspiciousActivityPerIp.computeIfAbsent(clientIp, k -> new AtomicInteger(0));
        int currentActivities = activities.incrementAndGet();
        
        if (currentActivities >= MAX_SUSPICIOUS_ACTIVITIES) {
            log.error("SECURITY ALERT: Multiple suspicious activities from IP: {} ({})", 
                    clientIp, currentActivities);
            alertSecurityTeam(clientIp, currentActivities);
        }
    }

    private void logSecurityEvent(String eventType, String clientIp, String path, String details) {
        // Log structured security event for SIEM systems
        log.info("SECURITY_EVENT: type={}, ip={}, path={}, details={}, timestamp={}", 
                eventType, clientIp, path, details, Instant.now());
        
        // Could also send to external security monitoring system
        // securityEventPublisher.publish(new SecurityEvent(eventType, clientIp, path, details));
    }

    private void alertSecurityTeam(String clientIp, int attempts) {
        // In a real implementation, this would send alerts via:
        // - Email
        // - Slack/Teams notification
        // - SIEM system
        // - Security dashboard
        
        log.error("SECURITY TEAM ALERT: Suspicious activity from IP: {} with {} attempts/activities", 
                clientIp, attempts);
        
        // Example: Send to notification service
        // notificationService.sendSecurityAlert(
        //     "Suspicious authentication activity detected from IP: " + clientIp
        // );
    }

    private Mono<Void> blockRequest(ServerWebExchange exchange, String reason) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add("Content-Type", "application/json");
        response.getHeaders().add("Retry-After", "900"); // 15 minutes
        
        String body = String.format(
                "{\"error\":\"Too Many Requests\",\"reason\":\"%s\",\"retryAfter\":900,\"timestamp\":\"%s\"}",
                reason, Instant.now()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(body.getBytes());
        return response.writeWith(reactor.core.publisher.Mono.just(buffer));
    }

    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        var remoteAddress = request.getRemoteAddress();
        if (remoteAddress != null && remoteAddress.getAddress() != null) {
            return remoteAddress.getAddress().getHostAddress();
        }
        
        return "unknown";
    }

    @Override
    public int getOrder() {
        return -50; // Execute after security filter but before routing
    }
}
