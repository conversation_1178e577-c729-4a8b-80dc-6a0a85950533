import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Storage as DatabaseIcon,
  TableChart as TableIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  Assessment as AnalyticsIcon,
  Security as SecurityIcon,
  Speed as PerformanceIcon,
  CloudDownload as BackupIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import databaseService from '../../services/databaseService';
import { DatabaseConnection, DatabaseStats, TableInfo } from '../../types/api';
import DatabaseExplorer from './DatabaseExplorer';
import DataVisualization from './DataVisualization';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`database-tabpanel-${index}`}
      aria-labelledby={`database-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const DatabaseDashboard: React.FC = () => {
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<string>('');
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [connectionDialog, setConnectionDialog] = useState(false);
  const [newConnection, setNewConnection] = useState({
    name: '',
    type: 'postgresql' as 'postgresql' | 'mongodb' | 'redis',
    host: 'localhost',
    port: 5432,
    database: '',
    username: '',
    password: ''
  });

  useEffect(() => {
    loadConnections();
    loadStats();
  }, []);

  useEffect(() => {
    if (selectedConnection) {
      loadTables(selectedConnection);
    }
  }, [selectedConnection]);

  const loadConnections = async () => {
    try {
      setLoading(true);
      const connectionsData = await databaseService.getConnections();
      setConnections(connectionsData);
      if (connectionsData.length > 0 && !selectedConnection) {
        setSelectedConnection(connectionsData[0].id);
      }
    } catch (error) {
      console.error('Error loading connections:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await databaseService.getStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const loadTables = async (connectionId: string) => {
    try {
      setLoading(true);
      const tablesData = await databaseService.getTables(connectionId);
      setTables(tablesData);
    } catch (error) {
      console.error('Error loading tables:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddConnection = async () => {
    try {
      await databaseService.addConnection(newConnection);
      setConnectionDialog(false);
      setNewConnection({
        name: '',
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        database: '',
        username: '',
        password: ''
      });
      loadConnections();
    } catch (error) {
      console.error('Error adding connection:', error);
    }
  };

  const getConnectionStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'success';
      case 'disconnected': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getConnectionStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <SuccessIcon />;
      case 'disconnected': return <WarningIcon />;
      case 'error': return <ErrorIcon />;
      default: return <DatabaseIcon />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          مستكشف قواعد البيانات
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setConnectionDialog(true)}
            sx={{ mr: 1 }}
          >
            إضافة اتصال
          </Button>
          <IconButton onClick={loadConnections} color="primary">
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <DatabaseIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      قواعد البيانات
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalDatabases}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TableIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      الجداول
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalTables}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <AnalyticsIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      إجمالي السجلات
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalRows.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PerformanceIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      حجم البيانات
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalSize}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Connections Sidebar */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الاتصالات
              </Typography>
              
              {loading && <LinearProgress sx={{ mb: 2 }} />}
              
              <List>
                {connections.map((connection) => (
                  <ListItem
                    key={connection.id}
                    button
                    selected={selectedConnection === connection.id}
                    onClick={() => setSelectedConnection(connection.id)}
                  >
                    <ListItemIcon>
                      {getConnectionStatusIcon(connection.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={connection.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {connection.type} - {connection.host}:{connection.port}
                          </Typography>
                          <Chip
                            label={connection.status}
                            size="small"
                            color={getConnectionStatusColor(connection.status) as any}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>

              {connections.length === 0 && !loading && (
                <Alert severity="info">
                  لا توجد اتصالات متاحة. أضف اتصال جديد للبدء.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content Area */}
        <Grid item xs={12} md={9}>
          <Card>
            <CardContent>
              <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                <Tab label="مستكشف البيانات" />
                <Tab label="تصور البيانات" />
                <Tab label="الأداء" />
                <Tab label="الأمان" />
                <Tab label="النسخ الاحتياطي" />
              </Tabs>

              <TabPanel value={tabValue} index={0}>
                {selectedConnection ? (
                  <DatabaseExplorer />
                ) : (
                  <Alert severity="info">
                    اختر اتصال قاعدة بيانات لبدء الاستكشاف
                  </Alert>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                {selectedConnection ? (
                  <DataVisualization database={selectedConnection} tableName="" />
                ) : (
                  <Alert severity="info">
                    اختر اتصال قاعدة بيانات لعرض التصورات
                  </Alert>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <Typography variant="h6" gutterBottom>
                  مراقبة الأداء
                </Typography>
                <Alert severity="info">
                  ميزة مراقبة الأداء قيد التطوير
                </Alert>
              </TabPanel>

              <TabPanel value={tabValue} index={3}>
                <Typography variant="h6" gutterBottom>
                  إعدادات الأمان
                </Typography>
                <Alert severity="info">
                  ميزة إعدادات الأمان قيد التطوير
                </Alert>
              </TabPanel>

              <TabPanel value={tabValue} index={4}>
                <Typography variant="h6" gutterBottom>
                  النسخ الاحتياطي والاستعادة
                </Typography>
                <Alert severity="info">
                  ميزة النسخ الاحتياطي قيد التطوير
                </Alert>
              </TabPanel>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Connection Dialog */}
      <Dialog open={connectionDialog} onClose={() => setConnectionDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة اتصال جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم الاتصال"
                value={newConnection.name}
                onChange={(e) => setNewConnection({ ...newConnection, name: e.target.value })}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>نوع قاعدة البيانات</InputLabel>
                <Select
                  value={newConnection.type}
                  onChange={(e) => setNewConnection({ ...newConnection, type: e.target.value as any })}
                  label="نوع قاعدة البيانات"
                >
                  <MenuItem value="postgresql">PostgreSQL</MenuItem>
                  <MenuItem value="mongodb">MongoDB</MenuItem>
                  <MenuItem value="redis">Redis</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={8}>
              <TextField
                fullWidth
                label="المضيف"
                value={newConnection.host}
                onChange={(e) => setNewConnection({ ...newConnection, host: e.target.value })}
              />
            </Grid>

            <Grid item xs={4}>
              <TextField
                fullWidth
                label="المنفذ"
                type="number"
                value={newConnection.port}
                onChange={(e) => setNewConnection({ ...newConnection, port: parseInt(e.target.value) })}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="اسم قاعدة البيانات"
                value={newConnection.database}
                onChange={(e) => setNewConnection({ ...newConnection, database: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConnectionDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddConnection} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseDashboard;
