import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Route as RouteIcon,
  Security as SecurityIcon,
  FilterList as FilterIcon,
  Code as CodeIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridRenderCellParams, GridActionsCellItem } from '@mui/x-data-grid';
import { apiGatewayService, RouteDefinitionDto, CreateRouteRequest } from '../../services/apiGatewayService';

const RoutesManagement: React.FC = () => {
  const [routes, setRoutes] = useState<RouteDefinitionDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAuthMethod, setFilterAuthMethod] = useState('ALL');
  const [filterEnabled, setFilterEnabled] = useState('ALL');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState<RouteDefinitionDto | null>(null);
  const [newRoute, setNewRoute] = useState<CreateRouteRequest>({
    id: '',
    uri: '',
    predicates: [],
    filters: [],
    authMethod: 'JWT',
    order: 1,
    metadata: {},
  });

  // Load routes data
  const loadRoutes = async () => {
    try {
      setLoading(true);
      const response = await apiGatewayService.getRoutes();
      
      if (response.success && response.data) {
        setRoutes(response.data);
      }
    } catch (error) {
      console.error('Error loading routes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRoutes();
  }, []);

  const getAuthMethodChip = (authMethod: string) => {
    const authConfig = {
      JWT: { label: 'JWT', color: 'primary' as const, icon: '🔐' },
      API_KEY: { label: 'API Key', color: 'secondary' as const, icon: '🔑' },
      OAUTH2: { label: 'OAuth2', color: 'info' as const, icon: '🌐' },
      BASIC: { label: 'Basic', color: 'warning' as const, icon: '🔒' },
      NONE: { label: 'None', color: 'default' as const, icon: '🔓' },
    };

    const config = authConfig[authMethod as keyof typeof authConfig] || { 
      label: authMethod, 
      color: 'default' as const, 
      icon: '❓' 
    };
    
    return (
      <Chip
        label={`${config.icon} ${config.label}`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getStatusChip = (enabled: boolean) => {
    return (
      <Chip
        label={enabled ? 'نشط' : 'معطل'}
        color={enabled ? 'success' : 'default'}
        size="small"
        variant="outlined"
        icon={enabled ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
      />
    );
  };

  const handleAddRoute = async () => {
    try {
      await apiGatewayService.createRoute(newRoute);
      setOpenAddDialog(false);
      setNewRoute({
        id: '',
        uri: '',
        predicates: [],
        filters: [],
        authMethod: 'JWT',
        order: 1,
        metadata: {},
      });
      loadRoutes();
      
      // Refresh gateway routes
      await apiGatewayService.refreshRoutes();
    } catch (error) {
      console.error('Error creating route:', error);
    }
  };

  const handleEditRoute = (route: RouteDefinitionDto) => {
    setSelectedRoute(route);
    setNewRoute({
      id: route.id,
      uri: route.uri,
      predicates: route.predicates,
      filters: route.filters,
      authMethod: route.authMethod,
      order: route.order,
      metadata: route.metadata,
    });
    setOpenEditDialog(true);
  };

  const handleUpdateRoute = async () => {
    if (!selectedRoute) return;

    try {
      await apiGatewayService.updateRoute(selectedRoute.id, newRoute);
      setOpenEditDialog(false);
      setSelectedRoute(null);
      setNewRoute({
        id: '',
        uri: '',
        predicates: [],
        filters: [],
        authMethod: 'JWT',
        order: 1,
        metadata: {},
      });
      loadRoutes();
      
      // Refresh gateway routes
      await apiGatewayService.refreshRoutes();
    } catch (error) {
      console.error('Error updating route:', error);
    }
  };

  const handleDeleteRoute = async (routeId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المسار؟')) {
      try {
        await apiGatewayService.deleteRoute(routeId);
        loadRoutes();
        
        // Refresh gateway routes
        await apiGatewayService.refreshRoutes();
      } catch (error) {
        console.error('Error deleting route:', error);
      }
    }
  };

  const handleToggleRoute = async (routeId: string, enabled: boolean) => {
    try {
      await apiGatewayService.updateRoute(routeId, { enabled: !enabled });
      loadRoutes();
      
      // Refresh gateway routes
      await apiGatewayService.refreshRoutes();
    } catch (error) {
      console.error('Error toggling route:', error);
    }
  };

  const handleRefreshGateway = async () => {
    try {
      await apiGatewayService.refreshRoutes();
      console.log('Gateway routes refreshed successfully');
    } catch (error) {
      console.error('Error refreshing gateway:', error);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'معرف المسار',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <RouteIcon fontSize="small" />
          </Avatar>
          <Typography variant="body2" sx={{ fontWeight: 'medium', fontFamily: 'monospace' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'uri',
      headerName: 'URI الوجهة',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'predicates',
      headerName: 'المحددات',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {(params.value as string[]).map((predicate, index) => (
            <Chip
              key={index}
              label={predicate}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          ))}
        </Box>
      ),
    },
    {
      field: 'authMethod',
      headerName: 'طريقة المصادقة',
      width: 150,
      renderCell: (params: GridRenderCellParams) => getAuthMethodChip(params.value),
    },
    {
      field: 'order',
      headerName: 'الترتيب',
      width: 80,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          size="small"
          color="info"
          variant="filled"
        />
      ),
    },
    {
      field: 'enabled',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params: GridRenderCellParams) => getStatusChip(params.value),
    },
    {
      field: 'updatedAt',
      headerName: 'آخر تحديث',
      width: 130,
      valueGetter: (params) => params.value ? new Date(params.value).toLocaleDateString('ar-SA') : 'غير محدد',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض التفاصيل">
              <VisibilityIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => console.log('View route details:', params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEditRoute(params.row)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title={params.row.enabled ? "تعطيل" : "تفعيل"}>
              {params.row.enabled ? <CancelIcon /> : <CheckCircleIcon />}
            </Tooltip>
          }
          label={params.row.enabled ? "تعطيل" : "تفعيل"}
          onClick={() => handleToggleRoute(params.id as string, params.row.enabled)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDeleteRoute(params.id as string)}
        />,
      ],
    },
  ];

  const filteredRoutes = routes.filter(route => {
    const matchesSearch = route.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.uri.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.predicates.some(p => p.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesAuthMethod = filterAuthMethod === 'ALL' || route.authMethod === filterAuthMethod;
    const matchesEnabled = filterEnabled === 'ALL' || 
                          (filterEnabled === 'ENABLED' && route.enabled) ||
                          (filterEnabled === 'DISABLED' && !route.enabled);
    
    return matchesSearch && matchesAuthMethod && matchesEnabled;
  });

  // Calculate stats
  const totalRoutes = routes.length;
  const enabledRoutes = routes.filter(r => r.enabled).length;
  const disabledRoutes = routes.filter(r => !r.enabled).length;
  const jwtRoutes = routes.filter(r => r.authMethod === 'JWT').length;
  const apiKeyRoutes = routes.filter(r => r.authMethod === 'API_KEY').length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          إدارة المسارات (Routes)
        </Typography>
        <Typography variant="body1" color="text.secondary">
          إدارة وتكوين مسارات API Gateway بشكل ديناميكي
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                {totalRoutes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي المسارات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                {enabledRoutes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المسارات النشطة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                {disabledRoutes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المسارات المعطلة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                {jwtRoutes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مسارات JWT
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {apiKeyRoutes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                مسارات API Key
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              placeholder="البحث في المسارات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>طريقة المصادقة</InputLabel>
              <Select
                value={filterAuthMethod}
                label="طريقة المصادقة"
                onChange={(e) => setFilterAuthMethod(e.target.value)}
              >
                <MenuItem value="ALL">جميع الطرق</MenuItem>
                <MenuItem value="JWT">JWT</MenuItem>
                <MenuItem value="API_KEY">API Key</MenuItem>
                <MenuItem value="OAUTH2">OAuth2</MenuItem>
                <MenuItem value="BASIC">Basic</MenuItem>
                <MenuItem value="NONE">بدون مصادقة</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>الحالة</InputLabel>
              <Select
                value={filterEnabled}
                label="الحالة"
                onChange={(e) => setFilterEnabled(e.target.value)}
              >
                <MenuItem value="ALL">جميع الحالات</MenuItem>
                <MenuItem value="ENABLED">نشط</MenuItem>
                <MenuItem value="DISABLED">معطل</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              تصفية متقدمة
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefreshGateway}
            >
              تحديث Gateway
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setOpenAddDialog(true)}
            >
              إضافة مسار
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Data Grid */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredRoutes}
            columns={columns}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>
      </Card>

      {/* Add Route Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>إضافة مسار جديد</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="معرف المسار"
                  value={newRoute.id}
                  onChange={(e) => setNewRoute({ ...newRoute, id: e.target.value })}
                  fullWidth
                  required
                  placeholder="auth-service"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="URI الوجهة"
                  value={newRoute.uri}
                  onChange={(e) => setNewRoute({ ...newRoute, uri: e.target.value })}
                  fullWidth
                  required
                  placeholder="lb://auth-service"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="المحددات (Predicates)"
                  value={newRoute.predicates.join(', ')}
                  onChange={(e) => setNewRoute({ ...newRoute, predicates: e.target.value.split(', ').filter(p => p.trim()) })}
                  fullWidth
                  required
                  placeholder="Path=/api/auth/**"
                  helperText="افصل بين المحددات بفاصلة"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="المرشحات (Filters)"
                  value={newRoute.filters?.join(', ') || ''}
                  onChange={(e) => setNewRoute({ ...newRoute, filters: e.target.value.split(', ').filter(f => f.trim()) })}
                  fullWidth
                  placeholder="StripPrefix=1"
                  helperText="افصل بين المرشحات بفاصلة"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>طريقة المصادقة</InputLabel>
                  <Select
                    value={newRoute.authMethod}
                    label="طريقة المصادقة"
                    onChange={(e) => setNewRoute({ ...newRoute, authMethod: e.target.value as any })}
                  >
                    <MenuItem value="JWT">JWT</MenuItem>
                    <MenuItem value="API_KEY">API Key</MenuItem>
                    <MenuItem value="OAUTH2">OAuth2</MenuItem>
                    <MenuItem value="BASIC">Basic</MenuItem>
                    <MenuItem value="NONE">بدون مصادقة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="الترتيب"
                  type="number"
                  value={newRoute.order}
                  onChange={(e) => setNewRoute({ ...newRoute, order: parseInt(e.target.value) || 1 })}
                  fullWidth
                  inputProps={{ min: 1, max: 1000 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>إلغاء</Button>
          <Button onClick={handleAddRoute} variant="contained">إضافة</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Route Dialog */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>تعديل المسار: {selectedRoute?.id}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="معرف المسار"
                  value={newRoute.id}
                  onChange={(e) => setNewRoute({ ...newRoute, id: e.target.value })}
                  fullWidth
                  required
                  disabled
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="URI الوجهة"
                  value={newRoute.uri}
                  onChange={(e) => setNewRoute({ ...newRoute, uri: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="المحددات (Predicates)"
                  value={newRoute.predicates.join(', ')}
                  onChange={(e) => setNewRoute({ ...newRoute, predicates: e.target.value.split(', ').filter(p => p.trim()) })}
                  fullWidth
                  required
                  helperText="افصل بين المحددات بفاصلة"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="المرشحات (Filters)"
                  value={newRoute.filters?.join(', ') || ''}
                  onChange={(e) => setNewRoute({ ...newRoute, filters: e.target.value.split(', ').filter(f => f.trim()) })}
                  fullWidth
                  helperText="افصل بين المرشحات بفاصلة"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>طريقة المصادقة</InputLabel>
                  <Select
                    value={newRoute.authMethod}
                    label="طريقة المصادقة"
                    onChange={(e) => setNewRoute({ ...newRoute, authMethod: e.target.value as any })}
                  >
                    <MenuItem value="JWT">JWT</MenuItem>
                    <MenuItem value="API_KEY">API Key</MenuItem>
                    <MenuItem value="OAUTH2">OAuth2</MenuItem>
                    <MenuItem value="BASIC">Basic</MenuItem>
                    <MenuItem value="NONE">بدون مصادقة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="الترتيب"
                  type="number"
                  value={newRoute.order}
                  onChange={(e) => setNewRoute({ ...newRoute, order: parseInt(e.target.value) || 1 })}
                  fullWidth
                  inputProps={{ min: 1, max: 1000 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>إلغاء</Button>
          <Button onClick={handleUpdateRoute} variant="contained">حفظ التغييرات</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoutesManagement;
