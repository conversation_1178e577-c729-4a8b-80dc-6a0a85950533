apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: tecnodrive-dev

resources:
  - ../../base

namespace: tecnodrive-dev

namePrefix: dev-

commonLabels:
  environment: development
  tier: dev

commonAnnotations:
  environment: development

patchesStrategicMerge:
  - deployment-patches.yaml
  - service-patches.yaml

configMapGenerator:
  - name: tecnodrive-config
    behavior: merge
    literals:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - DATABASE_LOGGING=true
      - KAFKA_DEBUG=true

secretGenerator:
  - name: tecnodrive-secrets
    behavior: merge
    literals:
      - JWT_SECRET=dev-jwt-secret-123
      - DATABASE_PASSWORD=dev-password-123

images:
  - name: tecnodrive/auth-service
    newTag: dev-latest
  - name: tecnodrive/booking-service
    newTag: dev-latest
  - name: tecnodrive/location-service
    newTag: dev-latest
  - name: tecnodrive/notification-service
    newTag: dev-latest

replicas:
  - name: auth-service
    count: 1
  - name: booking-service
    count: 1
  - name: location-service
    count: 1
  - name: notification-service
    count: 1
