server:
  port: 8085

spring:
  application:
    name: location-service
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# TecnoDrive Configuration
tecnodrive:
  maps:
    default-provider: openstreetmap
    google:
      api-key: ${GOOGLE_MAPS_API_KEY:}
    mapbox:
      access-token: ${MAPBOX_ACCESS_TOKEN:}
    osm:
      tile-server: https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png
      nominatim-url: https://nominatim.openstreetmap.org
      osrm-url: https://router.project-osrm.org
    default-center:
      lat: 24.7136
      lng: 46.6753
    default-zoom: 12
    max-zoom: 19

logging:
  level:
    com.tecnodrive.locationservice: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/location-service.log
