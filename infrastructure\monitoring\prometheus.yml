# Prometheus Configuration for TecnoDrive Platform
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tecnodrive-platform'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Eureka Server
  - job_name: 'eureka-server'
    static_configs:
      - targets: ['eureka-server:8761']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:8081']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Ride Service
  - job_name: 'ride-service'
    static_configs:
      - targets: ['ride-service:8082']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s

  # Parcel Service
  - job_name: 'parcel-service'
    static_configs:
      - targets: ['parcel-service:8084']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Location Service
  - job_name: 'location-service'
    static_configs:
      - targets: ['location-service:8085']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s

  # Payment Service
  - job_name: 'payment-service'
    static_configs:
      - targets: ['payment-service:8086']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s

  # SaaS Management Service
  - job_name: 'saas-management-service'
    static_configs:
      - targets: ['saas-management-service:8087']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Notification Service
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:8088']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Financial Service
  - job_name: 'financial-service'
    static_configs:
      - targets: ['financial-service:8089']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # HR Service
  - job_name: 'hr-service'
    static_configs:
      - targets: ['hr-service:8089']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 60s

  # Fleet Service
  - job_name: 'fleet-service'
    static_configs:
      - targets: ['fleet-service:8092']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Analytics Service
  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:8090']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # Infrastructure Services
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 60s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    scrape_interval: 30s

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://api-gateway:8080/actuator/health
        - http://auth-service:8081/actuator/health
        - http://ride-service:8082/actuator/health
        - http://payment-service:8086/actuator/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration for long-term storage (optional)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Remote read configuration (optional)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"
