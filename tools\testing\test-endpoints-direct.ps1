# Test Endpoints Directly
Write-Host "🧪 Testing Location Service Endpoints Directly" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Function to start service and test
function Start-ServiceAndTest {
    Write-Host "`n🚀 Starting Location Service..." -ForegroundColor Yellow
    
    # Start the service in background
    $process = Start-Process -FilePath "java" -ArgumentList @(
        "-Dspring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration",
        "-jar", "services/location-service/target/location-service-1.0.0.jar",
        "--server.port=8085"
    ) -PassThru -WindowStyle Hidden
    
    Write-Host "⏳ Waiting for service to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 25
    
    # Test if service is running
    try {
        $healthCheck = Invoke-RestMethod -Uri "http://localhost:8085/actuator/health" -TimeoutSec 5
        Write-Host "✅ Service is running!" -ForegroundColor Green
        
        # Test endpoints
        Test-AllEndpoints
        
    } catch {
        Write-Host "❌ Service failed to start: $($_.Exception.Message)" -ForegroundColor Red
        
        # Try to get process output
        if ($process -and !$process.HasExited) {
            Write-Host "🔍 Service is still running, trying endpoints anyway..." -ForegroundColor Yellow
            Test-AllEndpoints
        }
    }
    
    # Cleanup
    if ($process -and !$process.HasExited) {
        Write-Host "`n🛑 Stopping service..." -ForegroundColor Yellow
        $process.Kill()
        $process.WaitForExit(5000)
    }
}

# Function to test all endpoints
function Test-AllEndpoints {
    Write-Host "`n🧪 Testing Map Endpoints..." -ForegroundColor Cyan
    
    $endpoints = @(
        @{ Name = "Enhanced Map Config"; Url = "http://localhost:8085/api/map/config/enhanced" },
        @{ Name = "Tile Servers"; Url = "http://localhost:8085/api/map/tiles" },
        @{ Name = "Map Styles"; Url = "http://localhost:8085/api/map/styles" },
        @{ Name = "Geocoding Search"; Url = "http://localhost:8085/api/map/geocode/search?q=riyadh&limit=3" },
        @{ Name = "Reverse Geocoding"; Url = "http://localhost:8085/api/map/geocode/reverse?lat=24.7136&lng=46.6753" },
        @{ Name = "Route Calculation"; Url = "http://localhost:8085/api/map/route?startLat=24.7136&startLng=46.6753&endLat=24.7236&endLng=46.6853" }
    )
    
    $successCount = 0
    $totalCount = $endpoints.Count
    
    foreach ($endpoint in $endpoints) {
        Write-Host "`n🔍 Testing: $($endpoint.Name)" -ForegroundColor White
        Write-Host "   URL: $($endpoint.Url)" -ForegroundColor Gray
        
        try {
            $response = Invoke-RestMethod -Uri $endpoint.Url -Method GET -TimeoutSec 10
            
            if ($response) {
                Write-Host "   ✅ Success!" -ForegroundColor Green
                
                # Display response summary
                if ($response.success -eq $true) {
                    Write-Host "   📊 Response: Success = $($response.success)" -ForegroundColor Cyan
                    
                    if ($response.data) {
                        if ($response.data -is [System.Collections.IDictionary]) {
                            Write-Host "   📋 Data Keys: $($response.data.Keys -join ', ')" -ForegroundColor Cyan
                        } elseif ($response.data -is [System.Array]) {
                            Write-Host "   📋 Data Count: $($response.data.Count)" -ForegroundColor Cyan
                        }
                    }
                    
                    if ($response.count) {
                        Write-Host "   🔢 Count: $($response.count)" -ForegroundColor Cyan
                    }
                } else {
                    Write-Host "   📄 Response Type: $($response.GetType().Name)" -ForegroundColor Cyan
                }
                
                $successCount++
            } else {
                Write-Host "   ❌ Empty response" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "   ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
            
            # Try to get more details
            if ($_.Exception.Response) {
                Write-Host "   📄 Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
            }
        }
    }
    
    # Summary
    Write-Host "`n📊 Test Summary" -ForegroundColor Cyan
    Write-Host "===============" -ForegroundColor Cyan
    Write-Host "✅ Successful: $successCount/$totalCount" -ForegroundColor Green
    Write-Host "❌ Failed: $($totalCount - $successCount)/$totalCount" -ForegroundColor Red
    
    $successRate = [math]::Round(($successCount / $totalCount) * 100, 1)
    
    if ($successRate -ge 90) {
        Write-Host "🎉 Excellent! $successRate% success rate" -ForegroundColor Green
    } elseif ($successRate -ge 70) {
        Write-Host "✅ Good! $successRate% success rate" -ForegroundColor Yellow
    } else {
        Write-Host "⚠️ Needs improvement: $successRate% success rate" -ForegroundColor Red
    }
}

# Function to test basic connectivity
function Test-BasicConnectivity {
    Write-Host "`n🔍 Testing Basic Connectivity..." -ForegroundColor Yellow
    
    $basicTests = @(
        @{ Name = "Test Controller Hello"; Url = "http://localhost:8085/api/test/hello" },
        @{ Name = "Test Controller Map Config"; Url = "http://localhost:8085/api/test/map-config" },
        @{ Name = "Test Controller Tiles"; Url = "http://localhost:8085/api/test/tiles" }
    )
    
    foreach ($test in $basicTests) {
        Write-Host "`n🔍 Testing: $($test.Name)" -ForegroundColor White
        
        try {
            $response = Invoke-RestMethod -Uri $test.Url -Method GET -TimeoutSec 5
            Write-Host "   ✅ Success: $($response.message -or $response.status -or 'Response received')" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Function to check if port is in use
function Test-PortAvailability {
    Write-Host "`n🔍 Checking Port 8085..." -ForegroundColor Yellow
    
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port 8085 -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ Port 8085 is in use (service might be running)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Port 8085 is not in use" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Could not check port: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "🎯 Starting Endpoint Testing Process..." -ForegroundColor Green

# Check if port is already in use
if (Test-PortAvailability) {
    Write-Host "`n🔍 Service appears to be running, testing directly..." -ForegroundColor Yellow
    Test-BasicConnectivity
    Test-AllEndpoints
} else {
    Write-Host "`n🚀 No service detected, starting new instance..." -ForegroundColor Yellow
    Start-ServiceAndTest
}

Write-Host "`n🎉 Endpoint Testing Complete!" -ForegroundColor Green
Write-Host "`n💡 Next Steps:" -ForegroundColor Yellow
Write-Host "   • If tests passed: Integrate with frontend" -ForegroundColor White
Write-Host "   • If tests failed: Check service logs" -ForegroundColor White
Write-Host "   • Test CORS in browser console" -ForegroundColor White
