package com.tecnodrive.rideservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Ride Service Application
 *
 * This service handles:
 * - Ride request creation and management
 * - Driver-passenger matching algorithm
 * - Real-time location tracking
 * - Dynamic pricing calculation
 * - Route optimization
 * - Ride status management
 * - Integration with payment service
 * - Real-time notifications via WebSocket
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication

@EnableFeignClients
@EnableJpaAuditing
@EnableAsync
@EnableKafka
@EnableTransactionManagement
public class RideServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(RideServiceApplication.class, args);
    }
}
