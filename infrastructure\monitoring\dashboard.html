<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TECNO DRIVE - لوحة التحكم الرئيسية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2em;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .service-icon {
            font-size: 2em;
            margin-left: 15px;
        }
        
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .service-url {
            display: block;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin: 10px 0;
            padding: 10px 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        
        .service-url:hover {
            background: rgba(102, 126, 234, 0.2);
        }
        
        .status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status.online {
            background: #4CAF50;
            color: white;
        }
        
        .status.offline {
            background: #f44336;
            color: white;
        }
        
        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .action-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .action-btn.success {
            background: #4CAF50;
            color: white;
        }
        
        .action-btn.warning {
            background: #ff9800;
            color: white;
        }
        
        .action-btn.danger {
            background: #f44336;
            color: white;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 TECNO DRIVE Platform</h1>
        <p>لوحة التحكم الرئيسية - إدارة شاملة للنظام</p>
    </div>
    
    <div class="container">
        <div class="services-grid">
            <!-- Frontend Services -->
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🎯</span>
                    <span class="service-title">لوحة الإدارة الرئيسية</span>
                </div>
                <a href="http://localhost:3000" class="service-url" target="_blank">
                    http://localhost:3000
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">👥</span>
                    <span class="service-title">إدارة الموارد البشرية</span>
                </div>
                <a href="http://localhost:3002" class="service-url" target="_blank">
                    http://localhost:3002
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <!-- Backend Services -->
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🌐</span>
                    <span class="service-title">بوابة API الرئيسية</span>
                </div>
                <a href="http://localhost:8080" class="service-url" target="_blank">
                    http://localhost:8080
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🔐</span>
                    <span class="service-title">خدمة المصادقة</span>
                </div>
                <a href="http://localhost:8081" class="service-url" target="_blank">
                    http://localhost:8081
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">🔍</span>
                    <span class="service-title">Eureka Discovery</span>
                </div>
                <a href="http://localhost:8761" class="service-url" target="_blank">
                    http://localhost:8761
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <!-- Monitoring Services -->
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">📊</span>
                    <span class="service-title">Prometheus Monitoring</span>
                </div>
                <a href="http://localhost:9090" class="service-url" target="_blank">
                    http://localhost:9090
                </a>
                <span class="status online">متصل</span>
            </div>
            
            <div class="service-card">
                <div class="service-header">
                    <span class="service-icon">📈</span>
                    <span class="service-title">Grafana Dashboard</span>
                </div>
                <a href="http://localhost:3001" class="service-url" target="_blank">
                    http://localhost:3001
                </a>
                <span class="status online">متصل</span>
            </div>
        </div>
        
        <div class="quick-actions">
            <h2>🛠️ إجراءات سريعة</h2>
            <div class="action-buttons">
                <button class="action-btn primary" onclick="window.open('http://localhost:3000', '_blank')">
                    🎯 فتح لوحة الإدارة
                </button>
                <button class="action-btn success" onclick="window.open('http://localhost:8761', '_blank')">
                    🔍 مراقبة الخدمات
                </button>
                <button class="action-btn warning" onclick="window.open('http://localhost:9090', '_blank')">
                    📊 مراقبة الأداء
                </button>
                <button class="action-btn primary" onclick="window.open('http://localhost:3001', '_blank')">
                    📈 تحليل البيانات
                </button>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2024 TECNO DRIVE Platform - جميع الحقوق محفوظة</p>
        <p>النظام يعمل بكامل طاقته 🚀</p>
    </div>
    
    <script>
        // Auto-refresh status every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
        
        // Add click animations
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 150);
            });
        });
    </script>
</body>
</html>
