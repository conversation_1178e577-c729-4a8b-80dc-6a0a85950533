.simple-interactive-map-container {
  position: relative;
  width: 100%;
  height: 600px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Map Controls */
.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 250px;
  backdrop-filter: blur(10px);
}

.connection-status {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.status-indicator {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.layer-controls {
  margin-bottom: 15px;
}

.layer-controls label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
}

.layer-controls input[type="checkbox"] {
  margin-left: 8px;
  margin-right: 0;
}

.map-stats {
  font-size: 12px;
  color: #666;
}

.map-stats div {
  margin-bottom: 4px;
}

/* Simple Map Display */
.simple-map-display {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: crosshair;
  overflow: hidden;
}

.map-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #e8f5e8 25%, transparent 25%), 
              linear-gradient(-45deg, #e8f5e8 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #e8f5e8 75%), 
              linear-gradient(-45deg, transparent 75%, #e8f5e8 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  position: relative;
}

.map-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

/* Vehicle Markers */
.vehicle-marker {
  position: absolute;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 200;
  transform-origin: center;
}

.vehicle-marker:hover {
  transform: scale(1.2);
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
}

.vehicle-marker.active {
  filter: hue-rotate(120deg);
}

.vehicle-marker.busy {
  filter: hue-rotate(60deg);
}

.vehicle-marker.offline {
  filter: grayscale(100%);
  opacity: 0.5;
}

/* Route Overlay */
.route-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 150;
}

/* Traffic Indicators */
.traffic-indicator {
  position: absolute;
  font-size: 16px;
  cursor: pointer;
  z-index: 180;
  transition: all 0.3s ease;
}

.traffic-indicator:hover {
  transform: scale(1.3);
}

.traffic-indicator.heavy {
  filter: hue-rotate(0deg);
}

.traffic-indicator.moderate {
  filter: hue-rotate(30deg);
}

.traffic-indicator.light {
  filter: hue-rotate(60deg);
}

.traffic-indicator.free_flow {
  filter: hue-rotate(120deg);
}

/* Vehicle Details Panel */
.vehicle-details-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  overflow: hidden;
}

.panel-header {
  background: #3498db;
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-header button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.panel-header button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.panel-content {
  padding: 16px;
}

.panel-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #555;
}

.panel-content strong {
  color: #2c3e50;
}

/* Map Legend */
.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.map-legend h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #2c3e50;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.legend-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* Quick Actions */
.quick-actions {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-actions button {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.quick-actions button:hover {
  background: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-controls {
    position: relative;
    top: 0;
    right: 0;
    margin-bottom: 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .vehicle-details-panel {
    position: relative;
    bottom: 0;
    left: 0;
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .map-legend {
    position: relative;
    bottom: 0;
    right: 0;
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .quick-actions {
    position: relative;
    top: 0;
    left: 0;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 10px;
  }

  .simple-interactive-map-container {
    height: 400px;
  }
}

/* Animation for real-time updates */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.vehicle-marker.updating {
  animation: pulse 1s ease-in-out;
}

/* Loading states */
.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
