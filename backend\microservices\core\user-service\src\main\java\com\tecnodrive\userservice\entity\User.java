package com.tecnodrive.userservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.UUID;

/**
 * User Entity for User Management
 */
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_user_email", columnList = "email"),
    @Index(name = "idx_user_phone", columnList = "phoneNumber"),
    @Index(name = "idx_user_status", columnList = "status"),
    @Index(name = "idx_user_type", columnList = "userType"),
    @Index(name = "idx_user_company", columnList = "companyId")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false, unique = true, length = 100)
    private String email;

    @Column(nullable = false, length = 100)
    private String firstName;

    @Column(nullable = false, length = 100)
    private String lastName;

    @Column(length = 20)
    private String phoneNumber;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserType userType = UserType.PASSENGER;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserStatus status = UserStatus.ACTIVE;

    @Column
    private UUID companyId;

    @Column
    private String profileImageUrl;

    @Column(length = 500)
    private String address;

    @Column(length = 100)
    private String city;

    @Column(length = 100)
    private String country;

    @Column(length = 20)
    private String postalCode;

    @Column
    private Instant dateOfBirth;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Column(length = 10)
    private String preferredLanguage = "en";

    @Column(length = 50)
    private String timezone = "UTC";

    @Column
    private boolean emailNotifications = true;

    @Column
    private boolean smsNotifications = true;

    @Column
    private boolean pushNotifications = true;

    @Column
    private Instant lastLoginAt;

    @Column
    private boolean emailVerified = false;

    @Column
    private boolean phoneVerified = false;

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private Instant updatedAt;

    // Helper methods
    public String getFullName() {
        return firstName + " " + lastName;
    }

    public boolean isActive() {
        return status == UserStatus.ACTIVE;
    }

    public boolean isDriver() {
        return userType == UserType.DRIVER;
    }

    public boolean isPassenger() {
        return userType == UserType.PASSENGER;
    }

    public boolean isAdmin() {
        return userType == UserType.ADMIN;
    }

    // Enums
    public enum UserType {
        PASSENGER,
        DRIVER,
        ADMIN,
        FLEET_MANAGER,
        SUPPORT_AGENT
    }

    public enum UserStatus {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        PENDING_VERIFICATION,
        BANNED
    }

    public enum Gender {
        MALE,
        FEMALE,
        OTHER,
        PREFER_NOT_TO_SAY
    }
}
