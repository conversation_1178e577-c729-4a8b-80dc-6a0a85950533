package com.tecnodrive.userservice.mapper;

import com.tecnodrive.userservice.dto.UserRequest;
import com.tecnodrive.userservice.dto.UserResponse;
import com.tecnodrive.userservice.entity.User;
import org.mapstruct.*;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * User Mapper using MapStruct
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMapper {

    /**
     * Convert Create Request to Entity
     */
    User toEntity(UserRequest.Create request);

    /**
     * Convert Entity to Response
     */
    UserResponse toResponse(User user);

    /**
     * Convert Entity to Summary Response
     */
    UserResponse.Summary toSummaryResponse(User user);

    /**
     * Convert Entity to Public Profile Response
     */
    UserResponse.PublicProfile toPublicProfileResponse(User user);

    /**
     * Convert Entity to Activity Response
     */
    UserResponse.Activity toActivityResponse(User user);

    /**
     * Update Entity from Update Request
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromRequest(UserRequest.Update request, @MappingTarget User user);

    /**
     * Convert Entity List to Response List
     */
    List<UserResponse> toResponseList(List<User> users);

    /**
     * Convert Entity List to Summary Response List
     */
    List<UserResponse.Summary> toSummaryResponseList(List<User> users);

    /**
     * Calculate days since last login
     */
    default long calculateDaysSinceLastLogin(Instant lastLoginAt) {
        if (lastLoginAt == null) {
            return -1; // Never logged in
        }
        return ChronoUnit.DAYS.between(lastLoginAt, Instant.now());
    }
}
