package com.tecnodrive.locationservice.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * WebSocket Handler for Real-time Location Updates
 */
@Component
public class LocationWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(LocationWebSocketHandler.class);

    // Store active WebSocket sessions
    private final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();

    // Store session subscriptions (session -> subscribed entity IDs)
    private final Map<String, CopyOnWriteArraySet<String>> sessionSubscriptions = new ConcurrentHashMap<>();

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void afterConnectionEstablished(@NonNull WebSocketSession session) throws Exception {
        sessions.add(session);
        sessionSubscriptions.put(session.getId(), new CopyOnWriteArraySet<>());

        logger.info("WebSocket connection established: {}", session.getId());

        // Send welcome message
        sendMessage(session, Map.of(
            "type", "connection_established",
            "sessionId", session.getId(),
            "message", "Connected to TecnoDrive Location Service"
        ));
    }

    @Override
    public void handleMessage(@NonNull WebSocketSession session, @NonNull WebSocketMessage<?> message) throws Exception {
        try {
            String payload = message.getPayload().toString();
            @SuppressWarnings("unchecked")
            Map<String, Object> data = objectMapper.readValue(payload, Map.class);

            String messageType = (String) data.get("type");

            switch (messageType) {
                case "subscribe_vehicle":
                    handleVehicleSubscription(session, data);
                    break;
                case "subscribe_fleet":
                    handleFleetSubscription(session, data);
                    break;
                case "unsubscribe":
                    handleUnsubscription(session, data);
                    break;
                case "location_update":
                    handleLocationUpdate(session, data);
                    break;
                case "ping":
                    handlePing(session);
                    break;
                default:
                    logger.warn("Unknown message type: {}", messageType);
            }

        } catch (Exception e) {
            logger.error("Error handling WebSocket message", e);
            sendError(session, "Error processing message: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(@NonNull WebSocketSession session, @NonNull Throwable exception) throws Exception {
        logger.error("WebSocket transport error for session {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(@NonNull WebSocketSession session, @NonNull CloseStatus closeStatus) throws Exception {
        sessions.remove(session);
        sessionSubscriptions.remove(session.getId());

        logger.info("WebSocket connection closed: {} - {}", session.getId(), closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Handle vehicle subscription
     */
    private void handleVehicleSubscription(WebSocketSession session, Map<String, Object> data) {
        String vehicleId = (String) data.get("vehicleId");
        if (vehicleId != null) {
            sessionSubscriptions.get(session.getId()).add("vehicle:" + vehicleId);

            sendMessage(session, Map.of(
                "type", "subscription_confirmed",
                "entity", "vehicle",
                "entityId", vehicleId
            ));

            logger.info("Session {} subscribed to vehicle {}", session.getId(), vehicleId);
        }
    }

    /**
     * Handle fleet subscription (all vehicles)
     */
    private void handleFleetSubscription(WebSocketSession session, Map<String, Object> data) {
        sessionSubscriptions.get(session.getId()).add("fleet:all");

        sendMessage(session, Map.of(
            "type", "subscription_confirmed",
            "entity", "fleet",
            "entityId", "all"
        ));

        logger.info("Session {} subscribed to fleet updates", session.getId());
    }

    /**
     * Handle unsubscription
     */
    private void handleUnsubscription(WebSocketSession session, Map<String, Object> data) {
        String entity = (String) data.get("entity");
        String entityId = (String) data.get("entityId");

        if (entity != null && entityId != null) {
            String subscriptionKey = entity + ":" + entityId;
            sessionSubscriptions.get(session.getId()).remove(subscriptionKey);

            sendMessage(session, Map.of(
                "type", "unsubscription_confirmed",
                "entity", entity,
                "entityId", entityId
            ));
        }
    }

    /**
     * Handle location update from vehicles
     */
    private void handleLocationUpdate(WebSocketSession session, Map<String, Object> data) {
        // Broadcast location update to subscribed sessions
        broadcastLocationUpdate(data);
    }

    /**
     * Handle ping message
     */
    private void handlePing(WebSocketSession session) {
        sendMessage(session, Map.of(
            "type", "pong",
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * Broadcast location update to subscribed sessions
     */
    public void broadcastLocationUpdate(Map<String, Object> locationData) {
        String vehicleId = (String) locationData.get("vehicleId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null &&
                (subscriptions.contains("vehicle:" + vehicleId) || subscriptions.contains("fleet:all"))) {

                sendMessage(session, Map.of(
                    "type", "location_update",
                    "data", locationData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast alert to all connected sessions
     */
    public void broadcastAlert(Map<String, Object> alertData) {
        sessions.parallelStream().forEach(session -> {
            sendMessage(session, Map.of(
                "type", "alert",
                "data", alertData,
                "timestamp", System.currentTimeMillis()
            ));
        });
    }

    /**
     * Send message to specific session
     */
    private void sendMessage(WebSocketSession session, Map<String, Object> message) {
        try {
            if (session.isOpen()) {
                String json = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(json));
            }
        } catch (IOException e) {
            logger.error("Error sending WebSocket message to session {}", session.getId(), e);
        }
    }

    /**
     * Send error message to session
     */
    private void sendError(WebSocketSession session, String errorMessage) {
        sendMessage(session, Map.of(
            "type", "error",
            "message", errorMessage,
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * Get active sessions count
     */
    public int getActiveSessionsCount() {
        return sessions.size();
    }

    /**
     * Get session subscriptions for monitoring
     */
    public Map<String, CopyOnWriteArraySet<String>> getSessionSubscriptions() {
        return new ConcurrentHashMap<>(sessionSubscriptions);
    }

    /**
     * Broadcast vehicle tracking with enhanced data
     */
    public void broadcastVehicleTracking(Map<String, Object> vehicleData) {
        String vehicleId = (String) vehicleData.get("vehicleId");

        // Enhanced vehicle data with map-specific information
        Map<String, Object> enhancedData = new HashMap<>(vehicleData);
        enhancedData.put("mapType", "street");
        enhancedData.put("trackingMode", "realtime");
        enhancedData.put("lastUpdate", System.currentTimeMillis());

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("vehicle:" + vehicleId) ||
                subscriptions.contains("vehicle_tracking") ||
                subscriptions.contains("map:all") ||
                subscriptions.contains("fleet:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "vehicle_tracking",
                    "data", enhancedData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast route updates with navigation data
     */
    public void broadcastRouteUpdate(Map<String, Object> routeData) {
        String routeId = (String) routeData.get("routeId");
        String vehicleId = (String) routeData.get("vehicleId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("route:" + routeId) ||
                subscriptions.contains("vehicle:" + vehicleId) ||
                subscriptions.contains("route_update") ||
                subscriptions.contains("map:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "route_update",
                    "data", routeData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast traffic updates
     */
    public void broadcastTrafficUpdate(Map<String, Object> trafficData) {
        String streetId = (String) trafficData.get("streetId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("traffic:" + streetId) ||
                subscriptions.contains("traffic_update") ||
                subscriptions.contains("map:traffic") ||
                subscriptions.contains("map:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "traffic_update",
                    "data", trafficData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast geofence alerts
     */
    public void broadcastGeofenceAlert(Map<String, Object> geofenceData) {
        String geofenceId = (String) geofenceData.get("geofenceId");
        String vehicleId = (String) geofenceData.get("vehicleId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("geofence:" + geofenceId) ||
                subscriptions.contains("vehicle:" + vehicleId) ||
                subscriptions.contains("geofence_alert") ||
                subscriptions.contains("alerts:all") ||
                subscriptions.contains("map:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "geofence_alert",
                    "data", geofenceData,
                    "timestamp", System.currentTimeMillis(),
                    "priority", geofenceData.getOrDefault("priority", "medium")
                ));
            }
        });
    }

    /**
     * Broadcast location clustering updates
     */
    public void broadcastLocationCluster(Map<String, Object> clusterData) {
        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("location_cluster") ||
                subscriptions.contains("map:clusters") ||
                subscriptions.contains("analytics:clusters") ||
                subscriptions.contains("map:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "location_cluster",
                    "data", clusterData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast map viewport updates for synchronized viewing
     */
    public void broadcastMapViewport(Map<String, Object> viewportData) {
        String sessionId = (String) viewportData.get("sessionId");

        sessions.parallelStream()
            .filter(session -> !session.getId().equals(sessionId)) // Don't send back to sender
            .forEach(session -> {
                CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

                if (subscriptions != null && (
                    subscriptions.contains("map:viewport") ||
                    subscriptions.contains("map:sync") ||
                    subscriptions.contains("collaborative:all")
                )) {
                    sendMessage(session, Map.of(
                        "type", "map_viewport_sync",
                        "data", viewportData,
                        "timestamp", System.currentTimeMillis()
                    ));
                }
            });
    }

    /**
     * Broadcast advanced map layer updates
     */
    public void broadcastMapLayerUpdate(String layerType, Map<String, Object> layerData) {
        Map<String, Object> layerUpdate = Map.of(
            "type", "map_layer_update",
            "layerType", layerType,
            "data", layerData,
            "timestamp", System.currentTimeMillis()
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("map:layers") ||
                subscriptions.contains("layer:" + layerType) ||
                subscriptions.contains("map:all")
            )) {
                sendMessage(session, layerUpdate);
            }
        });
    }

    /**
     * Broadcast real-time heatmap data
     */
    public void broadcastHeatmapUpdate(Map<String, Object> heatmapData) {
        Map<String, Object> heatmapUpdate = Map.of(
            "type", "heatmap_update",
            "data", heatmapData,
            "timestamp", System.currentTimeMillis(),
            "intensity", heatmapData.getOrDefault("intensity", "medium")
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("heatmap:all") ||
                subscriptions.contains("map:heatmap") ||
                subscriptions.contains("analytics:heatmap")
            )) {
                sendMessage(session, heatmapUpdate);
            }
        });
    }

    /**
     * Broadcast street-level navigation updates
     */
    public void broadcastNavigationUpdate(Map<String, Object> navigationData) {
        Map<String, Object> navUpdate = Map.of(
            "type", "navigation_update",
            "data", navigationData,
            "timestamp", System.currentTimeMillis(),
            "navigationMode", "street_level"
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("navigation:all") ||
                subscriptions.contains("route:" + navigationData.get("routeId")) ||
                subscriptions.contains("vehicle:" + navigationData.get("vehicleId"))
            )) {
                sendMessage(session, navUpdate);
            }
        });
    }

    /**
     * Broadcast advanced traffic analytics
     */
    public void broadcastTrafficAnalytics(Map<String, Object> trafficAnalytics) {
        Map<String, Object> analyticsUpdate = Map.of(
            "type", "traffic_analytics",
            "data", trafficAnalytics,
            "timestamp", System.currentTimeMillis(),
            "analyticsType", "real_time"
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("analytics:traffic") ||
                subscriptions.contains("traffic:analytics") ||
                subscriptions.contains("map:analytics")
            )) {
                sendMessage(session, analyticsUpdate);
            }
        });
    }

    /**
     * Broadcast zone-based alerts (advanced geofencing)
     */
    public void broadcastZoneAlert(Map<String, Object> zoneAlertData) {
        Map<String, Object> zoneAlert = Map.of(
            "type", "zone_alert",
            "data", zoneAlertData,
            "timestamp", System.currentTimeMillis(),
            "priority", zoneAlertData.getOrDefault("priority", "medium"),
            "alertCategory", "geofence"
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("zones:all") ||
                subscriptions.contains("zone:" + zoneAlertData.get("zoneId")) ||
                subscriptions.contains("alerts:geofence") ||
                subscriptions.contains("vehicle:" + zoneAlertData.get("vehicleId"))
            )) {
                sendMessage(session, zoneAlert);
            }
        });
    }

    /**
     * Broadcast real-time demand analytics
     */
    public void broadcastDemandAnalytics(Map<String, Object> demandData) {
        Map<String, Object> demandUpdate = Map.of(
            "type", "demand_analytics",
            "data", demandData,
            "timestamp", System.currentTimeMillis(),
            "demandLevel", demandData.getOrDefault("level", "normal")
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("demand:all") ||
                subscriptions.contains("analytics:demand") ||
                subscriptions.contains("zone:" + demandData.get("zoneId"))
            )) {
                sendMessage(session, demandUpdate);
            }
        });
    }

    /**
     * Broadcast driver performance updates on map
     */
    public void broadcastDriverPerformance(Map<String, Object> performanceData) {
        Map<String, Object> perfUpdate = Map.of(
            "type", "driver_performance",
            "data", performanceData,
            "timestamp", System.currentTimeMillis(),
            "performanceCategory", "real_time"
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("performance:all") ||
                subscriptions.contains("driver:" + performanceData.get("driverId")) ||
                subscriptions.contains("vehicle:" + performanceData.get("vehicleId"))
            )) {
                sendMessage(session, perfUpdate);
            }
        });
    }

    /**
     * Broadcast advanced route optimization updates
     */
    public void broadcastRouteOptimization(Map<String, Object> optimizationData) {
        Map<String, Object> optUpdate = Map.of(
            "type", "route_optimization",
            "data", optimizationData,
            "timestamp", System.currentTimeMillis(),
            "optimizationType", "real_time"
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("optimization:all") ||
                subscriptions.contains("route:" + optimizationData.get("routeId")) ||
                subscriptions.contains("vehicle:" + optimizationData.get("vehicleId"))
            )) {
                sendMessage(session, optUpdate);
            }
        });
    }

    /**
     * Broadcast ride status update to subscribed sessions
     */
    public void broadcastRideUpdate(Map<String, Object> rideData) {
        String rideId = (String) rideData.get("rideId");
        String driverId = (String) rideData.get("driverId");
        String passengerId = (String) rideData.get("passengerId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("ride:" + rideId) ||
                subscriptions.contains("driver:" + driverId) ||
                subscriptions.contains("passenger:" + passengerId) ||
                subscriptions.contains("rides:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "ride_update",
                    "data", rideData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast parcel status update to subscribed sessions
     */
    public void broadcastParcelUpdate(Map<String, Object> parcelData) {
        String parcelId = (String) parcelData.get("parcelId");
        String trackingNumber = (String) parcelData.get("trackingNumber");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("parcel:" + parcelId) ||
                subscriptions.contains("tracking:" + trackingNumber) ||
                subscriptions.contains("parcels:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "parcel_update",
                    "data", parcelData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast fleet status update to subscribed sessions
     */
    public void broadcastFleetUpdate(Map<String, Object> fleetData) {
        String vehicleId = (String) fleetData.get("vehicleId");
        String fleetId = (String) fleetData.get("fleetId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("vehicle:" + vehicleId) ||
                subscriptions.contains("fleet:" + fleetId) ||
                subscriptions.contains("fleet:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "fleet_update",
                    "data", fleetData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast payment status update to subscribed sessions
     */
    public void broadcastPaymentUpdate(Map<String, Object> paymentData) {
        String paymentId = (String) paymentData.get("paymentId");
        String userId = (String) paymentData.get("userId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("payment:" + paymentId) ||
                subscriptions.contains("user:" + userId) ||
                subscriptions.contains("payments:all")
            )) {
                sendMessage(session, Map.of(
                    "type", "payment_update",
                    "data", paymentData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast system notification to all or specific sessions
     */
    public void broadcastNotification(Map<String, Object> notificationData) {
        String targetType = (String) notificationData.get("targetType");
        String targetId = (String) notificationData.get("targetId");

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            boolean shouldSend = false;

            if (subscriptions != null) {
                if ("all".equals(targetType)) {
                    shouldSend = true;
                } else if ("user".equals(targetType) && subscriptions.contains("user:" + targetId)) {
                    shouldSend = true;
                } else if ("driver".equals(targetType) && subscriptions.contains("driver:" + targetId)) {
                    shouldSend = true;
                } else if ("admin".equals(targetType) && subscriptions.contains("admin:all")) {
                    shouldSend = true;
                }
            }

            if (shouldSend) {
                sendMessage(session, Map.of(
                    "type", "notification",
                    "data", notificationData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast analytics update to subscribed sessions
     */
    public void broadcastAnalyticsUpdate(Map<String, Object> analyticsData) {
        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && subscriptions.contains("analytics:all")) {
                sendMessage(session, Map.of(
                    "type", "analytics_update",
                    "data", analyticsData,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    /**
     * Broadcast emergency alert to all sessions
     */
    public void broadcastEmergencyAlert(Map<String, Object> emergencyData) {
        sessions.parallelStream().forEach(session -> {
            sendMessage(session, Map.of(
                "type", "emergency_alert",
                "data", emergencyData,
                "timestamp", System.currentTimeMillis(),
                "priority", "high"
            ));
        });
    }

    /**
     * Send heartbeat to all connected sessions
     */
    public void sendHeartbeat() {
        Map<String, Object> heartbeat = Map.of(
            "type", "heartbeat",
            "timestamp", System.currentTimeMillis(),
            "activeConnections", sessions.size()
        );

        sessions.parallelStream().forEach(session -> {
            sendMessage(session, heartbeat);
        });
    }

    /**
     * Get connection statistics
     */
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalConnections", sessions.size());
        stats.put("totalSubscriptions", sessionSubscriptions.values().stream()
            .mapToInt(Set::size)
            .sum());
        stats.put("uptime", System.currentTimeMillis() - startTime);

        // Count subscriptions by type
        Map<String, Integer> subscriptionTypes = new HashMap<>();
        sessionSubscriptions.values().forEach(subscriptions -> {
            subscriptions.forEach(subscription -> {
                String type = subscription.split(":")[0];
                subscriptionTypes.merge(type, 1, Integer::sum);
            });
        });
        stats.put("subscriptionsByType", subscriptionTypes);

        return stats;
    }

    /**
     * Cleanup inactive sessions
     */
    public void cleanupInactiveSessions() {
        sessions.removeIf(session -> !session.isOpen());
        sessionSubscriptions.entrySet().removeIf(entry ->
            sessions.stream().noneMatch(session -> session.getId().equals(entry.getKey()))
        );
    }

    /**
     * Send bulk location updates for multiple vehicles
     */
    public void broadcastBulkLocationUpdate(List<Map<String, Object>> locationUpdates) {
        Map<String, Object> bulkUpdate = Map.of(
            "type", "bulk_location_update",
            "data", locationUpdates,
            "timestamp", System.currentTimeMillis(),
            "count", locationUpdates.size()
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("locations:all") ||
                locationUpdates.stream().anyMatch(update ->
                    subscriptions.contains("vehicle:" + update.get("vehicleId"))
                )
            )) {
                sendMessage(session, bulkUpdate);
            }
        });
    }

    /**
     * Send system status update
     */
    public void broadcastSystemStatus(Map<String, Object> systemStatus) {
        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && subscriptions.contains("system:status")) {
                sendMessage(session, Map.of(
                    "type", "system_status",
                    "data", systemStatus,
                    "timestamp", System.currentTimeMillis()
                ));
            }
        });
    }

    private long startTime = System.currentTimeMillis();

    /**
     * Get start time for uptime calculation
     */
    public long getStartTime() {
        return startTime;
    }

    /**
     * Get active sessions
     */
    public CopyOnWriteArraySet<WebSocketSession> getActiveSessions() {
        return sessions;
    }

    /**
     * Advanced session analytics and monitoring
     */
    public Map<String, Object> getAdvancedSessionAnalytics() {
        Map<String, Object> analytics = new HashMap<>();

        // Basic session stats
        analytics.put("totalSessions", sessions.size());
        analytics.put("activeConnections", sessions.stream().mapToLong(s -> s.isOpen() ? 1 : 0).sum());
        analytics.put("uptime", System.currentTimeMillis() - startTime);

        // Subscription analytics
        Map<String, Long> subscriptionCounts = new HashMap<>();
        sessionSubscriptions.values().forEach(subscriptions -> {
            subscriptions.forEach(subscription -> {
                subscriptionCounts.merge(subscription, 1L, Long::sum);
            });
        });
        analytics.put("subscriptionCounts", subscriptionCounts);

        // Most popular subscriptions
        List<Map.Entry<String, Long>> topSubscriptions = subscriptionCounts.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .limit(10)
            .collect(Collectors.toList());
        analytics.put("topSubscriptions", topSubscriptions);

        // Session distribution by subscription type
        Map<String, Long> sessionsByType = new HashMap<>();
        sessionSubscriptions.values().forEach(subscriptions -> {
            if (subscriptions.contains("map:all")) sessionsByType.merge("map_users", 1L, Long::sum);
            if (subscriptions.contains("vehicle_tracking")) sessionsByType.merge("tracking_users", 1L, Long::sum);
            if (subscriptions.contains("analytics:all")) sessionsByType.merge("analytics_users", 1L, Long::sum);
            if (subscriptions.contains("alerts:all")) sessionsByType.merge("alert_users", 1L, Long::sum);
        });
        analytics.put("sessionsByType", sessionsByType);

        return analytics;
    }

    /**
     * Real-time performance metrics
     */
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        // Message throughput (simplified - in production, use proper metrics)
        metrics.put("messagesPerSecond", calculateMessageThroughput());
        metrics.put("averageLatency", calculateAverageLatency());
        metrics.put("errorRate", calculateErrorRate());

        // Resource usage
        Runtime runtime = Runtime.getRuntime();
        metrics.put("memoryUsage", Map.of(
            "used", runtime.totalMemory() - runtime.freeMemory(),
            "free", runtime.freeMemory(),
            "total", runtime.totalMemory(),
            "max", runtime.maxMemory()
        ));

        // Connection health
        long healthySessions = sessions.stream().mapToLong(s -> s.isOpen() ? 1 : 0).sum();
        metrics.put("connectionHealth", Map.of(
            "healthy", healthySessions,
            "total", sessions.size(),
            "healthPercentage", sessions.isEmpty() ? 100 : (healthySessions * 100.0 / sessions.size())
        ));

        return metrics;
    }

    /**
     * Advanced message filtering and routing
     */
    public void broadcastFilteredMessage(String messageType, Map<String, Object> data,
                                       Map<String, Object> filters) {
        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && shouldReceiveMessage(subscriptions, messageType, filters)) {
                Map<String, Object> message = Map.of(
                    "type", messageType,
                    "data", data,
                    "timestamp", System.currentTimeMillis(),
                    "filters", filters
                );
                sendMessage(session, message);
            }
        });
    }

    /**
     * Check if session should receive message based on filters
     */
    private boolean shouldReceiveMessage(CopyOnWriteArraySet<String> subscriptions,
                                       String messageType, Map<String, Object> filters) {
        // Basic subscription check
        if (!subscriptions.contains(messageType) && !subscriptions.contains("all")) {
            return false;
        }

        // Apply filters
        if (filters.containsKey("vehicleId")) {
            String vehicleId = (String) filters.get("vehicleId");
            if (!subscriptions.contains("vehicle:" + vehicleId)) {
                return false;
            }
        }

        if (filters.containsKey("zone")) {
            String zone = (String) filters.get("zone");
            if (!subscriptions.contains("zone:" + zone)) {
                return false;
            }
        }

        if (filters.containsKey("priority")) {
            String priority = (String) filters.get("priority");
            if ("high".equals(priority) && !subscriptions.contains("alerts:high")) {
                return false;
            }
        }

        return true;
    }

    /**
     * Batch message sending for performance
     */
    public void broadcastBatchMessages(List<Map<String, Object>> messages) {
        Map<String, Object> batchMessage = Map.of(
            "type", "batch_update",
            "messages", messages,
            "count", messages.size(),
            "timestamp", System.currentTimeMillis()
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && subscriptions.contains("batch:all")) {
                sendMessage(session, batchMessage);
            }
        });
    }

    /**
     * Emergency broadcast to all sessions
     */
    public void broadcastEmergency(Map<String, Object> emergencyData) {
        Map<String, Object> emergencyMessage = Map.of(
            "type", "emergency",
            "data", emergencyData,
            "timestamp", System.currentTimeMillis(),
            "priority", "critical"
        );

        // Send to all sessions regardless of subscriptions
        sessions.parallelStream().forEach(session -> {
            sendMessage(session, emergencyMessage);
        });
    }

    // Helper methods for metrics (simplified implementations)
    private double calculateMessageThroughput() {
        // In production, implement proper message counting
        return 150.0; // messages per second
    }

    private double calculateAverageLatency() {
        // In production, implement proper latency tracking
        return 25.0; // milliseconds
    }

    private double calculateErrorRate() {
        // In production, implement proper error tracking
        return 0.5; // percentage
    }

    /**
     * Broadcast real-time map updates with street-level details
     */
    public void broadcastMapUpdate(Map<String, Object> mapData) {
        Map<String, Object> mapUpdate = Map.of(
            "type", "map_update",
            "data", mapData,
            "timestamp", System.currentTimeMillis()
        );

        sessions.parallelStream().forEach(session -> {
            CopyOnWriteArraySet<String> subscriptions = sessionSubscriptions.get(session.getId());

            if (subscriptions != null && (
                subscriptions.contains("map:all") ||
                subscriptions.contains("map:street") ||
                subscriptions.contains("map:realtime")
            )) {
                sendMessage(session, mapUpdate);
            }
        });
    }
}
