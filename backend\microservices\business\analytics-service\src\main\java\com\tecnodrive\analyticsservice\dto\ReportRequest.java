package com.tecnodrive.analyticsservice.dto;

import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.util.Map;

/**
 * Report Request DTO
 * 
 * Used for requesting report generation with various parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportRequest {

    /**
     * Report Type Enum
     */
    public enum ReportType {
        FINANCIAL_SUMMARY,
        REVENUE_ANALYSIS,
        EXPENSE_BREAKDOWN,
        RIDE_ANALYTICS,
        DRIVER_PERFORMANCE,
        CUSTOMER_ANALYTICS,
        VEHICLE_UTILIZATION,
        GEOGRAPHIC_ANALYSIS,
        OPERATIONAL_METRICS,
        USER_ENGAGEMENT,
        PAYMENT_ANALYTICS,
        NOTIFICATION_METRICS,
        CUSTOM_REPORT
    }

    /**
     * Export Format Enum
     */
    public enum Format {
        JSON,
        CSV,
        PDF,
        XLSX,
        HTML
    }

    /**
     * Report Period Enum
     */
    public enum Period {
        TODAY,
        YESTERDAY,
        LAST_7_DAYS,
        LAST_30_DAYS,
        THIS_MONTH,
        LAST_MONTH,
        THIS_QUARTER,
        LAST_QUARTER,
        THIS_YEAR,
        LAST_YEAR,
        CUSTOM
    }

    /**
     * Type of report to generate
     */
    @NotNull(message = "Report type is required")
    private ReportType reportType;

    /**
     * Report period (predefined or custom)
     */
    private Period period;

    /**
     * Start date for custom period
     */
    private LocalDate startDate;

    /**
     * End date for custom period
     */
    private LocalDate endDate;

    /**
     * Company/Tenant ID for filtering
     */
    @NotBlank(message = "Company ID is required")
    private String companyId;

    /**
     * Export format
     */
    @Builder.Default
    private Format format = Format.JSON;

    /**
     * Include detailed breakdown
     */
    @Builder.Default
    private boolean includeDetails = false;

    /**
     * Include charts and visualizations
     */
    @Builder.Default
    private boolean includeCharts = false;

    /**
     * Group by field (for aggregation)
     */
    private String groupBy;

    /**
     * Additional filters
     */
    private Map<String, Object> filters;

    /**
     * Page number for paginated results
     */
    @Min(value = 0, message = "Page number cannot be negative")
    private Integer page;

    /**
     * Page size for paginated results
     */
    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 1000, message = "Page size cannot exceed 1000")
    private Integer pageSize;

    /**
     * Sort field
     */
    private String sortBy;

    /**
     * Sort direction
     */
    private String sortDirection;

    /**
     * Report title (for exports)
     */
    private String title;

    /**
     * Report description
     */
    private String description;

    /**
     * User ID requesting the report
     */
    private String requestedBy;

    /**
     * Getter for reportType (for compatibility with usages)
     */
    public ReportType getReportType() {
        return this.reportType;
    }
}

