package com.tecnodrive.paymentservice.controller;

import com.tecnodrive.common.dto.common.ApiResponse;
import com.tecnodrive.paymentservice.dto.PaymentRequest;
import com.tecnodrive.paymentservice.dto.PaymentResponse;
import com.tecnodrive.paymentservice.dto.PaymentUpdateRequest;
import com.tecnodrive.paymentservice.entity.Payment;
import com.tecnodrive.paymentservice.service.PaymentService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

/**
 * Payment Controller
 * 
 * REST API endpoints for payment management
 */
@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
@Slf4j
public class PaymentController {

    private final PaymentService paymentService;

    /**
     * Create a new payment
     */
    @PostMapping
    public ResponseEntity<ApiResponse<PaymentResponse>> createPayment(
            @Valid @RequestBody PaymentRequest request) {
        log.info("Creating payment for entity: {} of type: {}", request.getEntityId(), request.getEntityType());
        
        PaymentResponse response = paymentService.createPayment(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Payment created successfully", response));
    }

    /**
     * Get payment by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PaymentResponse>> getPayment(@PathVariable String id) {
        log.debug("Retrieving payment with ID: {}", id);
        
        PaymentResponse response = paymentService.getPayment(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * Update payment
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PaymentResponse>> updatePayment(
            @PathVariable String id,
            @Valid @RequestBody PaymentUpdateRequest request) {
        log.info("Updating payment with ID: {}", id);
        
        PaymentResponse response = paymentService.updatePayment(id, request);
        return ResponseEntity.ok(ApiResponse.success("Payment updated successfully", response));
    }

    /**
     * Get payments by entity
     */
    @GetMapping("/entity/{entityId}")
    public ResponseEntity<ApiResponse<List<PaymentResponse>>> getPaymentsByEntity(
            @PathVariable String entityId,
            @RequestParam String entityType) {
        log.debug("Retrieving payments for entity: {} of type: {}", entityId, entityType);
        
        List<PaymentResponse> responses = paymentService.getPaymentsByEntity(entityId, entityType);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get payments by user
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<ApiResponse<Page<PaymentResponse>>> getPaymentsByUser(
            @PathVariable String userId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("Retrieving payments for user: {}", userId);
        
        Page<PaymentResponse> responses = paymentService.getPaymentsByUser(userId, pageable);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Get payments by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<List<PaymentResponse>>> getPaymentsByStatus(
            @PathVariable Payment.PaymentStatus status) {
        log.debug("Retrieving payments with status: {}", status);
        
        List<PaymentResponse> responses = paymentService.getPaymentsByStatus(status);
        return ResponseEntity.ok(ApiResponse.success(responses));
    }

    /**
     * Process payment
     */
    @PostMapping("/{id}/process")
    public ResponseEntity<ApiResponse<PaymentResponse>> processPayment(@PathVariable String id) {
        log.info("Processing payment with ID: {}", id);
        
        PaymentResponse response = paymentService.processPayment(id);
        return ResponseEntity.ok(ApiResponse.success("Payment processing started", response));
    }

    /**
     * Complete payment
     */
    @PostMapping("/{id}/complete")
    public ResponseEntity<ApiResponse<PaymentResponse>> completePayment(
            @PathVariable String id,
            @RequestParam String gatewayTransactionId) {
        log.info("Completing payment with ID: {}", id);
        
        PaymentResponse response = paymentService.completePayment(id, gatewayTransactionId);
        return ResponseEntity.ok(ApiResponse.success("Payment completed successfully", response));
    }

    /**
     * Fail payment
     */
    @PostMapping("/{id}/fail")
    public ResponseEntity<ApiResponse<PaymentResponse>> failPayment(
            @PathVariable String id,
            @RequestParam String reason) {
        log.info("Failing payment with ID: {} - Reason: {}", id, reason);
        
        PaymentResponse response = paymentService.failPayment(id, reason);
        return ResponseEntity.ok(ApiResponse.success("Payment marked as failed", response));
    }

    /**
     * Cancel payment
     */
    @PostMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<PaymentResponse>> cancelPayment(@PathVariable String id) {
        log.info("Cancelling payment with ID: {}", id);
        
        PaymentResponse response = paymentService.cancelPayment(id);
        return ResponseEntity.ok(ApiResponse.success("Payment cancelled successfully", response));
    }

    /**
     * Refund payment
     */
    @PostMapping("/{id}/refund")
    public ResponseEntity<ApiResponse<PaymentResponse>> refundPayment(@PathVariable String id) {
        log.info("Refunding payment with ID: {}", id);
        
        PaymentResponse response = paymentService.refundPayment(id);
        return ResponseEntity.ok(ApiResponse.success("Payment refunded successfully", response));
    }

    /**
     * Get payment statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<PaymentService.PaymentStatistics>> getPaymentStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant endDate) {
        log.debug("Generating payment statistics from {} to {}", startDate, endDate);
        
        PaymentService.PaymentStatistics statistics = paymentService.getPaymentStatistics(startDate, endDate);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }
}
