# Simple script to start all TecnoDrive services
Write-Host "🚀 Starting TecnoDrive Platform Services..." -ForegroundColor Green

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Check Docker status
if (-not (Test-DockerRunning)) {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Docker is running" -ForegroundColor Green

# Start infrastructure services first
Write-Host "📦 Starting infrastructure services..." -ForegroundColor Cyan

# Start PostgreSQL and Redis (if not already running)
Write-Host "  - Starting PostgreSQL..." -ForegroundColor Yellow
docker start postgres-tecno 2>$null
if ($LASTEXITCODE -ne 0) {
    docker run -d --name postgres-tecno `
        -e POSTGRES_DB=postgres `
        -e POSTGRES_USER=postgres `
        -e POSTGRES_PASSWORD=postgres `
        -p 5432:5432 `
        postgres:15-alpine
}

Write-Host "  - Starting Redis..." -ForegroundColor Yellow
docker start redis-tecno 2>$null
if ($LASTEXITCODE -ne 0) {
    docker run -d --name redis-tecno `
        -p 6379:6379 `
        redis:7-alpine
}

# Wait for databases to be ready
Write-Host "⏳ Waiting for databases to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Start Eureka Server
Write-Host "  - Starting Eureka Server..." -ForegroundColor Yellow
docker start infra-eureka-server-1 2>$null

# Wait for Eureka to be ready
Write-Host "⏳ Waiting for Eureka Server..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Start API Gateway
Write-Host "  - Starting API Gateway..." -ForegroundColor Yellow
docker start infra-api-gateway-1 2>$null

# Start microservices
Write-Host "🔧 Starting microservices..." -ForegroundColor Cyan

$services = @(
    "infra-auth-service-1",
    "infra-user-service-1", 
    "infra-ride-service-1",
    "infra-fleet-service-1",
    "infra-payment-service-1",
    "infra-notification-service-1",
    "infra-location-service-1",
    "infra-parcel-service-1",
    "infra-analytics-service-1",
    "infra-financial-service-1",
    "infra-hr-service-1",
    "infra-saas-management-service-1"
)

foreach ($service in $services) {
    Write-Host "  - Starting $service..." -ForegroundColor Yellow
    docker start $service 2>$null
    Start-Sleep -Seconds 2
}

# Check running services
Write-Host "📊 Checking service status..." -ForegroundColor Cyan
$runningServices = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "infra-|postgres-|redis-" }

if ($runningServices) {
    Write-Host "✅ Running Services:" -ForegroundColor Green
    $runningServices | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} else {
    Write-Host "⚠️  No services found running" -ForegroundColor Yellow
}

# Display access URLs
Write-Host "`n🌐 Service Access URLs:" -ForegroundColor Green
Write-Host "  • API Gateway:     http://localhost:8080" -ForegroundColor White
Write-Host "  • Eureka Dashboard: http://localhost:8761" -ForegroundColor White
Write-Host "  • PostgreSQL:      localhost:5432" -ForegroundColor White
Write-Host "  • Redis:           localhost:6379" -ForegroundColor White

Write-Host "`n🎉 TecnoDrive Platform startup completed!" -ForegroundColor Green
Write-Host "💡 Use 'docker ps' to check service status" -ForegroundColor Cyan
Write-Host "💡 Use 'docker logs <service-name>' to check service logs" -ForegroundColor Cyan
