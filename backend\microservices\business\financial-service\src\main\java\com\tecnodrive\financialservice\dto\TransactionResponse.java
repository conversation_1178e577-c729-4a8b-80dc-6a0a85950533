package com.tecnodrive.financialservice.dto;

import com.tecnodrive.financialservice.entity.FinancialTransactionLog;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * Transaction Response DTO
 * 
 * Used for returning transaction information to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionResponse {

    /**
     * Transaction ID
     */
    private String id;

    /**
     * Type of transaction
     */
    private FinancialTransactionLog.TransactionType transactionType;

    /**
     * Source entity type
     */
    private String sourceEntityType;

    /**
     * Source entity ID
     */
    private String sourceEntityId;

    /**
     * Transaction amount
     */
    private BigDecimal amount;

    /**
     * Currency code
     */
    private String currency;

    /**
     * Transaction status
     */
    private FinancialTransactionLog.TransactionStatus status;

    /**
     * Transaction description
     */
    private String description;

    /**
     * Reference number
     */
    private String referenceNumber;

    /**
     * Payment method used
     */
    private String paymentMethod;

    /**
     * Tax amount
     */
    private BigDecimal taxAmount;

    /**
     * Fee amount
     */
    private BigDecimal feeAmount;

    /**
     * Net amount (amount - tax - fees)
     */
    private BigDecimal netAmount;

    /**
     * Company/Tenant ID
     */
    private String companyId;

    /**
     * User ID who initiated the transaction
     */
    private String userId;

    /**
     * Transaction date
     */
    private Instant transactionDate;

    /**
     * Last update timestamp
     */
    private Instant updatedAt;

    /**
     * Additional metadata
     */
    private String metadata;
}
