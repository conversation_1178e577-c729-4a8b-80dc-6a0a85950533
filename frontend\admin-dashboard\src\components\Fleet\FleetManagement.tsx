import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
} from '@mui/material';
import {
  DirectionsCar as CarIcon,
  Map as MapIcon,
  Build as MaintenanceIcon,
  Analytics as AnalyticsIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import VehicleManagement from './VehicleManagement';
import LiveFleetMap from './LiveFleetMap';
import MaintenanceManagement from './MaintenanceManagement';
import FleetAnalytics from './FleetAnalytics';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`fleet-tabpanel-${index}`}
      aria-labelledby={`fleet-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const FleetManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Determine active tab based on current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/vehicles')) return 1;
    if (path.includes('/map')) return 2;
    if (path.includes('/maintenance')) return 3;
    if (path.includes('/analytics')) return 4;
    return 0; // Dashboard
  };

  const [tabValue, setTabValue] = React.useState(getActiveTab());

  React.useEffect(() => {
    setTabValue(getActiveTab());
  }, [location.pathname]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    const routes = ['', 'vehicles', 'map', 'maintenance', 'analytics'];
    navigate(`/fleet/${routes[newValue]}`);
  };
  // Mock data for dashboard
  const fleetOverviewData = [
    { month: 'يناير', distance: 12000, trips: 450 },
    { month: 'فبراير', distance: 13500, trips: 520 },
    { month: 'مارس', distance: 11800, trips: 480 },
    { month: 'أبريل', distance: 14200, trips: 580 },
    { month: 'مايو', distance: 13800, trips: 550 },
    { month: 'يونيو', distance: 14500, trips: 600 },
  ];

  const vehicleStatusDistribution = [
    { name: 'نشط', value: 3, color: '#4caf50' },
    { name: 'صيانة', value: 1, color: '#ff9800' },
    { name: 'غير متصل', value: 1, color: '#f44336' },
  ];

  const recentActivities = [
    { id: 1, type: 'vehicle', message: 'تم إضافة مركبة جديدة: YE-006', time: '10 دقائق' },
    { id: 2, type: 'maintenance', message: 'تم إكمال صيانة المركبة YE-002', time: '25 دقيقة' },
    { id: 3, type: 'location', message: 'المركبة YE-001 وصلت إلى الوجهة', time: '45 دقيقة' },
    { id: 4, type: 'alert', message: 'تنبيه: المركبة YE-004 تحتاج صيانة', time: '1 ساعة' },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'vehicle': return <CarIcon />;
      case 'maintenance': return <MaintenanceIcon />;
      case 'location': return <MapIcon />;
      case 'alert': return <TrendingUpIcon />;
      default: return <DashboardIcon />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'vehicle': return 'primary';
      case 'maintenance': return 'warning';
      case 'location': return 'success';
      case 'alert': return 'error';
      default: return 'default';
    }
  };

  return (
    <Routes>
      <Route path="/vehicles/*" element={<VehicleManagement />} />
      <Route path="/map" element={<LiveFleetMap />} />
      <Route path="/maintenance" element={<MaintenanceManagement />} />
      <Route path="/analytics" element={<FleetAnalytics />} />
      <Route path="/*" element={
        <Box>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              إدارة الأسطول
            </Typography>
            <Typography variant="body1" color="text.secondary">
              لوحة تحكم شاملة لإدارة المركبات والصيانة والتتبع
            </Typography>
          </Box>

          {/* Navigation Tabs */}
          <Card sx={{ mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab
                  icon={<DashboardIcon />}
                  label="لوحة التحكم"
                  iconPosition="start"
                />
                <Tab
                  icon={<CarIcon />}
                  label="المركبات"
                  iconPosition="start"
                />
                <Tab
                  icon={<MapIcon />}
                  label="الخريطة الحية"
                  iconPosition="start"
                />
                <Tab
                  icon={<MaintenanceIcon />}
                  label="الصيانة"
                  iconPosition="start"
                />
                <Tab
                  icon={<AnalyticsIcon />}
                  label="التحليلات"
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            {/* Dashboard Tab Content */}
            <TabPanel value={tabValue} index={0}>
              {/* Quick Stats */}
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' }, gap: 3, mb: 3 }}>
                <Box>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <CarIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            5
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            إجمالي المركبات
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
                <Box>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <span>🟢</span>
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            3
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            المركبات النشطة
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
                <Box>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'warning.main' }}>
                          <MaintenanceIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            1
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            في الصيانة
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
                <Box>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'info.main' }}>
                          <SpeedIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                            172,000
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            إجمالي المسافة (كم)
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Box>

              {/* Charts and Analytics */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, md: 8 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        أداء الأسطول الشهري
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={fleetOverviewData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Line
                            type="monotone"
                            dataKey="distance"
                            stroke="#1976d2"
                            strokeWidth={3}
                            name="المسافة (كم)"
                          />
                          <Line
                            type="monotone"
                            dataKey="trips"
                            stroke="#4caf50"
                            strokeWidth={3}
                            name="عدد الرحلات"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        توزيع حالة المركبات
                      </Typography>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={vehicleStatusDistribution}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                          >
                            {vehicleStatusDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Recent Activities and Quick Actions */}
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 8 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        الأنشطة الأخيرة
                      </Typography>
                      <List>
                        {recentActivities.map((activity) => (
                          <ListItem key={activity.id}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: `${getActivityColor(activity.type)}.main` }}>
                                {getActivityIcon(activity.type)}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={activity.message}
                              secondary={`منذ ${activity.time}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        إجراءات سريعة
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button
                          variant="contained"
                          startIcon={<CarIcon />}
                          onClick={() => navigate('/fleet/vehicles')}
                          fullWidth
                        >
                          إضافة مركبة جديدة
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<MapIcon />}
                          onClick={() => navigate('/fleet/map')}
                          fullWidth
                        >
                          عرض الخريطة الحية
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<MaintenanceIcon />}
                          onClick={() => navigate('/fleet/maintenance')}
                          fullWidth
                        >
                          جدولة صيانة
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<AnalyticsIcon />}
                          onClick={() => navigate('/fleet/analytics')}
                          fullWidth
                        >
                          عرض التحليلات
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Other tabs will show their respective components */}
            <TabPanel value={tabValue} index={1}>
              <VehicleManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
              <LiveFleetMap />
            </TabPanel>
            <TabPanel value={tabValue} index={3}>
              <MaintenanceManagement />
            </TabPanel>
            <TabPanel value={tabValue} index={4}>
              <FleetAnalytics />
            </TabPanel>
          </Card>
        </Box>
      } />
    </Routes>
  );
};

export default FleetManagement;
