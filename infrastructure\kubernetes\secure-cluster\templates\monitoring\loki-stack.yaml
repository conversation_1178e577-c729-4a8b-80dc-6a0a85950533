{{- if .Values.logging.loki.enabled }}
# Loki Configuration for Unified Logging
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-config
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: loki-config
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
data:
  loki.yaml: |
    auth_enabled: false
    
    server:
      http_listen_port: 3100
      grpc_listen_port: 9096
      log_level: {{ .Values.logging.loki.logLevel | default "info" }}
    
    common:
      path_prefix: /tmp/loki
      storage:
        filesystem:
          chunks_directory: /tmp/loki/chunks
          rules_directory: /tmp/loki/rules
      replication_factor: {{ .Values.logging.loki.replicationFactor | default 1 }}
      ring:
        instance_addr: 127.0.0.1
        kvstore:
          store: inmemory
    
    query_range:
      results_cache:
        cache:
          embedded_cache:
            enabled: true
            max_size_mb: 100
    
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
    
    ruler:
      alertmanager_url: http://prometheus-kube-prometheus-alertmanager:9093
    
    # TECNO DRIVE specific configuration
    limits_config:
      retention_period: {{ .Values.logging.loki.retentionPeriod | default "720h" }} # 30 days
      ingestion_rate_mb: 16
      ingestion_burst_size_mb: 32
      max_query_parallelism: 32
      max_streams_per_user: 10000
      max_line_size: 256000
      
    # Security and compliance settings
    analytics:
      reporting_enabled: false
    
    # Performance tuning for TECNO DRIVE
    chunk_store_config:
      max_look_back_period: 0s
    
    table_manager:
      retention_deletes_enabled: true
      retention_period: {{ .Values.logging.loki.retentionPeriod | default "720h" }}

---
# Loki Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loki
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tecno-drive.com/component: "logging"
spec:
  replicas: {{ .Values.logging.loki.replicas | default 1 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: loki
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: loki
        app.kubernetes.io/instance: {{ .Release.Name }}
        tecno-drive.com/component: "logging"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3100"
        prometheus.io/path: "/metrics"
        checksum/config: {{ include (print $.Template.BasePath "/monitoring/loki-stack.yaml") . | sha256sum }}
    spec:
      serviceAccountName: loki
      securityContext:
        runAsUser: 10001
        runAsGroup: 10001
        runAsNonRoot: true
        fsGroup: 10001
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: loki
          image: "grafana/loki:{{ .Values.logging.loki.image.tag | default "2.9.0" }}"
          imagePullPolicy: {{ .Values.logging.loki.image.pullPolicy | default "IfNotPresent" }}
          args:
            - -config.file=/etc/loki/loki.yaml
            - -target=all
          ports:
            - name: http-metrics
              containerPort: 3100
              protocol: TCP
            - name: grpc
              containerPort: 9096
              protocol: TCP
          env:
            - name: JAEGER_AGENT_HOST
              value: ""
            - name: JAEGER_ENDPOINT
              value: ""
            - name: JAEGER_SAMPLER_TYPE
              value: ""
            - name: JAEGER_SAMPLER_PARAM
              value: ""
          securityContext:
            runAsNonRoot: true
            runAsUser: 10001
            runAsGroup: 10001
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop: ["ALL"]
          livenessProbe:
            httpGet:
              path: /ready
              port: http-metrics
            initialDelaySeconds: 45
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: http-metrics
            initialDelaySeconds: 45
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 3
          resources:
            {{- toYaml .Values.logging.loki.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /etc/loki
            - name: storage
              mountPath: /tmp/loki
            - name: tmp
              mountPath: /tmp
      volumes:
        - name: config
          configMap:
            name: loki-config
        - name: storage
          {{- if .Values.logging.loki.persistence.enabled }}
          persistentVolumeClaim:
            claimName: loki-storage
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: tmp
          emptyDir: {}
      terminationGracePeriodSeconds: 4800

---
# Loki Service
apiVersion: v1
kind: Service
metadata:
  name: loki
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
spec:
  type: ClusterIP
  ports:
    - name: http-metrics
      port: 3100
      targetPort: http-metrics
      protocol: TCP
    - name: grpc
      port: 9096
      targetPort: grpc
      protocol: TCP
  selector:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: {{ .Release.Name }}

---
# Loki ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: loki
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
automountServiceAccountToken: true

---
# Loki PersistentVolumeClaim
{{- if .Values.logging.loki.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: loki-storage
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: loki-storage
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.logging.loki.persistence.size | default "10Gi" }}
  {{- if .Values.logging.loki.persistence.storageClass }}
  storageClassName: {{ .Values.logging.loki.persistence.storageClass }}
  {{- end }}
{{- end }}

---
# Promtail Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-config
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app.kubernetes.io/name: promtail-config
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
data:
  promtail.yaml: |
    server:
      http_listen_port: 3101
      grpc_listen_port: 0
    
    positions:
      filename: /tmp/positions.yaml
    
    clients:
      - url: http://loki:3100/loki/api/v1/push
        tenant_id: tecno-drive
    
    scrape_configs:
      # Kubernetes pods
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels:
              - __meta_kubernetes_pod_controller_name
            regex: ([0-9a-z-.]+?)(-[0-9a-f]{8,10})?
            action: replace
            target_label: __tmp_controller_name
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_name
              - __meta_kubernetes_pod_label_app
              - __tmp_controller_name
              - __meta_kubernetes_pod_name
            regex: ^;*([^;]+)(;.*)?$
            action: replace
            target_label: app
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_instance
              - __meta_kubernetes_pod_label_release
            regex: ^;*([^;]+)(;.*)?$
            action: replace
            target_label: instance
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_component
              - __meta_kubernetes_pod_label_component
            regex: ^;*([^;]+)(;.*)?$
            action: replace
            target_label: component
          - action: replace
            source_labels:
            - __meta_kubernetes_pod_node_name
            target_label: node_name
          - action: replace
            source_labels:
            - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
            - __meta_kubernetes_pod_uid
            - __meta_kubernetes_pod_container_name
            target_label: __path__
          - action: replace
            regex: true/(.*)
            replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
            - __meta_kubernetes_pod_annotationpresent_kubernetes_io_config_hash
            - __meta_kubernetes_pod_annotation_kubernetes_io_config_hash
            - __meta_kubernetes_pod_container_name
            target_label: __path__
        
        # TECNO DRIVE specific pipeline stages
        pipeline_stages:
          # Parse container logs
          - cri: {}
          
          # Extract TECNO DRIVE specific fields
          - regex:
              expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+(?P<level>\w+)\s+(?P<pid>\d+)\s+---\s+\[(?P<thread>.*?)\]\s+(?P<logger>\S+)\s*:\s+(?P<message>.*)'
          
          # Add TECNO DRIVE labels
          - labels:
              level:
              thread:
              logger:
              tecno_drive_service:
          
          # Filter security events
          - match:
              selector: '{app=~".*gatekeeper.*"}'
              stages:
                - regex:
                    expression: '(?P<violation_type>violation|security|unauthorized|forbidden|denied)'
                - labels:
                    violation_type:
                    security_event: "true"
          
          # Filter business events
          - match:
              selector: '{namespace="tecno-drive-system"}'
              stages:
                - regex:
                    expression: '(?P<business_event>ride|payment|user|fleet|notification)'
                - labels:
                    business_event:
                    platform: "tecno-drive"
          
          # Performance metrics extraction
          - match:
              selector: '{level="ERROR"}'
              stages:
                - regex:
                    expression: '(?P<error_type>timeout|exception|failed|error)'
                - labels:
                    error_type:
                    severity: "high"

---
# Promtail DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: promtail
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tecno-drive.com/component: "logging"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: promtail
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: promtail
        app.kubernetes.io/instance: {{ .Release.Name }}
        tecno-drive.com/component: "logging"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3101"
        prometheus.io/path: "/metrics"
        checksum/config: {{ include (print $.Template.BasePath "/monitoring/loki-stack.yaml") . | sha256sum }}
    spec:
      serviceAccountName: promtail
      securityContext:
        runAsUser: 0
        runAsGroup: 0
        fsGroup: 0
      containers:
        - name: promtail
          image: "grafana/promtail:{{ .Values.logging.promtail.image.tag | default "2.9.0" }}"
          imagePullPolicy: {{ .Values.logging.promtail.image.pullPolicy | default "IfNotPresent" }}
          args:
            - -config.file=/etc/promtail/promtail.yaml
            - -client.url=http://loki:3100/loki/api/v1/push
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          ports:
            - name: http-metrics
              containerPort: 3101
              protocol: TCP
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop: ["ALL"]
            readOnlyRootFilesystem: true
          livenessProbe:
            httpGet:
              path: /ready
              port: http-metrics
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /ready
              port: http-metrics
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 5
          resources:
            {{- toYaml .Values.logging.promtail.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /etc/promtail
            - name: run
              mountPath: /run/promtail
            - name: containers
              mountPath: /var/lib/docker/containers
              readOnly: true
            - name: pods
              mountPath: /var/log/pods
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: promtail-config
        - name: run
          hostPath:
            path: /run/promtail
        - name: containers
          hostPath:
            path: /var/lib/docker/containers
        - name: pods
          hostPath:
            path: /var/log/pods
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists

---
# Promtail ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: promtail
  namespace: {{ .Values.global.namespace | default "tecno-drive-system" }}
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"

---
# Promtail ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: promtail
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
rules:
  - apiGroups: [""]
    resources:
      - nodes
      - nodes/proxy
      - services
      - endpoints
      - pods
    verbs: ["get", "watch", "list"]

---
# Promtail ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: promtail
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: {{ .Release.Name }}
    tecno-drive.com/component: "logging"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: promtail
subjects:
  - kind: ServiceAccount
    name: promtail
    namespace: {{ .Release.Namespace }}
{{- end }}
