package com.tecnodrive.tests.contract;

import au.com.dius.pact.consumer.MockServer;
import au.com.dius.pact.consumer.dsl.PactDslWithProvider;
import au.com.dius.pact.consumer.junit5.PactConsumerTestExt;
import au.com.dius.pact.consumer.junit5.PactTestFor;
import au.com.dius.pact.core.model.RequestResponsePact;
import au.com.dius.pact.core.model.annotations.Pact;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Contract Tests for Auth Service
 * Tests the contract between API Gateway (consumer) and Auth Service (provider)
 */
@ExtendWith(PactConsumerTestExt.class)
@SpringBootTest
public class AuthServiceContractTest {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    // =====================================================
    // LOGIN CONTRACT TESTS
    // =====================================================

    @Pact(consumer = "api-gateway", provider = "auth-service")
    public RequestResponsePact loginSuccessContract(PactDslWithProvider builder) {
        return builder
                .given("user exists with valid credentials")
                .uponReceiving("a login request with valid credentials")
                .path("/api/v1/auth/login")
                .method("POST")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "Accept", "application/json"
                ))
                .body("""
                        {
                            "email": "<EMAIL>",
                            "password": "ValidPassword123!"
                        }
                        """)
                .willRespondWith()
                .status(200)
                .headers(Map.of("Content-Type", "application/json"))
                .body("""
                        {
                            "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
                            "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
                            "tokenType": "Bearer",
                            "expiresIn": 86400,
                            "user": {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "firstName": "Test",
                                "lastName": "User",
                                "role": "USER",
                                "active": true
                            }
                        }
                        """)
                .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "loginSuccessContract")
    public void testLoginSuccess(MockServer mockServer) throws Exception {
        // Arrange
        String loginUrl = mockServer.getUrl() + "/api/v1/auth/login";
        
        Map<String, String> loginRequest = new HashMap<>();
        loginRequest.put("email", "<EMAIL>");
        loginRequest.put("password", "ValidPassword123!");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(java.util.List.of(MediaType.APPLICATION_JSON));

        HttpEntity<Map<String, String>> request = new HttpEntity<>(loginRequest, headers);

        // Act
        ResponseEntity<String> response = restTemplate.exchange(
                loginUrl, HttpMethod.POST, request, String.class);

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("accessToken");
        assertThat(response.getBody()).contains("refreshToken");
        assertThat(response.getBody()).contains("<EMAIL>");
    }

    @Pact(consumer = "api-gateway", provider = "auth-service")
    public RequestResponsePact loginFailureContract(PactDslWithProvider builder) {
        return builder
                .given("user does not exist or invalid credentials")
                .uponReceiving("a login request with invalid credentials")
                .path("/api/v1/auth/login")
                .method("POST")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "Accept", "application/json"
                ))
                .body("""
                        {
                            "email": "<EMAIL>",
                            "password": "WrongPassword"
                        }
                        """)
                .willRespondWith()
                .status(401)
                .headers(Map.of("Content-Type", "application/json"))
                .body("""
                        {
                            "error": "UNAUTHORIZED",
                            "message": "Invalid email or password",
                            "timestamp": "2024-01-15T10:30:00Z",
                            "path": "/api/v1/auth/login"
                        }
                        """)
                .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "loginFailureContract")
    public void testLoginFailure(MockServer mockServer) throws Exception {
        // Arrange
        String loginUrl = mockServer.getUrl() + "/api/v1/auth/login";
        
        Map<String, String> loginRequest = new HashMap<>();
        loginRequest.put("email", "<EMAIL>");
        loginRequest.put("password", "WrongPassword");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(loginRequest, headers);

        // Act
        ResponseEntity<String> response = restTemplate.exchange(
                loginUrl, HttpMethod.POST, request, String.class);

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).contains("Invalid email or password");
    }

    // =====================================================
    // TOKEN VALIDATION CONTRACT TESTS
    // =====================================================

    @Pact(consumer = "api-gateway", provider = "auth-service")
    public RequestResponsePact validateTokenContract(PactDslWithProvider builder) {
        return builder
                .given("valid JWT token exists")
                .uponReceiving("a token validation request")
                .path("/api/v1/auth/validate")
                .method("POST")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "Authorization", "Bearer eyJhbGciOiJIUzUxMiJ9..."
                ))
                .body("""
                        {
                            "token": "eyJhbGciOiJIUzUxMiJ9..."
                        }
                        """)
                .willRespondWith()
                .status(200)
                .headers(Map.of("Content-Type", "application/json"))
                .body("""
                        {
                            "valid": true,
                            "userId": "123e4567-e89b-12d3-a456-426614174000",
                            "email": "<EMAIL>",
                            "role": "USER",
                            "expiresAt": "2024-01-16T10:30:00Z"
                        }
                        """)
                .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "validateTokenContract")
    public void testValidateToken(MockServer mockServer) throws Exception {
        // Arrange
        String validateUrl = mockServer.getUrl() + "/api/v1/auth/validate";
        
        Map<String, String> validateRequest = new HashMap<>();
        validateRequest.put("token", "eyJhbGciOiJIUzUxMiJ9...");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("eyJhbGciOiJIUzUxMiJ9...");

        HttpEntity<Map<String, String>> request = new HttpEntity<>(validateRequest, headers);

        // Act
        ResponseEntity<String> response = restTemplate.exchange(
                validateUrl, HttpMethod.POST, request, String.class);

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("\"valid\": true");
        assertThat(response.getBody()).contains("<EMAIL>");
    }

    // =====================================================
    // REGISTRATION CONTRACT TESTS
    // =====================================================

    @Pact(consumer = "api-gateway", provider = "auth-service")
    public RequestResponsePact registerUserContract(PactDslWithProvider builder) {
        return builder
                .given("email is not already registered")
                .uponReceiving("a user registration request")
                .path("/api/v1/auth/register")
                .method("POST")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "Accept", "application/json"
                ))
                .body("""
                        {
                            "email": "<EMAIL>",
                            "password": "SecurePassword123!",
                            "firstName": "New",
                            "lastName": "User",
                            "phoneNumber": "+967123456789",
                            "role": "USER"
                        }
                        """)
                .willRespondWith()
                .status(201)
                .headers(Map.of("Content-Type", "application/json"))
                .body("""
                        {
                            "id": "456e7890-e89b-12d3-a456-426614174001",
                            "email": "<EMAIL>",
                            "firstName": "New",
                            "lastName": "User",
                            "phoneNumber": "+967123456789",
                            "role": "USER",
                            "active": true,
                            "emailVerified": false,
                            "phoneVerified": false,
                            "createdAt": "2024-01-15T10:30:00Z"
                        }
                        """)
                .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "registerUserContract")
    public void testRegisterUser(MockServer mockServer) throws Exception {
        // Arrange
        String registerUrl = mockServer.getUrl() + "/api/v1/auth/register";
        
        Map<String, String> registerRequest = new HashMap<>();
        registerRequest.put("email", "<EMAIL>");
        registerRequest.put("password", "SecurePassword123!");
        registerRequest.put("firstName", "New");
        registerRequest.put("lastName", "User");
        registerRequest.put("phoneNumber", "+967123456789");
        registerRequest.put("role", "USER");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(java.util.List.of(MediaType.APPLICATION_JSON));

        HttpEntity<Map<String, String>> request = new HttpEntity<>(registerRequest, headers);

        // Act
        ResponseEntity<String> response = restTemplate.exchange(
                registerUrl, HttpMethod.POST, request, String.class);

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).contains("<EMAIL>");
        assertThat(response.getBody()).contains("\"active\": true");
    }

    // =====================================================
    // REFRESH TOKEN CONTRACT TESTS
    // =====================================================

    @Pact(consumer = "api-gateway", provider = "auth-service")
    public RequestResponsePact refreshTokenContract(PactDslWithProvider builder) {
        return builder
                .given("valid refresh token exists")
                .uponReceiving("a token refresh request")
                .path("/api/v1/auth/refresh")
                .method("POST")
                .headers(Map.of(
                        "Content-Type", "application/json",
                        "Accept", "application/json"
                ))
                .body("""
                        {
                            "refreshToken": "eyJhbGciOiJIUzUxMiJ9.refresh..."
                        }
                        """)
                .willRespondWith()
                .status(200)
                .headers(Map.of("Content-Type", "application/json"))
                .body("""
                        {
                            "accessToken": "eyJhbGciOiJIUzUxMiJ9.new...",
                            "refreshToken": "eyJhbGciOiJIUzUxMiJ9.new_refresh...",
                            "tokenType": "Bearer",
                            "expiresIn": 86400
                        }
                        """)
                .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "refreshTokenContract")
    public void testRefreshToken(MockServer mockServer) throws Exception {
        // Arrange
        String refreshUrl = mockServer.getUrl() + "/api/v1/auth/refresh";
        
        Map<String, String> refreshRequest = new HashMap<>();
        refreshRequest.put("refreshToken", "eyJhbGciOiJIUzUxMiJ9.refresh...");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(refreshRequest, headers);

        // Act
        ResponseEntity<String> response = restTemplate.exchange(
                refreshUrl, HttpMethod.POST, request, String.class);

        // Assert
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("accessToken");
        assertThat(response.getBody()).contains("refreshToken");
    }
}
