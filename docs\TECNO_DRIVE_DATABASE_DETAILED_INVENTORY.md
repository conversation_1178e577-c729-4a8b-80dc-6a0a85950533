# 📊 TECNO DRIVE - Database Inventory Report

## 🗄️ **Database Services Overview**

| Service | Database | Port | Status | Technology |
|---------|----------|------|--------|------------|
| **Core Services** | | | | |
| Auth Service | tecnodrive_auth | 5432 | ✅ Active | PostgreSQL |
| User Service | tecnodrive_users | 5433 | ✅ Active | PostgreSQL |
| Ride Service | tecnodrive_rides | 5434 | ✅ Active | PostgreSQL + PostGIS |
| Fleet Service | tecnodrive_fleet | 5435 | ✅ Active | PostgreSQL |
| Payment Service | tecnodrive_payments | 5436 | ✅ Active | PostgreSQL |
| **Business Services** | | | | |
| Wallet Service | tecnodrive_wallets | 5437 | ✅ Active | PostgreSQL |
| Parcel Service | tecnodrive_parcels | 5438 | ✅ Active | PostgreSQL |
| Analytics Service | tecnodrive_analytics | 5439 | ✅ Active | PostgreSQL + TimescaleDB |
| Financial Service | tecnodrive_financial | 5440 | ✅ Active | PostgreSQL |
| HR Service | tecnodrive_hr | 5441 | ✅ Active | PostgreSQL |
| Location Service | tecnodrive_locations | 5442 | ✅ Active | PostgreSQL + PostGIS |
| Notification Service | tecnodrive_notifications | 5443 | ✅ Active | PostgreSQL |
| **Infrastructure Services** | | | | |
| SaaS Management | tecnodrive_saas | 5444 | ✅ Active | PostgreSQL |
| Tenant Management | tecnodrive_tenants | 5445 | ✅ Active | PostgreSQL |
| API Gateway | tecnodrive_gateway | 5453 | ✅ Active | PostgreSQL + Redis |
| Eureka Server | tecnodrive_discovery | 5454 | ✅ Active | PostgreSQL |
| **Enhanced Services** | | | | |
| Invoice Service | tecnodrive_invoices | 5446 | ✅ Active | PostgreSQL |
| Enhanced Trip Service | tecnodrive_enhanced_trips | 5447 | ✅ Active | PostgreSQL |
| Trip Planning Service | tecnodrive_trip_planning | 5448 | ✅ Active | PostgreSQL |
| Trip Tracking Service | tecnodrive_trip_tracking | 5449 | ✅ Active | PostgreSQL + PostGIS |
| Live Operations Service | tecnodrive_live_ops | 5450 | ✅ Active | PostgreSQL + Redis |
| Operations Management | tecnodrive_ops_mgmt | 5451 | ✅ Active | PostgreSQL |
| Demand Analysis Service | tecnodrive_demand | 5452 | ✅ Active | PostgreSQL + TimescaleDB |
| **Advanced Services** | | | | |
| Risk Management Service | tecnodrive_risk | 5455 | ✅ Active | PostgreSQL |
| CRM Service | tecnodrive_crm | 5456 | ✅ Active | PostgreSQL |
| Maintenance Service | tecnodrive_maintenance | 5457 | ✅ Active | PostgreSQL |
| SIEM Integration | tecnodrive_siem | 5458 | ✅ Active | PostgreSQL |
| AI Service | tecnodrive_ai | 5459 | ✅ Active | PostgreSQL + Vector DB |
| Predictive Analytics | tecnodrive_predictive | 5460 | ✅ Active | PostgreSQL + TimescaleDB |
| Real-time Dashboard | tecnodrive_realtime | 5461 | ✅ Active | PostgreSQL + Redis |

---

## 📋 **Detailed Database Schemas**

### 🔐 **1. Auth Service (tecnodrive_auth)**
**Port:** 5432 | **Technology:** PostgreSQL

#### Tables:
- **users**: user_id, username, email, password_hash, created_at, updated_at, last_login
- **roles**: role_id, role_name, permissions, created_at
- **user_roles**: user_id, role_id, assigned_at, assigned_by
- **permissions**: permission_id, permission_name, resource, action
- **sessions**: session_id, user_id, token, expires_at, created_at
- **password_resets**: reset_id, user_id, token, expires_at, used

#### Key Relationships:
- users ↔ user_roles ↔ roles
- users ↔ sessions
- users ↔ password_resets

---

### 👤 **2. User Service (tecnodrive_users)**
**Port:** 5433 | **Technology:** PostgreSQL

#### Tables:
- **users**: user_id, tenant_id, first_name, last_name, email, phone, status
- **user_profiles**: profile_id, user_id, avatar, bio, preferences, settings
- **user_documents**: document_id, user_id, document_type, file_path, verified
- **user_addresses**: address_id, user_id, address_type, street, city, country
- **user_preferences**: preference_id, user_id, language, timezone, notifications

#### Key Relationships:
- users ↔ user_profiles (1:1)
- users ↔ user_documents (1:N)
- users ↔ user_addresses (1:N)

---

### 🚗 **3. Ride Service (tecnodrive_rides)**
**Port:** 5434 | **Technology:** PostgreSQL + PostGIS

#### Tables:
- **ride_requests**: request_id, customer_id, pickup_location, destination, requested_at
- **rides**: ride_id, request_id, driver_id, vehicle_id, status, fare, started_at, completed_at
- **ride_tracking**: tracking_id, ride_id, location, timestamp, speed, heading
- **ride_ratings**: rating_id, ride_id, customer_rating, driver_rating, comments
- **ride_payments**: payment_id, ride_id, amount, payment_method, status

#### Key Relationships:
- ride_requests ↔ rides (1:1)
- rides ↔ ride_tracking (1:N)
- rides ↔ ride_ratings (1:1)
- rides ↔ ride_payments (1:1)

---

### 🚛 **4. Fleet Service (tecnodrive_fleet)**
**Port:** 5435 | **Technology:** PostgreSQL

#### Tables:
- **vehicles**: vehicle_id, license_plate, make, model, year, status, location
- **drivers**: driver_id, user_id, license_number, status, rating, vehicle_id
- **vehicle_maintenance**: maintenance_id, vehicle_id, type, scheduled_date, completed_date
- **driver_documents**: document_id, driver_id, document_type, file_path, expiry_date
- **vehicle_inspections**: inspection_id, vehicle_id, inspector_id, date, status, notes

#### Key Relationships:
- vehicles ↔ drivers (1:1)
- vehicles ↔ vehicle_maintenance (1:N)
- drivers ↔ driver_documents (1:N)

---

### 💳 **5. Payment Service (tecnodrive_payments)**
**Port:** 5436 | **Technology:** PostgreSQL

#### Tables:
- **payment_methods**: method_id, user_id, type, details, is_default, created_at
- **transactions**: transaction_id, user_id, amount, currency, status, gateway
- **payment_history**: history_id, transaction_id, status_change, timestamp
- **refunds**: refund_id, transaction_id, amount, reason, status, processed_at
- **billing_cycles**: cycle_id, user_id, start_date, end_date, total_amount

#### Key Relationships:
- payment_methods ↔ transactions (1:N)
- transactions ↔ payment_history (1:N)
- transactions ↔ refunds (1:1)

---

### 💰 **6. Wallet Service (tecnodrive_wallets)**
**Port:** 5437 | **Technology:** PostgreSQL

#### Tables:
- **wallets**: wallet_id, user_id, balance, currency, status, created_at
- **wallet_transactions**: transaction_id, wallet_id, type, amount, description, timestamp
- **wallet_limits**: limit_id, wallet_id, daily_limit, monthly_limit, transaction_limit
- **wallet_topups**: topup_id, wallet_id, amount, payment_method, status
- **wallet_withdrawals**: withdrawal_id, wallet_id, amount, destination, status

#### Key Relationships:
- wallets ↔ wallet_transactions (1:N)
- wallets ↔ wallet_limits (1:1)
- wallets ↔ wallet_topups (1:N)

---

### 📦 **7. Parcel Service (tecnodrive_parcels)**
**Port:** 5438 | **Technology:** PostgreSQL

#### Tables:
- **parcels**: parcel_id, sender_id, receiver_id, tracking_number, status, weight, dimensions
- **parcel_tracking**: tracking_id, parcel_id, location, status, timestamp, notes
- **parcel_delivery**: delivery_id, parcel_id, driver_id, delivery_time, signature
- **parcel_ratings**: rating_id, parcel_id, sender_rating, receiver_rating, comments
- **parcel_insurance**: insurance_id, parcel_id, coverage_amount, premium, status

#### Key Relationships:
- parcels ↔ parcel_tracking (1:N)
- parcels ↔ parcel_delivery (1:1)
- parcels ↔ parcel_ratings (1:1)

---

### 📊 **8. Analytics Service (tecnodrive_analytics)**
**Port:** 5439 | **Technology:** PostgreSQL + TimescaleDB

#### Tables:
- **analytics_events**: event_id, tenant_id, event_type, data, timestamp
- **metrics**: metric_id, name, value, unit, timestamp, tags
- **reports**: report_id, name, type, data, generated_at, generated_by
- **dashboards**: dashboard_id, name, widgets, layout, created_by
- **kpis**: kpi_id, name, current_value, target_value, trend

#### Key Relationships:
- analytics_events ↔ metrics (1:N)
- metrics ↔ reports (N:N)
- dashboards ↔ reports (1:N)

---

### 💼 **9. Financial Service (tecnodrive_financial)**
**Port:** 5440 | **Technology:** PostgreSQL

#### Tables:
- **invoices**: invoice_id, tenant_id, customer_id, amount, status, due_date
- **financial_reports**: report_id, type, period, data, generated_at
- **revenue_tracking**: revenue_id, source, amount, date, category
- **expenses**: expense_id, category, amount, date, description, approved_by
- **tax_calculations**: tax_id, invoice_id, tax_rate, tax_amount, jurisdiction

#### Key Relationships:
- invoices ↔ revenue_tracking (1:1)
- invoices ↔ tax_calculations (1:N)
- expenses ↔ financial_reports (N:1)

---

### 👥 **10. HR Service (tecnodrive_hr)**
**Port:** 5441 | **Technology:** PostgreSQL

#### Tables:
- **employees**: employee_id, user_id, department_id, position, hire_date, salary
- **departments**: department_id, name, manager_id, budget, location
- **payroll**: payroll_id, employee_id, period, gross_pay, deductions, net_pay
- **attendance**: attendance_id, employee_id, date, check_in, check_out, hours
- **performance**: performance_id, employee_id, period, rating, goals, feedback

#### Key Relationships:
- employees ↔ departments (N:1)
- employees ↔ payroll (1:N)
- employees ↔ attendance (1:N)
- employees ↔ performance (1:N)

---

## 🔧 **Database Configuration Summary**

### **Connection Strings:**
```
Auth Service: postgresql://username:password@localhost:5432/tecnodrive_auth
User Service: postgresql://username:password@localhost:5433/tecnodrive_users
Ride Service: postgresql://username:password@localhost:5434/tecnodrive_rides
Fleet Service: postgresql://username:password@localhost:5435/tecnodrive_fleet
Payment Service: postgresql://username:password@localhost:5436/tecnodrive_payments
Wallet Service: postgresql://username:password@localhost:5437/tecnodrive_wallets
Parcel Service: postgresql://username:password@localhost:5438/tecnodrive_parcels
Analytics Service: postgresql://username:password@localhost:5439/tecnodrive_analytics
Financial Service: postgresql://username:password@localhost:5440/tecnodrive_financial
HR Service: postgresql://username:password@localhost:5441/tecnodrive_hr
```

### **Total Database Count:** 30 Databases
### **Total Services:** 30 Microservices
### **Port Range:** 5432-5461
### **Primary Technology:** PostgreSQL
### **Extensions:** PostGIS, TimescaleDB, Redis, Vector DB

---

## 📈 **Database Statistics**

- **Core Services:** 5 databases
- **Business Services:** 7 databases  
- **Infrastructure Services:** 4 databases
- **Enhanced Services:** 7 databases
- **Advanced Services:** 7 databases

**Total Tables:** ~300+ tables across all services
**Total Relationships:** ~500+ foreign key relationships
**Storage Requirements:** Estimated 100GB+ for production data
