package com.tecnodrive.notificationservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TECNO DRIVE Notification Service Application
 *
 * This service handles:
 * - Multi-channel notification delivery (Email, SMS, Push, In-App)
 * - Notification template management
 * - User notification preferences
 * - Notification history and logging
 * - Real-time notification delivery
 * - Notification analytics and reporting
 * - Template personalization and variables
 * - Delivery status tracking
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
@EnableTransactionManagement
@EnableAsync
public class NotificationServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(NotificationServiceApplication.class, args);
  }
}
