package com.tecnodrive.saasservice.mapper;

import com.tecnodrive.saasservice.dto.TenantRequest;
import com.tecnodrive.saasservice.dto.TenantResponse;
import com.tecnodrive.saasservice.dto.TenantUpdateRequest;
import com.tecnodrive.saasservice.entity.Tenant;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Tenant Mapper
 * 
 * Maps between Tenant entities and DTOs
 */
@Component
public class TenantMapper {

    /**
     * Convert TenantRequest to Tenant entity
     */
    public Tenant toEntity(TenantRequest request) {
        if (request == null) {
            return null;
        }

        return Tenant.builder()
                .name(request.getName())
                .displayName(request.getDisplayName())
                .type(request.getType())
                .contactPersonId(request.getContactPersonId())
                .email(request.getEmail())
                .phone(request.getPhone())
                .address(request.getAddress())
                .website(request.getWebsite())
                .serviceType(request.getServiceType())
                .pricingPlanId(request.getPricingPlanId())
                .maxUsers(request.getMaxUsers() != null ? request.getMaxUsers() : 100)
                .maxVehicles(request.getMaxVehicles() != null ? request.getMaxVehicles() : 10)
                .brandingConfig(request.getBrandingConfig())
                .featureFlags(request.getFeatureFlags())
                .metadata(request.getMetadata())
                .subscriptionStartDate(request.getSubscriptionStartDate())
                .subscriptionEndDate(request.getSubscriptionEndDate())
                .build();
    }

    /**
     * Convert Tenant entity to TenantResponse
     */
    public TenantResponse toResponse(Tenant tenant) {
        if (tenant == null) {
            return null;
        }

        return TenantResponse.builder()
                .id(tenant.getId().toString())
                .name(tenant.getName())
                .displayName(tenant.getDisplayName())
                .type(tenant.getType())
                .status(tenant.getStatus())
                .contactPersonId(tenant.getContactPersonId())
                .email(tenant.getEmail())
                .phone(tenant.getPhone())
                .address(tenant.getAddress())
                .website(tenant.getWebsite())
                .serviceType(tenant.getServiceType())
                .pricingPlanId(tenant.getPricingPlanId())
                .maxUsers(tenant.getMaxUsers())
                .maxVehicles(tenant.getMaxVehicles())
                .brandingConfig(tenant.getBrandingConfig())
                .featureFlags(tenant.getFeatureFlags())
                .metadata(tenant.getMetadata())
                .subscriptionStartDate(tenant.getSubscriptionStartDate())
                .subscriptionEndDate(tenant.getSubscriptionEndDate())
                .createdAt(tenant.getCreatedAt())
                .updatedAt(tenant.getUpdatedAt())
                .active(tenant.isActive())
                .subscriptionValid(tenant.isSubscriptionValid())
                .daysUntilExpiry(calculateDaysUntilExpiry(tenant.getSubscriptionEndDate()))
                .build();
    }

    /**
     * Update Tenant entity from TenantUpdateRequest
     */
    public void updateEntity(Tenant tenant, TenantUpdateRequest request) {
        if (tenant == null || request == null) {
            return;
        }

        if (request.getDisplayName() != null) {
            tenant.setDisplayName(request.getDisplayName());
        }
        if (request.getStatus() != null) {
            tenant.setStatus(request.getStatus());
        }
        if (request.getContactPersonId() != null) {
            tenant.setContactPersonId(request.getContactPersonId());
        }
        if (request.getEmail() != null) {
            tenant.setEmail(request.getEmail());
        }
        if (request.getPhone() != null) {
            tenant.setPhone(request.getPhone());
        }
        if (request.getAddress() != null) {
            tenant.setAddress(request.getAddress());
        }
        if (request.getWebsite() != null) {
            tenant.setWebsite(request.getWebsite());
        }
        if (request.getServiceType() != null) {
            tenant.setServiceType(request.getServiceType());
        }
        if (request.getPricingPlanId() != null) {
            tenant.setPricingPlanId(request.getPricingPlanId());
        }
        if (request.getMaxUsers() != null) {
            tenant.setMaxUsers(request.getMaxUsers());
        }
        if (request.getMaxVehicles() != null) {
            tenant.setMaxVehicles(request.getMaxVehicles());
        }
        if (request.getBrandingConfig() != null) {
            tenant.setBrandingConfig(request.getBrandingConfig());
        }
        if (request.getFeatureFlags() != null) {
            tenant.setFeatureFlags(request.getFeatureFlags());
        }
        if (request.getMetadata() != null) {
            tenant.setMetadata(request.getMetadata());
        }
        if (request.getSubscriptionStartDate() != null) {
            tenant.setSubscriptionStartDate(request.getSubscriptionStartDate());
        }
        if (request.getSubscriptionEndDate() != null) {
            tenant.setSubscriptionEndDate(request.getSubscriptionEndDate());
        }
    }

    /**
     * Convert list of Tenant entities to list of TenantResponse DTOs
     */
    public List<TenantResponse> toResponseList(List<Tenant> tenants) {
        if (tenants == null) {
            return null;
        }

        return tenants.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * Calculate days until subscription expiry
     */
    private long calculateDaysUntilExpiry(Instant subscriptionEndDate) {
        if (subscriptionEndDate == null) {
            return -1;
        }

        Instant now = Instant.now();
        if (subscriptionEndDate.isBefore(now)) {
            return 0; // Already expired
        }

        return Duration.between(now, subscriptionEndDate).toDays();
    }
}
