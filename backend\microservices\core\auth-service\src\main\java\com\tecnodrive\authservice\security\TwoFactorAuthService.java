package com.tecnodrive.authservice.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * Two-Factor Authentication Service
 * Implements TOTP (Time-based One-Time Password) and SMS-based 2FA
 */
@Slf4j
@Service
public class TwoFactorAuthService {

    private final RedisTemplate<String, String> redisTemplate;
    private final String issuer;
    private final int totpWindow;
    private final int totpPeriod;
    private final int totpDigits;
    private final long smsCodeExpiration;
    
    private static final String TOTP_SECRET_PREFIX = "2fa:totp:";
    private static final String SMS_CODE_PREFIX = "2fa:sms:";
    private static final String BACKUP_CODES_PREFIX = "2fa:backup:";
    private static final String FAILED_ATTEMPTS_PREFIX = "2fa:failed:";
    
    private static final int MAX_FAILED_ATTEMPTS = 3;
    private static final long LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

    public TwoFactorAuthService(
            RedisTemplate<String, String> redisTemplate,
            @Value("${security.two-factor.issuer:TecnoDrive Platform}") String issuer,
            @Value("${security.two-factor.totp.window:1}") int totpWindow,
            @Value("${security.two-factor.totp.period:30}") int totpPeriod,
            @Value("${security.two-factor.totp.digits:6}") int totpDigits,
            @Value("${security.two-factor.sms.expiration:300000}") long smsCodeExpiration) {
        
        this.redisTemplate = redisTemplate;
        this.issuer = issuer;
        this.totpWindow = totpWindow;
        this.totpPeriod = totpPeriod;
        this.totpDigits = totpDigits;
        this.smsCodeExpiration = smsCodeExpiration;
    }

    /**
     * Generate TOTP secret for user
     */
    public String generateTotpSecret(String userId) {
        byte[] secretBytes = new byte[20]; // 160-bit secret
        new SecureRandom().nextBytes(secretBytes);
        String secret = Base64.getEncoder().encodeToString(secretBytes);
        
        // Store secret in Redis
        redisTemplate.opsForValue().set(TOTP_SECRET_PREFIX + userId, secret);
        
        log.info("Generated TOTP secret for user: {}", userId);
        return secret;
    }

    /**
     * Generate QR code URL for TOTP setup
     */
    public String generateTotpQrCodeUrl(String userId, String userEmail, String secret) {
        String encodedIssuer = java.net.URLEncoder.encode(issuer, java.nio.charset.StandardCharsets.UTF_8);
        String encodedEmail = java.net.URLEncoder.encode(userEmail, java.nio.charset.StandardCharsets.UTF_8);
        String encodedSecret = java.net.URLEncoder.encode(secret, java.nio.charset.StandardCharsets.UTF_8);
        
        return String.format(
                "otpauth://totp/%s:%s?secret=%s&issuer=%s&digits=%d&period=%d",
                encodedIssuer, encodedEmail, encodedSecret, encodedIssuer, totpDigits, totpPeriod
        );
    }

    /**
     * Verify TOTP code
     */
    public boolean verifyTotpCode(String userId, String code) {
        if (isUserLockedOut(userId)) {
            log.warn("2FA verification blocked for locked out user: {}", userId);
            return false;
        }

        String secret = redisTemplate.opsForValue().get(TOTP_SECRET_PREFIX + userId);
        if (secret == null) {
            log.warn("No TOTP secret found for user: {}", userId);
            return false;
        }

        try {
            long currentTime = Instant.now().getEpochSecond() / totpPeriod;
            
            // Check current time window and adjacent windows
            for (int i = -totpWindow; i <= totpWindow; i++) {
                String expectedCode = generateTotpCode(secret, currentTime + i);
                if (code.equals(expectedCode)) {
                    resetFailedAttempts(userId);
                    log.info("TOTP verification successful for user: {}", userId);
                    return true;
                }
            }
            
            recordFailedAttempt(userId);
            log.warn("TOTP verification failed for user: {}", userId);
            return false;
            
        } catch (Exception e) {
            log.error("Error verifying TOTP code for user {}: {}", userId, e.getMessage());
            return false;
        }
    }

    /**
     * Generate SMS verification code
     */
    public String generateSmsCode(String userId, String phoneNumber) {
        if (isUserLockedOut(userId)) {
            log.warn("SMS code generation blocked for locked out user: {}", userId);
            return null;
        }

        // Generate 6-digit code
        String code = String.format("%06d", new SecureRandom().nextInt(1000000));
        
        // Store code in Redis with expiration
        redisTemplate.opsForValue().set(
                SMS_CODE_PREFIX + userId, 
                code, 
                smsCodeExpiration, 
                TimeUnit.MILLISECONDS
        );
        
        log.info("Generated SMS code for user: {} to phone: {}", userId, maskPhoneNumber(phoneNumber));
        return code;
    }

    /**
     * Verify SMS code
     */
    public boolean verifySmsCode(String userId, String code) {
        if (isUserLockedOut(userId)) {
            log.warn("SMS verification blocked for locked out user: {}", userId);
            return false;
        }

        String storedCode = redisTemplate.opsForValue().get(SMS_CODE_PREFIX + userId);
        if (storedCode == null) {
            log.warn("No SMS code found for user: {}", userId);
            return false;
        }

        if (code.equals(storedCode)) {
            // Remove used code
            redisTemplate.delete(SMS_CODE_PREFIX + userId);
            resetFailedAttempts(userId);
            log.info("SMS verification successful for user: {}", userId);
            return true;
        } else {
            recordFailedAttempt(userId);
            log.warn("SMS verification failed for user: {}", userId);
            return false;
        }
    }

    /**
     * Generate backup codes for user
     */
    public String[] generateBackupCodes(String userId) {
        String[] backupCodes = new String[10];
        SecureRandom random = new SecureRandom();
        
        for (int i = 0; i < backupCodes.length; i++) {
            backupCodes[i] = String.format("%08d", random.nextInt(100000000));
        }
        
        // Store hashed backup codes
        StringBuilder hashedCodes = new StringBuilder();
        for (String code : backupCodes) {
            if (hashedCodes.length() > 0) {
                hashedCodes.append(",");
            }
            hashedCodes.append(hashBackupCode(code));
        }
        
        redisTemplate.opsForValue().set(BACKUP_CODES_PREFIX + userId, hashedCodes.toString());
        
        log.info("Generated backup codes for user: {}", userId);
        return backupCodes;
    }

    /**
     * Verify backup code
     */
    public boolean verifyBackupCode(String userId, String code) {
        if (isUserLockedOut(userId)) {
            log.warn("Backup code verification blocked for locked out user: {}", userId);
            return false;
        }

        String storedCodes = redisTemplate.opsForValue().get(BACKUP_CODES_PREFIX + userId);
        if (storedCodes == null) {
            log.warn("No backup codes found for user: {}", userId);
            return false;
        }

        String hashedCode = hashBackupCode(code);
        String[] codes = storedCodes.split(",");
        
        for (int i = 0; i < codes.length; i++) {
            if (hashedCode.equals(codes[i])) {
                // Remove used code
                codes[i] = "USED";
                String updatedCodes = String.join(",", codes);
                redisTemplate.opsForValue().set(BACKUP_CODES_PREFIX + userId, updatedCodes);
                
                resetFailedAttempts(userId);
                log.info("Backup code verification successful for user: {}", userId);
                return true;
            }
        }
        
        recordFailedAttempt(userId);
        log.warn("Backup code verification failed for user: {}", userId);
        return false;
    }

    /**
     * Check if user has 2FA enabled
     */
    public boolean isTwoFactorEnabled(String userId) {
        return redisTemplate.hasKey(TOTP_SECRET_PREFIX + userId) ||
               redisTemplate.hasKey(BACKUP_CODES_PREFIX + userId);
    }

    /**
     * Disable 2FA for user
     */
    public void disableTwoFactor(String userId) {
        redisTemplate.delete(TOTP_SECRET_PREFIX + userId);
        redisTemplate.delete(BACKUP_CODES_PREFIX + userId);
        redisTemplate.delete(SMS_CODE_PREFIX + userId);
        resetFailedAttempts(userId);
        
        log.info("Disabled 2FA for user: {}", userId);
    }

    private String generateTotpCode(String secret, long timeCounter) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] secretBytes = Base64.getDecoder().decode(secret);
        byte[] timeBytes = ByteBuffer.allocate(8).putLong(timeCounter).array();
        
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(secretBytes, "HmacSHA1"));
        byte[] hash = mac.doFinal(timeBytes);
        
        int offset = hash[hash.length - 1] & 0x0F;
        int truncatedHash = ((hash[offset] & 0x7F) << 24) |
                           ((hash[offset + 1] & 0xFF) << 16) |
                           ((hash[offset + 2] & 0xFF) << 8) |
                           (hash[offset + 3] & 0xFF);
        
        int code = truncatedHash % (int) Math.pow(10, totpDigits);
        return String.format("%0" + totpDigits + "d", code);
    }

    private String hashBackupCode(String code) {
        // Simple hash for backup codes - in production, use BCrypt or similar
        return String.valueOf(code.hashCode());
    }

    private void recordFailedAttempt(String userId) {
        String key = FAILED_ATTEMPTS_PREFIX + userId;
        String attempts = redisTemplate.opsForValue().get(key);
        int count = attempts != null ? Integer.parseInt(attempts) : 0;
        count++;
        
        redisTemplate.opsForValue().set(key, String.valueOf(count), LOCKOUT_DURATION, TimeUnit.MILLISECONDS);
        
        if (count >= MAX_FAILED_ATTEMPTS) {
            log.warn("User {} locked out after {} failed 2FA attempts", userId, count);
        }
    }

    private void resetFailedAttempts(String userId) {
        redisTemplate.delete(FAILED_ATTEMPTS_PREFIX + userId);
    }

    private boolean isUserLockedOut(String userId) {
        String attempts = redisTemplate.opsForValue().get(FAILED_ATTEMPTS_PREFIX + userId);
        return attempts != null && Integer.parseInt(attempts) >= MAX_FAILED_ATTEMPTS;
    }

    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        return "*".repeat(phoneNumber.length() - 4) + phoneNumber.substring(phoneNumber.length() - 4);
    }
}
