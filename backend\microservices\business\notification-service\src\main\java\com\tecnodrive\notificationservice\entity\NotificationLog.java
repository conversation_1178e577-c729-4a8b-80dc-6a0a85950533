package com.tecnodrive.notificationservice.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

/**
 * Notification Log Entity
 * 
 * Records all notification delivery attempts and their status.
 * Provides audit trail and delivery analytics.
 */
@Entity
@Table(name = "notification_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class NotificationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * User ID who received the notification
     */
    @Column(nullable = false)
    private String userId;

    /**
     * Template used for this notification
     */
    @Column(nullable = false)
    private String templateName;

    /**
     * Notification channel used
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NotificationTemplate.NotificationChannel channel;

    /**
     * Notification priority
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private NotificationTemplate.NotificationPriority priority = NotificationTemplate.NotificationPriority.NORMAL;

    /**
     * Delivery status
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private DeliveryStatus status = DeliveryStatus.PENDING;

    /**
     * Recipient address (email, phone, device token, etc.)
     */
    @Column(nullable = false, length = 500)
    private String recipientAddress;

    /**
     * Final message subject (after variable substitution)
     */
    @Column(length = 500)
    private String messageSubject;

    /**
     * Final message content (after variable substitution)
     */
    @Column(columnDefinition = "TEXT")
    private String messageContent;

    /**
     * Template variables used (JSON format)
     */
    @Column(columnDefinition = "TEXT")
    private String templateVariables;

    /**
     * External provider message ID (for tracking)
     */
    private String externalMessageId;

    /**
     * Error message if delivery failed
     */
    @Column(columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * Number of delivery attempts
     */
    @Builder.Default
    private Integer attemptCount = 1;

    /**
     * Tenant ID for multi-tenant support
     */
    private String tenantId;

    /**
     * When the notification was sent
     */
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant sentAt;

    /**
     * When the notification was delivered (if applicable)
     */
    private Instant deliveredAt;

    /**
     * When the notification was read/opened (if applicable)
     */
    private Instant readAt;

    /**
     * Delivery Status Enum
     */
    public enum DeliveryStatus {
        PENDING,
        SENT,
        DELIVERED,
        READ,
        FAILED,
        BOUNCED,
        REJECTED
    }
}
