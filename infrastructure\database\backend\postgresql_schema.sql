-- =====================================================
-- TECNO DRIVE - PostgreSQL Backend Database Schema
-- قاعدة البيانات الخلفية الرئيسية
-- =====================================================

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- 1. ENUMS (أنواع البيانات المخصصة)
-- =====================================================

-- حالات الرحلات
CREATE TYPE trip_status_enum AS ENUM (
    'pending',
    'accepted', 
    'in_progress',
    'completed',
    'cancelled',
    'delayed'
);

-- حالات الطرود
CREATE TYPE parcel_status_enum AS ENUM (
    'Created',
    'PickedUp',
    'InTransit',
    'OutForDelivery',
    'Delivered',
    'Returned',
    'Cancelled',
    'Lost',
    'Damaged'
);

-- حالات التذاكر
CREATE TYPE ticket_status_enum AS ENUM (
    'open',
    'in_progress',
    'resolved',
    'closed',
    'escalated'
);

-- أولويات الطرود
CREATE TYPE parcel_priority_enum AS ENUM (
    'LOW',
    'MEDIUM', 
    'HIGH',
    'URGENT'
);

-- طرق الدفع
CREATE TYPE payment_method_enum AS ENUM (
    'cash',
    'card',
    'wallet',
    'bank_transfer',
    'mobile_payment'
);

-- حالات الدفع
CREATE TYPE payment_status_enum AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'refunded',
    'cancelled'
);

-- أنواع المركبات
CREATE TYPE vehicle_type_enum AS ENUM (
    'sedan',
    'suv',
    'van',
    'bus',
    'truck',
    'motorcycle'
);

-- حالات المركبات
CREATE TYPE vehicle_status_enum AS ENUM (
    'active',
    'inactive',
    'maintenance',
    'retired'
);

-- =====================================================
-- 2. CORE TABLES & RBAC
-- =====================================================

-- جدول المستخدمين الرئيسي
CREATE TABLE users (
    id              UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name       TEXT      NOT NULL,
    email           TEXT      UNIQUE NOT NULL,
    password_hash   TEXT      NOT NULL,
    phone           TEXT      UNIQUE,
    date_of_birth   DATE,
    gender          TEXT      CHECK (gender IN ('male', 'female')),
    profile_image   TEXT,
    language        TEXT      DEFAULT 'ar',
    timezone        TEXT      DEFAULT 'Asia/Aden',
    is_active       BOOLEAN   DEFAULT TRUE,
    is_verified     BOOLEAN   DEFAULT FALSE,
    email_verified_at TIMESTAMP,
    phone_verified_at TIMESTAMP,
    last_login_at   TIMESTAMP,
    login_attempts  INTEGER   DEFAULT 0,
    locked_until    TIMESTAMP,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID      REFERENCES users(id),
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by      UUID      REFERENCES users(id),
    deleted_at      TIMESTAMP,
    deleted_by      UUID      REFERENCES users(id)
);

-- جدول الأدوار
CREATE TABLE roles (
    role_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name       TEXT      UNIQUE NOT NULL,
    display_name    TEXT      NOT NULL,
    description     TEXT,
    is_system       BOOLEAN   DEFAULT FALSE,
    is_active       BOOLEAN   DEFAULT TRUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الصلاحيات
CREATE TABLE permissions (
    permission_id   UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    name            TEXT      UNIQUE NOT NULL,
    display_name    TEXT      NOT NULL,
    description     TEXT,
    resource        TEXT      NOT NULL, -- users, bookings, parcels, etc.
    action          TEXT      NOT NULL, -- create, read, update, delete, etc.
    is_system       BOOLEAN   DEFAULT FALSE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    user_id         UUID      REFERENCES users(id) ON DELETE CASCADE,
    role_id         UUID      REFERENCES roles(role_id) ON DELETE CASCADE,
    assigned_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by     UUID      REFERENCES users(id),
    expires_at      TIMESTAMP,
    is_active       BOOLEAN   DEFAULT TRUE,
    PRIMARY KEY(user_id, role_id)
);

-- ربط الأدوار بالصلاحيات
CREATE TABLE role_permissions (
    role_id         UUID      REFERENCES roles(role_id) ON DELETE CASCADE,
    permission_id   UUID      REFERENCES permissions(permission_id) ON DELETE CASCADE,
    granted_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by      UUID      REFERENCES users(id),
    PRIMARY KEY(role_id, permission_id)
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    session_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    access_token    TEXT      NOT NULL,
    refresh_token   TEXT      NOT NULL,
    device_info     JSONB,
    ip_address      INET,
    user_agent      TEXT,
    is_active       BOOLEAN   DEFAULT TRUE,
    expires_at      TIMESTAMP NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. BUSINESS TABLES
-- =====================================================

-- جدول الحجوزات الرئيسي
CREATE TABLE bookings (
    booking_id      TEXT      PRIMARY KEY,
    user_id         UUID      NOT NULL REFERENCES users(id),
    trip_id         UUID,
    from_location   TEXT      NOT NULL,
    to_location     TEXT      NOT NULL,
    from_latitude   DECIMAL(10,8),
    from_longitude  DECIMAL(11,8),
    to_latitude     DECIMAL(10,8),
    to_longitude    DECIMAL(11,8),
    departure_time  TIMESTAMP,
    arrival_time    TIMESTAMP,
    seat_count      INTEGER   NOT NULL DEFAULT 1,
    passenger_names JSONB,
    expected_price  DECIMAL(10,2),
    actual_price    DECIMAL(10,2),
    status          TEXT      NOT NULL DEFAULT 'pending',
    payment_status  payment_status_enum DEFAULT 'pending',
    booking_type    TEXT      DEFAULT 'regular',
    special_requests TEXT,
    cancellation_reason TEXT,
    cancelled_at    TIMESTAMP,
    cancelled_by    UUID      REFERENCES users(id),
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الطرق
CREATE TABLE routes (
    route_id        UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    route_name      TEXT      NOT NULL,
    from_location   TEXT      NOT NULL,
    to_location     TEXT      NOT NULL,
    from_latitude   DECIMAL(10,8),
    from_longitude  DECIMAL(11,8),
    to_latitude     DECIMAL(10,8),
    to_longitude    DECIMAL(11,8),
    distance_km     DECIMAL(8,2),
    estimated_duration INTERVAL,
    base_price      DECIMAL(10,2),
    price_per_km    DECIMAL(10,2),
    is_active       BOOLEAN   DEFAULT TRUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الكباتن
CREATE TABLE captains (
    captain_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id),
    license_number  TEXT      UNIQUE NOT NULL,
    license_expiry  DATE,
    rating          DECIMAL(3,2) DEFAULT 0.00,
    total_trips     INTEGER   DEFAULT 0,
    city            TEXT,
    verification_status TEXT DEFAULT 'pending',
    verification_date TIMESTAMP,
    verified_by     UUID      REFERENCES users(id),
    is_active       BOOLEAN   DEFAULT TRUE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول السائقين
CREATE TABLE drivers (
    driver_id       UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    captain_id      UUID      NOT NULL REFERENCES captains(captain_id),
    vehicle_id      UUID,
    shift_start     TIME,
    shift_end       TIME,
    is_available    BOOLEAN   DEFAULT FALSE,
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    last_location_update TIMESTAMP,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المركبات
CREATE TABLE vehicles (
    vehicle_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id        UUID      REFERENCES users(id),
    license_plate   TEXT      UNIQUE NOT NULL,
    make            TEXT      NOT NULL,
    model           TEXT      NOT NULL,
    year            INTEGER,
    color           TEXT,
    capacity        INTEGER   NOT NULL,
    vehicle_type    vehicle_type_enum NOT NULL,
    status          vehicle_status_enum DEFAULT 'active',
    registration_expiry DATE,
    insurance_expiry DATE,
    last_maintenance DATE,
    next_maintenance DATE,
    fuel_type       TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الرحلات
CREATE TABLE trips (
    trip_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id        UUID      REFERENCES routes(route_id),
    driver_id       UUID      REFERENCES drivers(driver_id),
    vehicle_id      UUID      REFERENCES vehicles(vehicle_id),
    departure_time  TIMESTAMP NOT NULL,
    arrival_time    TIMESTAMP,
    actual_departure_time TIMESTAMP,
    actual_arrival_time TIMESTAMP,
    available_seats INTEGER   NOT NULL,
    booked_seats    INTEGER   DEFAULT 0,
    base_price      DECIMAL(10,2),
    status          trip_status_enum DEFAULT 'pending',
    cancellation_reason TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID      REFERENCES users(id),
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by      UUID      REFERENCES users(id)
);

-- =====================================================
-- 4. PARCEL SYSTEM
-- =====================================================

-- جدول الطرود الرئيسي
CREATE TABLE parcels (
    parcel_id       TEXT      PRIMARY KEY,
    user_id         UUID      NOT NULL REFERENCES users(id),
    barcode         TEXT      UNIQUE NOT NULL,
    sender_name     TEXT      NOT NULL,
    receiver_name   TEXT      NOT NULL,
    sender_address  TEXT      NOT NULL,
    receiver_address TEXT     NOT NULL,
    sender_phone    TEXT,
    receiver_phone  TEXT,
    weight_kg       DECIMAL(8,3) NOT NULL,
    length_cm       INTEGER   NOT NULL,
    width_cm        INTEGER   NOT NULL,
    height_cm       INTEGER   NOT NULL,
    status          parcel_status_enum DEFAULT 'Created',
    priority        parcel_priority_enum DEFAULT 'MEDIUM',
    fragile         BOOLEAN   DEFAULT FALSE,
    insurance_value DECIMAL(12,2) DEFAULT 0,
    estimated_cost  DECIMAL(10,2),
    actual_cost     DECIMAL(10,2),
    notes           TEXT,
    estimated_delivery_date TIMESTAMP,
    actual_delivery_date TIMESTAMP,
    pickup_date     TIMESTAMP,
    delivery_attempts INTEGER DEFAULT 0,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID      REFERENCES users(id),
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by      UUID      REFERENCES users(id)
);

-- جدول تتبع الطرود
CREATE TABLE parcel_tracking (
    tracking_id     UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    parcel_id       TEXT      NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    status          parcel_status_enum NOT NULL,
    location_name   TEXT,
    latitude        DECIMAL(10,8),
    longitude       DECIMAL(11,8),
    timestamp       TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes           TEXT,
    updated_by      UUID      REFERENCES users(id),
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر الطرود
CREATE TABLE parcel_items (
    item_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    parcel_id       TEXT      NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    item_name       TEXT      NOT NULL,
    item_description TEXT,
    quantity        INTEGER   DEFAULT 1,
    weight_kg       DECIMAL(8,3),
    value           DECIMAL(12,2),
    category        TEXT,
    fragile         BOOLEAN   DEFAULT FALSE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أسعار الطرود
CREATE TABLE parcel_rates (
    rate_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    from_city       TEXT      NOT NULL,
    to_city         TEXT      NOT NULL,
    weight_from_kg  DECIMAL(8,3) NOT NULL,
    weight_to_kg    DECIMAL(8,3) NOT NULL,
    base_price      DECIMAL(10,2) NOT NULL,
    price_per_kg    DECIMAL(10,2) NOT NULL,
    express_multiplier DECIMAL(3,2) DEFAULT 1.5,
    fragile_fee     DECIMAL(10,2) DEFAULT 0,
    insurance_rate  DECIMAL(5,4) DEFAULT 0.02,
    is_active       BOOLEAN   DEFAULT TRUE,
    effective_from  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_to    TIMESTAMP,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. PAYMENT SYSTEM
-- =====================================================

-- جدول المدفوعات
CREATE TABLE payments (
    payment_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id),
    booking_id      TEXT      REFERENCES bookings(booking_id),
    parcel_id       TEXT      REFERENCES parcels(parcel_id),
    amount          DECIMAL(12,2) NOT NULL,
    currency        TEXT      DEFAULT 'YER',
    method          payment_method_enum NOT NULL,
    status          payment_status_enum DEFAULT 'pending',
    transaction_id  TEXT      UNIQUE,
    gateway_response JSONB,
    payment_date    TIMESTAMP,
    refund_amount   DECIMAL(12,2) DEFAULT 0,
    refund_date     TIMESTAMP,
    refund_reason   TEXT,
    notes           TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مدفوعات الطرود (منفصل للمرونة)
CREATE TABLE parcel_payments (
    payment_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    parcel_id       TEXT      NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
    amount          DECIMAL(12,2) NOT NULL,
    currency        TEXT      DEFAULT 'YER',
    method          payment_method_enum NOT NULL,
    status          payment_status_enum DEFAULT 'pending',
    transaction_id  TEXT      UNIQUE,
    payment_date    TIMESTAMP,
    notes           TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 6. NOTIFICATION SYSTEM
-- =====================================================

-- جدول الإشعارات
CREATE TABLE notifications (
    notification_id UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      REFERENCES users(id) ON DELETE CASCADE,
    title           TEXT      NOT NULL,
    message         TEXT      NOT NULL,
    type            TEXT      NOT NULL,
    channel         TEXT      NOT NULL, -- email, sms, push, in_app
    data            JSONB,
    is_read         BOOLEAN   DEFAULT FALSE,
    is_sent         BOOLEAN   DEFAULT FALSE,
    sent_at         TIMESTAMP,
    read_at         TIMESTAMP,
    expires_at      TIMESTAMP,
    priority        INTEGER   DEFAULT 1,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. INDEXES للأداء المحسن
-- =====================================================

-- فهارس المستخدمين
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- فهارس الحجوزات
CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_departure_time ON bookings(departure_time);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);

-- فهارس الطرود
CREATE INDEX idx_parcels_user_id ON parcels(user_id);
CREATE INDEX idx_parcels_barcode ON parcels(barcode);
CREATE INDEX idx_parcels_status ON parcels(status);
CREATE INDEX idx_parcels_created_at ON parcels(created_at);

-- فهارس التتبع
CREATE INDEX idx_parcel_tracking_parcel_id ON parcel_tracking(parcel_id);
CREATE INDEX idx_parcel_tracking_timestamp ON parcel_tracking(timestamp);

-- فهارس المدفوعات
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- فهارس الإشعارات
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- =====================================================
-- 8. EXTENDED BUSINESS TABLES
-- =====================================================

-- جدول الحجوزات الموسعة
CREATE TABLE bookings_extended (
    id              UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id      TEXT      NOT NULL REFERENCES bookings(booking_id),
    promo_code_id   UUID      REFERENCES promotions(promo_code_id),
    extra_fee       DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount      DECIMAL(10,2) DEFAULT 0,
    service_fee     DECIMAL(10,2) DEFAULT 0,
    total_amount    DECIMAL(10,2),
    loyalty_points_earned INTEGER DEFAULT 0,
    loyalty_points_used INTEGER DEFAULT 0,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العروض الترويجية
CREATE TABLE promotions (
    promo_code_id   UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    code            TEXT      UNIQUE NOT NULL,
    title           TEXT      NOT NULL,
    description     TEXT,
    discount_type   TEXT      NOT NULL CHECK (discount_type IN ('percentage', 'fixed', 'free_shipping')),
    value           DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_discount    DECIMAL(10,2),
    usage_limit     INTEGER,
    used_count      INTEGER   DEFAULT 0,
    user_limit      INTEGER   DEFAULT 1,
    is_active       BOOLEAN   DEFAULT TRUE,
    starts_at       TIMESTAMP NOT NULL,
    expires_at      TIMESTAMP NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول نقاط الطريق
CREATE TABLE way_points (
    waypoint_id     UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id      TEXT      NOT NULL REFERENCES bookings(booking_id) ON DELETE CASCADE,
    trip_id         UUID      REFERENCES trips(trip_id),
    sequence_order  INTEGER   NOT NULL,
    location_name   TEXT      NOT NULL,
    latitude        DECIMAL(10,8) NOT NULL,
    longitude       DECIMAL(11,8) NOT NULL,
    estimated_time  TIMESTAMP,
    actual_time     TIMESTAMP,
    status          TEXT      DEFAULT 'pending' CHECK (status IN ('pending', 'reached', 'skipped')),
    notes           TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تقييمات ما بعد الرحلة
CREATE TABLE post_trip_ratings (
    rating_id       UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id      TEXT      NOT NULL REFERENCES bookings(booking_id),
    user_id         UUID      NOT NULL REFERENCES users(id),
    driver_id       UUID      REFERENCES drivers(driver_id),
    trip_id         UUID      REFERENCES trips(trip_id),
    driver_rating   INTEGER   CHECK (driver_rating >= 1 AND driver_rating <= 5),
    vehicle_rating  INTEGER   CHECK (vehicle_rating >= 1 AND vehicle_rating <= 5),
    service_rating  INTEGER   CHECK (service_rating >= 1 AND service_rating <= 5),
    punctuality_rating INTEGER CHECK (punctuality_rating >= 1 AND punctuality_rating <= 5),
    overall_rating  INTEGER   CHECK (overall_rating >= 1 AND overall_rating <= 5),
    comment         TEXT,
    would_recommend BOOLEAN,
    tags            TEXT[],   -- Array of tags like 'clean', 'friendly', 'fast'
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 9. SUPPORT SYSTEM
-- =====================================================

-- جدول تذاكر الدعم
CREATE TABLE support_tickets (
    ticket_id       UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_number   TEXT      UNIQUE NOT NULL,
    user_id         UUID      NOT NULL REFERENCES users(id),
    category        TEXT      NOT NULL,
    subcategory     TEXT,
    subject         TEXT      NOT NULL,
    description     TEXT      NOT NULL,
    priority        TEXT      DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status          ticket_status_enum DEFAULT 'open',
    assigned_to     UUID      REFERENCES users(id),
    resolution      TEXT,
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    related_booking_id TEXT   REFERENCES bookings(booking_id),
    related_parcel_id TEXT    REFERENCES parcels(parcel_id),
    attachments     JSONB,
    tags            TEXT[],
    escalated_at    TIMESTAMP,
    resolved_at     TIMESTAMP,
    closed_at       TIMESTAMP,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ردود تذاكر الدعم
CREATE TABLE ticket_responses (
    response_id     UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id       UUID      NOT NULL REFERENCES support_tickets(ticket_id) ON DELETE CASCADE,
    responder_id    UUID      NOT NULL REFERENCES users(id),
    message         TEXT      NOT NULL,
    is_internal     BOOLEAN   DEFAULT FALSE,
    attachments     JSONB,
    sent_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 10. ANALYTICS & LOGS
-- =====================================================

-- جدول سجلات الأداء
CREATE TABLE performance_logs (
    log_id          UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name    TEXT      NOT NULL,
    endpoint        TEXT,
    method          TEXT,
    duration_ms     INTEGER   NOT NULL,
    status_code     INTEGER,
    user_id         UUID      REFERENCES users(id),
    ip_address      INET,
    user_agent      TEXT,
    request_size    INTEGER,
    response_size   INTEGER,
    error_message   TEXT,
    timestamp       TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الملخص المالي
CREATE TABLE financial_summary (
    summary_id      UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    period_type     TEXT      NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly')),
    period_start    DATE      NOT NULL,
    period_end      DATE      NOT NULL,
    total_revenue   DECIMAL(15,2) NOT NULL DEFAULT 0,
    booking_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    parcel_revenue  DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_expenses  DECIMAL(15,2) NOT NULL DEFAULT 0,
    driver_payments DECIMAL(15,2) NOT NULL DEFAULT 0,
    platform_fees   DECIMAL(15,2) NOT NULL DEFAULT 0,
    refunds         DECIMAL(15,2) NOT NULL DEFAULT 0,
    net_profit      DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_bookings  INTEGER   DEFAULT 0,
    total_parcels   INTEGER   DEFAULT 0,
    active_users    INTEGER   DEFAULT 0,
    new_users       INTEGER   DEFAULT 0,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(period_type, period_start, period_end)
);

-- جدول ملفات الوسائط
CREATE TABLE media_files (
    file_id         UUID      PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      REFERENCES users(id),
    original_name   TEXT      NOT NULL,
    file_name       TEXT      NOT NULL,
    file_path       TEXT      NOT NULL,
    file_size       BIGINT    NOT NULL,
    mime_type       TEXT      NOT NULL,
    file_type       TEXT      NOT NULL CHECK (file_type IN ('image', 'video', 'audio', 'document')),
    url             TEXT      NOT NULL,
    thumbnail_url   TEXT,
    description     TEXT,
    alt_text        TEXT,
    is_public       BOOLEAN   DEFAULT FALSE,
    download_count  INTEGER   DEFAULT 0,
    uploaded_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at      TIMESTAMP
);

-- جدول سجل التوصيات
CREATE TABLE recommendations_log (
    recommendation_id UUID    PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id         UUID      NOT NULL REFERENCES users(id),
    type            TEXT      NOT NULL, -- route, driver, price, etc.
    algorithm       TEXT      NOT NULL,
    input_data      JSONB     NOT NULL,
    recommendations JSONB     NOT NULL,
    selected_option JSONB,
    feedback_score  INTEGER   CHECK (feedback_score >= 1 AND feedback_score <= 5),
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback_at     TIMESTAMP
);

-- =====================================================
-- 11. ADDITIONAL INDEXES
-- =====================================================

-- فهارس الجداول الموسعة
CREATE INDEX idx_bookings_extended_booking_id ON bookings_extended(booking_id);
CREATE INDEX idx_promotions_code ON promotions(code);
CREATE INDEX idx_promotions_active ON promotions(is_active);
CREATE INDEX idx_waypoints_booking_id ON way_points(booking_id);
CREATE INDEX idx_ratings_booking_id ON post_trip_ratings(booking_id);
CREATE INDEX idx_ratings_driver_id ON post_trip_ratings(driver_id);

-- فهارس الدعم
CREATE INDEX idx_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_tickets_status ON support_tickets(status);
CREATE INDEX idx_tickets_category ON support_tickets(category);
CREATE INDEX idx_tickets_created_at ON support_tickets(created_at);

-- فهارس التحليلات
CREATE INDEX idx_performance_logs_service ON performance_logs(service_name);
CREATE INDEX idx_performance_logs_timestamp ON performance_logs(timestamp);
CREATE INDEX idx_financial_summary_period ON financial_summary(period_type, period_start);
CREATE INDEX idx_media_files_user_id ON media_files(user_id);
CREATE INDEX idx_media_files_type ON media_files(file_type);
CREATE INDEX idx_recommendations_user_id ON recommendations_log(user_id);
CREATE INDEX idx_recommendations_type ON recommendations_log(type);
