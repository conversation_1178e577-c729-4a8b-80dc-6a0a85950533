import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { useTheme } from '@mui/material/styles';

const mockData = [
  { month: 'يناير', revenue: 25000, rides: 450 },
  { month: 'فبراير', revenue: 28000, rides: 520 },
  { month: 'مارس', revenue: 32000, rides: 580 },
  { month: 'أبريل', revenue: 35000, rides: 620 },
  { month: 'مايو', revenue: 38000, rides: 680 },
  { month: 'يونيو', revenue: 42000, rides: 750 },
  { month: 'يوليو', revenue: 45000, rides: 820 },
  { month: 'أغسطس', revenue: 48000, rides: 890 },
  { month: 'سبتمبر', revenue: 44000, rides: 780 },
  { month: 'أكتوبر', revenue: 47000, rides: 850 },
  { month: 'نوفمبر', revenue: 51000, rides: 920 },
  { month: 'ديسمبر', revenue: 55000, rides: 1000 },
];

const RevenueChart: React.FC = () => {
  const theme = useTheme();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: theme.palette.background.paper,
            padding: '12px',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: '8px',
            boxShadow: theme.shadows[4],
          }}
        >
          <p style={{ margin: 0, fontWeight: 'bold', marginBottom: '8px' }}>
            {label}
          </p>
          <p style={{ margin: 0, color: theme.palette.primary.main }}>
            الإيرادات: {payload[0].value.toLocaleString()} ريال
          </p>
          <p style={{ margin: 0, color: theme.palette.secondary.main }}>
            الرحلات: {payload[1]?.value || 0}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={mockData}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <defs>
          <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
            <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
        <XAxis 
          dataKey="month" 
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <YAxis 
          stroke={theme.palette.text.secondary}
          fontSize={12}
          tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
        />
        <Tooltip content={<CustomTooltip />} />
        <Area
          type="monotone"
          dataKey="revenue"
          stroke={theme.palette.primary.main}
          fillOpacity={1}
          fill="url(#colorRevenue)"
          strokeWidth={3}
        />
        <Line
          type="monotone"
          dataKey="rides"
          stroke={theme.palette.secondary.main}
          strokeWidth={2}
          dot={{ fill: theme.palette.secondary.main, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: theme.palette.secondary.main, strokeWidth: 2 }}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default RevenueChart;
