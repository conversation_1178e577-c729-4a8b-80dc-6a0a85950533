# Default values for location-service
replicaCount: 3

image:
  repository: ghcr.io/tecnodrive/location-service
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8086"
  prometheus.io/path: "/actuator/prometheus"

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 8086
  targetPort: 8086

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
  hosts:
    - host: location-api.tecnodrive.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 768Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - location-service
        topologyKey: kubernetes.io/hostname

# Application configuration
config:
  server:
    port: 8086
  
  spring:
    application:
      name: location-service
    profiles:
      active: kubernetes
    
    datasource:
      url: *********************************************
      username: postgres
      password: ${POSTGRES_PASSWORD}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    
    jpa:
      hibernate:
        ddl-auto: validate
      show-sql: false
      properties:
        hibernate:
          dialect: org.hibernate.spatial.dialect.postgis.PostgisPG95Dialect
          format_sql: true
          jdbc:
            time_zone: UTC
    
    kafka:
      bootstrap-servers: kafka:9092
      producer:
        acks: all
        retries: 3
        enable-idempotence: true
      consumer:
        group-id: location-service-group
        auto-offset-reset: earliest
        enable-auto-commit: false
    
    redis:
      host: redis-master
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics,prometheus
    endpoint:
      health:
        show-details: always
    metrics:
      export:
        prometheus:
          enabled: true

  # OpenTelemetry configuration
  opentelemetry:
    jaeger:
      endpoint: http://jaeger-collector:14250
    prometheus:
      port: 9090

  # Location service specific config
  location:
    tracking:
      enabled: true
      update-interval: 30
      max-history-days: 30
      cleanup-interval: 3600
    
    geofencing:
      enabled: true
      default-radius: 1000
    
    analytics:
      enabled: true
      batch-size: 100
      processing-interval: 300

# PostgreSQL dependency
postgresql:
  enabled: true
  image:
    repository: postgis/postgis
    tag: "15-3.3"
  auth:
    postgresPassword: "postgres123"
    database: "location_db"
  primary:
    persistence:
      enabled: true
      size: 20Gi
    resources:
      limits:
        memory: 2Gi
        cpu: 1000m
      requests:
        memory: 1Gi
        cpu: 500m

# Redis dependency
redis:
  enabled: true
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      size: 8Gi
    resources:
      limits:
        memory: 512Mi
        cpu: 500m
      requests:
        memory: 256Mi
        cpu: 250m

# Environment variables
env:
  - name: POSTGRES_PASSWORD
    valueFrom:
      secretKeyRef:
        name: postgresql-secret
        key: postgres-password
  - name: JAVA_OPTS
    value: "-Xmx768m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
  - name: SPRING_PROFILES_ACTIVE
    value: "kubernetes"

# Probes
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8086
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8086
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Volume mounts
volumeMounts:
  - name: tmp
    mountPath: /tmp
  - name: cache
    mountPath: /app/cache

volumes:
  - name: tmp
    emptyDir: {}
  - name: cache
    emptyDir: {}
