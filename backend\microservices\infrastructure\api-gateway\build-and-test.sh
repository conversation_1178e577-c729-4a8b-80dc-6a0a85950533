#!/bin/bash

# TECNO DRIVE API Gateway - Build and Test Script

echo "🌐 Building TECNO DRIVE API Gateway..."

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven not found. Please install <PERSON>ven first."
    echo "   Download from: https://maven.apache.org/download.cgi"
    exit 1
fi

# Clean and compile
echo "🧹 Cleaning previous builds..."
mvn clean

echo "📦 Compiling and packaging..."
mvn package -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "🚀 To run the API Gateway:"
    echo "  java -jar target/api-gateway-1.0.0.jar"
    echo ""
    echo "🐳 To build Docker image:"
    echo "  docker build -t tecnodrive/api-gateway:latest ."
    echo ""
    echo "🔍 Health check:"
    echo "  curl http://localhost:8080/actuator/health"
    echo ""
    echo "🌐 Gateway routes:"
    echo "  curl http://localhost:8080/actuator/gateway/routes"
    echo ""
    echo "📊 Prometheus metrics:"
    echo "  curl http://localhost:8080/actuator/prometheus"
    echo ""
    echo "🔐 Rate limiting features:"
    echo "  - 10 requests/sec replenish rate"
    echo "  - 20 burst capacity"
    echo "  - IP-based rate limiting"
    echo "  - JWT validation for protected routes"
else
    echo "❌ Build failed!"
    exit 1
fi
