import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  ArrowBack,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DirectionsCar as CarIcon,
  LocationOn as LocationIcon,
  Build as MaintenanceIcon,
  LocalGasStation as FuelIcon,
  Speed as SpeedIcon,
  CalendarToday as CalendarIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { fleetService, VehicleDto, MaintenanceTaskDto, LocationDto } from '../../services/fleetService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vehicle-tabpanel-${index}`}
      aria-labelledby={`vehicle-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const VehicleDetail: React.FC = () => {
  const { vehicleId } = useParams<{ vehicleId: string }>();
  const navigate = useNavigate();
  
  const [vehicle, setVehicle] = useState<VehicleDto | null>(null);
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTaskDto[]>([]);
  const [locationHistory, setLocationHistory] = useState<LocationDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  // Load vehicle data
  const loadVehicleData = async () => {
    if (!vehicleId) return;

    try {
      setLoading(true);
      
      // Load vehicle details
      const vehicleResponse = await fleetService.getVehicleById(vehicleId);
      if (vehicleResponse.success && vehicleResponse.data) {
        setVehicle(vehicleResponse.data);
      }

      // Load maintenance tasks
      const maintenanceResponse = await fleetService.getMaintenanceTasks(vehicleId);
      if (maintenanceResponse.success && maintenanceResponse.data) {
        setMaintenanceTasks(maintenanceResponse.data);
      }

      // Load location history
      const locationResponse = await fleetService.getVehicleLocation(vehicleId, {
        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last 7 days
        to: new Date().toISOString(),
      });
      if (locationResponse.success && locationResponse.data) {
        setLocationHistory(locationResponse.data);
      }

    } catch (error) {
      console.error('Error loading vehicle data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadVehicleData();
  }, [vehicleId]);

  const getStatusChip = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: 'نشط', color: 'success' as const, icon: '🟢' },
      MAINTENANCE: { label: 'صيانة', color: 'warning' as const, icon: '🟠' },
      OFFLINE: { label: 'غير متصل', color: 'error' as const, icon: '🔴' },
      OUT_OF_SERVICE: { label: 'خارج الخدمة', color: 'default' as const, icon: '⚫' },
      SCHEDULED: { label: 'مجدولة', color: 'info' as const, icon: '📅' },
      IN_PROGRESS: { label: 'قيد التنفيذ', color: 'warning' as const, icon: '🔧' },
      COMPLETED: { label: 'مكتملة', color: 'success' as const, icon: '✅' },
      CANCELLED: { label: 'ملغية', color: 'default' as const, icon: '❌' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'default' as const, 
      icon: '⚪' 
    };
    
    return (
      <Chip
        label={`${config.icon} ${config.label}`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getPriorityChip = (priority: string) => {
    const priorityConfig = {
      LOW: { label: 'منخفضة', color: 'info' as const },
      MEDIUM: { label: 'متوسطة', color: 'warning' as const },
      HIGH: { label: 'عالية', color: 'error' as const },
      URGENT: { label: 'عاجلة', color: 'error' as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || { 
      label: priority, 
      color: 'default' as const 
    };
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        variant="filled"
      />
    );
  };

  const getFuelTypeIcon = (fuelType: string) => {
    switch (fuelType) {
      case 'ELECTRIC': return '🔋';
      case 'HYBRID': return '⚡';
      case 'DIESEL': return '⛽';
      default: return '⛽';
    }
  };

  const getTaskTypeLabel = (taskType: string) => {
    const taskTypes = {
      OIL_CHANGE: 'تغيير الزيت',
      TIRE_ROTATION: 'تدوير الإطارات',
      BRAKE_CHECK: 'فحص الفرامل',
      ENGINE_SERVICE: 'صيانة المحرك',
      GENERAL_INSPECTION: 'فحص عام',
      CUSTOM: 'مخصص',
    };
    return taskTypes[taskType as keyof typeof taskTypes] || taskType;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>جاري تحميل بيانات المركبة...</Typography>
      </Box>
    );
  }

  if (!vehicle) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">
          لم يتم العثور على المركبة
        </Typography>
        <Button onClick={() => navigate('/fleet/vehicles')} sx={{ mt: 2 }}>
          العودة إلى قائمة المركبات
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate('/fleet/vehicles')}>
            <ArrowBack />
          </IconButton>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            <CarIcon />
          </Avatar>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
              {vehicle.plateNumber} - {vehicle.make} {vehicle.model}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {vehicle.year} • {vehicle.color} • {vehicle.capacity} راكب
            </Typography>
          </Box>
          {getStatusChip(vehicle.status)}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/fleet/vehicles/${vehicleId}/edit`)}
          >
            تعديل
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
          >
            حذف
          </Button>
        </Box>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SpeedIcon color="primary" />
                <Box>
                  <Typography variant="h6">{vehicle.mileage?.toLocaleString() || 0} كم</Typography>
                  <Typography variant="body2" color="text.secondary">
                    المسافة المقطوعة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <span style={{ fontSize: '24px' }}>{getFuelTypeIcon(vehicle.fuelType)}</span>
                <Box>
                  <Typography variant="h6">
                    {vehicle.fuelType === 'GASOLINE' ? 'بنزين' :
                     vehicle.fuelType === 'DIESEL' ? 'ديزل' :
                     vehicle.fuelType === 'ELECTRIC' ? 'كهربائي' :
                     vehicle.fuelType === 'HYBRID' ? 'هجين' : vehicle.fuelType}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    نوع الوقود
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MaintenanceIcon color="warning" />
                <Box>
                  <Typography variant="h6">{maintenanceTasks.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    مهام الصيانة
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <LocationIcon color="info" />
                <Box>
                  <Typography variant="h6">
                    {vehicle.currentLocation?.address || 'غير محدد'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    الموقع الحالي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="معلومات المركبة" />
            <Tab label="الصيانة" />
            <Tab label="تاريخ المواقع" />
            <Tab label="الخريطة الحية" />
          </Tabs>
        </Box>

        {/* Tab 1: Vehicle Info */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  المعلومات الأساسية
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <CarIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="رقم اللوحة"
                      secondary={vehicle.plateNumber}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <span>🏭</span>
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="الماركة والموديل"
                      secondary={`${vehicle.make} ${vehicle.model}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <CalendarIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="سنة الصنع"
                      secondary={vehicle.year}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <span>🎨</span>
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="اللون"
                      secondary={vehicle.color}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  المعلومات التقنية
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <span>🔢</span>
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="رقم الهيكل (VIN)"
                      secondary={vehicle.vin}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <span>👥</span>
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="السعة"
                      secondary={`${vehicle.capacity} راكب`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <FuelIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="نوع الوقود"
                      secondary={
                        vehicle.fuelType === 'GASOLINE' ? 'بنزين' :
                        vehicle.fuelType === 'DIESEL' ? 'ديزل' :
                        vehicle.fuelType === 'ELECTRIC' ? 'كهربائي' :
                        vehicle.fuelType === 'HYBRID' ? 'هجين' : vehicle.fuelType
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <SpeedIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="المسافة المقطوعة"
                      secondary={`${vehicle.mileage?.toLocaleString() || 0} كم`}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab 2: Maintenance */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">مهام الصيانة</Typography>
            <Button variant="contained" startIcon={<AddIcon />}>
              إضافة مهمة صيانة
            </Button>
          </Box>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>نوع المهمة</TableCell>
                  <TableCell>الوصف</TableCell>
                  <TableCell>الأولوية</TableCell>
                  <TableCell>التاريخ المجدول</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>التكلفة</TableCell>
                  <TableCell>المسؤول</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {maintenanceTasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell>{getTaskTypeLabel(task.taskType)}</TableCell>
                    <TableCell>{task.description}</TableCell>
                    <TableCell>{getPriorityChip(task.priority)}</TableCell>
                    <TableCell>
                      {new Date(task.scheduledDate).toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>{getStatusChip(task.status)}</TableCell>
                    <TableCell>
                      {task.cost ? `${task.cost.toLocaleString()} ريال` : '-'}
                    </TableCell>
                    <TableCell>{task.assignedTo || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Tab 3: Location History */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            تاريخ المواقع (آخر 7 أيام)
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>التاريخ والوقت</TableCell>
                  <TableCell>العنوان</TableCell>
                  <TableCell>خط العرض</TableCell>
                  <TableCell>خط الطول</TableCell>
                  <TableCell>السرعة</TableCell>
                  <TableCell>الاتجاه</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {locationHistory.map((location) => (
                  <TableRow key={location.id}>
                    <TableCell>
                      {new Date(location.timestamp).toLocaleString('ar-SA')}
                    </TableCell>
                    <TableCell>{location.address || '-'}</TableCell>
                    <TableCell>{location.latitude.toFixed(6)}</TableCell>
                    <TableCell>{location.longitude.toFixed(6)}</TableCell>
                    <TableCell>
                      {location.speed ? `${location.speed.toFixed(1)} كم/س` : '-'}
                    </TableCell>
                    <TableCell>
                      {location.heading ? `${location.heading.toFixed(0)}°` : '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Tab 4: Live Map */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.100' }}>
            <Typography variant="h6" color="text.secondary">
              خريطة Google Maps الحية
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
              (سيتم تطويرها في المرحلة التالية)
            </Typography>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default VehicleDetail;
