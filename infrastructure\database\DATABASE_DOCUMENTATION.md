# 🗄️ قاعدة البيانات المحسنة - منصة TECNO DRIVE

## نظرة عامة

تم تطوير هيكل قاعدة بيانات شامل ومحسن لمنصة TECNO DRIVE يدعم جميع العمليات التجارية مع التركيز على الأداء والمرونة والقابلية للتوسع.

## 🏗️ هيكل قاعدة البيانات

### 1. قواعد البيانات المتعددة

#### 📱 قاعدة البيانات المحلية (SQLite/Room)
- **الغرض**: تخزين البيانات محلياً في التطبيق المحمول
- **التقنية**: SQLite مع Room Database
- **المزامنة**: Offline-First مع مزامنة تلقائية

#### 🖥️ قاعدة البيانات الخلفية (PostgreSQL)
- **الغرض**: قاعدة البيانات الرئيسية للخوادم
- **التقنية**: PostgreSQL 15+ مع امتدادات متقدمة
- **المزايا**: ACID، JSON، GIS، Full-text search

## 📊 نماذج البيانات

### 🎯 نموذج الطرد (Parcel)

#### JSON Structure:
```json
{
  "parcelId": "abc123",
  "barcode": "123456789012",
  "senderName": "أحمد محمد",
  "receiverName": "خالد علي",
  "senderAddress": "شارع بغداد، صنعاء",
  "receiverAddress": "شارع الزبيري، صنعاء",
  "weightKg": 2.5,
  "dimensions": {
    "lengthCm": 30,
    "widthCm": 20,
    "heightCm": 10
  },
  "status": "InTransit",
  "priority": "MEDIUM",
  "fragile": false,
  "insuranceValue": 1000.00
}
```

#### Java Entity Features:
- ✅ **Validation**: تحقق شامل من البيانات
- ✅ **Calculations**: حساب التكلفة والحجم تلقائياً
- ✅ **Status Management**: إدارة ذكية للحالات
- ✅ **Relationships**: علاقات مع التتبع والمدفوعات

## 🔐 نظام RBAC المحسن

### الأدوار (Roles):
```sql
super_admin    -- مدير النظام الرئيسي
admin          -- مدير عام
manager        -- مدير قسم
operator       -- مشغل
customer_service -- خدمة العملاء
driver         -- سائق
captain        -- كابتن
customer       -- عميل
guest          -- زائر
```

### الصلاحيات (Permissions):
- **المستخدمين**: create, read, update, delete, manage_roles
- **الحجوزات**: create, read, update, delete, cancel, confirm
- **الطرود**: create, read, update, delete, track, update_status
- **المدفوعات**: create, read, update, refund
- **المركبات**: create, read, update, delete
- **السائقين**: create, read, update, delete, verify
- **التقارير**: view, export
- **النظام**: settings, backup, logs, maintenance

## 📋 الجداول الرئيسية

### 👥 إدارة المستخدمين
```sql
users              -- المستخدمين الأساسي
roles              -- الأدوار
permissions        -- الصلاحيات
user_roles         -- ربط المستخدمين بالأدوار
role_permissions   -- ربط الأدوار بالصلاحيات
user_sessions      -- جلسات المستخدمين
```

### 🚗 نظام النقل
```sql
bookings           -- الحجوزات
bookings_extended  -- الحجوزات الموسعة
routes             -- الطرق
trips              -- الرحلات
way_points         -- نقاط الطريق
post_trip_ratings  -- تقييمات الرحلات
```

### 📦 نظام الطرود
```sql
parcels            -- الطرود الأساسي
parcel_tracking    -- تتبع الطرود
parcel_items       -- عناصر الطرود
parcel_rates       -- أسعار الطرود
parcel_payments    -- مدفوعات الطرود
```

### 🚛 إدارة الأسطول
```sql
captains           -- الكباتن
drivers            -- السائقين
vehicles           -- المركبات
```

### 💳 نظام المدفوعات
```sql
payments           -- المدفوعات العامة
parcel_payments    -- مدفوعات الطرود
promotions         -- العروض الترويجية
```

### 🔔 نظام الإشعارات والدعم
```sql
notifications      -- الإشعارات
support_tickets    -- تذاكر الدعم
ticket_responses   -- ردود الدعم
```

### 📈 التحليلات والسجلات
```sql
performance_logs   -- سجلات الأداء
financial_summary  -- الملخص المالي
media_files        -- ملفات الوسائط
recommendations_log -- سجل التوصيات
```

## 🎯 ENUMs المخصصة

### حالات الطرود:
```sql
CREATE TYPE parcel_status_enum AS ENUM (
    'Created',         -- تم الإنشاء
    'PickedUp',        -- تم الاستلام
    'InTransit',       -- في الطريق
    'OutForDelivery',  -- خارج للتوصيل
    'Delivered',       -- تم التوصيل
    'Returned',        -- تم الإرجاع
    'Cancelled',       -- ملغي
    'Lost',            -- مفقود
    'Damaged'          -- تالف
);
```

### أولويات الطرود:
```sql
CREATE TYPE parcel_priority_enum AS ENUM (
    'LOW',      -- منخفضة
    'MEDIUM',   -- متوسطة
    'HIGH',     -- عالية
    'URGENT'    -- عاجل
);
```

### طرق الدفع:
```sql
CREATE TYPE payment_method_enum AS ENUM (
    'cash',           -- نقدي
    'card',           -- بطاقة ائتمان
    'wallet',         -- محفظة إلكترونية
    'bank_transfer',  -- تحويل بنكي
    'mobile_payment'  -- دفع محمول
);
```

## 🚀 المزايا المحققة

### 🔧 الأداء:
- **فهارس محسنة**: على جميع الحقول المهمة
- **تقسيم البيانات**: للجداول الكبيرة
- **Views محسنة**: لاستعلامات سريعة
- **Triggers ذكية**: للتحديث التلقائي

### 🛡️ الأمان:
- **تشفير كلمات المرور**: bcrypt
- **RBAC شامل**: صلاحيات مفصلة
- **تدقيق العمليات**: تتبع جميع التغييرات
- **جلسات آمنة**: إدارة متقدمة للجلسات

### 📱 المزامنة:
- **Offline-First**: العمل بدون إنترنت
- **مزامنة ذكية**: تحديث تلقائي
- **حل التضارب**: إدارة التضارب في البيانات
- **سجل المزامنة**: تتبع حالة المزامنة

### 📊 التحليلات:
- **إحصائيات فورية**: Views محسنة
- **تقارير مالية**: ملخص شامل
- **تتبع الأداء**: سجلات مفصلة
- **توصيات ذكية**: نظام التوصيات

## 🔄 Migration والتحديث

### تطبيق التحديثات:
```bash
# تطبيق Migration
psql -d tecnodrive -f database/migrations/V2024_01_15__enhanced_database_schema.sql

# تحميل البيانات الأولية
psql -d tecnodrive -f database/backend/initial_data.sql
```

### التحقق من التحديث:
```sql
-- التحقق من الجداول الجديدة
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- التحقق من ENUMs
SELECT typname FROM pg_type 
WHERE typtype = 'e' 
ORDER BY typname;

-- التحقق من الفهارس
SELECT indexname FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY indexname;
```

## 📝 أفضل الممارسات

### 1. تصميم الجداول:
- استخدم UUID للمفاتيح الأساسية
- أضف created_at و updated_at لجميع الجداول
- استخدم ENUMs للحقول المحدودة
- أضف فهارس على الحقول المستعلمة

### 2. الأمان:
- لا تخزن كلمات المرور بوضوح
- استخدم RBAC للتحكم في الوصول
- سجل جميع العمليات الحساسة
- استخدم SSL/TLS للاتصالات

### 3. الأداء:
- استخدم Connection Pooling
- راقب الاستعلامات البطيئة
- استخدم Caching للبيانات المتكررة
- قم بتحسين الفهارس بانتظام

### 4. النسخ الاحتياطي:
- نسخ احتياطية يومية
- اختبر استعادة البيانات
- احتفظ بنسخ متعددة
- استخدم Point-in-time recovery

## 🔧 الصيانة

### مراقبة الأداء:
```sql
-- الاستعلامات البطيئة
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- حجم الجداول
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### تنظيف البيانات:
```sql
-- حذف الجلسات المنتهية
DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;

-- أرشفة البيانات القديمة
-- (حسب سياسة الشركة)
```

## 📞 الدعم

للمساعدة أو الاستفسارات حول قاعدة البيانات:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +967-1-234567
- 💬 الدردشة: support.tecnodrive.com

---

**آخر تحديث**: 15 يناير 2024  
**الإصدار**: 2.1.0  
**المطور**: فريق TECNO DRIVE
